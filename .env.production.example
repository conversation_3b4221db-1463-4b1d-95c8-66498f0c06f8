# 生产环境配置模板
# 使用方法：复制此文件为.env.production，并填入实际值

# 服务器配置
PORT=9090
NODE_ENV=production
API_PREFIX=/api/v1
CORS_ORIGIN=https://aibubb.com
BASE_URL=https://api.aibubb.com

# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=aibubb_db
DB_USER=aibubb_user
DB_PASSWORD=your_strong_password_here
DB_DIALECT=mysql
DB_LOGGING=false
DB_POOL_MAX=10
DB_POOL_MIN=0

# Redis配置
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your_redis_password_here

# JWT配置
JWT_SECRET=your_strong_jwt_secret_key_here
JWT_EXPIRES_IN=86400
JWT_REFRESH_EXPIRES_IN=604800

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# AI提供商配置
# 可选值: bytedance, aliyun, hunyuan
AI_PROVIDER=aliyun

# 字节大模型API配置
# 使用OpenAI SDK调用字节大模型
ARK_API_KEY=your_ark_api_key_here
ARK_API_MODEL=deepseek-r1-250120

# 阿里云百炼API配置
# 使用OpenAI SDK调用阿里云百炼API
# 请在此处填写您的阿里云百炼API密钥
DASHSCOPE_API_KEY=sk-your-aliyun-api-key-here
# 阿里云百炼模型ID
DASHSCOPE_API_MODEL=qwen-plus

# 腾讯混元大模型API配置
# 使用OpenAI SDK调用腾讯混元大模型
# 请在此处填写您的腾讯混元API密钥
HUNYUAN_API_KEY=your_hunyuan_api_key_here
# 腾讯混元模型ID
HUNYUAN_API_MODEL=hunyuan-turbos-latest

# 日志配置
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_LOGIN_WINDOW_MS=3600000
RATE_LIMIT_LOGIN_MAX=20
