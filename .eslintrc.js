module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true,
    jest: true // 添加Jest环境支持
  },
  globals: {
    // 微信小程序全局变量
    wx: true,
    App: true,
    Page: true,
    Component: true,
    getApp: true,
    getCurrentPages: true,
    Behavior: true,
    requirePlugin: true,

    // Jest全局变量
    describe: true,
    test: true,
    it: true,
    expect: true,
    beforeEach: true,
    afterEach: true,
    beforeAll: true,
    afterAll: true,
    jest: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module'
  },
  // TypeScript配置
  overrides: [
    {
      // 为TypeScript文件配置特殊规则
      files: ['*.ts', '*.tsx'],
      parser: '@typescript-eslint/parser',
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: __dirname,
        ecmaVersion: 2020,
        sourceType: 'module'
      },
      plugins: ['@typescript-eslint'],
      extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended'
      ],
      rules: {
        // TypeScript特定规则
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/no-explicit-any': 'warn',
        '@typescript-eslint/ban-ts-comment': 'warn',
        '@typescript-eslint/no-empty-function': 'warn',
        '@typescript-eslint/no-inferrable-types': 'warn',
        '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }]
      }
    }
  ],
  rules: {
    // 基本规则
    'indent': ['error', 2, { 'SwitchCase': 1 }],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',

    // 变量规则
    'no-unused-vars': ['warn', { 'vars': 'all', 'args': 'after-used', 'ignoreRestSiblings': true, 'argsIgnorePattern': '^_' }],
    'no-undef': 'error',
    'no-var': 'error',
    'prefer-const': ['warn', { 'destructuring': 'all' }],

    // 函数规则
    'arrow-spacing': ['error', { 'before': true, 'after': true }],
    'arrow-parens': ['warn', 'as-needed'],
    'arrow-body-style': ['warn', 'as-needed'],
    'no-confusing-arrow': 'error',

    // 对象规则
    'object-curly-spacing': ['error', 'always'],
    'key-spacing': ['error', { 'beforeColon': false, 'afterColon': true }],
    'no-dupe-keys': 'error',

    // 数组规则
    'array-bracket-spacing': ['error', 'never'],
    'array-callback-return': 'error',

    // 空格规则
    'space-before-blocks': ['error', 'always'],
    'space-before-function-paren': ['error', { 'anonymous': 'always', 'named': 'never', 'asyncArrow': 'always' }],
    'space-in-parens': ['error', 'never'],
    'space-infix-ops': 'error',
    'keyword-spacing': ['error', { 'before': true, 'after': true }],
    'no-trailing-spaces': 'error',

    // 其他规则
    'eqeqeq': ['error', 'always', { 'null': 'ignore' }],
    'no-multiple-empty-lines': ['error', { 'max': 2, 'maxEOF': 1 }],
    'no-multi-spaces': 'error',
    'no-nested-ternary': 'warn', // 降级为警告
    'no-unneeded-ternary': 'error',
    'brace-style': ['error', '1tbs', { 'allowSingleLine': true }],
    'curly': ['error', 'multi-line'],
    'camelcase': ['warn', { 'properties': 'never' }], // 降级为警告
    'no-prototype-builtins': 'warn', // 降级为警告
    'no-ex-assign': 'warn', // 降级为警告
    'no-async-promise-executor': 'warn' // 降级为警告
  }
};
