# 拉取请求描述

## 变更内容

<!-- 描述这个PR的主要变更内容 -->

## 相关问题

<!-- 链接到相关的问题或任务 -->
Fixes #

## 类型

<!-- 选择一个或多个适用的类型 -->
- [ ] 功能（新功能）
- [ ] 修复（错误修复）
- [ ] 文档（文档更新）
- [ ] 样式（格式化，缺少分号等；不影响代码功能）
- [ ] 重构（代码重构，不影响功能）
- [ ] 性能（性能改进）
- [ ] 测试（添加缺失的测试或修正现有测试）
- [ ] 构建（影响构建系统或外部依赖的更改）
- [ ] CI（CI配置文件和脚本的更改）
- [ ] 其他（请描述）：

## 检查清单

<!-- 确保你的PR满足以下要求 -->
- [ ] 我已阅读并理解[贡献指南](../CONTRIBUTING.md)
- [ ] 我的代码遵循项目的代码风格
- [ ] 我已经自测了我的代码
- [ ] 我已经为我的更改添加了测试
- [ ] 所有新的和现有的测试都通过
- [ ] 我的更改不会降低代码覆盖率
- [ ] 我已经更新了相关文档
- [ ] 我已经在本地运行了代码质量检查（`npm run quality:check`）

## 测试说明

<!-- 描述如何测试你的更改 -->

## 截图

<!-- 如果适用，添加截图以帮助解释你的更改 -->

## 其他信息

<!-- 添加任何其他相关信息 -->
