name: API契约测试

on:
  push:
    branches: [ main, backend-dev ]
    paths:
      - 'backend/**'
      - '.github/workflows/api-contract-tests.yml'
  pull_request:
    branches: [ main, backend-dev ]
    paths:
      - 'backend/**'
      - '.github/workflows/api-contract-tests.yml'
  workflow_dispatch:  # 允许手动触发

jobs:
  contract-tests:
    name: 运行API契约测试
    runs-on: ubuntu-latest
    
    services:
      # MySQL服务
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: aibubb_test
          MYSQL_USER: aibubb_test
          MYSQL_PASSWORD: test_password
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost -u root -proot"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
      
      # Redis服务
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v3
      
      - name: 设置Node.js环境
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'backend/package-lock.json'
      
      - name: 安装依赖
        working-directory: backend
        run: npm ci
      
      - name: 创建测试数据库表
        working-directory: backend
        run: |
          echo "创建测试数据库表..."
          # 如果有数据库迁移脚本，可以在这里运行
          # 例如: node scripts/migrate.js
          # 或者使用测试数据库初始化脚本
          # 例如: node scripts/init-test-db.js
      
      - name: 生成OpenAPI规范
        working-directory: backend
        run: |
          echo "生成OpenAPI规范..."
          # 如果有生成OpenAPI规范的脚本，可以在这里运行
          # 例如: node scripts/generate-swagger.js
          # 确保生成的规范文件位于正确的位置
          mkdir -p config
          touch config/swagger.json
          echo '{"openapi":"3.0.0","info":{"title":"AIBUBB API","version":"2.0.0"}}' > config/swagger.json
      
      - name: 运行API契约测试
        working-directory: backend
        run: |
          echo "运行API契约测试..."
          node scripts/run-contract-tests.js --report-format=junit --verbose
        env:
          CI: 'true'
          CONTRACT_TEST_PORT: 9092
          CONTRACT_TEST_DB_HOST: localhost
          CONTRACT_TEST_DB_PORT: 3306
          CONTRACT_TEST_DB_NAME: aibubb_test
          CONTRACT_TEST_DB_USER: aibubb_test
          CONTRACT_TEST_DB_PASSWORD: test_password
          CONTRACT_TEST_REDIS_URL: redis://localhost:6379
          CONTRACT_TEST_REDIS_ENABLED: 'true'
      
      - name: 上传测试报告
        if: always()  # 即使测试失败也上传报告
        uses: actions/upload-artifact@v3
        with:
          name: api-contract-test-reports
          path: backend/test-reports/contract-tests/
          retention-days: 7
      
      - name: 发布测试结果
        if: always()  # 即使测试失败也发布结果
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          files: backend/test-reports/contract-tests/*.xml
          check_name: "API契约测试结果"
          comment_title: "API契约测试结果"
          check_run_annotations: all
          fail_on: "failures"
