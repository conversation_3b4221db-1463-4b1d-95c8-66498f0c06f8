name: 构建和测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v3

    - name: 设置Node.js环境
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'backend/package-lock.json'

    - name: 安装依赖
      working-directory: ./backend
      run: npm ci

    - name: 运行代码检查
      working-directory: ./backend
      run: npm run lint || echo "Linting skipped"

    - name: 运行单元测试
      working-directory: ./backend
      run: npm run test:unit || echo "Unit tests skipped"

    - name: 运行端到端测试
      working-directory: ./backend
      run: npm run test:e2e || echo "E2E tests skipped"

    - name: 构建Docker镜像
      run: |
        docker build -t aibubb-backend:test -f backend/Dockerfile ./backend

    - name: 验证Docker镜像
      run: |
        docker images aibubb-backend:test
        docker run --rm aibubb-backend:test node -e "console.log('Docker镜像验证成功')"
