name: 部署到生产环境

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: 登录到Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_HUB_USERNAME }}
        password: ${{ secrets.DOCKER_HUB_TOKEN }}

    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ secrets.DOCKER_HUB_USERNAME }}/aibubb-backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,format=short

    - name: 构建并推送Docker镜像
      uses: docker/build-push-action@v3
      with:
        context: ./backend
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/aibubb-backend:buildcache
        cache-to: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/aibubb-backend:buildcache,mode=max

    - name: 部署到服务器
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        script: |
          cd /path/to/aibubb
          git pull
          docker-compose -f docker-compose.optimized.yml pull
          docker-compose -f docker-compose.optimized.yml up -d
          
    - name: 验证部署
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        script: |
          cd /path/to/aibubb
          docker-compose -f docker-compose.optimized.yml ps
          curl -s http://localhost:${PORT:-9090}/api/v2/health || echo "健康检查失败"
