name: 端到端测试

on:
  push:
    branches: [ main, backend-dev ]
    paths:
      - 'backend/**'
      - '.github/workflows/e2e-tests.yml'
  pull_request:
    branches: [ main, backend-dev ]
    paths:
      - 'backend/**'
      - '.github/workflows/e2e-tests.yml'
  workflow_dispatch:  # 允许手动触发

jobs:
  e2e-tests:
    name: 运行端到端测试
    runs-on: ubuntu-latest

    services:
      # MySQL服务
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: aibubb_e2e_test
          MYSQL_USER: root
          MYSQL_PASSWORD: secret
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost -u root -proot"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

      # Redis服务
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

    steps:
      - name: 检出代码
        uses: actions/checkout@v3

      - name: 设置Node.js环境
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'backend/package-lock.json'

      - name: 安装依赖
        working-directory: backend
        run: npm ci

      - name: 准备测试数据库
        working-directory: backend
        run: |
          echo "准备测试数据库..."
          # 运行数据库迁移脚本
          node scripts/migrate.js --force --env=test
          # 运行种子脚本填充测试数据
          node scripts/seed.js --env=test
        env:
          NODE_ENV: test
          DB_HOST: localhost
          DB_PORT: 3306
          DB_NAME: aibubb_e2e_test
          DB_USER: root
          DB_PASSWORD: secret

      - name: 生成API文档
        working-directory: backend
        run: |
          echo "生成API文档..."
          # 如果有生成API文档的脚本，可以在这里运行
          # 例如: node scripts/generate-swagger.js
          # 确保生成的文档文件位于正确的位置
          mkdir -p config
          touch config/swagger.json
          echo '{"openapi":"3.0.0","info":{"title":"AIBUBB API","version":"2.0.0"}}' > config/swagger.json

      - name: 启动端到端测试环境
        working-directory: backend
        run: |
          echo "启动端到端测试环境..."
          # 启动端到端测试环境，但不阻塞后续步骤
          node scripts/start-e2e-test-env.js --port=9093 --no-reset-db &
          # 等待服务器启动
          sleep 10
        env:
          CI: 'true'
          NODE_ENV: test
          E2E_TEST_PORT: 9093
          E2E_TEST_DB_HOST: localhost
          E2E_TEST_DB_PORT: 3306
          E2E_TEST_DB_NAME: aibubb_e2e_test
          E2E_TEST_DB_USER: root
          E2E_TEST_DB_PASSWORD: secret
          E2E_TEST_REDIS_URL: redis://localhost:6379
          E2E_TEST_REDIS_ENABLED: 'true'

      - name: 运行端到端测试
        working-directory: backend
        run: |
          echo "运行端到端测试..."
          npm run test:e2e:ci
        env:
          CI: 'true'
          NODE_ENV: test
          E2E_TEST_API_URL: http://localhost:9093/api/v2

      - name: 上传测试报告
        if: always()  # 即使测试失败也上传报告
        uses: actions/upload-artifact@v3
        with:
          name: e2e-test-reports
          path: backend/test-reports/e2e-tests/
          retention-days: 7

      - name: 发布测试结果
        if: always()  # 即使测试失败也发布结果
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          files: backend/test-reports/e2e-tests/*.xml
          check_name: "端到端测试结果"
          comment_title: "端到端测试结果"
          check_run_annotations: all
          fail_on: "failures"
