name: 质量门禁检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:  # 允许手动触发

jobs:
  quality-gate:
    name: 质量门禁检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'backend/package-lock.json'
    
    - name: 安装依赖
      working-directory: ./backend
      run: npm ci
    
    - name: 运行ESLint并生成报告
      working-directory: ./backend
      run: npm run lint -- --format json > eslint-report.json || true
    
    - name: 运行测试并生成覆盖率报告
      working-directory: ./backend
      run: npm run test:unit -- --coverage
    
    - name: 运行代码复杂度检查
      working-directory: ./backend
      run: |
        echo '{"rules":{"complexity":["error",10]}}' > complexity-check.json
        npx eslint . --ext .js,.ts --no-eslintrc --config complexity-check.json --format json > complexity-report.json || true
    
    - name: 安装jscpd
      working-directory: ./backend
      run: npm install -g jscpd
    
    - name: 运行代码重复检查
      working-directory: ./backend
      run: |
        mkdir -p code-quality-reports
        jscpd . --ignore "node_modules/**,coverage/**,dist/**,test-reports/**,__tests__/**" --reporters json --output code-quality-reports
    
    - name: 运行npm audit
      working-directory: ./backend
      run: |
        mkdir -p ../security-reports
        npm audit --json > ../security-reports/npm-audit-report.json || true
    
    - name: 运行质量门禁检查
      working-directory: ./backend
      run: npm run quality:gate:ci
    
    - name: 上传质量报告
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: quality-reports
        path: |
          backend/eslint-report.json
          backend/coverage/
          backend/complexity-report.json
          backend/code-quality-reports/
          security-reports/
        retention-days: 7
    
    - name: 发布质量门禁结果
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const fs = require('fs');
          
          // 读取ESLint报告
          let eslintErrors = 0;
          let eslintWarnings = 0;
          try {
            const eslintReport = JSON.parse(fs.readFileSync('backend/eslint-report.json', 'utf8'));
            eslintReport.forEach(file => {
              eslintErrors += file.errorCount;
              eslintWarnings += file.warningCount;
            });
          } catch (error) {
            console.error('读取ESLint报告时出错:', error);
          }
          
          // 读取覆盖率报告
          let lineCoverage = 0;
          let functionCoverage = 0;
          let branchCoverage = 0;
          try {
            const coverageSummary = JSON.parse(fs.readFileSync('backend/coverage/coverage-summary.json', 'utf8'));
            lineCoverage = coverageSummary.total.lines.pct;
            functionCoverage = coverageSummary.total.functions.pct;
            branchCoverage = coverageSummary.total.branches.pct;
          } catch (error) {
            console.error('读取覆盖率报告时出错:', error);
          }
          
          // 生成评论内容
          const comment = `## 质量门禁检查结果
          
          ### 代码规范
          - ESLint错误: ${eslintErrors}
          - ESLint警告: ${eslintWarnings}
          
          ### 测试覆盖率
          - 行覆盖率: ${lineCoverage}%
          - 函数覆盖率: ${functionCoverage}%
          - 分支覆盖率: ${branchCoverage}%
          
          ### 质量门禁状态
          ${eslintErrors > 0 || lineCoverage < 70 || functionCoverage < 70 || branchCoverage < 60 ? '❌ 未通过' : '✅ 通过'}
          
          [查看详细报告](${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})
          `;
          
          // 发布评论
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
