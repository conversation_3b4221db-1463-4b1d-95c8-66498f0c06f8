name: 单元测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    
    - name: 设置 Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '14'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行单元测试
      run: node scripts/run-tests.js all --coverage
      
    - name: 上传测试结果
      uses: actions/upload-artifact@v2
      with:
        name: test-results
        path: test-results/
        
    - name: 上传覆盖率报告
      uses: actions/upload-artifact@v2
      with:
        name: coverage-report
        path: coverage/
        
    - name: 检查覆盖率阈值
      run: |
        COVERAGE=$(node -e "const fs=require('fs');const summary=JSON.parse(fs.readFileSync('coverage/coverage-summary.json')).total;console.log(summary.statements.pct)")
        if (( $(echo "$COVERAGE < 70" | bc -l) )); then
          echo "覆盖率 $COVERAGE% 低于阈值 70%"
          exit 1
        else
          echo "覆盖率 $COVERAGE% 达到或超过阈值 70%"
        fi
