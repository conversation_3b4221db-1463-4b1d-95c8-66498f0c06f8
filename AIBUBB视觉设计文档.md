# AIBUBB 视觉设计文档 (V2.0)

## 文档说明

| 文档属性 | 值                       |
| :------- | :----------------------- |
| 状态     | 📝 **修订稿**            |
| 版本     | **2.0**                  |
| 日期     | **2025-05-19** (示例)    |
| 作者     | AI Assistant & 设计团队 |
| **修订说明** | 基于 V1.0 初稿，结合项目架构、数据库设计及潜在体验问题进行优化，细化了规范，明确了部分设计决策，并增强了对性能和一致性的要求。 |
| 文档目的 | 定义 AIBUBB 应用的核心视觉风格、设计原则和关键元素规范，确保界面的一致性、高性能和卓越用户体验。 |

## 一、设计哲学与原则

AIBUBB 的视觉设计旨在创造一个**既专业系统化，又生动有趣**的学习环境。设计应遵循以下核心原则：

1.  **用户中心**: 界面清晰直观，交互流畅自然，符合微信小程序用户习惯，降低学习成本。
2.  **趣味与探索**: 融入轻量、积极的视觉元素和动效（如泡泡/星星交互），激发用户好奇心和学习动力。
3.  **清晰与一致**: 保持色彩、字体、图标、布局和交互模式的高度一致性，建立统一的品牌感知。
4.  **轻盈与现代**: 采用现代设计语言，如毛玻璃效果 (`glass-card`)、平滑过渡动画，营造轻快、科技感的氛围。
5.  **保留与优化**: **（核心）** 所有视觉优化和新设计都应在现有页面实现的基础上进行，尊重并保留原有的核心视觉布局和交互逻辑，重点进行体验提升和规范统一。
6.  **(新增/强调) 性能优先**: 所有视觉设计（尤其是动效和复杂布局）必须优先考虑性能，确保在各种设备上流畅运行，避免卡顿和过度消耗资源。

## 二、核心视觉风格

*   **整体感觉**: 现代、简洁、富有科技感，同时带有一定的亲和力和趣味性。
*   **界面模式**:
    *   支持**亮色 (Light Mode)** 和 **暗色 (Dark Mode)** 两种主题模式，用户可切换，并提供良好的视觉对比度。(`profile/index.wxml` 已有切换开关)。**需确保两种模式下色彩对比度均符合无障碍标准。**
    *   首页 (`index`) 支持至少两种核心交互视觉风格：**泡泡 (Bubble)** 和 **星星 (Star)**，提供不同的探索体验。(`index/index.wxml` 已有切换逻辑)。**动画实现需严格进行性能测试与优化。**
*   **关键元素**:
    *   **卡片 (Card)**: 作为信息承载的主要容器，广泛应用于计划、模板、记录等列表展示。应具有清晰的边界、适当的圆角和内边距。
    *   **毛玻璃效果 (Glassmorphism)**: 在用户中心 (`profile`) 等页面已使用 (`glass-card`)，**应适度使用，并关注其对性能的影响，特别是在低端设备上。**
    *   **动态交互**: 首页的 Canvas 动画（泡泡/星星）是核心视觉亮点，应保持流畅、自然、物理感。**必须进行性能优化，确保流畅、自然、物理感的同时，降低 CPU/GPU 消耗。**

## 三、色彩系统

需定义明确的亮色和暗色模式下的调色板。以下为初步建议，需设计团队进一步细化：

*   **亮色模式 (Light Mode)**:
    *   **主色调 (Primary)**: 科技蓝 (`#3A86FF` 或类似)，用于关键操作按钮、高亮状态、核心图标。
    *   **辅助色 (Secondary)**: 活力紫 (`#8338EC` 或类似)、清新绿 (`#34D399` 或类似)，用于次要按钮、标签、进度条、图表等，区分信息。
    *   **强调色 (Accent)**: 暖橙 (`#FB8500` 或类似)，用于特别提示、警告或需要用户注意的元素。
    *   **中性色 (Neutral)**: 不同灰阶 (`#FFFFFF`, `#F3F4F6`, `#9CA3AF`, `#4B5563`, `#1F2937`)，用于背景、边框、文本。
    *   **状态色 (Status)**: 成功 (绿色系)、失败/警告 (红色/橙色系)、信息 (蓝色系)。

*   **暗色模式 (Dark Mode)**:
    *   **主背景**: 深灰 (`#1F2937` 或类似)。
    *   **卡片/次级背景**: 较浅的灰色 (`#374151` 或类似)。
    *   **主色调**: 保持或调整为更亮的蓝色，确保对比度。
    *   **辅助色/强调色**: 使用更鲜艳、饱和度更高的版本，在深色背景中突出。
    *   **中性色**: 反转灰阶使用，白色或浅灰色作为主要文本颜色。
    *   **状态色**: 保持语义，但可能需要调整亮度和饱和度以适应暗色背景。

*   **泡泡/星星颜色**: 在 Canvas 中可使用更丰富、渐变的色彩方案，与主题或标签内容关联，增加视觉吸引力。**可考虑定义几套预设的、与主题色系和谐搭配的渐变色方案。**

*   **(新增) 色彩规范与变量**:
    *   **必须定义明确的色彩 Token/变量** (例如在全局 CSS 或 WXSS 文件中定义 `--primary-color-light`, `--text-color-primary-dark` 等)，方便统一管理和主题切换。
    *   **严格遵循定义的调色板**，避免随意使用未定义的颜色。
    *   **提供渐变色使用指南**，规范渐变角度、色彩搭配和应用场景。

## 四、字体排版

*   **主字体**:
    *   **中文**: PingFang SC (iOS/macOS 默认)、Microsoft YaHei (Windows 默认) 或其他清晰易读的无衬线体。
    *   **英文/数字**: SF Pro Text (iOS/macOS)、Roboto (Android) 或 Inter 等现代无衬线体。
*   **字号层级**:
    *   **大标题 (Page Title/Hero)**: 约 20-24px (40-48rpx), 加粗。
    *   **卡片标题/重要信息 (Card Title/Key Info)**: 约 16-18px (32-36rpx), 加粗或中等。
    *   **正文/列表项 (Body/List Item)**: 约 14-15px (28-30rpx), 常规。
    *   **辅助文字/说明 (Caption/Helper Text)**: 约 12-13px (24-26rpx), 常规或灰色。
*   **字重**: 主要使用 Regular 和 Medium/Semibold，避免过多层级。
*   **行高**: 保持在 1.5-1.7 倍字号，确保阅读舒适性。
*   **颜色**: 亮色模式下主文本为深灰色，暗色模式下主文本为浅灰色/白色。辅助文本使用较浅的灰色。
*   **(新增) 排版规范与变量**:
    *   **推荐使用相对单位** (如 `rpx`) 定义字号和间距，以适应不同屏幕尺寸。
    *   **定义统一的类型层级 (Type Scale)** 并赋予变量名 (如 `--font-size-h1`, `--line-height-body`)，确保全局字体层级一致性。

## 五、图标系统

*   **风格**: 简洁、线性、表意明确。保持统一的线条粗细和圆角风格。(`profile/index.wxml` 中已使用 `/assets/icons/new/` 路径下的图标，应以此为基础)。
*   **来源**: **必须使用统一的图标库** (例如，确定选用 Remix Icon 或 Feather Icons)，或**维护一个严格规范的自定义图标库**。禁止混合使用不同风格的图标。
*   **颜色**: 通常使用中性色（灰），在激活或需要强调时使用主色或辅助色。**需定义图标在不同状态（默认、激活、禁用）下的颜色规范。**
*   **尺寸**: 定义常用尺寸规范（如 16x16, 20x20, 24x24px），并**明确各尺寸图标的应用场景**（例如：24px 用于底部导航，20px 用于列表项，16px 用于文本内联）。

## 六、布局与栅格

*   **原则**: 简洁、有序、呼吸感。
*   **间距**: **强制使用统一的间距规范** (例如基于 4px 或 8px 的倍数)，并**定义为变量** (如 `--space-xs: 4px`, `--space-sm: 8px`, `--space-md: 16px`) 应用于 `margin`, `padding` 等。
*   **对齐**: 强调左对齐为主，保持视觉流的顺畅。
*   **布局模式**:
    *   **卡片布局**: 用于列表展示（学习计划、模板、笔记等），确保卡片内部信息层级清晰。
    *   **列表布局**: 用于设置菜单等条目。
    *   **瀑布流**: 用于广场 (`square`) 的内容展示。
    *   **全屏画布**: 用于首页 (`index`) 的核心交互区。
*   **(新增) 响应式考量**: 虽然是小程序，但仍需考虑不同屏幕宽度下的布局微调，确保信息展示清晰。

## 七、核心交互元素与动效

*   **按钮 (Button)**:
    *   主操作按钮使用主色调填充。
    *   次要按钮可使用线框或次级颜色填充。
    *   **明确定义并视觉区分不同状态**（Default, Hover/Pressed, Disabled, Loading）的样式。
*   **输入框 (Input)**: 简洁的线框或浅底色，聚焦时**必须有清晰、即时的视觉反馈**（如边框变色）。
*   **选择器 (Selector)**: 如 `tag-scroll`，应有清晰的选中状态和流畅的滚动交互。
*   **加载状态 (Loading)**: **强制使用统一的加载模式**：列表或卡片使用**骨架屏 (Skeleton Screen)**，页面级或独立操作使用指定的**加载指示器 (Spinner)**。加载反馈需及时。
*   **动效 (Motion)**:
    *   **页面切换**: 使用 subtle 的淡入淡出或侧滑效果。**保持简洁，避免炫技**。
    *   **泡泡/星星**: 核心动效，应流畅、自然，模拟物理效果（漂浮、碰撞、合并动画）。**核心动效必须经过性能分析和优化，利用硬件加速（如 CSS Transform/Opacity 优先），模拟物理效果需自然且资源消耗可控。**
    *   **微交互**: 对用户的常见操作（如点赞、切换开关、展开/折叠）提供即时、轻量的视觉反馈（如状态变化、小动画）。**提供即时 (<100ms) 且轻量的视觉反馈**，避免干扰用户流程。
    *   **加载反馈**: 加载数据时，使用骨架屏或 Loading 组件占位，避免界面跳动。使用骨架屏或 Loading 组件**平滑过渡**，减少界面跳动。

## 八、关键页面视觉应用 (V2.0 重点优化)

*   **首页 (`index`)**:
    *   **Canvas 区域**: 视觉焦点。泡泡/星星效果需精致、吸引人，色彩丰富且与内容有关联（如关联标签或主题色）。动画应流畅、自然，模拟物理效果（漂浮、碰撞、合并动画），营造探索乐趣。**动画效果需精致，但必须进行性能剖析和针对性优化**。
    *   **背景**: 根据亮/暗模式调整，可考虑使用微妙的渐变或纹理增加深度，衬托 Canvas 区域。
    *   **核心交互循环**:
        1.  **内容呈现**: 画布上动态展示少量（6个）泡泡/星星，代表用户当前活跃学习计划中当天的核心学习任务（练习、观点、笔记），内容来源与 `daily_content` 及 `daily_content_relation` 关联。
        2.  **任务触发**: 用户点击一个泡泡/星星。
        3.  **任务执行 (模态弹窗)**: 弹出一个模态窗口展示对应的学习任务：
            *   **观点 (Insight)**: 直接在弹窗内展示观点内容，用户阅读后关闭弹窗即视为完成。
            *   **练习 (Exercise)**: 在弹窗内展示练习题目和交互控件（如输入框、选择按钮），用户完成提交并获得反馈后视为完成。
            *   **笔记 (Note)**: 在弹窗内直接展示笔记内容（支持富文本、图片和滚动查看长内容）。**为保持交互一致性，笔记阅读在弹窗内完成。完成条件简化为以下方式之一（需最终确定）：**
                *   **方案 N1 (滚动到底)**: 用户将弹窗内容滚动到底部后，视为完成。
                *   **方案 N2 (确认按钮)**: 提供一个明确的“完成阅读”按钮，用户点击后视为完成。可选：此按钮可在用户打开弹窗一定时间后（如 15-20 秒，无需强制显示计时器）才变为可用状态，以鼓励阅读。
            **弹窗内的任务交互需精心设计，确保简洁易用。**
        4.  **完成反馈**: 任务完成后，对应的泡泡/星星在画布上播放消失动画。
        5.  **即时奖励**: 调用后端接口，记录 `user_content_progress` 状态，更新用户积分/经验 (`user.exp_points`) 和计划进度 (`learning_plan.progress`)。
        6.  **阶段性奖励**: 当画布上当前批次的所有泡泡/星星都被消除后，触发特殊的"清屏"奖励视觉效果（如勋章动画），并可能记录相关成就。
    *   **UI 元素**: 新手引导层、加载/错误状态提示等需与整体风格统一。
    *   **底部悬浮按钮功能**:
        *   **现状**: `index.wxml` 中存在一个带脉冲效果的悬浮按钮 (`floating-button`)，其具体功能待定。
        *   **构想**: 用于"切换下一组任务"，以应对用户对当前任务不感兴趣或每日任务总量较多的情况。
        *   **设计挑战**: 此功能与"清屏奖励"机制存在潜在冲突。若允许用户在未完成当前组时切换，则清屏奖励难以触发。
        *   **待定方案**:
            *   **方案 A (保留清屏奖励)**: 按钮仅在当前组任务全部完成后激活，点击加载下一组任务（若有）。图标可为"加载更多"或"下一组"。
            *   **方案 B (优先选择权，调整奖励)**: 按钮始终可用，点击可切换至下一组未完成的任务。放弃清屏奖励，或将其调整为与每日总完成度挂钩。需明确被跳过任务的状态处理（隐藏或标记为 skipped）。图标可为"换一批"或"下一组"。
            *   **方案 C (简化设计)**: 移除此按钮，首页仅展示固定数量（如当天核心任务）的泡泡，完成即止。将任务分页或切换逻辑移至其他页面（如学习计划详情页）。
        *   **V2.0 决策**: **确定采用方案 A (保留清屏奖励)**。该悬浮按钮功能定为"**加载下一组任务**"，仅在当前画布上的泡泡/星星全部完成后才激活。按钮图标应明确传达"下一组"或类似含义。此设计强化了完成当前任务的动机和清屏奖励机制。

*   **学习 (`learn`)**:
    *   **页面定位**: 作为用户的**学习管理中心**，聚焦于**学习计划的创建、管理和模板资源的发现**，而非直接的内容消费或历史回顾。
    *   **核心视图与功能**:
        1.  **学习计划管理 (默认视图)**:
            *   以卡片列表 (`module-list`) 展示用户的所有 `learning_plan`（进行中、未完成、已完成等）。
            *   卡片需清晰展示关键信息：标题、封面（若有）、关联主题（图标/颜色）、进度 (`progress`)、状态 (`status`)。
            *   提供**创建新计划**入口 (如顶部的 "+" 按钮)。此入口将启动 AI 辅助创建流程。
            *   提供**查看计划详情**入口，导航至 `plan-detail` 页面。
            *   保留或优化**编辑/删除**计划的交互（如滑动操作按钮，删除需二次确认）。
            *   包含友好的空状态提示，引导用户创建第一个计划（或触发首次使用流程）。
        2.  **模版资源发现**: (通过顶部的 `view-toggle` 切换)
            *   以卡片列表 (`template-list`) 展示可用的 `learning_template`，区分官方和第三方来源。
            *   卡片展示模板核心信息：标题、封面、主题、难度、评分、价格/免费标识。
            *   提供"使用模板"创建计划的入口，同样会结合 AI 进行个性化调整。
            *   **未来规划**: 此视图将作为**模板市场**的基础，需考虑加入搜索、筛选（按主题、难度、评分等）、排序功能，并为创作者信息、评价体系预留设计空间。**(新增) 此视图未来作为模板市场，设计上需预留搜索框、筛选条件（主题、难度等 Chips 或下拉菜单）、排序选项、创作者信息和评分展示的空间。**
    *   **AI 赋能的计划创建**: 点击"创建新计划"或"使用模板"后，应启动一个专门的流程（可能在新页面 `create-plan` 或 `plan-generating`)：
        *   引导用户输入学习需求/目标（问答式、表单或自由文本）。
        *   调用 AI 服务生成个性化学习计划预览。
        *   提供用户调整和确认计划的界面。
        **(新增) AI 创建流程需独立设计 UX，确保引导清晰、步骤简洁、用户可预览和调整生成的计划，并有明确的进度反馈。**
    *   **首次使用体验 (Onboarding)**:
        *   当用户首次进入或没有任何学习计划时，触发特殊引导流程。
        *   通过简洁交互询问用户的核心需求或痛点。
        *   调用 AI 快速生成一个**简单的 3 日趣味学习计划**，旨在让用户快速体验核心学习闭环并获得初步成就感。
    *   **视觉与交互**: 保持卡片设计的清晰和一致性，视图切换平滑，加载和空状态友好。AI 创建流程的界面需简洁易懂。**(新增) 计划管理与模板发现之间的视图切换 (`view-toggle`) 动画需平滑（如淡入淡出或轻微位移），避免生硬跳转。**
    *   **移除视图**: "练习记录"视图从此页面的主切换视图中移除，其功能归入"我的"页面。


*   **广场 (`square`)**:
    *   **页面定位**: 作为与用户学习计划动态关联的**个性化内容发现区域**，而非简单的内容聚合。当前聚焦于公开的图文笔记 (`note`)，未来计划扩展支持视频等更多形式。
    *   **核心交互与布局**:
        1.  **顶部标签滚动选择器 (`tag-scroll`)**:
            *   横向滚动展示标签，用户可滑动选择感兴趣的主题。
            *   **中心高亮**: 视觉上突出当前选中的标签（如放大、特殊样式）。
            *   **默认"推荐"状态**: 初始加载时，默认选中"推荐"。此状态下，下方内容展示与用户**当前活跃学习计划核心标签**最相关的公开笔记。
            *   **标签切换**: 用户选择特定标签后，下方内容区动态更新，仅展示与该标签关联的公开笔记。
            *   数据来源需结合 `tag_category`, `tag` 以及用户的 `learning_plan` 和 `plan_tag`。
            **(新增) 交互需优化：保证滑动流畅，中心高亮/选中状态的视觉差异要足够明显，标签的触摸区域要足够大。**
        2.  **内容瀑布流 (`waterfall-content`)**:
            *   根据顶部选中的标签（或"推荐"逻辑）动态加载并展示相关内容。
            *   内容来源多样化，主要包括：
                *   **用户创建的公开笔记 (`note` where `is_public=true`)**。
                *   **AI 持续生成的笔记**: AI 基于用户需求、学习计划关联的标签、甚至搜索和推理，动态创造相关笔记内容。
            *   笔记卡片应简洁展示核心信息：封面图（若有）、标题、作者头像昵称（或标识 AI 生成）、点赞数等。
            *   卡片设计需考虑未来兼容视频等其他内容类型，并能清晰区分不同来源的内容（如通过小图标或文字标识）。**(新增) 笔记卡片必须在视觉上清晰区分"用户生成"和"AI 生成"的内容（可通过特定图标、标签或背景色等方式）。**
            *   需实现高效的瀑布流加载机制（如无限滚动或分页）。**(新增) 性能要求**: **瀑布流实现必须考虑性能：采用虚拟列表或按需加载技术，优化图片（懒加载、WebP 格式、合理尺寸），并提供平滑的骨架屏加载状态。**
        3.  **右下角发布按钮 (FAB)**:
            *   设置一个悬浮的发布按钮，鼓励用户贡献内容。
            *   点击后跳转至笔记创建页面 (`pages/note/edit.wxml`)。
            *   若用户在点击发布按钮前已在顶部选中了特定标签，则创建笔记时应**自动关联该标签**。
    *   **视觉与交互**: `tag-scroll` 的中心高亮和滚动交互需平滑自然。瀑布流卡片视觉风格统一，加载流畅。FAB 按钮样式遵循全局规范。**(新增) AI 生成内容的质量需有预期管理机制，如明确标识或提供反馈渠道。**


*   **我的 (`profile`)**:
    *   **页面定位**: 作为用户的**个人中心和控制塔**，聚合身份进展、成就荣誉、数据回顾、内容管理、社交连接和账户设置。
    *   **核心布局与元素**:
        1.  **顶部用户信息区 (`profile-header`)**:
            *   保留毛玻璃效果、主题切换开关、用户头像和昵称。
            *   **增强状态显示**: 显著展示当前**用户等级名称 (`level.name`)**，并配合醒目的**经验值进度条** (展示 `user.exp_points` 及下一级所需 `level.required_exp`)。**等级名称和经验进度条需视觉突出，增强成长感。**
            *   可补充展示 1-2 个核心统计数据，如"总学习天数"。
        2.  **(可选) 中部核心功能区 (`core-functions`)**:
            *   评估保留"我的学习"、"我的收藏"、"我的发布"等最高频快捷入口，或将其整合入下方菜单。
        3.  **底部菜单列表区 (`menu-section`)**:
            *   采用可折叠的菜单区块，逻辑化组织各项功能入口：**(新增) 各区块的折叠/展开交互需流畅，并考虑状态记忆。**
            *   **区块一：学习与成就**:
                *   `学习计划`: 入口，导航至计划管理视图/页面。
                *   `学习记录`: (**整合原"历史记录"和"练习记录"**) 入口，导航至详细记录页 (练习、观点、笔记等)。
                *   `学习统计`: 入口，导航至图表化统计页面。
                *   `我的徽章`: (**新增**) 入口，导航至徽章墙页面 (`user_badge`)。
                *   `成就墙`: (**新增**) 入口，导航至成就列表页面 (`user_achievement`)。
            *   **区块二：内容与创作**:
                *   `笔记管理`: 入口，导航至笔记列表及回收站功能页面。
                *   `我的收藏`: 入口，导航至收藏列表页。
                *   `我的发布`: 入口，导航至用户发布内容列表。
            *   **区块三：社交与分享**:
                *   `邀请好友`: (现有功能)
                *   `学习排行`: (现有功能)
                *   `分享主页/成就`: (**新增/细化**) 提供分享个人学习进展或成就的入口。
            *   **区块四：设置与帮助**:
                *   `账号设置`: 入口，包含**个人信息管理**（编辑头像、昵称等）。
                *   `通知设置`: 入口，管理通知偏好。
                *   `隐私设置`: (**新增/细化**) 入口，管理个人隐私选项。
                *   `主题偏好`: 入口或开关，切换亮/暗模式。
                *   (保留关于、客服、手机绑定、退出等)
    *   **视觉与交互**: 整体保持 `glass-card` 和清晰的信息层级。图标风格统一。菜单折叠/展开动画流畅。顶部经验条和等级显示需突出成长感。**(新增) 需管理好信息密度，利用折叠菜单、留白和清晰的排版层级避免页面拥挤。**
    *   **子页面**: 徽章墙、成就墙页面需注重视觉展示效果。学习记录页面需信息清晰。设置类页面需简洁易用。**(新增) 新增的徽章墙、成就墙页面需专门设计，注重视觉吸引力和荣誉感（可考虑网格布局、动态效果等）。学习统计页面应使用简洁易懂的图表组件（如柱状图、折线图）进行数据可视化。**


## 九、组件视觉规范

所有复用组件（基础组件、业务组件如 `PlanCard`, `NoteCard`, 以及自定义组件如 `tag-scroll`, `waterfall-content`）都必须严格遵循本文档定义的色彩、字体、间距、图标和交互规范，确保应用整体的视觉一致性。**必须建立共享的组件库（代码层面或设计工具层面），并为每个组件提供清晰的亮色/暗色模式定义和状态规范。**

## 十、可访问性

*   **颜色对比度**: 确保文本与背景、关键 UI 元素之间有足够的对比度（遵循 WCAG AA 级标准）。**亮色和暗色模式下均需满足 WCAG AA 级标准。**
*   **字体大小**: 提供足够的字体大小，避免使用过小的字号。
*   **触摸目标**: 确保按钮、列表项等可点击元素有足够大的触摸区域（建议不小于 44x44px）。
*   **(新增)** 焦点管理：确保交互元素（按钮、输入框等）在获得焦点时有清晰的视觉指示。

## 十一、未来展望

本视觉设计文档 V2.0 为修订稿，需要在后续的设计和开发过程中不断迭代和完善。设计团队应与开发团队紧密合作，将规范落实到具体的组件和页面实现中，并通过用户反馈持续优化视觉体验。**(新增) 建议建立定期的设计评审机制，确保新功能和迭代遵循本文档规范。**
