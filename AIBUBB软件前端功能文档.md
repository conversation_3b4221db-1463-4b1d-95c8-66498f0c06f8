
# AIBUBB 软件前端功能文档

## 1. 引言

AIBUBB 的前端致力于为用户打造一个既专业系统化，又生动有趣的个性化学习环境。前端功能的设计核心在于直观的交互、清晰的信息呈现、流畅的动态效果以及高度的视觉一致性，旨在激发用户的学习动力，提升学习体验，并支持用户在微信小程序生态内的使用习惯。

## 2. 通用前端功能与规范

这些功能和规范贯穿整个应用，确保体验的统一性和高质量：

*   **2.1 主题模式**
    *   支持**亮色 (Light Mode)** 和 **暗色 (Dark Mode)** 两种主题，用户可在“我的”页面进行切换。
    *   系统采用全局 CSS/WXSS 变量进行色彩管理，确保两种模式下色彩对比度均符合无障碍标准。
*   **2.2 核心视觉风格与布局**
    *   **整体风格**：现代、简洁、富有科技感，兼具亲和力和趣味性。
    *   **卡片式设计**：广泛应用于学习计划、模板、笔记、记录等信息的列表展示，具有清晰边界、圆角和内边距。
    *   **毛玻璃效果 (Glassmorphism)**：在“我的”页面等特定区域适度使用 (`glass-card`)，注重性能。
    *   **统一间距规范**：基于 4px 或 8px 的倍数定义全局间距变量 (如 `--space-xs`, `--space-sm`)，应用于内外边距。
    *   **响应式考量**：虽然是小程序，但会考虑不同屏幕宽度下的布局微调，确保信息展示清晰。
*   **2.3 字体与图标**
    *   **字体规范**：采用清晰易读的无衬线中英文字体，定义统一的字号层级 (如大标题、卡片标题、正文、辅助文字) 和行高，使用相对单位 `rpx`。
    *   **图标系统**：使用统一风格的线性图标库 (基于 `/assets/icons/new/` 或选定库如 Remix Icon)，明确图标在不同状态（默认、激活、禁用）下的颜色和尺寸规范（如 16px, 20px, 24px）。
*   **2.4 核心交互元素**
    *   **按钮 (Button)**：区分主操作按钮 (主色调填充)、次要按钮 (线框或次级颜色)，并明确定义不同状态 (Default, Hover/Pressed, Disabled, Loading) 的样式。
    *   **输入框 (Input)**：简洁设计，聚焦时有清晰、即时的视觉反馈 (如边框变色)。
    *   **选择器 (Selector)**：如 `tag-scroll`，具有清晰的选中状态和流畅的滚动交互。
*   **2.5 加载状态与反馈**
    *   **统一加载模式**：列表或卡片使用**骨架屏 (Skeleton Screen)**，页面级或独立操作使用指定的**加载指示器 (Spinner)**，确保加载反馈及时且平滑过渡，减少界面跳动。
    *   **错误状态提示**：提供统一风格的错误提示。
*   **2.6 动效 (Motion)**
    *   **页面切换**：采用微妙的淡入淡出或侧滑效果，保持简洁。
    *   **微交互**：对用户常见操作 (如点赞、切换开关、展开/折叠列表) 提供即时 (<100ms) 且轻量的视觉反馈。
*   **2.7 组件化与一致性**
    *   所有复用组件 (基础组件、业务组件如 `PlanCard`, `NoteCard`, 自定义组件如 `tag-scroll`) 严格遵循设计规范，确保亮/暗模式和各种状态下的视觉一致性。
*   **2.8 可访问性 (Accessibility)**
    *   **颜色对比度**：确保文本与背景、关键 UI 元素间对比度符合 WCAG AA 级标准。
    *   **字体大小**：提供足够的字体大小。
    *   **触摸目标**：按钮、列表项等可点击元素具有足够大的触摸区域 (不小于 44x44px)。
    *   **焦点管理**：交互元素获得焦点时有清晰的视觉指示。

## 3. 核心页面功能详述

### 3.1 首页 (`index`)

首页是用户核心学习任务的交互入口，提供动态和趣味性的体验。

*   **3.1.1 Canvas 动态交互区**
    *   支持**泡泡 (Bubble)** 和 **星星 (Star)** 两种核心视觉风格切换。
    *   Canvas 动画效果精致、吸引人，色彩丰富,为了保持风格统一，色彩随机显示，不会与任务内容（如标签）关联。
    *   动画模拟流畅、自然的物理效果 (如漂浮、碰撞、完成任务后消失)，营造探索乐趣，并经过性能优化。
*   **3.1.2 核心学习任务呈现与交互**
    *   画布上动态展示少量 (如6个) 泡泡/星星，代表用户当前活跃学习计划中当天的核心学习任务 (练习、观点、笔记)。
    *   用户点击泡泡/星星触发任务。
    *   任务在**模态弹窗 (Modal)** 内执行：
        *   **观点 (Insight)**: 直接在弹窗内展示观点内容，用户阅读后关闭弹窗即视为完成。
        *   **练习 (Exercise)**: 在弹窗内展示练习题目和交互控件 (如输入框、选择按钮)，用户完成提交并获得反馈后视为完成。
        *   **笔记 (Note)**: 在弹窗内直接展示笔记内容 (支持富文本、图片和滚动查看长内容)。完成条件可为滚动到底部或点击“完成阅读”按钮。
*   **3.1.3 任务完成与奖励反馈**
    *   每个任务完成后，对应的泡泡/星星在画布上播放消失动画。
    *   前端可能实时展示用户积分/经验 (`user.exp_points`) 的更新。
    *   当画布上当前批次的所有泡泡/星星都被消除后，触发特殊的"清屏"奖励视觉效果 (如勋章动画)。
*   **3.1.4 底部悬浮按钮**
    *   功能为 "**加载下一组任务**"。
    *   仅在当前画布上的泡泡/星星全部完成后才激活。
    *   按钮图标明确传达"下一组"或类似含义。

### 3.2 学习 (`learn`)

此页面作为用户的学习管理中心，聚焦于学习计划的创建、管理和模板资源的发现。

*   **3.2.1 学习计划管理 (默认视图)**
    *   以卡片列表 (`module-list`) 展示用户的所有学习计划 (`learning_plan`)。
    *   卡片清晰展示计划标题、封面 (若有)、关联主题 (图标/颜色)、进度 (`progress`)、状态 (`status`)。
    *   提供**创建新计划**的入口 (如顶部的 "+" 按钮)，启动 AI 辅助创建流程。
    *   提供**查看计划详情**的入口，导航至计划详情页 (`plan-detail`)。
    *   支持计划的**编辑/删除**操作 (如通过滑动操作按钮，删除操作需要二次确认)。
    *   包含友好的空状态提示，引导用户创建第一个计划或触发首次使用流程。
*   **3.2.2 模板资源发现 (通过顶部 `view-toggle` 切换)**
    *   以卡片列表 (`template-list`) 展示可用的学习模板 (`learning_template`)，区分官方和第三方来源。
    *   卡片展示模板核心信息：标题、封面、主题、难度、评分、价格/免费标识。
    *   提供 "**使用模板**" 创建计划的入口，结合 AI 进行个性化调整。
    *   **模板市场功能预留**：未来此视图将扩展为模板市场，前端需预留搜索框、筛选条件 (按主题、难度等，可使用 Chips 或下拉菜单)、排序选项、创作者信息和评分展示的空间。
    *   用户可以查看模板的评价和评分。
*   **3.2.3 AI 辅助的计划创建流程**
    *   在独立页面 (`create-plan` 或 `plan-generating`) 或流程中进行。
    *   引导用户输入学习需求/目标 (通过问答式、表单或自由文本输入)。
    *   展示 AI 生成的个性化学习计划预览。
    *   提供用户调整和确认计划的界面，并有明确的进度反馈。
*   **3.2.4 首次使用体验 (Onboarding)**
    *   当用户首次进入小程序时，或没有任何学习计划时，触发特殊引导流程。
    *   通过简洁交互询问用户的核心需求或痛点。
    *   调用 AI 快速生成一个简单的3日趣味学习计划，让用户快速体验核心学习闭环。
*   **3.2.5 视图切换**
    *   学习计划管理与模板资源发现之间的视图切换 (`view-toggle`) 需平滑过渡 (如淡入淡出或轻微位移)。

### 3.3 广场 (`square`)

作为与用户学习计划动态关联的个性化内容发现区域，当前聚焦于公开的图文笔记。

*   **3.3.1 顶部标签滚动选择器 (`tag-scroll`)**
    *   横向滚动展示标签，用户可滑动选择感兴趣的主题。
    *   视觉上突出当前选中的标签 (如放大、特殊样式)，标签的触摸区域足够大。
    *   默认选中 "**推荐**" 状态，展示与用户当前活跃学习计划核心标签最相关的公开笔记。
    *   用户选择特定标签后，下方内容区动态更新，仅展示与该标签关联的公开笔记。
*   **3.3.2 内容瀑布流 (`waterfall-content`)**
    *   根据顶部选中的标签（或"推荐"逻辑）动态加载并展示相关内容。
    *   内容来源包括用户创建的公开笔记和 AI 持续生成的笔记。
    *   **笔记卡片**：简洁展示核心信息，如封面图 (若有)、标题、作者头像昵称 (或标识 AI 生成)、点赞数等。
    *   **来源区分**：笔记卡片在视觉上清晰区分"用户生成"和"AI 生成"的内容 (可通过特定图标、标签或背景色等方式)。
    *   **加载机制**：实现高效的瀑布流加载机制，如无限滚动或分页加载，采用虚拟列表或按需加载技术，优化图片 (懒加载、WebP 格式、合理尺寸)，并提供平滑的骨架屏加载状态。
*   **3.3.3 发布按钮 (FAB - Floating Action Button)**
    *   右下角设置一个悬浮的发布按钮，鼓励用户贡献内容。
    *   点击后跳转至笔记创建/编辑页面 (`pages/note/edit.wxml`)。
    *   若用户在点击发布按钮前已在顶部选中了特定标签，则创建笔记时应自动关联该标签。
*   **3.3.4 社区互动**
    *   用户可以对笔记进行**点赞**。
    *   用户可以对笔记进行**评论**和回复。
    *   用户可以**分享**笔记。

### 3.4 我的 (`profile`)

作为用户的个人中心和控制塔，聚合身份进展、成就荣誉、数据回顾、内容管理、社交连接和账户设置。

*   **3.4.1 顶部用户信息区 (`profile-header`)**
    *   采用毛玻璃效果 (`glass-card`)。
    *   展示用户头像、昵称。
    *   显著展示当前**用户等级名称** (`level.name`)。
    *   配合醒目的**经验值进度条** (展示 `user.exp_points` 及达到下一级所需经验 `level.required_exp`)。
    *   可补充展示1-2个核心统计数据，如"总学习天数"。
    *   包含**主题切换开关** (亮色/暗色)。
*   **3.4.2 菜单列表区 (`menu-section`)**
    *   采用可折叠的菜单区块，逻辑化组织各项功能入口，折叠/展开交互流畅，并考虑状态记忆。
    *   **区块一：学习与成就**
        *   `学习计划`: 入口，导航至“学习”页面的计划管理视图或独立计划列表页。
        *   `学习记录`: 入口，导航至详细记录页面，整合原"历史记录"和"练习记录" (可查看练习、观点、笔记等学习历史)。
        *   `学习统计`: 入口，导航至图表化统计页面，展示如学习时长、完成度等数据。
        *   `我的徽章`: 入口，导航至徽章墙页面 (`user_badge`)，展示用户获得的徽章。
        *   `成就墙`: 入口，导航至成就列表页面 (`user_achievement`)，展示用户达成的成就。
    *   **区块二：内容与创作**
        *   `笔记管理`: 入口，导航至用户个人笔记列表页面，支持查看、编辑、删除笔记，可能包含回收站功能。
        *   `我的收藏`: 入口，导航至用户收藏的内容列表页。
        *   `我的发布`: 入口，导航至用户发布的内容列表 (如公开的笔记)。
    *   **区块三：社交与分享**
        *   `邀请好友`: 功能入口。
        *   `学习排行`: 功能入口，展示排行榜。
        *   `分享主页/成就`: 提供分享个人学习主页、进展或特定成就的入口。
        *   `我的关注/粉丝`: (若实现) 入口，查看关注列表和粉丝列表。
    *   **区块四：设置与帮助**
        *   `账号设置`: 入口，包含**个人信息管理** (编辑头像、昵称、性别等)。
        *   `通知设置`: 入口，管理各类通知的接收偏好。
        *   `隐私设置`: 入口，管理个人资料可见性、学习数据分享等隐私选项。
        *   `主题偏好`: 入口或开关，切换亮/暗模式 (可与顶部开关联动或为其主要设置地)。
        *   保留 `关于我们`、`联系客服`、`手机号绑定`、`退出登录` 等常规入口。
*   **3.4.3 子页面视觉与交互**
    *   **徽章墙、成就墙页面**：注重视觉展示效果和荣誉感，可采用网格布局、动态效果等。
    *   **学习记录页面**：信息展示清晰，易于回顾。
    *   **学习统计页面**：使用简洁易懂的图表组件 (如柱状图、折线图) 进行数据可视化。
    *   **设置类页面**：界面简洁易用，选项清晰。

## 4. 其他重要前端功能

*   **4.1 笔记创建/编辑页面 (`pages/note/edit.wxml`)**
    *   提供富文本编辑器，支持文本格式化、图片插入。
    *   允许用户输入笔记标题、选择关联标签。
    *   设置笔记是否公开。
    *   保存、发布笔记。
*   **4.2 AI 辅助创建计划的独立流程页面**
    *   清晰的步骤引导。
    *   友好的用户输入界面 (问答、表单、自由文本)。
    *   计划预览与调整界面。
    *   明确的进度反馈和确认操作。
*   **4.3 模态弹窗 (Modal) 交互**
    *   广泛用于执行任务 (首页泡泡任务)、信息确认 (如删除操作)、展示少量信息等。
    *   设计遵循统一风格，确保内容清晰，操作便捷。
*   **4.4 通知系统展示**
    *   可能存在独立的通知中心页面或列表，展示用户收到的各类通知 (点赞、评论、关注、成就达成、系统消息等)。
    *   通知项应包含关键信息和跳转链接 (如跳转到被评论的笔记)。
    *   支持标记已读/未读。
*   **4.5 内容详情页**
    *   例如学习计划详情页 (`plan-detail`)，展示计划的详细信息、包含的每日内容、标签、进度等。
    *   笔记详情页，展示笔记完整内容、作者信息、评论列表等。
*   **4.6 用户反馈功能**
    *   用户可能可以对标签、内容（如图书馆的模板或AI生成的内容）进行反馈。

## 5. 总结

AIBUBB 的前端功能旨在通过精心设计的用户界面和流畅的交互体验，有效地支撑起整个学习生态系统的运作。从个性化的首页动态任务，到结构化的学习计划管理，再到内容丰富的广场发现，以及全面的个人中心，前端的每一个模块都力求清晰、易用且富有吸引力，最终帮助用户达成学习目标并享受学习过程。