# AIBUBB API 端点列表

本文档列出了AIBUBB应用的所有API端点，包括URL、HTTP方法、认证要求和简要描述。

**重要说明：** 本项目API同时存在V1和V2版本。V1版本提供基础功能，V2版本提供增强功能（如软删除、批量操作等）。

## 认证相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| POST | `/api/v1/auth/login` | 不需要 | 微信登录 |
| POST | `/api/v1/auth/register/phone` | 不需要 | 手机号注册 |
| POST | `/api/v1/auth/login/phone` | 不需要 | 手机号登录 |
| GET | `/api/v1/auth/user` | 需要 | 获取用户信息 |

## 学习计划相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| POST | `/api/v1/learning-plans` | 需要 | 创建学习计划 |
| GET | `/api/v1/learning-plans` | 需要 | 获取用户的学习计划列表 |
| GET | `/api/v1/learning-plans/:id` | 可选 | 获取学习计划详情 |
| PUT | `/api/v1/learning-plans/:id/activate` | 需要 | 切换当前学习计划 |
| GET | `/api/v1/learning-plans/system/default` | 可选 | 获取系统默认学习计划 |

## 标签相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/tags/learning-plans/:id/tags` | 需要 | 获取学习计划的标签 |
| GET | `/api/v1/tags/current-plan/tags` | 需要 | 获取当前学习计划的标签 |
| PUT | `/api/v1/tags/reorder` | 需要 | 调整标签顺序 |
| GET | `/api/v1/tags/tags/:id` | 需要 | 获取标签详情 |
| PUT | `/api/v1/tags/tags/:id/weight` | 需要 | 更新标签权重 |
| PUT | `/api/v1/tags/tags/:id/verify` | 需要 | 更新标签验证状态 |
| POST | `/api/v1/tags/tags/:id/increment-usage` | 需要 | 增加标签使用次数 |
| GET | `/api/v1/tags/system/default/tags` | 可选 | 获取系统默认学习计划的标签 |

## 笔记相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/notes/user` | 需要 | 获取用户的笔记列表 |
| GET | `/api/v1/notes/tags/:tagId/notes` | 需要 | 获取标签下的笔记列表 |
| GET | `/api/v1/notes/:id` | 需要 | 获取笔记详情 |
| POST | `/api/v1/notes` | 需要 | 创建笔记 |
| PUT | `/api/v1/notes/:id` | 需要 | 更新笔记 |
| DELETE | `/api/v1/notes/:id` | 需要 | 删除笔记 |

## 观点相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/tags/:tagId/insights` | 需要 | 获取标签下的观点列表 |
| GET | `/api/v1/insights/:id` | 需要 | 获取观点详情 |
| POST | `/api/v1/insights` | 需要 | 创建观点 |
| PUT | `/api/v1/insights/:id` | 需要 | 更新观点 |

## 练习相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/tags/:tagId/exercises` | 需要 | 获取标签下的练习列表 |
| GET | `/api/v1/exercises/:id` | 需要 | 获取练习详情 |
| POST | `/api/v1/exercises` | 需要 | 创建练习 |
| PUT | `/api/v1/exercises/:id` | 需要 | 更新练习 |

## 泡泡相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/bubble/content` | 需要 | 获取泡泡内容 |
| POST | `/api/v1/bubble/interaction` | 需要 | 记录泡泡交互 |

## 广场相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/square/tags` | 不需要 | 获取广场标签列表 |
| GET | `/api/v1/square/notes` | 不需要 | 获取广场笔记列表 |
| GET | `/api/v1/square/notes/:id` | 可选 | 获取广场笔记详情 |

## 用户互动相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| POST | `/api/v1/notes/:noteId/like` | 需要 | 点赞笔记 |
| DELETE | `/api/v1/notes/:noteId/like` | 需要 | 取消点赞笔记 |
| GET | `/api/v1/notes/:noteId/comments` | 需要 | 获取笔记评论列表 |
| POST | `/api/v1/notes/:noteId/comments` | 需要 | 评论笔记 |
| GET | `/api/v1/share/note/:noteId` | 需要 | 生成笔记分享信息 |
| GET | `/api/v1/share/tag/:tagId` | 需要 | 生成标签分享信息 |

## 统计相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/statistics/learning` | 需要 | 获取学习统计数据 |
| GET | `/api/v1/statistics/daily` | 需要 | 获取每日学习记录 |

## 主题相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/themes` | 不需要 | 获取主题列表 |
| GET | `/api/v1/themes/:id` | 不需要 | 获取主题详情 |

## AI相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| POST | `/api/v1/ai/chat` | 需要 | 与AI对话 |
| POST | `/api/v1/ai/generate-plan` | 需要 | 生成学习计划 |

## 标签分类相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/themes/:themeId/categories` | 需要 | 获取主题下的标签分类 |
| GET | `/api/v1/categories/:id` | 需要 | 获取标签分类详情 |

## 标签同义词相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/tags/:tagId/synonyms` | 需要 | 获取标签的同义词列表 |
| GET | `/api/v1/synonyms/search` | 需要 | 搜索标签同义词 |

## 系统相关

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v1/health` | 不需要 | 健康检查 |
| GET | `/api-docs` | 不需要 | Swagger UI API文档 |
| GET | `/redoc` | 不需要 | ReDoc API文档 |
| GET | `/swagger.json` | 不需要 | Swagger规范JSON |

## V2版本API

以下是V2版本API的端点列表，主要增加了软删除、恢复功能和批量操作等特性。

### 标签相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v2/tags` | 需要 | 获取标签列表 |
| GET | `/api/v2/tags/:id` | 需要 | 获取标签详情 |
| POST | `/api/v2/tags` | 需要 | 创建标签 |
| PUT | `/api/v2/tags/:id` | 需要 | 更新标签 |
| DELETE | `/api/v2/tags/:id` | 需要 | 删除标签 |
| DELETE | `/api/v2/tags/:id/soft-delete` | 需要 | 软删除标签 |
| PUT | `/api/v2/tags/:id/restore` | 需要 | 恢复已删除的标签 |
| GET | `/api/v2/tags/deleted` | 需要 | 获取已删除的标签列表 |
| GET | `/api/v2/tags/current-plan/tags` | 需要 | 获取当前学习计划的标签 |
| GET | `/api/v2/tags/system/default/tags` | 可选 | 获取系统默认标签 |

### 学习计划相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v2/learning-plans` | 需要 | 获取学习计划列表 |
| GET | `/api/v2/learning-plans/:id` | 需要 | 获取学习计划详情 |
| POST | `/api/v2/learning-plans` | 需要 | 创建学习计划 |
| PUT | `/api/v2/learning-plans/:id` | 需要 | 更新学习计划 |
| DELETE | `/api/v2/learning-plans/:id` | 需要 | 删除学习计划 |
| DELETE | `/api/v2/learning-plans/:id/soft-delete` | 需要 | 软删除学习计划 |
| PUT | `/api/v2/learning-plans/:id/restore` | 需要 | 恢复已删除的学习计划 |
| GET | `/api/v2/learning-plans/deleted` | 需要 | 获取已删除的学习计划列表 |
| PUT | `/api/v2/learning-plans/:id/activate` | 需要 | 激活学习计划 |
| GET | `/api/v2/learning-plans/system/default` | 可选 | 获取系统默认学习计划 |

### 笔记相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v2/notes` | 需要 | 获取笔记列表 |
| GET | `/api/v2/notes/:id` | 需要 | 获取笔记详情 |
| POST | `/api/v2/notes` | 需要 | 创建笔记 |
| PUT | `/api/v2/notes/:id` | 需要 | 更新笔记 |
| DELETE | `/api/v2/notes/:id` | 需要 | 删除笔记 |
| DELETE | `/api/v2/notes/:id/soft-delete` | 需要 | 软删除笔记 |
| PUT | `/api/v2/notes/:id/restore` | 需要 | 恢复已删除的笔记 |
| GET | `/api/v2/notes/deleted` | 需要 | 获取已删除的笔记列表 |
| GET | `/api/v2/notes/user` | 需要 | 获取用户的笔记列表 |
| GET | `/api/v2/notes/tags/:tagId/notes` | 需要 | 获取标签下的笔记列表 |

### 洞察相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v2/insights` | 需要 | 获取洞察列表 |
| GET | `/api/v2/insights/:id` | 需要 | 获取洞察详情 |
| POST | `/api/v2/insights` | 需要 | 创建洞察 |
| PUT | `/api/v2/insights/:id` | 需要 | 更新洞察 |
| DELETE | `/api/v2/insights/:id` | 需要 | 删除洞察 |
| DELETE | `/api/v2/insights/:id/soft-delete` | 需要 | 软删除洞察 |
| PUT | `/api/v2/insights/:id/restore` | 需要 | 恢复已删除的洞察 |
| GET | `/api/v2/insights/deleted` | 需要 | 获取已删除的洞察列表 |
| GET | `/api/v2/insights/tags/:tagId/insights` | 需要 | 获取标签下的洞察列表 |

### 练习相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v2/exercises` | 需要 | 获取练习列表 |
| GET | `/api/v2/exercises/:id` | 需要 | 获取练习详情 |
| POST | `/api/v2/exercises` | 需要 | 创建练习 |
| PUT | `/api/v2/exercises/:id` | 需要 | 更新练习 |
| DELETE | `/api/v2/exercises/:id` | 需要 | 删除练习 |
| DELETE | `/api/v2/exercises/:id/soft-delete` | 需要 | 软删除练习 |
| PUT | `/api/v2/exercises/:id/restore` | 需要 | 恢复已删除的练习 |
| GET | `/api/v2/exercises/deleted` | 需要 | 获取已删除的练习列表 |
| GET | `/api/v2/exercises/tags/:tagId/exercises` | 需要 | 获取标签下的练习列表 |

### 主题相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v2/themes` | 不需要 | 获取主题列表 |
| GET | `/api/v2/themes/:id` | 不需要 | 获取主题详情 |
| POST | `/api/v2/themes` | 需要 | 创建主题 |
| PUT | `/api/v2/themes/:id` | 需要 | 更新主题 |
| DELETE | `/api/v2/themes/:id` | 需要 | 删除主题 |
| DELETE | `/api/v2/themes/:id/soft-delete` | 需要 | 软删除主题 |
| PUT | `/api/v2/themes/:id/restore` | 需要 | 恢复已删除的主题 |
| GET | `/api/v2/themes/deleted` | 需要 | 获取已删除的主题列表 |

### 每日内容相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| GET | `/api/v2/daily-contents` | 需要 | 获取每日内容列表 |
| GET | `/api/v2/daily-contents/:id` | 需要 | 获取每日内容详情 |
| POST | `/api/v2/daily-contents` | 需要 | 创建每日内容 |
| PUT | `/api/v2/daily-contents/:id` | 需要 | 更新每日内容 |
| DELETE | `/api/v2/daily-contents/:id` | 需要 | 删除每日内容 |
| DELETE | `/api/v2/daily-contents/:id/soft-delete` | 需要 | 软删除每日内容 |
| PUT | `/api/v2/daily-contents/:id/restore` | 需要 | 恢复已删除的每日内容 |
| GET | `/api/v2/daily-contents/deleted` | 需要 | 获取已删除的每日内容列表 |

### 批量操作相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| POST | `/api/v2/batch/tags/soft-delete` | 需要 | 批量软删除标签 |
| POST | `/api/v2/batch/tags/restore` | 需要 | 批量恢复标签 |
| POST | `/api/v2/batch/notes/soft-delete` | 需要 | 批量软删除笔记 |
| POST | `/api/v2/batch/notes/restore` | 需要 | 批量恢复笔记 |
| POST | `/api/v2/batch/insights/soft-delete` | 需要 | 批量软删除洞察 |
| POST | `/api/v2/batch/insights/restore` | 需要 | 批量恢复洞察 |
| POST | `/api/v2/batch/exercises/soft-delete` | 需要 | 批量软删除练习 |
| POST | `/api/v2/batch/exercises/restore` | 需要 | 批量恢复练习 |
| POST | `/api/v2/batch/learning-plans/soft-delete` | 需要 | 批量软删除学习计划 |
| POST | `/api/v2/batch/learning-plans/restore` | 需要 | 批量恢复学习计划 |

### 清理相关 (V2)

| 方法 | URL | 认证 | 描述 |
|------|-----|------|------|
| POST | `/api/v2/cleanup/expired-deleted` | 需要 | 清理过期的已删除资源 |
| GET | `/api/v2/cleanup/status` | 需要 | 获取清理状态 |