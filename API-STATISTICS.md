# 统计模块API文档

## 概述

统计模块API提供了用户学习活动的记录、统计和分析功能，是评估用户学习效果和平台使用情况的重要组成部分。

## API端点

### 获取学习统计数据

获取用户的学习统计数据，包括学习天数、连续天数、完成练习数等。

**请求**

```
GET /api/v1/statistics/learning
```

**请求头**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

**响应**

```json
{
  "success": true,
  "data": {
    "totalStudyDays": 15,
    "currentStreak": 3,
    "longestStreak": 7,
    "completedExercises": 25,
    "viewedInsights": 42,
    "createdNotes": 10,
    "bubbleInteractions": 15,
    "totalTimeSpent": 120,
    "activePlans": 2,
    "completedPlans": 3,
    "activityStats": {
      "login": 10,
      "view_exercise": 20,
      "complete_exercise": 25,
      "view_insight": 42,
      "create_note": 10,
      "bubble_interaction": 15
    }
  }
}
```

### 获取每日学习记录

获取用户的每日学习记录，包括学习时间、完成练习数等。

**请求**

```
GET /api/v1/statistics/daily
```

**请求头**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

**请求参数**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| startDate | string | 否 | 开始日期（YYYY-MM-DD） |
| endDate | string | 否 | 结束日期（YYYY-MM-DD） |
| limit | number | 否 | 每页记录数（默认30） |
| offset | number | 否 | 偏移量（默认0） |

**响应**

```json
{
  "success": true,
  "data": {
    "records": [
      {
        "date": "2023-06-15",
        "timeSpent": 30,
        "exercisesCompleted": 2,
        "insightsViewed": 5,
        "notesCreated": 1,
        "bubbleInteractions": 3,
        "hasActivity": true
      },
      {
        "date": "2023-06-16",
        "timeSpent": 45,
        "exercisesCompleted": 3,
        "insightsViewed": 7,
        "notesCreated": 2,
        "bubbleInteractions": 4,
        "hasActivity": true
      }
    ],
    "count": 2,
    "limit": 30,
    "offset": 0,
    "totalPages": 1
  }
}
```

### 记录学习活动

记录用户的学习活动，如查看练习、完成练习等。

**请求**

```
POST /api/v1/statistics/activities
```

**请求头**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Authorization | string | 是 | Bearer {token} |
| Content-Type | string | 是 | application/json |

**请求体**

```json
{
  "activityType": "complete_exercise",
  "planId": 789,
  "contentType": "exercise",
  "contentId": 456,
  "duration": 300
}
```

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| activityType | string | 是 | 活动类型，可选值：login, view_exercise, complete_exercise, view_insight, create_note, like_note, comment_note, bubble_interaction, share_content |
| planId | number | 否 | 学习计划ID |
| contentType | string | 否 | 内容类型，可选值：exercise, insight, note, tag, plan, bubble |
| contentId | number | 否 | 内容ID |
| duration | number | 否 | 持续时间（秒） |
| details | object | 否 | 活动详情 |

**响应**

```json
{
  "success": true,
  "data": {
    "activityId": 123,
    "activityType": "complete_exercise",
    "createdAt": "2023-06-15T08:00:00Z"
  },
  "message": "学习活动已记录"
}
```

### 获取学习活动列表

获取用户的学习活动列表，支持分页和过滤。

**请求**

```
GET /api/v1/statistics/activities
```

**请求头**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

**请求参数**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| activityType | string | 否 | 活动类型 |
| startDate | string | 否 | 开始日期（YYYY-MM-DD） |
| endDate | string | 否 | 结束日期（YYYY-MM-DD） |
| contentType | string | 否 | 内容类型 |
| planId | number | 否 | 学习计划ID |
| page | number | 否 | 页码（默认1） |
| pageSize | number | 否 | 每页记录数（默认20） |

**响应**

```json
{
  "success": true,
  "data": {
    "activities": [
      {
        "id": 123,
        "activityType": "complete_exercise",
        "contentType": "exercise",
        "contentId": 456,
        "planId": 789,
        "duration": 300,
        "createdAt": "2023-06-15T08:00:00Z"
      },
      {
        "id": 124,
        "activityType": "view_insight",
        "contentType": "insight",
        "contentId": 457,
        "planId": 789,
        "duration": 180,
        "createdAt": "2023-06-15T09:00:00Z"
      }
    ],
    "count": 50,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  }
}
```

### 获取学习概览

获取用户的学习概览，包括统计数据和最近趋势。

**请求**

```
GET /api/v1/statistics/overview
```

**请求头**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

**响应**

```json
{
  "success": true,
  "data": {
    "statistics": {
      "totalStudyDays": 15,
      "currentStreak": 3,
      "longestStreak": 7,
      "completedExercises": 25,
      "viewedInsights": 42,
      "createdNotes": 10,
      "bubbleInteractions": 15,
      "totalTimeSpent": 120,
      "activePlans": 2,
      "completedPlans": 3,
      "activityStats": {
        "login": 10,
        "view_exercise": 20,
        "complete_exercise": 25,
        "view_insight": 42,
        "create_note": 10,
        "bubble_interaction": 15
      }
    },
    "recentTrend": [
      {
        "date": "2023-06-10",
        "timeSpent": 0,
        "exercisesCompleted": 0,
        "insightsViewed": 0,
        "notesCreated": 0,
        "hasActivity": false
      },
      {
        "date": "2023-06-11",
        "timeSpent": 15,
        "exercisesCompleted": 1,
        "insightsViewed": 2,
        "notesCreated": 0,
        "hasActivity": true
      },
      // ... 更多日期
    ]
  }
}
```

### 获取学习趋势

获取用户的学习趋势数据。

**请求**

```
GET /api/v1/statistics/trend
```

**请求头**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

**请求参数**

| 名称 | 类型 | 必填 | 描述 |
|------|------|------|------|
| days | number | 否 | 天数（默认30，最大90） |

**响应**

```json
{
  "success": true,
  "data": {
    "trend": [
      {
        "date": "2023-05-17",
        "timeSpent": 0,
        "exercisesCompleted": 0,
        "insightsViewed": 0,
        "notesCreated": 0,
        "hasActivity": false
      },
      {
        "date": "2023-05-18",
        "timeSpent": 30,
        "exercisesCompleted": 2,
        "insightsViewed": 5,
        "notesCreated": 1,
        "hasActivity": true
      },
      // ... 更多日期
    ]
  }
}
```

## 错误响应

所有API端点在发生错误时都会返回以下格式的响应：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {} // 可选，错误详情
  }
}
```

### 常见错误码

| 错误码 | 描述 |
|--------|------|
| UNAUTHORIZED | 未授权访问 |
| BAD_REQUEST | 请求参数错误 |
| NOT_FOUND | 资源不存在 |
| SERVER_ERROR | 服务器内部错误 |

## 数据模型

### 活动类型

| 活动类型 | 描述 |
|----------|------|
| login | 登录 |
| view_exercise | 查看练习 |
| complete_exercise | 完成练习 |
| view_insight | 查看观点 |
| create_note | 创建笔记 |
| like_note | 点赞笔记 |
| comment_note | 评论笔记 |
| bubble_interaction | 泡泡互动 |
| share_content | 分享内容 |

### 内容类型

| 内容类型 | 描述 |
|----------|------|
| exercise | 练习 |
| insight | 观点 |
| note | 笔记 |
| tag | 标签 |
| plan | 学习计划 |
| bubble | 泡泡 |
