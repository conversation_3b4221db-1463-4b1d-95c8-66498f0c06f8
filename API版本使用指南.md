# API版本使用指南

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-06 |
| 最后更新 | 2025-05-06 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档提供了AIBUBB项目中API版本的使用指南，帮助开发人员决定何时使用V1版本，何时使用V2版本的API。AIBUBB项目目前同时支持V1和V2两个版本的API，它们有不同的功能和适用场景。

## 2. 版本差异

### 2.1 功能差异

| 功能 | V1版本 | V2版本 |
|------|--------|--------|
| 基础CRUD操作 | ✅ | ✅ |
| 软删除功能 | ❌ | ✅ |
| 恢复已删除资源 | ❌ | ✅ |
| 查看已删除资源 | ❌ | ✅ |
| 批量操作 | ❌ | ✅ |
| 定期清理机制 | ❌ | ✅ |

### 2.2 实现差异

| 特性 | V1版本 | V2版本 |
|------|--------|--------|
| 控制器实现 | 直接实现 | 使用依赖注入 |
| 架构风格 | 混合 | 分层架构 |
| 错误处理 | 基础 | 增强 |
| 参数验证 | 基础 | 增强 |
| 响应格式 | 标准格式 | 标准格式（增加软删除信息） |

### 2.3 URL路径差异

V1版本和V2版本的API使用不同的URL路径前缀：

- V1版本：`/api/v1/{resource}`
- V2版本：`/api/v2/{resource}`

例如：
- V1版本获取标签列表：`GET /api/v1/tags`
- V2版本获取标签列表：`GET /api/v2/tags`

## 3. 版本选择指南

### 3.1 何时使用V1版本

在以下情况下，应该使用V1版本的API：

1. **兼容性要求**：需要与旧版本客户端保持兼容性
2. **简单场景**：只需要基础的CRUD操作，不需要软删除和批量操作功能
3. **性能考虑**：在某些极端情况下，V1版本可能比V2版本性能更好（因为实现更简单）
4. **特定功能**：某些只在V1版本中存在的特定功能（如果有）

### 3.2 何时使用V2版本

在以下情况下，应该使用V2版本的API：

1. **需要软删除功能**：需要支持软删除和恢复功能
2. **需要批量操作**：需要批量处理多个资源
3. **需要查看已删除资源**：需要查看和管理已删除的资源
4. **新功能开发**：开发新功能时，应优先使用V2版本
5. **更好的架构**：需要更好的架构和依赖注入支持

### 3.3 迁移建议

对于新开发的功能，我们建议使用V2版本的API。对于现有功能，我们建议根据以下原则逐步迁移到V2版本：

1. **优先级**：先迁移重要和常用的功能
2. **依赖关系**：考虑功能之间的依赖关系，避免部分迁移导致的不一致
3. **测试覆盖**：确保迁移前有足够的测试覆盖，避免引入新问题
4. **平滑过渡**：在迁移期间，保持V1和V2版本的并行运行，确保平滑过渡

## 4. 版本使用示例

### 4.1 标签管理

#### V1版本（基础功能）

```javascript
// 获取标签列表
const getTags = async () => {
  try {
    const response = await fetch('/api/v1/tags');
    const data = await response.json();
    return data.success ? data.data.tags : [];
  } catch (error) {
    console.error('获取标签失败', error);
    return [];
  }
};

// 删除标签（永久删除）
const deleteTag = async (tagId) => {
  try {
    const response = await fetch(`/api/v1/tags/${tagId}`, {
      method: 'DELETE'
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('删除标签失败', error);
    return false;
  }
};
```

#### V2版本（软删除和批量操作）

```javascript
// 获取标签列表（包括未删除的标签）
const getTags = async () => {
  try {
    const response = await fetch('/api/v2/tags');
    const data = await response.json();
    return data.success ? data.data.tags : [];
  } catch (error) {
    console.error('获取标签失败', error);
    return [];
  }
};

// 获取已删除的标签列表
const getDeletedTags = async () => {
  try {
    const response = await fetch('/api/v2/tags/deleted');
    const data = await response.json();
    return data.success ? data.data.tags : [];
  } catch (error) {
    console.error('获取已删除标签失败', error);
    return [];
  }
};

// 软删除标签
const softDeleteTag = async (tagId) => {
  try {
    const response = await fetch(`/api/v2/tags/${tagId}/soft-delete`, {
      method: 'DELETE'
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('软删除标签失败', error);
    return false;
  }
};

// 恢复已删除的标签
const restoreTag = async (tagId) => {
  try {
    const response = await fetch(`/api/v2/tags/${tagId}/restore`, {
      method: 'PUT'
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('恢复标签失败', error);
    return false;
  }
};

// 批量软删除标签
const batchSoftDeleteTags = async (tagIds) => {
  try {
    const response = await fetch('/api/v2/batch/tags/soft-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ ids: tagIds })
    });
    const data = await response.json();
    return data.success ? data.data : null;
  } catch (error) {
    console.error('批量软删除标签失败', error);
    return null;
  }
};
```

### 4.2 学习计划管理

#### V1版本（基础功能）

```javascript
// 获取学习计划列表
const getLearningPlans = async () => {
  try {
    const response = await fetch('/api/v1/learning-plans');
    const data = await response.json();
    return data.success ? data.data.plans : [];
  } catch (error) {
    console.error('获取学习计划失败', error);
    return [];
  }
};

// 删除学习计划（永久删除）
const deleteLearningPlan = async (planId) => {
  try {
    const response = await fetch(`/api/v1/learning-plans/${planId}`, {
      method: 'DELETE'
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('删除学习计划失败', error);
    return false;
  }
};
```

#### V2版本（软删除和批量操作）

```javascript
// 获取学习计划列表（包括未删除的学习计划）
const getLearningPlans = async () => {
  try {
    const response = await fetch('/api/v2/learning-plans');
    const data = await response.json();
    return data.success ? data.data.plans : [];
  } catch (error) {
    console.error('获取学习计划失败', error);
    return [];
  }
};

// 获取已删除的学习计划列表
const getDeletedLearningPlans = async () => {
  try {
    const response = await fetch('/api/v2/learning-plans/deleted');
    const data = await response.json();
    return data.success ? data.data.plans : [];
  } catch (error) {
    console.error('获取已删除学习计划失败', error);
    return [];
  }
};

// 软删除学习计划
const softDeleteLearningPlan = async (planId) => {
  try {
    const response = await fetch(`/api/v2/learning-plans/${planId}/soft-delete`, {
      method: 'DELETE'
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('软删除学习计划失败', error);
    return false;
  }
};

// 恢复已删除的学习计划
const restoreLearningPlan = async (planId) => {
  try {
    const response = await fetch(`/api/v2/learning-plans/${planId}/restore`, {
      method: 'PUT'
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('恢复学习计划失败', error);
    return false;
  }
};
```

## 5. 版本共存策略

在AIBUBB项目中，V1和V2版本的API将共存一段时间，以确保平滑过渡。我们的版本共存策略如下：

1. **并行运行**：V1和V2版本的API将并行运行，确保现有客户端不受影响
2. **新功能优先V2**：新功能优先在V2版本中实现，必要时再向V1版本移植
3. **逐步迁移**：鼓励客户端逐步迁移到V2版本，但不强制
4. **版本监控**：监控V1和V2版本的使用情况，了解迁移进度
5. **长期支持**：承诺在一定时间内（至少6个月）继续支持V1版本

## 6. 未来计划

我们计划在未来进一步改进API版本管理：

1. **API网关**：引入API网关，统一管理不同版本的API
2. **版本自动化管理**：实现版本自动化管理工具，简化版本管理
3. **客户端适配器**：提供客户端适配器，简化客户端迁移
4. **版本弃用通知**：实现版本弃用通知机制，提前通知客户端
5. **统一到V2**：最终目标是统一到V2版本，减少维护负担

## 7. 结论

AIBUBB项目目前同时支持V1和V2两个版本的API。V1版本提供基础功能，V2版本提供增强功能（如软删除、批量操作等）。在选择使用哪个版本时，应该考虑功能需求、兼容性要求和架构需求。对于新功能开发，我们建议优先使用V2版本的API。
