# AIBUBB API 设计规范

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-06 |
| 最后更新 | 2025-05-06 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档定义了AIBUBB应用的API设计规范，包括命名约定、参数格式、响应格式和错误处理等。这些规范旨在确保API设计的一致性、可维护性和可用性。

## 2. API设计原则

### 2.1 RESTful设计原则

- 使用HTTP方法表示操作：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 使用URL路径表示资源：`/api/v1/resources`
- 使用查询参数表示过滤、排序和分页：`/api/v1/resources?filter=value&sort=field&page=1`
- 使用HTTP状态码表示操作结果：200（成功）、201（创建成功）、400（请求错误）、401（未授权）、404（资源不存在）、500（服务器错误）

### 2.2 API-First设计原则

- 先设计API，再实现功能
- API设计应该独立于实现细节
- API应该是自描述的，通过API文档可以了解API的功能和使用方式
- API应该是版本化的，以支持向后兼容性

### 2.3 安全性原则

- 所有API通信使用HTTPS加密
- 敏感数据（如密码）在传输和存储时都经过加密
- 使用JWT进行认证和授权
- 实施适当的限流策略，防止滥用

## 3. 命名约定

### 3.1 URL路径命名

- 使用kebab-case（短横线命名法）：`/learning-plans`而非`/learningPlans`或`/learning_plans`
- 资源名称使用复数形式：`/tags`而非`/tag`
- 子资源使用嵌套路径：`/tags/:tagId/exercises`
- 操作使用动词：`/learning-plans/:id/activate`
- 版本号放在URL路径的开头：`/api/v1/resources`

示例：
```
/api/v1/learning-plans
/api/v1/tags/:tagId/exercises
/api/v1/learning-plans/:id/activate
/api/v2/tags/:id/soft-delete
```

### 3.2 查询参数命名

- 使用camelCase（小驼峰命名法）：`?pageSize=10`而非`?page_size=10`或`?PageSize=10`
- 分页参数使用`page`和`pageSize`
- 过滤参数使用资源属性名：`?status=active`
- 排序参数使用`sortBy`和`sortOrder`
- 搜索参数使用`q`或`search`

示例：
```
/api/v1/learning-plans?page=1&pageSize=10&status=active
/api/v1/tags?sortBy=name&sortOrder=asc
/api/v1/notes?search=关键词
```

### 3.3 请求体字段命名

- 使用camelCase（小驼峰命名法）：`{ "title": "学习计划" }`
- 字段名与数据库字段名保持一致（转换为camelCase）
- 嵌套对象使用嵌套结构：`{ "userInfo": { "nickname": "用户昵称" } }`
- 布尔值字段使用`is`或`has`前缀：`isPublic`、`hasAttachment`

示例：
```json
{
  "title": "提升与伴侣的沟通能力",
  "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
  "targetDays": 14,
  "themeId": 1,
  "isPublic": true,
  "tags": [
    {
      "name": "倾听",
      "relevanceScore": 0.95
    }
  ]
}
```

### 3.4 响应字段命名

- 使用camelCase（小驼峰命名法）：`{ "id": 1, "title": "学习计划" }`
- 字段名与数据库字段名保持一致（转换为camelCase）
- 嵌套对象使用嵌套结构：`{ "user": { "id": 1, "nickname": "用户昵称" } }`
- 日期时间字段使用ISO 8601格式：`"createdAt": "2023-06-15T08:00:00Z"`
- ID字段使用`id`或`xxxId`：`id`、`userId`、`tagId`

示例：
```json
{
  "id": 101,
  "title": "提升与伴侣的沟通能力",
  "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
  "targetDays": 14,
  "completedDays": 5,
  "progress": 35,
  "status": "in_progress",
  "startDate": "2023-06-15",
  "endDate": "2023-06-29",
  "isCurrent": true,
  "createdAt": "2023-06-15T08:00:00Z",
  "theme": {
    "id": 1,
    "name": "人际沟通"
  }
}
```

## 4. 参数格式

### 4.1 分页参数

- 使用`page`和`pageSize`参数
- `page`从1开始
- `pageSize`默认为20，最大为50
- 响应中包含分页信息：`total`、`page`、`pageSize`、`totalPages`

示例：
```
/api/v1/learning-plans?page=1&pageSize=10
```

响应中的分页信息：
```json
{
  "success": true,
  "data": {
    "plans": [...],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 10,
      "totalPages": 10
    }
  }
}
```

### 4.2 过滤参数

- 使用资源属性名作为参数名
- 支持精确匹配和范围查询
- 多值使用逗号分隔
- 日期范围使用`startDate`和`endDate`

示例：
```
/api/v1/learning-plans?status=active,completed
/api/v1/statistics/activities?startDate=2023-01-01&endDate=2023-12-31
```

### 4.3 排序参数

- 使用`sortBy`和`sortOrder`参数
- `sortBy`指定排序字段
- `sortOrder`可选值为`asc`和`desc`，默认为`asc`
- 多字段排序使用逗号分隔

示例：
```
/api/v1/tags?sortBy=name&sortOrder=asc
/api/v1/notes?sortBy=createdAt,title&sortOrder=desc,asc
```

## 5. 响应格式

### 5.1 成功响应

所有API的成功响应都应该使用以下格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

- `success`：布尔值，表示操作是否成功，成功响应中始终为`true`
- `message`：字符串，表示操作结果的描述，可选
- `data`：对象，包含响应数据

对于列表类API，`data`中应包含列表数据和分页信息：

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 10,
      "totalPages": 10
    }
  }
}
```

### 5.2 错误响应

所有API的错误响应都应该使用以下格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      // 错误详情
    }
  }
}
```

- `success`：布尔值，表示操作是否成功，错误响应中始终为`false`
- `error`：对象，包含错误信息
  - `code`：字符串，表示错误码
  - `message`：字符串，表示错误描述
  - `details`：对象，包含错误详情，可选

### 5.3 HTTP状态码

API应该使用适当的HTTP状态码表示操作结果：

- 200 OK：请求成功
- 201 Created：资源创建成功
- 400 Bad Request：请求参数错误
- 401 Unauthorized：未授权访问
- 403 Forbidden：禁止访问
- 404 Not Found：资源不存在
- 422 Unprocessable Entity：数据验证失败
- 500 Internal Server Error：服务器内部错误

## 6. 错误处理

### 6.1 错误码

错误码应该使用大写下划线命名法（UPPER_SNAKE_CASE），并且应该具有描述性：

- `BAD_REQUEST`：请求参数错误
- `UNAUTHORIZED`：未授权访问
- `FORBIDDEN`：禁止访问
- `RESOURCE_NOT_FOUND`：资源不存在
- `VALIDATION_ERROR`：数据验证失败
- `SERVER_ERROR`：服务器内部错误

### 6.2 错误消息

错误消息应该简洁明了，提供具体的错误原因，避免技术术语，并在适当的情况下提供解决建议：

- 简洁明了：`"标题不能为空"`而非`"字段验证失败：标题"`
- 提供具体原因：`"学习计划不存在或已被删除"`而非`"资源不存在"`
- 避免技术术语：`"无法连接到服务器"`而非`"TCP连接超时"`
- 提供解决建议：`"请检查网络连接后重试"`

### 6.3 错误详情

对于验证错误，应该提供每个字段的错误信息：

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": {
      "title": "标题不能为空",
      "targetDays": "目标天数必须是正整数",
      "themeId": "主题不存在"
    }
  }
}
```

## 7. 版本管理

### 7.1 版本命名

- 使用语义化版本号：主版本号.次版本号.修订号
- 主版本号：不兼容的API变更（如V1、V2）
- 次版本号：向后兼容的功能性变更（如V1.1、V1.2）
- 修订号：向后兼容的问题修复（如V1.1.1、V1.1.2）

### 7.2 版本控制方式

- 使用URL路径中的版本号：`/api/v1/resources`
- 主版本号放在URL路径中，次版本号和修订号通过文档和发布说明管理
- 当API发生不兼容的变更时，增加主版本号

### 7.3 版本兼容性

- 新版本应该尽可能保持向后兼容性
- 不删除字段，只添加新字段
- 不改变字段类型和语义
- 新增的请求字段应该是可选的
- 为新增字段提供合理的默认值

## 8. API文档

### 8.1 文档格式

- 使用OpenAPI 3.0规范编写API文档
- 使用Swagger UI和ReDoc展示API文档
- 文档应该包含API的功能描述、请求参数、响应格式和示例

### 8.2 文档内容

- API端点：URL路径、HTTP方法、认证要求
- 请求参数：路径参数、查询参数、请求体
- 响应格式：成功响应、错误响应
- 示例：请求示例、响应示例
- 错误码：可能的错误码和描述

### 8.3 文档更新

- 文档应该与代码同步更新
- 使用Swagger注释自动生成文档
- 在代码审查中检查文档更新

## 9. 安全考虑

### 9.1 认证和授权

- 使用JWT进行认证
- 令牌有效期为24小时
- 刷新令牌有效期为7天
- 使用RBAC（基于角色的访问控制）进行授权

### 9.2 数据保护

- 所有API通信使用HTTPS加密
- 敏感数据（如密码）在传输和存储时都经过加密
- 使用参数验证防止注入攻击
- 使用CORS（跨域资源共享）控制跨域访问

### 9.3 限流和防滥用

- 实施适当的限流策略
- 普通请求：每个IP每15分钟最多100个请求
- 登录请求：每个IP每小时最多20次登录尝试
- 使用验证码防止暴力破解

## 10. 最佳实践

### 10.1 API设计最佳实践

- 使用名词而非动词表示资源
- 使用复数形式表示资源集合
- 使用HTTP方法表示操作
- 使用查询参数进行过滤、排序和分页
- 使用嵌套资源表示关系

### 10.2 性能最佳实践

- 使用分页减少响应大小
- 使用部分响应减少不必要的数据传输
- 使用缓存减少重复请求
- 使用压缩减少传输大小
- 使用异步处理长时间运行的操作

### 10.3 可维护性最佳实践

- 遵循一致的命名约定
- 提供详细的API文档
- 使用版本控制支持API演进
- 实施自动化测试确保API质量
- 监控API使用情况，及时发现问题
