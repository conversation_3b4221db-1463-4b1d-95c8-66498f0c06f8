# 微信小程序TabBar图标转换与当前配置说明

## 问题说明
微信小程序的tabBar配置要求图标必须是PNG、JPG或JPEG格式，不支持SVG格式。

## 历史操作 (可能已过时)

之前可能通过以下步骤将SVG转换为PNG：
1. 打开本地的 `icon-converter.html` 文件。
2. 点击"转换"按钮生成PNG。
3. 将生成的PNG文件保存到 `assets/icons/` 目录下（如 `home.png`, `learn.png` 等）。

**注意：以下描述的是当前实际生效的配置。**

## 当前实际配置

项目当前 `app.json` 中的 `tabBar` 配置使用的是位于 **`assets/icons/new/`** 目录下的 **PNG** 图标文件。这些图标文件可能由其他方式生成（例如使用 `generate_icons.py` 或 `create_custom_icon.py` 脚本）。

当前使用的图标文件包括：
- `assets/icons/new/list.png` (首页默认)
- `assets/icons/new/list-active.png` (首页选中)
- `assets/icons/new/book.png` (学习默认)
- `assets/icons/new/book-active.png` (学习选中)
- `assets/icons/new/grid.png` (广场默认)
- `assets/icons/new/grid-active.png` (广场选中)
- `assets/icons/new/user.png` (我的默认)
- `assets/icons/new/user-active.png` (我的选中)

请确保在修改 TabBar 或图标时，以 `app.json` 的实际配置和 `assets/icons/new/` 目录下的文件为准。

~~## TabBar配置~~
~~已更新`app.json`中的tabBar配置，更改图标路径为PNG格式。~~ (此句已合并到上方说明) 