# AIBUBB (AI互动泡泡)

## 项目概述

AIBUBB 是一个基于微信小程序的 AI 辅助学习平台，专注于提升用户的人际沟通能力。其核心理念是围绕用户个性化的**学习计划**，通过 AI 分析生成**标签**，并关联到**练习、观点、笔记**三种形式的学习内容。内容通过首页的**互动泡泡**和**广场社区**呈现，结合**学习统计**功能，旨在提供一个沉浸式、个性化、数据驱动的学习体验。

## 主要功能

*   创建和管理个性化学习计划
*   AI 驱动的标签生成与内容关联
*   互动式泡泡学习界面
*   社区广场内容发现与分享
*   学习进度跟踪与统计分析
*   用户注册、登录与个人中心

## 技术栈

本项目主要采用以下技术栈：

*   **后端:** Node.js (Express.js), MySQL (Sequelize ORM), Redis, JWT, Winston
*   **AI 服务:** 可配置 (通过 `AI_PROVIDER` 环境变量)，支持：
    *   字节跳动火山方舟 (默认 Provider: `bytedance`, 需 `ARK_API_KEY`, 可能使用 DeepSeek 等模型)
    *   阿里云百炼 (Provider: `aliyun`, 需 `DASHSCOPE_API_KEY`, 使用通义千问模型)
    *   腾讯混元 (Provider: `hunyuan`, 需 `HUNYUAN_API_KEY`)
*   **前端:** 微信小程序原生开发
*   **部署:** Docker, Docker Compose

更详细的技术架构、设计原则和数据流信息，请参考 [架构本质原理 (ARCHITECTURE-PRINCIPLES.md)](ARCHITECTURE-PRINCIPLES.md)。

## 项目结构

```
.
├── .git/               # Git 版本控制目录
├── .idea/              # IDE 配置目录 (可选)
├── backend/            # 后端 Node.js (Express) 服务代码
│   ├── config/         # 配置 (数据库, 日志, Swagger等)
│   ├── controllers/    # 控制器 (处理请求)
│   ├── middlewares/    # 中间件 (认证, 验证, 错误处理等)
│   ├── migrations/     # 数据库迁移脚本
│   ├── models/         # Sequelize 数据模型定义
│   ├── routes/         # API 路由定义
│   ├── scripts/        # 后端相关脚本 (AI测试, 初始化等)
│   ├── services/       # 业务逻辑服务 (AI, 学习计划等)
│   └── utils/          # 后端工具函数
├── pages/              # 微信小程序页面
├── components/         # 微信小程序自定义组件
├── custom-tab-bar/   # 微信小程序自定义 TabBar
├── utils/              # 微信小程序公共工具函数 (API请求, 存储等)
├── assets/             # 静态资源 (图标, 图片等)
├── scripts/            # 项目级脚本 (例如：图标生成, 容器启停等)
├── env_backup/         # 环境变量备份目录 (由脚本创建)
├── mysql-init/         # MySQL Docker 配置与初始化脚本
├── redis/              # Redis Docker 配置
├── logs/               # 日志文件目录
├── ai-test-results/    # AI 服务测试结果目录
├── __tests__/          # 测试文件目录 (单元/集成测试)
├── node_modules/       # Node.js 依赖
├── .cloudbase/         # 微信云开发相关配置 (可能未使用)
├── app.js              # 小程序应用入口逻辑
├── app.json            # 小程序全局配置 (页面, 窗口, TabBar等)
├── app.wxss            # 小程序全局样式
├── docker-compose.yml  # Docker Compose 配置文件
├── docker-start.sh     # 启动 Docker 服务的脚本
├── docker-stop.sh      # 停止 Docker 服务的脚本
├── docker-backup.sh    # 备份数据库的脚本
├── docker-restore.sh   # 恢复数据库的脚本
├── migrate-env.sh      # 迁移/更新环境变量脚本
├── validate-env.sh     # 验证环境变量脚本
├── package.json        # 项目依赖和脚本定义 (主要是后端和工具)
├── package-lock.json   # 锁定依赖版本
├── project.config.json # 微信开发者工具项目配置
├── project.private.config.json # 微信开发者工具私有配置
├── server.js           # 后端服务入口文件 (通常由 docker-compose 调用)
├── .gitignore          # Git 忽略文件配置
├── *.md                # 项目文档 (见下方文档索引)
└── ...                 # 其他配置文件、脚本或临时文件
```

## 环境要求

*   Docker
*   Docker Compose

## 快速启动

1.  **克隆仓库:**
    ```bash
    git clone [你的仓库地址]
    cd AIBUBB
    ```

2.  **配置环境:**
    *   在项目根目录创建或复制一份 `.env` 文件 (可参考 `.env.example` 或其他环境的文件)。
    *   **必需配置:**
        *   数据库密码: `DB_PASSWORD`, `DB_ROOT_PASSWORD`
        *   JWT 密钥: `JWT_SECRET`
        *   选择的 AI 服务商对应的 API Key (例如，若使用默认的 `bytedance`，则需配置 `ARK_API_KEY`)
    *   根据需要修改其他配置 (如 `PORT`, `DB_USER`, `DB_NAME`, `AI_PROVIDER` 等)。
    *   **(注意:** `docker-compose.yml` 会覆盖 `.env` 中的 `DB_HOST=mysql` 和 `REDIS_URL=redis://redis:6379` 以确保容器间通信，无需在 `.env` 中为 Docker 环境修改这些)。

3.  **启动服务:**
    *   确保 Docker 和 Docker Compose 已经运行。
    *   在项目根目录执行启动脚本：
      ```bash
      ./docker-start.sh
      ```
    *   脚本会自动创建所需目录并使用 `docker-compose up -d` 启动所有后端服务（Node API, MySQL, Redis）。

4.  **运行小程序:**
    *   使用微信开发者工具导入项目根目录。
    *   配置开发者工具以连接本地 API（默认 `http://localhost:9090/api/v1` 已在 `utils/api.js` 中配置好开发环境）。
    *   编译并运行小程序。

5.  **访问服务:**
    *   **API 服务:** `http://localhost:9090`
    *   **权威 API 文档 (推荐):**
        *   Swagger UI: `http://localhost:9090/api-docs`
        *   ReDoc: `http://localhost:9090/redoc`

## 停止服务

```bash
./docker-stop.sh
```

## 文档索引

**核心文档 (建议优先阅读):**

*   **[README.md](./README.md):** (本文档) 项目入口，提供概述、快速启动和文档导航。
*   **[项目架构说明 (PROJECT-ARCHITECTURE.md)](./PROJECT-ARCHITECTURE.md):** 通俗易懂地解释项目各组件之间的关系和数据流程，推荐新手开发者首先阅读。
*   **[架构本质原理 (ARCHITECTURE-PRINCIPLES.md)](./ARCHITECTURE-PRINCIPLES.md):** 深入阐述项目的核心设计理念、系统架构、功能模块关联、数据流动机制和技术选型。**强烈建议首先阅读此文档以理解项目全貌。**
*   **[数据库设计 (DATABASE-DESIGN.md)](./DATABASE-DESIGN.md):** 详细描述数据库的表结构、字段、关系和索引。
*   **[Docker 部署指南 (DOCKER-README.md)](./DOCKER-README.md):** 侧重于 Docker 环境的部署、维护、数据持久化、备份恢复、配置和故障排除。
*   **[Docker 开发指南 (DOCKER-DEVELOPMENT.md)](./DOCKER-DEVELOPMENT.md):** 侧重于开发者如何使用 Docker 环境进行日常开发。

**功能与模块文档:**

*   **[TabBar 图标说明 (README-TABBAR.md)](./README-TABBAR.md):** 解释了 TabBar 图标的处理方式和当前配置。**(可能需要代码验证)**
*   **[首页 Canvas 组件说明 (首页Canvas组件说明.md)](./首页Canvas组件说明.md):** 对首页核心交互组件的详细说明。**(已部分验证)**

**API 与开发规范:**

*   **[API 设计规范 (API-DESIGN.md)](./API-DESIGN.md):** API 设计的原则和规范。**(注意 V1/V2 版本说明)**
*   **[API 端点列表 (API-ENDPOINTS.md)](./API-ENDPOINTS.md):** **(已过时)** 请参考运行时 API 文档 (Swagger/ReDoc)。
*   **[Swagger 示例文档 (SWAGGER-EXAMPLES.md)](./SWAGGER-EXAMPLES.md):** 如何使用和编写 Swagger 注释的示例。
*   **运行时 API 文档:**
    *   Swagger UI: 访问 `/api-docs` (服务运行时)
    *   ReDoc: 访问 `/redoc` (服务运行时)
    *   **(推荐查看运行时文档获取最新 API 信息，但需注意其准确性依赖 `swagger.js` 配置和代码注释的同步维护)**

**性能、测试与维护:**

*   **[性能优化指南 (PERFORMANCE-OPTIMIZATION.md)](./PERFORMANCE-OPTIMIZATION.md):** 记录了已实施的性能优化措施。**(可能包含历史信息)**
*   **[AI 模型测试 (AI-MODEL-TESTING.md)](./AI-MODEL-TESTING.md):** AI 模型相关的测试说明。**(可能包含历史信息)**
*   **[AI 模型优化 (AI-MODEL-OPTIMIZATION.md)](./AI-MODEL-OPTIMIZATION.md):** AI 模型优化相关的记录。**(可能包含历史信息)**

**历史、变更与规划文档:**

*   **[Taro 迁移计划 (TARO-MIGRATION-GUIDE.md)](./TARO-MIGRATION-GUIDE.md):** **(仅供参考)** 描述了将项目迁移到 Taro 框架的计划，当前项目并未使用 Taro。
*   **[系统改进计划 (system-improvement-plan.md)](./system-improvement-plan.md):** (可能包含历史信息) 对系统未来改进的规划和思考。
*   **[重构分析 (refactoring-analysis.md)](./refactoring-analysis.md):** (可能包含历史信息) 代码重构相关的分析。
*   **[Phase 2 总结 (phase2-summary.md)](./phase2-summary.md):** (历史文档) 项目第二阶段的总结。
*   **[AI互动泡泡-商业计划书.md](./AI互动泡泡-商业计划书.md):** 项目的商业计划书。
*   **[架构图 (architecture-diagrams.md)](./architecture-diagrams.md):** 可能包含架构图的 Markdown 文件。
*   ~~[Statistics Migration Plan (statistics-migration-plan.md)](./statistics-migration-plan.md):~~ **(已删除 - 涉及旧表)**
*   ~~[Upgrade Report (upgrade_report.md)](./upgrade_report.md):~~ **(已删除 - 历史报告)**
*   ~~[Upgrade Complete Report (upgrade_complete_report.md)](./upgrade_complete_report.md):~~ **(已删除 - 历史报告)**

## 贡献指南

### 代码风格

*   **后端 (Node.js):** 请遵循 [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript) 并使用 Prettier 进行代码格式化。
*   **前端 (微信小程序):** 请遵循微信官方推荐的编码规范，并使用 Prettier。

### 提交规范

*   请使用清晰、简洁的提交消息，遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范。
*   示例: `feat: 添加用户笔记搜索功能`, `fix: 修复学习计划进度计算错误`, `docs: 更新 README 文档`

### 分支策略

*   `main`: 生产分支，保持稳定。
*   `develop`: 开发分支，用于集成新功能。
*   `feature/<feature-name>`: 功能开发分支，从 `develop`

## 文档维护建议

为了保持项目文档的准确性和时效性，建议遵循以下原则：

**1. 明确信息来源 (Single Source of Truth):**

*   **API:** 优先依赖 Swagger/OpenAPI 从代码注释**自动生成**的运行时文档 (`/api-docs`, `/redoc`)。修改 API 代码时**同步更新**代码注释。
*   **数据库:** 以 `backend/models/` 中的 Sequelize 模型为准。定期或变更后更新 `DATABASE-DESIGN.md`。
*   **架构/设计:** 通过 Markdown (`ARCHITECTURE-PRINCIPLES.md` 等) 维护，重大变更时更新。

**2. 集成到开发流程:**

*   **代码评审包含文档评审:** 功能/API 变更时，相关文档修改需随代码一同提交和评审。
*   **文档更新纳入"完成"定义:** 功能开发需包含文档更新才算完成。

**3. 自动化与工具:**

*   **利用 API 文档生成工具。**
*   (可选) 考虑在 CI/CD 中加入 Markdown 链接检查。

**4. 责任与审查:**

*   **团队共识:** 维护文档是团队共同的责任。
*   **定期审查:** 可定期（如季度/版本发布后）快速审查核心文档。
*   **主动更新与删除:** 鼓励成员发现问题时主动修正，并果断删除无用、过时的文档。

**5. 优化内容:**

*   **保持 `README.md` 索引更新:** 及时增删链接，确保描述准确。
*   **聚焦关键信息:** 侧重记录"为什么"、"是什么"、"如何做"，避免过多易变的实现细节。
*   **简洁与模块化:** 保持文档聚焦，过大时考虑拆分。