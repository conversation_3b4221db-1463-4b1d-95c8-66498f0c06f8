# Swagger注释模板

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-06 |
| 最后更新 | 2025-05-06 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档提供了AIBUBB项目中使用的Swagger注释模板，用于生成API文档。这些模板遵循OpenAPI 3.0规范，并且与AIBUBB的API设计规范保持一致。

## 2. 控制器方法注释模板

### 2.1 GET方法（获取列表）

```javascript
/**
 * @swagger
 * /api/v{version}/{resource}:
 *   get:
 *     summary: 获取{资源}列表
 *     description: 获取用户的{资源}列表，支持分页、过滤和排序
 *     tags: [{模块}]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码，从1开始
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 50
 *         description: 每页数量，最大50
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed, paused]
 *         description: 状态过滤
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: createdAt
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取{资源}列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/{资源模型}'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           example: 100
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         pageSize:
 *                           type: integer
 *                           example: 20
 *                         totalPages:
 *                           type: integer
 *                           example: 5
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.2 GET方法（获取详情）

```javascript
/**
 * @swagger
 * /api/v{version}/{resource}/{id}:
 *   get:
 *     summary: 获取{资源}详情
 *     description: 根据ID获取{资源}的详细信息
 *     tags: [{模块}]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: {资源}ID
 *     responses:
 *       200:
 *         description: 成功获取{资源}详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/{资源模型}'
 *       404:
 *         description: {资源}不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.3 POST方法（创建资源）

```javascript
/**
 * @swagger
 * /api/v{version}/{resource}:
 *   post:
 *     summary: 创建{资源}
 *     description: 创建新的{资源}
 *     tags: [{模块}]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/{资源创建模型}'
 *           example:
 *             {
 *               // 请求体示例
 *             }
 *     responses:
 *       201:
 *         description: {资源}创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/{资源模型}'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       422:
 *         description: 数据验证失败
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationError'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.4 PUT方法（更新资源）

```javascript
/**
 * @swagger
 * /api/v{version}/{resource}/{id}:
 *   put:
 *     summary: 更新{资源}
 *     description: 根据ID更新{资源}的信息
 *     tags: [{模块}]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: {资源}ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/{资源更新模型}'
 *           example:
 *             {
 *               // 请求体示例
 *             }
 *     responses:
 *       200:
 *         description: {资源}更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/{资源模型}'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: {资源}不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       422:
 *         description: 数据验证失败
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationError'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.5 DELETE方法（删除资源）

```javascript
/**
 * @swagger
 * /api/v{version}/{resource}/{id}:
 *   delete:
 *     summary: 删除{资源}
 *     description: 根据ID删除{资源}
 *     tags: [{模块}]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: {资源}ID
 *     responses:
 *       200:
 *         description: {资源}删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "{资源}已成功删除"
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: {资源}不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.6 软删除方法（V2版本）

```javascript
/**
 * @swagger
 * /api/v2/{resource}/{id}/soft-delete:
 *   delete:
 *     summary: 软删除{资源}
 *     description: 根据ID软删除{资源}，可以通过恢复接口恢复
 *     tags: [{模块}]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: {资源}ID
 *     responses:
 *       200:
 *         description: {资源}软删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "{资源}已软删除"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     deletedAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-05-06T10:00:00Z"
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: {资源}不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.7 恢复方法（V2版本）

```javascript
/**
 * @swagger
 * /api/v2/{resource}/{id}/restore:
 *   put:
 *     summary: 恢复已删除的{资源}
 *     description: 根据ID恢复已软删除的{资源}
 *     tags: [{模块}]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: {资源}ID
 *     responses:
 *       200:
 *         description: {资源}恢复成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "{资源}已恢复"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     deletedAt:
 *                       type: null
 *                       example: null
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: {资源}不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.8 获取已删除资源列表（V2版本）

```javascript
/**
 * @swagger
 * /api/v2/{resource}/deleted:
 *   get:
 *     summary: 获取已删除的{资源}列表
 *     description: 获取用户已软删除的{资源}列表，支持分页、过滤和排序
 *     tags: [{模块}]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码，从1开始
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 50
 *         description: 每页数量，最大50
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: deletedAt
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取已删除的{资源}列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/{资源模型}'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           example: 100
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         pageSize:
 *                           type: integer
 *                           example: 20
 *                         totalPages:
 *                           type: integer
 *                           example: 5
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

### 2.9 批量操作方法（V2版本）

```javascript
/**
 * @swagger
 * /api/v2/batch/{resource}/{action}:
 *   post:
 *     summary: 批量{操作}{资源}
 *     description: 批量{操作}多个{资源}
 *     tags: [{模块}]
 *     parameters:
 *       - in: path
 *         name: resource
 *         required: true
 *         schema:
 *           type: string
 *         description: 资源类型
 *       - in: path
 *         name: action
 *         required: true
 *         schema:
 *           type: string
 *           enum: [soft-delete, restore, permanent-delete]
 *         description: 操作类型
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3]
 *     responses:
 *       200:
 *         description: 批量操作成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "{资源}已批量{操作}"
 *                 data:
 *                   type: object
 *                   properties:
 *                     successCount:
 *                       type: integer
 *                       example: 3
 *                     failedCount:
 *                       type: integer
 *                       example: 0
 *                     results:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           success:
 *                             type: boolean
 *                             example: true
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

## 3. 模型定义模板

### 3.1 错误模型

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     Error:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         error:
 *           type: object
 *           properties:
 *             code:
 *               type: string
 *               example: "RESOURCE_NOT_FOUND"
 *             message:
 *               type: string
 *               example: "资源不存在或已被删除"
 *             details:
 *               type: object
 *               example: {}
 */
```

### 3.2 验证错误模型

```javascript
/**
 * @swagger
 * components:
 *   schemas:
 *     ValidationError:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         error:
 *           type: object
 *           properties:
 *             code:
 *               type: string
 *               example: "VALIDATION_ERROR"
 *             message:
 *               type: string
 *               example: "数据验证失败"
 *             details:
 *               type: object
 *               example:
 *                 title: "标题不能为空"
 *                 targetDays: "目标天数必须是正整数"
 */
```

## 4. 使用示例

### 4.1 标签控制器示例

```javascript
/**
 * @swagger
 * /api/v2/tags:
 *   get:
 *     summary: 获取标签列表
 *     description: 获取用户的标签列表，支持分页、过滤和排序
 *     tags: [标签]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码，从1开始
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 50
 *         description: 每页数量，最大50
 *       - in: query
 *         name: planId
 *         schema:
 *           type: integer
 *         description: 学习计划ID
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: sortOrder
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取标签列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     tags:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Tag'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           example: 10
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         pageSize:
 *                           type: integer
 *                           example: 20
 *                         totalPages:
 *                           type: integer
 *                           example: 1
 *       401:
 *         description: 未授权访问
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *     security:
 *       - bearerAuth: []
 */
```

## 5. 注意事项

1. 使用模板时，请替换`{version}`、`{resource}`、`{资源}`、`{模块}`等占位符为实际值。
2. 根据实际API的参数和响应格式调整模板。
3. 确保模型定义（`$ref: '#/components/schemas/{资源模型}'`）指向正确的模型。
4. 添加详细的参数描述和示例，帮助API使用者理解API的用法。
5. 保持注释风格的一致性，遵循项目的API设计规范。
6. 在代码审查中检查Swagger注释的完整性和准确性。
