# Taro 跨平台开发指南 - (注意：此为技术调研/迁移计划文档)

**重要提示：本项目当前仍使用原生微信小程序开发，尚未进行 Taro 迁移。本文档仅作为技术选型调研和未来可能的迁移计划参考。**

## 1. 项目迁移概述

### 1.1 为什么选择 Taro

在对比了 Uni-app 和 Taro 后，我们选择 Taro 作为 AI 互动泡泡项目的跨平台开发框架，主要基于以下优势：

- **微信小程序兼容性**：Taro 对已有微信小程序代码的迁移支持更好
- **Canvas 和动画处理**：更好支持项目中的气泡画布和复杂动画效果
- **React 生态系统**：使用 React 语法，适合处理复杂状态管理
- **TypeScript 友好**：对 TypeScript 支持完善，提升代码质量
- **社区活跃度**：由京东凹凸实验室维护，更新迭代频繁

### 1.2 迁移目标平台

我们计划将微信小程序扩展到以下平台：

- 支付宝小程序
- 字节跳动小程序
- H5 网页版
- ReactNative 应用（Android/iOS）

## 2. 开发环境搭建

### 2.1 基础环境要求

```bash
# 安装 Node.js (推荐 v16.x 或更高版本)
# 安装 Taro CLI
npm install -g @tarojs/cli

# 检查安装是否成功
taro -v
```

### 2.2 项目初始化

```bash
# 创建新的 Taro 项目（基于 React）
taro init ai-bubble-cross-platform

# 选择配置
# - 框架：React
# - 语言：TypeScript
# - CSS 预处理器：Sass
# - 模板：默认模板
```

### 2.3 项目依赖安装

```bash
# 安装核心依赖
npm install --save

# 安装开发依赖
npm install --save-dev @types/react @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint-config-taro eslint-plugin-react eslint-plugin-react-hooks
```

## 3. 项目代码迁移

### 3.1 目录结构对比

**微信小程序原始结构**:
```
├── pages/                # 页面目录
│   ├── index/            # 首页
│   ├── profile/          # 个人页
│   ├── learn/            # 学习页
│   └── square/           # 广场页
├── utils/                # 公共工具
├── assets/               # 资源文件
├── custom-tab-bar/       # 自定义底部栏
├── app.js                # 应用入口
├── app.json              # 应用配置
└── app.wxss              # 全局样式
```

**Taro 项目结构**:
```
├── src/                  # 源码目录
│   ├── pages/            # 页面目录
│   ├── components/       # 组件目录
│   ├── utils/            # 公共工具
│   ├── assets/           # 资源文件
│   ├── constants/        # 常量定义
│   ├── services/         # API 服务
│   ├── hooks/            # 自定义钩子
│   ├── custom-tab-bar/   # 自定义底部栏
│   ├── app.tsx           # 应用入口
│   ├── app.config.ts     # 应用配置
│   └── app.scss          # 全局样式
├── config/               # 编译配置
├── types/                # 类型定义
├── project.config.json   # 项目配置
└── package.json          # 包配置
```

### 3.2 页面迁移步骤

1. **WXML 转 JSX**:
   - 转换标签：`view` → `View`，`text` → `Text` 等
   - 转换属性：`bindtap` → `onClick`，`wx:if` → `{condition && ...}`
   - 转换数据绑定：`{{variable}}` → `{variable}`

2. **WXSS 转 SCSS**:
   - 保留大部分样式规则
   - 使用 Taro 的跨平台单位 `PX` 或 `Px`
   - 适配不同平台的样式差异

3. **JS 转 TSX**:
   - 将 Page 构造转换为函数组件
   - 使用 React Hooks 管理状态和生命周期
   - 使用 TypeScript 类型声明增强代码健壮性

### 3.3 Canvas 和气泡动画迁移

Canvas 是本项目的核心部分，迁移时需特别关注：

```typescript
// 原微信小程序代码
const BubbleCanvas = require('./bubble-canvas');
Page({
  onLoad() {
    this.bubbleCanvas = new BubbleCanvas(this);
    this.bubbleCanvas.init();
  }
});

// Taro 中的改造
import { useEffect, useRef } from 'react';
import Taro from '@tarojs/taro';
import { Canvas } from '@tarojs/components';
import { BubbleCanvas } from './bubble-canvas';

const IndexPage: React.FC = () => {
  const canvasRef = useRef<any>(null);
  const bubbleCanvasRef = useRef<any>(null);
  
  useEffect(() => {
    (async () => {
      // 获取 canvas 上下文
      const query = Taro.createSelectorQuery();
      const canvas = await new Promise<any>(resolve => {
        query.select('#bubble-canvas')
          .fields({ node: true, size: true })
          .exec((res) => resolve(res[0]));
      });
      
      if (canvas) {
        const bubbleCanvas = new BubbleCanvas(canvas);
        bubbleCanvas.init();
        bubbleCanvasRef.current = bubbleCanvas;
      }
    })();
    
    return () => {
      if (bubbleCanvasRef.current) {
        bubbleCanvasRef.current.destroy();
      }
    };
  }, []);
  
  return (
    <Canvas
      type="2d"
      id="bubble-canvas"
      className="bubble-canvas"
      ref={canvasRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    />
  );
};

export default IndexPage;
```

## 4. 平台差异处理

### 4.1 条件编译

使用 Taro 提供的条件编译处理不同平台的差异：

```typescript
// 平台特定代码
if (process.env.TARO_ENV === 'weapp') {
  // 微信小程序特有代码
} else if (process.env.TARO_ENV === 'alipay') {
  // 支付宝小程序特有代码
} else if (process.env.TARO_ENV === 'h5') {
  // H5 环境特有代码
} else if (process.env.TARO_ENV === 'rn') {
  // React Native 环境特有代码
}

// 或使用条件编译注释
/*#ifdef WEAPP*/
const weappSpecificCode = 'only in weapp';
/*#endif*/

/*#ifdef H5*/
const h5SpecificCode = 'only in h5';
/*#endif*/
```

### 4.2 API 适配

```typescript
// 使用 Taro 统一 API
import Taro from '@tarojs/taro';

// 原生 API 调用
// wx.request({...}) -> Taro.request({...})
// wx.navigateTo({...}) -> Taro.navigateTo({...})

// 示例：获取系统信息
const getSystemInfo = async () => {
  try {
    const systemInfo = await Taro.getSystemInfo();
    return systemInfo;
  } catch (error) {
    console.error('获取系统信息失败', error);
    return null;
  }
};
```

### 4.3 样式适配

```scss
// 全局样式适配
.cross-platform-container {
  // 微信小程序样式
  /*#ifdef WEAPP*/
  padding: 20px;
  /*#endif*/
  
  // H5 样式
  /*#ifdef H5*/
  padding: 15px;
  /*#endif*/
  
  // React Native 样式
  /*#ifdef RN*/
  padding-top: 20px;
  padding-horizontal: 15px;
  /*#endif*/
}
```

## 5. 核心功能迁移策略

### 5.1 AI 互动泡泡画布

画布是项目核心，需要对各平台分别适配：

1. **微信/支付宝/字节小程序**：使用原生 Canvas
2. **H5**：使用 HTML5 Canvas API
3. **ReactNative**：使用 `react-native-canvas` 或 `react-native-svg`

```typescript
// Canvas 工厂模式示例
class CanvasFactory {
  static createCanvas(platform) {
    switch (platform) {
      case 'weapp':
      case 'alipay':
      case 'tt':
        return new MiniProgramCanvas();
      case 'h5':
        return new H5Canvas();
      case 'rn':
        return new ReactNativeCanvas();
      default:
        return new MiniProgramCanvas();
    }
  }
}

// 使用示例
const canvas = CanvasFactory.createCanvas(process.env.TARO_ENV);
canvas.init();
canvas.drawBubbles();
```

### 5.2 状态管理迁移

使用 React Context 或 Redux 统一管理应用状态：

```typescript
// 学习计划上下文
import { createContext, useContext, useState, useEffect } from 'react';

interface LearningPlan {
  id: string;
  name: string;
  description: string;
  tags: string[];
}

interface LearningPlanContextType {
  currentPlan: LearningPlan | null;
  plans: LearningPlan[];
  loading: boolean;
  setCurrentPlan: (plan: LearningPlan) => void;
  loadPlans: () => Promise<void>;
}

const LearningPlanContext = createContext<LearningPlanContextType | null>(null);

export const LearningPlanProvider: React.FC = ({ children }) => {
  const [currentPlan, setCurrentPlan] = useState<LearningPlan | null>(null);
  const [plans, setPlans] = useState<LearningPlan[]>([]);
  const [loading, setLoading] = useState(false);
  
  const loadPlans = async () => {
    setLoading(true);
    try {
      // 从 API 加载学习计划
      const data = await fetchLearningPlans();
      setPlans(data);
      if (data.length > 0) {
        setCurrentPlan(data[0]);
      }
    } catch (error) {
      console.error('加载学习计划失败', error);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadPlans();
  }, []);
  
  return (
    <LearningPlanContext.Provider
      value={{
        currentPlan,
        plans,
        loading,
        setCurrentPlan,
        loadPlans
      }}
    >
      {children}
    </LearningPlanContext.Provider>
  );
};

export const useLearningPlan = () => {
  const context = useContext(LearningPlanContext);
  if (!context) {
    throw new Error('useLearningPlan must be used within LearningPlanProvider');
  }
  return context;
};
```

## 6. API 服务迁移

### 6.1 网络请求封装

```typescript
// services/api.ts
import Taro from '@tarojs/taro';

const BASE_URL = 'https://api.aibubb.com';

interface RequestOptions extends Taro.request.Option {
  showLoading?: boolean;
  loadingText?: string;
}

export default {
  request: async <T = any>(options: RequestOptions): Promise<T> => {
    const { url, data, method = 'GET', showLoading = false, loadingText = '加载中...' } = options;
    
    if (showLoading) {
      Taro.showLoading({ title: loadingText });
    }
    
    try {
      const response = await Taro.request({
        url: url.startsWith('http') ? url : `${BASE_URL}${url}`,
        data,
        method,
        header: {
          'Content-Type': 'application/json',
          ...options.header
        }
      });
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.data as T;
      } else {
        throw new Error(`请求失败: ${response.statusCode}`);
      }
    } catch (error) {
      console.error('API 请求错误', error);
      throw error;
    } finally {
      if (showLoading) {
        Taro.hideLoading();
      }
    }
  },
  
  get: <T = any>(url: string, data?: any, options?: Omit<RequestOptions, 'url' | 'data' | 'method'>) => {
    return this.request<T>({ url, data, method: 'GET', ...options });
  },
  
  post: <T = any>(url: string, data?: any, options?: Omit<RequestOptions, 'url' | 'data' | 'method'>) => {
    return this.request<T>({ url, data, method: 'POST', ...options });
  }
};
```

### 6.2 API 服务模块化

```typescript
// services/learning-plan.ts
import api from './api';

export interface LearningPlan {
  id: string;
  name: string;
  description: string;
  theme_id: string;
  created_at: string;
  tags: Array<{
    id: string;
    name: string;
  }>;
}

export const getLearningPlans = () => {
  return api.get<LearningPlan[]>('/api/learning-plans');
};

export const getLearningPlanDetail = (id: string) => {
  return api.get<LearningPlan>(`/api/learning-plans/${id}`);
};

export const createLearningPlan = (data: {
  name: string;
  description: string;
  theme_id: string;
}) => {
  return api.post<LearningPlan>('/api/learning-plans', data);
};
```

## 7. 打包与部署

### 7.1 多平台构建命令

```json
// package.json
{
  "scripts": {
    "build:weapp": "taro build --type weapp",
    "build:alipay": "taro build --type alipay",
    "build:tt": "taro build --type tt",
    "build:h5": "taro build --type h5",
    "build:rn": "taro build --type rn",
    "build:all": "npm run build:weapp && npm run build:alipay && npm run build:h5",
    "dev:weapp": "npm run build:weapp -- --watch",
    "dev:alipay": "npm run build:alipay -- --watch",
    "dev:tt": "npm run build:tt -- --watch",
    "dev:h5": "npm run build:h5 -- --watch",
    "dev:rn": "npm run build:rn -- --watch"
  }
}
```

### 7.2 CI/CD 配置

```yaml
# .github/workflows/deploy.yml
name: Build and Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build H5
      run: npm run build:h5
      
    - name: Build WeApp
      run: npm run build:weapp
      
    - name: Build Alipay
      run: npm run build:alipay
      
    - name: Deploy H5 to OSS
      uses: manyuanrong/setup-ossutil@v2.0
      with:
        endpoint: oss-cn-hangzhou.aliyuncs.com
        access-key-id: ${{ secrets.OSS_KEY_ID }}
        access-key-secret: ${{ secrets.OSS_KEY_SECRET }}
    
    - run: ossutil cp -rf ./dist/h5/ oss://aibubb-h5/
```

## 8. 最佳实践与注意事项

### 8.1 性能优化建议

1. **按需加载**：使用动态 import 按需加载组件
2. **状态管理**：避免过度使用全局状态，合理划分状态作用域
3. **渲染优化**：
   - 使用 `React.memo` 减少不必要的重渲染
   - 使用 `useCallback` 和 `useMemo` 缓存函数和计算结果
4. **资源优化**：
   - 压缩图片资源
   - 使用 CDN 分发静态资源
   - 使用 Taro 的图片组件自动选择合适尺寸

### 8.2 常见问题与解决方案

1. **样式差异问题**
   - 问题：不同平台样式渲染存在差异
   - 解决：使用条件编译针对不同平台编写特定样式

2. **API 兼容性问题**
   - 问题：部分 API 在某些平台不可用
   - 解决：使用 Taro.canIUse 检测 API 可用性，并提供降级方案

3. **Canvas 性能问题**
   - 问题：复杂 Canvas 动画在低端设备上性能不佳
   - 解决：根据设备性能动态调整动画复杂度和帧率

4. **状态管理复杂问题**
   - 问题：多层嵌套组件状态管理复杂
   - 解决：使用 Context API 或 Redux 统一管理状态

### 8.3 项目迁移时间线

| 阶段 | 时间（工作日） | 主要工作 |
|------|--------------|---------|
| 准备阶段 | 5 | 环境搭建、项目结构设计、技术选型确认 |
| 基础框架迁移 | 10 | 页面结构迁移、路由配置、全局样式 |
| 核心功能迁移 | 15 | Canvas 画布、泡泡动画、交互逻辑 |
| 业务逻辑迁移 | 10 | 学习计划、标签系统、内容展示 |
| 多平台适配 | 10 | 小程序、H5、RN 平台适配 |
| 测试与优化 | 10 | 功能测试、性能优化、BUG 修复 |
| **总计** | **60** | 约 3 个月（按每月 20 个工作日计算） |

## 9. 附录

### 9.1 有用的资源链接

- [Taro 官方文档](https://taro-docs.jd.com/)
- [Taro GitHub 仓库](https://github.com/NervJS/taro)
- [Taro UI 组件库](https://taro-ui.jd.com/)
- [React 官方文档](https://reactjs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)

### 9.2 Taro 版本更新记录

请关注 [Taro 更新日志](https://github.com/NervJS/taro/blob/master/CHANGELOG.md) 以获取最新版本信息。

### 9.3 团队分工建议

- **前端架构师**：负责整体迁移方案设计和核心模块实现
- **UI 开发**：负责组件迁移和样式适配
- **业务开发**：负责业务逻辑迁移和功能实现
- **测试人员**：负责多平台测试和质量保证 