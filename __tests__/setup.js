/**
 * Jest测试设置文件
 * 在所有测试之前运行
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-purposes-only';
process.env.JWT_EXPIRES_IN = '1h';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';

// 输出测试环境信息
console.log('测试环境配置已加载');

// 模拟Redis客户端
jest.mock('../config/redis', () => {
  const mockRedisClient = {
    isOpen: true,
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue('OK'),
    del: jest.fn().mockResolvedValue(1),
    sAdd: jest.fn().mockResolvedValue(1),
    sMembers: jest.fn().mockResolvedValue([]),
    sRem: jest.fn().mockResolvedValue(1),
    expire: jest.fn().mockResolvedValue(1),
    multi: jest.fn().mockReturnValue({
      set: jest.fn().mockReturnThis(),
      del: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([])
    })
  };
  
  return {
    redisClient: mockRedisClient
  };
});

// 模拟日志记录器
jest.mock('../config/logger', () => {
  return {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn()
  };
});

// 全局清理函数
global.afterAll(async () => {
  // 在所有测试完成后执行清理操作
  console.log('测试完成，执行清理操作');
});
