/**
 * API客户端单元测试
 */

import { ApiClient } from '../../utils/api-client';
import { CacheManager } from '../../utils/api-client/cache-manager';
import { DataTransformer } from '../../utils/api-client/data-transformer';

// 模拟fetch
global.fetch = jest.fn();

describe('ApiClient', () => {
  let apiClient;

  beforeEach(() => {
    // 重置fetch模拟
    fetch.mockReset();

    // 创建API客户端实例
    apiClient = new ApiClient({
      baseUrl: 'http://test-api.example.com/api/v1',
      timeout: 5000,
      maxRetries: 2
    });
  });

  test('should create an instance with default config', () => {
    expect(apiClient).toBeInstanceOf(ApiClient);
    expect(apiClient.config.baseUrl).toBe('http://test-api.example.com/api/v1');
    expect(apiClient.config.timeout).toBe(5000);
    expect(apiClient.config.maxRetries).toBe(2);
  });

  test('should handle GET request successfully', async () => {
    // 模拟成功响应
    fetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: 1,
          name: 'Test Theme',
          description: 'Test Description',
          is_active: true
        },
        message: 'Success'
      }),
      headers: new Headers()
    });

    const result = await apiClient.get('/themes/1');

    // 验证fetch调用
    expect(fetch).toHaveBeenCalledTimes(1);
    expect(fetch.mock.calls[0][0]).toContain('/themes/1');

    // 验证响应处理
    expect(result.data).toEqual({
      id: 1,
      name: 'Test Theme',
      description: 'Test Description',
      isActive: true
    });
    expect(result.message).toBe('Success');
  });

  test('should handle POST request with data', async () => {
    // 模拟成功响应
    fetch.mockResolvedValueOnce({
      ok: true,
      status: 201,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: 2,
          name: 'New Theme',
          description: 'New Description',
          is_active: true
        },
        message: 'Created'
      }),
      headers: new Headers()
    });

    const data = {
      name: 'New Theme',
      description: 'New Description',
      isActive: true
    };

    const result = await apiClient.post('/themes', data);

    // 验证fetch调用
    expect(fetch).toHaveBeenCalledTimes(1);
    expect(fetch.mock.calls[0][0]).toContain('/themes');
    expect(JSON.parse(fetch.mock.calls[0][1].body)).toEqual({
      name: 'New Theme',
      description: 'New Description',
      is_active: true
    });

    // 验证响应处理
    expect(result.data).toEqual({
      id: 2,
      name: 'New Theme',
      description: 'New Description',
      isActive: true
    });
    expect(result.message).toBe('Created');
  });

  test('should handle error response', async () => {
    // 模拟错误响应
    fetch.mockResolvedValueOnce({
      ok: false,
      status: 404,
      json: () => Promise.resolve({
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'Theme not found'
        }
      }),
      headers: new Headers()
    });

    // 期望抛出错误
    await expect(apiClient.get('/themes/999')).rejects.toThrow('Theme not found');
  });

  test('should retry failed requests', async () => {
    // 前两次请求失败，第三次成功
    fetch.mockRejectedValueOnce(new Error('Network error'));
    fetch.mockRejectedValueOnce(new Error('Network error'));
    fetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: 1,
          name: 'Test Theme'
        }
      }),
      headers: new Headers()
    });

    const result = await apiClient.get('/themes/1');

    // 验证fetch调用了3次
    expect(fetch).toHaveBeenCalledTimes(3);

    // 验证最终成功
    expect(result.data).toEqual({
      id: 1,
      name: 'Test Theme'
    });
  });
});

describe('CacheManager', () => {
  let cacheManager;

  beforeEach(() => {
    // 创建缓存管理器实例
    cacheManager = new CacheManager({
      defaultTTL: 1000, // 1秒，便于测试
      useLocalStorage: false // 禁用本地存储，避免测试环境问题
    });
  });

  test('should set and get cache', () => {
    const key = 'test-key';
    const data = { id: 1, name: 'Test' };

    cacheManager.set(key, data);
    const cachedData = cacheManager.get(key);

    expect(cachedData).toEqual(data);
  });

  test('should expire cache after TTL', async () => {
    const key = 'test-key';
    const data = { id: 1, name: 'Test' };

    cacheManager.set(key, data, 100); // 100毫秒后过期

    // 立即获取应该成功
    expect(cacheManager.get(key)).toEqual(data);

    // 等待过期
    await new Promise(resolve => setTimeout(resolve, 200));

    // 过期后获取应该返回null
    expect(cacheManager.get(key)).toBeNull();
  });

  test('should clear cache by pattern', () => {
    cacheManager.set('theme:1', { id: 1 });
    cacheManager.set('theme:2', { id: 2 });
    cacheManager.set('user:1', { id: 1 });

    // 清除theme相关缓存
    cacheManager.clear('theme');

    expect(cacheManager.get('theme:1')).toBeNull();
    expect(cacheManager.get('theme:2')).toBeNull();
    expect(cacheManager.get('user:1')).not.toBeNull();
  });
});

describe('DataTransformer', () => {
  let dataTransformer;

  beforeEach(() => {
    // 创建数据转换器实例
    dataTransformer = new DataTransformer();
  });

  test('should transform snake_case to camelCase', () => {
    const snakeData = {
      user_id: 1,
      first_name: 'John',
      last_name: 'Doe',
      address: {
        street_name: 'Main St',
        zip_code: '12345'
      },
      phone_numbers: [
        { phone_type: 'home', phone_number: '************' },
        { phone_type: 'work', phone_number: '************' }
      ]
    };

    const camelData = dataTransformer.snakeToCamel(snakeData);

    expect(camelData).toEqual({
      userId: 1,
      firstName: 'John',
      lastName: 'Doe',
      address: {
        streetName: 'Main St',
        zipCode: '12345'
      },
      phoneNumbers: [
        { phoneType: 'home', phoneNumber: '************' },
        { phoneType: 'work', phoneNumber: '************' }
      ]
    });
  });

  test('should transform camelCase to snake_case', () => {
    const camelData = {
      userId: 1,
      firstName: 'John',
      lastName: 'Doe',
      address: {
        streetName: 'Main St',
        zipCode: '12345'
      }
    };

    const snakeData = dataTransformer.camelToSnake(camelData);

    expect(snakeData).toEqual({
      user_id: 1,
      first_name: 'John',
      last_name: 'Doe',
      address: {
        street_name: 'Main St',
        zip_code: '12345'
      }
    });
  });

  test('should validate data against schema', () => {
    const schema = {
      name: { type: 'string', required: true },
      age: { type: 'number', required: true },
      email: {
        type: 'string',
        required: true,
        validator: value => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      }
    };

    // 有效数据
    const validData = {
      name: 'John',
      age: 30,
      email: '<EMAIL>'
    };

    // 无效数据 - 缺少必填字段
    const invalidData1 = {
      name: 'John',
      age: 30
    };

    // 无效数据 - 类型错误
    const invalidData2 = {
      name: 'John',
      age: '30', // 应该是数字
      email: '<EMAIL>'
    };

    // 无效数据 - 验证失败
    const invalidData3 = {
      name: 'John',
      age: 30,
      email: 'invalid-email'
    };

    expect(dataTransformer.validate(validData, schema)).toBe(true);
    expect(dataTransformer.validate(invalidData1, schema)).toBe(false);
    expect(dataTransformer.validate(invalidData2, schema)).toBe(false);
    expect(dataTransformer.validate(invalidData3, schema)).toBe(false);
  });
});
