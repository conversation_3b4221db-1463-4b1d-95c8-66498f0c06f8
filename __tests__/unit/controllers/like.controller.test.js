const likeController = require('../../../controllers/like.controller');
const { Note, Tag, NoteLike, TagLike, LearningPlan, sequelize, PlanTag } = require('../../../models');
const apiResponse = require('../../../utils/apiResponse');

// 模拟依赖
jest.mock('../../../models', () => {
  const mockSequelize = {
    transaction: jest.fn().mockResolvedValue({
      commit: jest.fn().mockResolvedValue(),
      rollback: jest.fn().mockResolvedValue()
    })
  };

  return {
    Note: {
      findOne: jest.fn(),
      increment: jest.fn()
    },
    Tag: {
      findOne: jest.fn(),
      increment: jest.fn()
    },
    NoteLike: {
      findOne: jest.fn(),
      create: jest.fn(),
      destroy: jest.fn(),
      findAndCountAll: jest.fn()
    },
    TagLike: {
      findOne: jest.fn(),
      create: jest.fn(),
      destroy: jest.fn(),
      findAndCountAll: jest.fn()
    },
    PlanTag: {},
    LearningPlan: {},
    sequelize: mockSequelize
  };
});

jest.mock('../../../utils/apiResponse', () => ({
  success: jest.fn(),
  error: jest.fn(),
  notFound: jest.fn(),
  badRequest: jest.fn()
}));

describe('Like Controller', () => {
  // 模拟请求和响应对象
  let req;
  let res;

  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();

    // 初始化请求和响应对象
    req = {
      params: {},
      query: {},
      user: { userId: 'test-user-id' }
    };
    res = {};

    // 模拟响应方法
    apiResponse.success.mockImplementation(() => res);
    apiResponse.error.mockImplementation(() => res);
    apiResponse.notFound.mockImplementation(() => res);
    apiResponse.badRequest.mockImplementation(() => res);
  });

  describe('likeNote', () => {
    it('should like a note successfully', async () => {
      // 模拟请求参数
      req.params.id = 'note-1';

      // 模拟笔记查询结果
      const mockNote = {
        id: 'note-1',
        title: '测试笔记',
        user_id: 'author-id'
      };
      Note.findOne.mockResolvedValue(mockNote);

      // 模拟点赞查询结果 - 未点赞
      NoteLike.findOne.mockResolvedValue(null);

      // 模拟点赞创建结果
      const mockLike = {
        id: 'like-1',
        user_id: 'test-user-id',
        note_id: 'note-1',
        created_at: new Date()
      };
      NoteLike.create.mockResolvedValue(mockLike);

      // 调用控制器方法
      await likeController.likeNote(req, res);

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证笔记查询
      expect(Note.findOne).toHaveBeenCalledWith({
        where: { id: 'note-1' }
      });

      // 验证点赞查询
      expect(NoteLike.findOne).toHaveBeenCalledWith({
        where: {
          user_id: 'test-user-id',
          note_id: 'note-1'
        }
      });

      // 验证点赞创建
      expect(NoteLike.create).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        note_id: 'note-1'
      }, { transaction });

      // 验证笔记点赞数增加
      expect(Note.increment).toHaveBeenCalledWith('like_count', {
        where: { id: 'note-1' },
        transaction
      });

      // 验证事务提交
      expect(transaction.commit).toHaveBeenCalled();

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(
        res,
        {
          noteId: 'note-1',
          liked: true
        },
        '笔记点赞成功'
      );
    });

    it('should handle already liked note', async () => {
      // 模拟请求参数
      req.params.id = 'note-1';

      // 模拟笔记查询结果
      const mockNote = {
        id: 'note-1',
        title: '测试笔记',
        user_id: 'author-id'
      };
      Note.findOne.mockResolvedValue(mockNote);

      // 模拟点赞查询结果 - 已点赞
      const mockLike = {
        id: 'like-1',
        user_id: 'test-user-id',
        note_id: 'note-1'
      };
      NoteLike.findOne.mockResolvedValue(mockLike);

      // 调用控制器方法
      await likeController.likeNote(req, res);

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证笔记查询
      expect(Note.findOne).toHaveBeenCalledWith({
        where: { id: 'note-1' }
      });

      // 验证点赞查询
      expect(NoteLike.findOne).toHaveBeenCalledWith({
        where: {
          user_id: 'test-user-id',
          note_id: 'note-1'
        }
      });

      // 验证未创建点赞
      expect(NoteLike.create).not.toHaveBeenCalled();

      // 验证未增加笔记点赞数
      expect(Note.increment).not.toHaveBeenCalled();

      // 验证事务回滚
      expect(transaction.rollback).toHaveBeenCalled();

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(
        res,
        {
          noteId: 'note-1',
          liked: true
        },
        '已经点赞过该笔记'
      );
    });

    it('should handle note not found', async () => {
      // 模拟请求参数
      req.params.id = 'non-existent-note';

      // 模拟笔记查询结果 - 未找到
      Note.findOne.mockResolvedValue(null);

      // 调用控制器方法
      await likeController.likeNote(req, res);

      // 验证笔记查询
      expect(Note.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-note' }
      });

      // 验证未查询点赞
      expect(NoteLike.findOne).not.toHaveBeenCalled();

      // 验证未创建点赞
      expect(NoteLike.create).not.toHaveBeenCalled();

      // 验证响应
      expect(apiResponse.notFound).toHaveBeenCalledWith(
        res,
        '笔记不存在'
      );
    });

    it('should handle database errors', async () => {
      // 模拟请求参数
      req.params.id = 'note-1';

      // 模拟数据库错误
      const error = new Error('数据库错误');
      Note.findOne.mockRejectedValue(error);

      // 调用控制器方法
      await likeController.likeNote(req, res);

      // 验证笔记查询
      expect(Note.findOne).toHaveBeenCalledWith({
        where: { id: 'note-1' }
      });

      // 验证错误响应
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '点赞笔记失败',
        'SERVER_ERROR',
        500
      );
    });
  });

  describe('likeTag', () => {
    it('should like a tag successfully', async () => {
      // 模拟请求参数
      req.params.id = 'tag-1';

      // 模拟标签查询结果
      const mockTag = {
        id: 'tag-1',
        name: '倾听',
        plan_id: 'plan-1',
        learningPlan: {
          id: 'plan-1',
          user_id: 'test-user-id'
        }
      };
      Tag.findOne.mockResolvedValue(mockTag);

      // 模拟点赞查询结果 - 未点赞
      TagLike.findOne.mockResolvedValue(null);

      // 模拟点赞创建结果
      const mockLike = {
        id: 'like-1',
        user_id: 'test-user-id',
        tag_id: 'tag-1',
        created_at: new Date()
      };
      TagLike.create.mockResolvedValue(mockLike);

      // 调用控制器方法
      await likeController.likeTag(req, res);

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证标签查询
      expect(Tag.findOne).toHaveBeenCalledWith({
        where: { id: 'tag-1' },
        include: [
          {
            model: LearningPlan,
            as: 'learningPlan',
            attributes: ['id', 'user_id']
          }
        ]
      });

      // 验证点赞查询
      expect(TagLike.findOne).toHaveBeenCalledWith({
        where: {
          user_id: 'test-user-id',
          tag_id: 'tag-1'
        }
      });

      // 验证点赞创建
      expect(TagLike.create).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        tag_id: 'tag-1'
      }, { transaction });

      // 验证标签点赞数增加
      expect(Tag.increment).toHaveBeenCalledWith('like_count', {
        where: { id: 'tag-1' },
        transaction
      });

      // 验证事务提交
      expect(transaction.commit).toHaveBeenCalled();

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(
        res,
        {
          tagId: 'tag-1',
          liked: true
        },
        '标签点赞成功'
      );
    });

    it('should handle tag not found', async () => {
      // 模拟请求参数
      req.params.id = 'non-existent-tag';

      // 模拟标签查询结果 - 未找到
      Tag.findOne.mockResolvedValue(null);

      // 调用控制器方法
      await likeController.likeTag(req, res);

      // 验证标签查询
      expect(Tag.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-tag' },
        include: [
          {
            model: LearningPlan,
            as: 'learningPlan',
            attributes: ['id', 'user_id']
          }
        ]
      });

      // 验证未查询点赞
      expect(TagLike.findOne).not.toHaveBeenCalled();

      // 验证未创建点赞
      expect(TagLike.create).not.toHaveBeenCalled();

      // 验证响应
      expect(apiResponse.notFound).toHaveBeenCalledWith(
        res,
        '标签不存在'
      );
    });
  });

  describe('getLikedNotes', () => {
    it('should return liked notes with pagination', async () => {
      // 模拟请求查询参数
      req.query = {
        page: '1',
        pageSize: '10'
      };

      // 模拟点赞查询结果
      const mockLikes = {
        count: 2,
        rows: [
          {
            id: 'like-1',
            user_id: 'test-user-id',
            note_id: 'note-1',
            created_at: new Date(),
            note: {
              id: 'note-1',
              title: '笔记1',
              content: '内容1',
              user_id: 'author-1',
              user: {
                id: 'author-1',
                nickname: '作者1'
              }
            }
          },
          {
            id: 'like-2',
            user_id: 'test-user-id',
            note_id: 'note-2',
            created_at: new Date(),
            note: {
              id: 'note-2',
              title: '笔记2',
              content: '内容2',
              user_id: 'author-2',
              user: {
                id: 'author-2',
                nickname: '作者2'
              }
            }
          }
        ]
      };
      NoteLike.findAndCountAll.mockResolvedValue(mockLikes);

      // 调用控制器方法
      await likeController.getLikedNotes(req, res);

      // 验证点赞查询
      expect(NoteLike.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: { user_id: 'test-user-id' },
        limit: 10,
        offset: 0,
        order: [['created_at', 'DESC']]
      }));

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(
        res,
        {
          notes: expect.arrayContaining([
            expect.objectContaining({
              id: 'note-1',
              title: '笔记1'
            }),
            expect.objectContaining({
              id: 'note-2',
              title: '笔记2'
            })
          ]),
          pagination: {
            total: 2,
            page: 1,
            pageSize: 10,
            totalPages: 1
          }
        }
      );
    });

    it('should handle database errors', async () => {
      // 模拟数据库错误
      const error = new Error('数据库错误');
      NoteLike.findAndCountAll.mockRejectedValue(error);

      // 调用控制器方法
      await likeController.getLikedNotes(req, res);

      // 验证错误响应
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '获取点赞笔记列表失败',
        'SERVER_ERROR',
        500
      );
    });
  });

  describe('getLikedTags', () => {
    it('should get liked tags successfully with pagination', async () => {
      // 模拟请求查询参数
      req.query = { page: '2', pageSize: '5' };

      // 模拟 TagLike.findAndCountAll 的返回结果
      const mockLikedTags = [
        {
          id: 'taglike-1',
          user_id: 'test-user-id',
          tag_id: 'tag-1',
          created_at: new Date(),
          tag: { // 模拟包含的 Tag
            id: 'tag-1',
            name: '倾听',
            planTags: [ // 模拟包含的 PlanTag 数组
              { plan_id: 'plan-abc' }
            ]
          }
        },
        {
          id: 'taglike-2',
          user_id: 'test-user-id',
          tag_id: 'tag-2',
          created_at: new Date(),
          tag: {
            id: 'tag-2',
            name: '表达',
            planTags: [
              { plan_id: 'plan-abc' }
            ]
          }
        },
        { // 模拟一个 Tag 可能没有 PlanTag 的情况
          id: 'taglike-3',
          user_id: 'test-user-id',
          tag_id: 'tag-3',
          created_at: new Date(),
          tag: {
            id: 'tag-3',
            name: '反馈',
            planTags: [] // 空数组
          }
        }
      ];
      TagLike.findAndCountAll.mockResolvedValue({
        count: 12, // 总共有 12 条点赞记录
        rows: mockLikedTags.slice(0, 5) // 假设当前页返回 5 条
      });

      // 调用控制器方法
      await likeController.getLikedTags(req, res);

      // 验证 TagLike.findAndCountAll 调用
      expect(TagLike.findAndCountAll).toHaveBeenCalledWith({
        where: { user_id: 'test-user-id' },
        include: [
          {
            model: Tag,
            as: 'tag',
            attributes: ['id', 'name'],
            include: [{
              model: PlanTag, // 确保模型模拟中有 PlanTag
              as: 'planTags',
              attributes: ['plan_id']
            }]
          }
        ],
        limit: 5,
        offset: 5, // (2 - 1) * 5
        order: [['created_at', 'DESC']]
      });

      // 验证响应
      const expectedTags = [
        { id: 'tag-1', name: '倾听', likedAt: expect.any(Date), planId: 'plan-abc' },
        { id: 'tag-2', name: '表达', likedAt: expect.any(Date), planId: 'plan-abc' },
        { id: 'tag-3', name: '反馈', likedAt: expect.any(Date), planId: null } // planId 应为 null
      ];
      expect(apiResponse.success).toHaveBeenCalledWith(res, {
        tags: expectedTags.slice(0,5), // 确认返回数量符合 pageSize
        pagination: {
          currentPage: 2,
          pageSize: 5,
          totalItems: 12,
          totalPages: 3 // Math.ceil(12 / 5)
        }
      });
    });

    it('should handle errors when getting liked tags', async () => {
      // 模拟数据库错误
      const error = new Error('数据库查询失败');
      TagLike.findAndCountAll.mockRejectedValue(error);

      // 调用控制器方法
      await likeController.getLikedTags(req, res);

      // 验证错误响应
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '获取点赞标签列表失败',
        'SERVER_ERROR',
        500
      );
      // 验证错误日志记录（如果 logger 被 mock 的话）
      // expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('获取点赞标签列表失败'), error.stack);
    });
  });
});
