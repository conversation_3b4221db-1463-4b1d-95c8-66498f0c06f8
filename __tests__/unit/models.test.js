/**
 * 数据模型单元测试
 */

import { ThemeTransformer } from '../../utils/models/transformers/theme-transformer';
import { LearningPlanTransformer } from '../../utils/models/transformers/learning-plan-transformer';
import { TagTransformer } from '../../utils/models/transformers/tag-transformer';
import { ThemeValidator } from '../../utils/models/validators/theme-validator';
import { LearningPlanValidator } from '../../utils/models/validators/learning-plan-validator';
import { ThemeModelService } from '../../utils/models/services/theme-model-service';
import { LearningPlanModelService } from '../../utils/models/services/learning-plan-model-service';
import { LearningPlanStatus } from '../../utils/models/interfaces/learning-plan';

describe('数据转换器测试', () => {
  describe('ThemeTransformer', () => {
    const themeTransformer = new ThemeTransformer();

    test('应该将API响应数据转换为前端主题模型', () => {
      const apiData = {
        id: 1,
        name: '编程',
        english_name: 'Programming',
        description: '编程相关的学习内容',
        icon: 'code',
        color: '#3498db',
        cover_image_url: 'https://example.com/images/programming.jpg',
        sort_order: 10,
        is_active: true,
        parent_id: null,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
        deleted_at: null
      };

      const theme = themeTransformer.fromApi(apiData);

      expect(theme).toEqual({
        id: 1,
        name: '编程',
        englishName: 'Programming',
        description: '编程相关的学习内容',
        icon: 'code',
        color: '#3498db',
        coverImageUrl: 'https://example.com/images/programming.jpg',
        sortOrder: 10,
        isActive: true,
        parentId: null,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z',
        deletedAt: null
      });
    });

    test('应该将前端主题模型转换为API请求数据', () => {
      const themeModel = {
        name: '编程',
        englishName: 'Programming',
        description: '编程相关的学习内容',
        icon: 'code',
        color: '#3498db',
        coverImageUrl: 'https://example.com/images/programming.jpg',
        sortOrder: 10,
        isActive: true,
        parentId: null
      };

      const apiData = themeTransformer.toApi(themeModel);

      expect(apiData).toEqual({
        name: '编程',
        english_name: 'Programming',
        description: '编程相关的学习内容',
        icon: 'code',
        color: '#3498db',
        cover_image_url: 'https://example.com/images/programming.jpg',
        sort_order: 10,
        is_active: true,
        parent_id: null
      });
    });

    test('应该将创建主题请求转换为API请求数据', () => {
      const createRequest = {
        name: '编程',
        englishName: 'Programming',
        description: '编程相关的学习内容',
        icon: 'code',
        color: '#3498db',
        coverImageUrl: 'https://example.com/images/programming.jpg',
        sortOrder: 10,
        isActive: true
      };

      const apiData = themeTransformer.createRequestToApi(createRequest);

      expect(apiData).toEqual({
        name: '编程',
        english_name: 'Programming',
        description: '编程相关的学习内容',
        icon: 'code',
        color: '#3498db',
        cover_image_url: 'https://example.com/images/programming.jpg',
        sort_order: 10,
        is_active: true,
        parent_id: undefined
      });
    });
  });

  describe('LearningPlanTransformer', () => {
    const learningPlanTransformer = new LearningPlanTransformer();

    test('应该将API响应数据转换为前端学习计划模型', () => {
      const apiData = {
        id: 1,
        user_id: 100,
        template_id: 10,
        theme_id: 5,
        title: '30天学习JavaScript',
        description: '从零开始学习JavaScript',
        cover_image_url: 'https://example.com/images/js.jpg',
        target_days: 30,
        completed_days: 10,
        progress: 33,
        daily_goal_exercises: 3,
        daily_goal_insights: 5,
        daily_goal_minutes: 60,
        status: 'in_progress',
        start_date: '2023-01-01',
        end_date: '2023-01-30',
        is_current: true,
        is_system_default: false,
        is_public: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-10T00:00:00Z',
        deleted_at: null,
        theme: {
          id: 5,
          name: '编程',
          english_name: 'Programming',
          color: '#3498db',
          is_active: true
        },
        tags: [
          { id: 1, name: 'JavaScript', category_id: 1 },
          { id: 2, name: '前端', category_id: 1 }
        ]
      };

      const learningPlan = learningPlanTransformer.fromApi(apiData);

      expect(learningPlan).toEqual({
        id: 1,
        userId: 100,
        templateId: 10,
        themeId: 5,
        title: '30天学习JavaScript',
        description: '从零开始学习JavaScript',
        coverImageUrl: 'https://example.com/images/js.jpg',
        targetDays: 30,
        completedDays: 10,
        progress: 33,
        dailyGoalExercises: 3,
        dailyGoalInsights: 5,
        dailyGoalMinutes: 60,
        status: LearningPlanStatus.InProgress,
        startDate: '2023-01-01',
        endDate: '2023-01-30',
        isCurrent: true,
        isSystemDefault: false,
        isPublic: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-10T00:00:00Z',
        deletedAt: null,
        theme: {
          id: 5,
          name: '编程',
          englishName: 'Programming',
          color: '#3498db',
          isActive: true,
          createdAt: undefined,
          updatedAt: undefined
        },
        tags: [
          {
            id: 1,
            name: 'JavaScript',
            categoryId: 1,
            relevanceScore: 1,
            weight: 1,
            usageCount: 0,
            likeCount: 0,
            isVerified: false,
            isOfficial: true,
            sortOrder: 0,
            createdAt: undefined,
            updatedAt: undefined
          },
          {
            id: 2,
            name: '前端',
            categoryId: 1,
            relevanceScore: 1,
            weight: 1,
            usageCount: 0,
            likeCount: 0,
            isVerified: false,
            isOfficial: true,
            sortOrder: 0,
            createdAt: undefined,
            updatedAt: undefined
          }
        ]
      });
    });

    test('应该将前端学习计划模型转换为API请求数据', () => {
      const learningPlanModel = {
        userId: 100,
        templateId: 10,
        themeId: 5,
        title: '30天学习JavaScript',
        description: '从零开始学习JavaScript',
        targetDays: 30,
        dailyGoalExercises: 3,
        dailyGoalInsights: 5,
        dailyGoalMinutes: 60,
        status: LearningPlanStatus.InProgress,
        startDate: '2023-01-01',
        isPublic: true
      };

      const apiData = learningPlanTransformer.toApi(learningPlanModel);

      expect(apiData).toEqual({
        user_id: 100,
        template_id: 10,
        theme_id: 5,
        title: '30天学习JavaScript',
        description: '从零开始学习JavaScript',
        target_days: 30,
        daily_goal_exercises: 3,
        daily_goal_insights: 5,
        daily_goal_minutes: 60,
        status: 'in_progress',
        start_date: '2023-01-01',
        is_public: true
      });
    });
  });
});

describe('数据验证器测试', () => {
  describe('ThemeValidator', () => {
    const themeValidator = new ThemeValidator();

    test('应该验证有效的主题数据', () => {
      const validTheme = {
        name: '编程',
        englishName: 'Programming',
        description: '编程相关的学习内容',
        color: '#3498db',
        sortOrder: 10
      };

      const result = themeValidator.validate(validTheme);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该检测到无效的主题数据', () => {
      const invalidTheme = {
        name: '', // 名称为空
        color: 'invalid-color', // 无效的颜色格式
        sortOrder: -1 // 无效的排序顺序
      };

      const result = themeValidator.validate(invalidTheme);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('应该验证创建主题请求', () => {
      const validRequest = {
        name: '编程',
        englishName: 'Programming',
        color: '#3498db'
      };

      const result = themeValidator.validateCreateRequest(validRequest);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该检测到无效的创建主题请求', () => {
      const invalidRequest = {
        // 缺少必填的name字段
        englishName: 'Programming',
        color: '#3498db'
      };

      const result = themeValidator.validateCreateRequest(invalidRequest);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('LearningPlanValidator', () => {
    const learningPlanValidator = new LearningPlanValidator();

    test('应该验证有效的学习计划数据', () => {
      const validPlan = {
        title: '30天学习JavaScript',
        targetDays: 30,
        dailyGoalExercises: 3,
        dailyGoalInsights: 5,
        dailyGoalMinutes: 60,
        status: LearningPlanStatus.InProgress,
        startDate: '2023-01-01',
        endDate: '2023-01-30'
      };

      const result = learningPlanValidator.validate(validPlan);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该检测到无效的学习计划数据', () => {
      const invalidPlan = {
        title: '', // 标题为空
        targetDays: 0, // 无效的目标天数
        dailyGoalExercises: -1, // 无效的每日目标
        status: 'invalid-status', // 无效的状态
        startDate: '2023-01-30',
        endDate: '2023-01-01' // 结束日期早于开始日期
      };

      const result = learningPlanValidator.validate(invalidPlan);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('应该验证创建学习计划请求', () => {
      const validRequest = {
        title: '30天学习JavaScript',
        templateId: 10,
        themeId: 5,
        targetDays: 30,
        startDate: '2023-01-01'
      };

      const result = learningPlanValidator.validateCreateRequest(validRequest);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该检测到无效的创建学习计划请求', () => {
      const invalidRequest = {
        // 缺少必填的title字段
        templateId: 10,
        themeId: 5,
        targetDays: 30
      };

      const result = learningPlanValidator.validateCreateRequest(invalidRequest);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});

describe('模型服务测试', () => {
  describe('ThemeModelService', () => {
    const themeService = new ThemeModelService();

    test('应该转换API响应为模型', () => {
      const apiResponse = {
        data: {
          id: 1,
          name: '编程',
          english_name: 'Programming',
          color: '#3498db',
          is_active: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-02T00:00:00Z'
        },
        message: 'Success'
      };

      const theme = themeService.fromApiResponse(apiResponse);

      expect(theme.id).toBe(1);
      expect(theme.name).toBe('编程');
      expect(theme.englishName).toBe('Programming');
      expect(theme.color).toBe('#3498db');
      expect(theme.isActive).toBe(true);
      expect(theme.createdAt).toBe('2023-01-01T00:00:00Z');
      expect(theme.updatedAt).toBe('2023-01-02T00:00:00Z');
    });

    test('应该转换API分页响应为模型数组', () => {
      const apiResponse = {
        data: [
          {
            id: 1,
            name: '编程',
            english_name: 'Programming',
            is_active: true
          },
          {
            id: 2,
            name: '设计',
            english_name: 'Design',
            is_active: true
          }
        ],
        meta: {
          total: 2,
          page: 1,
          pageSize: 10,
          totalPages: 1
        }
      };

      const result = themeService.fromApiPaginatedResponse(apiResponse);

      expect(result.data).toHaveLength(2);
      expect(result.data[0].id).toBe(1);
      expect(result.data[0].name).toBe('编程');
      expect(result.data[1].id).toBe(2);
      expect(result.data[1].name).toBe('设计');
      expect(result.meta.total).toBe(2);
      expect(result.meta.page).toBe(1);
    });

    test('应该转换主题查询参数为API查询参数', () => {
      const queryParams = {
        page: 1,
        pageSize: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        isActive: true,
        search: '编程'
      };

      const apiParams = themeService.themeQueryParamsToApiParams(queryParams);

      expect(apiParams).toEqual({
        page: 1,
        page_size: 10,
        sort_by: 'created_at',
        sort_order: 'desc',
        is_active: true,
        search: '编程'
      });
    });
  });

  describe('LearningPlanModelService', () => {
    const learningPlanService = new LearningPlanModelService();

    test('应该转换学习计划查询参数为API查询参数', () => {
      const queryParams = {
        page: 1,
        pageSize: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        userId: 100,
        themeId: 5,
        status: LearningPlanStatus.InProgress,
        isCurrent: true,
        isPublic: true,
        includeTags: true,
        includeTheme: true
      };

      const apiParams = learningPlanService.learningPlanQueryParamsToApiParams(queryParams);

      expect(apiParams).toEqual({
        page: 1,
        page_size: 10,
        sort_by: 'created_at',
        sort_order: 'desc',
        user_id: 100,
        theme_id: 5,
        status: 'in_progress',
        is_current: true,
        is_public: true,
        include_tags: true,
        include_theme: true
      });
    });
  });
});
