const { sequelize } = require('../backend/config/database');
const logger = require('../backend/config/logger');
const { User, Theme, LearningPlan, Tag, Exercise, Insight, Note, PlanTag } = require('../backend/models');

/**
 * 为指定用户添加模拟数据
 * @param {string} userId - 用户ID
 */
const addMockDataForUser = async userId => {
  try {
    logger.info(`开始为用户 ${userId} 添加模拟数据...`);

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      logger.error(`用户 ${userId} 不存在`);
      return;
    }

    // 查找人际沟通主题
    const theme = await Theme.findOne({ where: { name: '人际沟通', is_active: true } });
    if (!theme) {
      logger.error('人际沟通主题不存在或未激活');
      return;
    }

    // 创建学习计划
    logger.info('添加学习计划数据...');
    const learningPlan = await LearningPlan.create({
      user_id: userId,
      theme_id: theme.id,
      title: '《极简沟通》2.0',
      description: '学习如何在各种场景中有效沟通，建立良好的人际关系',
      target_days: 30,
      completed_days: 5,
      progress: 17,
      status: 'in_progress',
      start_date: new Date(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      is_current: true
    });

    // 准备标签数据 (移除 plan_id)
    logger.info('准备标签数据...');
    const tagInputData = [
      {
        name: '倾听',
        relevance_score: 0.95,
        weight: 1.0,
        usage_count: 8,
        is_verified: true,
        sort_order: 0
      },
      {
        name: '表达',
        relevance_score: 0.92,
        weight: 0.9,
        usage_count: 6,
        is_verified: true,
        sort_order: 1
      },
      {
        name: '同理心',
        relevance_score: 0.88,
        weight: 0.85,
        usage_count: 5,
        is_verified: true,
        sort_order: 2
      },
      {
        name: '反馈',
        relevance_score: 0.85,
        weight: 0.8,
        usage_count: 4,
        is_verified: true,
        sort_order: 3
      },
      {
        name: '情绪管理',
        relevance_score: 0.82,
        weight: 0.75,
        usage_count: 3,
        is_verified: false,
        sort_order: 4
      }
    ];

    // 批量创建标签 (或查找已存在的)
    logger.info('创建或查找标签...');
    const createdOrFoundTags = await Promise.all(
      tagInputData.map(data => Tag.findOrCreate({
        where: { name: data.name }, // 假设 name 是唯一的标识
        defaults: data
      }))
    );

    // 提取标签实例
    const tags = createdOrFoundTags.map(([tagInstance]) => tagInstance);
    logger.info(`成功创建或找到 ${tags.length} 个标签`);

    // 创建 PlanTag 关联
    logger.info('创建 PlanTag 关联...');
    const planTagData = tags.map((tag, index) => ({
      plan_id: learningPlan.id,
      tag_id: tag.id,
      relevance_score: tagInputData[index].relevance_score, // 从原始输入数据获取
      weight: tagInputData[index].weight,
      is_primary: tagInputData[index].sort_order < 3, // 假设前3个是主要标签
      sort_order: tagInputData[index].sort_order
    }));

    await PlanTag.bulkCreate(planTagData, { ignoreDuplicates: true });
    logger.info(`为学习计划 ${learningPlan.id} 创建了 ${planTagData.length} 个 PlanTag 关联`);

    // 为标签添加练习
    logger.info('添加练习数据...');
    const listeningTag = tags.find(tag => tag.name === '倾听');
    const expressionTag = tags.find(tag => tag.name === '表达');
    const empathyTag = tags.find(tag => tag.name === '同理心');

    if (listeningTag && expressionTag && empathyTag) {
      await Exercise.bulkCreate([
        {
          tag_id: listeningTag.id,
          title: '积极倾听练习',
          description: '在下一次对话中，尝试完全专注于对方的讲话，不打断，不急于回应。记下你注意到的对方的非语言线索，以及你通常会忽略的细节。',
          expected_result: '能够复述对方的关键点，并注意到对方的情绪变化和非语言线索。',
          difficulty: 'beginner',
          time_estimate: 15
        },
        {
          tag_id: expressionTag.id,
          title: '结构化表达练习',
          description: '选择一个你需要向他人解释的复杂话题。使用"情境-问题-解决方案-结果"的结构来组织你的表达。先练习写下来，然后尝试口头表达。',
          expected_result: '能够清晰、有条理地表达复杂信息，让听众容易理解。',
          difficulty: 'intermediate',
          time_estimate: 20
        },
        {
          tag_id: empathyTag.id,
          title: '换位思考练习',
          description: '回想一次你与他人有分歧的情况。尝试从对方的角度思考整个事件，写下对方可能的感受、想法和动机。',
          expected_result: '能够理解并描述对方的观点和感受，即使你不同意。',
          difficulty: 'intermediate',
          time_estimate: 25
        }
      ]);
    }

    // 为标签添加观点
    logger.info('添加观点数据...');
    if (listeningTag && expressionTag && empathyTag) {
      await Insight.bulkCreate([
        {
          tag_id: listeningTag.id,
          content: '真正的倾听不仅是听到对方的话，更是理解对方的情感和需求。',
          source: '《非暴力沟通》',
          background: '研究表明，大多数人在对话中只能记住25%的内容，而积极倾听可以将这一比例提高到60%以上。'
        },
        {
          tag_id: expressionTag.id,
          content: '清晰表达的关键在于先理清自己的思路，知道自己想要传达什么核心信息。',
          source: '《高效能人士的七个习惯》',
          background: '有效的表达需要考虑受众的知识背景和关注点，调整信息的复杂度和重点。'
        },
        {
          tag_id: empathyTag.id,
          content: '同理心是人际关系的桥梁，它让我们能够真正理解他人的处境和感受。',
          source: '《情商》',
          background: '神经科学研究发现，当我们对他人表达同理心时，大脑中的镜像神经元会被激活，让我们能够"感受"到他人的情绪。'
        }
      ]);
    }

    // 为标签添加笔记
    logger.info('添加笔记数据...');
    if (listeningTag && expressionTag) {
      await Note.bulkCreate([
        {
          tag_id: listeningTag.id,
          user_id: userId,
          title: '今日倾听心得',
          content: '今天在会议中尝试了积极倾听技巧，发现当我完全专注于对方发言，不急于打断或回应时，不仅能获取更多信息，对方也更愿意分享。特别注意到了一些平时容易忽略的非语言线索，如语调变化和肢体语言，这些都包含了重要信息。',
          image_url: null,
          likes: 5,
          comments: 2,
          is_ai_generated: false,
          status: 'published'
        },
        {
          tag_id: expressionTag.id,
          user_id: userId,
          title: '结构化表达的实践',
          content: '今天尝试了PREP结构（观点-理由-例子-观点）来组织我的表达，效果很好。在团队会议上介绍项目进展时，先说明了我的核心观点，然后给出支持这一观点的理由，接着用具体例子说明，最后重申观点。同事们反馈说这次的汇报特别清晰，容易理解。',
          image_url: null,
          likes: 3,
          comments: 1,
          is_ai_generated: false,
          status: 'published'
        }
      ]);
    }

    // 更新用户学习天数和等级
    await user.update({
      study_days: 29,
      level: 2
    });

    logger.info(`为用户 ${userId} 添加模拟数据完成！`);
    return {
      success: true,
      message: `为用户 ${userId} 添加模拟数据完成！`,
      data: {
        learningPlan,
        tags,
        user
      }
    };
  } catch (error) {
    logger.error(`为用户 ${userId} 添加模拟数据失败: ${error.message}`);
    logger.error(error.stack);
    return {
      success: false,
      message: `为用户 ${userId} 添加模拟数据失败: ${error.message}`,
      error
    };
  }
};

// 如果直接运行此脚本，则需要提供用户ID
if (require.main === module) {
  const userId = process.argv[2];

  if (!userId) {
    console.error('请提供用户ID作为参数');
    process.exit(1);
  }

  addMockDataForUser(userId)
    .then(result => {
      if (result.success) {
        console.log(result.message);
        process.exit(0);
      } else {
        console.error(result.message);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('执行脚本时发生错误:', error);
      process.exit(1);
    });
} else {
  // 作为模块导出
  module.exports = {
    addMockDataForUser
  };
}
