// 测试API调用
require('dotenv').config({ path: './backend/.env' });
const axios = require('axios');

// 从环境变量中获取配置
const apiKey = process.env.ARK_API_KEY;
const apiUrl = process.env.ARK_API_URL;
const model = process.env.ARK_API_MODEL;

console.log('配置信息:');
console.log('API Key:', apiKey);
console.log('API URL:', apiUrl);
console.log('Model:', model);

// 测试函数
async function testAPI() {
  try {
    console.log('正在测试API调用...');

    // 构建请求体
    const requestBody = {
      model: model,
      messages: [
        {
          role: 'user',
          content: '你好，请生成5个关于\'人际沟通\'的标签'
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    };

    // 发送请求
    const response = await axios.post(apiUrl, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 30000
    });

    // 输出响应
    console.log('API调用成功!');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

    return true;
  } catch (error) {
    console.error('API调用失败!');

    if (error.response) {
      // 服务器返回了错误响应
      console.error('状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('没有收到响应:', error.request);
    } else {
      // 设置请求时发生错误
      console.error('错误信息:', error.message);
    }

    return false;
  }
}

// 执行测试
testAPI()
  .then(success => {
    if (success) {
      console.log('API测试成功!');
    } else {
      console.log('API测试失败!');
    }
  })
  .catch(err => {
    console.error('测试过程中发生未处理的错误:', err);
  });
