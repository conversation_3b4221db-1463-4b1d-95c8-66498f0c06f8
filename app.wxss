/**app.wxss**/
/* 导入设计系统变量 */
@import './styles/variables.wxss';

/* 全局基础样式 */
page {
  background: linear-gradient(180deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
  min-height: 100vh;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 0;
  box-sizing: border-box;
}

/* 玻璃卡片基础样式 */
.glass-card {
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-md);
  box-shadow: 0 8rpx 32rpx var(--card-shadow);
  border: 1px solid var(--card-border);
  padding: var(--space-md);
  margin: 0 var(--space-sm) var(--space-sm) var(--space-sm);
  overflow: hidden;
  transition: all var(--transition-normal) var(--easing-standard);
  width: calc(100% - var(--space-md));
  box-sizing: border-box;
}

.glass-card:active {
  transform: scale(0.98);
  background: var(--card-bg);
  opacity: 0.9;
}

/* 渐变蓝色按钮 */
.gradient-button {
  font-size: var(--font-size-sm);
  color: var(--white);
  background: linear-gradient(135deg, var(--button-gradient-start), var(--button-gradient-end));
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  border: none;
  box-shadow: 0 var(--space-xs) var(--space-sm) var(--button-shadow);
  text-align: center;
}

/* 统一的文本颜色 */
.text-primary {
  color: var(--text-color-primary);
}

.text-secondary {
  color: var(--text-color-secondary);
}

.text-light {
  color: var(--text-color-disabled);
}

.text-accent {
  color: var(--primary-color);
}

/* 统一的边框和分割线 */
.border-light {
  border: 1px solid var(--divider-color);
}

.divider {
  height: 2rpx;
  background: var(--divider-color);
  margin: var(--space-sm) 0;
}

/* 微调旧样式，确保兼容性 */
.container view {
  box-sizing: border-box;
}