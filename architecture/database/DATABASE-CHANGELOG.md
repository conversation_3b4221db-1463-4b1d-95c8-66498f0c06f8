# 数据库变更日志

## 概述

本文档记录AIBUBB项目数据库结构的历史变更，包括表结构修改、索引优化、数据迁移等重要变更。

**重要提示**: 所有数据库结构的创建、修改和演变均通过 `backend/migrations/` 目录下的 Sequelize 迁移脚本进行管理。迁移脚本是数据库结构的唯一真实来源 (Single Source of Truth)。

## 变更记录

### 2023年重大升级 (V3)

#### 核心架构调整
- **LearningPlan和Tag关系重构**: 从一对多调整为多对多关系
  - 移除Tag表中的plan_id字段
  - 创建PlanTag关联表实现多对多关系
  - 支持一个标签属于多个学习计划

#### 新增表结构
- **DailyContent表**: 存储学习计划的每日内容
  ```sql
  CREATE TABLE DailyContent (
    id INT AUTO_INCREMENT PRIMARY KEY,
    plan_id INT NOT NULL COMMENT '所属学习计划ID',
    day_number INT NOT NULL COMMENT '天数（第几天）',
    title VARCHAR(100) NOT NULL COMMENT '日内容标题',
    content TEXT NOT NULL COMMENT '日内容详情',
    is_completed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已完成',
    completion_date DATETIME NULL COMMENT '完成日期',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX daily_content_plan_id_idx (plan_id),
    UNIQUE INDEX daily_content_plan_day_idx (plan_id, day_number),
    CONSTRAINT fk_daily_content_plan FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE
  );
  ```

#### 数据规范化改进
1. **命名规范统一**:
   - 所有表名和字段名采用统一命名规范
   - 主键统一命名为id，外键命名为{表名}_id
   - 时间戳字段统一为created_at和updated_at

2. **数据类型优化**:
   - 用户表主键优化，支持大规模用户增长
   - 合理使用VARCHAR、TEXT、JSON等类型
   - 为需要搜索的字段添加FULLTEXT索引

3. **软删除机制**:
   - 主要表添加deleted_at字段，支持软删除
   - 实现级联软删除机制，保持数据一致性
   - 添加数据恢复功能

4. **JSON字段优化**:
   - 将大型JSON字段拆分为独立的列
   - 为频繁查询的JSON属性创建独立列
   - 保留低频访问属性在JSON字段中

5. **索引策略优化**:
   - 为所有外键、常用查询字段添加索引
   - 为标签、内容标题等添加全文索引
   - 优化复合索引设计，提高查询性能

### 数据库修复和完善

#### 结构修复
- **DailyContent表创建**: 解决模型定义与实际表结构不一致问题
- **外键约束完善**: 添加缺失的外键约束，确保引用完整性
- **索引优化**: 根据查询模式优化索引配置

#### 代码质量提升
- **模型名称统一**: 修复DailyContent模型表名不一致问题
- **错误处理标准化**: 创建通用错误处理工具
- **迁移脚本框架**: 建立标准化的迁移管理机制

### 预留功能表说明

以下表是为未来功能预留的，目前没有对应的业务模型：

1. **Achievement**: 成就系统表 - 存储用户可获得的成就信息
2. **Level**: 用户等级表 - 定义用户等级系统
3. **USettings**: 用户设置表 - 存储用户个性化设置
4. **UserAchievement**: 用户成就关联表 - 记录用户获得的成就
5. **UserContentProgress**: 用户内容进度表 - 跟踪用户学习进度
6. **UserLearningStats**: 用户学习统计表 - 存储学习数据统计
7. **UserReward**: 用户奖励表 - 记录用户获得的奖励

这些表构成了完整的用户成就和奖励系统，以及学习进度跟踪系统，为未来的游戏化功能提供数据基础。

## 迁移管理机制

### 迁移脚本框架
- **Migration基类**: 提供标准的迁移脚本框架
- **向前/向后迁移**: 支持up和down操作
- **状态记录**: 自动记录迁移执行状态

### 迁移管理工具
- **MigrationManager**: 管理迁移脚本执行
- **命令行工具**: migration-cli.js提供友好的操作界面
- **模板生成器**: create-migration.js自动生成迁移脚本模板

### 最佳实践
1. **使用迁移脚本管理所有数据库变更**
2. **迁移脚本包含完整的向前和向后逻辑**
3. **在开发环境充分测试迁移脚本**
4. **保持文档与实际结构同步**

## 性能优化记录

### 索引优化
- **主键索引**: 所有表都有高效的主键设计
- **外键索引**: 所有外键都创建了对应索引
- **复合索引**: 根据查询模式设计复合索引
- **全文索引**: 为搜索功能添加全文索引

### 查询优化
- **JOIN优化**: 优化表关联查询性能
- **分页查询**: 实现高效的分页机制
- **缓存策略**: 配合Redis实现查询缓存

## 数据完整性保障

### 外键约束
- **引用完整性**: 确保所有外键引用有效
- **级联操作**: 合理设置级联删除和更新
- **约束检查**: 定期检查约束完整性

### 数据验证
- **业务规则**: 在应用层实现业务规则验证
- **数据类型**: 使用合适的数据类型确保数据有效性
- **唯一约束**: 设置必要的唯一约束防止重复数据

## 备份和恢复策略

### 备份机制
- **定期备份**: 实现自动化的定期备份
- **增量备份**: 支持增量备份减少存储空间
- **备份验证**: 定期验证备份文件完整性

### 恢复流程
- **快速恢复**: 提供快速恢复机制
- **点时间恢复**: 支持恢复到特定时间点
- **灾难恢复**: 制定完整的灾难恢复计划

## 监控和维护

### 性能监控
- **查询性能**: 监控慢查询和性能瓶颈
- **索引使用**: 分析索引使用效率
- **存储空间**: 监控数据库存储使用情况

### 定期维护
- **统计信息更新**: 定期更新表统计信息
- **索引重建**: 必要时重建索引提高性能
- **数据清理**: 定期清理过期和无用数据

---

**最后更新**: 2025-01-27
**维护责任**: 后端开发团队
**更新频率**: 每次数据库结构变更后及时更新