# 数据库设计更新文档 (2023)

## 更新概述

本文档记录了AIBUBB项目数据库设计的最新更新，包括表结构变更、关系调整和新增表。这些更新旨在提高数据库的一致性、完整性和性能。

## 主要变更

### 1. LearningPlan和Tag关系调整

将LearningPlan和Tag之间的关系从一对多调整为多对多，通过PlanTag关联表实现。

#### 变更前：
- Tag表中有plan_id字段，直接关联到LearningPlan表
- 一个学习计划可以有多个标签，但一个标签只能属于一个学习计划

#### 变更后：
- 移除Tag表中的plan_id字段
- 创建PlanTag关联表，实现多对多关系
- 一个学习计划可以有多个标签，一个标签也可以属于多个学习计划

### 2. 新增DailyContent表

新增DailyContent表，用于存储学习计划的每日内容。

```sql
CREATE TABLE DailyContent (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL COMMENT '所属学习计划ID',
  day_number INT NOT NULL COMMENT '天数（第几天）',
  title VARCHAR(100) NOT NULL COMMENT '日内容标题',
  content TEXT NOT NULL COMMENT '日内容详情',
  is_completed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已完成',
  completion_date DATETIME NULL COMMENT '完成日期',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX daily_content_plan_id_idx (plan_id),
  UNIQUE INDEX daily_content_plan_day_idx (plan_id, day_number),
  CONSTRAINT fk_daily_content_plan FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE
);
```

### 3. 预留功能表

以下表是为未来功能预留的，目前没有对应的模型：

1. **Achievement**: 成就系统表
   - 存储用户可以获得的成就信息
   - 包含成就名称、描述、图标、获取条件等

2. **Level**: 用户等级表
   - 定义用户等级系统
   - 包含等级名称、所需经验值、奖励等

3. **USettings**: 用户设置表
   - 存储用户的个性化设置
   - 包含界面偏好、通知设置等

4. **UserAchievement**: 用户成就关联表
   - 记录用户获得的成就
   - 包含获得时间、进度等

5. **UserContentProgress**: 用户内容进度表
   - 跟踪用户对不同内容的学习进度
   - 包含内容类型、熟练度等

6. **UserLearningStats**: 用户学习统计表
   - 存储用户的学习数据统计
   - 包含总学习时间、完成内容数等

7. **UserReward**: 用户奖励表
   - 记录用户获得的奖励
   - 包含奖励类型、获得时间等

## 迁移管理

为了更好地管理数据库变更，我们引入了基于迁移脚本的数据库变更管理机制：

1. **迁移脚本框架**：
   - 创建了Migration基类，提供标准的迁移脚本框架
   - 支持向前迁移(up)和向后迁移(down)
   - 自动记录迁移执行状态

2. **迁移脚本管理器**：
   - 创建了MigrationManager类，用于管理迁移脚本
   - 支持执行和回滚单个或所有迁移
   - 提供迁移状态查询功能

3. **命令行工具**：
   - 创建了migration-cli.js命令行工具，用于执行迁移操作
   - 支持up、down和status命令
   - 提供友好的命令行界面

4. **迁移脚本模板生成器**：
   - 创建了create-migration.js工具，用于生成迁移脚本模板
   - 自动生成时间戳和文件名
   - 提供标准的迁移脚本结构

## 最佳实践

1. **使用迁移脚本管理数据库变更**：
   - 所有数据库结构变更应通过迁移脚本管理
   - 迁移脚本应包含向前和向后迁移的逻辑
   - 使用命令行工具执行迁移操作

2. **保持文档更新**：
   - 及时更新DATABASE-DESIGN.md文档
   - 记录数据库结构的变更历史
   - 确保文档与实际数据库结构一致

3. **测试迁移脚本**：
   - 在开发环境中测试迁移脚本
   - 确保向前和向后迁移都能正常工作
   - 验证数据完整性和一致性

## 后续计划

1. **完善外键约束**：
   - 检查并添加缺失的外键约束
   - 确保引用完整性

2. **优化索引**：
   - 根据查询模式优化索引
   - 提高查询性能

3. **数据清理**：
   - 清理无用的测试数据
   - 保持数据库整洁
