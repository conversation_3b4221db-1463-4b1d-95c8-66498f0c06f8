# 标签领域（Tag Context）

## 1. 概述

标签领域是AIBUBB系统的支撑领域之一，负责管理标签系统，支持内容分类和检索。标签是一种元数据，用于描述和分类学习内容、学习模板等资源，帮助用户更好地组织和查找信息。

## 2. 业务价值

标签系统为用户提供了灵活的内容组织和检索方式，使用户能够根据自己的需求和兴趣快速找到相关内容。良好的标签系统可以提高用户体验，增强系统的可用性和可发现性。

## 3. 领域模型

### 3.1 实体

#### Tag（标签）

标签是一个简单的标识符，用于分类和描述内容。

```typescript
export class Tag extends AggregateRoot<number> {
  name: string;
  slug: string;
  description: string | null;
  type: TagType;
  status: TagStatus;
  category: TagCategory | null;
  createdBy: number; // 用户ID
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  
  // 方法
  activate(): void;
  deactivate(): void;
  incrementUsage(): void;
  decrementUsage(): void;
  softDelete(): void;
  restore(): void;
  // ...
}
```

#### TagCategory（标签分类）

标签分类用于组织和分类标签。

```typescript
export class TagCategory extends Entity<number> {
  name: string;
  slug: string;
  description: string | null;
  parentCategory: TagCategory | null;
  childCategories: TagCategory[];
  tags: Tag[];
  createdBy: number; // 用户ID
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  
  // 方法
  addTag(tag: Tag): void;
  removeTag(tagId: number): void;
  addChildCategory(category: TagCategory): void;
  removeChildCategory(categoryId: number): void;
  softDelete(): void;
  restore(): void;
  // ...
}
```

#### TagRelation（标签关系）

标签关系用于表示标签之间的关系，如相关、父子等。

```typescript
export class TagRelation extends Entity<number> {
  sourceTagId: number;
  targetTagId: number;
  relationType: TagRelationType;
  createdBy: number; // 用户ID
  createdAt: Date;
  
  // 方法
  equals(other: TagRelation): boolean;
  // ...
}
```

### 3.2 值对象

#### TagType（标签类型）

表示标签的类型，如系统标签、用户标签等。

```typescript
export class TagType extends ValueObject {
  readonly value: string;
  
  private constructor(value: string) {
    super();
    this.value = value;
  }
  
  static SYSTEM = new TagType('system');
  static USER = new TagType('user');
  static RECOMMENDED = new TagType('recommended');
  
  // 方法
  equals(other: TagType): boolean {
    return this.value === other.value;
  }
  
  // ...
}
```

#### TagStatus（标签状态）

表示标签的状态，如活跃、禁用等。

```typescript
export class TagStatus extends ValueObject {
  readonly value: string;
  
  private constructor(value: string) {
    super();
    this.value = value;
  }
  
  static ACTIVE = new TagStatus('active');
  static INACTIVE = new TagStatus('inactive');
  
  // 方法
  equals(other: TagStatus): boolean {
    return this.value === other.value;
  }
  
  // ...
}
```

#### TagRelationType（标签关系类型）

表示标签关系的类型，如相关、父子等。

```typescript
export class TagRelationType extends ValueObject {
  readonly value: string;
  
  private constructor(value: string) {
    super();
    this.value = value;
  }
  
  static RELATED = new TagRelationType('related');
  static PARENT_CHILD = new TagRelationType('parent_child');
  static SYNONYM = new TagRelationType('synonym');
  
  // 方法
  equals(other: TagRelationType): boolean {
    return this.value === other.value;
  }
  
  // ...
}
```

### 3.3 聚合

#### TagAggregate（标签聚合）

以Tag为聚合根的聚合，包含标签及其关系。

#### TagCategoryAggregate（标签分类聚合）

以TagCategory为聚合根的聚合，包含标签分类及其子分类和标签。

### 3.4 仓储

```typescript
export interface TagRepository extends Repository<Tag, number> {
  findByName(name: string): Promise<Tag | null>;
  findBySlug(slug: string): Promise<Tag | null>;
  findByType(type: TagType): Promise<Tag[]>;
  findByStatus(status: TagStatus): Promise<Tag[]>;
  findByCategory(categoryId: number): Promise<Tag[]>;
  findByCreatedBy(userId: number): Promise<Tag[]>;
  findMostUsed(limit: number): Promise<Tag[]>;
  // ...
}

export interface TagCategoryRepository extends Repository<TagCategory, number> {
  findByName(name: string): Promise<TagCategory | null>;
  findBySlug(slug: string): Promise<TagCategory | null>;
  findByParent(parentId: number): Promise<TagCategory[]>;
  findRootCategories(): Promise<TagCategory[]>;
  // ...
}

export interface TagRelationRepository extends Repository<TagRelation, number> {
  findBySourceTag(sourceTagId: number): Promise<TagRelation[]>;
  findByTargetTag(targetTagId: number): Promise<TagRelation[]>;
  findByRelationType(relationType: TagRelationType): Promise<TagRelation[]>;
  // ...
}
```

### 3.5 领域事件

```typescript
export class TagCreatedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'tag.created';
  readonly occurredOn: Date;
  readonly aggregateId: string;
  readonly aggregateType: string;
  readonly version: number;
  readonly payload: {
    id: number;
    name: string;
    type: string;
    createdBy: number;
  };
  
  constructor(tag: Tag) {
    this.eventId = generateUuid();
    this.occurredOn = new Date();
    this.aggregateId = tag.id.toString();
    this.aggregateType = 'Tag';
    this.version = 1;
    this.payload = {
      id: tag.id,
      name: tag.name,
      type: tag.type.value,
      createdBy: tag.createdBy
    };
  }
}

// 其他事件类似...
```

## 4. 应用服务

```typescript
export class TagApplicationService {
  constructor(
    private readonly tagRepository: TagRepository,
    private readonly tagCategoryRepository: TagCategoryRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}
  
  async createTag(command: CreateTagCommand): Promise<TagDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 检查标签名称是否已存在
      const existingTag = await this.tagRepository.findByName(command.name);
      if (existingTag) {
        throw new Error('Tag name already exists');
      }
      
      // 创建标签
      const tag = Tag.create(
        command.name,
        this.generateSlug(command.name),
        command.description,
        TagType.fromString(command.type),
        command.createdBy
      );
      
      // 设置标签分类
      if (command.categoryId) {
        const category = await this.tagCategoryRepository.findById(command.categoryId);
        if (category) {
          tag.category = category;
        }
      }
      
      // 保存标签
      const savedTag = await this.tagRepository.save(tag);
      
      // 返回DTO
      return this.toTagDto(savedTag);
    });
  }
  
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '');
  }
  
  // 其他方法...
}
```

## 5. 与其他上下文的关系

### 5.1 标签领域 - 学习内容领域

标签领域为学习内容领域提供标签服务，学习内容领域通过防腐层与标签领域交互。

```typescript
export class ContentTagService {
  constructor(
    private readonly tagRepository: TagRepository,
    private readonly contentTagRepository: ContentTagRepository
  ) {}
  
  async getTagsForContent(contentId: number): Promise<Tag[]> {
    // 实现...
  }
  
  async addTagToContent(contentId: number, tagId: number): Promise<void> {
    // 实现...
  }
  
  async removeTagFromContent(contentId: number, tagId: number): Promise<void> {
    // 实现...
  }
  
  async getContentsByTag(tagId: number): Promise<Content[]> {
    // 实现...
  }
}
```

### 5.2 标签领域 - 学习模板领域

标签领域为学习模板领域提供标签服务，学习模板领域通过防腐层与标签领域交互。

```typescript
export class TemplateTagService {
  constructor(
    private readonly tagRepository: TagRepository,
    private readonly templateTagRepository: TemplateTagRepository
  ) {}
  
  async getTagsForTemplate(templateId: number): Promise<Tag[]> {
    // 实现...
  }
  
  async addTagToTemplate(templateId: number, tagId: number): Promise<void> {
    // 实现...
  }
  
  async removeTagFromTemplate(templateId: number, tagId: number): Promise<void> {
    // 实现...
  }
  
  async getTemplatesByTag(tagId: number): Promise<Template[]> {
    // 实现...
  }
}
```

### 5.3 标签领域 - 用户领域

标签领域使用用户领域提供的用户信息，通过防腐层与用户领域交互。

```typescript
export class UserAntiCorruptionLayer {
  constructor(private readonly userRepository: UserRepository) {}
  
  async getUserForTag(tagId: number): Promise<User | null> {
    // 实现...
  }
  
  async checkUserPermission(userId: number, tagId: number): Promise<boolean> {
    // 实现...
  }
}
```

## 6. 实施指南

### 6.1 目录结构

```
backend/
  └── domain/
      ├── models/
      │   └── tag/
      │       ├── Tag.ts
      │       ├── TagCategory.ts
      │       ├── TagRelation.ts
      │       ├── TagType.ts
      │       ├── TagStatus.ts
      │       └── TagRelationType.ts
      ├── repositories/
      │   └── tag/
      │       ├── TagRepository.ts
      │       ├── TagCategoryRepository.ts
      │       └── TagRelationRepository.ts
      ├── services/
      │   └── tag/
      │       ├── TagService.ts
      │       └── TagRelationService.ts
      └── events/
          └── tag/
              ├── TagCreatedEvent.ts
              ├── TagUpdatedEvent.ts
              ├── TagDeletedEvent.ts
              └── TagCategoryCreatedEvent.ts
```

### 6.2 实施步骤

1. 创建领域模型
2. 实现仓储接口
3. 实现领域服务
4. 实现应用服务
5. 实现防腐层
6. 集成测试
