# 用户领域（User Context）

## 1. 概述

用户领域是AIBUBB系统的核心领域之一，负责管理用户信息、认证和授权。用户领域是系统的基础，为其他领域提供用户身份和权限管理服务。

## 2. 业务价值

用户领域为系统提供了用户管理和安全保障，确保只有授权用户才能访问系统资源。良好的用户体验和安全性是系统成功的关键因素。

## 3. 领域模型

### 3.1 实体

#### User（用户）

用户是系统的核心实体，代表系统的使用者。

```typescript
export class User extends AggregateRoot<number> {
  username: string;
  email: Email;
  phoneNumber: PhoneNumber | null;
  passwordHash: string;
  salt: string;
  status: UserStatus;
  roles: Role[];
  profile: UserProfile;
  settings: UserSetting[];
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  
  // 方法
  activate(): void;
  deactivate(): void;
  changePassword(newPassword: string): void;
  addRole(role: Role): void;
  removeRole(roleId: number): void;
  hasPermission(permission: string): boolean;
  softDelete(): void;
  restore(): void;
  // ...
}
```

#### Role（角色）

角色是权限的集合，用于管理用户的访问权限。

```typescript
export class Role extends Entity<number> {
  name: string;
  description: string;
  permissions: Permission[];
  
  // 方法
  addPermission(permission: Permission): void;
  removePermission(permissionId: number): void;
  hasPermission(permissionId: number): boolean;
  // ...
}
```

#### Permission（权限）

权限定义了用户可以执行的操作。

```typescript
export class Permission extends Entity<number> {
  name: string;
  description: string;
  resource: string;
  action: string;
  
  // 方法
  equals(other: Permission): boolean;
  // ...
}
```

#### UserProfile（用户资料）

用户资料包含用户的个人信息。

```typescript
export class UserProfile extends Entity<number> {
  userId: number;
  firstName: string;
  lastName: string;
  gender: Gender;
  birthDate: Date | null;
  avatar: string | null;
  bio: string | null;
  location: string | null;
  website: string | null;
  
  // 方法
  updateProfile(profile: Partial<UserProfile>): void;
  // ...
}
```

#### UserSetting（用户设置）

用户设置包含用户的个性化设置。

```typescript
export class UserSetting extends Entity<number> {
  userId: number;
  key: string;
  value: string;
  
  // 方法
  updateValue(value: string): void;
  // ...
}
```

### 3.2 值对象

#### Credential（凭证）

用户的登录凭证，包含用户名/邮箱和密码。

```typescript
export class Credential extends ValueObject {
  readonly identifier: string;
  readonly password: string;
  
  constructor(identifier: string, password: string) {
    super();
    this.identifier = identifier;
    this.password = password;
  }
  
  // 方法
  equals(other: Credential): boolean {
    return this.identifier === other.identifier;
  }
  
  // ...
}
```

#### Email（电子邮件）

用户的电子邮件地址。

```typescript
export class Email extends ValueObject {
  readonly value: string;
  
  constructor(value: string) {
    super();
    if (!this.isValid(value)) {
      throw new Error('Invalid email address');
    }
    this.value = value;
  }
  
  private isValid(email: string): boolean {
    // 邮箱验证逻辑
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
  
  // 方法
  equals(other: Email): boolean {
    return this.value === other.value;
  }
  
  // ...
}
```

#### PhoneNumber（电话号码）

用户的电话号码。

```typescript
export class PhoneNumber extends ValueObject {
  readonly value: string;
  
  constructor(value: string) {
    super();
    if (!this.isValid(value)) {
      throw new Error('Invalid phone number');
    }
    this.value = value;
  }
  
  private isValid(phoneNumber: string): boolean {
    // 电话号码验证逻辑
    return /^\d{11}$/.test(phoneNumber);
  }
  
  // 方法
  equals(other: PhoneNumber): boolean {
    return this.value === other.value;
  }
  
  // ...
}
```

#### UserStatus（用户状态）

表示用户的状态，如活跃、禁用、未验证等。

```typescript
export class UserStatus extends ValueObject {
  readonly value: string;
  
  private constructor(value: string) {
    super();
    this.value = value;
  }
  
  static ACTIVE = new UserStatus('active');
  static INACTIVE = new UserStatus('inactive');
  static PENDING = new UserStatus('pending');
  static BANNED = new UserStatus('banned');
  
  // 方法
  equals(other: UserStatus): boolean {
    return this.value === other.value;
  }
  
  // ...
}
```

#### Gender（性别）

表示用户的性别。

```typescript
export class Gender extends ValueObject {
  readonly value: string;
  
  private constructor(value: string) {
    super();
    this.value = value;
  }
  
  static MALE = new Gender('male');
  static FEMALE = new Gender('female');
  static OTHER = new Gender('other');
  static PREFER_NOT_TO_SAY = new Gender('prefer_not_to_say');
  
  // 方法
  equals(other: Gender): boolean {
    return this.value === other.value;
  }
  
  // ...
}
```

### 3.3 聚合

#### UserAggregate（用户聚合）

以User为聚合根的聚合，包含用户及其资料和设置。

#### RoleAggregate（角色聚合）

以Role为聚合根的聚合，包含角色及其权限。

### 3.4 仓储

```typescript
export interface UserRepository extends Repository<User, number> {
  findByUsername(username: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findByPhoneNumber(phoneNumber: string): Promise<User | null>;
  findByStatus(status: UserStatus): Promise<User[]>;
  findByRole(roleId: number): Promise<User[]>;
  // ...
}

export interface RoleRepository extends Repository<Role, number> {
  findByName(name: string): Promise<Role | null>;
  findByPermission(permissionId: number): Promise<Role[]>;
  // ...
}

export interface PermissionRepository extends Repository<Permission, number> {
  findByName(name: string): Promise<Permission | null>;
  findByResource(resource: string): Promise<Permission[]>;
  findByAction(action: string): Promise<Permission[]>;
  // ...
}
```

### 3.5 领域事件

```typescript
export class UserCreatedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'user.created';
  readonly occurredOn: Date;
  readonly aggregateId: string;
  readonly aggregateType: string;
  readonly version: number;
  readonly payload: {
    id: number;
    username: string;
    email: string;
  };
  
  constructor(user: User) {
    this.eventId = generateUuid();
    this.occurredOn = new Date();
    this.aggregateId = user.id.toString();
    this.aggregateType = 'User';
    this.version = 1;
    this.payload = {
      id: user.id,
      username: user.username,
      email: user.email.value
    };
  }
}

// 其他事件类似...
```

## 4. 应用服务

```typescript
export class UserApplicationService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository,
    private readonly passwordHasher: PasswordHasher,
    private readonly unitOfWork: UnitOfWork
  ) {}
  
  async registerUser(command: RegisterUserCommand): Promise<UserDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 检查用户名和邮箱是否已存在
      const existingUserByUsername = await this.userRepository.findByUsername(command.username);
      if (existingUserByUsername) {
        throw new Error('Username already exists');
      }
      
      const existingUserByEmail = await this.userRepository.findByEmail(command.email);
      if (existingUserByEmail) {
        throw new Error('Email already exists');
      }
      
      // 创建用户
      const { salt, hash } = this.passwordHasher.hashPassword(command.password);
      
      const user = User.create(
        command.username,
        new Email(command.email),
        command.phoneNumber ? new PhoneNumber(command.phoneNumber) : null,
        hash,
        salt,
        UserStatus.PENDING
      );
      
      // 创建用户资料
      const profile = UserProfile.create(
        user.id,
        command.firstName,
        command.lastName,
        Gender.fromString(command.gender),
        command.birthDate ? new Date(command.birthDate) : null
      );
      
      user.profile = profile;
      
      // 添加默认角色
      const defaultRole = await this.roleRepository.findByName('user');
      if (defaultRole) {
        user.addRole(defaultRole);
      }
      
      // 保存用户
      const savedUser = await this.userRepository.save(user);
      
      // 返回DTO
      return this.toUserDto(savedUser);
    });
  }
  
  // 其他方法...
}
```

## 5. 与其他上下文的关系

### 5.1 用户领域 - 学习内容领域

用户领域为学习内容领域提供用户信息和权限验证服务。

```typescript
export class ContentUserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly permissionChecker: PermissionChecker
  ) {}
  
  async canUserAccessContent(userId: number, contentId: number): Promise<boolean> {
    // 实现...
  }
  
  async getUserForContent(contentId: number): Promise<User | null> {
    // 实现...
  }
}
```

### 5.2 用户领域 - 学习模板领域

用户领域为学习模板领域提供用户信息和权限验证服务。

```typescript
export class TemplateUserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly permissionChecker: PermissionChecker
  ) {}
  
  async canUserAccessTemplate(userId: number, templateId: number): Promise<boolean> {
    // 实现...
  }
  
  async getUserForTemplate(templateId: number): Promise<User | null> {
    // 实现...
  }
}
```

### 5.3 用户领域 - 游戏化领域

用户领域与游戏化领域紧密合作，共同管理用户的成就和奖励。

```typescript
export class GamificationUserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly achievementRepository: AchievementRepository
  ) {}
  
  async getUserAchievements(userId: number): Promise<Achievement[]> {
    // 实现...
  }
  
  async addAchievementToUser(userId: number, achievementId: number): Promise<void> {
    // 实现...
  }
}
```

## 6. 实施指南

### 6.1 目录结构

```
backend/
  └── domain/
      ├── models/
      │   └── user/
      │       ├── User.ts
      │       ├── Role.ts
      │       ├── Permission.ts
      │       ├── UserProfile.ts
      │       ├── UserSetting.ts
      │       ├── Credential.ts
      │       ├── Email.ts
      │       ├── PhoneNumber.ts
      │       ├── UserStatus.ts
      │       └── Gender.ts
      ├── repositories/
      │   └── user/
      │       ├── UserRepository.ts
      │       ├── RoleRepository.ts
      │       └── PermissionRepository.ts
      ├── services/
      │   └── user/
      │       ├── UserService.ts
      │       ├── AuthenticationService.ts
      │       └── AuthorizationService.ts
      └── events/
          └── user/
              ├── UserCreatedEvent.ts
              ├── UserUpdatedEvent.ts
              ├── UserDeletedEvent.ts
              └── UserLoggedInEvent.ts
```

### 6.2 实施步骤

1. 创建领域模型
2. 实现仓储接口
3. 实现领域服务
4. 实现应用服务
5. 实现防腐层
6. 集成测试
