# AIBUBB API 设计文档

## 概述

本文档定义了AIBUBB应用的API接口规范，包括URL、HTTP方法、请求参数、响应格式和状态码。这些API接口用于支持AIBUBB微信小程序的各项功能，包括用户认证、学习计划管理、标签系统、内容管理和社交互动等。

**重要说明：**

*   **当前激活版本:** 本项目 API 同时存在 V1 和 V2 版本。V1 版本提供基础功能，V2 版本提供增强功能（如软删除、批量操作等）。根据 `backend/server.js` 的配置，当前**两个版本都已激活并可用**。
*   **版本差异:** V2 版本主要增加了以下功能：
    * 软删除功能：通过 `/api/v2/{resource}/:id/soft-delete` 端点实现
    * 恢复功能：通过 `/api/v2/{resource}/:id/restore` 端点实现
    * 查看已删除内容：通过 `/api/v2/{resource}/deleted` 端点实现
    * 批量操作：通过 `/api/v2/batch` 端点实现
*   **文档时效性:** 本文档主要描述 V1 API，V2 API 的详细说明请通过运行时生成的文档查看：
    *   Swagger UI: `/api-docs`
    *   ReDoc: `/redoc`
    *   **(注意：运行时文档依赖于 `backend/config/swagger.js` 中定义的 Schema 和从路由/控制器文件 (`options.apis` 指定) 中提取的 `@swagger` 注释。修改 API 或数据模型时，务必同步更新 `swagger.js` 中的 Schema 定义和相关注释，以保证文档准确性。)**
*   **端点列表:** 一个更简洁的端点列表可参考 [API 端点列表 (API-ENDPOINTS.md)](./API-ENDPOINTS.md)。

## 基础信息

### V1 版本
- **基础URL**: `https://api.aibubb.com/api/v1`
- **开发环境URL**: `http://localhost:9090/api/v1`
- **API文档**: `/api-docs` (Swagger UI), `/redoc` (ReDoc)
- **认证方式**: Bearer Token (JWT)

### V2 版本
- **基础URL**: `https://api.aibubb.com/api/v2`
- **开发环境URL**: `http://localhost:9090/api/v2`
- **API文档**: `/api-docs` (Swagger UI), `/redoc` (ReDoc)
- **认证方式**: Bearer Token (JWT)
- **主要特性**: 软删除、恢复功能、批量操作

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      // 错误详情
    }
  }
}
```

### 常见错误码

| 错误码 | 描述 |
|--------|------|
| `BAD_REQUEST` | 请求参数错误 |
| `UNAUTHORIZED` | 未授权访问 |
| `FORBIDDEN` | 禁止访问 |
| `NOT_FOUND` | 资源不存在 |
| `VALIDATION_ERROR` | 数据验证失败 |
| `SERVER_ERROR` | 服务器内部错误 |

## API端点

### 认证相关

#### 微信登录

- **URL**: `/auth/login`
- **方法**: `POST`
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "code": "微信登录code",
    "userInfo": {
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "gender": 1
    }
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "token": "JWT令牌",
      "userId": "用户ID",
      "expiresIn": 86400,
      "isNewUser": false
    }
  }
  ```

#### 手机号注册

- **URL**: `/auth/register/phone`
- **方法**: `POST`
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "phone": "手机号",
    "password": "密码",
    "nickname": "用户昵称"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "token": "JWT令牌",
      "userId": "用户ID",
      "expiresIn": 86400,
      "isNewUser": true
    }
  }
  ```

#### 手机号登录

- **URL**: `/auth/login/phone`
- **方法**: `POST`
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "phone": "手机号",
    "password": "密码"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "token": "JWT令牌",
      "userId": "用户ID",
      "expiresIn": 86400,
      "isNewUser": false
    }
  }
  ```

#### 获取用户信息

- **URL**: `/auth/user`
- **方法**: `GET`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "userId": "用户ID",
      "nickname": "用户昵称",
      "avatarUrl": "头像URL",
      "gender": 1,
      "studyDays": 10,
      "level": 2,
      "lastLoginAt": "2023-06-15T08:00:00Z"
    }
  }
  ```

### 学习计划相关

#### 获取学习计划列表

- **URL**: `/learning-plans`
- **方法**: `GET`
- **认证**: 需要
- **查询参数**:
  - `themeId`: 主题ID (可选)
  - `status`: 状态 (可选, 枚举: not_started, in_progress, completed, paused)
  - `page`: 页码 (可选, 默认: 1)
  - `pageSize`: 每页数量 (可选, 默认: 10)
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "plans": [
        {
          "id": 101,
          "themeId": 1,
          "themeName": "人际沟通",
          "title": "提升与伴侣的沟通能力",
          "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
          "targetDays": 14,
          "completedDays": 5,
          "progress": 35,
          "status": "in_progress",
          "startDate": "2023-06-15",
          "endDate": "2023-06-29",
          "isCurrent": true,
          "createdAt": "2023-06-15T08:00:00Z"
        }
      ],
      "pagination": {
        "total": 1,
        "page": 1,
        "pageSize": 10,
        "totalPages": 1
      }
    }
  }
  ```

#### 获取学习计划详情

- **URL**: `/learning-plans/:id`
- **方法**: `GET`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "id": 101,
      "themeId": 1,
      "themeName": "人际沟通",
      "title": "提升与伴侣的沟通能力",
      "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
      "targetDays": 14,
      "completedDays": 5,
      "progress": 35,
      "status": "in_progress",
      "startDate": "2023-06-15",
      "endDate": "2023-06-29",
      "isCurrent": true,
      "createdAt": "2023-06-15T08:00:00Z",
      "tags": [
        {
          "id": 1001,
          "name": "倾听",
          "relevanceScore": 0.95,
          "sortOrder": 1
        }
      ],
      "dailyContents": [
        {
          "id": 2001,
          "dayNumber": 1,
          "title": "倾听的艺术",
          "content": "今天我们将学习倾听的基本技巧...",
          "isCompleted": true
        }
      ]
    }
  }
  ```

#### 创建学习计划

- **URL**: `/learning-plans`
- **方法**: `POST`
- **认证**: 需要
- **请求体**:
  ```json
  {
    "themeId": 1,
    "title": "提升与伴侣的沟通能力",
    "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
    "targetDays": 14,
    "troubleContent": "在与伴侣交流时经常出现误解，导致冲突",
    "learningIntensity": "medium"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "id": 101,
      "themeId": 1,
      "themeName": "人际沟通",
      "title": "提升与伴侣的沟通能力",
      "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
      "targetDays": 14,
      "completedDays": 0,
      "progress": 0,
      "status": "not_started",
      "startDate": "2023-06-15",
      "endDate": "2023-06-29",
      "isCurrent": true,
      "createdAt": "2023-06-15T08:00:00Z"
    }
  }
  ```

#### 设置当前学习计划

- **URL**: `/learning-plans/:id/activate`
- **方法**: `PUT`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "message": "已激活为当前学习计划"
  }
  ```

#### [新增] 获取系统默认学习计划

- **URL**: `/learning-plans/system/default`
- **方法**: `GET`
- **认证**: 可选 (根据 `auth-config.js`，未登录也可访问，但有认证会使用用户信息)
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      // 系统默认学习计划详情，结构类似 GET /learning-plans/:id
    }
  }
  ```

### 标签相关

#### 获取学习计划的标签

- **URL**: `/tags/learning-plans/:id/tags`
- **方法**: `GET`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "tags": [
        {
          "id": 1001,
          "name": "倾听",
          "relevanceScore": 0.95,
          "weight": 0.8,
          "usageCount": 5,
          "isVerified": true,
          "sortOrder": 0,
          "category": {
            "id": 1,
            "name": "基础技能"
          }
        }
      ]
    }
  }
  ```

#### 获取当前学习计划的标签

- **URL**: `/tags/current-plan/tags`
- **方法**: `GET`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "tags": [
        {
          "id": 1001,
          "name": "倾听",
          "relevanceScore": 0.95,
          "weight": 0.8,
          "usageCount": 5,
          "isVerified": true,
          "sortOrder": 0,
          "category": {
            "id": 1,
            "name": "基础技能"
          }
        }
      ]
    }
  }
  ```

#### 获取标签详情

- **URL**: `/tags/tags/:id`
- **方法**: `GET`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "id": 1001,
      "name": "倾听",
      "relevanceScore": 0.95,
      "weight": 0.8,
      "usageCount": 5,
      "isVerified": true,
      "sortOrder": 0,
      "createdAt": "2023-06-15T08:00:00Z",
      "plan": {
        "id": 101,
        "title": "提升与伴侣的沟通能力"
      },
      "category": {
        "id": 1,
        "name": "基础技能",
        "level": 1
      },
      "synonyms": [
        {
          "id": 1,
          "name": "聆听",
          "similarityScore": 0.9
        }
      ]
    }
  }
  ```

#### 增加标签使用次数

- **URL**: `/tags/tags/:id/increment-usage`
- **方法**: `PUT`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "message": "标签使用次数已更新",
    "data": {
      "id": 1001,
      "name": "倾听",
      "usageCount": 6
    }
  }
  ```

### 内容相关

#### 获取泡泡内容

- **URL**: `/bubble/content`
- **方法**: `GET`
- **认证**: 需要
- **查询参数**:
  - `tagId`: 标签ID
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "contentType": "exercise",
      "content": {
        "id": 1,
        "title": "倾听练习",
        "description": "与伴侣进行5分钟的倾听练习，不打断对方",
        "difficulty": "medium"
      }
    }
  }
  ```

#### 获取标签下的笔记列表

- **URL**: `/notes/tags/:tagId/notes`
- **方法**: `GET`
- **认证**: 需要
- **查询参数**:
  - `page`: 页码 (可选, 默认: 1)
  - `pageSize`: 每页数量 (可选, 默认: 10)
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "notes": [
        {
          "id": 1,
          "title": "如何成为更好的倾听者",
          "content": "倾听是沟通的基础...",
          "imageUrl": "https://example.com/image.jpg",
          "createdAt": "2023-06-15T08:00:00Z",
          "user": {
            "id": "user-1",
            "nickname": "用户昵称",
            "avatarUrl": "头像URL"
          },
          "likesCount": 5,
          "commentsCount": 2
        }
      ],
      "pagination": {
        "total": 1,
        "page": 1,
        "pageSize": 10,
        "totalPages": 1
      }
    }
  }
  ```

#### 创建笔记

- **URL**: `/notes`
- **方法**: `POST`
- **认证**: 需要
- **请求体**:
  ```json
  {
    "title": "如何成为更好的倾听者",
    "content": "倾听是沟通的基础...",
    "tagId": 1001,
    "imageUrl": "https://example.com/image.jpg",
    "isPublic": true
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "id": 1,
      "title": "如何成为更好的倾听者",
      "content": "倾听是沟通的基础...",
      "imageUrl": "https://example.com/image.jpg",
      "createdAt": "2023-06-15T08:00:00Z",
      "tagId": 1001,
      "tagName": "倾听",
      "isPublic": true
    }
  }
  ```

### 广场相关

#### 获取广场标签列表

- **URL**: `/square/tags`
- **方法**: `GET`
- **认证**: 不需要
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "tags": [
        {
          "id": 1001,
          "name": "倾听",
          "notesCount": 5
        }
      ]
    }
  }
  ```

#### 获取广场笔记列表

- **URL**: `/square/notes`
- **方法**: `GET`
- **认证**: 不需要
- **查询参数**:
  - `tagId`: 标签ID (可选, 使用"all"获取所有标签的笔记)
  - `page`: 页码 (可选, 默认: 1)
  - `pageSize`: 每页数量 (可选, 默认: 10)
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "notes": [
        {
          "id": 1,
          "title": "如何成为更好的倾听者",
          "content": "倾听是沟通的基础...",
          "imageUrl": "https://example.com/image.jpg",
          "createdAt": "2023-06-15T08:00:00Z",
          "user": {
            "id": "user-1",
            "nickname": "用户昵称",
            "avatarUrl": "头像URL"
          },
          "tag": {
            "id": 1001,
            "name": "倾听"
          },
          "likesCount": 5,
          "commentsCount": 2
        }
      ],
      "pagination": {
        "total": 1,
        "page": 1,
        "pageSize": 10,
        "totalPages": 1
      }
    }
  }
  ```

### 统计相关

#### 获取学习统计数据

- **URL**: `/statistics/learning`
- **方法**: `GET`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "studyDays": 10,
      "totalTimeSpent": 300,
      "exercisesCompleted": 15,
      "insightsViewed": 25,
      "notesCreated": 5,
      "currentStreak": 3,
      "longestStreak": 5,
      "lastActiveDate": "2023-06-15"
    }
  }
  ```

#### 获取每日学习记录

- **URL**: `/statistics/daily`
- **方法**: `GET`
- **认证**: 需要
- **查询参数**:
  - `startDate`: 开始日期 (可选, 格式: YYYY-MM-DD)
  - `endDate`: 结束日期 (可选, 格式: YYYY-MM-DD)
  - `page`: 页码 (可选, 默认: 1)
  - `pageSize`: 每页数量 (可选, 默认: 10)
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "records": [
        {
          "date": "2023-06-15",
          "timeSpent": 30,
          "exercisesCompleted": 2,
          "insightsViewed": 3,
          "notesCreated": 1,
          "bubbleInteractions": 5,
          "hasActivity": true
        }
      ],
      "pagination": {
        "total": 1,
        "page": 1,
        "pageSize": 10,
        "totalPages": 1
      }
    }
  }
  ```

## 认证策略

### 公开路由（无需认证）

- `/auth/login` (POST)
- `/auth/register/phone` (POST)
- `/auth/login/phone` (POST)
- `/themes` (GET)
- `/themes/:id` (GET)
- `/health` (GET)
- `/square/notes` (GET)
- `/square/tags` (GET)

### 可选认证路由（有认证则使用认证信息，无认证也可访问）

- `/tags/system/default/tags` (GET)
- `/learning-plans/system/default` (GET)
- `/square/notes/:id` (GET)

### 需要认证的路由

- 所有其他路由

## 错误处理

### 常见HTTP状态码

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": {
      "title": "标题不能为空"
    }
  }
}
```

## 版本控制

API版本通过URL路径中的版本号指定，例如 `/api/v1/auth/login`。当API发生不兼容的变更时，将增加版本号，例如 `/api/v2/auth/login`。

## 限流策略

为了保护API不被滥用，实施了以下限流策略：

- 普通请求: 每个IP每15分钟最多100个请求
- 登录请求: 每个IP每小时最多20次登录尝试

## 安全考虑

- 所有API通信使用HTTPS加密
- 敏感数据（如密码）在传输和存储时都经过加密
- JWT令牌有效期为24小时
- 刷新令牌有效期为7天

## V2版本API

V2版本API在V1版本的基础上增加了软删除、恢复功能和批量操作等特性。以下是V2版本API的主要端点和示例。

### 软删除功能

#### 软删除标签

- **URL**: `/api/v2/tags/:id/soft-delete`
- **方法**: `DELETE`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "message": "标签已软删除",
    "data": {
      "id": 1001,
      "name": "倾听",
      "deletedAt": "2025-05-06T10:00:00Z"
    }
  }
  ```

#### 恢复已删除的标签

- **URL**: `/api/v2/tags/:id/restore`
- **方法**: `PUT`
- **认证**: 需要
- **响应**:
  ```json
  {
    "success": true,
    "message": "标签已恢复",
    "data": {
      "id": 1001,
      "name": "倾听",
      "deletedAt": null
    }
  }
  ```

#### 获取已删除的标签列表

- **URL**: `/api/v2/tags/deleted`
- **方法**: `GET`
- **认证**: 需要
- **查询参数**:
  - `page`: 页码 (可选, 默认: 1)
  - `pageSize`: 每页数量 (可选, 默认: 10)
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "tags": [
        {
          "id": 1001,
          "name": "倾听",
          "deletedAt": "2025-05-06T10:00:00Z"
        }
      ],
      "pagination": {
        "total": 1,
        "page": 1,
        "pageSize": 10,
        "totalPages": 1
      }
    }
  }
  ```

### 批量操作

#### 批量软删除标签

- **URL**: `/api/v2/batch/tags/soft-delete`
- **方法**: `POST`
- **认证**: 需要
- **请求体**:
  ```json
  {
    "ids": [1001, 1002, 1003]
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "标签已批量软删除",
    "data": {
      "successCount": 3,
      "failedCount": 0,
      "results": [
        {
          "id": 1001,
          "success": true
        },
        {
          "id": 1002,
          "success": true
        },
        {
          "id": 1003,
          "success": true
        }
      ]
    }
  }
  ```

#### 批量恢复标签

- **URL**: `/api/v2/batch/tags/restore`
- **方法**: `POST`
- **认证**: 需要
- **请求体**:
  ```json
  {
    "ids": [1001, 1002, 1003]
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "标签已批量恢复",
    "data": {
      "successCount": 3,
      "failedCount": 0,
      "results": [
        {
          "id": 1001,
          "success": true
        },
        {
          "id": 1002,
          "success": true
        },
        {
          "id": 1003,
          "success": true
        }
      ]
    }
  }
  ```

### 其他V2版本API

V2版本还提供了以下模块的软删除、恢复和批量操作功能：

- 学习计划 (`/api/v2/learning-plans`)
- 笔记 (`/api/v2/notes`)
- 练习 (`/api/v2/exercises`)
- 洞察 (`/api/v2/insights`)
- 主题 (`/api/v2/themes`)
- 每日内容 (`/api/v2/daily-contents`)

每个模块都遵循相同的API设计模式，提供以下端点：

- `/:id/soft-delete`: 软删除资源
- `/:id/restore`: 恢复已删除的资源
- `/deleted`: 获取已删除的资源列表

批量操作通过 `/api/v2/batch/{resource}/{action}` 端点提供，支持的操作包括：

- `soft-delete`: 批量软删除
- `restore`: 批量恢复
- `permanent-delete`: 批量永久删除（需要管理员权限）

详细的API定义请参考Swagger文档。
