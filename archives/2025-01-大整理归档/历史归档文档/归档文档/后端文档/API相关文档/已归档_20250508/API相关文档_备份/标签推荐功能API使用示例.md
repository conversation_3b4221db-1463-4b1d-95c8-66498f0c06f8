# 标签推荐功能API使用示例

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-15 |
| 最后更新 | 2025-05-15 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档提供了AIBUBB项目中标签推荐功能API的使用示例，帮助开发人员理解如何使用这些API。标签推荐功能支持两种推荐模式：基于现有模板推荐标签和基于模板内容推荐标签。

## 2. API基础信息

- **基础URL**: `/api/v2/learning-templates/recommended-tags`
- **认证要求**: 不需要认证
- **响应格式**: JSON
- **版本**: V2

## 3. 使用示例

### 3.1 基于现有模板推荐标签

当用户正在编辑一个现有的学习模板时，可以使用此API获取推荐的标签，帮助用户更好地标记模板内容。

```javascript
/**
 * 基于现有模板获取推荐标签
 * @param {number} templateId - 学习模板ID
 * @param {number} limit - 返回的最大数量
 * @returns {Promise<Array>} - 推荐的标签列表
 */
async function getRecommendedTagsForTemplate(templateId, limit = 10) {
  try {
    const url = `/api/v2/learning-templates/recommended-tags?templateId=${templateId}&limit=${limit}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取推荐标签失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取推荐标签时出错:', error);
    throw error;
  }
}

// 使用示例
getRecommendedTagsForTemplate(123, 5)
  .then(tags => {
    console.log('推荐的标签:', tags);
    // 处理推荐的标签
    // 例如，显示在标签选择界面中
    displayRecommendedTags(tags);
  })
  .catch(error => {
    // 处理错误
    showErrorMessage('无法获取推荐标签');
  });

// 显示推荐标签的函数
function displayRecommendedTags(tags) {
  const tagContainer = document.getElementById('recommended-tags');
  tagContainer.innerHTML = '';
  
  tags.forEach(tag => {
    const tagElement = document.createElement('div');
    tagElement.className = 'tag-item';
    tagElement.textContent = tag.name;
    tagElement.dataset.tagId = tag.id;
    
    // 添加点击事件，选择标签
    tagElement.addEventListener('click', () => {
      selectTag(tag.id, tag.name);
    });
    
    tagContainer.appendChild(tagElement);
  });
}
```

### 3.2 基于模板内容推荐标签

当用户正在创建一个新的学习模板时，可以根据用户输入的标题和描述，实时获取推荐的标签。

```javascript
/**
 * 基于模板内容获取推荐标签
 * @param {string} title - 模板标题
 * @param {string} description - 模板描述
 * @param {number} themeId - 主题ID
 * @param {number} limit - 返回的最大数量
 * @returns {Promise<Array>} - 推荐的标签列表
 */
async function getRecommendedTagsForContent(title, description, themeId, limit = 10) {
  try {
    // 构建查询参数
    const params = new URLSearchParams({
      title,
      description,
      themeId: themeId.toString(),
      limit: limit.toString()
    });
    
    const url = `/api/v2/learning-templates/recommended-tags?${params.toString()}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取推荐标签失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取推荐标签时出错:', error);
    throw error;
  }
}

// 使用示例 - 实时推荐
let debounceTimer;

// 监听标题和描述输入框的变化
document.getElementById('template-title').addEventListener('input', triggerRecommendation);
document.getElementById('template-description').addEventListener('input', triggerRecommendation);

// 防抖函数，避免频繁请求
function triggerRecommendation() {
  clearTimeout(debounceTimer);
  debounceTimer = setTimeout(() => {
    const title = document.getElementById('template-title').value;
    const description = document.getElementById('template-description').value;
    const themeId = document.getElementById('theme-selector').value;
    
    // 只有当标题和描述都不为空时才请求推荐
    if (title && description && themeId) {
      getRecommendedTagsForContent(title, description, parseInt(themeId))
        .then(tags => {
          displayRecommendedTags(tags);
        })
        .catch(error => {
          console.error('获取推荐标签失败:', error);
        });
    }
  }, 500); // 500毫秒的防抖延迟
}
```

### 3.3 集成到表单提交流程

在表单提交前，可以获取推荐标签并提示用户选择。

```javascript
// 表单提交前获取推荐标签
document.getElementById('template-form').addEventListener('submit', async function(event) {
  event.preventDefault();
  
  const title = document.getElementById('template-title').value;
  const description = document.getElementById('template-description').value;
  const themeId = parseInt(document.getElementById('theme-selector').value);
  
  // 获取用户已选择的标签
  const selectedTagIds = Array.from(document.querySelectorAll('.selected-tag'))
    .map(tag => parseInt(tag.dataset.tagId));
  
  // 如果用户没有选择任何标签，获取推荐标签
  if (selectedTagIds.length === 0) {
    try {
      const recommendedTags = await getRecommendedTagsForContent(title, description, themeId);
      
      if (recommendedTags.length > 0) {
        // 显示推荐标签选择对话框
        showTagSelectionDialog(recommendedTags, () => {
          // 用户确认后提交表单
          submitForm();
        });
      } else {
        // 没有推荐标签，直接提交
        submitForm();
      }
    } catch (error) {
      console.error('获取推荐标签失败:', error);
      // 出错时也允许提交
      submitForm();
    }
  } else {
    // 已有标签，直接提交
    submitForm();
  }
});

// 显示标签选择对话框
function showTagSelectionDialog(tags, onConfirm) {
  const dialog = document.getElementById('tag-selection-dialog');
  const tagContainer = document.getElementById('dialog-tag-container');
  
  // 清空容器
  tagContainer.innerHTML = '';
  
  // 添加推荐标签
  tags.forEach(tag => {
    const tagElement = document.createElement('div');
    tagElement.className = 'dialog-tag-item';
    tagElement.textContent = tag.name;
    tagElement.dataset.tagId = tag.id;
    
    // 添加点击事件，切换选择状态
    tagElement.addEventListener('click', () => {
      tagElement.classList.toggle('selected');
    });
    
    tagContainer.appendChild(tagElement);
  });
  
  // 显示对话框
  dialog.style.display = 'block';
  
  // 确认按钮事件
  document.getElementById('dialog-confirm').onclick = () => {
    // 获取选中的标签
    const selectedTags = Array.from(document.querySelectorAll('.dialog-tag-item.selected'))
      .map(el => ({
        id: parseInt(el.dataset.tagId),
        name: el.textContent
      }));
    
    // 添加到表单中
    selectedTags.forEach(tag => {
      addTagToForm(tag.id, tag.name);
    });
    
    // 关闭对话框
    dialog.style.display = 'none';
    
    // 调用确认回调
    onConfirm();
  };
  
  // 取消按钮事件
  document.getElementById('dialog-cancel').onclick = () => {
    dialog.style.display = 'none';
    onConfirm();
  };
}

// 添加标签到表单
function addTagToForm(tagId, tagName) {
  const tagContainer = document.getElementById('selected-tags');
  
  // 检查标签是否已存在
  if (document.querySelector(`.selected-tag[data-tag-id="${tagId}"]`)) {
    return;
  }
  
  const tagElement = document.createElement('div');
  tagElement.className = 'selected-tag';
  tagElement.textContent = tagName;
  tagElement.dataset.tagId = tagId;
  
  // 添加删除按钮
  const removeButton = document.createElement('span');
  removeButton.className = 'remove-tag';
  removeButton.textContent = '×';
  removeButton.onclick = () => {
    tagElement.remove();
  };
  
  tagElement.appendChild(removeButton);
  tagContainer.appendChild(tagElement);
  
  // 添加隐藏的表单字段
  const input = document.createElement('input');
  input.type = 'hidden';
  input.name = 'tagIds[]';
  input.value = tagId;
  document.getElementById('template-form').appendChild(input);
}

// 提交表单
function submitForm() {
  const formData = new FormData(document.getElementById('template-form'));
  
  fetch('/api/v2/learning-templates', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${getToken()}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(Object.fromEntries(formData))
  })
  .then(response => response.json())
  .then(data => {
    // 处理成功响应
    showSuccessMessage('学习模板创建成功');
    // 跳转到模板详情页
    window.location.href = `/templates/${data.id}`;
  })
  .catch(error => {
    // 处理错误
    showErrorMessage('创建学习模板失败');
  });
}
```

## 4. 响应示例

### 4.1 成功响应

```json
[
  {
    "id": 5,
    "name": "沟通技巧",
    "categoryId": 2,
    "description": "有效沟通的方法和技巧",
    "popularity": 85,
    "creatorId": "admin",
    "createdAt": "2025-01-15T08:00:00Z",
    "updatedAt": "2025-05-10T14:30:00Z",
    "deletedAt": null,
    "isDeleted": false,
    "synonyms": ["交流技巧", "表达能力"]
  },
  {
    "id": 8,
    "name": "演讲",
    "categoryId": 2,
    "description": "公开演讲和演示技巧",
    "popularity": 72,
    "creatorId": "admin",
    "createdAt": "2025-01-20T09:15:00Z",
    "updatedAt": "2025-05-08T11:45:00Z",
    "deletedAt": null,
    "isDeleted": false,
    "synonyms": ["公开演讲", "演示"]
  },
  {
    "id": 12,
    "name": "倾听",
    "categoryId": 2,
    "description": "有效倾听的方法和重要性",
    "popularity": 68,
    "creatorId": "admin",
    "createdAt": "2025-02-05T10:30:00Z",
    "updatedAt": "2025-04-20T16:20:00Z",
    "deletedAt": null,
    "isDeleted": false,
    "synonyms": ["积极倾听", "聆听技巧"]
  }
]
```

### 4.2 错误响应

```json
{
  "message": "获取推荐标签需要提供模板ID或模板内容和主题ID"
}
```

## 5. 最佳实践

1. **使用防抖**：在用户输入时使用防抖技术，避免频繁请求API
2. **适当的时机**：在合适的时机请求推荐标签，如用户完成标题和描述输入后
3. **优雅降级**：如果推荐API失败，仍然允许用户手动选择标签
4. **缓存结果**：对于短时间内相同的请求，可以缓存结果
5. **提供反馈**：让用户知道系统正在为他们推荐标签
6. **允许自定义**：始终允许用户添加自己的标签，不仅限于推荐标签
