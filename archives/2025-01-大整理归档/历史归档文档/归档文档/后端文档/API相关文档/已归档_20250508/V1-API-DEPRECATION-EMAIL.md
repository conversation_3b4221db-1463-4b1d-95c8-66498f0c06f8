# V1 API废弃通知

尊敬的AIBUBB项目开发者：

我们计划在**7天后**完全废弃V1 API，专注于开发和维护V2 API。请尽快将您的代码从V1 API迁移到V2 API。

## 废弃原因

1. **减少技术债务**：维护两套API接口增加了系统复杂性和维护成本。
2. **消除DI系统割裂问题**：V1到V2的兼容层由于DI系统割裂导致功能不稳定。
3. **简化系统架构**：移除兼容层后，系统架构将更加清晰和简洁。
4. **资源集中**：将开发资源集中在V2 API上，加速新功能开发和现有功能优化。
5. **项目仍处于开发阶段**：在此阶段迁移成本相对较低。

## 废弃时间表

| 日期 | 阶段 | 描述 |
|------|------|------|
| 即日起 | 标记废弃 | 所有V1 API响应中添加废弃警告 |
| 即日起 | 停止新功能 | V1 API不再添加新功能 |
| 即日起 | 迁移开始 | 开始将所有客户端迁移到V2 API |
| 7天后 | 完全废弃 | 完全移除V1 API |

## 迁移资源

为了帮助您顺利迁移，我们提供了以下资源：

1. **迁移指南**：详细的V1到V2 API迁移指南，包括API映射、请求和响应格式变化等。
   - 后端文档：`backend/docs/V1-API-DEPRECATION.md`
   - 前端文档：`frontend/docs/V1-TO-V2-MIGRATION-GUIDE.md`

2. **V2 API客户端**：封装了V2 API的客户端库，简化了API调用和错误处理。
   - 前端客户端：`frontend/src/api/v2-client.js`

3. **字段命名转换工具**：用于处理V1和V2 API之间的字段命名差异。
   - 前端工具：`frontend/src/utils/case-converter.js`

## 迁移支持

如果您在迁移过程中遇到任何问题，请随时联系后端开发团队。我们将提供必要的支持，确保您的代码顺利迁移到V2 API。

感谢您的理解和配合！

AIBUBB后端开发团队
