# 用户领域DDD实施方案

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 进行中（已完成仓库、应用服务和控制器实现，正在进行单元测试） |
| 创建日期 | 2025-05-16 |
| 最后更新 | 2025-06-04 |
| 作者 | AIBUBB技术团队 |

## 目录

1. [背景与目标](#1-背景与目标)
2. [领域知识梳理](#2-领域知识梳理)
3. [领域模型设计](#3-领域模型设计)
4. [实体与值对象](#4-实体与值对象)
5. [聚合与聚合根](#5-聚合与聚合根)
6. [领域事件](#6-领域事件)
7. [仓库设计](#7-仓库设计)
8. [应用服务](#8-应用服务)
9. [控制器设计](#9-控制器设计)
10. [测试策略](#10-测试策略)
11. [实施步骤](#11-实施步骤)
12. [时间规划](#12-时间规划)

## 1. 背景与目标

用户领域是AIBUBB系统的核心领域之一，负责用户管理、认证、授权和用户设置等功能。随着系统的不断发展，用户领域的功能也越来越复杂，需要更好的设计和实现来支持业务需求。本方案旨在使用领域驱动设计(DDD)方法重构用户领域，提高代码质量和可维护性。

### 1.1 主要目标

- 梳理用户领域的业务概念和规则
- 设计清晰的用户领域模型
- 实现用户领域的DDD架构
- 提高用户领域代码的可维护性和可扩展性
- 支持用户领域的业务需求变化

## 2. 领域知识梳理

### 2.1 核心概念

#### 2.1.1 用户(User)

用户是系统的核心实体，代表使用AIBUBB系统的个人。用户具有以下特征：

- **唯一标识**：每个用户有一个唯一的ID
- **基本信息**：包括用户名、昵称、头像、性别、生日等
- **认证信息**：包括手机号、微信OpenID、密码哈希等
- **状态**：包括正常、禁用、待验证等
- **角色**：包括普通用户、VIP用户、管理员等
- **注册时间**：用户注册的时间
- **最后登录时间**：用户最后一次登录的时间

#### 2.1.2 用户设置(UserSetting)

用户设置包含用户的个性化配置，如：

- **通知设置**：包括是否接收系统通知、学习提醒、社区互动通知等
- **隐私设置**：包括是否公开学习计划、笔记、关注列表等
- **界面设置**：包括主题颜色、字体大小、布局偏好等
- **学习设置**：包括每日学习目标、学习提醒时间等

#### 2.1.3 用户通知设置(UserNotificationSetting)

用户通知设置是用户设置的一部分，专门管理用户的通知偏好：

- **通知类型**：系统通知、学习提醒、社区互动通知等
- **通知渠道**：应用内、微信、短信等
- **通知频率**：实时、每日摘要、每周摘要等
- **免打扰时间**：用户不希望接收通知的时间段

#### 2.1.4 用户认证(Authentication)

用户认证负责验证用户身份，包括：

- **登录方式**：密码登录、微信登录、手机验证码登录等
- **认证令牌**：JWT令牌、刷新令牌等
- **认证状态**：已认证、未认证、认证过期等
- **多设备登录**：是否允许多设备同时登录

#### 2.1.5 用户授权(Authorization)

用户授权负责控制用户对系统资源的访问权限：

- **角色**：用户角色，如普通用户、VIP用户、管理员等
- **权限**：具体的操作权限，如创建内容、删除内容、管理用户等
- **资源**：系统资源，如学习计划、笔记、练习等
- **策略**：授权策略，如基于角色的授权、基于资源的授权等

### 2.2 业务规则

#### 2.2.1 用户注册规则

- 用户名必须唯一
- 手机号必须唯一
- 微信OpenID必须唯一
- 密码必须符合安全要求（至少8位，包含字母和数字）
- 用户注册后默认为普通用户角色
- 用户注册后自动创建默认的用户设置

#### 2.2.2 用户认证规则

- 用户可以使用密码、微信或手机验证码登录
- 认证令牌有效期为24小时
- 刷新令牌有效期为30天
- 连续5次密码错误将锁定账户1小时
- 用户可以主动登出，使当前认证令牌失效

#### 2.2.3 用户授权规则

- 普通用户可以创建和管理自己的内容
- VIP用户可以访问高级功能和内容
- 管理员可以管理所有用户和内容
- 用户只能修改自己的设置
- 用户只能查看自己的敏感信息

#### 2.2.4 用户设置规则

- 用户可以随时修改自己的设置
- 用户设置修改后立即生效
- 用户设置有默认值
- 用户设置可以导出和导入

### 2.3 领域术语表

| 术语 | 定义 | 所属上下文 | 关联概念 |
|------|------|------------|----------|
| User | 系统的用户，代表使用AIBUBB系统的个人 | 用户领域 | UserSetting, Authentication, Authorization |
| UserSetting | 用户的个性化配置 | 用户领域 | User, UserNotificationSetting |
| UserNotificationSetting | 用户的通知偏好设置 | 用户领域 | UserSetting |
| Authentication | 验证用户身份的过程和机制 | 用户领域 | User, AuthToken |
| Authorization | 控制用户对系统资源的访问权限的过程和机制 | 用户领域 | User, Role, Permission |
| AuthToken | 认证令牌，用于验证用户身份 | 用户领域 | Authentication |
| RefreshToken | 刷新令牌，用于获取新的认证令牌 | 用户领域 | Authentication, AuthToken |
| Role | 用户角色，如普通用户、VIP用户、管理员等 | 用户领域 | User, Authorization, Permission |
| Permission | 具体的操作权限，如创建内容、删除内容、管理用户等 | 用户领域 | Role, Authorization |
| UserStatus | 用户状态，如正常、禁用、待验证等 | 用户领域 | User |
| LoginMethod | 登录方式，如密码登录、微信登录、手机验证码登录等 | 用户领域 | Authentication |
| NotificationType | 通知类型，如系统通知、学习提醒、社区互动通知等 | 用户领域 | UserNotificationSetting |
| NotificationChannel | 通知渠道，如应用内、微信、短信等 | 用户领域 | UserNotificationSetting |

## 3. 领域模型设计

### 3.1 用户聚合

用户聚合是用户领域的核心聚合，包含用户实体和相关值对象。

#### 3.1.1 聚合根

- **User**：用户实体，是用户聚合的聚合根

#### 3.1.2 实体

- **User**：用户实体，包含用户的基本信息和状态
- **UserSetting**：用户设置实体，包含用户的个性化配置
- **UserNotificationSetting**：用户通知设置实体，包含用户的通知偏好

#### 3.1.3 值对象

- **UserId**：用户ID值对象，表示用户的唯一标识
- **Email**：邮箱值对象，包含邮箱地址和验证状态
- **PhoneNumber**：手机号值对象，包含手机号和验证状态
- **Password**：密码值对象，包含密码哈希和盐值
- **UserStatus**：用户状态值对象，表示用户的状态（正常、禁用、待验证等）
- **NotificationType**：通知类型值对象，表示通知的类型
- **NotificationChannel**：通知渠道值对象，表示通知的渠道

### 3.2 认证聚合

认证聚合负责用户认证相关的功能。

#### 3.2.1 聚合根

- **AuthToken**：认证令牌实体，是认证聚合的聚合根

#### 3.2.2 实体

- **AuthToken**：认证令牌实体，包含令牌信息和状态
- **RefreshToken**：刷新令牌实体，用于获取新的认证令牌

#### 3.2.3 值对象

- **TokenId**：令牌ID值对象，表示令牌的唯一标识
- **TokenStatus**：令牌状态值对象，表示令牌的状态（有效、过期、撤销等）
- **LoginMethod**：登录方式值对象，表示用户的登录方式

### 3.3 授权聚合

授权聚合负责用户授权相关的功能。

#### 3.3.1 聚合根

- **Role**：角色实体，是授权聚合的聚合根

#### 3.3.2 实体

- **Role**：角色实体，包含角色信息和权限
- **Permission**：权限实体，表示具体的操作权限

#### 3.3.3 值对象

- **RoleId**：角色ID值对象，表示角色的唯一标识
- **PermissionId**：权限ID值对象，表示权限的唯一标识
- **ResourceType**：资源类型值对象，表示系统资源的类型

## 4. 实体与值对象

### 4.1 实体设计

#### 4.1.1 User实体

```typescript
// domain/models/user/User.ts
export class User extends AggregateRootBase {
  private _username: string;
  private _nickname: string;
  private _email: Email | null;
  private _phoneNumber: PhoneNumber | null;
  private _password: Password | null;
  private _wechatOpenId: string | null;
  private _avatar: string | null;
  private _gender: Gender;
  private _birthday: Date | null;
  private _status: UserStatus;
  private _roles: Role[];
  private _registeredAt: Date;
  private _lastLoginAt: Date | null;
  private _userSetting: UserSetting;

  constructor(
    id: number,
    username: string,
    nickname: string,
    email: Email | null,
    phoneNumber: PhoneNumber | null,
    password: Password | null,
    wechatOpenId: string | null,
    avatar: string | null,
    gender: Gender,
    birthday: Date | null,
    status: UserStatus,
    roles: Role[],
    registeredAt: Date,
    lastLoginAt: Date | null,
    userSetting: UserSetting
  ) {
    super(id);
    this._username = username;
    this._nickname = nickname;
    this._email = email;
    this._phoneNumber = phoneNumber;
    this._password = password;
    this._wechatOpenId = wechatOpenId;
    this._avatar = avatar;
    this._gender = gender;
    this._birthday = birthday;
    this._status = status;
    this._roles = roles;
    this._registeredAt = registeredAt;
    this._lastLoginAt = lastLoginAt;
    this._userSetting = userSetting;
  }

  // 业务方法
  updateProfile(nickname: string, avatar: string | null, gender: Gender, birthday: Date | null): void {
    this._nickname = nickname;
    this._avatar = avatar;
    this._gender = gender;
    this._birthday = birthday;
    this.addDomainEvent(new UserProfileUpdatedEvent(this));
  }

  updateEmail(email: Email): void {
    if (this._email && this._email.equals(email)) {
      return;
    }
    this._email = email;
    this.addDomainEvent(new UserEmailUpdatedEvent(this));
  }

  updatePhoneNumber(phoneNumber: PhoneNumber): void {
    if (this._phoneNumber && this._phoneNumber.equals(phoneNumber)) {
      return;
    }
    this._phoneNumber = phoneNumber;
    this.addDomainEvent(new UserPhoneNumberUpdatedEvent(this));
  }

  updatePassword(password: Password): void {
    this._password = password;
    this.addDomainEvent(new UserPasswordUpdatedEvent(this));
  }

  disable(): void {
    if (this._status.equals(UserStatus.DISABLED)) {
      return;
    }
    this._status = UserStatus.DISABLED;
    this.addDomainEvent(new UserDisabledEvent(this));
  }

  enable(): void {
    if (this._status.equals(UserStatus.ACTIVE)) {
      return;
    }
    this._status = UserStatus.ACTIVE;
    this.addDomainEvent(new UserEnabledEvent(this));
  }

  addRole(role: Role): void {
    if (this._roles.some(r => r.id === role.id)) {
      return;
    }
    this._roles.push(role);
    this.addDomainEvent(new UserRoleAddedEvent(this, role));
  }

  removeRole(roleId: number): void {
    const index = this._roles.findIndex(r => r.id === roleId);
    if (index === -1) {
      return;
    }
    const role = this._roles[index];
    this._roles.splice(index, 1);
    this.addDomainEvent(new UserRoleRemovedEvent(this, role));
  }

  updateLastLoginAt(lastLoginAt: Date): void {
    this._lastLoginAt = lastLoginAt;
  }

  // 获取器
  get username(): string {
    return this._username;
  }

  get nickname(): string {
    return this._nickname;
  }

  get email(): Email | null {
    return this._email;
  }

  get phoneNumber(): PhoneNumber | null {
    return this._phoneNumber;
  }

  get password(): Password | null {
    return this._password;
  }

  get wechatOpenId(): string | null {
    return this._wechatOpenId;
  }

  get avatar(): string | null {
    return this._avatar;
  }

  get gender(): Gender {
    return this._gender;
  }

  get birthday(): Date | null {
    return this._birthday;
  }

  get status(): UserStatus {
    return this._status;
  }

  get roles(): Role[] {
    return [...this._roles];
  }

  get registeredAt(): Date {
    return this._registeredAt;
  }

  get lastLoginAt(): Date | null {
    return this._lastLoginAt;
  }

  get userSetting(): UserSetting {
    return this._userSetting;
  }
}
```

#### 4.1.2 UserSetting实体

```typescript
// domain/models/user/UserSetting.ts
export class UserSetting extends Entity {
  private _userId: number;
  private _theme: string;
  private _fontSize: number;
  private _dailyGoalMinutes: number;
  private _reminderTime: string | null;
  private _privacySettings: PrivacySettings;
  private _notificationSettings: UserNotificationSetting[];

  constructor(
    id: number,
    userId: number,
    theme: string,
    fontSize: number,
    dailyGoalMinutes: number,
    reminderTime: string | null,
    privacySettings: PrivacySettings,
    notificationSettings: UserNotificationSetting[]
  ) {
    super(id);
    this._userId = userId;
    this._theme = theme;
    this._fontSize = fontSize;
    this._dailyGoalMinutes = dailyGoalMinutes;
    this._reminderTime = reminderTime;
    this._privacySettings = privacySettings;
    this._notificationSettings = notificationSettings;
  }

  // 业务方法
  updateTheme(theme: string): void {
    this._theme = theme;
  }

  updateFontSize(fontSize: number): void {
    this._fontSize = fontSize;
  }

  updateDailyGoal(dailyGoalMinutes: number): void {
    this._dailyGoalMinutes = dailyGoalMinutes;
  }

  updateReminderTime(reminderTime: string | null): void {
    this._reminderTime = reminderTime;
  }

  updatePrivacySettings(privacySettings: PrivacySettings): void {
    this._privacySettings = privacySettings;
  }

  addNotificationSetting(notificationSetting: UserNotificationSetting): void {
    const index = this._notificationSettings.findIndex(
      ns => ns.notificationType.equals(notificationSetting.notificationType)
    );
    if (index !== -1) {
      this._notificationSettings[index] = notificationSetting;
    } else {
      this._notificationSettings.push(notificationSetting);
    }
  }

  removeNotificationSetting(notificationType: NotificationType): void {
    const index = this._notificationSettings.findIndex(
      ns => ns.notificationType.equals(notificationType)
    );
    if (index !== -1) {
      this._notificationSettings.splice(index, 1);
    }
  }

  // 获取器
  get userId(): number {
    return this._userId;
  }

  get theme(): string {
    return this._theme;
  }

  get fontSize(): number {
    return this._fontSize;
  }

  get dailyGoalMinutes(): number {
    return this._dailyGoalMinutes;
  }

  get reminderTime(): string | null {
    return this._reminderTime;
  }

  get privacySettings(): PrivacySettings {
    return this._privacySettings;
  }

  get notificationSettings(): UserNotificationSetting[] {
    return [...this._notificationSettings];
  }
}
```

#### 4.1.3 Role实体

```typescript
// domain/models/user/Role.ts
export class Role extends AggregateRootBase {
  private _name: string;
  private _description: string;
  private _permissions: Permission[];

  constructor(
    id: number,
    name: string,
    description: string,
    permissions: Permission[]
  ) {
    super(id);
    this._name = name;
    this._description = description;
    this._permissions = permissions;
  }

  // 业务方法
  updateName(name: string): void {
    this._name = name;
    this.addDomainEvent(new RoleUpdatedEvent(this));
  }

  updateDescription(description: string): void {
    this._description = description;
    this.addDomainEvent(new RoleUpdatedEvent(this));
  }

  addPermission(permission: Permission): void {
    if (this._permissions.some(p => p.id === permission.id)) {
      return;
    }
    this._permissions.push(permission);
    this.addDomainEvent(new RolePermissionAddedEvent(this, permission));
  }

  removePermission(permissionId: number): void {
    const index = this._permissions.findIndex(p => p.id === permissionId);
    if (index === -1) {
      return;
    }
    const permission = this._permissions[index];
    this._permissions.splice(index, 1);
    this.addDomainEvent(new RolePermissionRemovedEvent(this, permission));
  }

  hasPermission(permissionId: number): boolean {
    return this._permissions.some(p => p.id === permissionId);
  }

  // 获取器
  get name(): string {
    return this._name;
  }

  get description(): string {
    return this._description;
  }

  get permissions(): Permission[] {
    return [...this._permissions];
  }
}
```

### 4.2 值对象设计

#### 4.2.1 Email值对象

```typescript
// domain/models/user/Email.ts
export class Email {
  private readonly _address: string;
  private readonly _verified: boolean;

  private constructor(address: string, verified: boolean) {
    this._address = address;
    this._verified = verified;
  }

  static create(address: string, verified: boolean = false): Email {
    if (!Email.isValid(address)) {
      throw new Error(`Invalid email address: ${address}`);
    }
    return new Email(address, verified);
  }

  static isValid(address: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(address);
  }

  verify(): Email {
    return new Email(this._address, true);
  }

  equals(other: Email): boolean {
    return this._address === other._address;
  }

  get address(): string {
    return this._address;
  }

  get verified(): boolean {
    return this._verified;
  }
}
```

#### 4.2.2 PhoneNumber值对象

```typescript
// domain/models/user/PhoneNumber.ts
export class PhoneNumber {
  private readonly _number: string;
  private readonly _verified: boolean;

  private constructor(number: string, verified: boolean) {
    this._number = number;
    this._verified = verified;
  }

  static create(number: string, verified: boolean = false): PhoneNumber {
    if (!PhoneNumber.isValid(number)) {
      throw new Error(`Invalid phone number: ${number}`);
    }
    return new PhoneNumber(number, verified);
  }

  static isValid(number: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(number);
  }

  verify(): PhoneNumber {
    return new PhoneNumber(this._number, true);
  }

  equals(other: PhoneNumber): boolean {
    return this._number === other._number;
  }

  get number(): string {
    return this._number;
  }

  get verified(): boolean {
    return this._verified;
  }
}
```

#### 4.2.3 Password值对象

```typescript
// domain/models/user/Password.ts
export class Password {
  private readonly _hash: string;
  private readonly _salt: string;

  private constructor(hash: string, salt: string) {
    this._hash = hash;
    this._salt = salt;
  }

  static create(plainPassword: string): Password {
    const salt = Password.generateSalt();
    const hash = Password.hashPassword(plainPassword, salt);
    return new Password(hash, salt);
  }

  static fromHash(hash: string, salt: string): Password {
    return new Password(hash, salt);
  }

  private static generateSalt(): string {
    // 实际实现应该使用加密安全的随机数生成器
    return Math.random().toString(36).substring(2, 15);
  }

  private static hashPassword(plainPassword: string, salt: string): string {
    // 实际实现应该使用安全的哈希算法，如bcrypt或Argon2
    // 这里仅作示例
    return require('crypto')
      .createHash('sha256')
      .update(plainPassword + salt)
      .digest('hex');
  }

  verify(plainPassword: string): boolean {
    const hash = Password.hashPassword(plainPassword, this._salt);
    return this._hash === hash;
  }

  get hash(): string {
    return this._hash;
  }

  get salt(): string {
    return this._salt;
  }
}
```

#### 4.2.4 UserStatus值对象

```typescript
// domain/models/user/UserStatus.ts
export class UserStatus {
  private readonly _value: string;

  private constructor(value: string) {
    this._value = value;
  }

  static ACTIVE = new UserStatus('ACTIVE');
  static DISABLED = new UserStatus('DISABLED');
  static PENDING = new UserStatus('PENDING');
  static LOCKED = new UserStatus('LOCKED');

  static fromString(value: string): UserStatus {
    switch (value.toUpperCase()) {
      case 'ACTIVE': return UserStatus.ACTIVE;
      case 'DISABLED': return UserStatus.DISABLED;
      case 'PENDING': return UserStatus.PENDING;
      case 'LOCKED': return UserStatus.LOCKED;
      default: throw new Error(`Invalid user status value: ${value}`);
    }
  }

  equals(other: UserStatus): boolean {
    return this._value === other._value;
  }

  get value(): string {
    return this._value;
  }
}
```

#### 4.2.5 Gender值对象

```typescript
// domain/models/user/Gender.ts
export class Gender {
  private readonly _value: string;

  private constructor(value: string) {
    this._value = value;
  }

  static MALE = new Gender('MALE');
  static FEMALE = new Gender('FEMALE');
  static OTHER = new Gender('OTHER');
  static UNKNOWN = new Gender('UNKNOWN');

  static fromString(value: string): Gender {
    switch (value.toUpperCase()) {
      case 'MALE': return Gender.MALE;
      case 'FEMALE': return Gender.FEMALE;
      case 'OTHER': return Gender.OTHER;
      case 'UNKNOWN': return Gender.UNKNOWN;
      default: throw new Error(`Invalid gender value: ${value}`);
    }
  }

  equals(other: Gender): boolean {
    return this._value === other._value;
  }

  get value(): string {
    return this._value;
  }
}
```

## 5. 聚合与聚合根

### 5.1 用户聚合

用户聚合是用户领域的核心聚合，包含用户实体和相关值对象。

#### 5.1.1 聚合边界

用户聚合的边界包括：

- 用户实体（聚合根）
- 用户设置实体
- 用户通知设置实体
- 用户相关的值对象（Email、PhoneNumber、Password、UserStatus、Gender等）

#### 5.1.2 不变性规则

用户聚合需要维护以下不变性规则：

- 用户名必须唯一
- 手机号必须唯一
- 微信OpenID必须唯一
- 密码必须符合安全要求
- 用户状态必须是有效值
- 用户必须有至少一个角色
- 用户设置必须关联到一个用户

#### 5.1.3 聚合关系

- 用户实体是聚合根，负责协调聚合内的所有实体和值对象
- 用户设置实体属于用户聚合，但有自己的生命周期
- 用户通知设置实体属于用户设置实体，共享生命周期
- 用户通过ID引用角色，而不是直接引用

#### 5.1.4 聚合实现

```typescript
// domain/models/user/UserAggregate.ts
export class UserAggregate {
  private readonly _user: User;

  constructor(user: User) {
    this._user = user;
  }

  // 聚合操作
  register(username: string, password: string): User {
    // 验证用户名是否唯一
    // 创建用户
    // 创建默认用户设置
    // 分配默认角色
    // 返回用户
  }

  updateProfile(nickname: string, avatar: string | null, gender: Gender, birthday: Date | null): void {
    this._user.updateProfile(nickname, avatar, gender, birthday);
  }

  updateEmail(email: Email): void {
    // 验证邮箱是否唯一
    this._user.updateEmail(email);
  }

  updatePhoneNumber(phoneNumber: PhoneNumber): void {
    // 验证手机号是否唯一
    this._user.updatePhoneNumber(phoneNumber);
  }

  updatePassword(password: Password): void {
    // 验证密码是否符合安全要求
    this._user.updatePassword(password);
  }

  updateUserSetting(userSetting: UserSetting): void {
    // 验证用户设置是否属于该用户
    this._user.updateUserSetting(userSetting);
  }

  addRole(role: Role): void {
    this._user.addRole(role);
  }

  removeRole(roleId: number): void {
    // 验证用户至少有一个角色
    if (this._user.roles.length <= 1) {
      throw new Error('用户必须至少有一个角色');
    }
    this._user.removeRole(roleId);
  }

  disable(): void {
    this._user.disable();
  }

  enable(): void {
    this._user.enable();
  }

  // 获取器
  get user(): User {
    return this._user;
  }
}
```

### 5.2 认证聚合

认证聚合负责用户认证相关的功能。

#### 5.2.1 聚合边界

认证聚合的边界包括：

- 认证令牌实体（聚合根）
- 刷新令牌实体
- 认证相关的值对象（TokenId、TokenStatus、LoginMethod等）

#### 5.2.2 不变性规则

认证聚合需要维护以下不变性规则：

- 认证令牌必须关联到一个用户
- 认证令牌必须有有效期
- 刷新令牌必须关联到一个认证令牌
- 刷新令牌必须有有效期
- 认证令牌状态必须是有效值

#### 5.2.3 聚合关系

- 认证令牌实体是聚合根，负责协调聚合内的所有实体和值对象
- 刷新令牌实体属于认证令牌聚合，共享生命周期
- 认证令牌通过ID引用用户，而不是直接引用

#### 5.2.4 聚合实现

```typescript
// domain/models/user/AuthTokenAggregate.ts
export class AuthTokenAggregate {
  private readonly _authToken: AuthToken;

  constructor(authToken: AuthToken) {
    this._authToken = authToken;
  }

  // 聚合操作
  createToken(userId: number, loginMethod: LoginMethod, deviceInfo: string): AuthToken {
    // 创建认证令牌
    // 创建刷新令牌
    // 返回认证令牌
  }

  refreshToken(refreshToken: RefreshToken): AuthToken {
    // 验证刷新令牌是否有效
    // 创建新的认证令牌
    // 更新刷新令牌状态
    // 返回新的认证令牌
  }

  revokeToken(): void {
    // 撤销认证令牌
    // 撤销关联的刷新令牌
  }

  validateToken(): boolean {
    // 验证认证令牌是否有效
    return this._authToken.isValid();
  }

  // 获取器
  get authToken(): AuthToken {
    return this._authToken;
  }
}
```

### 5.3 授权聚合

授权聚合负责用户授权相关的功能。

#### 5.3.1 聚合边界

授权聚合的边界包括：

- 角色实体（聚合根）
- 权限实体
- 授权相关的值对象（RoleId、PermissionId、ResourceType等）

#### 5.3.2 不变性规则

授权聚合需要维护以下不变性规则：

- 角色名称必须唯一
- 权限名称必须唯一
- 角色必须有至少一个权限
- 权限必须关联到一个资源类型

#### 5.3.3 聚合关系

- 角色实体是聚合根，负责协调聚合内的所有实体和值对象
- 权限实体属于角色聚合，但有自己的生命周期
- 角色通过包含权限实体直接引用权限

#### 5.3.4 聚合实现

```typescript
// domain/models/user/RoleAggregate.ts
export class RoleAggregate {
  private readonly _role: Role;

  constructor(role: Role) {
    this._role = role;
  }

  // 聚合操作
  createRole(name: string, description: string, permissions: Permission[]): Role {
    // 验证角色名称是否唯一
    // 验证权限是否有效
    // 创建角色
    // 返回角色
  }

  updateRole(name: string, description: string): void {
    // 验证角色名称是否唯一
    this._role.updateName(name);
    this._role.updateDescription(description);
  }

  addPermission(permission: Permission): void {
    this._role.addPermission(permission);
  }

  removePermission(permissionId: number): void {
    // 验证角色至少有一个权限
    if (this._role.permissions.length <= 1) {
      throw new Error('角色必须至少有一个权限');
    }
    this._role.removePermission(permissionId);
  }

  hasPermission(permissionId: number): boolean {
    return this._role.hasPermission(permissionId);
  }

  // 获取器
  get role(): Role {
    return this._role;
  }
}
```

## 6. 领域事件

### 6.1 用户领域事件

用户领域事件表示用户领域中发生的重要事件，用于在不同聚合之间进行通信，或者触发系统中的其他操作。

#### 6.1.1 用户相关事件

```typescript
// domain/events/user/UserCreatedEvent.ts
export class UserCreatedEvent extends DomainEventBase {
  constructor(public readonly user: User) {
    super();
  }
}

// domain/events/user/UserProfileUpdatedEvent.ts
export class UserProfileUpdatedEvent extends DomainEventBase {
  constructor(public readonly user: User) {
    super();
  }
}

// domain/events/user/UserEmailUpdatedEvent.ts
export class UserEmailUpdatedEvent extends DomainEventBase {
  constructor(public readonly user: User) {
    super();
  }
}

// domain/events/user/UserPhoneNumberUpdatedEvent.ts
export class UserPhoneNumberUpdatedEvent extends DomainEventBase {
  constructor(public readonly user: User) {
    super();
  }
}

// domain/events/user/UserPasswordUpdatedEvent.ts
export class UserPasswordUpdatedEvent extends DomainEventBase {
  constructor(public readonly user: User) {
    super();
  }
}

// domain/events/user/UserDisabledEvent.ts
export class UserDisabledEvent extends DomainEventBase {
  constructor(public readonly user: User) {
    super();
  }
}

// domain/events/user/UserEnabledEvent.ts
export class UserEnabledEvent extends DomainEventBase {
  constructor(public readonly user: User) {
    super();
  }
}

// domain/events/user/UserDeletedEvent.ts
export class UserDeletedEvent extends DomainEventBase {
  constructor(public readonly userId: number) {
    super();
  }
}

// domain/events/user/UserRoleAddedEvent.ts
export class UserRoleAddedEvent extends DomainEventBase {
  constructor(
    public readonly user: User,
    public readonly role: Role
  ) {
    super();
  }
}

// domain/events/user/UserRoleRemovedEvent.ts
export class UserRoleRemovedEvent extends DomainEventBase {
  constructor(
    public readonly user: User,
    public readonly role: Role
  ) {
    super();
  }
}
```

#### 6.1.2 用户设置相关事件

```typescript
// domain/events/user/UserSettingUpdatedEvent.ts
export class UserSettingUpdatedEvent extends DomainEventBase {
  constructor(public readonly userSetting: UserSetting) {
    super();
  }
}

// domain/events/user/UserNotificationSettingAddedEvent.ts
export class UserNotificationSettingAddedEvent extends DomainEventBase {
  constructor(
    public readonly userSetting: UserSetting,
    public readonly notificationSetting: UserNotificationSetting
  ) {
    super();
  }
}

// domain/events/user/UserNotificationSettingRemovedEvent.ts
export class UserNotificationSettingRemovedEvent extends DomainEventBase {
  constructor(
    public readonly userSetting: UserSetting,
    public readonly notificationType: NotificationType
  ) {
    super();
  }
}
```

### 6.2 认证领域事件

认证领域事件表示认证领域中发生的重要事件，用于在不同聚合之间进行通信，或者触发系统中的其他操作。

```typescript
// domain/events/user/AuthTokenCreatedEvent.ts
export class AuthTokenCreatedEvent extends DomainEventBase {
  constructor(public readonly authToken: AuthToken) {
    super();
  }
}

// domain/events/user/AuthTokenRefreshedEvent.ts
export class AuthTokenRefreshedEvent extends DomainEventBase {
  constructor(
    public readonly oldAuthToken: AuthToken,
    public readonly newAuthToken: AuthToken
  ) {
    super();
  }
}

// domain/events/user/AuthTokenRevokedEvent.ts
export class AuthTokenRevokedEvent extends DomainEventBase {
  constructor(public readonly authToken: AuthToken) {
    super();
  }
}

// domain/events/user/UserLoggedInEvent.ts
export class UserLoggedInEvent extends DomainEventBase {
  constructor(
    public readonly userId: number,
    public readonly loginMethod: LoginMethod,
    public readonly deviceInfo: string
  ) {
    super();
  }
}

// domain/events/user/UserLoggedOutEvent.ts
export class UserLoggedOutEvent extends DomainEventBase {
  constructor(public readonly userId: number) {
    super();
  }
}
```

### 6.3 授权领域事件

授权领域事件表示授权领域中发生的重要事件，用于在不同聚合之间进行通信，或者触发系统中的其他操作。

```typescript
// domain/events/user/RoleCreatedEvent.ts
export class RoleCreatedEvent extends DomainEventBase {
  constructor(public readonly role: Role) {
    super();
  }
}

// domain/events/user/RoleUpdatedEvent.ts
export class RoleUpdatedEvent extends DomainEventBase {
  constructor(public readonly role: Role) {
    super();
  }
}

// domain/events/user/RoleDeletedEvent.ts
export class RoleDeletedEvent extends DomainEventBase {
  constructor(public readonly roleId: number) {
    super();
  }
}

// domain/events/user/RolePermissionAddedEvent.ts
export class RolePermissionAddedEvent extends DomainEventBase {
  constructor(
    public readonly role: Role,
    public readonly permission: Permission
  ) {
    super();
  }
}

// domain/events/user/RolePermissionRemovedEvent.ts
export class RolePermissionRemovedEvent extends DomainEventBase {
  constructor(
    public readonly role: Role,
    public readonly permission: Permission
  ) {
    super();
  }
}
```

### 6.4 事件处理器

事件处理器负责处理领域事件，执行相应的业务逻辑。

#### 6.4.1 用户事件处理器

```typescript
// application/events/user/UserCreatedEventHandler.ts
@injectable()
export class UserCreatedEventHandler implements EventHandler<UserCreatedEvent> {
  constructor(
    @inject('EmailService') private readonly emailService: EmailService
  ) {}

  async handle(event: UserCreatedEvent): Promise<void> {
    // 发送欢迎邮件
    if (event.user.email) {
      await this.emailService.sendWelcomeEmail(event.user.email.address, event.user.nickname);
    }
  }
}

// application/events/user/UserDisabledEventHandler.ts
@injectable()
export class UserDisabledEventHandler implements EventHandler<UserDisabledEvent> {
  constructor(
    @inject('AuthTokenRepository') private readonly authTokenRepository: AuthTokenRepository
  ) {}

  async handle(event: UserDisabledEvent): Promise<void> {
    // 撤销用户的所有认证令牌
    await this.authTokenRepository.revokeAllByUserId(event.user.id);
  }
}
```

#### 6.4.2 认证事件处理器

```typescript
// application/events/user/UserLoggedInEventHandler.ts
@injectable()
export class UserLoggedInEventHandler implements EventHandler<UserLoggedInEvent> {
  constructor(
    @inject('UserRepository') private readonly userRepository: UserRepository
  ) {}

  async handle(event: UserLoggedInEvent): Promise<void> {
    // 更新用户最后登录时间
    const user = await this.userRepository.findById(event.userId);
    if (user) {
      user.updateLastLoginAt(new Date());
      await this.userRepository.save(user);
    }
  }
}
```

#### 6.4.3 授权事件处理器

```typescript
// application/events/user/RoleUpdatedEventHandler.ts
@injectable()
export class RoleUpdatedEventHandler implements EventHandler<RoleUpdatedEvent> {
  constructor(
    @inject('CacheService') private readonly cacheService: CacheService
  ) {}

  async handle(event: RoleUpdatedEvent): Promise<void> {
    // 清除角色缓存
    await this.cacheService.invalidate(`role:${event.role.id}`);
  }
}
```

## 7. 仓库设计

### 7.1 仓库接口

仓库接口定义在领域层，用于获取和持久化聚合实例。

#### 7.1.1 用户仓库接口

```typescript
// domain/repositories/user/UserRepository.ts
export interface UserRepository extends Repository<User> {
  findByUsername(username: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findByPhoneNumber(phoneNumber: string): Promise<User | null>;
  findByWechatOpenId(wechatOpenId: string): Promise<User | null>;
  findWithPagination(page: number, pageSize: number): Promise<{ items: User[], total: number }>;
  findByRole(roleId: number): Promise<User[]>;
  findDeleted(): Promise<User[]>;
  restore(id: number): Promise<User | null>;
}
```

#### 7.1.2 用户设置仓库接口

```typescript
// domain/repositories/user/UserSettingRepository.ts
export interface UserSettingRepository extends Repository<UserSetting> {
  findByUserId(userId: number): Promise<UserSetting | null>;
}
```

#### 7.1.3 认证令牌仓库接口

```typescript
// domain/repositories/user/AuthTokenRepository.ts
export interface AuthTokenRepository extends Repository<AuthToken> {
  findByToken(token: string): Promise<AuthToken | null>;
  findByRefreshToken(refreshToken: string): Promise<AuthToken | null>;
  findByUserId(userId: number): Promise<AuthToken[]>;
  revokeAllByUserId(userId: number): Promise<void>;
  revokeExpired(): Promise<void>;
}
```

#### 7.1.4 角色仓库接口

```typescript
// domain/repositories/user/RoleRepository.ts
export interface RoleRepository extends Repository<Role> {
  findByName(name: string): Promise<Role | null>;
  findWithPermissions(): Promise<Role[]>;
  findWithPermissionsById(id: number): Promise<Role | null>;
}
```

#### 7.1.5 权限仓库接口

```typescript
// domain/repositories/user/PermissionRepository.ts
export interface PermissionRepository extends Repository<Permission> {
  findByName(name: string): Promise<Permission | null>;
  findByResourceType(resourceType: string): Promise<Permission[]>;
}
```

### 7.2 仓库实现

仓库实现在基础设施层，使用具体的持久化技术实现仓库接口。

#### 7.2.1 用户仓库实现

```typescript
// infrastructure/persistence/repositories/user/SequelizeUserRepository.ts
@injectable()
export class SequelizeUserRepository extends RepositoryBase<User> implements UserRepository {
  constructor(
    @inject('UserModel') userModel: any,
    @inject('UserSettingModel') userSettingModel: any,
    @inject('RoleModel') roleModel: any,
    @inject('UnitOfWork') unitOfWork: UnitOfWork
  ) {
    super(userModel, unitOfWork);
    this.userSettingModel = userSettingModel;
    this.roleModel = roleModel;
  }

  async findByUsername(username: string): Promise<User | null> {
    const user = await this.model.findOne({
      where: { username },
      transaction: this.unitOfWork.getTransaction()
    });
    if (!user) return null;
    return this.toDomain(user);
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.model.findOne({
      where: { email },
      transaction: this.unitOfWork.getTransaction()
    });
    if (!user) return null;
    return this.toDomain(user);
  }

  async findByPhoneNumber(phoneNumber: string): Promise<User | null> {
    const user = await this.model.findOne({
      where: { phone_number: phoneNumber },
      transaction: this.unitOfWork.getTransaction()
    });
    if (!user) return null;
    return this.toDomain(user);
  }

  async findByWechatOpenId(wechatOpenId: string): Promise<User | null> {
    const user = await this.model.findOne({
      where: { wechat_open_id: wechatOpenId },
      transaction: this.unitOfWork.getTransaction()
    });
    if (!user) return null;
    return this.toDomain(user);
  }

  async findWithPagination(page: number, pageSize: number): Promise<{ items: User[], total: number }> {
    const { rows, count } = await this.model.findAndCountAll({
      limit: pageSize,
      offset: (page - 1) * pageSize,
      transaction: this.unitOfWork.getTransaction()
    });
    return {
      items: rows.map(user => this.toDomain(user)),
      total: count
    };
  }

  async findByRole(roleId: number): Promise<User[]> {
    const users = await this.model.findAll({
      include: [
        {
          model: this.roleModel,
          where: { id: roleId }
        }
      ],
      transaction: this.unitOfWork.getTransaction()
    });
    return users.map(user => this.toDomain(user));
  }

  async findDeleted(): Promise<User[]> {
    const users = await this.model.findAll({
      where: { deleted_at: { [Op.not]: null } },
      paranoid: false,
      transaction: this.unitOfWork.getTransaction()
    });
    return users.map(user => this.toDomain(user));
  }

  async restore(id: number): Promise<User | null> {
    await this.model.restore({
      where: { id },
      transaction: this.unitOfWork.getTransaction()
    });
    return this.findById(id);
  }

  protected async toDomain(entity: any): Promise<User> {
    // 加载用户设置
    const userSetting = await this.userSettingModel.findOne({
      where: { user_id: entity.id },
      transaction: this.unitOfWork.getTransaction()
    });

    // 加载用户角色
    const roles = await entity.getRoles({ transaction: this.unitOfWork.getTransaction() });

    // 创建用户实体
    return new User(
      entity.id,
      entity.username,
      entity.nickname,
      entity.email ? Email.create(entity.email, entity.email_verified) : null,
      entity.phone_number ? PhoneNumber.create(entity.phone_number, entity.phone_verified) : null,
      entity.password_hash && entity.password_salt ? Password.fromHash(entity.password_hash, entity.password_salt) : null,
      entity.wechat_open_id,
      entity.avatar,
      Gender.fromString(entity.gender),
      entity.birthday,
      UserStatus.fromString(entity.status),
      roles.map(role => this.roleToEntity(role)),
      entity.created_at,
      entity.last_login_at,
      this.userSettingToEntity(userSetting)
    );
  }

  protected toPersistence(entity: User): any {
    return {
      id: entity.id,
      username: entity.username,
      nickname: entity.nickname,
      email: entity.email?.address,
      email_verified: entity.email?.verified,
      phone_number: entity.phoneNumber?.number,
      phone_verified: entity.phoneNumber?.verified,
      password_hash: entity.password?.hash,
      password_salt: entity.password?.salt,
      wechat_open_id: entity.wechatOpenId,
      avatar: entity.avatar,
      gender: entity.gender.value,
      birthday: entity.birthday,
      status: entity.status.value,
      created_at: entity.registeredAt,
      last_login_at: entity.lastLoginAt
    };
  }

  private roleToEntity(role: any): Role {
    return new Role(
      role.id,
      role.name,
      role.description,
      role.permissions.map(permission => this.permissionToEntity(permission))
    );
  }

  private permissionToEntity(permission: any): Permission {
    return new Permission(
      permission.id,
      permission.name,
      permission.description,
      permission.resource_type
    );
  }

  private userSettingToEntity(userSetting: any): UserSetting {
    return new UserSetting(
      userSetting.id,
      userSetting.user_id,
      userSetting.theme,
      userSetting.font_size,
      userSetting.daily_goal_minutes,
      userSetting.reminder_time,
      this.privacySettingsToEntity(userSetting.privacy_settings),
      this.notificationSettingsToEntity(userSetting.notification_settings)
    );
  }

  private privacySettingsToEntity(privacySettings: any): PrivacySettings {
    return new PrivacySettings(
      privacySettings.public_profile,
      privacySettings.public_learning_plan,
      privacySettings.public_notes,
      privacySettings.public_exercises
    );
  }

  private notificationSettingsToEntity(notificationSettings: any[]): UserNotificationSetting[] {
    return notificationSettings.map(ns => new UserNotificationSetting(
      ns.id,
      NotificationType.fromString(ns.type),
      NotificationChannel.fromString(ns.channel),
      ns.enabled
    ));
  }
}
```

#### 7.2.2 认证令牌仓库实现

```typescript
// infrastructure/persistence/repositories/user/SequelizeAuthTokenRepository.ts
@injectable()
export class SequelizeAuthTokenRepository extends RepositoryBase<AuthToken> implements AuthTokenRepository {
  constructor(
    @inject('AuthTokenModel') authTokenModel: any,
    @inject('RefreshTokenModel') refreshTokenModel: any,
    @inject('UnitOfWork') unitOfWork: UnitOfWork
  ) {
    super(authTokenModel, unitOfWork);
    this.refreshTokenModel = refreshTokenModel;
  }

  async findByToken(token: string): Promise<AuthToken | null> {
    const authToken = await this.model.findOne({
      where: { token },
      transaction: this.unitOfWork.getTransaction()
    });
    if (!authToken) return null;
    return this.toDomain(authToken);
  }

  async findByRefreshToken(refreshToken: string): Promise<AuthToken | null> {
    const refreshTokenEntity = await this.refreshTokenModel.findOne({
      where: { token: refreshToken },
      transaction: this.unitOfWork.getTransaction()
    });
    if (!refreshTokenEntity) return null;

    const authToken = await this.model.findOne({
      where: { id: refreshTokenEntity.auth_token_id },
      transaction: this.unitOfWork.getTransaction()
    });
    if (!authToken) return null;

    return this.toDomain(authToken);
  }

  async findByUserId(userId: number): Promise<AuthToken[]> {
    const authTokens = await this.model.findAll({
      where: { user_id: userId },
      transaction: this.unitOfWork.getTransaction()
    });
    return Promise.all(authTokens.map(authToken => this.toDomain(authToken)));
  }

  async revokeAllByUserId(userId: number): Promise<void> {
    await this.model.update(
      { status: TokenStatus.REVOKED.value },
      {
        where: { user_id: userId },
        transaction: this.unitOfWork.getTransaction()
      }
    );
  }

  async revokeExpired(): Promise<void> {
    await this.model.update(
      { status: TokenStatus.EXPIRED.value },
      {
        where: {
          expires_at: { [Op.lt]: new Date() },
          status: TokenStatus.ACTIVE.value
        },
        transaction: this.unitOfWork.getTransaction()
      }
    );
  }

  protected async toDomain(entity: any): Promise<AuthToken> {
    // 加载刷新令牌
    const refreshToken = await this.refreshTokenModel.findOne({
      where: { auth_token_id: entity.id },
      transaction: this.unitOfWork.getTransaction()
    });

    // 创建认证令牌实体
    return new AuthToken(
      entity.id,
      entity.user_id,
      entity.token,
      TokenStatus.fromString(entity.status),
      entity.expires_at,
      LoginMethod.fromString(entity.login_method),
      entity.device_info,
      refreshToken ? this.refreshTokenToEntity(refreshToken) : null
    );
  }

  protected toPersistence(entity: AuthToken): any {
    return {
      id: entity.id,
      user_id: entity.userId,
      token: entity.token,
      status: entity.status.value,
      expires_at: entity.expiresAt,
      login_method: entity.loginMethod.value,
      device_info: entity.deviceInfo
    };
  }

  private refreshTokenToEntity(refreshToken: any): RefreshToken {
    return new RefreshToken(
      refreshToken.id,
      refreshToken.auth_token_id,
      refreshToken.token,
      TokenStatus.fromString(refreshToken.status),
      refreshToken.expires_at
    );
  }
}
```

## 8. 应用服务

应用服务协调领域对象完成特定的用例，处理事务边界，并将领域层与外部系统隔离。

### 8.1 用户应用服务

```typescript
// application/services/user/UserApplicationService.ts
@injectable()
export class UserApplicationService extends ApplicationService {
  constructor(
    @inject('UnitOfWork') unitOfWork: UnitOfWork,
    @inject('EventPublisher') eventPublisher: EventPublisher,
    @inject('UserRepository') private readonly userRepository: UserRepository,
    @inject('RoleRepository') private readonly roleRepository: RoleRepository
  ) {
    super(unitOfWork, eventPublisher);
  }

  async registerUser(command: RegisterUserCommand): Promise<UserDto> {
    return this.executeInTransaction(async () => {
      // 验证用户名是否唯一
      const existingUserByUsername = await this.userRepository.findByUsername(command.username);
      if (existingUserByUsername) {
        throw new Error(`用户名 ${command.username} 已存在`);
      }

      // 验证邮箱是否唯一
      if (command.email) {
        const existingUserByEmail = await this.userRepository.findByEmail(command.email);
        if (existingUserByEmail) {
          throw new Error(`邮箱 ${command.email} 已存在`);
        }
      }

      // 验证手机号是否唯一
      if (command.phoneNumber) {
        const existingUserByPhoneNumber = await this.userRepository.findByPhoneNumber(command.phoneNumber);
        if (existingUserByPhoneNumber) {
          throw new Error(`手机号 ${command.phoneNumber} 已存在`);
        }
      }

      // 验证微信OpenID是否唯一
      if (command.wechatOpenId) {
        const existingUserByWechatOpenId = await this.userRepository.findByWechatOpenId(command.wechatOpenId);
        if (existingUserByWechatOpenId) {
          throw new Error(`微信OpenID ${command.wechatOpenId} 已存在`);
        }
      }

      // 获取默认角色
      const defaultRole = await this.roleRepository.findByName('user');
      if (!defaultRole) {
        throw new Error('默认角色不存在');
      }

      // 创建用户
      const user = new User(
        0,
        command.username,
        command.nickname || command.username,
        command.email ? Email.create(command.email) : null,
        command.phoneNumber ? PhoneNumber.create(command.phoneNumber) : null,
        command.password ? Password.create(command.password) : null,
        command.wechatOpenId,
        null,
        Gender.UNKNOWN,
        null,
        UserStatus.ACTIVE,
        [defaultRole],
        new Date(),
        null,
        this.createDefaultUserSetting(0)
      );

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      // 发布领域事件
      this.eventPublisher.publish(new UserCreatedEvent(savedUser));

      return this.toDto(savedUser);
    });
  }

  async updateUserProfile(command: UpdateUserProfileCommand): Promise<UserDto> {
    return this.executeInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 更新用户资料
      user.updateProfile(
        command.nickname,
        command.avatar,
        Gender.fromString(command.gender),
        command.birthday
      );

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toDto(savedUser);
    });
  }

  async updateUserEmail(command: UpdateUserEmailCommand): Promise<UserDto> {
    return this.executeInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 验证邮箱是否唯一
      const existingUserByEmail = await this.userRepository.findByEmail(command.email);
      if (existingUserByEmail && existingUserByEmail.id !== command.userId) {
        throw new Error(`邮箱 ${command.email} 已存在`);
      }

      // 更新用户邮箱
      user.updateEmail(Email.create(command.email, command.verified));

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toDto(savedUser);
    });
  }

  async updateUserPassword(command: UpdateUserPasswordCommand): Promise<UserDto> {
    return this.executeInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 验证旧密码
      if (user.password && !user.password.verify(command.oldPassword)) {
        throw new Error('旧密码不正确');
      }

      // 更新用户密码
      user.updatePassword(Password.create(command.newPassword));

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toDto(savedUser);
    });
  }

  async disableUser(command: DisableUserCommand): Promise<UserDto> {
    return this.executeInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 禁用用户
      user.disable();

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toDto(savedUser);
    });
  }

  async enableUser(command: EnableUserCommand): Promise<UserDto> {
    return this.executeInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 启用用户
      user.enable();

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toDto(savedUser);
    });
  }

  async getUser(query: GetUserQuery): Promise<UserDto | null> {
    const user = await this.userRepository.findById(query.userId);
    if (!user) {
      return null;
    }
    return this.toDto(user);
  }

  async getUserByUsername(query: GetUserByUsernameQuery): Promise<UserDto | null> {
    const user = await this.userRepository.findByUsername(query.username);
    if (!user) {
      return null;
    }
    return this.toDto(user);
  }

  async getUserWithPagination(query: GetUserWithPaginationQuery): Promise<{ items: UserDto[], total: number }> {
    const { items, total } = await this.userRepository.findWithPagination(query.page, query.pageSize);
    return {
      items: items.map(user => this.toDto(user)),
      total
    };
  }

  private createDefaultUserSetting(userId: number): UserSetting {
    return new UserSetting(
      0,
      userId,
      'default',
      16,
      30,
      null,
      new PrivacySettings(false, false, false, false),
      []
    );
  }

  private toDto(user: User): UserDto {
    return {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      email: user.email?.address,
      emailVerified: user.email?.verified,
      phoneNumber: user.phoneNumber?.number,
      phoneVerified: user.phoneNumber?.verified,
      wechatOpenId: user.wechatOpenId,
      avatar: user.avatar,
      gender: user.gender.value,
      birthday: user.birthday,
      status: user.status.value,
      roles: user.roles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description
      })),
      registeredAt: user.registeredAt,
      lastLoginAt: user.lastLoginAt,
      userSetting: {
        id: user.userSetting.id,
        theme: user.userSetting.theme,
        fontSize: user.userSetting.fontSize,
        dailyGoalMinutes: user.userSetting.dailyGoalMinutes,
        reminderTime: user.userSetting.reminderTime,
        privacySettings: {
          publicProfile: user.userSetting.privacySettings.publicProfile,
          publicLearningPlan: user.userSetting.privacySettings.publicLearningPlan,
          publicNotes: user.userSetting.privacySettings.publicNotes,
          publicExercises: user.userSetting.privacySettings.publicExercises
        },
        notificationSettings: user.userSetting.notificationSettings.map(ns => ({
          id: ns.id,
          type: ns.notificationType.value,
          channel: ns.notificationChannel.value,
          enabled: ns.enabled
        }))
      }
    };
  }
}
```

### 8.2 认证应用服务

```typescript
// application/services/user/AuthenticationApplicationService.ts
@injectable()
export class AuthenticationApplicationService extends ApplicationService {
  constructor(
    @inject('UnitOfWork') unitOfWork: UnitOfWork,
    @inject('EventPublisher') eventPublisher: EventPublisher,
    @inject('UserRepository') private readonly userRepository: UserRepository,
    @inject('AuthTokenRepository') private readonly authTokenRepository: AuthTokenRepository,
    @inject('JwtService') private readonly jwtService: JwtService
  ) {
    super(unitOfWork, eventPublisher);
  }

  async login(command: LoginCommand): Promise<AuthTokenDto> {
    return this.executeInTransaction(async () => {
      let user: User | null = null;

      // 根据登录方式获取用户
      switch (command.loginMethod) {
        case 'password':
          user = await this.userRepository.findByUsername(command.username!);
          if (!user || !user.password || !user.password.verify(command.password!)) {
            throw new Error('用户名或密码不正确');
          }
          break;
        case 'wechat':
          user = await this.userRepository.findByWechatOpenId(command.wechatOpenId!);
          if (!user) {
            throw new Error('微信用户不存在');
          }
          break;
        case 'phone':
          user = await this.userRepository.findByPhoneNumber(command.phoneNumber!);
          if (!user) {
            throw new Error('手机号用户不存在');
          }
          // 验证码验证逻辑省略
          break;
        default:
          throw new Error('不支持的登录方式');
      }

      // 检查用户状态
      if (user.status.equals(UserStatus.DISABLED)) {
        throw new Error('用户已被禁用');
      }

      if (user.status.equals(UserStatus.LOCKED)) {
        throw new Error('用户已被锁定');
      }

      // 创建认证令牌
      const tokenValue = this.jwtService.generateToken({
        userId: user.id,
        username: user.username,
        roles: user.roles.map(role => role.name)
      });

      const refreshTokenValue = this.jwtService.generateRefreshToken({
        userId: user.id
      });

      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      const refreshExpiresAt = new Date();
      refreshExpiresAt.setDate(refreshExpiresAt.getDate() + 30);

      const authToken = new AuthToken(
        0,
        user.id,
        tokenValue,
        TokenStatus.ACTIVE,
        expiresAt,
        LoginMethod.fromString(command.loginMethod),
        command.deviceInfo,
        new RefreshToken(
          0,
          0,
          refreshTokenValue,
          TokenStatus.ACTIVE,
          refreshExpiresAt
        )
      );

      // 保存认证令牌
      const savedAuthToken = await this.authTokenRepository.save(authToken);

      // 更新用户最后登录时间
      user.updateLastLoginAt(new Date());
      await this.userRepository.save(user);

      // 发布领域事件
      this.eventPublisher.publish(new UserLoggedInEvent(
        user.id,
        LoginMethod.fromString(command.loginMethod),
        command.deviceInfo
      ));

      return this.toDto(savedAuthToken);
    });
  }

  async refreshToken(command: RefreshTokenCommand): Promise<AuthTokenDto> {
    return this.executeInTransaction(async () => {
      // 获取认证令牌
      const authToken = await this.authTokenRepository.findByRefreshToken(command.refreshToken);
      if (!authToken || !authToken.refreshToken) {
        throw new Error('刷新令牌无效');
      }

      // 验证刷新令牌
      if (!authToken.refreshToken.isValid()) {
        throw new Error('刷新令牌已过期或已撤销');
      }

      // 获取用户
      const user = await this.userRepository.findById(authToken.userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 检查用户状态
      if (user.status.equals(UserStatus.DISABLED)) {
        throw new Error('用户已被禁用');
      }

      if (user.status.equals(UserStatus.LOCKED)) {
        throw new Error('用户已被锁定');
      }

      // 创建新的认证令牌
      const tokenValue = this.jwtService.generateToken({
        userId: user.id,
        username: user.username,
        roles: user.roles.map(role => role.name)
      });

      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      const newAuthToken = new AuthToken(
        0,
        user.id,
        tokenValue,
        TokenStatus.ACTIVE,
        expiresAt,
        authToken.loginMethod,
        authToken.deviceInfo,
        authToken.refreshToken
      );

      // 保存新的认证令牌
      const savedAuthToken = await this.authTokenRepository.save(newAuthToken);

      // 撤销旧的认证令牌
      authToken.revoke();
      await this.authTokenRepository.save(authToken);

      // 发布领域事件
      this.eventPublisher.publish(new AuthTokenRefreshedEvent(authToken, savedAuthToken));

      return this.toDto(savedAuthToken);
    });
  }

  async logout(command: LogoutCommand): Promise<void> {
    return this.executeInTransaction(async () => {
      // 获取认证令牌
      const authToken = await this.authTokenRepository.findByToken(command.token);
      if (!authToken) {
        throw new Error('认证令牌无效');
      }

      // 撤销认证令牌
      authToken.revoke();
      await this.authTokenRepository.save(authToken);

      // 发布领域事件
      this.eventPublisher.publish(new UserLoggedOutEvent(authToken.userId));
    });
  }

  async validateToken(query: ValidateTokenQuery): Promise<boolean> {
    const authToken = await this.authTokenRepository.findByToken(query.token);
    if (!authToken) {
      return false;
    }

    return authToken.isValid();
  }

  private toDto(authToken: AuthToken): AuthTokenDto {
    return {
      token: authToken.token,
      refreshToken: authToken.refreshToken?.token,
      expiresAt: authToken.expiresAt,
      userId: authToken.userId
    };
  }
}
```

## 9. 控制器设计

控制器负责处理HTTP请求，调用应用服务，并返回HTTP响应。

### 9.1 用户控制器

```typescript
// interfaces/api/controllers/user/UserController.ts
@Controller('api/v2/users')
@ApiTags('用户')
@UseGuards(AuthGuard)
export class UserController {
  constructor(
    private readonly userApplicationService: UserApplicationService
  ) {}

  @Post()
  @ApiOperation({ summary: '注册用户' })
  @ApiResponse({ status: 201, description: '用户注册成功', type: UserResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 409, description: '用户名、邮箱或手机号已存在' })
  @Public()
  async registerUser(@Body() registerUserDto: RegisterUserDto): Promise<UserResponseDto> {
    try {
      const command = new RegisterUserCommand(
        registerUserDto.username,
        registerUserDto.password,
        registerUserDto.nickname,
        registerUserDto.email,
        registerUserDto.phoneNumber,
        registerUserDto.wechatOpenId
      );

      const user = await this.userApplicationService.registerUser(command);

      return {
        success: true,
        data: user
      };
    } catch (error) {
      if (error.message.includes('已存在')) {
        throw new ConflictException(error.message);
      }
      throw new BadRequestException(error.message);
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '获取用户信息成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getUser(@Param('id') id: number): Promise<UserResponseDto> {
    const query = new GetUserQuery(id);
    const user = await this.userApplicationService.getUser(query);

    if (!user) {
      throw new NotFoundException(`用户ID ${id} 不存在`);
    }

    return {
      success: true,
      data: user
    };
  }

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiQuery({ name: 'page', description: '页码', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', description: '每页数量', required: false, type: Number })
  @ApiResponse({ status: 200, description: '获取用户列表成功', type: UserListResponseDto })
  @Roles('admin')
  async getUserList(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 20
  ): Promise<UserListResponseDto> {
    const query = new GetUserWithPaginationQuery(page, pageSize);
    const { items, total } = await this.userApplicationService.getUserWithPagination(query);

    return {
      success: true,
      data: {
        items,
        pagination: {
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  }

  @Patch(':id/profile')
  @ApiOperation({ summary: '更新用户资料' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '更新用户资料成功', type: UserResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async updateUserProfile(
    @Param('id') id: number,
    @Body() updateUserProfileDto: UpdateUserProfileDto,
    @CurrentUser() currentUser: JwtPayload
  ): Promise<UserResponseDto> {
    // 检查权限
    if (currentUser.userId !== id && !currentUser.roles.includes('admin')) {
      throw new ForbiddenException('没有权限更新其他用户的资料');
    }

    try {
      const command = new UpdateUserProfileCommand(
        id,
        updateUserProfileDto.nickname,
        updateUserProfileDto.avatar,
        updateUserProfileDto.gender,
        updateUserProfileDto.birthday
      );

      const user = await this.userApplicationService.updateUserProfile(command);

      return {
        success: true,
        data: user
      };
    } catch (error) {
      if (error.message.includes('不存在')) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException(error.message);
    }
  }

  @Patch(':id/email')
  @ApiOperation({ summary: '更新用户邮箱' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '更新用户邮箱成功', type: UserResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @ApiResponse({ status: 409, description: '邮箱已存在' })
  async updateUserEmail(
    @Param('id') id: number,
    @Body() updateUserEmailDto: UpdateUserEmailDto,
    @CurrentUser() currentUser: JwtPayload
  ): Promise<UserResponseDto> {
    // 检查权限
    if (currentUser.userId !== id && !currentUser.roles.includes('admin')) {
      throw new ForbiddenException('没有权限更新其他用户的邮箱');
    }

    try {
      const command = new UpdateUserEmailCommand(
        id,
        updateUserEmailDto.email,
        updateUserEmailDto.verified || false
      );

      const user = await this.userApplicationService.updateUserEmail(command);

      return {
        success: true,
        data: user
      };
    } catch (error) {
      if (error.message.includes('不存在')) {
        throw new NotFoundException(error.message);
      }
      if (error.message.includes('已存在')) {
        throw new ConflictException(error.message);
      }
      throw new BadRequestException(error.message);
    }
  }

  @Patch(':id/password')
  @ApiOperation({ summary: '更新用户密码' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '更新用户密码成功', type: UserResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async updateUserPassword(
    @Param('id') id: number,
    @Body() updateUserPasswordDto: UpdateUserPasswordDto,
    @CurrentUser() currentUser: JwtPayload
  ): Promise<UserResponseDto> {
    // 检查权限
    if (currentUser.userId !== id && !currentUser.roles.includes('admin')) {
      throw new ForbiddenException('没有权限更新其他用户的密码');
    }

    try {
      const command = new UpdateUserPasswordCommand(
        id,
        updateUserPasswordDto.oldPassword,
        updateUserPasswordDto.newPassword
      );

      const user = await this.userApplicationService.updateUserPassword(command);

      return {
        success: true,
        data: user
      };
    } catch (error) {
      if (error.message.includes('不存在')) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException(error.message);
    }
  }

  @Patch(':id/disable')
  @ApiOperation({ summary: '禁用用户' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '禁用用户成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @Roles('admin')
  async disableUser(@Param('id') id: number): Promise<UserResponseDto> {
    try {
      const command = new DisableUserCommand(id);
      const user = await this.userApplicationService.disableUser(command);

      return {
        success: true,
        data: user
      };
    } catch (error) {
      if (error.message.includes('不存在')) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException(error.message);
    }
  }

  @Patch(':id/enable')
  @ApiOperation({ summary: '启用用户' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '启用用户成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @Roles('admin')
  async enableUser(@Param('id') id: number): Promise<UserResponseDto> {
    try {
      const command = new EnableUserCommand(id);
      const user = await this.userApplicationService.enableUser(command);

      return {
        success: true,
        data: user
      };
    } catch (error) {
      if (error.message.includes('不存在')) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException(error.message);
    }
  }
}
```

### 9.2 认证控制器

```typescript
// interfaces/api/controllers/user/AuthController.ts
@Controller('api/v2/auth')
@ApiTags('认证')
export class AuthController {
  constructor(
    private readonly authenticationApplicationService: AuthenticationApplicationService
  ) {}

  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功', type: AuthTokenResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '认证失败' })
  @Public()
  async login(@Body() loginDto: LoginDto, @Req() request: Request): Promise<AuthTokenResponseDto> {
    try {
      const command = new LoginCommand(
        loginDto.loginMethod,
        loginDto.username,
        loginDto.password,
        loginDto.phoneNumber,
        loginDto.verificationCode,
        loginDto.wechatOpenId,
        request.headers['user-agent'] || 'unknown'
      );

      const authToken = await this.authenticationApplicationService.login(command);

      return {
        success: true,
        data: authToken
      };
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  @Post('refresh-token')
  @ApiOperation({ summary: '刷新令牌' })
  @ApiResponse({ status: 200, description: '刷新令牌成功', type: AuthTokenResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '刷新令牌无效' })
  @Public()
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<AuthTokenResponseDto> {
    try {
      const command = new RefreshTokenCommand(refreshTokenDto.refreshToken);
      const authToken = await this.authenticationApplicationService.refreshToken(command);

      return {
        success: true,
        data: authToken
      };
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  @Post('logout')
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({ status: 200, description: '登出成功' })
  @ApiResponse({ status: 401, description: '认证令牌无效' })
  @UseGuards(AuthGuard)
  async logout(@Headers('authorization') authorization: string): Promise<SuccessResponseDto> {
    try {
      const token = authorization.replace('Bearer ', '');
      const command = new LogoutCommand(token);
      await this.authenticationApplicationService.logout(command);

      return {
        success: true
      };
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  @Get('validate-token')
  @ApiOperation({ summary: '验证令牌' })
  @ApiResponse({ status: 200, description: '验证令牌成功', type: TokenValidationResponseDto })
  @Public()
  async validateToken(@Headers('authorization') authorization: string): Promise<TokenValidationResponseDto> {
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return {
        success: true,
        data: {
          valid: false
        }
      };
    }

    const token = authorization.replace('Bearer ', '');
    const query = new ValidateTokenQuery(token);
    const valid = await this.authenticationApplicationService.validateToken(query);

    return {
      success: true,
      data: {
        valid
      }
    };
  }
}
```

## 10. 测试策略

测试是确保代码质量和功能正确性的重要手段。对于用户领域的DDD实现，我们采用多层次的测试策略。

### 10.1 单元测试

单元测试用于测试单个组件的功能，确保其按照预期工作。

#### 10.1.1 实体测试

```typescript
// test/unit/domain/models/user/User.spec.ts
describe('User', () => {
  let user: User;
  let defaultRole: Role;

  beforeEach(() => {
    defaultRole = new Role(1, 'user', '普通用户', []);
    user = new User(
      1,
      'testuser',
      'Test User',
      Email.create('<EMAIL>'),
      PhoneNumber.create('13800138000'),
      Password.create('password123'),
      null,
      null,
      Gender.MALE,
      new Date('1990-01-01'),
      UserStatus.ACTIVE,
      [defaultRole],
      new Date(),
      null,
      new UserSetting(
        1,
        1,
        'default',
        16,
        30,
        null,
        new PrivacySettings(false, false, false, false),
        []
      )
    );
  });

  describe('updateProfile', () => {
    it('should update user profile and add domain event', () => {
      // Arrange
      const newNickname = 'New Name';
      const newAvatar = 'new-avatar.jpg';
      const newGender = Gender.FEMALE;
      const newBirthday = new Date('1995-05-05');

      // Act
      user.updateProfile(newNickname, newAvatar, newGender, newBirthday);

      // Assert
      expect(user.nickname).toBe(newNickname);
      expect(user.avatar).toBe(newAvatar);
      expect(user.gender).toBe(newGender);
      expect(user.birthday).toBe(newBirthday);
      expect(user.domainEvents.length).toBe(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserProfileUpdatedEvent);
    });
  });

  describe('updateEmail', () => {
    it('should update user email and add domain event', () => {
      // Arrange
      const newEmail = Email.create('<EMAIL>');

      // Act
      user.updateEmail(newEmail);

      // Assert
      expect(user.email).toBe(newEmail);
      expect(user.domainEvents.length).toBe(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserEmailUpdatedEvent);
    });

    it('should not update email if it is the same', () => {
      // Arrange
      const sameEmail = Email.create('<EMAIL>');

      // Act
      user.updateEmail(sameEmail);

      // Assert
      expect(user.email?.address).toBe('<EMAIL>');
      expect(user.domainEvents.length).toBe(0);
    });
  });

  // 其他方法的测试...
});
```

#### 10.1.2 值对象测试

```typescript
// test/unit/domain/models/user/Email.spec.ts
describe('Email', () => {
  describe('create', () => {
    it('should create a valid email', () => {
      // Act
      const email = Email.create('<EMAIL>');

      // Assert
      expect(email.address).toBe('<EMAIL>');
      expect(email.verified).toBe(false);
    });

    it('should create a verified email', () => {
      // Act
      const email = Email.create('<EMAIL>', true);

      // Assert
      expect(email.address).toBe('<EMAIL>');
      expect(email.verified).toBe(true);
    });

    it('should throw an error for invalid email', () => {
      // Act & Assert
      expect(() => Email.create('invalid-email')).toThrow('Invalid email address: invalid-email');
    });
  });

  describe('verify', () => {
    it('should return a new verified email', () => {
      // Arrange
      const email = Email.create('<EMAIL>');

      // Act
      const verifiedEmail = email.verify();

      // Assert
      expect(verifiedEmail.address).toBe('<EMAIL>');
      expect(verifiedEmail.verified).toBe(true);
    });
  });

  describe('equals', () => {
    it('should return true for emails with the same address', () => {
      // Arrange
      const email1 = Email.create('<EMAIL>');
      const email2 = Email.create('<EMAIL>', true);

      // Act & Assert
      expect(email1.equals(email2)).toBe(true);
    });

    it('should return false for emails with different addresses', () => {
      // Arrange
      const email1 = Email.create('<EMAIL>');
      const email2 = Email.create('<EMAIL>');

      // Act & Assert
      expect(email1.equals(email2)).toBe(false);
    });
  });
});
```

#### 10.1.3 应用服务测试

```typescript
// test/unit/application/services/user/UserApplicationService.spec.ts
describe('UserApplicationService', () => {
  let userApplicationService: UserApplicationService;
  let userRepository: MockUserRepository;
  let roleRepository: MockRoleRepository;
  let unitOfWork: MockUnitOfWork;
  let eventPublisher: MockEventPublisher;

  beforeEach(() => {
    userRepository = new MockUserRepository();
    roleRepository = new MockRoleRepository();
    unitOfWork = new MockUnitOfWork();
    eventPublisher = new MockEventPublisher();

    userApplicationService = new UserApplicationService(
      unitOfWork,
      eventPublisher,
      userRepository,
      roleRepository
    );
  });

  describe('registerUser', () => {
    it('should register a new user', async () => {
      // Arrange
      const command = new RegisterUserCommand(
        'newuser',
        'password123',
        'New User',
        '<EMAIL>',
        '13800138000',
        null
      );

      const defaultRole = new Role(1, 'user', '普通用户', []);
      roleRepository.findByName.mockResolvedValue(defaultRole);
      userRepository.findByUsername.mockResolvedValue(null);
      userRepository.findByEmail.mockResolvedValue(null);
      userRepository.findByPhoneNumber.mockResolvedValue(null);
      userRepository.save.mockImplementation((user) => {
        user.id = 1;
        return Promise.resolve(user);
      });

      // Act
      const result = await userApplicationService.registerUser(command);

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
      expect(result.username).toBe('newuser');
      expect(result.nickname).toBe('New User');
      expect(result.email).toBe('<EMAIL>');
      expect(result.phoneNumber).toBe('13800138000');
      expect(result.status).toBe('ACTIVE');
      expect(result.roles).toHaveLength(1);
      expect(result.roles[0].name).toBe('user');
      expect(unitOfWork.begin).toHaveBeenCalled();
      expect(unitOfWork.commit).toHaveBeenCalled();
      expect(eventPublisher.publish).toHaveBeenCalledWith(expect.any(UserCreatedEvent));
    });

    it('should throw an error if username already exists', async () => {
      // Arrange
      const command = new RegisterUserCommand(
        'existinguser',
        'password123',
        'Existing User',
        '<EMAIL>',
        '13800138000',
        null
      );

      const existingUser = new User(
        1,
        'existinguser',
        'Existing User',
        Email.create('<EMAIL>'),
        PhoneNumber.create('13900139000'),
        Password.create('password123'),
        null,
        null,
        Gender.UNKNOWN,
        null,
        UserStatus.ACTIVE,
        [],
        new Date(),
        null,
        new UserSetting(1, 1, 'default', 16, 30, null, new PrivacySettings(false, false, false, false), [])
      );

      userRepository.findByUsername.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(userApplicationService.registerUser(command)).rejects.toThrow('用户名 existinguser 已存在');
      expect(unitOfWork.begin).toHaveBeenCalled();
      expect(unitOfWork.rollback).toHaveBeenCalled();
    });

    // 其他测试用例...
  });

  // 其他方法的测试...
});
```

### 10.2 集成测试

集成测试用于测试多个组件之间的交互，确保它们能够正确协作。

#### 10.2.1 仓库集成测试

```typescript
// test/integration/infrastructure/persistence/repositories/user/SequelizeUserRepository.spec.ts
describe('SequelizeUserRepository', () => {
  let sequelize: Sequelize;
  let userRepository: SequelizeUserRepository;
  let unitOfWork: UnitOfWorkImpl;
  let userModel: any;
  let roleModel: any;
  let userSettingModel: any;

  beforeAll(async () => {
    // 设置测试数据库
    sequelize = new Sequelize({
      dialect: 'sqlite',
      storage: ':memory:',
      logging: false
    });

    // 初始化模型
    userModel = initUserModel(sequelize);
    roleModel = initRoleModel(sequelize);
    userSettingModel = initUserSettingModel(sequelize);

    // 设置关联关系
    setupAssociations(userModel, roleModel, userSettingModel);

    // 同步数据库
    await sequelize.sync({ force: true });

    // 创建工作单元
    unitOfWork = new UnitOfWorkImpl(sequelize, new MockEventPublisher());

    // 创建仓库
    userRepository = new SequelizeUserRepository(
      userModel,
      userSettingModel,
      roleModel,
      unitOfWork
    );
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    // 清空数据库
    await userModel.destroy({ truncate: true, cascade: true });
    await roleModel.destroy({ truncate: true, cascade: true });
    await userSettingModel.destroy({ truncate: true, cascade: true });

    // 插入测试数据
    await roleModel.create({
      id: 1,
      name: 'user',
      description: '普通用户'
    });

    await userModel.create({
      id: 1,
      username: 'testuser',
      nickname: 'Test User',
      email: '<EMAIL>',
      email_verified: true,
      phone_number: '13800138000',
      phone_verified: true,
      password_hash: 'hash',
      password_salt: 'salt',
      gender: 'MALE',
      status: 'ACTIVE',
      created_at: new Date()
    });

    await userSettingModel.create({
      id: 1,
      user_id: 1,
      theme: 'default',
      font_size: 16,
      daily_goal_minutes: 30,
      privacy_settings: JSON.stringify({
        publicProfile: false,
        publicLearningPlan: false,
        publicNotes: false,
        publicExercises: false
      })
    });

    // 关联用户和角色
    const user = await userModel.findByPk(1);
    const role = await roleModel.findByPk(1);
    await user.addRole(role);
  });

  describe('findById', () => {
    it('should find user by id', async () => {
      // Act
      await unitOfWork.begin();
      const user = await userRepository.findById(1);
      await unitOfWork.commit();

      // Assert
      expect(user).toBeDefined();
      expect(user?.id).toBe(1);
      expect(user?.username).toBe('testuser');
      expect(user?.nickname).toBe('Test User');
      expect(user?.email?.address).toBe('<EMAIL>');
      expect(user?.email?.verified).toBe(true);
      expect(user?.phoneNumber?.number).toBe('13800138000');
      expect(user?.phoneNumber?.verified).toBe(true);
      expect(user?.gender.value).toBe('MALE');
      expect(user?.status.value).toBe('ACTIVE');
      expect(user?.roles).toHaveLength(1);
      expect(user?.roles[0].name).toBe('user');
      expect(user?.userSetting).toBeDefined();
      expect(user?.userSetting.theme).toBe('default');
      expect(user?.userSetting.fontSize).toBe(16);
      expect(user?.userSetting.dailyGoalMinutes).toBe(30);
    });

    it('should return null if user not found', async () => {
      // Act
      await unitOfWork.begin();
      const user = await userRepository.findById(999);
      await unitOfWork.commit();

      // Assert
      expect(user).toBeNull();
    });
  });

  // 其他方法的测试...
});
```

#### 10.2.2 应用服务集成测试

```typescript
// test/integration/application/services/user/UserApplicationService.spec.ts
describe('UserApplicationService Integration', () => {
  let sequelize: Sequelize;
  let userApplicationService: UserApplicationService;
  let userRepository: SequelizeUserRepository;
  let roleRepository: SequelizeRoleRepository;
  let unitOfWork: UnitOfWorkImpl;
  let eventPublisher: EventPublisher;
  let userModel: any;
  let roleModel: any;
  let userSettingModel: any;

  beforeAll(async () => {
    // 设置测试数据库
    sequelize = new Sequelize({
      dialect: 'sqlite',
      storage: ':memory:',
      logging: false
    });

    // 初始化模型
    userModel = initUserModel(sequelize);
    roleModel = initRoleModel(sequelize);
    userSettingModel = initUserSettingModel(sequelize);

    // 设置关联关系
    setupAssociations(userModel, roleModel, userSettingModel);

    // 同步数据库
    await sequelize.sync({ force: true });

    // 创建事件发布者
    eventPublisher = new InMemoryEventPublisher();

    // 创建工作单元
    unitOfWork = new UnitOfWorkImpl(sequelize, eventPublisher);

    // 创建仓库
    userRepository = new SequelizeUserRepository(
      userModel,
      userSettingModel,
      roleModel,
      unitOfWork
    );

    roleRepository = new SequelizeRoleRepository(
      roleModel,
      unitOfWork
    );

    // 创建应用服务
    userApplicationService = new UserApplicationService(
      unitOfWork,
      eventPublisher,
      userRepository,
      roleRepository
    );
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    // 清空数据库
    await userModel.destroy({ truncate: true, cascade: true });
    await roleModel.destroy({ truncate: true, cascade: true });
    await userSettingModel.destroy({ truncate: true, cascade: true });

    // 插入测试数据
    await roleModel.create({
      id: 1,
      name: 'user',
      description: '普通用户'
    });
  });

  describe('registerUser', () => {
    it('should register a new user', async () => {
      // Arrange
      const command = new RegisterUserCommand(
        'newuser',
        'password123',
        'New User',
        '<EMAIL>',
        '13800138000',
        null
      );

      // Act
      const result = await userApplicationService.registerUser(command);

      // Assert
      expect(result).toBeDefined();
      expect(result.username).toBe('newuser');
      expect(result.nickname).toBe('New User');
      expect(result.email).toBe('<EMAIL>');
      expect(result.phoneNumber).toBe('13800138000');
      expect(result.status).toBe('ACTIVE');
      expect(result.roles).toHaveLength(1);
      expect(result.roles[0].name).toBe('user');

      // 验证数据库中的用户
      const user = await userModel.findOne({
        where: { username: 'newuser' },
        include: [roleModel, userSettingModel]
      });
      expect(user).toBeDefined();
      expect(user.username).toBe('newuser');
      expect(user.nickname).toBe('New User');
      expect(user.email).toBe('<EMAIL>');
      expect(user.phone_number).toBe('13800138000');
      expect(user.status).toBe('ACTIVE');
      expect(user.Roles).toHaveLength(1);
      expect(user.Roles[0].name).toBe('user');
      expect(user.UserSetting).toBeDefined();
      expect(user.UserSetting.theme).toBe('default');
    });

    // 其他测试用例...
  });

  // 其他方法的测试...
});
```

### 10.3 端到端测试

端到端测试用于测试整个系统的功能，从用户界面到数据库，确保系统作为一个整体能够正常工作。

```typescript
// test/e2e/user.e2e-spec.ts
describe('User API (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // 创建测试数据
    await createTestData(app);

    // 获取认证令牌
    const loginResponse = await request(app.getHttpServer())
      .post('/api/v2/auth/login')
      .send({
        loginMethod: 'password',
        username: 'admin',
        password: 'admin123'
      });

    authToken = loginResponse.body.data.token;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /api/v2/users', () => {
    it('should register a new user', async () => {
      // Act
      const response = await request(app.getHttpServer())
        .post('/api/v2/users')
        .send({
          username: 'newuser',
          password: 'password123',
          nickname: 'New User',
          email: '<EMAIL>',
          phoneNumber: '13800138000'
        });

      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.username).toBe('newuser');
      expect(response.body.data.nickname).toBe('New User');
      expect(response.body.data.email).toBe('<EMAIL>');
      expect(response.body.data.phoneNumber).toBe('13800138000');
      expect(response.body.data.status).toBe('ACTIVE');
      expect(response.body.data.roles).toHaveLength(1);
      expect(response.body.data.roles[0].name).toBe('user');
    });

    it('should return 409 if username already exists', async () => {
      // Act
      const response = await request(app.getHttpServer())
        .post('/api/v2/users')
        .send({
          username: 'admin',
          password: 'password123',
          nickname: 'Admin User',
          email: '<EMAIL>',
          phoneNumber: '13900139000'
        });

      // Assert
      expect(response.status).toBe(409);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
      expect(response.body.error.message).toContain('用户名 admin 已存在');
    });

    // 其他测试用例...
  });

  // 其他端点的测试...
});
```

## 11. 实施步骤

### 11.1 准备阶段（1天）

- 创建用户领域的目录结构
- 设计数据库表结构
- 准备测试环境

### 11.2 领域层实现（3天）

- 实现实体和值对象
- 实现聚合和聚合根
- 实现领域事件
- 定义仓库接口

### 11.3 基础设施层实现（3天）

- 实现仓库
- 实现工作单元
- 实现事件发布者
- 实现数据库模型

### 11.4 应用层实现（3天）

- 实现应用服务
- 实现命令和查询对象
- 实现DTO
- 实现事件处理器

### 11.5 接口层实现（2天）

- 实现控制器
- 实现中间件
- 实现验证器
- 实现响应格式化

### 11.6 测试（3天）

- 编写单元测试
- 编写集成测试
- 编写端到端测试
- 运行测试并修复问题

## 12. 时间规划

| 阶段 | 时间 | 负责人 |
|------|------|-------|
| 准备阶段 | 2025-05-16 | 后端团队 |
| 领域层实现 | 2025-05-17 ~ 2025-05-19 | 后端团队 |
| 基础设施层实现 | 2025-05-20 ~ 2025-05-22 | 后端团队 |
| 应用层实现 | 2025-05-23 ~ 2025-05-25 | 后端团队 |
| 接口层实现 | 2025-05-26 ~ 2025-05-27 | 后端团队 |
| 测试 | 2025-05-28 ~ 2025-05-30 | 测试团队 |
| 完成 | 2025-05-31 | - |

## 13. 实施进度

### 13.1 已完成工作

#### 13.1.1 准备阶段（2025-05-16）
- [x] 创建用户领域的目录结构
  - 创建了domain/models/user、domain/events/user、domain/repositories/user等目录
  - 创建了application/services/user、application/commands/user等目录
  - 创建了infrastructure/persistence/repositories/user目录
  - 创建了interfaces/api/controllers/user目录

#### 13.1.2 领域层实现（2025-05-17 ~ 2025-05-19）
- [x] 实现基础实体和聚合根基类
  - 实现了Entity基类
  - 实现了AggregateRoot基类
  - 实现了DomainEvent接口和DomainEventBase基类
- [x] 实现值对象
  - 实现了Email值对象
  - 实现了PhoneNumber值对象
  - 实现了Password值对象
  - 实现了UserStatus值对象
  - 实现了Gender值对象
  - 实现了NotificationType值对象
  - 实现了NotificationChannel值对象
  - 实现了PrivacySettings值对象
- [x] 实现实体
  - 实现了User实体（聚合根）
  - 实现了UserSetting实体
  - 实现了UserNotificationSetting实体
  - 实现了Role实体（聚合根）
  - 实现了Permission实体
- [x] 实现领域事件
  - 实现了用户相关的领域事件（UserCreatedEvent、UserProfileUpdatedEvent等）
  - 实现了角色相关的领域事件（RoleCreatedEvent、RoleUpdatedEvent等）
- [x] 定义仓库接口
  - 定义了Repository基础接口
  - 定义了UserRepository接口
  - 定义了UserSettingRepository接口
  - 定义了RoleRepository接口
  - 定义了PermissionRepository接口

### 13.2 进行中工作

#### 13.2.1 基础设施层实现（2025-05-20 ~ 2025-05-22）
- [ ] 实现仓库
  - [ ] 实现SequelizeUserRepository
  - [ ] 实现SequelizeUserSettingRepository
  - [ ] 实现SequelizeRoleRepository
  - [ ] 实现SequelizePermissionRepository
- [ ] 实现工作单元
  - [ ] 实现UnitOfWork接口
  - [ ] 实现UnitOfWorkImpl
- [ ] 实现事件发布者
  - [ ] 实现EventPublisher接口
  - [ ] 实现EventPublisherImpl
- [ ] 实现数据库模型
  - [ ] 实现UserModel
  - [ ] 实现UserSettingModel
  - [ ] 实现RoleModel
  - [ ] 实现PermissionModel

### 13.3 进行中工作

#### 13.3.1 测试（2025-05-28 ~ 2025-06-05）
- [x] 编写实体单元测试
  - [x] 实现User实体的单元测试
  - [x] 实现Role实体的单元测试
  - [x] 实现Permission实体的单元测试
  - [x] 实现UserSetting实体的单元测试
  - [x] 实现UserNotificationSetting实体的单元测试
- [x] 编写值对象单元测试
  - [x] 实现Email值对象的单元测试
  - [x] 实现PhoneNumber值对象的单元测试
  - [x] 实现Password值对象的单元测试
  - [x] 实现Gender值对象的单元测试
  - [x] 实现UserStatus值对象的单元测试
  - [x] 实现NotificationType值对象的单元测试
  - [x] 实现NotificationChannel值对象的单元测试
  - [x] 实现PrivacySettings值对象的单元测试
- [ ] 编写仓库单元测试
  - [ ] 实现SequelizeUserRepository的单元测试
  - [ ] 实现SequelizeUserSettingRepository的单元测试
  - [ ] 实现SequelizeRoleRepository的单元测试
  - [ ] 实现SequelizePermissionRepository的单元测试
- [ ] 编写应用服务单元测试
  - [ ] 实现UserApplicationService的单元测试
  - [ ] 实现AuthenticationApplicationService的单元测试
- [ ] 编写控制器集成测试
  - [ ] 实现UserController的集成测试
  - [ ] 实现AuthController的集成测试
- [ ] 编写端到端测试
  - [ ] 实现用户管理的端到端测试
  - [ ] 实现认证功能的端到端测试
- [ ] 运行测试并修复问题
