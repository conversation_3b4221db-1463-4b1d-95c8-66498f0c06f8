# 领域事件机制实施方案

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 进行中 |
| 创建日期 | 2025-05-16 |
| 最后更新 | 2025-05-16 |
| 作者 | AIBUBB技术团队 |

## 目录

1. [背景与目标](#1-背景与目标)
2. [领域事件概述](#2-领域事件概述)
3. [事件架构设计](#3-事件架构设计)
4. [事件实现方案](#4-事件实现方案)
5. [事件处理器](#5-事件处理器)
6. [事件存储与重放](#6-事件存储与重放)
7. [实施步骤](#7-实施步骤)
8. [时间规划](#8-时间规划)

## 1. 背景与目标

随着AIBUBB系统的不断发展和领域驱动设计的深入应用，领域事件变得越来越重要。本方案旨在实现完整的领域事件机制，支持聚合之间的通信，实现系统的松耦合和可扩展性。

### 1.1 主要目标

- 设计和实现领域事件架构
- 实现事件发布和订阅机制
- 实现事件存储和重放功能
- 设计和实现事件处理器
- 支持跨聚合的业务流程

## 2. 领域事件概述

### 2.1 什么是领域事件

领域事件是领域中发生的事情，通常是过去时态的动词，如"用户注册完成"、"订单已创建"等。领域事件是领域驱动设计中的重要概念，用于在不同聚合之间进行通信，实现系统的松耦合和可扩展性。

### 2.2 领域事件的特点

- **不可变**：领域事件一旦创建，就不应该被修改
- **自包含**：领域事件应该包含足够的信息，使接收者能够理解事件的含义
- **有时间戳**：领域事件应该记录发生的时间
- **有唯一标识**：领域事件应该有唯一标识，便于跟踪和调试
- **表示过去发生的事情**：领域事件通常使用过去时态命名

### 2.3 领域事件的作用

- **聚合之间的通信**：通过领域事件，不同的聚合可以在不直接依赖的情况下进行通信
- **解耦系统**：领域事件可以帮助解耦系统，使系统更加灵活和可扩展
- **实现事件溯源**：领域事件可以用于实现事件溯源，记录系统的状态变化
- **支持复杂业务流程**：领域事件可以用于支持跨多个聚合的复杂业务流程
- **实现系统集成**：领域事件可以用于与外部系统集成

## 3. 事件架构设计

### 3.1 事件模型

#### 3.1.1 领域事件接口

```typescript
// domain/events/DomainEvent.ts
export interface DomainEvent {
  readonly eventId: string;
  readonly occurredOn: Date;
}
```

#### 3.1.2 基础领域事件

```typescript
// domain/events/DomainEventBase.ts
export abstract class DomainEventBase implements DomainEvent {
  readonly eventId: string;
  readonly occurredOn: Date;

  constructor() {
    this.eventId = uuidv4();
    this.occurredOn = new Date();
  }
}
```

#### 3.1.3 具体领域事件

```typescript
// domain/events/content/theme/ThemeCreatedEvent.ts
export class ThemeCreatedEvent extends DomainEventBase {
  constructor(public readonly theme: Theme) {
    super();
  }
}
```

### 3.2 事件发布者

#### 3.2.1 事件发布者接口

```typescript
// domain/events/EventPublisher.ts
export interface EventPublisher {
  publish(event: DomainEvent): Promise<void>;
  publishAll(events: DomainEvent[]): Promise<void>;
}
```

#### 3.2.2 聚合根基类

```typescript
// domain/models/AggregateRootBase.ts
export abstract class AggregateRootBase implements AggregateRoot {
  private _domainEvents: DomainEvent[] = [];

  constructor(public readonly id: number) {}

  get domainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  clearEvents(): void {
    this._domainEvents = [];
  }

  addDomainEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }
}
```

### 3.3 事件订阅者

#### 3.3.1 事件处理器接口

```typescript
// domain/events/EventHandler.ts
export interface EventHandler<T extends DomainEvent> {
  handle(event: T): Promise<void>;
}
```

#### 3.3.2 事件总线接口

```typescript
// domain/events/EventBus.ts
export interface EventBus {
  subscribe<T extends DomainEvent>(eventType: Constructor<T>, handler: EventHandler<T>): void;
  unsubscribe<T extends DomainEvent>(eventType: Constructor<T>, handler: EventHandler<T>): void;
  publish(event: DomainEvent): Promise<void>;
}
```

## 4. 事件实现方案

### 4.1 内存事件总线

```typescript
// infrastructure/events/InMemoryEventBus.ts
export class InMemoryEventBus implements EventBus {
  private handlers: Map<string, EventHandler<any>[]> = new Map();

  subscribe<T extends DomainEvent>(eventType: Constructor<T>, handler: EventHandler<T>): void {
    const eventName = eventType.name;
    const handlers = this.handlers.get(eventName) || [];
    handlers.push(handler);
    this.handlers.set(eventName, handlers);
  }

  unsubscribe<T extends DomainEvent>(eventType: Constructor<T>, handler: EventHandler<T>): void {
    const eventName = eventType.name;
    const handlers = this.handlers.get(eventName) || [];
    const index = handlers.indexOf(handler);
    if (index !== -1) {
      handlers.splice(index, 1);
      this.handlers.set(eventName, handlers);
    }
  }

  async publish(event: DomainEvent): Promise<void> {
    const eventName = event.constructor.name;
    const handlers = this.handlers.get(eventName) || [];
    
    for (const handler of handlers) {
      try {
        await handler.handle(event);
      } catch (error) {
        console.error(`Error handling event ${eventName}:`, error);
      }
    }
  }
}
```

### 4.2 事件发布者实现

```typescript
// infrastructure/events/DomainEventPublisher.ts
export class DomainEventPublisher implements EventPublisher {
  constructor(
    private readonly eventBus: EventBus,
    private readonly eventStore: EventStore
  ) {}

  async publish(event: DomainEvent): Promise<void> {
    await this.eventStore.store(event);
    await this.eventBus.publish(event);
  }

  async publishAll(events: DomainEvent[]): Promise<void> {
    for (const event of events) {
      await this.publish(event);
    }
  }
}
```

### 4.3 工作单元集成

```typescript
// infrastructure/persistence/UnitOfWorkImpl.ts
export class UnitOfWorkImpl implements UnitOfWork {
  private transaction: Transaction | null = null;

  constructor(
    private readonly sequelize: Sequelize,
    private readonly eventPublisher: EventPublisher
  ) {}

  async begin(): Promise<void> {
    this.transaction = await this.sequelize.transaction();
  }

  async commit(): Promise<void> {
    if (!this.transaction) {
      throw new Error('No active transaction');
    }

    await this.transaction.commit();
    this.transaction = null;
  }

  async rollback(): Promise<void> {
    if (!this.transaction) {
      throw new Error('No active transaction');
    }

    await this.transaction.rollback();
    this.transaction = null;
  }

  getTransaction(): Transaction | null {
    return this.transaction;
  }

  async publishEvents(aggregateRoot: AggregateRoot): Promise<void> {
    const events = aggregateRoot.domainEvents;
    if (events.length > 0) {
      await this.eventPublisher.publishAll(events);
      aggregateRoot.clearEvents();
    }
  }
}
```

## 5. 事件处理器

### 5.1 事件处理器设计

事件处理器负责处理特定类型的领域事件，执行相应的业务逻辑。事件处理器应该是无状态的，可以并行执行。

### 5.2 事件处理器示例

```typescript
// application/events/content/theme/ThemeCreatedEventHandler.ts
@injectable()
export class ThemeCreatedEventHandler implements EventHandler<ThemeCreatedEvent> {
  constructor(
    @inject('LearningTemplateRepository') private readonly learningTemplateRepository: LearningTemplateRepository
  ) {}

  async handle(event: ThemeCreatedEvent): Promise<void> {
    // 创建默认学习模板
    const theme = event.theme;
    const learningTemplate = new LearningTemplate(
      0,
      theme.id,
      `${theme.name}学习模板`,
      `基于${theme.name}主题的默认学习模板`,
      true,
      []
    );

    await this.learningTemplateRepository.save(learningTemplate);
  }
}
```

### 5.3 事件处理器注册

```typescript
// infrastructure/events/EventHandlerRegistry.ts
export class EventHandlerRegistry {
  constructor(
    private readonly eventBus: EventBus,
    private readonly container: Container
  ) {}

  registerHandlers(): void {
    // 注册主题相关事件处理器
    this.register(ThemeCreatedEvent, 'ThemeCreatedEventHandler');
    this.register(ThemeUpdatedEvent, 'ThemeUpdatedEventHandler');
    this.register(ThemeDeletedEvent, 'ThemeDeletedEventHandler');

    // 注册学习计划相关事件处理器
    this.register(LearningPlanCreatedEvent, 'LearningPlanCreatedEventHandler');
    this.register(LearningPlanStartedEvent, 'LearningPlanStartedEventHandler');
    this.register(LearningPlanCompletedEvent, 'LearningPlanCompletedEventHandler');

    // 注册其他事件处理器...
  }

  private register<T extends DomainEvent>(eventType: Constructor<T>, handlerName: string): void {
    const handler = this.container.get<EventHandler<T>>(handlerName);
    this.eventBus.subscribe(eventType, handler);
  }
}
```

## 6. 事件存储与重放

### 6.1 事件存储

#### 6.1.1 事件存储接口

```typescript
// domain/events/EventStore.ts
export interface EventStore {
  store(event: DomainEvent): Promise<void>;
  getEvents(aggregateId: number, aggregateType: string): Promise<DomainEvent[]>;
  getAllEvents(): Promise<DomainEvent[]>;
}
```

#### 6.1.2 数据库事件存储

```typescript
// infrastructure/events/DatabaseEventStore.ts
export class DatabaseEventStore implements EventStore {
  constructor(
    @inject('EventModel') private readonly eventModel: any,
    @inject('UnitOfWork') private readonly unitOfWork: UnitOfWork
  ) {}

  async store(event: DomainEvent): Promise<void> {
    const eventData = {
      event_id: event.eventId,
      event_type: event.constructor.name,
      occurred_on: event.occurredOn,
      payload: JSON.stringify(event),
      aggregate_id: this.getAggregateId(event),
      aggregate_type: this.getAggregateType(event)
    };

    await this.eventModel.create(eventData, {
      transaction: this.unitOfWork.getTransaction()
    });
  }

  async getEvents(aggregateId: number, aggregateType: string): Promise<DomainEvent[]> {
    const events = await this.eventModel.findAll({
      where: {
        aggregate_id: aggregateId,
        aggregate_type: aggregateType
      },
      order: [['occurred_on', 'ASC']],
      transaction: this.unitOfWork.getTransaction()
    });

    return events.map(event => this.deserializeEvent(event));
  }

  async getAllEvents(): Promise<DomainEvent[]> {
    const events = await this.eventModel.findAll({
      order: [['occurred_on', 'ASC']],
      transaction: this.unitOfWork.getTransaction()
    });

    return events.map(event => this.deserializeEvent(event));
  }

  private getAggregateId(event: DomainEvent): number | null {
    // 从事件中提取聚合ID
    const anyEvent = event as any;
    if (anyEvent.theme) return anyEvent.theme.id;
    if (anyEvent.learningPlan) return anyEvent.learningPlan.id;
    if (anyEvent.exercise) return anyEvent.exercise.id;
    if (anyEvent.themeId) return anyEvent.themeId;
    return null;
  }

  private getAggregateType(event: DomainEvent): string {
    // 从事件中提取聚合类型
    const eventName = event.constructor.name;
    if (eventName.startsWith('Theme')) return 'Theme';
    if (eventName.startsWith('LearningPlan')) return 'LearningPlan';
    if (eventName.startsWith('Exercise')) return 'Exercise';
    return 'Unknown';
  }

  private deserializeEvent(eventRecord: any): DomainEvent {
    const payload = JSON.parse(eventRecord.payload);
    // 根据事件类型反序列化事件
    // 这里需要一个事件类型注册表
    return payload;
  }
}
```

### 6.2 事件重放

```typescript
// infrastructure/events/EventReplayer.ts
export class EventReplayer {
  constructor(
    private readonly eventStore: EventStore,
    private readonly eventBus: EventBus
  ) {}

  async replayAll(): Promise<void> {
    const events = await this.eventStore.getAllEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  async replayForAggregate(aggregateId: number, aggregateType: string): Promise<void> {
    const events = await this.eventStore.getEvents(aggregateId, aggregateType);
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }
}
```

## 7. 实施步骤

### 7.1 准备阶段（1天）

- 创建事件相关的数据库表
- 设计事件模型和接口
- 准备测试环境

### 7.2 基础设施实现（3天）

- 实现事件总线
- 实现事件存储
- 实现事件发布者
- 集成工作单元

### 7.3 事件处理器实现（3天）

- 实现事件处理器接口
- 实现具体事件处理器
- 实现事件处理器注册

### 7.4 事件重放实现（2天）

- 实现事件重放功能
- 实现事件序列化和反序列化
- 实现事件重放命令

### 7.5 集成测试（2天）

- 编写集成测试
- 验证事件发布和订阅
- 验证事件存储和重放
- 验证跨聚合业务流程

## 8. 时间规划

| 阶段 | 时间 | 负责人 |
|------|------|-------|
| 准备阶段 | 2025-05-16 | 后端团队 |
| 基础设施实现 | 2025-05-17 ~ 2025-05-19 | 后端团队 |
| 事件处理器实现 | 2025-05-20 ~ 2025-05-22 | 后端团队 |
| 事件重放实现 | 2025-05-23 ~ 2025-05-24 | 后端团队 |
| 集成测试 | 2025-05-25 ~ 2025-05-26 | 测试团队 |
| 完成 | 2025-05-27 | - |
