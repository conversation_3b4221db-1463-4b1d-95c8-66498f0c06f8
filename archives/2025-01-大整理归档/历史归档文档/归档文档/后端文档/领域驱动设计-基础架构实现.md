# AIBUBB领域驱动设计 - 基础架构实现

## 一、概述

本文档详细描述了AIBUBB系统领域驱动设计(DDD)基础架构的实现方案，作为架构调整的第一阶段。基础架构实现是整个DDD架构调整的基础，为后续的领域模型实现和业务逻辑迁移提供支持。

## 二、目录结构设计

为了支持DDD架构，我们需要重新组织项目的目录结构，明确各层的职责和边界。

### 2.1 新的目录结构

```
backend/
├── application/              # 应用层
│   ├── commands/            # 命令对象
│   ├── dtos/                # 数据传输对象
│   ├── queries/             # 查询对象
│   └── services/            # 应用服务
├── domain/                  # 领域层
│   ├── events/              # 领域事件
│   ├── models/              # 领域模型
│   ├── repositories/        # 仓库接口
│   └── services/            # 领域服务
├── infrastructure/          # 基础设施层
│   ├── config/              # 配置
│   ├── events/              # 事件实现
│   ├── persistence/         # 持久化
│   │   └── repositories/    # 仓库实现
│   └── services/            # 基础设施服务
├── interfaces/              # 接口层
│   ├── api/                 # API接口
│   │   ├── controllers/     # 控制器
│   │   ├── middlewares/     # 中间件
│   │   ├── routes/          # 路由
│   │   └── validators/      # 请求验证器
│   └── events/              # 事件处理器
├── utils/                   # 工具类
└── server.js                # 服务器入口
```

### 2.2 与现有结构的映射关系

| 现有结构 | 新结构 | 说明 |
|---------|--------|------|
| `controllers/` | `interfaces/api/controllers/` | 控制器移至接口层 |
| `routes/` | `interfaces/api/routes/` | 路由移至接口层 |
| `middlewares/` | `interfaces/api/middlewares/` | 中间件移至接口层 |
| `services/` | `application/services/` | 应用服务移至应用层 |
| `repositories/` | `infrastructure/persistence/repositories/` | 仓库实现移至基础设施层 |
| `models/` | `domain/models/` | 领域模型移至领域层 |
| `config/` | `infrastructure/config/` | 配置移至基础设施层 |
| `utils/` | `utils/` | 工具类保持不变 |

## 三、核心接口与抽象类设计

### 3.1 领域层核心接口

#### 3.1.1 实体接口

```typescript
// domain/models/Entity.ts
export interface Entity<T> {
  id: T;
  equals(entity: Entity<T>): boolean;
}
```

#### 3.1.2 值对象接口

```typescript
// domain/models/ValueObject.ts
export interface ValueObject {
  equals(valueObject: ValueObject): boolean;
}
```

#### 3.1.3 聚合根接口

```typescript
// domain/models/AggregateRoot.ts
import { Entity } from './Entity';
import { DomainEvent } from '../events/DomainEvent';

export interface AggregateRoot<T> extends Entity<T> {
  domainEvents: DomainEvent[];
  clearEvents(): void;
  addEvent(event: DomainEvent): void;
}
```

#### 3.1.4 仓库接口

```typescript
// domain/repositories/Repository.ts
import { AggregateRoot } from '../models/AggregateRoot';

export interface Repository<T, ID> {
  findById(id: ID): Promise<T | null>;
  findAll(): Promise<T[]>;
  save(entity: T): Promise<T>;
  delete(entity: T): Promise<void>;
}
```

#### 3.1.5 领域事件接口

```typescript
// domain/events/DomainEvent.ts
export interface DomainEvent {
  eventId: string;
  eventType: string;
  occurredOn: Date;
  aggregateId: string;
  aggregateType: string;
  version: number;
  payload: any;
}
```

### 3.2 应用层核心接口

#### 3.2.1 命令接口

```typescript
// application/commands/Command.ts
export interface Command {
  commandId: string;
  commandType: string;
}
```

#### 3.2.2 查询接口

```typescript
// application/queries/Query.ts
export interface Query {
  queryId: string;
  queryType: string;
}
```

#### 3.2.3 应用服务接口

```typescript
// application/services/ApplicationService.ts
import { Command } from '../commands/Command';
import { Query } from '../queries/Query';

export interface ApplicationService<C extends Command, Q extends Query, R> {
  execute(command: C): Promise<void>;
  query(query: Q): Promise<R>;
}
```

### 3.3 基础设施层核心接口

#### 3.3.1 单元工作接口

```typescript
// infrastructure/persistence/UnitOfWork.ts
export interface UnitOfWork {
  begin(): Promise<void>;
  commit(): Promise<void>;
  rollback(): Promise<void>;
  runInTransaction<T>(work: () => Promise<T>): Promise<T>;
}
```

#### 3.3.2 事件发布者接口

```typescript
// infrastructure/events/EventPublisher.ts
import { DomainEvent } from '../../domain/events/DomainEvent';

export interface EventPublisher {
  publish(event: DomainEvent): Promise<void>;
  publishAll(events: DomainEvent[]): Promise<void>;
}
```

## 四、核心抽象类实现

### 4.1 领域层抽象类

#### 4.1.1 实体抽象类

```typescript
// domain/models/EntityBase.ts
import { Entity } from './Entity';

export abstract class EntityBase<T> implements Entity<T> {
  id: T;

  constructor(id: T) {
    this.id = id;
  }

  equals(entity: Entity<T>): boolean {
    if (entity === this) return true;
    if (entity === null || entity === undefined) return false;
    if (!(entity instanceof EntityBase)) return false;
    return this.id === entity.id;
  }
}
```

#### 4.1.2 聚合根抽象类

```typescript
// domain/models/AggregateRootBase.ts
import { EntityBase } from './EntityBase';
import { AggregateRoot } from './AggregateRoot';
import { DomainEvent } from '../events/DomainEvent';

export abstract class AggregateRootBase<T> extends EntityBase<T> implements AggregateRoot<T> {
  private _domainEvents: DomainEvent[] = [];

  get domainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  clearEvents(): void {
    this._domainEvents = [];
  }

  addEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }
}
```

### 4.2 基础设施层抽象类

#### 4.2.1 仓库抽象类

```typescript
// infrastructure/persistence/repositories/RepositoryBase.ts
import { Repository } from '../../../domain/repositories/Repository';
import { AggregateRoot } from '../../../domain/models/AggregateRoot';
import { UnitOfWork } from '../UnitOfWork';
import { EventPublisher } from '../../events/EventPublisher';

export abstract class RepositoryBase<T extends AggregateRoot<ID>, ID> implements Repository<T, ID> {
  constructor(
    protected readonly unitOfWork: UnitOfWork,
    protected readonly eventPublisher: EventPublisher
  ) {}

  abstract findById(id: ID): Promise<T | null>;
  abstract findAll(): Promise<T[]>;

  async save(entity: T): Promise<T> {
    const savedEntity = await this.doSave(entity);
    await this.eventPublisher.publishAll(entity.domainEvents);
    entity.clearEvents();
    return savedEntity;
  }

  async delete(entity: T): Promise<void> {
    await this.doDelete(entity);
    await this.eventPublisher.publishAll(entity.domainEvents);
    entity.clearEvents();
  }

  protected abstract doSave(entity: T): Promise<T>;
  protected abstract doDelete(entity: T): Promise<void>;
}
```

## 五、依赖注入容器实现

为了支持DDD架构，我们需要一个更强大的依赖注入容器，支持接口和实现的绑定，以及生命周期管理。

### 5.1 依赖注入容器接口

```typescript
// infrastructure/config/Container.ts
export interface Container {
  bind<T>(token: string | symbol, factory: (container: Container) => T, options?: BindOptions): void;
  bindClass<T>(token: string | symbol, constructor: new (...args: any[]) => T, options?: BindOptions): void;
  bindInterface<T>(token: string | symbol, implementation: string | symbol, options?: BindOptions): void;
  get<T>(token: string | symbol): T;
  resolve<T>(constructor: new (...args: any[]) => T): T;
}

export interface BindOptions {
  singleton?: boolean;
  eager?: boolean;
}
```

### 5.2 依赖注入容器实现

```typescript
// infrastructure/config/ContainerImpl.ts
import { Container, BindOptions } from './Container';

type Factory<T> = (container: Container) => T;
type Registration<T> = {
  factory: Factory<T>;
  singleton: boolean;
  instance?: T;
};

export class ContainerImpl implements Container {
  private registrations = new Map<string | symbol, Registration<any>>();

  bind<T>(token: string | symbol, factory: Factory<T>, options: BindOptions = {}): void {
    const registration: Registration<T> = {
      factory,
      singleton: options.singleton !== false
    };

    this.registrations.set(token, registration);

    if (options.eager && options.singleton) {
      this.get(token);
    }
  }

  bindClass<T>(token: string | symbol, constructor: new (...args: any[]) => T, options: BindOptions = {}): void {
    this.bind(token, (container) => this.resolve(constructor), options);
  }

  bindInterface<T>(token: string | symbol, implementation: string | symbol, options: BindOptions = {}): void {
    this.bind(token, (container) => container.get(implementation), options);
  }

  get<T>(token: string | symbol): T {
    const registration = this.registrations.get(token);
    if (!registration) {
      throw new Error(`No registration found for token: ${String(token)}`);
    }

    if (registration.singleton) {
      if (!registration.instance) {
        registration.instance = registration.factory(this);
      }
      return registration.instance;
    }

    return registration.factory(this);
  }

  resolve<T>(constructor: new (...args: any[]) => T): T {
    const paramTypes = Reflect.getMetadata('design:paramtypes', constructor) || [];
    const params = paramTypes.map((paramType: any) => this.get(paramType));
    return new constructor(...params);
  }
}
```

## 六、单元工作实现

单元工作(Unit of Work)是DDD架构中的重要概念，用于管理事务和确保聚合的一致性。

### 6.1 Sequelize单元工作实现

```typescript
// infrastructure/persistence/SequelizeUnitOfWork.ts
import { UnitOfWork } from './UnitOfWork';
import { Sequelize, Transaction } from 'sequelize';

export class SequelizeUnitOfWork implements UnitOfWork {
  private transaction: Transaction | null = null;

  constructor(private readonly sequelize: Sequelize) {}

  async begin(): Promise<void> {
    if (this.transaction) {
      throw new Error('Transaction already started');
    }
    this.transaction = await this.sequelize.transaction();
  }

  async commit(): Promise<void> {
    if (!this.transaction) {
      throw new Error('No transaction to commit');
    }
    await this.transaction.commit();
    this.transaction = null;
  }

  async rollback(): Promise<void> {
    if (!this.transaction) {
      throw new Error('No transaction to rollback');
    }
    await this.transaction.rollback();
    this.transaction = null;
  }

  async runInTransaction<T>(work: () => Promise<T>): Promise<T> {
    const isOuterTransaction = !this.transaction;
    
    if (isOuterTransaction) {
      await this.begin();
    }

    try {
      const result = await work();
      
      if (isOuterTransaction) {
        await this.commit();
      }
      
      return result;
    } catch (error) {
      if (isOuterTransaction && this.transaction) {
        await this.rollback();
      }
      throw error;
    }
  }

  getTransaction(): Transaction | null {
    return this.transaction;
  }
}
```

## 七、领域事件机制实现

领域事件是DDD架构中的重要概念，用于捕捉业务中的重要变化，支持松耦合的系统集成。

### 7.1 领域事件基类

```typescript
// domain/events/DomainEventBase.ts
import { DomainEvent } from './DomainEvent';
import { v4 as uuidv4 } from 'uuid';

export abstract class DomainEventBase implements DomainEvent {
  readonly eventId: string;
  readonly occurredOn: Date;
  readonly version: number;

  constructor(
    readonly eventType: string,
    readonly aggregateId: string,
    readonly aggregateType: string,
    readonly payload: any
  ) {
    this.eventId = uuidv4();
    this.occurredOn = new Date();
    this.version = 1;
  }
}
```

### 7.2 事件发布者实现

```typescript
// infrastructure/events/EventPublisherImpl.ts
import { EventPublisher } from './EventPublisher';
import { DomainEvent } from '../../domain/events/DomainEvent';
import { EventBus } from './EventBus';

export class EventPublisherImpl implements EventPublisher {
  constructor(private readonly eventBus: EventBus) {}

  async publish(event: DomainEvent): Promise<void> {
    await this.eventBus.publish(event.eventType, event);
  }

  async publishAll(events: DomainEvent[]): Promise<void> {
    for (const event of events) {
      await this.publish(event);
    }
  }
}
```

### 7.3 事件总线实现

```typescript
// infrastructure/events/EventBusImpl.ts
import { EventBus } from './EventBus';
import { EventHandler } from './EventHandler';

export class EventBusImpl implements EventBus {
  private handlers: Map<string, EventHandler[]> = new Map();

  subscribe(eventType: string, handler: EventHandler): void {
    const handlers = this.handlers.get(eventType) || [];
    handlers.push(handler);
    this.handlers.set(eventType, handlers);
  }

  async publish(eventType: string, event: any): Promise<void> {
    const handlers = this.handlers.get(eventType) || [];
    
    for (const handler of handlers) {
      try {
        await handler.handle(event);
      } catch (error) {
        console.error(`Error handling event ${eventType}:`, error);
      }
    }
  }
}
```

## 八、实施步骤

### 8.1 准备工作

1. **安装必要的依赖**
   ```bash
   npm install reflect-metadata uuid
   ```

2. **配置TypeScript**
   ```json
   // tsconfig.json
   {
     "compilerOptions": {
       "experimentalDecorators": true,
       "emitDecoratorMetadata": true,
       // 其他配置...
     }
   }
   ```

### 8.2 实施顺序

1. **创建目录结构**
   - 创建新的目录结构，保留现有文件
   - 在新目录中创建空的`.gitkeep`文件，确保目录被Git跟踪

2. **实现核心接口和抽象类**
   - 实现领域层核心接口和抽象类
   - 实现应用层核心接口
   - 实现基础设施层核心接口和抽象类

3. **实现依赖注入容器**
   - 实现Container接口和ContainerImpl类
   - 创建容器配置文件

4. **实现单元工作**
   - 实现UnitOfWork接口和SequelizeUnitOfWork类
   - 将单元工作集成到仓库中

5. **实现领域事件机制**
   - 实现DomainEvent接口和DomainEventBase类
   - 实现EventPublisher接口和EventPublisherImpl类
   - 实现EventBus接口和EventBusImpl类

### 8.3 验证步骤

1. **创建简单的测试用例**
   - 创建一个简单的领域模型和仓库
   - 使用依赖注入容器注册和解析
   - 测试单元工作和事件发布

2. **编写单元测试**
   - 为核心接口和抽象类编写单元测试
   - 为依赖注入容器编写单元测试
   - 为单元工作编写单元测试
   - 为领域事件机制编写单元测试

## 九、下一步计划

完成基础架构实现后，我们将进入架构调整的第二阶段：核心领域迁移。我们将从标签领域开始，实现完整的DDD架构，包括：

1. **实现标签领域模型**
   - Tag实体
   - TagCategory实体
   - TagSynonym实体
   - TagFeedback实体

2. **实现标签领域服务**
   - TagDomainService

3. **实现标签仓库接口和实现**
   - TagRepository接口
   - SequelizeTagRepository实现

4. **实现标签应用服务**
   - TagApplicationService

5. **实现标签控制器**
   - TagController
