# 授权逻辑迁移计划

## 1. 概述

本文档提供了将现有控制器中的授权逻辑迁移到统一授权中间件的计划。迁移的目标是集中化授权逻辑，提高可维护性和安全性。

## 2. 迁移步骤

### 2.1 准备工作

1. 确保已安装统一授权中间件
2. 确保已配置授权规则
3. 确保已实现资源所有者检查函数和公开资源检查函数

### 2.2 识别控制器中的授权逻辑

1. 识别所有控制器中的授权检查逻辑
2. 分析授权检查的类型（如管理员检查、资源所有者检查、权限检查等）
3. 记录每个控制器方法的授权需求

### 2.3 更新路由配置

1. 更新路由配置，使用统一授权中间件
2. 移除控制器中的授权检查逻辑
3. 测试新的授权逻辑

## 3. 迁移示例

### 3.1 笔记控制器迁移

#### 3.1.1 原始代码

```javascript
// 获取笔记详情
const getNoteById = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用笔记服务获取笔记详情
    const note = await noteService.getNoteDetails(id);

    if (!note) {
      return handleNotFoundError(res, '笔记不存在');
    }

    // 验证笔记是否属于当前用户或者是公开的
    const isOwner = note.user_id === userId;
    const isPlanOwner = note.tag && note.tag.learningPlan && note.tag.learningPlan.user_id === userId;

    if (!isOwner && !isPlanOwner && note.status !== 'published') {
      return handleForbiddenError(res, '无权访问该笔记');
    }

    // 返回笔记详情
    return apiResponse.success(res, {
      // 笔记详情
    });
  } catch (error) {
    return handleApiError(error, res, 'getNoteById');
  }
};
```

#### 3.1.2 迁移后的代码

路由配置：

```javascript
// 获取笔记详情
router.get(
  '/notes/:id',
  authMiddleware,
  authorize('note', 'read'),
  noteController.getNoteById
);
```

控制器代码：

```javascript
// 获取笔记详情
const getNoteById = async (req, res) => {
  try {
    const { id } = req.params;

    // 使用笔记服务获取笔记详情
    const note = await noteService.getNoteDetails(id);

    if (!note) {
      return handleNotFoundError(res, '笔记不存在');
    }

    // 授权检查已经由中间件处理，无需在控制器中重复检查

    // 返回笔记详情
    return apiResponse.success(res, {
      // 笔记详情
    });
  } catch (error) {
    return handleApiError(error, res, 'getNoteById');
  }
};
```

### 3.2 用户控制器迁移

#### 3.2.1 原始代码

```javascript
// 软删除用户
const softDeleteUser = async (req, res) => {
  try {
    // 检查是否是管理员
    if (!req.user.isAdmin) {
      return apiResponse.forbidden(res, '需要管理员权限');
    }

    const { id } = req.params;

    // 使用用户服务软删除用户
    await userService.softDeleteUser(id);

    return apiResponse.success(res, {
      message: '用户已被软删除'
    });
  } catch (error) {
    return handleApiError(error, res, 'softDeleteUser');
  }
};
```

#### 3.2.2 迁移后的代码

路由配置：

```javascript
// 软删除用户
router.delete(
  '/users/:id/soft-delete',
  authMiddleware,
  authorize('user', 'delete'),
  userController.softDeleteUser
);
```

控制器代码：

```javascript
// 软删除用户
const softDeleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 使用用户服务软删除用户
    await userService.softDeleteUser(id);

    return apiResponse.success(res, {
      message: '用户已被软删除'
    });
  } catch (error) {
    return handleApiError(error, res, 'softDeleteUser');
  }
};
```

## 4. 迁移优先级

按照以下优先级顺序进行迁移：

1. **高优先级**：
   - 用户控制器（UserController）
   - 认证控制器（AuthController）
   - 管理员控制器（AdminController）

2. **中优先级**：
   - 笔记控制器（NoteController）
   - 标签控制器（TagController）
   - 学习计划控制器（LearningPlanController）

3. **低优先级**：
   - 评论控制器（CommentController）
   - 点赞控制器（LikeController）
   - 其他控制器

## 5. 测试计划

### 5.1 单元测试

1. 为统一授权中间件编写单元测试
2. 测试不同的授权规则和场景
3. 测试资源所有者检查和公开资源检查

### 5.2 集成测试

1. 测试迁移后的路由和控制器
2. 测试不同角色和权限的用户
3. 测试边界情况和错误处理

### 5.3 手动测试

1. 使用Postman或其他API测试工具测试API端点
2. 测试不同角色和权限的用户
3. 测试授权失败的情况

## 6. 回滚计划

如果迁移过程中出现问题，可以按照以下步骤回滚：

1. 恢复原始的路由配置
2. 恢复控制器中的授权检查逻辑
3. 禁用统一授权中间件

## 7. 迁移时间表

| 阶段 | 任务 | 预计时间 |
|------|------|---------|
| 准备 | 安装和配置统一授权中间件 | 1天 |
| 高优先级 | 迁移用户、认证和管理员控制器 | 2天 |
| 中优先级 | 迁移笔记、标签和学习计划控制器 | 2天 |
| 低优先级 | 迁移其他控制器 | 1天 |
| 测试 | 单元测试、集成测试和手动测试 | 2天 |
| 总计 | | 8天 |

## 8. 结论

通过将授权逻辑从控制器迁移到统一授权中间件，我们可以提高代码的可维护性和安全性。迁移过程需要谨慎进行，确保不影响现有功能。按照本计划进行迁移，可以平稳地完成授权逻辑的集中化。
