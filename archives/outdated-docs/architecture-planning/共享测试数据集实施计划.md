# 共享测试数据集实施计划

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-06-15 |
| 最后更新 | 2025-06-15 |
| 作者 | AIBUBB技术团队 |

## 1. 背景与目标

当前前后端团队使用不同的测试数据，导致集成测试中出现不一致性，影响开发效率。本计划旨在创建共享的测试数据集，确保前后端团队可以使用相同的测试数据，提高测试效率和准确性。

### 1.1 主要目标

- 开发共享的测试数据生成工具
- 建立标准测试场景库
- 确保前后端团队可以使用相同的测试数据
- 减少集成测试中的不一致性
- 提高测试效率

## 2. 当前状态分析

### 2.1 现有测试数据生成

- 已有初步的测试数据生成脚本(`backend/scripts/generate-test-data.js`)
- 脚本可以生成用户、标签分类、标签、主题和学习计划等基本数据
- 数据以JSON格式保存在`backend/test/contract-test-data`目录

### 2.2 存在的问题

- 缺乏标准测试场景库
- 数据量不足，无法满足性能测试需求
- 缺乏前后端共享机制
- 数据之间的关联关系不完整
- 缺乏版本控制和更新机制

## 3. 实施方案

### 3.1 扩展测试数据生成工具

#### 3.1.1 增加实体类型支持

扩展现有的`generate-test-data.js`脚本，增加以下实体类型的数据生成：

- 练习(Exercise)
- 观点(Insight)
- 笔记(Note)
- 学习模板(LearningTemplate)
- 用户设置(UserSetting)
- 标签同义词(TagSynonym)
- 成就(Achievement)
- 徽章(Badge)
- 等级(Level)

#### 3.1.2 添加数据量配置选项

增加命令行选项，允许用户指定生成的数据量：

```javascript
program
  .option('--env <env>', '环境 (test, development)', 'test')
  .option('--output-dir <dir>', '输出目录', 'backend/test/contract-test-data')
  .option('--verbose', '显示详细日志', false)
  .option('--scale <scale>', '数据量级 (small, medium, large)', 'medium')
  .option('--entity <entity>', '指定生成的实体类型 (all, users, tags, ...)', 'all')
  .parse(process.argv);
```

数据量级定义：
- small: 每种实体类型5-10条记录
- medium: 每种实体类型20-50条记录
- large: 每种实体类型100-500条记录

#### 3.1.3 支持SQL脚本导出

增加SQL脚本导出功能，方便数据库导入：

```javascript
function generateSQLScript(data) {
  let sql = '';
  
  // 生成用户SQL
  sql += 'INSERT INTO users (id, username, email, role, created_at, updated_at) VALUES\n';
  data.users.forEach((user, index) => {
    sql += `(${user.id}, '${user.username}', '${user.email}', '${user.role}', '${user.createdAt}', '${user.updatedAt}')`;
    sql += index < data.users.length - 1 ? ',\n' : ';\n\n';
  });
  
  // 生成其他实体的SQL
  // ...
  
  return sql;
}
```

### 3.2 建立标准测试场景库

#### 3.2.1 定义标准测试场景

创建以下标准测试场景：

1. **基础数据场景**：包含基本的用户、主题、标签等数据，用于基本功能测试
2. **学习计划场景**：包含完整的学习计划、练习、笔记等数据，用于学习计划相关功能测试
3. **用户互动场景**：包含用户互动数据，如评论、点赞等，用于社交功能测试
4. **游戏化场景**：包含成就、徽章、等级等数据，用于游戏化功能测试
5. **性能测试场景**：包含大量数据，用于性能测试

#### 3.2.2 实现场景生成器

创建场景生成器，根据场景定义生成相应的测试数据：

```javascript
const scenarioGenerators = {
  // 基础数据场景
  basic: async (scale) => {
    const users = generateUsers(scale === 'small' ? 5 : scale === 'medium' ? 20 : 100);
    const themes = generateThemes(scale === 'small' ? 3 : scale === 'medium' ? 10 : 50);
    const tags = generateTags(scale === 'small' ? 10 : scale === 'medium' ? 30 : 150);
    
    return { users, themes, tags };
  },
  
  // 学习计划场景
  learningPlan: async (scale) => {
    const basicData = await scenarioGenerators.basic(scale);
    const learningPlans = generateLearningPlans(basicData.users, basicData.themes);
    const exercises = generateExercises(learningPlans);
    const notes = generateNotes(learningPlans);
    
    return { ...basicData, learningPlans, exercises, notes };
  },
  
  // 其他场景...
};
```

### 3.3 实现前后端共享机制

#### 3.3.1 创建统一的数据访问API

创建`backend/api/test-data.js`，提供测试数据访问API：

```javascript
// GET /api/test-data/scenarios
// 获取可用的测试场景列表
router.get('/scenarios', (req, res) => {
  const scenarios = fs.readdirSync(dataDir)
    .filter(file => file.endsWith('.json') && !file.includes('full-data'));
  
  res.json({
    success: true,
    data: scenarios.map(file => file.replace('.json', ''))
  });
});

// GET /api/test-data/scenarios/:name
// 获取指定测试场景的数据
router.get('/scenarios/:name', (req, res) => {
  const scenarioName = req.params.name;
  const filePath = path.join(dataDir, `${scenarioName}.json`);
  
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'SCENARIO_NOT_FOUND',
        message: `测试场景 ${scenarioName} 不存在`
      }
    });
  }
  
  const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  
  res.json({
    success: true,
    data
  });
});
```

#### 3.3.2 提供数据导入/导出功能

创建数据导入/导出功能，方便前后端团队共享测试数据：

```javascript
// POST /api/test-data/import
// 导入测试数据
router.post('/import', (req, res) => {
  const { data, name } = req.body;
  
  if (!data || !name) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_REQUEST',
        message: '缺少必要参数'
      }
    });
  }
  
  const filePath = path.join(dataDir, `${name}.json`);
  
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  
  res.json({
    success: true,
    message: `测试数据 ${name} 导入成功`
  });
});

// GET /api/test-data/export/:name
// 导出测试数据
router.get('/export/:name', (req, res) => {
  const name = req.params.name;
  const filePath = path.join(dataDir, `${name}.json`);
  
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'DATA_NOT_FOUND',
        message: `测试数据 ${name} 不存在`
      }
    });
  }
  
  res.download(filePath);
});
```

## 4. 实施步骤

### 4.1 阶段一：基础工具开发（3天）

1. 扩展测试数据生成工具
   - 增加实体类型支持
   - 添加数据量配置选项
   - 实现SQL脚本导出功能

2. 创建基本测试场景
   - 实现基础数据场景
   - 实现学习计划场景

### 4.2 阶段二：场景库建设（2天）

1. 完善测试场景库
   - 实现用户互动场景
   - 实现游戏化场景
   - 实现性能测试场景

2. 优化数据关联关系
   - 确保实体之间的关联关系正确
   - 添加数据一致性验证

### 4.3 阶段三：共享机制实现（2天）

1. 创建数据访问API
   - 实现场景列表API
   - 实现场景数据获取API

2. 实现数据导入/导出功能
   - 实现数据导入API
   - 实现数据导出API

### 4.4 阶段四：文档和集成（1天）

1. 编写使用文档
   - 创建工具使用指南
   - 创建API使用指南

2. 集成到CI/CD流程
   - 添加自动生成测试数据的步骤
   - 配置测试数据版本控制

## 5. 验收标准

1. 测试数据生成工具可以生成所有必要的实体类型数据
2. 标准测试场景库包含至少5个不同的测试场景
3. 前后端团队可以通过API访问相同的测试数据
4. 测试数据之间的关联关系正确
5. 文档完整，包含工具使用指南和API使用指南

## 6. 风险与缓解措施

| 风险 | 影响 | 可能性 | 缓解措施 |
|------|------|--------|---------|
| 数据量过大导致性能问题 | 中 | 中 | 实现数据分页和按需加载 |
| 数据结构变更导致工具失效 | 高 | 低 | 实现数据结构版本控制和兼容性检查 |
| 前后端团队使用不同版本的测试数据 | 高 | 中 | 实现数据版本控制和同步机制 |
| 测试数据不符合业务规则 | 中 | 中 | 添加业务规则验证 |

## 7. 后续计划

1. 实现测试数据自动更新机制
2. 添加更多测试场景
3. 实现测试数据可视化工具
4. 集成到测试自动化流程
