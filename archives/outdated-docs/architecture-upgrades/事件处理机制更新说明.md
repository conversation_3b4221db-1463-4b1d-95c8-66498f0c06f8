# 事件处理机制更新说明

## 背景

在AIBUBB系统中，领域事件机制是实现模块间松耦合通信的核心机制。然而，在系统评估过程中，我们发现事件处理机制存在一些问题，特别是在事件发布和订阅方面。本文档旨在说明事件处理机制的最新实现和最佳实践。

## 事件处理机制概述

AIBUBB系统的事件处理机制基于领域驱动设计(DDD)的事件驱动架构，主要包括以下组件：

1. **领域事件(Domain Events)**：表示系统中发生的重要业务事实
2. **事件总线(Event Bus)**：负责事件的发布和订阅
3. **事件发布者(Event Publisher)**：负责将领域事件持久化并发布
4. **事件处理器(Event Handlers)**：负责处理特定类型的事件
5. **事件存储(Event Store)**：负责事件的持久化存储

## 更新内容

### 1. 事件处理器注册机制

我们更新了事件处理器的注册机制，确保所有事件处理器都能正确注册到事件总线：

```typescript
// backend/infrastructure/events/handlers/event-handler-registry.ts
@injectable()
export class EventHandlerRegistry {
  // ...

  public registerHandlers(): void {
    this.logger.info('注册事件处理器...');

    // 注册各种事件处理器
    this.registerHandler('ExerciseCreated', this.exerciseCreatedHandler);
    this.registerHandler('NoteCreated', this.noteCreatedHandler);
    this.registerHandler('LearningPlanCreated', this.learningPlanCreatedHandler);
    // ... 其他事件处理器

    this.logger.info(`完成事件处理器注册。总共注册的事件类型: ${this.handlers.size}`);
  }

  // ...
}
```

### 2. 依赖注入容器配置

我们更新了依赖注入容器配置，确保所有事件处理器都被正确注册：

```typescript
// backend/infrastructure/di/containerConfig.ts
// 注册事件处理器
container.bind<ExerciseCreatedEventHandler>('ExerciseCreatedEventHandler').to(ExerciseCreatedEventHandler);
container.bind<NoteCreatedEventHandler>('NoteCreatedEventHandler').to(NoteCreatedEventHandler);
container.bind<LearningPlanCreatedEventHandler>('LearningPlanCreatedEventHandler').to(LearningPlanCreatedEventHandler);
```

### 3. WebSocket集成

我们加强了事件处理器与WebSocket服务的集成，确保实时通知能够正确发送：

```typescript
// backend/infrastructure/events/handlers/event-handler.base.ts
@injectable()
export abstract class EventHandlerBase<T extends DomainEvent> {
  constructor(
    @inject('Logger') protected logger: Logger,
    @inject('WebSocketService') protected webSocketService: WebSocketService
  ) {}

  // ...

  protected async sendUserNotification(userId: string, notificationType: string, data: any): Promise<void> {
    try {
      await this.webSocketService.publishToUser(userId, {
        type: notificationType,
        payload: data,
        timestamp: new Date().toISOString()
      });

      this.logger.info(`通知已发送给用户: ${userId}`, { notificationType, userId });
    } catch (error) {
      this.logger.error(`发送通知给用户失败: ${userId}`, { error, notificationType, userId });
    }
  }

  // ...
}
```

### 4. 事件持久化

我们改进了事件持久化机制，确保所有事件都能被正确存储和追踪：

```typescript
// backend/infrastructure/events/DatabaseEventStore.ts
@injectable()
export class DatabaseEventStore {
  async storeEvent(event: DomainEvent): Promise<void> {
    await EventModel.create({
      event_id: event.eventId,
      aggregate_id: event.aggregateId,
      event_type: event.eventType,
      occurred_on: event.occurredOn,
      event_data: JSON.stringify(event)
    });
  }

  // ...
}
```

## 最佳实践

### 1. 创建领域事件

领域事件应该在领域模型中创建，通常是在聚合根的方法中：

```typescript
// 在领域模型中创建事件
class Exercise extends AggregateRoot {
  publish() {
    // 业务逻辑...
    
    // 创建并注册事件
    const event = new ExercisePublishedEvent(
      this.id.toString(),
      this.title,
      this.difficulty,
      this.creatorId.toString(),
      this.isPublic
    );
    
    this.registerDomainEvent(event);
  }
}
```

### 2. 发布领域事件

领域事件应该在应用服务层通过工作单元发布：

```typescript
// 在应用服务中发布事件
@injectable()
export class ExerciseApplicationService {
  // ...
  
  async publishExercise(command: PublishExerciseCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 获取聚合根
      const exercise = await this.exerciseRepository.findById(command.exerciseId);
      if (!exercise) {
        throw new EntityNotFoundError('Exercise', command.exerciseId);
      }
      
      // 执行领域逻辑
      exercise.publish();
      
      // 保存聚合根
      await this.exerciseRepository.save(exercise);
      
      // 工作单元会自动发布注册的领域事件
      
      return this.toExerciseDto(exercise);
    });
  }
}
```

### 3. 处理领域事件

事件处理器应该专注于单一职责，处理特定类型的事件：

```typescript
@injectable()
export class ExercisePublishedEventHandler extends EventHandlerBase<ExercisePublishedEvent> {
  // ...
  
  protected async processEvent(event: ExercisePublishedEvent): Promise<void> {
    // 1. 更新统计数据
    await this.statisticsService.incrementContentStat('exercises', 'published');
    
    // 2. 检查成就
    if (event.creatorId) {
      await this.achievementService.checkAndUnlockAchievement(
        event.creatorId,
        'CONTENT_PUBLISHER',
        { contentType: 'exercise', contentId: event.aggregateId }
      );
    }
  }
  
  protected async sendNotification(event: ExercisePublishedEvent): Promise<void> {
    // 发送通知
    if (event.isPublic) {
      await this.sendBroadcastNotification('NEW_PUBLIC_EXERCISE', {
        exerciseId: event.aggregateId,
        title: event.title
      });
    }
  }
}
```

## 结论

通过这些更新，我们修复了事件处理机制中的问题，确保了从领域事件发生到前端收到实时通知的完整链路。这些改进将提高系统的可靠性、可维护性和可扩展性。

开发人员在使用事件处理机制时，应遵循上述最佳实践，确保事件的正确创建、发布和处理。
