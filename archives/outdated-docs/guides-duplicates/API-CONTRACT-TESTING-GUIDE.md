# API契约测试指南

## 概述

API契约测试是一种确保API实现与其规范（契约）一致的测试方法。在AIBUBB项目中，我们使用OpenAPI/Swagger规范作为API契约，并通过自动化工具验证API实现是否符合这些规范。

本指南介绍如何使用项目中的API契约测试工具进行测试，以及如何解释测试结果。

## 工具介绍

AIBUBB项目提供了两个API契约测试工具：

1. **api-contract-test.js**：基本的API契约测试工具，模拟请求和响应
2. **api-contract-validator.js**：高级API契约验证工具，支持实际请求和详细验证

这些工具位于`backend/scripts/`目录下。

## 前提条件

使用这些工具前，请确保：

1. 已安装所有依赖：`npm install`
2. OpenAPI规范文件已生成：`swagger.json`
3. 如果需要发送实际请求，API服务器已启动

## 使用api-contract-validator.js

### 基本用法

```bash
# 测试所有API端点（模拟模式）
node backend/scripts/api-contract-validator.js --all

# 测试特定路径和方法（模拟模式）
node backend/scripts/api-contract-validator.js --path=/api/v2/users --method=GET

# 发送实际请求到服务器
node backend/scripts/api-contract-validator.js --path=/api/v2/users --method=GET --live

# 显示详细输出
node backend/scripts/api-contract-validator.js --path=/api/v2/users --method=GET --live --verbose

# 使用认证令牌
node backend/scripts/api-contract-validator.js --path=/api/v2/users --method=GET --live --auth-token=YOUR_TOKEN

# 输出结果到文件
node backend/scripts/api-contract-validator.js --all --output=api-test-results.json
```

### 命令行选项

| 选项 | 描述 | 默认值 |
|------|------|--------|
| `--path` | 要测试的API路径 | 无（测试所有路径） |
| `--method` | 要测试的HTTP方法 | 无（测试所有方法） |
| `--all` | 测试所有API端点 | 无 |
| `--live` | 发送实际请求到服务器 | 无（默认模拟模式） |
| `--verbose` | 显示详细输出 | 无 |
| `--base-url` | 基础URL | `http://localhost:PORT` |
| `--auth-token` | 认证令牌 | 无 |
| `--output` | 输出结果到文件 | 无 |

### 测试模式

工具支持两种测试模式：

1. **模拟模式**（默认）：不发送实际请求，只验证OpenAPI规范的一致性
2. **实时模式**（`--live`）：发送实际请求到服务器，验证响应是否符合规范

## 测试结果解释

测试结果包括以下信息：

- **端点信息**：路径、方法、操作ID和描述
- **请求数据**：生成的测试数据（如果有）
- **响应验证**：响应是否符合规范
- **验证错误**：如果验证失败，显示具体错误

### 成功示例

```
测试端点: GET /api/v2/users
操作ID: getUsers
描述: 获取用户列表

发送请求到: http://localhost:3000/api/v2/users
✓ 请求成功 (200)
✓ 响应验证通过
```

### 失败示例

```
测试端点: GET /api/v2/users
操作ID: getUsers
描述: 获取用户列表

发送请求到: http://localhost:3000/api/v2/users
✓ 请求成功 (200)
✗ 响应验证失败
验证错误: [
  {
    "keyword": "required",
    "dataPath": ".data",
    "schemaPath": "#/properties/data/required",
    "params": { "missingProperty": "items" },
    "message": "should have required property 'items'"
  }
]
```

## 集成到CI/CD流程

可以将API契约测试集成到CI/CD流程中，确保每次代码变更都符合API规范。

### GitHub Actions示例

```yaml
name: API Contract Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  api-contract-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Start API server
        run: npm run start:test &
        # 等待服务器启动
      - name: Wait for API server
        run: sleep 10
      - name: Run API contract tests
        run: node backend/scripts/api-contract-validator.js --all --live
```

## 最佳实践

1. **定期运行测试**：确保API实现与规范保持一致
2. **更新规范后测试**：每次更新OpenAPI规范后运行测试
3. **更新实现后测试**：每次更新API实现后运行测试
4. **集成到CI/CD**：自动化测试流程，确保每次变更都符合规范
5. **保存测试结果**：使用`--output`选项保存测试结果，便于分析和比较

## 故障排除

### 无法找到swagger.json

确保已生成swagger.json文件：

```bash
# 检查文件是否存在
ls -la backend/config/swagger.json

# 如果不存在，可能需要生成
# 具体生成方式取决于项目配置
```

### 请求失败

如果使用`--live`模式发送实际请求失败，可能是因为：

1. API服务器未启动
2. 基础URL不正确
3. 需要认证令牌
4. 网络问题

解决方法：

```bash
# 确保API服务器已启动
npm start

# 指定正确的基础URL
node backend/scripts/api-contract-validator.js --path=/api/v2/users --method=GET --live --base-url=http://localhost:3001

# 提供认证令牌
node backend/scripts/api-contract-validator.js --path=/api/v2/users --method=GET --live --auth-token=YOUR_TOKEN
```

### 验证失败

如果响应验证失败，可能是因为：

1. API实现与规范不一致
2. 规范定义不正确
3. 测试数据生成不正确

解决方法：

1. 检查API实现是否符合规范
2. 检查OpenAPI规范是否正确
3. 使用`--verbose`选项查看详细信息

## 扩展和定制

如果需要扩展或定制API契约测试工具，可以修改以下文件：

- `backend/scripts/api-contract-validator.js`：主要测试工具
- `backend/scripts/api-contract-test.js`：基本测试工具

可以定制的方面包括：

1. 测试数据生成逻辑
2. 响应验证逻辑
3. 测试报告格式
4. 支持的命令行选项
