# AIBUBB API 文档（基于实际实现）

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 已完成 |
| 创建日期 | 2025-07-07 |
| 最后更新 | 2025-07-07 |
| 作者 | AIBUBB技术团队 |

## 目录

1. [API概述](#1-api概述)
2. [认证](#2-认证)
3. [用户管理](#3-用户管理)
4. [标签管理](#4-标签管理)
5. [学习计划](#5-学习计划)
6. [学习内容](#6-学习内容)
7. [错误处理](#7-错误处理)

**相关文档**：
- [API使用示例](./API-USAGE-EXAMPLES-REAL.md)
- [API版本迁移指南](./API-MIGRATION-GUIDE-REAL.md)

## 1. API概述

### 1.1 基础URL

所有API端点都以以下基础URL开头：

```
https://api.aibubb.com/api/v2
```

开发环境：

```
http://localhost:3000/api/v2
```

### 1.2 请求格式

- 请求体应使用JSON格式
- 请求头应包含`Content-Type: application/json`
- 认证请求头应包含`Authorization: Bearer {token}`

### 1.3 响应格式

所有API响应都使用以下格式：

```json
{
  "success": true,
  "data": { ... }
}
```

错误响应：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": { ... }
  }
}
```

### 1.4 分页

支持分页的端点接受以下参数：

- `page`：页码，从1开始
- `pageSize`：每页项目数，默认为10

分页响应格式：

```json
{
  "success": true,
  "data": {
    "items": [ ... ],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 10,
      "totalPages": 10
    }
  }
}
```

### 1.5 API文档访问

项目提供了两种方式查看API文档：

1. **Swagger UI**：访问`/api-docs`路径
2. **ReDoc**：访问`/redoc`路径

这两种方式都是在运行时从代码中的Swagger注释生成的，确保文档与实际实现保持同步。

## 2. 认证

### 2.1 用户登录

**端点**：`POST /auth/login`

**请求体**：

```json
{
  "code": "wx_code_from_wechat",
  "loginType": "wechat"
}
```

**响应**：

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 123456789,
      "nickname": "用户昵称",
      "avatarUrl": "https://example.com/avatar.jpg",
      "gender": 1,
      "studyDays": 10,
      "level": 2,
      "createdAt": "2023-06-15T08:00:00Z",
      "updatedAt": "2023-06-15T08:00:00Z"
    }
  }
}
```

### 2.2 刷新令牌

**端点**：`POST /auth/refresh-token`

**请求头**：`Authorization: Bearer {token}`

**请求体**：空

**响应**：

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2.3 用户登出

**端点**：`POST /auth/logout`

**请求头**：`Authorization: Bearer {token}`

**请求体**：空

**响应**：

```json
{
  "success": true,
  "data": {
    "message": "登出成功"
  }
}
```

## 3. 用户管理

### 3.1 获取当前用户信息

**端点**：`GET /users/me`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 123456789,
    "nickname": "用户昵称",
    "avatarUrl": "https://example.com/avatar.jpg",
    "gender": 1,
    "studyDays": 10,
    "level": 2,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z"
  }
}
```

### 3.2 更新用户信息

**端点**：`PATCH /users/me`

**请求头**：`Authorization: Bearer {token}`

**请求体**：

```json
{
  "nickname": "新昵称",
  "avatarUrl": "https://example.com/new-avatar.jpg"
}
```

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 123456789,
    "nickname": "新昵称",
    "avatarUrl": "https://example.com/new-avatar.jpg",
    "gender": 1,
    "studyDays": 10,
    "level": 2,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z"
  }
}
```

## 4. 标签管理

### 4.1 获取标签列表

**端点**：`GET /tags`

**请求头**：`Authorization: Bearer {token}`

**查询参数**：
- `page`：页码，从1开始
- `pageSize`：每页项目数，默认为10
- `includeDeleted`：是否包含已删除的标签（true, false）

**响应**：

```json
{
  "success": true,
  "data": {
    "tags": [
      {
        "id": 1001,
        "name": "倾听",
        "relevanceScore": 0.85,
        "weight": 1.0,
        "usageCount": 15,
        "isVerified": true,
        "sortOrder": 1,
        "category": {
          "id": 1,
          "name": "沟通技能"
        }
      }
    ],
    "count": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

### 4.2 获取标签详情

**端点**：`GET /tags/{id}`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 1001,
    "name": "倾听",
    "relevanceScore": 0.85,
    "weight": 1.0,
    "usageCount": 15,
    "isVerified": true,
    "sortOrder": 1,
    "category": {
      "id": 1,
      "name": "沟通技能"
    },
    "synonyms": [
      "积极倾听",
      "专注聆听"
    ]
  }
}
```

### 4.3 创建标签

**端点**：`POST /tags`

**请求头**：`Authorization: Bearer {token}`

**请求体**：

```json
{
  "name": "共情",
  "categoryId": 1,
  "relevanceScore": 0.8,
  "weight": 1.0
}
```

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 1002,
    "name": "共情",
    "relevanceScore": 0.8,
    "weight": 1.0,
    "usageCount": 0,
    "isVerified": false,
    "sortOrder": 0,
    "category": {
      "id": 1,
      "name": "沟通技能"
    }
  }
}
```

### 4.4 更新标签

**端点**：`PUT /tags/{id}`

**请求头**：`Authorization: Bearer {token}`

**请求体**：

```json
{
  "name": "共情能力",
  "categoryId": 1,
  "relevanceScore": 0.9,
  "weight": 1.2
}
```

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 1002,
    "name": "共情能力",
    "relevanceScore": 0.9,
    "weight": 1.2,
    "usageCount": 0,
    "isVerified": false,
    "sortOrder": 0,
    "category": {
      "id": 1,
      "name": "沟通技能"
    }
  }
}
```

### 4.5 软删除标签

**端点**：`DELETE /tags/{id}/soft-delete`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "message": "标签已被软删除"
  }
}
```

### 4.6 恢复标签

**端点**：`POST /tags/{id}/restore`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "message": "标签已被恢复"
  }
}
```

### 4.7 获取已删除的标签列表

**端点**：`GET /tags/deleted`

**请求头**：`Authorization: Bearer {token}`

**查询参数**：
- `page`：页码，从1开始
- `pageSize`：每页项目数，默认为10

**响应**：

```json
{
  "success": true,
  "data": {
    "tags": [
      {
        "id": 1003,
        "name": "表达",
        "relevanceScore": 0.75,
        "weight": 1.0,
        "usageCount": 5,
        "isVerified": false,
        "sortOrder": 3,
        "deletedAt": "2023-06-15T08:00:00Z",
        "category": {
          "id": 1,
          "name": "沟通技能"
        }
      }
    ],
    "count": 5,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

## 5. 学习计划

### 5.1 获取学习计划列表

**端点**：`GET /learning-plans`

**请求头**：`Authorization: Bearer {token}`

**查询参数**：
- `status`：状态（not_started, in_progress, completed, paused）
- `themeId`：主题ID
- `isCurrent`：是否当前计划（true, false）
- `page`：页码
- `pageSize`：每页项目数
- `includeDeleted`：是否包含已删除的计划（true, false）

**响应**：

```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": 101,
        "userId": 123456789,
        "themeId": 1,
        "title": "提升与伴侣的沟通能力",
        "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
        "targetDays": 14,
        "completedDays": 5,
        "progress": 35,
        "status": "in_progress",
        "startDate": "2023-06-15",
        "endDate": "2023-06-29",
        "isCurrent": true,
        "createdAt": "2023-06-15T08:00:00Z",
        "updatedAt": "2023-06-15T08:00:00Z",
        "tags": [
          {
            "id": 1001,
            "name": "倾听"
          },
          {
            "id": 1002,
            "name": "共情"
          }
        ]
      }
    ],
    "count": 5,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 5.2 获取学习计划详情

**端点**：`GET /learning-plans/{id}`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "提升与伴侣的沟通能力",
    "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
    "targetDays": 14,
    "completedDays": 5,
    "progress": 35,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-06-29",
    "isCurrent": true,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听",
        "relevanceScore": 0.85
      },
      {
        "id": 1002,
        "name": "共情",
        "relevanceScore": 0.75
      }
    ],
    "dailyContents": [
      {
        "day": 1,
        "title": "了解倾听的重要性",
        "content": "今天我们将学习倾听的重要性...",
        "isCompleted": true,
        "completionDate": "2023-06-15T10:30:00Z"
      },
      {
        "day": 2,
        "title": "练习积极倾听",
        "content": "今天我们将练习积极倾听技巧...",
        "isCompleted": true,
        "completionDate": "2023-06-16T11:15:00Z"
      }
    ]
  }
}
```

### 5.3 创建学习计划

**端点**：`POST /learning-plans`

**请求头**：`Authorization: Bearer {token}`

**请求体**：

```json
{
  "title": "提升与伴侣的沟通能力",
  "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
  "themeId": 1,
  "targetDays": 14,
  "tagIds": [1001, 1002]
}
```

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "提升与伴侣的沟通能力",
    "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
    "targetDays": 14,
    "completedDays": 0,
    "progress": 0,
    "status": "not_started",
    "startDate": "2023-06-15",
    "endDate": "2023-06-29",
    "isCurrent": true,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听"
      },
      {
        "id": 1002,
        "name": "共情"
      }
    ]
  }
}
```

### 5.4 更新学习计划

**端点**：`PUT /learning-plans/{id}`

**请求头**：`Authorization: Bearer {token}`

**请求体**：

```json
{
  "title": "改善与伴侣的沟通",
  "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
  "targetDays": 21
}
```

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "改善与伴侣的沟通",
    "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
    "targetDays": 21,
    "completedDays": 5,
    "progress": 24,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-07-06",
    "isCurrent": true,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-17T09:30:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听"
      },
      {
        "id": 1002,
        "name": "共情"
      }
    ]
  }
}
```

### 5.5 软删除学习计划

**端点**：`DELETE /learning-plans/{id}/soft-delete`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "message": "学习计划已被软删除"
  }
}
```

### 5.6 恢复学习计划

**端点**：`POST /learning-plans/{id}/restore`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "message": "学习计划已被恢复"
  }
}
```

## 6. 学习内容

### 6.1 获取练习列表

**端点**：`GET /exercises`

**请求头**：`Authorization: Bearer {token}`

**查询参数**：
- `tagId`：标签ID
- `difficulty`：难度（beginner, intermediate, advanced）
- `status`：状态（draft, published, archived）
- `page`：页码
- `pageSize`：每页项目数
- `includeDeleted`：是否包含已删除的练习（true, false）

**响应**：

```json
{
  "success": true,
  "data": {
    "exercises": [
      {
        "id": 401,
        "tagId": 1001,
        "title": "积极倾听练习",
        "description": "这个练习帮助你提高积极倾听的能力...",
        "expectedResult": "通过这个练习，你将能够更好地理解他人的观点...",
        "difficulty": "beginner",
        "timeEstimateMinutes": 15,
        "isOfficial": true,
        "status": "published",
        "createdAt": "2023-06-15T08:00:00Z",
        "updatedAt": "2023-06-15T08:00:00Z"
      }
    ],
    "count": 10,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 6.2 获取练习详情

**端点**：`GET /exercises/{id}`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 401,
    "tagId": 1001,
    "title": "积极倾听练习",
    "description": "这个练习帮助你提高积极倾听的能力...",
    "content": "1. 找一个安静的环境\n2. 邀请一个朋友或家人进行对话\n3. 专注于对方说的话，不要打断\n4. 通过点头、微笑等非语言方式表示你在听\n5. 在对方说完后，复述你所理解的内容\n6. 询问是否有任何遗漏或误解",
    "expectedResult": "通过这个练习，你将能够更好地理解他人的观点...",
    "difficulty": "beginner",
    "timeEstimateMinutes": 15,
    "isOfficial": true,
    "status": "published",
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z",
    "tag": {
      "id": 1001,
      "name": "倾听"
    }
  }
}
```

### 6.3 创建练习

**端点**：`POST /exercises`

**请求头**：`Authorization: Bearer {token}`

**请求体**：

```json
{
  "tagId": 1001,
  "title": "深度倾听练习",
  "description": "这个练习帮助你提高深度倾听的能力",
  "content": "1. 找一个安静的环境\n2. 邀请一个朋友或家人进行对话\n3. 专注于对方说的话，不要打断\n4. 通过点头、微笑等非语言方式表示你在听\n5. 在对方说完后，复述你所理解的内容\n6. 询问是否有任何遗漏或误解",
  "expectedResult": "通过这个练习，你将能够更好地理解他人的观点",
  "difficulty": "intermediate",
  "timeEstimateMinutes": 30
}
```

**响应**：

```json
{
  "success": true,
  "data": {
    "id": 402,
    "tagId": 1001,
    "title": "深度倾听练习",
    "description": "这个练习帮助你提高深度倾听的能力",
    "content": "1. 找一个安静的环境\n2. 邀请一个朋友或家人进行对话\n3. 专注于对方说的话，不要打断\n4. 通过点头、微笑等非语言方式表示你在听\n5. 在对方说完后，复述你所理解的内容\n6. 询问是否有任何遗漏或误解",
    "expectedResult": "通过这个练习，你将能够更好地理解他人的观点",
    "difficulty": "intermediate",
    "timeEstimateMinutes": 30,
    "isOfficial": false,
    "status": "draft",
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z",
    "tag": {
      "id": 1001,
      "name": "倾听"
    }
  }
}
```

### 6.4 软删除练习

**端点**：`DELETE /exercises/{id}/soft-delete`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "message": "练习已被软删除"
  }
}
```

### 6.5 恢复练习

**端点**：`POST /exercises/{id}/restore`

**请求头**：`Authorization: Bearer {token}`

**响应**：

```json
{
  "success": true,
  "data": {
    "message": "练习已被恢复"
  }
}
```

## 7. 错误处理

### 7.1 错误响应格式

所有错误响应都使用以下格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": { ... }
  }
}
```

### 7.2 常见错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `INVALID_PARAMETERS` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权，需要登录 |
| `FORBIDDEN` | 403 | 禁止访问，权限不足 |
| `RESOURCE_NOT_FOUND` | 404 | 请求的资源不存在 |
| `RESOURCE_CONFLICT` | 409 | 资源冲突，例如已存在同名资源 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求过于频繁 |
| `SERVER_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务暂时不可用 |
| `VALIDATION_ERROR` | 422 | 数据验证失败 |

### 7.3 错误处理最佳实践

1. 始终检查响应中的`success`字段
2. 如果`success`为`false`，从`error`对象中获取错误信息
3. 根据`error.code`字段处理不同类型的错误
4. 显示用户友好的错误消息，可以使用`error.message`
5. 对于表单验证错误，使用`error.details`中的字段级错误信息

### 7.2 常见错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `INVALID_PARAMETERS` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权，需要登录 |
| `FORBIDDEN` | 403 | 禁止访问，权限不足 |
| `RESOURCE_NOT_FOUND` | 404 | 请求的资源不存在 |
| `RESOURCE_CONFLICT` | 409 | 资源冲突，例如已存在同名资源 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求过于频繁 |
| `SERVER_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务暂时不可用 |
| `VALIDATION_ERROR` | 422 | 数据验证失败 |

### 7.3 错误处理最佳实践

1. 始终检查响应中的`success`字段
2. 如果`success`为`false`，从`error`对象中获取错误信息
3. 根据`error.code`字段处理不同类型的错误
4. 显示用户友好的错误消息，可以使用`error.message`
5. 对于表单验证错误，使用`error.details`中的字段级错误信息