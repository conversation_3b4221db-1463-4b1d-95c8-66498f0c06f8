# AIBUBB API 版本迁移指南（基于实际实现）

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 已完成 |
| 创建日期 | 2025-07-07 |
| 最后更新 | 2025-07-07 |
| 作者 | AIBUBB技术团队 |

## 目录

1. [概述](#1-概述)
2. [V1到V2的主要变化](#2-v1到v2的主要变化)
3. [迁移步骤](#3-迁移步骤)
4. [API端点变更详情](#4-api端点变更详情)
5. [常见问题](#5-常见问题)

## 1. 概述

本文档提供了从AIBUBB API V1版本迁移到V2版本的指南，包括主要变化、迁移步骤和API端点变更详情。V2版本引入了软删除功能、批量操作和更好的架构设计，建议所有客户端尽快迁移到V2版本。

### 1.1 迁移时间表

| 阶段 | 日期 | 描述 |
|------|------|------|
| V2版本发布 | 2025-01-01 | V2版本正式发布 |
| V1版本弃用通知 | 2025-04-01 | 开始向使用V1版本的客户端发送弃用通知 |
| V1版本停止维护 | 2025-07-01 | V1版本停止维护，不再修复bug |
| V1版本关闭 | 2025-10-01 | V1版本完全关闭，所有请求将返回410 Gone |

## 2. V1到V2的主要变化

### 2.1 架构变更

- **领域驱动设计**：V2版本采用领域驱动设计架构，提高了代码的可维护性和可扩展性
- **统一响应格式**：所有API响应都使用统一的格式，包括`success`字段和`data`或`error`字段
- **统一错误处理**：所有错误响应都使用统一的格式，包括错误代码、错误消息和详细信息

### 2.2 功能增强

- **软删除**：V2版本支持软删除功能，允许恢复已删除的资源
- **批量操作**：V2版本支持批量创建、更新和删除操作
- **分页改进**：V2版本的分页响应包含更多信息，如总记录数和总页数
- **查询参数增强**：V2版本支持更多查询参数，如排序和过滤

### 2.3 URL路径变更

- **版本前缀**：V2版本的API端点使用`/api/v2`前缀，而不是`/api/v1`
- **资源命名**：V2版本使用更一致的资源命名，如`learning-plans`而不是`learningPlans`
- **操作命名**：V2版本使用更RESTful的操作命名，如`/tags/{id}/restore`表示恢复标签

### 2.4 请求和响应格式变更

- **请求体格式**：V2版本使用camelCase命名字段，而不是snake_case
- **响应格式**：V2版本的响应始终包含`success`字段，成功响应包含`data`字段，错误响应包含`error`字段
- **错误响应**：V2版本的错误响应包含`code`、`message`和`details`字段

## 3. 迁移步骤

### 3.1 更新API基础URL

将所有API请求的基础URL从`/api/v1`更新为`/api/v2`：

```javascript
// V1
const API_BASE_URL = '/api/v1';

// V2
const API_BASE_URL = '/api/v2';
```

### 3.2 更新请求处理

更新请求处理逻辑，适应V2版本的统一响应格式：

```javascript
// V1
fetch('/api/v1/users/me')
  .then(response => response.json())
  .then(user => {
    // 直接使用用户数据
    console.log(user.nickname);
  });

// V2
fetch('/api/v2/users/me')
  .then(response => response.json())
  .then(result => {
    if (result.success) {
      // 从data字段获取用户数据
      const user = result.data;
      console.log(user.nickname);
    } else {
      // 处理错误
      console.error(result.error.message);
    }
  });
```

### 3.3 更新错误处理

更新错误处理逻辑，适应V2版本的统一错误格式：

```javascript
// V1
fetch('/api/v1/users/me')
  .then(response => {
    if (!response.ok) {
      throw new Error('请求失败');
    }
    return response.json();
  })
  .catch(error => {
    console.error(error.message);
  });

// V2
fetch('/api/v2/users/me')
  .then(response => response.json())
  .then(result => {
    if (!result.success) {
      throw new Error(result.error.message);
    }
    return result.data;
  })
  .catch(error => {
    console.error(error.message);
  });
```

### 3.4 使用新功能

更新代码以使用V2版本的新功能，如软删除和批量操作：

```javascript
// 软删除标签
fetch('/api/v2/tags/1001/soft-delete', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
  .then(response => response.json())
  .then(result => {
    if (result.success) {
      console.log('标签已被软删除');
    }
  });

// 恢复标签
fetch('/api/v2/tags/1001/restore', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
  .then(response => response.json())
  .then(result => {
    if (result.success) {
      console.log('标签已被恢复');
    }
  });
```

## 4. API端点变更详情

### 4.1 认证相关端点

| V1端点 | V2端点 | 变更说明 |
|--------|--------|----------|
| `POST /api/v1/auth/login` | `POST /api/v2/auth/login` | 响应格式变更，增加`success`字段 |
| `POST /api/v1/auth/refresh-token` | `POST /api/v2/auth/refresh-token` | 响应格式变更，增加`success`字段 |
| `POST /api/v1/auth/logout` | `POST /api/v2/auth/logout` | 响应格式变更，增加`success`字段 |

### 4.2 用户相关端点

| V1端点 | V2端点 | 变更说明 |
|--------|--------|----------|
| `GET /api/v1/users/me` | `GET /api/v2/users/me` | 响应格式变更，增加`success`字段 |
| `PATCH /api/v1/users/me` | `PATCH /api/v2/users/me` | 响应格式变更，增加`success`字段 |

### 4.3 标签相关端点

| V1端点 | V2端点 | 变更说明 |
|--------|--------|----------|
| `GET /api/v1/tags` | `GET /api/v2/tags` | 响应格式变更，增加`success`字段，支持`includeDeleted`参数 |
| `GET /api/v1/tags/:id` | `GET /api/v2/tags/:id` | 响应格式变更，增加`success`字段 |
| `POST /api/v1/tags` | `POST /api/v2/tags` | 响应格式变更，增加`success`字段 |
| `PUT /api/v1/tags/:id` | `PUT /api/v2/tags/:id` | 响应格式变更，增加`success`字段 |
| `DELETE /api/v1/tags/:id` | `DELETE /api/v2/tags/:id` | 响应格式变更，增加`success`字段，实现为硬删除 |
| 不存在 | `DELETE /api/v2/tags/:id/soft-delete` | 新增端点，实现软删除功能 |
| 不存在 | `POST /api/v2/tags/:id/restore` | 新增端点，实现恢复功能 |
| 不存在 | `GET /api/v2/tags/deleted` | 新增端点，获取已删除的标签列表 |

### 4.4 学习计划相关端点

| V1端点 | V2端点 | 变更说明 |
|--------|--------|----------|
| `GET /api/v1/learningPlans` | `GET /api/v2/learning-plans` | 路径变更，响应格式变更，增加`success`字段，支持`includeDeleted`参数 |
| `GET /api/v1/learningPlans/:id` | `GET /api/v2/learning-plans/:id` | 路径变更，响应格式变更，增加`success`字段 |
| `POST /api/v1/learningPlans` | `POST /api/v2/learning-plans` | 路径变更，响应格式变更，增加`success`字段 |
| `PUT /api/v1/learningPlans/:id` | `PUT /api/v2/learning-plans/:id` | 路径变更，响应格式变更，增加`success`字段 |
| `DELETE /api/v1/learningPlans/:id` | `DELETE /api/v2/learning-plans/:id` | 路径变更，响应格式变更，增加`success`字段，实现为硬删除 |
| 不存在 | `DELETE /api/v2/learning-plans/:id/soft-delete` | 新增端点，实现软删除功能 |
| 不存在 | `POST /api/v2/learning-plans/:id/restore` | 新增端点，实现恢复功能 |
| 不存在 | `GET /api/v2/learning-plans/deleted` | 新增端点，获取已删除的学习计划列表 |

### 4.5 练习相关端点

| V1端点 | V2端点 | 变更说明 |
|--------|--------|----------|
| `GET /api/v1/exercises` | `GET /api/v2/exercises` | 响应格式变更，增加`success`字段，支持`includeDeleted`参数 |
| `GET /api/v1/exercises/:id` | `GET /api/v2/exercises/:id` | 响应格式变更，增加`success`字段 |
| `POST /api/v1/exercises` | `POST /api/v2/exercises` | 响应格式变更，增加`success`字段 |
| `PUT /api/v1/exercises/:id` | `PUT /api/v2/exercises/:id` | 响应格式变更，增加`success`字段 |
| `DELETE /api/v1/exercises/:id` | `DELETE /api/v2/exercises/:id` | 响应格式变更，增加`success`字段，实现为硬删除 |
| 不存在 | `DELETE /api/v2/exercises/:id/soft-delete` | 新增端点，实现软删除功能 |
| 不存在 | `POST /api/v2/exercises/:id/restore` | 新增端点，实现恢复功能 |
| 不存在 | `GET /api/v2/exercises/deleted` | 新增端点，获取已删除的练习列表 |

## 5. 常见问题

### 5.1 如何处理V1和V2版本的兼容性问题？

在迁移期间，您可能需要同时支持V1和V2版本的API。建议创建一个适配层，根据API版本调用不同的处理逻辑：

```javascript
function fetchUser(version = 'v2') {
  const url = version === 'v1' ? '/api/v1/users/me' : '/api/v2/users/me';

  return fetch(url)
    .then(response => response.json())
    .then(result => {
      if (version === 'v1') {
        return result; // V1直接返回用户数据
      } else {
        return result.success ? result.data : Promise.reject(result.error);
      }
    });
}
```

### 5.2 如何处理V2版本的错误响应？

V2版本的错误响应包含更详细的信息，建议根据错误代码进行不同的处理：

```javascript
fetch('/api/v2/users/me')
  .then(response => response.json())
  .then(result => {
    if (!result.success) {
      const error = result.error;

      switch (error.code) {
        case 'UNAUTHORIZED':
          // 处理未授权错误
          redirectToLogin();
          break;
        case 'RESOURCE_NOT_FOUND':
          // 处理资源不存在错误
          showNotFoundMessage();
          break;
        case 'VALIDATION_ERROR':
          // 处理验证错误
          showValidationErrors(error.details);
          break;
        default:
          // 处理其他错误
          showErrorMessage(error.message);
      }

      return Promise.reject(error);
    }

    return result.data;
  });
```

### 5.3 如何使用V2版本的软删除功能？

V2版本引入了软删除功能，允许恢复已删除的资源。以下是使用软删除功能的示例：

```javascript
// 软删除标签
function softDeleteTag(tagId) {
  return fetch(`/api/v2/tags/${tagId}/soft-delete`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        return result.data;
      } else {
        return Promise.reject(result.error);
      }
    });
}

// 恢复标签
function restoreTag(tagId) {
  return fetch(`/api/v2/tags/${tagId}/restore`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        return result.data;
      } else {
        return Promise.reject(result.error);
      }
    });
}
```