#!/bin/bash
# batch-analyze.sh - 批量文档分析脚本

echo "🚀 开始AIBUBB项目文档全面分析..."

# 创建分析结果目录
mkdir -p "文档梳理工作/分析结果"
mkdir -p "文档梳理工作/分类结果"

# 生成文档清单（排除node_modules和archives）
echo "📋 生成文档清单..."
find . -name "*.md" -type f | grep -v -E "(node_modules|archives)" > "文档梳理工作/文档清单.txt"

TOTAL_DOCS=$(wc -l < "文档梳理工作/文档清单.txt")
echo "📊 发现 $TOTAL_DOCS 个文档需要分析"

# 初始化分类计数器
PERMANENT_COUNT=0
LONG_TERM_COUNT=0
MEDIUM_TERM_COUNT=0
SHORT_TERM_COUNT=0
ARCHIVABLE_COUNT=0

# 创建分类文件
> "文档梳理工作/分类结果/permanent.txt"
> "文档梳理工作/分类结果/longTerm.txt"
> "文档梳理工作/分类结果/mediumTerm.txt"
> "文档梳理工作/分类结果/shortTerm.txt"
> "文档梳理工作/分类结果/archivable.txt"

# 批量价值评估
CURRENT=0
while read -r file; do
    CURRENT=$((CURRENT + 1))
    echo "🔍 [$CURRENT/$TOTAL_DOCS] 分析: $file"

    # 运行评估
    RESULT=$(node tools/document-governance-toolkit.js assess "$file" 2>/dev/null)

    if [ $? -eq 0 ]; then
        # 保存详细结果
        echo "$RESULT" > "文档梳理工作/分析结果/$(basename "$file" .md)_assessment.json"

        # 提取价值等级
        VALUE_LEVEL=$(echo "$RESULT" | grep -o '"valueLevel": "[^"]*"' | cut -d'"' -f4)
        VALUE_SCORE=$(echo "$RESULT" | grep -o '"valueScore": [0-9]*' | cut -d':' -f2 | tr -d ' ')

        # 分类统计
        case "$VALUE_LEVEL" in
            "permanent")
                echo "$file (score: $VALUE_SCORE)" >> "文档梳理工作/分类结果/permanent.txt"
                PERMANENT_COUNT=$((PERMANENT_COUNT + 1))
                ;;
            "longTerm")
                echo "$file (score: $VALUE_SCORE)" >> "文档梳理工作/分类结果/longTerm.txt"
                LONG_TERM_COUNT=$((LONG_TERM_COUNT + 1))
                ;;
            "mediumTerm")
                echo "$file (score: $VALUE_SCORE)" >> "文档梳理工作/分类结果/mediumTerm.txt"
                MEDIUM_TERM_COUNT=$((MEDIUM_TERM_COUNT + 1))
                ;;
            "shortTerm")
                echo "$file (score: $VALUE_SCORE)" >> "文档梳理工作/分类结果/shortTerm.txt"
                SHORT_TERM_COUNT=$((SHORT_TERM_COUNT + 1))
                ;;
            "archivable")
                echo "$file (score: $VALUE_SCORE)" >> "文档梳理工作/分类结果/archivable.txt"
                ARCHIVABLE_COUNT=$((ARCHIVABLE_COUNT + 1))
                ;;
        esac
    else
        echo "❌ 分析失败: $file"
    fi
done < "文档梳理工作/文档清单.txt"

# 生成分析报告
echo "📊 生成分析报告..."
cat > "文档梳理工作/分析报告.md" << EOF
# AIBUBB项目文档分析报告

## 📊 分析统计

- **总文档数**: $TOTAL_DOCS
- **永久保留**: $PERMANENT_COUNT 个
- **长期保留**: $LONG_TERM_COUNT 个
- **中期保留**: $MEDIUM_TERM_COUNT 个
- **短期保留**: $SHORT_TERM_COUNT 个
- **可归档**: $ARCHIVABLE_COUNT 个

## 📈 分布比例

- 永久保留: $(echo "scale=1; $PERMANENT_COUNT * 100 / $TOTAL_DOCS" | bc)%
- 长期保留: $(echo "scale=1; $LONG_TERM_COUNT * 100 / $TOTAL_DOCS" | bc)%
- 中期保留: $(echo "scale=1; $MEDIUM_TERM_COUNT * 100 / $TOTAL_DOCS" | bc)%
- 短期保留: $(echo "scale=1; $SHORT_TERM_COUNT * 100 / $TOTAL_DOCS" | bc)%
- 可归档: $(echo "scale=1; $ARCHIVABLE_COUNT * 100 / $TOTAL_DOCS" | bc)%

## 🔴 永久保留文档

EOF

if [ -s "文档梳理工作/分类结果/permanent.txt" ]; then
    cat "文档梳理工作/分类结果/permanent.txt" >> "文档梳理工作/分析报告.md"
else
    echo "无" >> "文档梳理工作/分析报告.md"
fi

cat >> "文档梳理工作/分析报告.md" << EOF

## 🟡 长期保留文档

EOF

if [ -s "文档梳理工作/分类结果/longTerm.txt" ]; then
    cat "文档梳理工作/分类结果/longTerm.txt" >> "文档梳理工作/分析报告.md"
else
    echo "无" >> "文档梳理工作/分析报告.md"
fi

cat >> "文档梳理工作/分析报告.md" << EOF

## 🟢 中期保留文档

EOF

if [ -s "文档梳理工作/分类结果/mediumTerm.txt" ]; then
    head -10 "文档梳理工作/分类结果/mediumTerm.txt" >> "文档梳理工作/分析报告.md"
    if [ $(wc -l < "文档梳理工作/分类结果/mediumTerm.txt") -gt 10 ]; then
        echo "... (还有 $(($(wc -l < "文档梳理工作/分类结果/mediumTerm.txt") - 10)) 个)" >> "文档梳理工作/分析报告.md"
    fi
else
    echo "无" >> "文档梳理工作/分析报告.md"
fi

cat >> "文档梳理工作/分析报告.md" << EOF

## ⚪ 可归档文档（需人工确认）

EOF

if [ -s "文档梳理工作/分类结果/archivable.txt" ]; then
    cat "文档梳理工作/分类结果/archivable.txt" >> "文档梳理工作/分析报告.md"
else
    echo "无" >> "文档梳理工作/分析报告.md"
fi

echo ""
echo "✅ 分析完成！"
echo "📄 详细报告: 文档梳理工作/分析报告.md"
echo "📁 分类结果: 文档梳理工作/分类结果/"
echo "📊 详细分析: 文档梳理工作/分析结果/"
