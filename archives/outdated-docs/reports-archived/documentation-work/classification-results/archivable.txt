./DEPLOYMENT-CHECKLIST.md (score: 20)
./核心设计文档/statistics-module-design.md (score: 25)
./核心设计文档/PROJECT-ARCHITECTURE.md (score: 15)
./开发指南/INTEGRATION-TESTING-GUIDE.md (score: 20)
./开发指南/API-CONTRACT-TEST-ENV-GUIDE.md (score: 20)
./CI-CD-GUIDE.md (score: 20)
./DATABASE-CHANGELOG.md (score: 20)
./DOCKER-README.md (score: 20)
./cursor-mcp-guide.md (score: 15)
./API-DESIGN.md (score: 15)
./backend/docs/SECURITY-CODE-REVIEW-CHECKLIST.md (score: 25)
./backend/docs/CODE-REVIEW-CHECKLIST.md (score: 20)
./working/AIBUBB文档验证最终总结报告-更新版.md (score: 15)
./working/AIBUBB文档验证最终总结报告.md (score: 15)
./working/README.md (score: 23)
./docs/README.md (score: 20)
./DOCKER-DEVELOPMENT.md (score: 20)
./architecture-diagrams.md (score: 25)
./后端培训文档/AIBUBB后端系统全貌培训文档-第4章-领域模型.md (score: 10)
./后端培训文档/AIBUBB后端系统全貌培训文档-第6章-事件驱动架构.md (score: 25)
./后端培训文档/AIBUBB后端系统全貌培训文档-大纲.md (score: 24)
./后端培训文档/AIBUBB后端系统全貌培训文档-第1章-系统概述.md (score: 17)
./后端培训文档/AIBUBB后端系统全貌培训文档-第2章-系统架构.md (score: 25)
./CONTRIBUTING.md (score: 15)
./scripts/README.md (score: 20)
./.github/PULL_REQUEST_TEMPLATE.md (score: 10)
./.github/ISSUE_TEMPLATE/feature_request.md (score: 10)
./.github/ISSUE_TEMPLATE/bug_report.md (score: 10)
./DEPLOYMENT-SUMMARY.md (score: 20)
./API-CONTRACT-TEST-ENV-GUIDE.md (score: 20)
./ARCHITECTURE-PRINCIPLES.md (score: 20)
./QUICK-DEPLOYMENT-GUIDE.md (score: 20)
./DOCKER-OPTIMIZED-GUIDE.md (score: 20)
./reports/analysis/2025-01/refactoring-analysis.md (score: 24)
./reports/planning/2025-01/next_phase_backend_investigation_plan.md (score: 24)
./reports/verification/2025-01/summary/AIBUBB文档验证执行摘要.md (score: 29)
./reports/summary/2025-01/测试策略升级进展.md (score: 24)
./reports/summary/2025-01/测试工作完成情况总结.md (score: 24)
