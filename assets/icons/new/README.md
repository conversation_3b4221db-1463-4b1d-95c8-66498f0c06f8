# 小程序图标库

本目录包含了为小程序特别设计的104个PNG格式图标，共52种不同的图标，每种图标都有普通状态和激活状态两个版本。

## 图标特点

- **透明背景**：所有图标都有透明背景，便于在不同背景色上使用
- **多种风格**：包含圆形、方形、圆角方形、菱形、三角形、六边形、星形、十字形、心形和圆环等10种不同风格
- **丰富颜色**：图标使用了10种不同的现代色彩
- **成对设计**：每个图标都有普通和激活状态版本，适合TabBar和按钮使用
- **尺寸统一**：所有图标都是64×64像素，保持设计一致性

## 图标列表

本图标库包含以下图标：

1. home - 首页
2. profile - 个人中心
3. message - 消息
4. notification - 通知
5. settings - 设置
6. search - 搜索
7. favorite - 收藏
8. share - 分享
9. comment - 评论
10. like - 点赞
11. camera - 相机
12. gallery - 图库
13. location - 位置
14. calendar - 日历
15. clock - 时钟
16. cart - 购物车
17. order - 订单
18. wallet - 钱包
19. gift - 礼物
20. coupon - 优惠券
21. edit - 编辑
22. delete - 删除
23. add - 添加
24. minus - 减少
25. close - 关闭
26. menu - 菜单
27. list - 列表
28. grid - 网格
29. chart - 图表
30. graph - 图形
31. user - 用户
32. users - 用户组
33. lock - 锁定
34. unlock - 解锁
35. key - 钥匙
36. file - 文件
37. folder - 文件夹
38. download - 下载
39. upload - 上传
40. refresh - 刷新
41. play - 播放
42. pause - 暂停
43. stop - 停止
44. forward - 前进
45. backward - 后退
46. audio - 音频
47. video - 视频
48. wifi - 无线网络
49. bluetooth - 蓝牙
50. battery - 电池
51. book - 书本/学习
52. chat - 聊天

## 使用方法

在小程序项目中使用这些图标：

1. 将图标文件复制到您的项目的assets/icons目录下
2. 在TabBar配置中引用图标路径：

```json
"tabBar": {
  "color": "#999999",
  "selectedColor": "#3B82F6",
  "backgroundColor": "#FFFFFF",
  "list": [
    {
      "pagePath": "pages/index/index",
      "text": "首页",
      "iconPath": "assets/icons/new/home.png",
      "selectedIconPath": "assets/icons/new/home-active.png"
    },
    {
      "pagePath": "pages/learn/index",
      "text": "学习",
      "iconPath": "assets/icons/new/book.png",
      "selectedIconPath": "assets/icons/new/book-active.png"
    },
    {
      "pagePath": "pages/square/index",
      "text": "广场",
      "iconPath": "assets/icons/new/share.png",
      "selectedIconPath": "assets/icons/new/share-active.png"
    },
    {
      "pagePath": "pages/profile/index",
      "text": "我的",
      "iconPath": "assets/icons/new/user.png",
      "selectedIconPath": "assets/icons/new/user-active.png"
    }
  ]
}
```

3. 在页面中使用图标：

```html
<image src="/assets/icons/new/search.png" class="icon" />
```

## 自定义图标

如需添加新图标，可以使用项目根目录下的`create_custom_icon.py`脚本：

```bash
python3 create_custom_icon.py <图标名称> <样式> <颜色>
```

例如：
```bash
python3 create_custom_icon.py chat circle purple
```

运行`python3 create_custom_icon.py --help`查看详细使用说明。

## 授权说明

这些图标由AI互动泡泡团队创建，仅供本小程序项目使用。 