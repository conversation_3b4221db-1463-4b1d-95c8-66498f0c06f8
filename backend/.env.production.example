# AIBUBB 生产环境配置
NODE_ENV=production
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=aibubb
DB_USER=aibubb_user
DB_PASSWORD=your_secure_database_password_here
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=your_super_secure_jwt_secret_key_at_least_32_characters_long
JWT_EXPIRES_IN=7d

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password_here

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 监控配置
MONITORING_ENABLED=true
HEALTH_CHECK_ENABLED=true

# 性能配置
COMPRESSION_ENABLED=true
STATIC_CACHE_MAX_AGE=86400

# 告警配置
ALERT_WEBHOOK_URL=your_alert_webhook_url
ALERT_EMAIL=<EMAIL>

# 缓存配置
CACHE_ENABLED=true
CACHE_MAX_SIZE=104857600

# HTTPS配置
HTTPS_ENABLED=false
HTTPS_PORT=443
HTTPS_REDIRECT=false
SSL_KEY_PATH=./ssl/private.key
SSL_CERT_PATH=./ssl/certificate.crt
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000

# 集群配置
CLUSTER_ENABLED=true
CLUSTER_WORKERS=0
PM2_INSTANCES=max

# 备份配置
BACKUP_ENABLED=true
BACKUP_DIR=../backups
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION_LEVEL=6