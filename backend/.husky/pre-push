#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "\033[1;33m运行推送前检查...\033[0m"

# 获取当前分支名
BRANCH=$(git symbolic-ref --short HEAD)

# 如果是main或develop分支，运行完整测试
if [ "$BRANCH" = "main" ] || [ "$BRANCH" = "develop" ]; then
  echo "\033[1;33m检测到main或develop分支，运行完整测试...\033[0m"
  cd backend && npm run test:unit
else
  # 其他分支只运行与更改相关的测试
  echo "\033[1;33m运行与更改相关的测试...\033[0m"
  cd backend && npm run test:unit -- --findRelatedTests $(git diff --name-only --staged --diff-filter=ACMR | grep -E '\.js$|\.ts$' | tr '\n' ' ')
fi
