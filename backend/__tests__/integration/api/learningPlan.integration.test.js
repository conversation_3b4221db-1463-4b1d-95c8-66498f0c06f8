/**
 * 学习计划API集成测试
 */

const request = require('supertest');
const app = require('../../../server');
const { sequelize } = require('../../../models');
const jwt = require('jsonwebtoken');

describe('Learning Plan API Integration Tests', () => {
  let authToken;
  let testUserId = 'test-user-123';
  let testPlanId;

  beforeAll(async () => {
    // 生成测试用的JWT token
    authToken = jwt.sign(
      { userId: testUserId, role: 'user' },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' },
    );

    // 确保数据库连接
    await sequelize.authenticate();
  });

  afterAll(async () => {
    // 清理测试数据
    await sequelize.query('DELETE FROM learning_plans WHERE user_id = ?', {
      replacements: [testUserId],
    });

    // 关闭数据库连接
    await sequelize.close();
  });

  describe('POST /api/v2/learning-plans', () => {
    it('should create a new learning plan', async () => {
      const planData = {
        themeId: 1,
        title: '测试学习计划',
        description: '这是一个测试学习计划',
        targetDays: 7,
        tags: [
          { name: '沟通技巧', relevance_score: 0.9 },
          { name: '人际关系', relevance_score: 0.8 },
        ],
      };

      const response = await request(app)
        .post('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .send(planData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.title).toBe(planData.title);
      expect(response.body.data.description).toBe(planData.description);

      testPlanId = response.body.data.id;
    });

    it('should return 400 for invalid theme ID', async () => {
      const planData = {
        themeId: 999999,
        title: '无效主题计划',
        description: '测试无效主题',
        targetDays: 7,
      };

      const response = await request(app)
        .post('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .send(planData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('主题不存在');
    });

    it('should return 401 without authentication', async () => {
      const planData = {
        themeId: 1,
        title: '未认证计划',
        description: '测试未认证',
        targetDays: 7,
      };

      await request(app).post('/api/v2/learning-plans').send(planData).expect(401);
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('验证失败');
    });
  });

  describe('GET /api/v2/learning-plans', () => {
    it('should get user learning plans with pagination', async () => {
      const response = await request(app)
        .get('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ page: 1, pageSize: 10 })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('plans');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.plans)).toBe(true);

      if (response.body.data.plans.length > 0) {
        const plan = response.body.data.plans[0];
        expect(plan).toHaveProperty('id');
        expect(plan).toHaveProperty('title');
        expect(plan).toHaveProperty('status');
        expect(plan).toHaveProperty('progress');
      }
    });

    it('should filter plans by status', async () => {
      const response = await request(app)
        .get('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ status: 'active' })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);

      // 验证所有返回的计划都是active状态
      response.body.data.plans.forEach(plan => {
        expect(plan.status).toBe('active');
      });
    });

    it('should handle invalid page parameters', async () => {
      const response = await request(app)
        .get('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ page: -1, pageSize: 0 })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('GET /api/v2/learning-plans/:id', () => {
    it('should get plan details by ID', async () => {
      if (!testPlanId) {
        // 如果没有测试计划ID，先创建一个
        const createResponse = await request(app)
          .post('/api/v2/learning-plans')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            themeId: 1,
            title: '详情测试计划',
            description: '用于测试详情接口',
            targetDays: 7,
          });

        testPlanId = createResponse.body.data.id;
      }

      const response = await request(app)
        .get(`/api/v2/learning-plans/${testPlanId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('plan');
      expect(response.body.data).toHaveProperty('dailyContents');
      expect(response.body.data.plan.id).toBe(testPlanId);
    });

    it('should return 404 for non-existent plan', async () => {
      const response = await request(app)
        .get('/api/v2/learning-plans/999999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不存在');
    });

    it("should return 403 for other user's plan", async () => {
      // 创建另一个用户的token
      const otherUserToken = jwt.sign(
        { userId: 'other-user-456', role: 'user' },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1h' },
      );

      if (testPlanId) {
        const response = await request(app)
          .get(`/api/v2/learning-plans/${testPlanId}`)
          .set('Authorization', `Bearer ${otherUserToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('success', false);
      }
    });
  });

  describe('PUT /api/v2/learning-plans/:id', () => {
    it('should update plan details', async () => {
      if (!testPlanId) return;

      const updateData = {
        title: '更新后的计划标题',
        description: '更新后的计划描述',
        status: 'active',
      };

      const response = await request(app)
        .put(`/api/v2/learning-plans/${testPlanId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.description).toBe(updateData.description);
    });

    it('should validate update data', async () => {
      if (!testPlanId) return;

      const response = await request(app)
        .put(`/api/v2/learning-plans/${testPlanId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ status: 'invalid_status' })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('DELETE /api/v2/learning-plans/:id', () => {
    it('should soft delete a plan', async () => {
      if (!testPlanId) return;

      const response = await request(app)
        .delete(`/api/v2/learning-plans/${testPlanId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.message).toContain('删除成功');
    });

    it('should return 404 for already deleted plan', async () => {
      if (!testPlanId) return;

      const response = await request(app)
        .delete(`/api/v2/learning-plans/${testPlanId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('POST /api/v2/learning-plans/:id/restore', () => {
    it('should restore a soft deleted plan', async () => {
      if (!testPlanId) return;

      const response = await request(app)
        .post(`/api/v2/learning-plans/${testPlanId}/restore`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.message).toContain('恢复成功');
    });

    it('should return 400 for non-deleted plan', async () => {
      if (!testPlanId) return;

      const response = await request(app)
        .post(`/api/v2/learning-plans/${testPlanId}/restore`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('未被删除');
    });
  });

  describe('GET /api/v2/learning-plans/system/default', () => {
    it('should get system default plan', async () => {
      const response = await request(app)
        .get('/api/v2/learning-plans/system/default')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('plan');
      expect(response.body.data).toHaveProperty('tags');
      expect(response.body.data).toHaveProperty('dailyContents');
      expect(response.body.data.plan.isSystemDefault).toBe(true);
    });
  });

  describe('POST /api/v2/learning-plans/system/copy', () => {
    it('should copy system default plan to user', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans/system/copy')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.isSystemDefault).toBe(false);
      expect(response.body.data.isCurrent).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // 这个测试需要模拟数据库连接错误
      // 在实际环境中可能需要使用测试数据库或mock

      const response = await request(app)
        .get('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`);

      // 至少应该返回一个响应，不应该导致服务器崩溃
      expect(response.status).toBeDefined();
    });

    it('should handle malformed JSON requests', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    it('should handle very large request payloads', async () => {
      const largeDescription = 'x'.repeat(10000); // 10KB description

      const response = await request(app)
        .post('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          themeId: 1,
          title: '大数据测试',
          description: largeDescription,
          targetDays: 7,
        });

      // 应该要么成功处理，要么返回适当的错误
      expect([200, 201, 400, 413]).toContain(response.status);
    });
  });

  describe('Rate Limiting', () => {
    it('should handle rate limiting properly', async () => {
      // 快速发送多个请求来测试限流
      const requests = Array(10)
        .fill()
        .map(() =>
          request(app).get('/api/v2/learning-plans').set('Authorization', `Bearer ${authToken}`),
        );

      const responses = await Promise.all(requests);

      // 检查是否有请求被限流
      const rateLimitedResponses = responses.filter(res => res.status === 429);

      // 如果启用了限流，应该有一些请求被限制
      // 如果没有启用限流，所有请求都应该成功
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });
  });

  describe('Security', () => {
    it('should reject requests with invalid JWT tokens', async () => {
      const response = await request(app)
        .get('/api/v2/learning-plans')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    it('should reject requests with expired JWT tokens', async () => {
      const expiredToken = jwt.sign(
        { userId: testUserId, role: 'user' },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '-1h' }, // 已过期
      );

      const response = await request(app)
        .get('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    it('should sanitize input to prevent XSS', async () => {
      const xssPayload = {
        themeId: 1,
        title: '<script>alert("xss")</script>',
        description: '<img src="x" onerror="alert(1)">',
        targetDays: 7,
      };

      const response = await request(app)
        .post('/api/v2/learning-plans')
        .set('Authorization', `Bearer ${authToken}`)
        .send(xssPayload);

      if (response.status === 201) {
        // 如果创建成功，检查返回的数据是否已被清理
        expect(response.body.data.title).not.toContain('<script>');
        expect(response.body.data.description).not.toContain('<img');
      }
    });
  });
});
