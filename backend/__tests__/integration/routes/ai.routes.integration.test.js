const request = require('supertest');
const app = require('../../../server');
const aiService = require('../../../services/ai.service');
const jwt = require('jsonwebtoken');
const config = require('../../../config/config');

// 模拟AI服务
jest.mock('../../../services/ai.service', () => ({
  generateTags: jest.fn(),
  getUsageStats: jest.fn(),
  resetUsageStats: jest.fn()
}));

describe('AI Routes Integration Tests', () => {
  // 生成测试用JWT令牌
  const generateTestToken = (userId = 'test-user-id') => {
    return jwt.sign({ id: userId }, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn
    });
  };

  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();
  });

  describe('POST /api/v1/ai/test/tags', () => {
    it('should generate tags successfully', async () => {
      // 模拟generateTags返回值
      const mockTags = [
        { name: '倾听', relevanceScore: 0.95, sortOrder: 0 },
        { name: '表达', relevanceScore: 0.90, sortOrder: 1 },
        { name: '同理心', relevanceScore: 0.85, sortOrder: 2 }
      ];
      aiService.generateTags.mockResolvedValue(mockTags);
      aiService.getUsageStats.mockReturnValue({
        requestCount: 1,
        errorCount: 0,
        tokenUsage: { total: 150 }
      });

      // 测试数据
      const testData = {
        title: '提升与伴侣的沟通能力',
        description: '我希望能更好地与伴侣沟通，减少误解和冲突'
      };

      // 发送请求
      const response = await request(app)
        .post('/api/v1/ai/test/tags')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send(testData);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.tags).toHaveLength(3);
      expect(response.body.data.tags[0].name).toBe('倾听');
      expect(response.body.data.plan.title).toBe(testData.title);

      // 验证服务调用
      expect(aiService.generateTags).toHaveBeenCalledWith(expect.objectContaining({
        title: testData.title,
        description: testData.description
      }));
    });

    it('should handle missing title', async () => {
      // 发送请求（缺少标题）
      const response = await request(app)
        .post('/api/v1/ai/test/tags')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send({ description: '测试描述' });

      // 验证响应
      expect(response.status).toBe(422); // 使用express-validator时，验证错误返回422
      expect(response.body.success).toBe(false);
      // express-validator的错误消息可能是"请求验证失败"而不是具体的字段错误
      expect(response.body.error.message).toBeDefined();

      // 验证服务未调用
      expect(aiService.generateTags).not.toHaveBeenCalled();
    });

    it('should handle AI service errors', async () => {
      // 模拟AI服务错误
      aiService.generateTags.mockRejectedValue(new Error('API调用失败'));

      // 测试数据
      const testData = {
        title: '测试计划',
        description: '测试描述'
      };

      // 发送请求
      const response = await request(app)
        .post('/api/v1/ai/test/tags')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send(testData);

      // 验证响应
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('测试标签生成失败');

      // 验证服务调用
      expect(aiService.generateTags).toHaveBeenCalled();
    });
  });

  describe('GET /api/v1/ai/stats', () => {
    it('should return AI usage statistics', async () => {
      // 模拟getUsageStats返回值
      aiService.getUsageStats.mockReturnValue({
        requestCount: 10,
        errorCount: 1,
        tokenUsage: {
          prompt: 1200,
          completion: 800,
          total: 2000
        },
        errorRate: 0.1
      });

      // 发送请求
      const response = await request(app)
        .get('/api/v1/ai/stats')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.stats.requestCount).toBe(10);
      expect(response.body.data.stats.errorCount).toBe(1);
      expect(response.body.data.stats.tokenUsage.total).toBe(2000);
      expect(response.body.data.stats.estimatedCost).toBeDefined();

      // 验证服务调用
      expect(aiService.getUsageStats).toHaveBeenCalled();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/ai/stats');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);

      // 验证服务未调用
      expect(aiService.getUsageStats).not.toHaveBeenCalled();
    });
  });

  describe('POST /api/v1/ai/stats/reset', () => {
    it('should reset AI usage statistics', async () => {
      // 发送请求
      const response = await request(app)
        .post('/api/v1/ai/stats/reset')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('已重置');

      // 验证服务调用
      expect(aiService.resetUsageStats).toHaveBeenCalled();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .post('/api/v1/ai/stats/reset');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);

      // 验证服务未调用
      expect(aiService.resetUsageStats).not.toHaveBeenCalled();
    });
  });
});
