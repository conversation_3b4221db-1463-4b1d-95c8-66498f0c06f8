const request = require('supertest');
const app = require('../../../server');
const { BubbleInteraction, BubbleContent, Tag, User } = require('../../../models');
const { generateTestToken } = require('../../testUtils');

// 模拟数据库模型
jest.mock('../../../models');

describe('Bubble Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/v1/bubble/interactions', () => {
    it('should record bubble interaction', async () => {
      // 模拟数据库操作
      Tag.findByPk = jest.fn().mockResolvedValue({
        id: 1,
        name: '倾听',
        plan_id: 1
      });

      BubbleInteraction.create = jest.fn().mockResolvedValue({
        id: 1,
        user_id: 1,
        tag_id: 1,
        interaction_type: 'click',
        created_at: new Date()
      });

      // 模拟事务
      const mockTransaction = {
        commit: jest.fn().mockResolvedValue(),
        rollback: jest.fn().mockResolvedValue()
      };
      const sequelize = require('../../../config/database').sequelize;
      sequelize.transaction = jest.fn().mockResolvedValue(mockTransaction);
      sequelize.literal = jest.fn().mockReturnValue('usage_count + 1');

      // 发送请求
      const response = await request(app)
        .post('/api/v1/bubble/interactions')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send({
          tagId: 1,
          interactionType: 'click',
          duration: 1000,
          positionX: 100,
          positionY: 200
        });

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.tagId).toBe(1);
      expect(response.body.data.interactionType).toBe('click');
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .post('/api/v1/bubble/interactions')
        .send({
          tagId: 1,
          interactionType: 'click'
        });

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should validate request body', async () => {
      // 发送请求（无效的互动类型）
      const response = await request(app)
        .post('/api/v1/bubble/interactions')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send({
          tagId: 1,
          interactionType: 'invalid'
        });

      // 验证响应
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/v1/bubble/content', () => {
    it('should get bubble content', async () => {
      // 模拟数据库操作
      Tag.findByPk = jest.fn().mockResolvedValue({
        id: 1,
        name: '倾听',
        plan_id: 1
      });

      BubbleContent.findOne = jest.fn().mockResolvedValue(null);

      // 模拟随机内容
      const mockExercise = {
        id: 1,
        title: '练习1',
        description: '描述1',
        difficulty: 'beginner',
        time_estimate: 10
      };

      const sequelize = require('../../../config/database').sequelize;
      sequelize.random = jest.fn().mockReturnValue('RAND()');

      // 模拟Exercise.findOne
      const Exercise = require('../../../models').Exercise;
      Exercise.findOne = jest.fn().mockResolvedValue(mockExercise);

      // 发送请求
      const response = await request(app)
        .get('/api/v1/bubble/content')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .query({ tagId: 1 });

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.contentType).toBeDefined();
      expect(response.body.data.content).toBeDefined();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/bubble/content')
        .query({ tagId: 1 });

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should validate query parameters', async () => {
      // 发送请求（无标签ID）
      const response = await request(app)
        .get('/api/v1/bubble/content')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  // 可以添加更多测试...
});
