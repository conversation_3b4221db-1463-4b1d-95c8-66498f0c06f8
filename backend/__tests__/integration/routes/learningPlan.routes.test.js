const request = require('supertest');
const app = require('../../../server');
const { LearningPlan, Theme, Tag } = require('../../../models');
const aiService = require('../../../services/ai.service');
const jwt = require('jsonwebtoken');
const config = require('../../../config/config');

// 模拟数据库模型
jest.mock('../../../models', () => {
  const mockSequelize = {
    transaction: jest.fn().mockResolvedValue({
      commit: jest.fn().mockResolvedValue(),
      rollback: jest.fn().mockResolvedValue()
    })
  };

  return {
    LearningPlan: {
      findOne: jest.fn(),
      findAll: jest.fn(),
      findAndCountAll: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn()
    },
    Theme: {
      findOne: jest.fn()
    },
    Tag: {
      create: jest.fn(),
      findAll: jest.fn()
    },
    sequelize: mockSequelize
  };
});

// 模拟AI服务
jest.mock('../../../services/ai.service', () => ({
  generateTags: jest.fn()
}));

describe('Learning Plan Routes Integration Tests', () => {
  // 生成测试用JWT令牌
  const testUserId = 'test-user-id';
  const generateTestToken = (userId = testUserId) => {
    return jwt.sign({ id: userId }, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn
    });
  };

  // 模拟请求对象中的用户信息
  const mockReqUser = (req, res, next) => {
    if (req.headers && req.headers.authorization) {
      req.user = { userId: testUserId };
    }
    next();
  };

  // 添加中间件模拟
  jest.mock('../../../middlewares/auth.middleware', () => ({
    authenticateJWT: (req, res, next) => {
      if (req.headers && req.headers.authorization) {
        req.user = { userId: testUserId };
        next();
      } else {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问'
          }
        });
      }
    }
  }));

  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();
  });

  describe('POST /api/v1/learning-plans', () => {
    it('should create a learning plan successfully', async () => {
      // 模拟主题查询结果
      Theme.findOne.mockResolvedValue({
        id: 1,
        name: '人际沟通',
        is_active: true
      });

      // 模拟学习计划计数
      LearningPlan.count.mockResolvedValue(0);

      // 模拟学习计划创建结果
      const mockLearningPlan = {
        id: 'plan-1',
        user_id: 'test-user-id',
        theme_id: 1,
        title: '提升与伴侣的沟通能力',
        description: '我希望能更好地与伴侣沟通，减少误解和冲突',
        target_days: 14,
        status: 'not_started',
        start_date: new Date(),
        end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        is_current: true,
        progress: 0,
        created_at: new Date()
      };

      LearningPlan.create.mockResolvedValue(mockLearningPlan);

      // 模拟AI标签生成结果
      const mockTags = [
        { name: '倾听', relevanceScore: 0.95, sortOrder: 0 },
        { name: '表达', relevanceScore: 0.90, sortOrder: 1 },
        { name: '同理心', relevanceScore: 0.85, sortOrder: 2 }
      ];

      aiService.generateTags.mockResolvedValue(mockTags);

      // 模拟标签创建结果
      const createdTags = mockTags.map((tag, index) => ({
        id: `tag-${index + 1}`,
        name: tag.name,
        plan_id: 'plan-1',
        relevance_score: tag.relevanceScore,
        sort_order: tag.sortOrder
      }));

      Tag.create.mockImplementation((data) => {
        const index = mockTags.findIndex(t => t.name === data.name);
        return Promise.resolve(createdTags[index]);
      });

      // 测试数据
      const testData = {
        themeId: 1,
        title: '提升与伴侣的沟通能力',
        description: '我希望能更好地与伴侣沟通，减少误解和冲突',
        targetDays: 14
      };

      // 发送请求
      const response = await request(app)
        .post('/api/v1/learning-plans')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send(testData);

      // 验证响应
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.plan.title).toBe(testData.title);
      expect(response.body.data.tags).toHaveLength(3);

      // 验证服务调用
      expect(Theme.findOne).toHaveBeenCalledWith({
        where: { id: testData.themeId, is_active: true }
      });
      expect(LearningPlan.create).toHaveBeenCalled();
      expect(aiService.generateTags).toHaveBeenCalled();
      expect(Tag.create).toHaveBeenCalledTimes(3);
    });

    it('should handle theme not found', async () => {
      // 模拟主题查询结果 - 未找到
      Theme.findOne.mockResolvedValue(null);

      // 测试数据
      const testData = {
        themeId: 999,
        title: '测试计划',
        description: '测试描述',
        targetDays: 7
      };

      // 发送请求
      const response = await request(app)
        .post('/api/v1/learning-plans')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send(testData);

      // 验证响应
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('主题不存在');

      // 验证未创建学习计划
      expect(LearningPlan.create).not.toHaveBeenCalled();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .post('/api/v1/learning-plans')
        .send({
          themeId: 1,
          title: '测试计划',
          description: '测试描述'
        });

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);

      // 验证未查询主题
      expect(Theme.findOne).not.toHaveBeenCalled();
    });

    it('should validate required fields', async () => {
      // 发送请求（缺少标题）
      const response = await request(app)
        .post('/api/v1/learning-plans')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .send({
          themeId: 1,
          description: '测试描述'
        });

      // 验证响应
      expect(response.status).toBe(422); // 使用express-validator时，验证错误返回422
      expect(response.body.success).toBe(false);
      // express-validator的错误消息可能是"请求验证失败"而不是具体的字段错误
      expect(response.body.error.message).toBeDefined();

      // 验证未查询主题
      expect(Theme.findOne).not.toHaveBeenCalled();
    });
  });

  describe('GET /api/v1/learning-plans', () => {
    it('should return learning plans with pagination', async () => {
      // 模拟学习计划查询结果
      const mockPlans = [
        {
          id: 'plan-1',
          theme_id: 1,
          title: '提升与伴侣的沟通能力',
          description: '我希望能更好地与伴侣沟通，减少误解和冲突',
          progress: 30,
          status: 'in_progress',
          start_date: new Date(),
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          is_current: true,
          created_at: new Date(),
          theme: {
            name: '人际沟通'
          }
        },
        {
          id: 'plan-2',
          theme_id: 1,
          title: '提高团队协作能力',
          description: '我希望能更好地与团队成员协作',
          progress: 0,
          status: 'not_started',
          start_date: new Date(),
          end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          is_current: false,
          created_at: new Date(),
          theme: {
            name: '人际沟通'
          }
        }
      ];

      LearningPlan.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockPlans
      });

      // 发送请求
      const response = await request(app)
        .get('/api/v1/learning-plans')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.plans).toHaveLength(2);
      expect(response.body.data.pagination.total).toBe(2);

      // 验证服务调用
      expect(LearningPlan.findAndCountAll).toHaveBeenCalled();
    });

    it('should filter plans by theme and status', async () => {
      // 模拟学习计划查询结果
      const mockPlans = [
        {
          id: 'plan-1',
          theme_id: 1,
          title: '提升与伴侣的沟通能力',
          description: '我希望能更好地与伴侣沟通，减少误解和冲突',
          progress: 30,
          status: 'in_progress',
          start_date: new Date(),
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          is_current: true,
          created_at: new Date(),
          theme: {
            name: '人际沟通'
          }
        }
      ];

      LearningPlan.findAndCountAll.mockResolvedValue({
        count: 1,
        rows: mockPlans
      });

      // 发送请求
      const response = await request(app)
        .get('/api/v1/learning-plans?themeId=1&status=in_progress')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.plans).toHaveLength(1);
      expect(response.body.data.plans[0].status).toBe('in_progress');

      // 验证服务调用
      expect(LearningPlan.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            theme_id: "1", // Express查询参数会被转为字符串
            status: 'in_progress'
          })
        })
      );
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/learning-plans');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);

      // 验证未查询学习计划
      expect(LearningPlan.findAndCountAll).not.toHaveBeenCalled();
    });
  });

  describe('GET /api/v1/learning-plans/:id', () => {
    it('should return learning plan details with tags', async () => {
      // 模拟学习计划查询结果
      const mockPlan = {
        id: 'plan-1',
        user_id: 'test-user-id',
        theme_id: 1,
        title: '提升与伴侣的沟通能力',
        description: '我希望能更好地与伴侣沟通，减少误解和冲突',
        target_days: 14,
        completed_days: 4,
        progress: 30,
        status: 'in_progress',
        start_date: new Date(),
        end_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
        is_current: true,
        created_at: new Date(),
        theme: {
          name: '人际沟通'
        },
        tags: [
          {
            id: 'tag-1',
            name: '倾听',
            relevance_score: 0.95,
            sort_order: 0
          },
          {
            id: 'tag-2',
            name: '表达',
            relevance_score: 0.90,
            sort_order: 1
          },
          {
            id: 'tag-3',
            name: '同理心',
            relevance_score: 0.85,
            sort_order: 2
          }
        ]
      };

      LearningPlan.findOne.mockResolvedValue(mockPlan);

      // 发送请求
      const response = await request(app)
        .get('/api/v1/learning-plans/plan-1')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.plan.id).toBe('plan-1');
      expect(response.body.data.tags).toHaveLength(3);
      expect(response.body.data.statistics).toBeDefined();

      // 验证服务调用
      expect(LearningPlan.findOne).toHaveBeenCalled();
      // 由于模拟的复杂性，我们不检查具体的参数
    });

    it('should handle plan not found', async () => {
      // 模拟学习计划查询结果 - 未找到
      LearningPlan.findOne.mockResolvedValue(null);

      // 发送请求
      const response = await request(app)
        .get('/api/v1/learning-plans/non-existent-plan')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('学习计划不存在');
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/learning-plans/plan-1');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);

      // 验证未查询学习计划
      expect(LearningPlan.findOne).not.toHaveBeenCalled();
    });
  });

  describe('PUT /api/v1/learning-plans/:id/activate', () => {
    it('should activate a learning plan successfully', async () => {
      // 模拟学习计划查询结果
      const mockPlan = {
        id: 'plan-1',
        user_id: 'test-user-id',
        is_current: false,
        update: jest.fn().mockResolvedValue(true)
      };

      LearningPlan.findOne.mockResolvedValue(mockPlan);
      LearningPlan.update.mockResolvedValue([1]); // 更新了1条记录

      // 发送请求
      const response = await request(app)
        .put('/api/v1/learning-plans/plan-1/activate')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.planId).toBe('plan-1');
      expect(response.body.data.isCurrent).toBe(true);

      // 验证服务调用
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { id: 'plan-1', user_id: 'test-user-id' }
      });
      expect(LearningPlan.update).toHaveBeenCalledWith(
        { is_current: false },
        {
          where: { user_id: 'test-user-id' },
          transaction: expect.anything()
        }
      );
      expect(mockPlan.update).toHaveBeenCalledWith(
        { is_current: true },
        { transaction: expect.anything() }
      );
    });

    it('should handle plan not found', async () => {
      // 模拟学习计划查询结果 - 未找到
      LearningPlan.findOne.mockResolvedValue(null);

      // 发送请求
      const response = await request(app)
        .put('/api/v1/learning-plans/non-existent-plan/activate')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('学习计划不存在');

      // 验证未更新任何计划
      expect(LearningPlan.update).not.toHaveBeenCalled();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .put('/api/v1/learning-plans/plan-1/activate');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);

      // 验证未查询学习计划
      expect(LearningPlan.findOne).not.toHaveBeenCalled();
    });
  });
});
