const request = require('supertest');
const app = require('../../../server');
const { Note, Tag, User, NoteLike, LearningPlan, LearningActivity } = require('../../../models');
const { generateTestToken } = require('../../testUtils');

// 模拟数据库模型
jest.mock('../../../models');

describe('Square Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/square/notes', () => {
    it('should get square notes', async () => {
      // 模拟数据库操作
      const mockNotes = {
        count: 2,
        rows: [
          {
            id: 1,
            tag_id: 1,
            user_id: 2,
            title: '笔记1',
            content: '内容1',
            image_url: 'image1.jpg',
            likes: 10,
            comments: 5,
            is_ai_generated: false,
            created_at: new Date(),
            tag: {
              id: 1,
              name: '倾听'
            },
            user: {
              id: 2,
              nickname: '用户2',
              avatar_url: 'avatar2.jpg'
            }
          },
          {
            id: 2,
            tag_id: 2,
            user_id: 3,
            title: '笔记2',
            content: '内容2',
            image_url: 'image2.jpg',
            likes: 20,
            comments: 8,
            is_ai_generated: true,
            created_at: new Date(),
            tag: {
              id: 2,
              name: '表达'
            },
            user: {
              id: 3,
              nickname: '用户3',
              avatar_url: 'avatar3.jpg'
            }
          }
        ]
      };

      const mockLikedNotes = [
        { note_id: 1 }
      ];

      const mockActivePlan = {
        id: 1,
        title: '学习计划1'
      };

      Note.findAndCountAll = jest.fn().mockResolvedValue(mockNotes);
      NoteLike.findAll = jest.fn().mockResolvedValue(mockLikedNotes);
      LearningPlan.findOne = jest.fn().mockResolvedValue(mockActivePlan);
      LearningActivity.create = jest.fn().mockResolvedValue({});

      // 发送请求
      const response = await request(app)
        .get('/api/v1/square/notes')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .query({
          tagId: 'all',
          page: 1,
          pageSize: 10,
          sortBy: 'latest'
        });

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.notes).toHaveLength(2);
      expect(response.body.data.pagination).toBeDefined();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/square/notes');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/v1/square/tags', () => {
    it('should get square tags', async () => {
      // 模拟数据库操作
      const mockActivePlan = {
        id: 1,
        title: '学习计划1'
      };

      const mockTags = [
        {
          id: 1,
          name: '倾听',
          weight: 0.9,
          usage_count: 15,
          is_verified: true
        },
        {
          id: 2,
          name: '表达',
          weight: 0.8,
          usage_count: 10,
          is_verified: false
        }
      ];

      const mockNoteCounts = [
        {
          tag_id: 1,
          getDataValue: jest.fn().mockReturnValue('5')
        },
        {
          tag_id: 2,
          getDataValue: jest.fn().mockReturnValue('3')
        }
      ];

      LearningPlan.findOne = jest.fn().mockResolvedValue(mockActivePlan);
      Tag.findAll = jest.fn().mockResolvedValue(mockTags);
      Note.findAll = jest.fn().mockResolvedValue(mockNoteCounts);
      Note.count = jest.fn().mockResolvedValue(8);

      // 发送请求
      const response = await request(app)
        .get('/api/v1/square/tags')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.planId).toBe(1);
      expect(response.body.data.planTitle).toBe('学习计划1');
      expect(response.body.data.tags).toBeDefined();
      expect(response.body.data.tags.length).toBeGreaterThan(0);
    });

    it('should handle no active plan', async () => {
      // 模拟数据库操作
      LearningPlan.findOne = jest.fn().mockResolvedValue(null);

      // 发送请求
      const response = await request(app)
        .get('/api/v1/square/tags')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('未找到活跃的学习计划');
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/square/tags');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  // 可以添加更多测试...
});
