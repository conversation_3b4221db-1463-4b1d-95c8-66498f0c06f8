const request = require('supertest');
const app = require('../../../server');
const { LearningActivity, DailyRecord, LearningPlan } = require('../../../models');
const { generateTestToken } = require('../../testUtils');

// 模拟数据库模型
jest.mock('../../../models');

describe('Statistics Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/statistics/learning', () => {
    it('should get learning statistics', async () => {
      // 模拟数据库操作
      const mockActivities = [
        {
          activity_type: 'complete_exercise',
          getDataValue: jest.fn().mockReturnValue('10')
        },
        {
          activity_type: 'view_insight',
          getDataValue: jest.fn().mockReturnValue('20')
        }
      ];

      const mockDailyRecords = [
        {
          date: '2023-06-15',
          time_spent: 30
        },
        {
          date: '2023-06-14',
          time_spent: 45
        }
      ];

      LearningActivity.findAll = jest.fn().mockResolvedValue(mockActivities);
      DailyRecord.findAll = jest.fn().mockResolvedValue(mockDailyRecords);
      LearningPlan.count = jest.fn()
        .mockResolvedValueOnce(2) // activePlans
        .mockResolvedValueOnce(1); // completedPlans

      // 发送请求
      const response = await request(app)
        .get('/api/v1/statistics/learning')
        .set('Authorization', `Bearer ${generateTestToken()}`);

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.totalStudyDays).toBeDefined();
      expect(response.body.data.completedExercises).toBeDefined();
      expect(response.body.data.viewedInsights).toBeDefined();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/statistics/learning');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/v1/statistics/daily', () => {
    it('should get daily records', async () => {
      // 模拟数据库操作
      const mockRecords = [
        {
          date: '2023-06-15',
          time_spent: 30,
          exercises_completed: 2,
          insights_viewed: 5,
          notes_created: 1,
          bubble_interactions: 10,
          has_activity: true
        },
        {
          date: '2023-06-14',
          time_spent: 45,
          exercises_completed: 3,
          insights_viewed: 7,
          notes_created: 0,
          bubble_interactions: 5,
          has_activity: true
        }
      ];

      DailyRecord.findAll = jest.fn().mockResolvedValue(mockRecords);

      // 发送请求
      const response = await request(app)
        .get('/api/v1/statistics/daily')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .query({
          startDate: '2023-06-01',
          endDate: '2023-06-30'
        });

      // 验证响应
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.records).toHaveLength(2);
      expect(response.body.data.records[0].date).toBeDefined();
      expect(response.body.data.records[0].timeSpent).toBeDefined();
    });

    it('should require authentication', async () => {
      // 发送请求（无认证）
      const response = await request(app)
        .get('/api/v1/statistics/daily');

      // 验证响应
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should validate query parameters', async () => {
      // 发送请求（无效的日期格式）
      const response = await request(app)
        .get('/api/v1/statistics/daily')
        .set('Authorization', `Bearer ${generateTestToken()}`)
        .query({
          startDate: 'invalid-date'
        });

      // 验证响应
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  // 可以添加更多测试...
});
