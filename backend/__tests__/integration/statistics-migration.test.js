/**
 * 统计模块迁移测试
 * 测试新旧API的兼容性和功能
 */

const request = require('supertest');
const app = require('../../server');
const db = require('../../models');
const { User, LearningActivity } = db;

describe('统计模块迁移测试', () => {
  let testUser;
  let authToken;

  // 在所有测试前创建测试用户和活动数据
  beforeAll(async () => {
    // 清理测试数据
    await LearningActivity.destroy({ where: {} });
    await User.destroy({ where: { username: 'test_migration_user' } });

    // 创建测试用户
    testUser = await User.create({
      username: 'test_migration_user',
      password: 'password123',
      email: '<EMAIL>'
    });

    // 登录获取token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'test_migration_user',
        password: 'password123'
      });

    authToken = loginResponse.body.data.token;

    // 创建测试活动数据
    const activities = [
      {
        userId: testUser.id,
        activityType: 'complete_exercise',
        contentType: 'exercise',
        contentId: 1,
        duration: 300,
        createdAt: new Date(Date.now() - 86400000 * 5) // 5天前
      },
      {
        userId: testUser.id,
        activityType: 'view_insight',
        contentType: 'insight',
        contentId: 2,
        duration: 120,
        createdAt: new Date(Date.now() - 86400000 * 3) // 3天前
      },
      {
        userId: testUser.id,
        activityType: 'create_note',
        contentType: 'note',
        contentId: 3,
        duration: 180,
        createdAt: new Date(Date.now() - 86400000 * 1) // 1天前
      }
    ];

    await LearningActivity.bulkCreate(activities);
  });

  // 在所有测试后清理测试数据
  afterAll(async () => {
    await LearningActivity.destroy({ where: { userId: testUser.id } });
    await User.destroy({ where: { id: testUser.id } });
  });

  // 测试旧版API
  describe('旧版API测试', () => {
    test('获取学习统计数据', async () => {
      const response = await request(app)
        .get('/api/statistics/learning')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalStudyDays).toBeDefined();
    });

    test('获取每日学习记录', async () => {
      const response = await request(app)
        .get('/api/statistics/daily')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.records).toBeDefined();
    });

    test('记录学习活动', async () => {
      const response = await request(app)
        .post('/api/statistics/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          activityType: 'view_insight',
          contentType: 'insight',
          contentId: 5,
          duration: 60
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  // 测试新版API
  describe('新版API测试', () => {
    test('获取学习统计数据', async () => {
      const response = await request(app)
        .get('/api/statistics/learning')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalStudyDays).toBeDefined();
    });

    test('获取每日学习记录', async () => {
      const response = await request(app)
        .get('/api/statistics/daily')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.records).toBeDefined();
    });

    test('记录学习活动', async () => {
      const response = await request(app)
        .post('/api/statistics/activities')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          activityType: 'view_insight',
          contentType: 'insight',
          contentId: 6,
          duration: 60
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('获取学习活动列表', async () => {
      const response = await request(app)
        .get('/api/statistics/activities')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.activities).toBeDefined();
      expect(Array.isArray(response.body.data.activities)).toBe(true);
    });

    test('获取学习概览', async () => {
      const response = await request(app)
        .get('/api/statistics/overview')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.statistics).toBeDefined();
      expect(response.body.data.recentTrend).toBeDefined();
    });

    test('获取学习趋势', async () => {
      const response = await request(app)
        .get('/api/statistics/trend?days=30')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.trend).toBeDefined();
      expect(Array.isArray(response.body.data.trend)).toBe(true);
    });
  });

  // 测试适配器功能
  describe('适配器功能测试', () => {
    test('新旧API返回数据结构兼容', async () => {
      // 获取旧版API数据
      const oldResponse = await request(app)
        .get('/api/statistics/learning')
        .set('Authorization', `Bearer ${authToken}`);

      // 获取新版API数据
      const newResponse = await request(app)
        .get('/api/statistics/learning')
        .set('Authorization', `Bearer ${authToken}`);

      // 验证基本结构一致
      expect(oldResponse.body.success).toBe(newResponse.body.success);
      expect(typeof oldResponse.body.data).toBe(typeof newResponse.body.data);

      // 验证关键字段一致
      const oldData = oldResponse.body.data;
      const newData = newResponse.body.data;

      expect(oldData.totalStudyDays).toBe(newData.totalStudyDays);
      expect(oldData.currentStreak).toBe(newData.currentStreak);
      expect(oldData.longestStreak).toBe(newData.longestStreak);
    });
  });
});
