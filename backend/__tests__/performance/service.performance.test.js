/**
 * 服务层性能测试
 */
const StatisticsService = require('../../services/statistics.service');

// 模拟仓库
const mockRepository = {
  getLearningActivityStats: jest.fn(),
  getLearningStreaks: jest.fn(),
  getContentCompletionStats: jest.fn(),
  getLearningPlanStats: jest.fn(),
  getTotalLearningTime: jest.fn(),
  getDailyRecords: jest.fn(),
  getOrCreateDailyRecord: jest.fn(),
  recordActivity: jest.fn(),
  updateDailyRecord: jest.fn(),
  getLearningActivities: jest.fn(),
  getLearningTrend: jest.fn()
};

// 模拟依赖
jest.mock('../../config/logger', () => ({
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../models', () => ({
  sequelize: {
    transaction: jest.fn().mockResolvedValue({
      commit: jest.fn(),
      rollback: jest.fn()
    })
  }
}));

// 性能测试函数
const measurePerformance = async (fn, iterations = 100) => {
  const startTime = process.hrtime.bigint();
  
  for (let i = 0; i < iterations; i++) {
    await fn();
  }
  
  const endTime = process.hrtime.bigint();
  const duration = Number(endTime - startTime) / 1_000_000; // 转换为毫秒
  
  return {
    totalTime: duration,
    averageTime: duration / iterations
  };
};

describe('Service Layer Performance Test', () => {
  let service;
  
  beforeEach(() => {
    jest.clearAllMocks();
    service = new StatisticsService(mockRepository);
    
    // 设置模拟返回值
    mockRepository.getLearningActivityStats.mockResolvedValue([
      { activity_type: 'login', dataValues: { count: '10' } },
      { activity_type: 'view_exercise', dataValues: { count: '20' } }
    ]);
    mockRepository.getLearningStreaks.mockResolvedValue({ currentStreak: 3, longestStreak: 7 });
    mockRepository.getContentCompletionStats.mockResolvedValue({
      completedExercises: 25,
      viewedInsights: 42,
      createdNotes: 10,
      bubbleInteractions: 15
    });
    mockRepository.getLearningPlanStats.mockResolvedValue({
      activePlans: 2,
      completedPlans: 3,
      totalPlans: 5
    });
    mockRepository.getTotalLearningTime.mockResolvedValue(120);
    mockRepository.getDailyRecords.mockResolvedValue({
      count: 15,
      records: []
    });
  });
  
  test('Performance: getLearningStatistics', async () => {
    console.log('Running performance test for getLearningStatistics...');
    
    // 测试性能
    const performance = await measurePerformance(async () => {
      await service.getLearningStatistics('user123');
    }, 1000);
    
    console.log(`getLearningStatistics performance: ${performance.averageTime.toFixed(2)}ms per call`);
    console.log(`Total time for 1000 calls: ${performance.totalTime.toFixed(2)}ms`);
    
    // 验证调用
    expect(mockRepository.getLearningActivityStats).toHaveBeenCalledWith('user123');
    expect(mockRepository.getLearningStreaks).toHaveBeenCalledWith('user123');
    expect(mockRepository.getContentCompletionStats).toHaveBeenCalledWith('user123');
    expect(mockRepository.getLearningPlanStats).toHaveBeenCalledWith('user123');
    expect(mockRepository.getTotalLearningTime).toHaveBeenCalledWith('user123');
    expect(mockRepository.getDailyRecords).toHaveBeenCalledWith('user123');
    
    // 验证性能
    expect(performance.averageTime).toBeLessThan(1); // 平均每次调用应该小于1毫秒
  });
  
  test('Performance: recordLearningActivity', async () => {
    console.log('Running performance test for recordLearningActivity...');
    
    // 设置模拟返回值
    const mockActivity = {
      id: 123,
      user_id: 'user123',
      activity_type: 'complete_exercise',
      content_id: 456,
      content_type: 'exercise',
      created_at: new Date()
    };
    
    const mockDailyRecord = {
      id: 789,
      user_id: 'user123',
      date: new Date(),
      time_spent: 30,
      exercises_completed: 2,
      insights_viewed: 5,
      notes_created: 1,
      bubble_interactions: 3,
      has_activity: true
    };
    
    mockRepository.recordActivity.mockResolvedValue(mockActivity);
    mockRepository.getOrCreateDailyRecord.mockResolvedValue({
      record: mockDailyRecord,
      created: false
    });
    
    // 测试性能
    const performance = await measurePerformance(async () => {
      await service.recordLearningActivity(
        'user123',
        'complete_exercise',
        {
          content_id: 456,
          content_type: 'exercise'
        }
      );
    }, 1000);
    
    console.log(`recordLearningActivity performance: ${performance.averageTime.toFixed(2)}ms per call`);
    console.log(`Total time for 1000 calls: ${performance.totalTime.toFixed(2)}ms`);
    
    // 验证调用
    expect(mockRepository.recordActivity).toHaveBeenCalled();
    expect(mockRepository.getOrCreateDailyRecord).toHaveBeenCalled();
    expect(mockRepository.updateDailyRecord).toHaveBeenCalled();
    
    // 验证性能
    expect(performance.averageTime).toBeLessThan(1); // 平均每次调用应该小于1毫秒
  });
});
