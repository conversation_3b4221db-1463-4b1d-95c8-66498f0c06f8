/**
 * Jest测试设置文件
 * 在每个测试文件执行前运行
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
process.env.DB_HOST = 'localhost';
process.env.DB_NAME = 'aibubb_test';
process.env.DB_USER = 'test_user';
process.env.DB_PASSWORD = 'test_password';
process.env.REDIS_URL = 'redis://localhost:6379/1';
process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出

// 设置测试超时
jest.setTimeout(30000);

// 全局测试前置设置
beforeAll(async () => {
  // 这里可以添加全局测试前置逻辑
  console.log('🧪 开始运行测试套件');
});

// 全局测试后置清理
afterAll(async () => {
  // 这里可以添加全局测试后置清理逻辑
  console.log('✅ 测试套件运行完成');
});

// 每个测试前的设置
beforeEach(() => {
  // 清除所有模拟
  jest.clearAllMocks();
});

// 每个测试后的清理
afterEach(() => {
  // 这里可以添加每个测试后的清理逻辑
});

// 模拟外部依赖
jest.mock('../config/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

// 模拟数据库连接
jest.mock('../config/database', () => ({
  authenticate: jest.fn().mockResolvedValue(true),
  transaction: jest.fn().mockImplementation((callback) => {
    const mockTransaction = {
      commit: jest.fn().mockResolvedValue(),
      rollback: jest.fn().mockResolvedValue()
    };
    return callback(mockTransaction);
  }),
  close: jest.fn().mockResolvedValue()
}));

// 模拟Redis连接
jest.mock('../config/redis', () => ({
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  expire: jest.fn()
}));

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('测试中发现未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('测试中发现未捕获的异常:', error);
});