/**
 * 广场控制器 V2 测试
 */
const squareController = require('../../../controllers/squareV2.controller');
const squareService = require('../../../services/square.service');
const apiResponse = require('../../../utils/apiResponse');
const logger = require('../../../config/logger');

// 模拟依赖
jest.mock('../../../services/square.service');
jest.mock('../../../utils/apiResponse');
jest.mock('../../../config/logger');

describe('Square Controller V2', () => {
  // 在每个测试前重置所有模拟
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getSquareNotes', () => {
    it('should return square notes successfully', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' },
        query: {
          tagId: 'all',
          page: '1',
          pageSize: '10',
          sortBy: 'latest'
        }
      };
      const res = {};

      // 模拟服务返回数据
      const mockServiceResult = {
        notes: [
          {
            id: 1,
            tagId: 1,
            tagName: 'Tag 1',
            userId: 'user1',
            userName: 'User 1',
            userAvatar: 'avatar1.jpg',
            title: 'Note 1',
            content: 'Content 1',
            imageUrl: 'image1.jpg',
            likes: 10,
            comments: 5,
            isLiked: true,
            isAiGenerated: false,
            createdAt: '2023-01-01T00:00:00Z'
          }
        ],
        pagination: {
          total: 1,
          page: 1,
          pageSize: 10,
          totalPages: 1
        }
      };
      squareService.getSquareNotes.mockResolvedValue(mockServiceResult);

      // 调用控制器方法
      await squareController.getSquareNotes(req, res);

      // 验证服务方法调用
      expect(squareService.getSquareNotes).toHaveBeenCalledWith(
        'user1',
        { tagId: 'all' },
        '1',
        '10',
        'latest'
      );

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, mockServiceResult);
    });

    it('should handle anonymous user', async () => {
      // 模拟请求和响应对象
      const req = {
        user: null,
        query: {
          tagId: 'all',
          page: '1',
          pageSize: '10',
          sortBy: 'latest'
        }
      };
      const res = {};

      // 模拟服务返回数据
      const mockServiceResult = {
        notes: [],
        pagination: {
          total: 0,
          page: 1,
          pageSize: 10,
          totalPages: 0
        }
      };
      squareService.getSquareNotes.mockResolvedValue(mockServiceResult);

      // 调用控制器方法
      await squareController.getSquareNotes(req, res);

      // 验证服务方法调用
      expect(squareService.getSquareNotes).toHaveBeenCalledWith(
        null,
        { tagId: 'all' },
        '1',
        '10',
        'latest'
      );

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, mockServiceResult);
    });

    it('should handle errors', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' },
        query: {
          tagId: 'all',
          page: '1',
          pageSize: '10',
          sortBy: 'latest'
        }
      };
      const res = {};

      // 模拟服务抛出错误
      const error = new Error('Service error');
      squareService.getSquareNotes.mockRejectedValue(error);

      // 调用控制器方法
      await squareController.getSquareNotes(req, res);

      // 验证错误处理
      expect(logger.error).toHaveBeenCalledWith(`获取广场笔记列表失败: ${error.message}`);
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '获取广场笔记列表失败',
        'SERVER_ERROR',
        500
      );
    });
  });

  describe('getSquareTags', () => {
    it('should return square tags successfully', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' }
      };
      const res = {};

      // 模拟服务返回数据
      const mockServiceResult = {
        planId: 1,
        planTitle: 'Plan 1',
        tags: [
          {
            id: 'all',
            name: '推荐',
            weight: 1,
            isPrimary: true,
            relevanceScore: 1,
            sortOrder: 0,
            usageCount: 0,
            isVerified: true,
            noteCount: 15
          },
          {
            id: 1,
            name: 'Tag 1',
            weight: 0.8,
            isPrimary: true,
            relevanceScore: 0.9,
            sortOrder: 1,
            usageCount: 5,
            isVerified: true,
            noteCount: 10
          }
        ]
      };
      squareService.getSquareTags.mockResolvedValue(mockServiceResult);

      // 调用控制器方法
      await squareController.getSquareTags(req, res);

      // 验证服务方法调用
      expect(squareService.getSquareTags).toHaveBeenCalledWith('user1');

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, mockServiceResult);
    });

    it('should handle anonymous user', async () => {
      // 模拟请求和响应对象
      const req = {
        user: null
      };
      const res = {};

      // 模拟服务返回数据
      const mockServiceResult = {
        planId: 1,
        planTitle: 'Plan 1',
        tags: []
      };
      squareService.getSquareTags.mockResolvedValue(mockServiceResult);

      // 调用控制器方法
      await squareController.getSquareTags(req, res);

      // 验证服务方法调用
      expect(squareService.getSquareTags).toHaveBeenCalledWith(null);

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, mockServiceResult);
    });

    it('should handle not found error', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' }
      };
      const res = {};

      // 模拟服务抛出特定错误
      const error = new Error('未找到系统默认学习计划');
      squareService.getSquareTags.mockRejectedValue(error);

      // 调用控制器方法
      await squareController.getSquareTags(req, res);

      // 验证错误处理
      expect(logger.error).toHaveBeenCalledWith(`获取广场标签列表失败: ${error.message}`);
      expect(apiResponse.notFound).toHaveBeenCalledWith(res, '未找到系统默认学习计划');
    });

    it('should handle general errors', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' }
      };
      const res = {};

      // 模拟服务抛出一般错误
      const error = new Error('Service error');
      squareService.getSquareTags.mockRejectedValue(error);

      // 调用控制器方法
      await squareController.getSquareTags(req, res);

      // 验证错误处理
      expect(logger.error).toHaveBeenCalledWith(`获取广场标签列表失败: ${error.message}`);
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '获取广场标签列表失败',
        'SERVER_ERROR',
        500
      );
    });
  });

  describe('getRecommendedNotes', () => {
    it('should return recommended notes successfully', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' },
        query: {
          limit: '5'
        }
      };
      const res = {};

      // 模拟服务返回数据
      const mockServiceResult = {
        notes: [
          {
            id: 1,
            tagId: 1,
            tagName: 'Tag 1',
            userId: 'user1',
            userName: 'User 1',
            userAvatar: 'avatar1.jpg',
            title: 'Note 1',
            content: 'Content 1',
            imageUrl: 'image1.jpg',
            likes: 10,
            comments: 5,
            isLiked: true,
            isAiGenerated: false,
            createdAt: '2023-01-01T00:00:00Z'
          }
        ]
      };
      squareService.getRecommendedNotes.mockResolvedValue(mockServiceResult);

      // 调用控制器方法
      await squareController.getRecommendedNotes(req, res);

      // 验证服务方法调用
      expect(squareService.getRecommendedNotes).toHaveBeenCalledWith('user1', '5');

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, mockServiceResult);
    });

    it('should handle anonymous user', async () => {
      // 模拟请求和响应对象
      const req = {
        user: null,
        query: {
          limit: '5'
        }
      };
      const res = {};

      // 模拟服务返回数据
      const mockServiceResult = {
        notes: []
      };
      squareService.getRecommendedNotes.mockResolvedValue(mockServiceResult);

      // 调用控制器方法
      await squareController.getRecommendedNotes(req, res);

      // 验证服务方法调用
      expect(squareService.getRecommendedNotes).toHaveBeenCalledWith(null, '5');

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, mockServiceResult);
    });

    it('should handle not found error', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' },
        query: {
          limit: '5'
        }
      };
      const res = {};

      // 模拟服务抛出特定错误
      const error = new Error('未找到系统默认学习计划');
      squareService.getRecommendedNotes.mockRejectedValue(error);

      // 调用控制器方法
      await squareController.getRecommendedNotes(req, res);

      // 验证错误处理
      expect(logger.error).toHaveBeenCalledWith(`获取推荐笔记失败: ${error.message}`);
      expect(apiResponse.notFound).toHaveBeenCalledWith(res, '未找到系统默认学习计划');
    });

    it('should handle general errors', async () => {
      // 模拟请求和响应对象
      const req = {
        user: { userId: 'user1' },
        query: {
          limit: '5'
        }
      };
      const res = {};

      // 模拟服务抛出一般错误
      const error = new Error('Service error');
      squareService.getRecommendedNotes.mockRejectedValue(error);

      // 调用控制器方法
      await squareController.getRecommendedNotes(req, res);

      // 验证错误处理
      expect(logger.error).toHaveBeenCalledWith(`获取推荐笔记失败: ${error.message}`);
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '获取推荐笔记失败',
        'SERVER_ERROR',
        500
      );
    });
  });
});
