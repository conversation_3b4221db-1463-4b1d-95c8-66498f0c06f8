/**
 * 标签V2控制器单元测试
 * 测试软删除相关功能
 */
const tagV2Controller = require('../../../controllers/tagV2.controller');
const apiResponse = require('../../../utils/apiResponse');
const { handleApiError, handleNotFoundError, handleBadRequestError } = require('../../../utils/errorHandler');

// 模拟依赖
jest.mock('../../../config/serviceContainer', () => ({
  getService: jest.fn().mockReturnValue({
    softDeleteTag: jest.fn(),
    restoreTag: jest.fn(),
    getDeletedTags: jest.fn()
  })
}));

jest.mock('../../../utils/apiResponse', () => ({
  success: jest.fn(),
  error: jest.fn(),
  notFound: jest.fn(),
  badRequest: jest.fn()
}));

jest.mock('../../../utils/errorHandler', () => ({
  handleApiError: jest.fn(),
  handleNotFoundError: jest.fn(),
  handleBadRequestError: jest.fn()
}));

// 获取模拟的标签服务
const serviceContainer = require('../../../config/serviceContainer');
const tagService = serviceContainer.getService('tagService');

describe('TagV2Controller', () => {
  // 在每个测试前重置模拟
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // 模拟请求和响应对象
  const req = {
    user: { userId: 'user-123' },
    params: { id: 'tag-123' },
    query: {}
  };
  const res = {};

  describe('softDeleteTag', () => {
    it('should soft delete a tag successfully', async () => {
      // 模拟标签服务返回成功
      tagService.softDeleteTag.mockResolvedValue(true);

      // 调用控制器方法
      await tagV2Controller.softDeleteTag(req, res);

      // 验证服务方法被调用
      expect(tagService.softDeleteTag).toHaveBeenCalledWith('tag-123', 'user-123');

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, {
        message: '标签已被软删除'
      });
    });

    it('should handle tag not found error', async () => {
      // 模拟标签服务抛出标签不存在错误
      tagService.softDeleteTag.mockRejectedValue(new Error('标签不存在或不属于当前用户'));

      // 调用控制器方法
      await tagV2Controller.softDeleteTag(req, res);

      // 验证错误处理
      expect(handleNotFoundError).toHaveBeenCalledWith(res, '标签不存在或不属于当前用户');
    });

    it('should handle general errors', async () => {
      // 模拟标签服务抛出一般错误
      const error = new Error('服务器错误');
      tagService.softDeleteTag.mockRejectedValue(error);

      // 调用控制器方法
      await tagV2Controller.softDeleteTag(req, res);

      // 验证错误处理
      expect(handleApiError).toHaveBeenCalledWith(error, res, 'softDeleteTag');
    });
  });

  describe('restoreTag', () => {
    it('should restore a tag successfully', async () => {
      // 模拟标签服务返回成功
      tagService.restoreTag.mockResolvedValue(true);

      // 调用控制器方法
      await tagV2Controller.restoreTag(req, res);

      // 验证服务方法被调用
      expect(tagService.restoreTag).toHaveBeenCalledWith('tag-123', 'user-123');

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, {
        message: '标签已被恢复'
      });
    });

    it('should handle tag not found error', async () => {
      // 模拟标签服务抛出标签不存在错误
      tagService.restoreTag.mockRejectedValue(new Error('标签不存在或不属于当前用户'));

      // 调用控制器方法
      await tagV2Controller.restoreTag(req, res);

      // 验证错误处理
      expect(handleNotFoundError).toHaveBeenCalledWith(res, '标签不存在或不属于当前用户');
    });

    it('should handle tag not deleted error', async () => {
      // 模拟标签服务抛出标签未删除错误
      tagService.restoreTag.mockRejectedValue(new Error('标签未被删除，无需恢复'));

      // 调用控制器方法
      await tagV2Controller.restoreTag(req, res);

      // 验证错误处理
      expect(handleBadRequestError).toHaveBeenCalledWith(res, '标签未被删除，无需恢复');
    });

    it('should handle general errors', async () => {
      // 模拟标签服务抛出一般错误
      const error = new Error('服务器错误');
      tagService.restoreTag.mockRejectedValue(error);

      // 调用控制器方法
      await tagV2Controller.restoreTag(req, res);

      // 验证错误处理
      expect(handleApiError).toHaveBeenCalledWith(error, res, 'restoreTag');
    });
  });

  describe('getDeletedTags', () => {
    it('should get deleted tags successfully', async () => {
      // 模拟请求参数
      req.query = { page: '2', pageSize: '20' };

      // 模拟标签服务返回结果
      const mockResult = {
        rows: [
          {
            id: 'tag-1',
            name: '标签1',
            relevance_score: 0.9,
            weight: 0.8,
            usage_count: 5,
            is_verified: true,
            sort_order: 1,
            deleted_at: '2023-01-01T00:00:00Z',
            category: { id: 'cat-1', name: '分类1' }
          }
        ],
        count: 1,
        totalPages: 1
      };
      tagService.getDeletedTags.mockResolvedValue(mockResult);

      // 调用控制器方法
      await tagV2Controller.getDeletedTags(req, res);

      // 验证服务方法被调用
      expect(tagService.getDeletedTags).toHaveBeenCalledWith('user-123', 2, 20);

      // 验证响应
      expect(apiResponse.success).toHaveBeenCalledWith(res, {
        tags: [
          {
            id: 'tag-1',
            name: '标签1',
            relevanceScore: 0.9,
            weight: 0.8,
            usageCount: 5,
            isVerified: true,
            sortOrder: 1,
            deletedAt: '2023-01-01T00:00:00Z',
            category: { id: 'cat-1', name: '分类1' }
          }
        ],
        count: 1,
        page: 2,
        pageSize: 20,
        totalPages: 1
      });
    });

    it('should handle general errors', async () => {
      // 模拟标签服务抛出一般错误
      const error = new Error('服务器错误');
      tagService.getDeletedTags.mockRejectedValue(error);

      // 调用控制器方法
      await tagV2Controller.getDeletedTags(req, res);

      // 验证错误处理
      expect(handleApiError).toHaveBeenCalledWith(error, res, 'getDeletedTags');
    });
  });
});
