const { authenticateJWT, optionalAuthJWT } = require('../../../middlewares/auth.middleware');
const { verifyToken, extractTokenFromHeader } = require('../../../utils/jwt');
const apiResponse = require('../../../utils/apiResponse');
const logger = require('../../../config/logger');

// 模拟依赖
jest.mock('../../../utils/jwt', () => ({
  verifyToken: jest.fn(),
  extractTokenFromHeader: jest.fn()
}));

jest.mock('../../../utils/apiResponse', () => ({
  unauthorized: jest.fn()
}));

jest.mock('../../../config/logger', () => ({
  error: jest.fn()
}));

describe('Auth Middleware', () => {
  // 模拟请求和响应对象
  let req;
  let res;
  let next;

  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();

    // 初始化请求和响应对象
    req = {
      headers: {}
    };
    res = {};
    next = jest.fn();

    // 模拟响应方法
    apiResponse.unauthorized.mockImplementation(() => res);
  });

  describe('authenticateJWT', () => {
    it('should call next() when a valid token is provided', () => {
      // 模拟token提取
      const mockToken = 'valid.jwt.token';
      extractTokenFromHeader.mockReturnValue(mockToken);

      // 模拟token验证
      const mockPayload = { userId: 'user-123', nickname: 'Test User' };
      verifyToken.mockReturnValue(mockPayload);

      // 调用中间件
      authenticateJWT(req, res, next);

      // 验证token提取
      expect(extractTokenFromHeader).toHaveBeenCalledWith(req);

      // 验证token验证
      expect(verifyToken).toHaveBeenCalledWith(mockToken);

      // 验证用户信息设置
      expect(req.user).toEqual(mockPayload);

      // 验证next调用
      expect(next).toHaveBeenCalled();

      // 验证未调用错误响应
      expect(apiResponse.unauthorized).not.toHaveBeenCalled();
    });

    it('should return unauthorized when no token is provided', () => {
      // 模拟token提取 - 无token
      extractTokenFromHeader.mockReturnValue(null);

      // 调用中间件
      authenticateJWT(req, res, next);

      // 验证token提取
      expect(extractTokenFromHeader).toHaveBeenCalledWith(req);

      // 验证未调用token验证
      expect(verifyToken).not.toHaveBeenCalled();

      // 验证未设置用户信息
      expect(req.user).toBeUndefined();

      // 验证未调用next
      expect(next).not.toHaveBeenCalled();

      // 验证错误响应
      expect(apiResponse.unauthorized).toHaveBeenCalledWith(res, '未提供认证令牌');
    });

    it('should return unauthorized when token is invalid', () => {
      // 模拟token提取
      const mockToken = 'invalid.jwt.token';
      extractTokenFromHeader.mockReturnValue(mockToken);

      // 模拟token验证 - 无效token
      verifyToken.mockReturnValue(null);

      // 调用中间件
      authenticateJWT(req, res, next);

      // 验证token提取
      expect(extractTokenFromHeader).toHaveBeenCalledWith(req);

      // 验证token验证
      expect(verifyToken).toHaveBeenCalledWith(mockToken);

      // 验证未设置用户信息
      expect(req.user).toBeUndefined();

      // 验证未调用next
      expect(next).not.toHaveBeenCalled();

      // 验证错误响应
      expect(apiResponse.unauthorized).toHaveBeenCalledWith(res, '无效或过期的令牌');
    });

    it('should handle errors and return unauthorized', () => {
      // 模拟token提取抛出错误
      const error = new Error('提取token错误');
      extractTokenFromHeader.mockImplementation(() => {
        throw error;
      });

      // 调用中间件
      authenticateJWT(req, res, next);

      // 验证错误日志
      expect(logger.error).toHaveBeenCalledWith(`认证中间件错误: ${error.message}`);

      // 验证未调用next
      expect(next).not.toHaveBeenCalled();

      // 验证错误响应
      expect(apiResponse.unauthorized).toHaveBeenCalledWith(res, '认证失败');
    });
  });

  describe('optionalAuthJWT', () => {
    it('should set user info and call next() when a valid token is provided', () => {
      // 模拟token提取
      const mockToken = 'valid.jwt.token';
      extractTokenFromHeader.mockReturnValue(mockToken);

      // 模拟token验证
      const mockPayload = { userId: 'user-123', nickname: 'Test User' };
      verifyToken.mockReturnValue(mockPayload);

      // 调用中间件
      optionalAuthJWT(req, res, next);

      // 验证token提取
      expect(extractTokenFromHeader).toHaveBeenCalledWith(req);

      // 验证token验证
      expect(verifyToken).toHaveBeenCalledWith(mockToken);

      // 验证用户信息设置
      expect(req.user).toEqual(mockPayload);

      // 验证next调用
      expect(next).toHaveBeenCalled();
    });

    it('should call next() without setting user info when no token is provided', () => {
      // 模拟token提取 - 无token
      extractTokenFromHeader.mockReturnValue(null);

      // 调用中间件
      optionalAuthJWT(req, res, next);

      // 验证token提取
      expect(extractTokenFromHeader).toHaveBeenCalledWith(req);

      // 验证未调用token验证
      expect(verifyToken).not.toHaveBeenCalled();

      // 验证未设置用户信息
      expect(req.user).toBeUndefined();

      // 验证next调用
      expect(next).toHaveBeenCalled();
    });

    it('should call next() without setting user info when token is invalid', () => {
      // 模拟token提取
      const mockToken = 'invalid.jwt.token';
      extractTokenFromHeader.mockReturnValue(mockToken);

      // 模拟token验证 - 无效token
      verifyToken.mockReturnValue(null);

      // 调用中间件
      optionalAuthJWT(req, res, next);

      // 验证token提取
      expect(extractTokenFromHeader).toHaveBeenCalledWith(req);

      // 验证token验证
      expect(verifyToken).toHaveBeenCalledWith(mockToken);

      // 验证未设置用户信息
      expect(req.user).toBeUndefined();

      // 验证next调用
      expect(next).toHaveBeenCalled();
    });

    it('should handle errors and still call next()', () => {
      // 模拟token提取抛出错误
      const error = new Error('提取token错误');
      extractTokenFromHeader.mockImplementation(() => {
        throw error;
      });

      // 调用中间件
      optionalAuthJWT(req, res, next);

      // 验证错误日志
      expect(logger.error).toHaveBeenCalledWith(`可选认证中间件错误: ${error.message}`);

      // 验证next调用
      expect(next).toHaveBeenCalled();
    });
  });
});
