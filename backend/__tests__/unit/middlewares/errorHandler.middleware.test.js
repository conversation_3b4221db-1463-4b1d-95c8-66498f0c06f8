/**
 * 全局错误处理中间件单元测试
 */
const { errorHandler, handleEncodingError } = require('../../../middlewares/errorHandler');
const apiResponse = require('../../../utils/apiResponse');
const logger = require('../../../config/logger');

// 模拟 apiResponse
jest.mock('../../../utils/apiResponse', () => ({
  error: jest.fn().mockReturnValue('error-response'),
  validationError: jest.fn().mockReturnValue('validation-error-response'),
  unauthorized: jest.fn().mockReturnValue('unauthorized-response'),
  forbidden: jest.fn().mockReturnValue('forbidden-response'),
  notFound: jest.fn().mockReturnValue('not-found-response')
}));

// 模拟 logger
jest.mock('../../../config/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  info: jest.fn()
}));

describe('全局错误处理中间件', () => {
  // 在每个测试后重置模拟
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleEncodingError 函数', () => {
    it('应该处理编码错误', () => {
      const error = new Error('Invalid character encoding');
      const result = handleEncodingError(error);
      
      expect(logger.error).toHaveBeenCalledWith('检测到可能的编码错误:', 'Invalid character encoding');
      expect(result).toBe('Invalid character encoding');
    });

    it('应该处理没有消息的错误', () => {
      const error = {};
      const result = handleEncodingError(error);
      
      expect(result).toBe('Unknown error');
    });

    it('应该处理非编码错误', () => {
      const error = new Error('普通错误');
      const result = handleEncodingError(error);
      
      expect(logger.error).not.toHaveBeenCalled();
      expect(result).toBe('普通错误');
    });

    it('应该处理编码修复失败的情况', () => {
      // 模拟Buffer.from抛出异常
      const originalFrom = Buffer.from;
      Buffer.from = jest.fn().mockImplementation(() => {
        throw new Error('Buffer error');
      });

      const error = new Error('decode error');
      const result = handleEncodingError(error);
      
      expect(logger.error).toHaveBeenCalledWith('检测到可能的编码错误:', 'decode error');
      expect(logger.error).toHaveBeenCalledWith('修复编码错误失败:', expect.any(Error));
      expect(result).toBe('decode error');

      // 恢复原始函数
      Buffer.from = originalFrom;
    });
  });

  describe('errorHandler 中间件', () => {
    it('应该处理通用错误', () => {
      const err = new Error('服务器错误');
      const req = { originalUrl: '/api/test', method: 'GET', ip: '127.0.0.1' };
      const res = {};
      const next = jest.fn();
      
      errorHandler(err, req, res, next);
      
      expect(logger.error).toHaveBeenCalledWith('500 - 服务器错误 - /api/test - GET - 127.0.0.1');
      expect(logger.error).toHaveBeenCalledWith(err.stack);
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '服务器错误',
        'SERVER_ERROR',
        500,
        {}
      );
    });

    it('应该处理ValidationError', () => {
      const err = new Error('验证错误');
      err.name = 'ValidationError';
      err.details = { field: 'invalid' };
      const req = { originalUrl: '/api/test', method: 'GET', ip: '127.0.0.1' };
      const res = {};
      const next = jest.fn();
      
      errorHandler(err, req, res, next);
      
      expect(apiResponse.validationError).toHaveBeenCalledWith(
        res,
        '验证错误',
        { field: 'invalid' }
      );
    });

    it('应该处理UnauthorizedError', () => {
      const err = new Error('未授权');
      err.name = 'UnauthorizedError';
      const req = { originalUrl: '/api/test', method: 'GET', ip: '127.0.0.1' };
      const res = {};
      const next = jest.fn();
      
      errorHandler(err, req, res, next);
      
      expect(apiResponse.unauthorized).toHaveBeenCalledWith(res, '未授权');
    });

    it('应该处理ForbiddenError', () => {
      const err = new Error('禁止访问');
      err.name = 'ForbiddenError';
      const req = { originalUrl: '/api/test', method: 'GET', ip: '127.0.0.1' };
      const res = {};
      const next = jest.fn();
      
      errorHandler(err, req, res, next);
      
      expect(apiResponse.forbidden).toHaveBeenCalledWith(res, '禁止访问');
    });

    it('应该处理NotFoundError', () => {
      const err = new Error('资源不存在');
      err.name = 'NotFoundError';
      const req = { originalUrl: '/api/test', method: 'GET', ip: '127.0.0.1' };
      const res = {};
      const next = jest.fn();
      
      errorHandler(err, req, res, next);
      
      expect(apiResponse.notFound).toHaveBeenCalledWith(res, '资源不存在');
    });

    it('应该在生产环境中隐藏500错误的详细信息', () => {
      // 保存原始环境变量
      const originalNodeEnv = process.env.NODE_ENV;
      
      // 设置为生产环境
      process.env.NODE_ENV = 'production';
      
      const err = new Error('敏感的服务器错误');
      err.status = 500;
      const req = { originalUrl: '/api/test', method: 'GET', ip: '127.0.0.1' };
      const res = {};
      const next = jest.fn();
      
      errorHandler(err, req, res, next);
      
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '服务器内部错误', // 应该使用通用消息
        'SERVER_ERROR',
        500,
        {}
      );
      
      // 恢复原始环境变量
      process.env.NODE_ENV = originalNodeEnv;
    });

    it('应该使用自定义错误代码和状态码', () => {
      const err = new Error('自定义错误');
      err.code = 'CUSTOM_ERROR';
      err.status = 418;
      err.details = { reason: 'teapot' };
      const req = { originalUrl: '/api/test', method: 'GET', ip: '127.0.0.1' };
      const res = {};
      const next = jest.fn();
      
      errorHandler(err, req, res, next);
      
      expect(apiResponse.error).toHaveBeenCalledWith(
        res,
        '自定义错误',
        'CUSTOM_ERROR',
        418,
        { reason: 'teapot' }
      );
    });
  });
});
