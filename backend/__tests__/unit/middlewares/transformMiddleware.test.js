/**
 * 数据转换中间件测试
 */
const { createTransformMiddleware } = require('../../../utils/dataTransformer');

describe('数据转换中间件', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      body: {
        userName: 'test',
        createdAt: '2023-01-01',
        userSettings: {
          notificationEnabled: true,
          themeColor: 'dark'
        }
      }
    };

    res = {
      json: jest.fn().mockReturnThis()
    };

    next = jest.fn();
  });

  it('应该转换请求体', () => {
    const middleware = createTransformMiddleware({ transformRequest: true, transformResponse: false });
    middleware(req, res, next);

    expect(req.body).toEqual({
      user_name: 'test',
      created_at: '2023-01-01',
      user_settings: {
        notification_enabled: true,
        theme_color: 'dark'
      }
    });

    expect(next).toHaveBeenCalled();
  });

  it('应该转换响应体', () => {
    const middleware = createTransformMiddleware({ transformRequest: false, transformResponse: true });
    middleware(req, res, next);

    // 模拟响应数据
    const responseData = {
      success: true,
      data: {
        id: 1,
        user_name: 'test',
        created_at: '2023-01-01',
        user_settings: {
          notification_enabled: true,
          theme_color: 'dark'
        }
      }
    };

    // 调用重写的res.json方法
    res.json(responseData);

    // 验证原始的res.json方法被调用，且数据被转换
    expect(res.json).toHaveBeenCalledWith({
      success: true,
      data: {
        id: 1,
        userName: 'test',
        createdAt: '2023-01-01',
        userSettings: {
          notificationEnabled: true,
          themeColor: 'dark'
        }
      }
    });

    expect(next).toHaveBeenCalled();
  });

  it('应该同时转换请求体和响应体', () => {
    const middleware = createTransformMiddleware();
    middleware(req, res, next);

    // 验证请求体被转换
    expect(req.body).toEqual({
      user_name: 'test',
      created_at: '2023-01-01',
      user_settings: {
        notification_enabled: true,
        theme_color: 'dark'
      }
    });

    // 模拟响应数据
    const responseData = {
      success: true,
      data: {
        id: 1,
        user_name: 'test',
        created_at: '2023-01-01',
        user_settings: {
          notification_enabled: true,
          theme_color: 'dark'
        }
      }
    };

    // 调用重写的res.json方法
    res.json(responseData);

    // 验证原始的res.json方法被调用，且数据被转换
    expect(res.json).toHaveBeenCalledWith({
      success: true,
      data: {
        id: 1,
        userName: 'test',
        createdAt: '2023-01-01',
        userSettings: {
          notificationEnabled: true,
          themeColor: 'dark'
        }
      }
    });

    expect(next).toHaveBeenCalled();
  });

  it('应该处理非标准响应格式', () => {
    const middleware = createTransformMiddleware({ transformRequest: false, transformResponse: true });
    middleware(req, res, next);

    // 模拟非标准响应数据
    const responseData = {
      id: 1,
      user_name: 'test',
      created_at: '2023-01-01'
    };

    // 调用重写的res.json方法
    res.json(responseData);

    // 验证原始的res.json方法被调用，且数据被转换
    expect(res.json).toHaveBeenCalledWith({
      id: 1,
      userName: 'test',
      createdAt: '2023-01-01'
    });

    expect(next).toHaveBeenCalled();
  });

  it('应该不转换请求体和响应体当选项为false', () => {
    const middleware = createTransformMiddleware({ transformRequest: false, transformResponse: false });
    middleware(req, res, next);

    // 验证请求体未被转换
    expect(req.body).toEqual({
      userName: 'test',
      createdAt: '2023-01-01',
      userSettings: {
        notificationEnabled: true,
        themeColor: 'dark'
      }
    });

    // 模拟响应数据
    const responseData = {
      success: true,
      data: {
        id: 1,
        user_name: 'test',
        created_at: '2023-01-01'
      }
    };

    // 调用重写的res.json方法
    res.json(responseData);

    // 验证原始的res.json方法被调用，且数据未被转换
    expect(res.json).toHaveBeenCalledWith({
      success: true,
      data: {
        id: 1,
        user_name: 'test',
        created_at: '2023-01-01'
      }
    });

    expect(next).toHaveBeenCalled();
  });
});
