/**
 * BaseRepository单元测试
 * 测试软删除相关功能
 */
const BaseRepository = require('../../../repositories/base.repository');

// 模拟Sequelize模型
const mockModel = {
  findByPk: jest.fn(),
  findOne: jest.fn(),
  findAll: jest.fn(),
  findAndCountAll: jest.fn(),
  create: jest.fn(),
  bulkCreate: jest.fn(),
  update: jest.fn(),
  destroy: jest.fn(),
  restore: jest.fn(),
  count: jest.fn(),
  sequelize: {
    transaction: jest.fn()
  },
  options: {
    paranoid: true // 模拟支持软删除
  }
};

// 模拟事务
const mockTransaction = {
  commit: jest.fn(),
  rollback: jest.fn()
};

describe('BaseRepository', () => {
  let repository;

  beforeEach(() => {
    jest.clearAllMocks();
    mockModel.sequelize.transaction.mockResolvedValue(mockTransaction);
    repository = new BaseRepository(mockModel);
  });

  describe('constructor', () => {
    it('should initialize with model and detect soft delete support', () => {
      expect(repository.model).toBe(mockModel);
      expect(repository.supportsSoftDelete).toBe(true);
    });

    it('should detect when model does not support soft delete', () => {
      const nonParanoidModel = { ...mockModel, options: { paranoid: false } };
      const repo = new BaseRepository(nonParanoidModel);
      expect(repo.supportsSoftDelete).toBe(false);
    });
  });

  describe('findById', () => {
    it('should call findByPk with default options', async () => {
      mockModel.findByPk.mockResolvedValue({ id: 1, name: 'Test' });
      
      const result = await repository.findById(1);
      
      expect(mockModel.findByPk).toHaveBeenCalledWith(1, {});
      expect(result).toEqual({ id: 1, name: 'Test' });
    });

    it('should include deleted records when withDeleted is true', async () => {
      mockModel.findByPk.mockResolvedValue({ id: 1, name: 'Test', deleted_at: new Date() });
      
      const result = await repository.findById(1, { withDeleted: true });
      
      expect(mockModel.findByPk).toHaveBeenCalledWith(1, { paranoid: false });
      expect(result).toEqual({ id: 1, name: 'Test', deleted_at: expect.any(Date) });
    });
  });

  describe('findOne', () => {
    it('should call findOne with default options', async () => {
      mockModel.findOne.mockResolvedValue({ id: 1, name: 'Test' });
      
      const result = await repository.findOne({ id: 1 });
      
      expect(mockModel.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(result).toEqual({ id: 1, name: 'Test' });
    });

    it('should include deleted records when withDeleted is true', async () => {
      mockModel.findOne.mockResolvedValue({ id: 1, name: 'Test', deleted_at: new Date() });
      
      const result = await repository.findOne({ id: 1 }, { withDeleted: true });
      
      expect(mockModel.findOne).toHaveBeenCalledWith({ where: { id: 1 }, paranoid: false });
      expect(result).toEqual({ id: 1, name: 'Test', deleted_at: expect.any(Date) });
    });
  });

  describe('softDelete', () => {
    it('should call destroy with paranoid true (soft delete)', async () => {
      mockModel.destroy.mockResolvedValue(1);
      
      const result = await repository.softDelete({ id: 1 });
      
      expect(mockModel.destroy).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(result).toBe(1);
    });

    it('should throw error if model does not support soft delete', async () => {
      repository.supportsSoftDelete = false;
      
      await expect(repository.softDelete({ id: 1 })).rejects.toThrow('此模型不支持软删除');
    });
  });

  describe('hardDelete', () => {
    it('should call destroy with force true (hard delete)', async () => {
      mockModel.destroy.mockResolvedValue(1);
      
      const result = await repository.hardDelete({ id: 1 });
      
      expect(mockModel.destroy).toHaveBeenCalledWith({ where: { id: 1 }, force: true });
      expect(result).toBe(1);
    });
  });

  describe('delete', () => {
    it('should call destroy with default options (soft delete if supported)', async () => {
      mockModel.destroy.mockResolvedValue(1);
      
      const result = await repository.delete({ id: 1 });
      
      expect(mockModel.destroy).toHaveBeenCalledWith({ where: { id: 1 }, force: false });
      expect(result).toBe(1);
    });

    it('should call destroy with force true when specified', async () => {
      mockModel.destroy.mockResolvedValue(1);
      
      const result = await repository.delete({ id: 1 }, { force: true });
      
      expect(mockModel.destroy).toHaveBeenCalledWith({ where: { id: 1 }, force: true });
      expect(result).toBe(1);
    });
  });

  describe('restore', () => {
    it('should call restore with provided where condition', async () => {
      mockModel.restore.mockResolvedValue(1);
      
      const result = await repository.restore({ id: 1 });
      
      expect(mockModel.restore).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(result).toBe(1);
    });

    it('should throw error if model does not support soft delete', async () => {
      repository.supportsSoftDelete = false;
      
      await expect(repository.restore({ id: 1 })).rejects.toThrow('此模型不支持软删除');
    });
  });

  describe('count', () => {
    it('should call count with default options', async () => {
      mockModel.count.mockResolvedValue(5);
      
      const result = await repository.count({ status: 'active' });
      
      expect(mockModel.count).toHaveBeenCalledWith({ where: { status: 'active' } });
      expect(result).toBe(5);
    });

    it('should include deleted records when withDeleted is true', async () => {
      mockModel.count.mockResolvedValue(8);
      
      const result = await repository.count({ status: 'active' }, { withDeleted: true });
      
      expect(mockModel.count).toHaveBeenCalledWith({ where: { status: 'active' }, paranoid: false });
      expect(result).toBe(8);
    });
  });

  describe('transaction', () => {
    it('should handle transaction correctly on success', async () => {
      const callback = jest.fn().mockResolvedValue('result');
      
      const result = await repository.transaction(callback);
      
      expect(mockModel.sequelize.transaction).toHaveBeenCalled();
      expect(callback).toHaveBeenCalledWith(mockTransaction);
      expect(mockTransaction.commit).toHaveBeenCalled();
      expect(mockTransaction.rollback).not.toHaveBeenCalled();
      expect(result).toBe('result');
    });

    it('should rollback transaction on error', async () => {
      const error = new Error('Transaction failed');
      const callback = jest.fn().mockRejectedValue(error);
      
      await expect(repository.transaction(callback)).rejects.toThrow('Transaction failed');
      
      expect(mockModel.sequelize.transaction).toHaveBeenCalled();
      expect(callback).toHaveBeenCalledWith(mockTransaction);
      expect(mockTransaction.commit).not.toHaveBeenCalled();
      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });
});
