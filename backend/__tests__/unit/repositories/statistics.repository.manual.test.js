/**
 * 统计仓库单元测试（手动测试）
 */

// 模拟依赖
jest.mock('../../../models', () => {
  const mockLearningActivity = {
    findAll: jest.fn(),
    findAndCountAll: jest.fn(),
    count: jest.fn(),
    create: jest.fn()
  };

  const mockDailyRecord = {
    findAll: jest.fn(),
    findAndCountAll: jest.fn(),
    findOne: jest.fn(),
    findOrCreate: jest.fn(),
    update: jest.fn(),
    sum: jest.fn()
  };

  const mockLearningPlan = {
    count: jest.fn()
  };

  const mockSequelize = {
    fn: jest.fn(),
    col: jest.fn(),
    literal: jest.fn(),
    transaction: jest.fn(),
    Op: {
      gte: 'gte',
      lte: 'lte',
      between: 'between'
    }
  };

  return {
    LearningActivity: mockLearningActivity,
    DailyRecord: mockDailyRecord,
    LearningPlan: mockLearningPlan,
    sequelize: mockSequelize
  };
});

const StatisticsRepository = require('../../../repositories/statistics.repository');

describe('StatisticsRepository', () => {
  let statisticsRepository;

  beforeEach(() => {
    jest.clearAllMocks();
    statisticsRepository = new StatisticsRepository();
  });

  describe('getLearningActivityStats', () => {
    it('should return activity stats for a user', async () => {
      // 准备模拟数据
      const mockStats = [
        {
          activity_type: 'login',
          dataValues: { count: '10' }
        },
        {
          activity_type: 'view_exercise',
          dataValues: { count: '20' }
        }
      ];

      // 设置模拟函数返回值
      mockLearningActivity.findAll.mockResolvedValue(mockStats);

      // 调用方法
      const result = await statisticsRepository.getLearningActivityStats('user123');

      // 验证结果
      expect(mockLearningActivity.findAll).toHaveBeenCalled();
      expect(result).toEqual(mockStats);
    });
  });

  describe('getDailyRecords', () => {
    it('should return daily records for a user', async () => {
      // 准备模拟数据
      const mockRecords = {
        count: 2,
        rows: [
          {
            id: 1,
            date: new Date('2023-06-15'),
            time_spent: 30
          },
          {
            id: 2,
            date: new Date('2023-06-16'),
            time_spent: 45
          }
        ]
      };

      // 设置模拟函数返回值
      mockDailyRecord.findAndCountAll.mockResolvedValue(mockRecords);

      // 调用方法
      const result = await statisticsRepository.getDailyRecords('user123');

      // 验证结果
      expect(mockDailyRecord.findAndCountAll).toHaveBeenCalled();
      expect(result.records).toEqual(mockRecords.rows);
      expect(result.count).toEqual(mockRecords.count);
    });
  });

  describe('getOrCreateDailyRecord', () => {
    it('should return existing record if found', async () => {
      // 准备模拟数据
      const mockRecord = {
        id: 1,
        user_id: 'user123',
        date: '2023-06-15',
        time_spent: 30
      };

      // 设置模拟函数返回值
      mockDailyRecord.findOrCreate.mockResolvedValue([mockRecord, false]);

      // 调用方法
      const result = await statisticsRepository.getOrCreateDailyRecord('user123', '2023-06-15');

      // 验证结果
      expect(mockDailyRecord.findOrCreate).toHaveBeenCalled();
      expect(result.record).toEqual(mockRecord);
      expect(result.created).toEqual(false);
    });
  });

  describe('recordActivity', () => {
    it('should create activity record', async () => {
      // 准备模拟数据
      const activityData = {
        user_id: 'user123',
        activity_type: 'view_exercise',
        content_id: 456,
        content_type: 'exercise'
      };

      const mockActivity = {
        id: 123,
        ...activityData,
        created_at: new Date()
      };

      // 设置模拟函数返回值
      mockLearningActivity.create.mockResolvedValue(mockActivity);

      // 调用方法
      const result = await statisticsRepository.recordActivity(activityData);

      // 验证结果
      expect(mockLearningActivity.create).toHaveBeenCalledWith(activityData, { transaction: null });
      expect(result).toEqual(mockActivity);
    });
  });

  describe('getLearningPlanStats', () => {
    it('should return learning plan statistics', async () => {
      // 设置模拟函数返回值
      mockLearningPlan.count.mockResolvedValueOnce(2); // 活跃计划
      mockLearningPlan.count.mockResolvedValueOnce(3); // 已完成计划

      // 调用方法
      const result = await statisticsRepository.getLearningPlanStats('user123');

      // 验证结果
      expect(mockLearningPlan.count).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        activePlans: 2,
        completedPlans: 3,
        totalPlans: 5
      });
    });
  });

  describe('getTotalLearningTime', () => {
    it('should return total learning time', async () => {
      // 设置模拟函数返回值
      mockDailyRecord.sum.mockResolvedValue(120);

      // 调用方法
      const result = await statisticsRepository.getTotalLearningTime('user123');

      // 验证结果
      expect(mockDailyRecord.sum).toHaveBeenCalled();
      expect(result).toBe(120);
    });

    it('should return 0 if no records found', async () => {
      // 设置模拟函数返回值
      mockDailyRecord.sum.mockResolvedValue(null);

      // 调用方法
      const result = await statisticsRepository.getTotalLearningTime('user123');

      // 验证结果
      expect(result).toBe(0);
    });
  });
});
