/**
 * 学习计划V2服务单元测试
 */

// 模拟所有依赖
jest.mock('../../../config/logger');
jest.mock('../../../models');
jest.mock('../../../config/serviceContainer');

const LearningPlanService = require('../../../services/learningPlanV2.service');
const logger = require('../../../config/logger');
const { sequelize } = require('../../../models');

// 模拟依赖
jest.mock('../../../config/logger', () => ({
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
}));

jest.mock('../../../models', () => ({
  sequelize: {
    transaction: jest.fn(),
    query: jest.fn(),
    QueryTypes: { SELECT: 'SELECT' },
  },
  LearningPlan: {
    findAndCountAll: jest.fn(),
    findOne: jest.fn(),
    findByPk: jest.fn(),
    create: jest.fn(),
  },
  Theme: {
    findOne: jest.fn(),
  },
  Tag: {
    create: jest.fn(),
  },
  PlanTag: {
    create: jest.fn(),
    findAll: jest.fn(),
  },
  DailyContent: {
    findAll: jest.fn(),
    create: jest.fn(),
  },
}));

jest.mock('../../../config/serviceContainer', () => ({
  getService: jest.fn(),
  getRepository: jest.fn(),
}));

describe('LearningPlanService', () => {
  let learningPlanService;
  let mockTransaction;

  beforeEach(() => {
    jest.clearAllMocks();

    // 创建模拟事务
    mockTransaction = {
      commit: jest.fn(),
      rollback: jest.fn(),
    };

    sequelize.transaction.mockResolvedValue(mockTransaction);

    // 创建服务实例
    learningPlanService = new LearningPlanService();
  });

  describe('getUserPlans', () => {
    it('should return user plans with pagination', async () => {
      // 准备模拟数据
      const mockPlans = {
        count: 2,
        rows: [
          {
            id: 1,
            title: '测试计划1',
            description: '测试描述1',
            theme_id: 1,
            status: 'active',
            progress: 50,
            created_at: new Date(),
            updated_at: new Date(),
            theme: {
              name: '测试主题',
              color: '#FF0000',
            },
          },
          {
            id: 2,
            title: '测试计划2',
            description: '测试描述2',
            theme_id: 2,
            status: 'completed',
            progress: 100,
            created_at: new Date(),
            updated_at: new Date(),
            theme: {
              name: '测试主题2',
              color: '#00FF00',
            },
          },
        ],
      };

      const { LearningPlan } = require('../../../models');
      LearningPlan.findAndCountAll.mockResolvedValue(mockPlans);

      // 调用方法
      const result = await learningPlanService.getUserPlans('user123', 1, 10);

      // 验证结果
      expect(LearningPlan.findAndCountAll).toHaveBeenCalledWith({
        where: { user_id: 'user123' },
        include: expect.any(Array),
        order: [['updated_at', 'DESC']],
        offset: 0,
        limit: 10,
      });

      expect(result).toEqual({
        plans: [
          {
            id: 1,
            title: '测试计划1',
            description: '测试描述1',
            themeId: 1,
            themeName: '测试主题',
            themeColor: '#FF0000',
            status: 'active',
            progress: 50,
            createdAt: mockPlans.rows[0].created_at,
            updatedAt: mockPlans.rows[0].updated_at,
          },
          {
            id: 2,
            title: '测试计划2',
            description: '测试描述2',
            themeId: 2,
            themeName: '测试主题2',
            themeColor: '#00FF00',
            status: 'completed',
            progress: 100,
            createdAt: mockPlans.rows[1].created_at,
            updatedAt: mockPlans.rows[1].updated_at,
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          pageSize: 10,
          totalPages: 1,
        },
      });
    });

    it('should handle status filter', async () => {
      const { LearningPlan } = require('../../../models');
      LearningPlan.findAndCountAll.mockResolvedValue({ count: 0, rows: [] });

      await learningPlanService.getUserPlans('user123', 1, 10, 'active');

      expect(LearningPlan.findAndCountAll).toHaveBeenCalledWith({
        where: { user_id: 'user123', status: 'active' },
        include: expect.any(Array),
        order: [['updated_at', 'DESC']],
        offset: 0,
        limit: 10,
      });
    });

    it('should handle errors', async () => {
      const { LearningPlan } = require('../../../models');
      const error = new Error('Database error');
      LearningPlan.findAndCountAll.mockRejectedValue(error);

      await expect(learningPlanService.getUserPlans('user123')).rejects.toThrow();
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('createPlan', () => {
    it('should create a new learning plan with tags', async () => {
      // 准备模拟数据
      const mockTheme = { id: 1, name: '测试主题', is_active: true };
      const mockPlan = { id: 1, user_id: 'user123', theme_id: 1, title: '测试计划' };
      const mockTags = [
        { id: 1, name: '标签1' },
        { id: 2, name: '标签2' },
      ];

      const { Theme, LearningPlan, Tag, PlanTag } = require('../../../models');
      Theme.findOne.mockResolvedValue(mockTheme);
      LearningPlan.create.mockResolvedValue(mockPlan);
      Tag.create.mockResolvedValueOnce(mockTags[0]).mockResolvedValueOnce(mockTags[1]);
      PlanTag.create.mockResolvedValue({});

      const planData = {
        themeId: 1,
        title: '测试计划',
        description: '测试描述',
        targetDays: 7,
      };

      const tags = [
        { name: '标签1', relevance_score: 0.9 },
        { name: '标签2', relevance_score: 0.8 },
      ];

      // 调用方法
      const result = await learningPlanService.createPlan('user123', planData, tags);

      // 验证结果
      expect(Theme.findOne).toHaveBeenCalledWith({
        where: { id: 1, is_active: true },
        transaction: mockTransaction,
      });

      expect(LearningPlan.create).toHaveBeenCalledWith(
        {
          user_id: 'user123',
          theme_id: 1,
          title: '测试计划',
          description: '测试描述',
          target_days: 7,
          daily_goal_exercises: 3,
          daily_goal_insights: 5,
          daily_goal_time: 15,
        },
        { transaction: mockTransaction },
      );

      expect(Tag.create).toHaveBeenCalledTimes(2);
      expect(PlanTag.create).toHaveBeenCalledTimes(2);
      expect(mockTransaction.commit).toHaveBeenCalled();
      expect(result).toEqual(mockPlan);
    });

    it('should rollback transaction on theme not found', async () => {
      const { Theme } = require('../../../models');
      Theme.findOne.mockResolvedValue(null);

      const planData = { themeId: 999, title: '测试计划' };

      await expect(learningPlanService.createPlan('user123', planData)).rejects.toThrow(
        '主题不存在或未激活',
      );
      expect(mockTransaction.rollback).toHaveBeenCalled();
    });

    it('should handle creation errors', async () => {
      const { Theme, LearningPlan } = require('../../../models');
      Theme.findOne.mockResolvedValue({ id: 1, is_active: true });
      LearningPlan.create.mockRejectedValue(new Error('Creation failed'));

      const planData = { themeId: 1, title: '测试计划' };

      await expect(learningPlanService.createPlan('user123', planData)).rejects.toThrow();
      expect(mockTransaction.rollback).toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getPlanById', () => {
    it('should return plan details with daily contents', async () => {
      const mockPlan = {
        id: 1,
        title: '测试计划',
        description: '测试描述',
        theme_id: 1,
        status: 'active',
        progress: 50,
        target_days: 7,
        completed_days: 3,
        start_date: new Date(),
        end_date: new Date(),
        daily_goal_exercises: 3,
        daily_goal_insights: 5,
        daily_goal_time: 15,
        created_at: new Date(),
        updated_at: new Date(),
        theme: {
          name: '测试主题',
          english_name: 'test-theme',
          color: '#FF0000',
        },
      };

      const mockDailyContents = [
        {
          id: 1,
          day_number: 1,
          title: '第一天',
          summary: '第一天总结',
          is_completed: true,
          completion_date: new Date(),
        },
      ];

      const { LearningPlan, DailyContent } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(mockPlan);
      DailyContent.findAll.mockResolvedValue(mockDailyContents);

      // 调用方法
      const result = await learningPlanService.getPlanById(1, 'user123');

      // 验证结果
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { id: 1, user_id: 'user123' },
        include: expect.any(Array),
      });

      expect(DailyContent.findAll).toHaveBeenCalledWith({
        where: { plan_id: 1 },
        order: [['day_number', 'ASC']],
      });

      expect(result).toHaveProperty('plan');
      expect(result).toHaveProperty('dailyContents');
      expect(result.plan.id).toBe(1);
      expect(result.dailyContents).toHaveLength(1);
    });

    it('should throw error when plan not found', async () => {
      const { LearningPlan } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(null);

      await expect(learningPlanService.getPlanById(999, 'user123')).rejects.toThrow(
        '学习计划不存在或不属于当前用户',
      );
    });
  });

  describe('softDeletePlan', () => {
    it('should soft delete a plan', async () => {
      const mockPlan = {
        id: 1,
        user_id: 'user123',
        destroy: jest.fn().mockResolvedValue(true),
      };

      const { LearningPlan } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(mockPlan);

      // 调用方法
      const result = await learningPlanService.softDeletePlan(1, 'user123');

      // 验证结果
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { id: 1, user_id: 'user123' },
      });

      expect(mockPlan.destroy).toHaveBeenCalledWith({ force: false });
      expect(result).toBe(true);
    });

    it('should throw error when plan not found', async () => {
      const { LearningPlan } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(null);

      await expect(learningPlanService.softDeletePlan(999, 'user123')).rejects.toThrow(
        '学习计划不存在或不属于当前用户',
      );
    });
  });

  describe('restorePlan', () => {
    it('should restore a soft deleted plan', async () => {
      const mockPlan = {
        id: 1,
        user_id: 'user123',
        deleted_at: new Date(),
        restore: jest.fn().mockResolvedValue(true),
      };

      const { LearningPlan } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(mockPlan);

      // 调用方法
      const result = await learningPlanService.restorePlan(1, 'user123');

      // 验证结果
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { id: 1, user_id: 'user123' },
        paranoid: false,
      });

      expect(mockPlan.restore).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should throw error when plan is not deleted', async () => {
      const mockPlan = {
        id: 1,
        user_id: 'user123',
        deleted_at: null,
      };

      const { LearningPlan } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(mockPlan);

      await expect(learningPlanService.restorePlan(1, 'user123')).rejects.toThrow(
        '学习计划未被删除，无需恢复',
      );
    });
  });

  describe('getSystemDefaultPlan', () => {
    it('should return system default plan with tags and daily contents', async () => {
      const mockPlan = {
        id: 1,
        title: '系统默认计划',
        description: '系统默认描述',
        theme_id: 1,
        is_system_default: true,
        target_days: 7,
        completed_days: 0,
        progress: 0,
        status: 'active',
        start_date: null,
        end_date: null,
        created_at: new Date(),
        theme: {
          name: '默认主题',
          english_name: 'default-theme',
          description: '默认主题描述',
          icon: 'default-icon',
          color: '#0066CC',
        },
      };

      const mockTags = [
        {
          id: 1,
          name: '标签1',
          relevance_score: 0.9,
          weight: 1.0,
          is_primary: true,
          sort_order: 0,
        },
      ];

      const mockDailyContents = [
        {
          id: 1,
          day_number: 1,
          title: '第一天',
          content: '第一天内容',
          is_completed: false,
          completed_at: null,
        },
      ];

      const { LearningPlan, DailyContent } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(mockPlan);
      sequelize.query.mockResolvedValue(mockTags);
      DailyContent.findAll.mockResolvedValue(mockDailyContents);

      // 调用方法
      const result = await learningPlanService.getSystemDefaultPlan();

      // 验证结果
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { is_system_default: true },
        include: expect.any(Array),
      });

      expect(result).toHaveProperty('plan');
      expect(result).toHaveProperty('tags');
      expect(result).toHaveProperty('dailyContents');
      expect(result).toHaveProperty('statistics');
      expect(result.plan.isSystemDefault).toBe(true);
      expect(result.tags).toHaveLength(1);
      expect(result.dailyContents).toHaveLength(1);
    });

    it('should throw error when system default plan not found', async () => {
      const { LearningPlan } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(null);

      await expect(learningPlanService.getSystemDefaultPlan()).rejects.toThrow(
        '系统默认学习计划不存在',
      );
    });
  });

  describe('copySystemDefaultPlanToUser', () => {
    it('should copy system default plan to user', async () => {
      const mockDefaultPlan = {
        id: 1,
        theme_id: 1,
        title: '系统默认计划',
        description: '系统默认描述',
        target_days: 7,
        is_system_default: true,
      };

      const mockUserPlan = {
        id: 2,
        user_id: 'user123',
        theme_id: 1,
        title: '系统默认计划',
        description: '系统默认描述',
        target_days: 7,
        is_system_default: false,
      };

      const mockPlanTags = [
        {
          tag: {
            id: 1,
            name: '标签1',
            category_id: 1,
            relevance_score: 0.9,
            weight: 1.0,
            is_verified: true,
            sort_order: 0,
          },
          relevance_score: 0.9,
          weight: 1.0,
          sort_order: 0,
        },
      ];

      const mockDailyContents = [
        {
          id: 1,
          plan_id: 1,
          day_number: 1,
          title: '第一天',
          content: '第一天内容',
        },
      ];

      const { LearningPlan, PlanTag, Tag, DailyContent } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(mockDefaultPlan);
      LearningPlan.create.mockResolvedValue(mockUserPlan);
      PlanTag.findAll.mockResolvedValue(mockPlanTags);
      Tag.create.mockResolvedValue({ id: 2, name: '标签1' });
      PlanTag.create.mockResolvedValue({});
      DailyContent.findAll.mockResolvedValue(mockDailyContents);
      DailyContent.create.mockResolvedValue({});

      // 调用方法
      const result = await learningPlanService.copySystemDefaultPlanToUser('user123');

      // 验证结果
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { is_system_default: true },
        transaction: mockTransaction,
      });

      expect(LearningPlan.create).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'user123',
          theme_id: 1,
          title: '系统默认计划',
          is_system_default: false,
          is_current: true,
        }),
        { transaction: mockTransaction },
      );

      expect(mockTransaction.commit).toHaveBeenCalled();
      expect(result).toEqual(mockUserPlan);
    });

    it('should rollback transaction when default plan not found', async () => {
      const { LearningPlan } = require('../../../models');
      LearningPlan.findOne.mockResolvedValue(null);

      await expect(learningPlanService.copySystemDefaultPlanToUser('user123')).rejects.toThrow(
        '系统默认学习计划不存在',
      );
      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });
});
