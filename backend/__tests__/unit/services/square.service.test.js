/**
 * 广场服务测试
 */
const squareService = require('../../../services/square.service');
const SquareRepository = require('../../../repositories/square.repository');
const logger = require('../../../config/logger');

// 模拟依赖
jest.mock('../../../repositories/square.repository');
jest.mock('../../../config/logger');

describe('Square Service', () => {
  // 在每个测试前重置所有模拟
  beforeEach(() => {
    jest.clearAllMocks();
    // 模拟 SquareRepository 实例
    squareService.squareRepository = new SquareRepository();
  });

  describe('getSquareNotes', () => {
    it('should return square notes successfully', async () => {
      // 模拟数据
      const mockNotes = {
        count: 2,
        rows: [
          {
            id: 1,
            tag_id: 1,
            user_id: 'user1',
            title: 'Note 1',
            content: 'Content 1',
            image_url: 'image1.jpg',
            likes: 10,
            comments: 5,
            is_ai_generated: false,
            created_at: '2023-01-01T00:00:00Z',
            tag: { id: 1, name: 'Tag 1' },
            user: { id: 'user1', nickname: 'User 1', avatar_url: 'avatar1.jpg' }
          },
          {
            id: 2,
            tag_id: 2,
            user_id: 'user2',
            title: 'Note 2',
            content: 'Content 2',
            image_url: 'image2.jpg',
            likes: 20,
            comments: 10,
            is_ai_generated: true,
            created_at: '2023-01-02T00:00:00Z',
            tag: { id: 2, name: 'Tag 2' },
            user: { id: 'user2', nickname: 'User 2', avatar_url: 'avatar2.jpg' }
          }
        ]
      };

      const mockLikedNoteIds = [1];

      // 模拟仓库方法
      squareService.squareRepository.getSquareNotes.mockResolvedValue(mockNotes);
      squareService.squareRepository.getUserLikedNoteIds.mockResolvedValue(mockLikedNoteIds);

      // 调用服务方法
      const result = await squareService.getSquareNotes('user1', { tagId: 'all' }, 1, 10, 'latest');

      // 验证结果
      expect(result).toEqual({
        notes: [
          {
            id: 1,
            tagId: 1,
            tagName: 'Tag 1',
            userId: 'user1',
            userName: 'User 1',
            userAvatar: 'avatar1.jpg',
            title: 'Note 1',
            content: 'Content 1',
            imageUrl: 'image1.jpg',
            likes: 10,
            comments: 5,
            isLiked: true,
            isAiGenerated: false,
            createdAt: '2023-01-01T00:00:00Z'
          },
          {
            id: 2,
            tagId: 2,
            tagName: 'Tag 2',
            userId: 'user2',
            userName: 'User 2',
            userAvatar: 'avatar2.jpg',
            title: 'Note 2',
            content: 'Content 2',
            imageUrl: 'image2.jpg',
            likes: 20,
            comments: 10,
            isLiked: false,
            isAiGenerated: true,
            createdAt: '2023-01-02T00:00:00Z'
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          pageSize: 10,
          totalPages: 1
        }
      });

      // 验证仓库方法调用
      expect(squareService.squareRepository.getSquareNotes).toHaveBeenCalledWith(
        { tagId: 'all' },
        1,
        10,
        'latest'
      );
      expect(squareService.squareRepository.getUserLikedNoteIds).toHaveBeenCalledWith(
        'user1',
        [1, 2]
      );
    });

    it('should handle errors', async () => {
      // 模拟仓库方法抛出错误
      const error = new Error('Database error');
      squareService.squareRepository.getSquareNotes.mockRejectedValue(error);

      // 调用服务方法并验证错误处理
      await expect(squareService.getSquareNotes('user1', {}, 1, 10, 'latest')).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`获取广场笔记列表失败: ${error.message}`);
    });
  });

  describe('getSquareTags', () => {
    it('should return square tags successfully', async () => {
      // 模拟数据
      const mockCurrentPlan = {
        id: 1,
        title: 'Plan 1'
      };

      const mockPlanTags = [
        {
          tag_id: 1,
          weight: 0.8,
          is_primary: true,
          relevance_score: 0.9,
          sort_order: 1,
          tag: {
            id: 1,
            name: 'Tag 1',
            usage_count: 5,
            is_verified: true
          }
        },
        {
          tag_id: 2,
          weight: 0.6,
          is_primary: false,
          relevance_score: 0.7,
          sort_order: 2,
          tag: {
            id: 2,
            name: 'Tag 2',
            usage_count: 3,
            is_verified: false
          }
        }
      ];

      const mockNoteCountMap = {
        1: 10,
        2: 5
      };

      const mockAllNotesCount = 15;

      // 模拟仓库方法
      squareService.squareRepository.getSquareTags.mockResolvedValue({
        currentPlan: mockCurrentPlan,
        planTags: mockPlanTags,
        noteCountMap: mockNoteCountMap
      });
      squareService.squareRepository.getAllNotesCount.mockResolvedValue(mockAllNotesCount);

      // 调用服务方法
      const result = await squareService.getSquareTags('user1');

      // 验证结果
      expect(result).toEqual({
        planId: 1,
        planTitle: 'Plan 1',
        tags: [
          {
            id: 'all',
            name: '推荐',
            weight: 1,
            isPrimary: true,
            relevanceScore: 1,
            sortOrder: 0,
            usageCount: 0,
            isVerified: true,
            noteCount: 15
          },
          {
            id: 1,
            name: 'Tag 1',
            weight: 0.8,
            isPrimary: true,
            relevanceScore: 0.9,
            sortOrder: 1,
            usageCount: 5,
            isVerified: true,
            noteCount: 10
          },
          {
            id: 2,
            name: 'Tag 2',
            weight: 0.6,
            isPrimary: false,
            relevanceScore: 0.7,
            sortOrder: 2,
            usageCount: 3,
            isVerified: false,
            noteCount: 5
          }
        ]
      });

      // 验证仓库方法调用
      expect(squareService.squareRepository.getSquareTags).toHaveBeenCalledWith('user1');
      expect(squareService.squareRepository.getAllNotesCount).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // 模拟仓库方法抛出错误
      const error = new Error('Database error');
      squareService.squareRepository.getSquareTags.mockRejectedValue(error);

      // 调用服务方法并验证错误处理
      await expect(squareService.getSquareTags('user1')).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`获取广场标签列表失败: ${error.message}`);
    });
  });

  describe('getRecommendedNotes', () => {
    it('should return recommended notes successfully', async () => {
      // 模拟数据
      const mockNotes = [
        {
          id: 1,
          tag_id: 1,
          user_id: 'user1',
          title: 'Note 1',
          content: 'Content 1',
          image_url: 'image1.jpg',
          likes: 10,
          comments: 5,
          is_ai_generated: false,
          created_at: '2023-01-01T00:00:00Z',
          tag: { id: 1, name: 'Tag 1' },
          user: { id: 'user1', nickname: 'User 1', avatar_url: 'avatar1.jpg' }
        }
      ];

      const mockLikedNoteIds = [1];

      // 模拟仓库方法
      squareService.squareRepository.getRecommendedNotes.mockResolvedValue({
        notes: mockNotes
      });
      squareService.squareRepository.getUserLikedNoteIds.mockResolvedValue(mockLikedNoteIds);

      // 调用服务方法
      const result = await squareService.getRecommendedNotes('user1', 5);

      // 验证结果
      expect(result).toEqual({
        notes: [
          {
            id: 1,
            tagId: 1,
            tagName: 'Tag 1',
            userId: 'user1',
            userName: 'User 1',
            userAvatar: 'avatar1.jpg',
            title: 'Note 1',
            content: 'Content 1',
            imageUrl: 'image1.jpg',
            likes: 10,
            comments: 5,
            isLiked: true,
            isAiGenerated: false,
            createdAt: '2023-01-01T00:00:00Z'
          }
        ]
      });

      // 验证仓库方法调用
      expect(squareService.squareRepository.getRecommendedNotes).toHaveBeenCalledWith('user1', 5);
      expect(squareService.squareRepository.getUserLikedNoteIds).toHaveBeenCalledWith('user1', [1]);
    });

    it('should handle errors', async () => {
      // 模拟仓库方法抛出错误
      const error = new Error('Database error');
      squareService.squareRepository.getRecommendedNotes.mockRejectedValue(error);

      // 调用服务方法并验证错误处理
      await expect(squareService.getRecommendedNotes('user1', 5)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`获取推荐笔记失败: ${error.message}`);
    });
  });
});
