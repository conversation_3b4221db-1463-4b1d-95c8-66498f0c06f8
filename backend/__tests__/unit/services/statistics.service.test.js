/**
 * 统计服务单元测试
 */
const StatisticsService = require('../../../services/statistics.service');
const logger = require('../../../config/logger');
const { sequelize } = require('../../../models');

// 模拟依赖
jest.mock('../../../config/logger', () => ({
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn()
}));

jest.mock('../../../models', () => ({
  sequelize: {
    transaction: jest.fn()
  }
}));

describe('StatisticsService', () => {
  let statisticsService;
  let mockStatisticsRepository;
  let mockTransaction;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 创建模拟事务
    mockTransaction = {
      commit: jest.fn(),
      rollback: jest.fn()
    };
    
    // 设置模拟事务返回值
    sequelize.transaction.mockResolvedValue(mockTransaction);
    
    // 创建模拟仓库
    mockStatisticsRepository = {
      getLearningActivityStats: jest.fn(),
      getLearningStreaks: jest.fn(),
      getContentCompletionStats: jest.fn(),
      getLearningPlanStats: jest.fn(),
      getTotalLearningTime: jest.fn(),
      getDailyRecords: jest.fn(),
      getOrCreateDailyRecord: jest.fn(),
      recordActivity: jest.fn(),
      updateDailyRecord: jest.fn(),
      getLearningActivities: jest.fn(),
      getLearningTrend: jest.fn()
    };
    
    // 创建服务实例
    statisticsService = new StatisticsService(mockStatisticsRepository);
  });
  
  describe('getLearningStatistics', () => {
    it('should return learning statistics for a user', async () => {
      // 准备模拟数据
      const mockActivityStats = [
        {
          activity_type: 'login',
          dataValues: { count: '10' }
        },
        {
          activity_type: 'view_exercise',
          dataValues: { count: '20' }
        }
      ];
      
      const mockStreaks = {
        currentStreak: 3,
        longestStreak: 7
      };
      
      const mockContentStats = {
        completedExercises: 25,
        viewedInsights: 42,
        createdNotes: 10,
        bubbleInteractions: 15
      };
      
      const mockPlanStats = {
        activePlans: 2,
        completedPlans: 3,
        totalPlans: 5
      };
      
      const mockDailyRecords = {
        count: 15,
        records: []
      };
      
      // 设置模拟函数返回值
      mockStatisticsRepository.getLearningActivityStats.mockResolvedValue(mockActivityStats);
      mockStatisticsRepository.getLearningStreaks.mockResolvedValue(mockStreaks);
      mockStatisticsRepository.getContentCompletionStats.mockResolvedValue(mockContentStats);
      mockStatisticsRepository.getLearningPlanStats.mockResolvedValue(mockPlanStats);
      mockStatisticsRepository.getTotalLearningTime.mockResolvedValue(120);
      mockStatisticsRepository.getDailyRecords.mockResolvedValue(mockDailyRecords);
      
      // 调用方法
      const result = await statisticsService.getLearningStatistics('user123');
      
      // 验证结果
      expect(mockStatisticsRepository.getLearningActivityStats).toHaveBeenCalledWith('user123');
      expect(mockStatisticsRepository.getLearningStreaks).toHaveBeenCalledWith('user123');
      expect(mockStatisticsRepository.getContentCompletionStats).toHaveBeenCalledWith('user123');
      expect(mockStatisticsRepository.getLearningPlanStats).toHaveBeenCalledWith('user123');
      expect(mockStatisticsRepository.getTotalLearningTime).toHaveBeenCalledWith('user123');
      expect(mockStatisticsRepository.getDailyRecords).toHaveBeenCalledWith('user123');
      
      expect(result).toEqual({
        totalStudyDays: 15,
        currentStreak: 3,
        longestStreak: 7,
        completedExercises: 25,
        viewedInsights: 42,
        createdNotes: 10,
        bubbleInteractions: 15,
        totalTimeSpent: 120,
        activePlans: 2,
        completedPlans: 3,
        activityStats: {
          login: 10,
          view_exercise: 20
        }
      });
    });
    
    it('should handle errors', async () => {
      // 设置模拟函数抛出错误
      const error = new Error('Test error');
      mockStatisticsRepository.getLearningActivityStats.mockRejectedValue(error);
      
      // 调用方法并捕获错误
      await expect(statisticsService.getLearningStatistics('user123')).rejects.toThrow(error);
      
      // 验证日志记录
      expect(logger.error).toHaveBeenCalledWith(`获取学习统计数据失败: ${error.message}`);
    });
  });
  
  describe('getDailyRecords', () => {
    it('should return formatted daily records', async () => {
      // 准备模拟数据
      const mockRecords = {
        records: [
          {
            date: new Date('2023-06-15'),
            time_spent: 30,
            exercises_completed: 2,
            insights_viewed: 5,
            notes_created: 1,
            bubble_interactions: 3,
            has_activity: true
          },
          {
            date: new Date('2023-06-16'),
            time_spent: 45,
            exercises_completed: 3,
            insights_viewed: 7,
            notes_created: 2,
            bubble_interactions: 4,
            has_activity: true
          }
        ],
        count: 2,
        limit: 10,
        offset: 0,
        totalPages: 1
      };
      
      // 设置模拟函数返回值
      mockStatisticsRepository.getDailyRecords.mockResolvedValue(mockRecords);
      
      // 调用方法
      const result = await statisticsService.getDailyRecords('user123', {
        startDate: '2023-06-01',
        endDate: '2023-06-30',
        limit: 10,
        offset: 0
      });
      
      // 验证结果
      expect(mockStatisticsRepository.getDailyRecords).toHaveBeenCalledWith(
        'user123',
        new Date('2023-06-01'),
        new Date('2023-06-30'),
        10,
        0
      );
      
      expect(result).toEqual({
        records: [
          {
            date: mockRecords.records[0].date,
            timeSpent: 30,
            exercisesCompleted: 2,
            insightsViewed: 5,
            notesCreated: 1,
            bubbleInteractions: 3,
            hasActivity: true
          },
          {
            date: mockRecords.records[1].date,
            timeSpent: 45,
            exercisesCompleted: 3,
            insightsViewed: 7,
            notesCreated: 2,
            bubbleInteractions: 4,
            hasActivity: true
          }
        ],
        count: 2,
        limit: 10,
        offset: 0,
        totalPages: 1
      });
    });
    
    it('should handle errors', async () => {
      // 设置模拟函数抛出错误
      const error = new Error('Test error');
      mockStatisticsRepository.getDailyRecords.mockRejectedValue(error);
      
      // 调用方法并捕获错误
      await expect(statisticsService.getDailyRecords('user123')).rejects.toThrow(error);
      
      // 验证日志记录
      expect(logger.error).toHaveBeenCalledWith(`获取每日学习记录失败: ${error.message}`);
    });
  });
  
  describe('recordLearningActivity', () => {
    it('should record activity and update daily record', async () => {
      // 准备模拟数据
      const mockActivity = {
        id: 123,
        user_id: 'user123',
        activity_type: 'complete_exercise',
        content_id: 456,
        content_type: 'exercise',
        created_at: new Date()
      };
      
      const mockDailyRecord = {
        id: 789,
        user_id: 'user123',
        date: new Date(),
        time_spent: 30,
        exercises_completed: 2,
        insights_viewed: 5,
        notes_created: 1,
        bubble_interactions: 3,
        has_activity: true
      };
      
      const mockUpdatedRecord = {
        ...mockDailyRecord,
        exercises_completed: 3
      };
      
      // 设置模拟函数返回值
      mockStatisticsRepository.recordActivity.mockResolvedValue(mockActivity);
      mockStatisticsRepository.getOrCreateDailyRecord.mockResolvedValue({
        record: mockDailyRecord,
        created: false
      });
      mockStatisticsRepository.updateDailyRecord.mockResolvedValue(mockUpdatedRecord);
      
      // 调用方法
      const result = await statisticsService.recordLearningActivity(
        'user123',
        'complete_exercise',
        {
          content_id: 456,
          content_type: 'exercise'
        }
      );
      
      // 验证结果
      expect(mockStatisticsRepository.recordActivity).toHaveBeenCalledWith(
        {
          user_id: 'user123',
          activity_type: 'complete_exercise',
          content_id: 456,
          content_type: 'exercise'
        },
        mockTransaction
      );
      
      expect(mockStatisticsRepository.getOrCreateDailyRecord).toHaveBeenCalledWith(
        'user123',
        expect.any(Date),
        mockTransaction
      );
      
      expect(mockStatisticsRepository.updateDailyRecord).toHaveBeenCalledWith(
        mockDailyRecord.id,
        { exercises_completed: 3 },
        mockTransaction
      );
      
      expect(mockTransaction.commit).toHaveBeenCalled();
      expect(mockTransaction.rollback).not.toHaveBeenCalled();
      
      expect(result).toEqual({
        activityId: mockActivity.id,
        activityType: mockActivity.activity_type,
        createdAt: mockActivity.created_at
      });
    });
    
    it('should update time spent when duration is provided', async () => {
      // 准备模拟数据
      const mockActivity = {
        id: 123,
        user_id: 'user123',
        activity_type: 'view_insight',
        duration: 300,
        created_at: new Date()
      };
      
      const mockDailyRecord = {
        id: 789,
        user_id: 'user123',
        date: new Date(),
        time_spent: 30,
        exercises_completed: 2,
        insights_viewed: 5,
        notes_created: 1,
        bubble_interactions: 3,
        has_activity: true
      };
      
      // 设置模拟函数返回值
      mockStatisticsRepository.recordActivity.mockResolvedValue(mockActivity);
      mockStatisticsRepository.getOrCreateDailyRecord.mockResolvedValue({
        record: mockDailyRecord,
        created: false
      });
      
      // 调用方法
      await statisticsService.recordLearningActivity(
        'user123',
        'view_insight',
        {
          duration: 300
        }
      );
      
      // 验证结果
      expect(mockStatisticsRepository.updateDailyRecord).toHaveBeenCalledWith(
        mockDailyRecord.id,
        {
          time_spent: 35, // 30 + 300/60 = 30 + 5 = 35
          insights_viewed: 6 // 5 + 1 = 6
        },
        mockTransaction
      );
    });
    
    it('should handle transaction rollback on error', async () => {
      // 设置模拟函数抛出错误
      const error = new Error('Test error');
      mockStatisticsRepository.recordActivity.mockRejectedValue(error);
      
      // 调用方法并捕获错误
      await expect(statisticsService.recordLearningActivity(
        'user123',
        'view_insight',
        {}
      )).rejects.toThrow(error);
      
      // 验证事务回滚
      expect(mockTransaction.rollback).toHaveBeenCalled();
      expect(mockTransaction.commit).not.toHaveBeenCalled();
      
      // 验证日志记录
      expect(logger.error).toHaveBeenCalledWith(`记录学习活动失败: ${error.message}`);
    });
  });
  
  describe('getLearningActivities', () => {
    it('should return formatted learning activities', async () => {
      // 准备模拟数据
      const mockActivities = {
        activities: [
          {
            id: 123,
            activity_type: 'complete_exercise',
            content_type: 'exercise',
            content_id: 456,
            plan_id: 789,
            duration: 300,
            created_at: new Date('2023-06-15T08:00:00Z')
          },
          {
            id: 124,
            activity_type: 'view_insight',
            content_type: 'insight',
            content_id: 457,
            plan_id: 789,
            duration: 180,
            created_at: new Date('2023-06-15T09:00:00Z')
          }
        ],
        count: 2,
        page: 1,
        pageSize: 20,
        totalPages: 1
      };
      
      // 设置模拟函数返回值
      mockStatisticsRepository.getLearningActivities.mockResolvedValue(mockActivities);
      
      // 调用方法
      const result = await statisticsService.getLearningActivities(
        'user123',
        { activityType: 'complete_exercise' },
        1,
        20
      );
      
      // 验证结果
      expect(mockStatisticsRepository.getLearningActivities).toHaveBeenCalledWith(
        'user123',
        { activityType: 'complete_exercise' },
        1,
        20
      );
      
      expect(result).toEqual({
        activities: [
          {
            id: 123,
            activityType: 'complete_exercise',
            contentType: 'exercise',
            contentId: 456,
            planId: 789,
            duration: 300,
            createdAt: mockActivities.activities[0].created_at
          },
          {
            id: 124,
            activityType: 'view_insight',
            contentType: 'insight',
            contentId: 457,
            planId: 789,
            duration: 180,
            createdAt: mockActivities.activities[1].created_at
          }
        ],
        count: 2,
        page: 1,
        pageSize: 20,
        totalPages: 1
      });
    });
    
    it('should handle errors', async () => {
      // 设置模拟函数抛出错误
      const error = new Error('Test error');
      mockStatisticsRepository.getLearningActivities.mockRejectedValue(error);
      
      // 调用方法并捕获错误
      await expect(statisticsService.getLearningActivities('user123')).rejects.toThrow(error);
      
      // 验证日志记录
      expect(logger.error).toHaveBeenCalledWith(`获取学习活动列表失败: ${error.message}`);
    });
  });
  
  describe('getLearningTrend', () => {
    it('should return learning trend data', async () => {
      // 准备模拟数据
      const mockTrend = [
        {
          date: '2023-06-15',
          timeSpent: 30,
          exercisesCompleted: 2,
          insightsViewed: 5,
          notesCreated: 1,
          hasActivity: true
        },
        {
          date: '2023-06-16',
          timeSpent: 45,
          exercisesCompleted: 3,
          insightsViewed: 7,
          notesCreated: 2,
          hasActivity: true
        }
      ];
      
      // 设置模拟函数返回值
      mockStatisticsRepository.getLearningTrend.mockResolvedValue(mockTrend);
      
      // 调用方法
      const result = await statisticsService.getLearningTrend('user123', 7);
      
      // 验证结果
      expect(mockStatisticsRepository.getLearningTrend).toHaveBeenCalledWith('user123', 7);
      expect(result).toEqual(mockTrend);
    });
    
    it('should handle errors', async () => {
      // 设置模拟函数抛出错误
      const error = new Error('Test error');
      mockStatisticsRepository.getLearningTrend.mockRejectedValue(error);
      
      // 调用方法并捕获错误
      await expect(statisticsService.getLearningTrend('user123')).rejects.toThrow(error);
      
      // 验证日志记录
      expect(logger.error).toHaveBeenCalledWith(`获取学习趋势失败: ${error.message}`);
    });
  });
});
