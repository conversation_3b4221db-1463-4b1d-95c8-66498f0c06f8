const { Tag, LearningPlan, sequelize } = require('../../../models');
const logger = require('../../../config/logger');

// 模拟依赖
jest.mock('../../../models', () => {
  const mockSequelize = {
    transaction: jest.fn().mockResolvedValue({
      commit: jest.fn().mockResolvedValue(),
      rollback: jest.fn().mockResolvedValue()
    }),
    literal: jest.fn().mockImplementation(str => str)
  };

  return {
    Tag: {
      findOne: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      destroy: jest.fn()
    },
    LearningPlan: {
      findOne: jest.fn()
    },
    sequelize: mockSequelize
  };
});

jest.mock('../../../config/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

// 创建标签服务
const tagService = {
  /**
   * 获取学习计划的标签
   * @param {String} planId - 学习计划ID
   * @param {String} userId - 用户ID
   * @returns {Promise<Array>} 标签数组
   */
  async getTagsByPlanId(planId, userId) {
    try {
      // 验证学习计划是否存在且属于当前用户
      const learningPlan = await LearningPlan.findOne({
        where: { id: planId, user_id: userId }
      });

      if (!learningPlan) {
        throw new Error('学习计划不存在');
      }

      // 获取标签
      const tags = await Tag.findAll({
        where: { plan_id: planId },
        order: [['sort_order', 'ASC']]
      });

      return tags;
    } catch (error) {
      logger.error(`获取标签列表失败: ${error.message}`);
      throw error;
    }
  },

  /**
   * 获取当前学习计划的标签
   * @param {String} userId - 用户ID
   * @returns {Promise<Array>} 标签数组
   */
  async getCurrentPlanTags(userId) {
    try {
      // 获取当前学习计划
      const currentPlan = await LearningPlan.findOne({
        where: { user_id: userId, is_current: true }
      });

      if (!currentPlan) {
        throw new Error('没有当前学习计划');
      }

      // 获取标签
      const tags = await Tag.findAll({
        where: { plan_id: currentPlan.id },
        order: [['sort_order', 'ASC']]
      });

      return tags;
    } catch (error) {
      logger.error(`获取当前计划标签失败: ${error.message}`);
      throw error;
    }
  },

  /**
   * 更新标签权重
   * @param {String} tagId - 标签ID
   * @param {String} userId - 用户ID
   * @param {Number} weight - 权重值
   * @returns {Promise<Object>} 更新后的标签
   */
  async updateTagWeight(tagId, userId, weight) {
    const transaction = await sequelize.transaction();

    try {
      // 验证权重值
      if (weight < 0 || weight > 1) {
        throw new Error('权重必须在0-1之间');
      }

      // 查询标签
      const tag = await Tag.findOne({
        where: { id: tagId },
        include: [
          {
            model: LearningPlan,
            as: 'learningPlan',
            where: { user_id: userId },
            attributes: ['id', 'user_id']
          }
        ]
      });

      if (!tag) {
        throw new Error('标签不存在或不属于当前用户');
      }

      // 更新标签权重
      await tag.update({ weight }, { transaction });

      await transaction.commit();

      return tag;
    } catch (error) {
      await transaction.rollback();
      logger.error(`更新标签权重失败: ${error.message}`);
      throw error;
    }
  },

  /**
   * 增加标签使用次数
   * @param {String} tagId - 标签ID
   * @param {String} userId - 用户ID
   * @returns {Promise<Object>} 更新后的标签
   */
  async incrementTagUsage(tagId, userId) {
    const transaction = await sequelize.transaction();

    try {
      // 查询标签
      const tag = await Tag.findOne({
        where: { id: tagId },
        include: [
          {
            model: LearningPlan,
            as: 'learningPlan',
            where: { user_id: userId },
            attributes: ['id', 'user_id']
          }
        ]
      });

      if (!tag) {
        throw new Error('标签不存在或不属于当前用户');
      }

      // 增加使用次数
      await tag.update(
        { usage_count: sequelize.literal('usage_count + 1') },
        { transaction }
      );

      await transaction.commit();

      // 重新获取更新后的标签
      const updatedTag = await Tag.findOne({
        where: { id: tagId }
      });

      return updatedTag;
    } catch (error) {
      await transaction.rollback();
      logger.error(`增加标签使用次数失败: ${error.message}`);
      throw error;
    }
  }
};

describe('Tag Service', () => {
  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();
  });

  describe('getTagsByPlanId', () => {
    it('should return tags for a valid learning plan', async () => {
      // 模拟学习计划查询结果
      const mockLearningPlan = {
        id: 'plan-1',
        user_id: 'user-1',
        title: '测试计划'
      };
      LearningPlan.findOne.mockResolvedValue(mockLearningPlan);

      // 模拟标签查询结果
      const mockTags = [
        {
          id: 'tag-1',
          name: '倾听',
          plan_id: 'plan-1',
          relevance_score: 0.95,
          weight: 0.8,
          usage_count: 5,
          sort_order: 0
        },
        {
          id: 'tag-2',
          name: '表达',
          plan_id: 'plan-1',
          relevance_score: 0.9,
          weight: 0.7,
          usage_count: 3,
          sort_order: 1
        }
      ];
      Tag.findAll.mockResolvedValue(mockTags);

      // 调用服务方法
      const result = await tagService.getTagsByPlanId('plan-1', 'user-1');

      // 验证学习计划查询
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { id: 'plan-1', user_id: 'user-1' }
      });

      // 验证标签查询
      expect(Tag.findAll).toHaveBeenCalledWith({
        where: { plan_id: 'plan-1' },
        order: [['sort_order', 'ASC']]
      });

      // 验证结果
      expect(result).toEqual(mockTags);
    });

    it('should throw error when learning plan does not exist', async () => {
      // 模拟学习计划查询结果 - 未找到
      LearningPlan.findOne.mockResolvedValue(null);

      // 调用服务方法并验证抛出错误
      await expect(tagService.getTagsByPlanId('non-existent-plan', 'user-1'))
        .rejects
        .toThrow('学习计划不存在');

      // 验证学习计划查询
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-plan', user_id: 'user-1' }
      });

      // 验证未查询标签
      expect(Tag.findAll).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      // 模拟数据库错误
      const error = new Error('数据库错误');
      LearningPlan.findOne.mockRejectedValue(error);

      // 调用服务方法并验证抛出错误
      await expect(tagService.getTagsByPlanId('plan-1', 'user-1'))
        .rejects
        .toThrow('数据库错误');

      // 验证错误日志
      expect(logger.error).toHaveBeenCalledWith(`获取标签列表失败: ${error.message}`);
    });
  });

  describe('getCurrentPlanTags', () => {
    it('should return tags for the current learning plan', async () => {
      // 模拟当前学习计划查询结果
      const mockCurrentPlan = {
        id: 'plan-1',
        user_id: 'user-1',
        title: '当前计划',
        is_current: true
      };
      LearningPlan.findOne.mockResolvedValue(mockCurrentPlan);

      // 模拟标签查询结果
      const mockTags = [
        {
          id: 'tag-1',
          name: '倾听',
          plan_id: 'plan-1',
          relevance_score: 0.95,
          weight: 0.8,
          usage_count: 5,
          sort_order: 0
        },
        {
          id: 'tag-2',
          name: '表达',
          plan_id: 'plan-1',
          relevance_score: 0.9,
          weight: 0.7,
          usage_count: 3,
          sort_order: 1
        }
      ];
      Tag.findAll.mockResolvedValue(mockTags);

      // 调用服务方法
      const result = await tagService.getCurrentPlanTags('user-1');

      // 验证当前学习计划查询
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { user_id: 'user-1', is_current: true }
      });

      // 验证标签查询
      expect(Tag.findAll).toHaveBeenCalledWith({
        where: { plan_id: 'plan-1' },
        order: [['sort_order', 'ASC']]
      });

      // 验证结果
      expect(result).toEqual(mockTags);
    });

    it('should throw error when no current plan exists', async () => {
      // 模拟当前学习计划查询结果 - 未找到
      LearningPlan.findOne.mockResolvedValue(null);

      // 调用服务方法并验证抛出错误
      await expect(tagService.getCurrentPlanTags('user-1'))
        .rejects
        .toThrow('没有当前学习计划');

      // 验证当前学习计划查询
      expect(LearningPlan.findOne).toHaveBeenCalledWith({
        where: { user_id: 'user-1', is_current: true }
      });

      // 验证未查询标签
      expect(Tag.findAll).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      // 模拟数据库错误
      const error = new Error('数据库错误');
      LearningPlan.findOne.mockRejectedValue(error);

      // 调用服务方法并验证抛出错误
      await expect(tagService.getCurrentPlanTags('user-1'))
        .rejects
        .toThrow('数据库错误');

      // 验证错误日志
      expect(logger.error).toHaveBeenCalledWith(`获取当前计划标签失败: ${error.message}`);
    });
  });

  describe('updateTagWeight', () => {
    it('should update tag weight successfully', async () => {
      // 模拟标签查询结果
      const mockTag = {
        id: 'tag-1',
        name: '倾听',
        plan_id: 'plan-1',
        relevance_score: 0.95,
        weight: 0.5,
        usage_count: 5,
        sort_order: 0,
        learningPlan: {
          id: 'plan-1',
          user_id: 'user-1'
        },
        update: jest.fn().mockResolvedValue(true)
      };
      Tag.findOne.mockResolvedValue(mockTag);

      // 调用服务方法
      const result = await tagService.updateTagWeight('tag-1', 'user-1', 0.8);

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证标签查询
      expect(Tag.findOne).toHaveBeenCalledWith({
        where: { id: 'tag-1' },
        include: [
          {
            model: LearningPlan,
            as: 'learningPlan',
            where: { user_id: 'user-1' },
            attributes: ['id', 'user_id']
          }
        ]
      });

      // 验证标签更新
      expect(mockTag.update).toHaveBeenCalledWith(
        { weight: 0.8 },
        { transaction }
      );

      // 验证事务提交
      expect(transaction.commit).toHaveBeenCalled();

      // 验证结果
      expect(result).toEqual(mockTag);
    });

    it('should throw error when weight is invalid', async () => {
      // 调用服务方法并验证抛出错误
      await expect(tagService.updateTagWeight('tag-1', 'user-1', 1.5))
        .rejects
        .toThrow('权重必须在0-1之间');

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证未查询标签
      expect(Tag.findOne).not.toHaveBeenCalled();

      // 验证事务回滚
      expect(transaction.rollback).toHaveBeenCalled();
    });

    it('should throw error when tag does not exist', async () => {
      // 模拟标签查询结果 - 未找到
      Tag.findOne.mockResolvedValue(null);

      // 调用服务方法并验证抛出错误
      await expect(tagService.updateTagWeight('non-existent-tag', 'user-1', 0.8))
        .rejects
        .toThrow('标签不存在或不属于当前用户');

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证标签查询
      expect(Tag.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-tag' },
        include: expect.anything()
      });

      // 验证事务回滚
      expect(transaction.rollback).toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      // 模拟数据库错误
      const error = new Error('数据库错误');
      Tag.findOne.mockRejectedValue(error);

      // 调用服务方法并验证抛出错误
      await expect(tagService.updateTagWeight('tag-1', 'user-1', 0.8))
        .rejects
        .toThrow('数据库错误');

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证事务回滚
      expect(transaction.rollback).toHaveBeenCalled();

      // 验证错误日志
      expect(logger.error).toHaveBeenCalledWith(`更新标签权重失败: ${error.message}`);
    });
  });

  describe('incrementTagUsage', () => {
    it('should increment tag usage count successfully', async () => {
      // 模拟标签查询结果
      const mockTag = {
        id: 'tag-1',
        name: '倾听',
        plan_id: 'plan-1',
        relevance_score: 0.95,
        weight: 0.8,
        usage_count: 5,
        sort_order: 0,
        learningPlan: {
          id: 'plan-1',
          user_id: 'user-1'
        },
        update: jest.fn().mockResolvedValue(true)
      };
      Tag.findOne.mockResolvedValueOnce(mockTag).mockResolvedValueOnce({
        ...mockTag,
        usage_count: 6
      });

      // 调用服务方法
      const result = await tagService.incrementTagUsage('tag-1', 'user-1');

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证标签查询
      expect(Tag.findOne).toHaveBeenCalledWith({
        where: { id: 'tag-1' },
        include: [
          {
            model: LearningPlan,
            as: 'learningPlan',
            where: { user_id: 'user-1' },
            attributes: ['id', 'user_id']
          }
        ]
      });

      // 验证标签更新
      expect(mockTag.update).toHaveBeenCalledWith(
        { usage_count: 'usage_count + 1' },
        { transaction }
      );

      // 验证事务提交
      expect(transaction.commit).toHaveBeenCalled();

      // 验证结果
      expect(result.usage_count).toBe(6);
    });

    it('should throw error when tag does not exist', async () => {
      // 模拟标签查询结果 - 未找到
      Tag.findOne.mockResolvedValue(null);

      // 调用服务方法并验证抛出错误
      await expect(tagService.incrementTagUsage('non-existent-tag', 'user-1'))
        .rejects
        .toThrow('标签不存在或不属于当前用户');

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证标签查询
      expect(Tag.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-tag' },
        include: expect.anything()
      });

      // 验证事务回滚
      expect(transaction.rollback).toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      // 模拟数据库错误
      const error = new Error('数据库错误');
      Tag.findOne.mockRejectedValue(error);

      // 调用服务方法并验证抛出错误
      await expect(tagService.incrementTagUsage('tag-1', 'user-1'))
        .rejects
        .toThrow('数据库错误');

      // 验证事务处理
      expect(sequelize.transaction).toHaveBeenCalled();
      const transaction = await sequelize.transaction();

      // 验证事务回滚
      expect(transaction.rollback).toHaveBeenCalled();

      // 验证错误日志
      expect(logger.error).toHaveBeenCalledWith(`增加标签使用次数失败: ${error.message}`);
    });
  });
});
