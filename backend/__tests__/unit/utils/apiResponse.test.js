const apiResponse = require('../../../utils/apiResponse');

describe('API Response Utility', () => {
  let res;
  
  beforeEach(() => {
    // 创建模拟的响应对象
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis()
    };
  });
  
  describe('success', () => {
    it('should return success response with status 200', () => {
      const data = { id: 1, name: 'Test' };
      apiResponse.success(res, data);
      
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Success',
        data
      });
    });
    
    it('should return success response with custom message', () => {
      const data = { id: 1 };
      const message = 'Custom success message';
      apiResponse.success(res, data, message);
      
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message,
        data
      });
    });
    
    it('should return success response with custom status code', () => {
      const data = { id: 1 };
      const message = 'Created';
      const statusCode = 201;
      apiResponse.success(res, data, message, statusCode);
      
      expect(res.status).toHaveBeenCalledWith(statusCode);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message,
        data
      });
    });
  });
  
  describe('error', () => {
    it('should return error response with status 500', () => {
      apiResponse.error(res);
      
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: 'Error',
          details: {}
        }
      });
    });
    
    it('should return error response with custom message and code', () => {
      const message = 'Custom error message';
      const code = 'CUSTOM_ERROR';
      apiResponse.error(res, message, code);
      
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code,
          message,
          details: {}
        }
      });
    });
    
    it('should return error response with custom status code and details', () => {
      const message = 'Validation failed';
      const code = 'VALIDATION_ERROR';
      const statusCode = 422;
      const details = { field: 'Invalid value' };
      apiResponse.error(res, message, code, statusCode, details);
      
      expect(res.status).toHaveBeenCalledWith(statusCode);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code,
          message,
          details
        }
      });
    });
  });
  
  describe('created', () => {
    it('should return created response with status 201', () => {
      const data = { id: 1 };
      apiResponse.created(res, data);
      
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Resource created successfully',
        data
      });
    });
  });
  
  describe('noContent', () => {
    it('should return no content response with status 204', () => {
      apiResponse.noContent(res);
      
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.end).toHaveBeenCalled();
    });
  });
  
  describe('unauthorized', () => {
    it('should return unauthorized response with status 401', () => {
      apiResponse.unauthorized(res);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized access',
          details: {}
        }
      });
    });
  });
  
  describe('forbidden', () => {
    it('should return forbidden response with status 403', () => {
      apiResponse.forbidden(res);
      
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Access forbidden',
          details: {}
        }
      });
    });
  });
  
  describe('notFound', () => {
    it('should return not found response with status 404', () => {
      apiResponse.notFound(res);
      
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Resource not found',
          details: {}
        }
      });
    });
  });
  
  describe('badRequest', () => {
    it('should return bad request response with status 400', () => {
      apiResponse.badRequest(res);
      
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_PARAMS',
          message: 'Invalid parameters',
          details: {}
        }
      });
    });
  });
  
  describe('conflict', () => {
    it('should return conflict response with status 409', () => {
      apiResponse.conflict(res);
      
      expect(res.status).toHaveBeenCalledWith(409);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'DUPLICATE_ENTITY',
          message: 'Resource already exists',
          details: {}
        }
      });
    });
  });
  
  describe('validationError', () => {
    it('should return validation error response with status 422', () => {
      apiResponse.validationError(res);
      
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: {}
        }
      });
    });
  });
});
