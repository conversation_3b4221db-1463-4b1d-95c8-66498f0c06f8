/**
 * 数据转换工具测试
 */
const {
  snakeToCamel,
  camelToSnake,
  transformToCamel,
  transformToSnake
} = require('../../../utils/dataTransformer');

describe('数据转换工具', () => {
  describe('snakeToCamel', () => {
    it('应该将snake_case转换为camelCase', () => {
      expect(snakeToCamel('created_at')).toBe('createdAt');
      expect(snakeToCamel('user_name')).toBe('userName');
      expect(snakeToCamel('id')).toBe('id');
      expect(snakeToCamel('is_deleted')).toBe('isDeleted');
      expect(snakeToCamel('user_notification_setting')).toBe('userNotificationSetting');
    });

    it('应该处理特殊情况', () => {
      expect(snakeToCamel('')).toBe('');
      expect(snakeToCamel('_private')).toBe('Private');
      expect(snakeToCamel('with__double__underscore')).toBe('withDoubleUnderscore');
    });
  });

  describe('camelToSnake', () => {
    it('应该将camelCase转换为snake_case', () => {
      expect(camelToSnake('createdAt')).toBe('created_at');
      expect(camelToSnake('userName')).toBe('user_name');
      expect(camelToSnake('id')).toBe('id');
      expect(camelToSnake('isDeleted')).toBe('is_deleted');
      expect(camelToSnake('userNotificationSetting')).toBe('user_notification_setting');
    });

    it('应该处理特殊情况', () => {
      expect(camelToSnake('')).toBe('');
      expect(camelToSnake('Private')).toBe('_private');
      expect(camelToSnake('withMultipleCapitals')).toBe('with_multiple_capitals');
    });
  });

  describe('transformToCamel', () => {
    it('应该将对象的键从snake_case转换为camelCase', () => {
      const input = {
        id: 1,
        created_at: '2023-01-01',
        user_name: 'test',
        is_deleted: false
      };

      const expected = {
        id: 1,
        createdAt: '2023-01-01',
        userName: 'test',
        isDeleted: false
      };

      expect(transformToCamel(input)).toEqual(expected);
    });

    it('应该递归处理嵌套对象', () => {
      const input = {
        id: 1,
        user_info: {
          first_name: 'John',
          last_name: 'Doe',
          contact_details: {
            email_address: '<EMAIL>',
            phone_number: '123456789'
          }
        },
        created_at: '2023-01-01'
      };

      const expected = {
        id: 1,
        userInfo: {
          firstName: 'John',
          lastName: 'Doe',
          contactDetails: {
            emailAddress: '<EMAIL>',
            phoneNumber: '123456789'
          }
        },
        createdAt: '2023-01-01'
      };

      expect(transformToCamel(input)).toEqual(expected);
    });

    it('应该处理数组', () => {
      const input = [
        { id: 1, user_name: 'user1', created_at: '2023-01-01' },
        { id: 2, user_name: 'user2', created_at: '2023-01-02' }
      ];

      const expected = [
        { id: 1, userName: 'user1', createdAt: '2023-01-01' },
        { id: 2, userName: 'user2', createdAt: '2023-01-02' }
      ];

      expect(transformToCamel(input)).toEqual(expected);
    });

    it('应该处理非对象值', () => {
      expect(transformToCamel(null)).toBeNull();
      expect(transformToCamel(undefined)).toBeUndefined();
      expect(transformToCamel('string')).toBe('string');
      expect(transformToCamel(123)).toBe(123);
      expect(transformToCamel(true)).toBe(true);
    });
  });

  describe('transformToSnake', () => {
    it('应该将对象的键从camelCase转换为snake_case', () => {
      const input = {
        id: 1,
        createdAt: '2023-01-01',
        userName: 'test',
        isDeleted: false
      };

      const expected = {
        id: 1,
        created_at: '2023-01-01',
        user_name: 'test',
        is_deleted: false
      };

      expect(transformToSnake(input)).toEqual(expected);
    });

    it('应该递归处理嵌套对象', () => {
      const input = {
        id: 1,
        userInfo: {
          firstName: 'John',
          lastName: 'Doe',
          contactDetails: {
            emailAddress: '<EMAIL>',
            phoneNumber: '123456789'
          }
        },
        createdAt: '2023-01-01'
      };

      const expected = {
        id: 1,
        user_info: {
          first_name: 'John',
          last_name: 'Doe',
          contact_details: {
            email_address: '<EMAIL>',
            phone_number: '123456789'
          }
        },
        created_at: '2023-01-01'
      };

      expect(transformToSnake(input)).toEqual(expected);
    });

    it('应该处理数组', () => {
      const input = [
        { id: 1, userName: 'user1', createdAt: '2023-01-01' },
        { id: 2, userName: 'user2', createdAt: '2023-01-02' }
      ];

      const expected = [
        { id: 1, user_name: 'user1', created_at: '2023-01-01' },
        { id: 2, user_name: 'user2', created_at: '2023-01-02' }
      ];

      expect(transformToSnake(input)).toEqual(expected);
    });

    it('应该处理非对象值', () => {
      expect(transformToSnake(null)).toBeNull();
      expect(transformToSnake(undefined)).toBeUndefined();
      expect(transformToSnake('string')).toBe('string');
      expect(transformToSnake(123)).toBe(123);
      expect(transformToSnake(true)).toBe(true);
    });
  });
});
