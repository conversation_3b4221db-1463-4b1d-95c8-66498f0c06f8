const jwt = require('jsonwebtoken');
const { generateToken, verifyToken, extractTokenFromHeader } = require('../../../utils/jwt');
const config = require('../../../config/config');

// 模拟jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(),
  verify: jest.fn()
}));

describe('JWT Utilities', () => {
  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();
  });

  describe('generateToken', () => {
    it('should generate a JWT token with the provided payload', () => {
      // 模拟jwt.sign返回值
      const mockToken = 'mock.jwt.token';
      jwt.sign.mockReturnValue(mockToken);

      // 测试数据
      const payload = { userId: 'user-123', nickname: 'Test User' };

      // 调用方法
      const token = generateToken(payload);

      // 验证结果
      expect(token).toBe(mockToken);

      // 验证jwt.sign调用
      expect(jwt.sign).toHaveBeenCalledWith(
        payload,
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
      );
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token and return the decoded payload', () => {
      // 模拟jwt.verify返回值
      const mockPayload = { userId: 'user-123', nickname: 'Test User', iat: 1234567890, exp: 9876543210 };
      jwt.verify.mockReturnValue(mockPayload);

      // 测试数据
      const token = 'valid.jwt.token';

      // 调用方法
      const result = verifyToken(token);

      // 验证结果
      expect(result).toEqual(mockPayload);

      // 验证jwt.verify调用
      expect(jwt.verify).toHaveBeenCalledWith(token, config.jwt.secret);
    });

    it('should return null for an invalid token', () => {
      // 模拟jwt.verify抛出错误
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // 测试数据
      const token = 'invalid.jwt.token';

      // 调用方法
      const result = verifyToken(token);

      // 验证结果
      expect(result).toBeNull();

      // 验证jwt.verify调用
      expect(jwt.verify).toHaveBeenCalledWith(token, config.jwt.secret);
    });
  });

  describe('extractTokenFromHeader', () => {
    it('should extract token from valid authorization header', () => {
      // 测试数据
      const req = {
        headers: {
          authorization: 'Bearer valid.jwt.token'
        }
      };

      // 调用方法
      const token = extractTokenFromHeader(req);

      // 验证结果
      expect(token).toBe('valid.jwt.token');
    });

    it('should return null when authorization header is missing', () => {
      // 测试数据
      const req = {
        headers: {}
      };

      // 调用方法
      const token = extractTokenFromHeader(req);

      // 验证结果
      expect(token).toBeNull();
    });

    it('should return null when authorization header does not start with "Bearer "', () => {
      // 测试数据
      const req = {
        headers: {
          authorization: 'Basic dXNlcjpwYXNzd29yZA=='
        }
      };

      // 调用方法
      const token = extractTokenFromHeader(req);

      // 验证结果
      expect(token).toBeNull();
    });
  });
});
