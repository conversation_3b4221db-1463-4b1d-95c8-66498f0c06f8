/**
 * 事务管理工具类单元测试
 */
const transactionManager = require('../../../utils/transaction-manager');
const { sequelize } = require('../../../models');
const logger = require('../../../config/logger');

// 模拟依赖
jest.mock('../../../models', () => {
  const mockTransaction = {
    commit: jest.fn().mockResolvedValue(),
    rollback: jest.fn().mockResolvedValue()
  };
  
  return {
    sequelize: {
      transaction: jest.fn().mockResolvedValue(mockTransaction)
    }
  };
});

jest.mock('../../../config/logger', () => ({
  debug: jest.fn(),
  error: jest.fn()
}));

describe('TransactionManager', () => {
  let mockTransaction;
  
  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();
    
    // 获取模拟事务对象
    mockTransaction = sequelize.transaction.mock.results[0]?.value;
  });
  
  describe('runInTransaction', () => {
    it('should execute callback and commit transaction on success', async () => {
      // 准备
      const callback = jest.fn().mockResolvedValue('result');
      
      // 执行
      const result = await transactionManager.runInTransaction(callback, { context: 'test' });
      
      // 验证
      expect(sequelize.transaction).toHaveBeenCalled();
      expect(callback).toHaveBeenCalledWith(mockTransaction);
      expect(mockTransaction.commit).toHaveBeenCalled();
      expect(mockTransaction.rollback).not.toHaveBeenCalled();
      expect(result).toBe('result');
      expect(logger.debug).toHaveBeenCalledWith(expect.stringContaining('事务成功完成 [test]'));
    });
    
    it('should rollback transaction on error', async () => {
      // 准备
      const error = new Error('Test error');
      const callback = jest.fn().mockRejectedValue(error);
      
      // 执行和验证
      await expect(transactionManager.runInTransaction(callback, { context: 'test' }))
        .rejects.toThrow(error);
      
      // 验证
      expect(sequelize.transaction).toHaveBeenCalled();
      expect(callback).toHaveBeenCalledWith(mockTransaction);
      expect(mockTransaction.commit).not.toHaveBeenCalled();
      expect(mockTransaction.rollback).toHaveBeenCalled();
      expect(logger.debug).toHaveBeenCalledWith(expect.stringContaining('事务已回滚 [test]'));
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('事务失败 [test]'));
    });
    
    it('should pass transaction options to sequelize.transaction', async () => {
      // 准备
      const callback = jest.fn().mockResolvedValue('result');
      const options = {
        readOnly: true,
        isolationLevel: 'READ COMMITTED',
        timeout: 30000,
        context: 'test'
      };
      
      // 执行
      await transactionManager.runInTransaction(callback, options);
      
      // 验证
      expect(sequelize.transaction).toHaveBeenCalledWith({
        readOnly: true,
        isolationLevel: 'READ COMMITTED',
        timeout: 30000
      });
    });
    
    it('should handle rollback error', async () => {
      // 准备
      const error = new Error('Test error');
      const rollbackError = new Error('Rollback error');
      const callback = jest.fn().mockRejectedValue(error);
      
      // 模拟rollback失败
      mockTransaction.rollback.mockRejectedValue(rollbackError);
      
      // 执行和验证
      await expect(transactionManager.runInTransaction(callback, { context: 'test' }))
        .rejects.toThrow(error); // 应该抛出原始错误，而不是回滚错误
      
      // 验证
      expect(mockTransaction.rollback).toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('事务回滚失败 [test]'));
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('事务失败 [test]'));
    });
  });
  
  describe('runInReadOnlyTransaction', () => {
    it('should call runInTransaction with readOnly option', async () => {
      // 准备
      const callback = jest.fn().mockResolvedValue('result');
      const spy = jest.spyOn(transactionManager, 'runInTransaction');
      
      // 执行
      await transactionManager.runInReadOnlyTransaction(callback, { context: 'test' });
      
      // 验证
      expect(spy).toHaveBeenCalledWith(callback, { context: 'test', readOnly: true });
    });
  });
  
  describe('runOperationsInTransaction', () => {
    it('should execute all operations in a single transaction', async () => {
      // 准备
      const operations = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockResolvedValue('result2'),
        jest.fn().mockResolvedValue('result3')
      ];
      
      // 执行
      const results = await transactionManager.runOperationsInTransaction(operations, { context: 'test' });
      
      // 验证
      expect(sequelize.transaction).toHaveBeenCalled();
      operations.forEach(operation => {
        expect(operation).toHaveBeenCalledWith(mockTransaction);
      });
      expect(mockTransaction.commit).toHaveBeenCalled();
      expect(results).toEqual(['result1', 'result2', 'result3']);
    });
    
    it('should rollback if any operation fails', async () => {
      // 准备
      const error = new Error('Test error');
      const operations = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockRejectedValue(error),
        jest.fn().mockResolvedValue('result3')
      ];
      
      // 执行和验证
      await expect(transactionManager.runOperationsInTransaction(operations, { context: 'test' }))
        .rejects.toThrow(error);
      
      // 验证
      expect(operations[0]).toHaveBeenCalled();
      expect(operations[1]).toHaveBeenCalled();
      expect(operations[2]).not.toHaveBeenCalled(); // 第三个操作不应该被执行
      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });
  
  describe('runBatchOperationInTransaction', () => {
    it('should process items in batches', async () => {
      // 准备
      const items = [1, 2, 3, 4, 5];
      const processItem = jest.fn().mockImplementation(item => item * 2);
      const spy = jest.spyOn(transactionManager, 'runInTransaction');
      
      // 执行
      const result = await transactionManager.runBatchOperationInTransaction(items, processItem, {
        batchSize: 2,
        context: 'test'
      });
      
      // 验证
      expect(spy).toHaveBeenCalledTimes(3); // 5个项目，每批2个，需要3个事务
      expect(processItem).toHaveBeenCalledTimes(5);
      expect(result.success).toHaveLength(5);
      expect(result.failure).toHaveLength(0);
    });
    
    it('should handle item processing errors with continueOnError=true', async () => {
      // 准备
      const items = [1, 2, 3, 4, 5];
      const error = new Error('Test error');
      const processItem = jest.fn().mockImplementation(item => {
        if (item === 3) {
          throw error;
        }
        return item * 2;
      });
      
      // 执行
      const result = await transactionManager.runBatchOperationInTransaction(items, processItem, {
        batchSize: 2,
        continueOnError: true,
        context: 'test'
      });
      
      // 验证
      expect(processItem).toHaveBeenCalledTimes(5);
      expect(result.success).toHaveLength(4);
      expect(result.failure).toHaveLength(1);
      expect(result.failure[0].item).toBe(3);
      expect(result.failure[0].error).toBe(error);
    });
    
    it('should stop processing on error with continueOnError=false', async () => {
      // 准备
      const items = [1, 2, 3, 4, 5];
      const error = new Error('Test error');
      const processItem = jest.fn().mockImplementation(item => {
        if (item === 2) {
          throw error;
        }
        return item * 2;
      });
      
      // 执行
      const result = await transactionManager.runBatchOperationInTransaction(items, processItem, {
        batchSize: 2,
        continueOnError: false,
        context: 'test'
      });
      
      // 验证
      expect(processItem).toHaveBeenCalledTimes(2);
      expect(result.success).toHaveLength(1);
      expect(result.failure).toHaveLength(1);
      expect(result.failure[0].item).toBe(2);
      expect(result.failure[0].error).toBe(error);
    });
  });
});
