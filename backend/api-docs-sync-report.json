{"timestamp": "2025-05-27T11:43:15.512Z", "summary": {"totalRoutes": 95, "documentedRoutes": 65, "undocumentedRoutes": 30, "inconsistentRoutes": 65, "deprecatedRoutes": 0, "issueCount": 163, "documentationCoverage": 68}, "issues": [{"type": "swagger_missing", "endpoint": "GET /", "file": "routes/themeV2.routes.js", "line": 29, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /{id}", "file": "routes/themeV2.routes.js", "line": 53, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "DELETE /{id}/soft-delete", "file": "routes/themeV2.routes.js", "line": 83, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /{id}/restore", "file": "routes/themeV2.routes.js", "line": 115, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /deleted", "file": "routes/themeV2.routes.js", "line": 149, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "DELETE /tags/{id}/soft-delete", "file": "routes/tagV2.routes.js", "line": 19, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /tags/{id}/restore", "file": "routes/tagV2.routes.js", "line": 31, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /tags/deleted", "file": "routes/tagV2.routes.js", "line": 43, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /statistics/learning", "file": "routes/statisticsV2.routes.js", "line": 87, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /statistics/daily", "file": "routes/statisticsV2.routes.js", "line": 188, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /statistics/activities", "file": "routes/statisticsV2.routes.js", "line": 273, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /statistics/activities", "file": "routes/statisticsV2.routes.js", "line": 417, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /statistics/overview", "file": "routes/statisticsV2.routes.js", "line": 484, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /statistics/trend", "file": "routes/statisticsV2.routes.js", "line": 531, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /metrics", "file": "routes/statisticsMonitor.routes.js", "line": 64, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /endpoints", "file": "routes/statisticsMonitor.routes.js", "line": 120, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /endpoints/{endpoint}", "file": "routes/statisticsMonitor.routes.js", "line": 183, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /slow-requests", "file": "routes/statisticsMonitor.routes.js", "line": 242, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /reset", "file": "routes/statisticsMonitor.routes.js", "line": 286, "message": "Swagger规范中缺少此端点"}, {"type": "missing_documentation", "endpoint": "GET /square/notes", "file": "routes/squareV2.routes.js", "line": 13, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /square/tags", "file": "routes/squareV2.routes.js", "line": 25, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /square/recommended-notes", "file": "routes/squareV2.routes.js", "line": 30, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /tags/{tagId}/notes", "file": "routes/noteV2.routes.js", "line": 14, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /notes/user", "file": "routes/noteV2.routes.js", "line": 25, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /notes/{id}", "file": "routes/noteV2.routes.js", "line": 36, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /notes", "file": "routes/noteV2.routes.js", "line": 42, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "PUT /notes/{id}", "file": "routes/noteV2.routes.js", "line": 58, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "DELETE /notes/{id}", "file": "routes/noteV2.routes.js", "line": 74, "message": "缺少API文档"}, {"type": "swagger_missing", "endpoint": "DELETE /notes/{id}/soft-delete", "file": "routes/noteV2.routes.js", "line": 85, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /notes/{id}/restore", "file": "routes/noteV2.routes.js", "line": 96, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /notes/deleted", "file": "routes/noteV2.routes.js", "line": 107, "message": "Swagger规范中缺少此端点"}, {"type": "missing_documentation", "endpoint": "POST /notes/{noteId}/like", "file": "routes/noteV2.routes.js", "line": 118, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /notes/{noteId}/comments", "file": "routes/noteV2.routes.js", "line": 124, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /users/{userId}", "file": "routes/mockData.routes.js", "line": 6, "message": "缺少API文档"}, {"type": "swagger_missing", "endpoint": "GET /", "file": "routes/learningPlanV2.routes.js", "line": 61, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /{id}", "file": "routes/learningPlanV2.routes.js", "line": 89, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /", "file": "routes/learningPlanV2.routes.js", "line": 131, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "PUT /{id}", "file": "routes/learningPlanV2.routes.js", "line": 177, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "DELETE /{id}/soft-delete", "file": "routes/learningPlanV2.routes.js", "line": 205, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /{id}/restore", "file": "routes/learningPlanV2.routes.js", "line": 235, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /deleted", "file": "routes/learningPlanV2.routes.js", "line": 267, "message": "Swagger规范中缺少此端点"}, {"type": "missing_documentation", "endpoint": "GET /tags/{tagId}/insights", "file": "routes/insightV2.routes.js", "line": 13, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /insights/{id}", "file": "routes/insightV2.routes.js", "line": 24, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /insights", "file": "routes/insightV2.routes.js", "line": 30, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "PUT /insights/{id}", "file": "routes/insightV2.routes.js", "line": 43, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "DELETE /insights/{id}", "file": "routes/insightV2.routes.js", "line": 55, "message": "缺少API文档"}, {"type": "swagger_missing", "endpoint": "DELETE /insights/{id}/soft-delete", "file": "routes/insightV2.routes.js", "line": 66, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /insights/{id}/restore", "file": "routes/insightV2.routes.js", "line": 77, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /tags/{tagId}/insights/deleted", "file": "routes/insightV2.routes.js", "line": 88, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /", "file": "routes/health.routes.js", "line": 78, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /liveness", "file": "routes/health.routes.js", "line": 172, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /readiness", "file": "routes/health.routes.js", "line": 208, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /database", "file": "routes/health.routes.js", "line": 252, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /redis", "file": "routes/health.routes.js", "line": 267, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /system", "file": "routes/health.routes.js", "line": 282, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /full", "file": "routes/health.routes.js", "line": 297, "message": "Swagger规范中缺少此端点"}, {"type": "missing_documentation", "endpoint": "GET /tags/{tagId}/exercises", "file": "routes/exerciseV2.routes.js", "line": 13, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /exercises/{id}", "file": "routes/exerciseV2.routes.js", "line": 26, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /exercises", "file": "routes/exerciseV2.routes.js", "line": 32, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "PUT /exercises/{id}", "file": "routes/exerciseV2.routes.js", "line": 49, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "DELETE /exercises/{id}", "file": "routes/exerciseV2.routes.js", "line": 65, "message": "缺少API文档"}, {"type": "swagger_missing", "endpoint": "DELETE /exercises/{id}/soft-delete", "file": "routes/exerciseV2.routes.js", "line": 76, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /exercises/{id}/restore", "file": "routes/exerciseV2.routes.js", "line": 87, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /tags/{tagId}/exercises/deleted", "file": "routes/exerciseV2.routes.js", "line": 98, "message": "Swagger规范中缺少此端点"}, {"type": "missing_documentation", "endpoint": "GET /error-monitor/stats", "file": "routes/errorMonitor.routes.js", "line": 9, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /error-monitor/reset", "file": "routes/errorMonitor.routes.js", "line": 15, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /error-monitor/dates", "file": "routes/errorMonitor.routes.js", "line": 21, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /error-monitor/logs/{date}", "file": "routes/errorMonitor.routes.js", "line": 27, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /", "file": "routes/deadLetterQueue.routes.js", "line": 13, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "GET /{id}", "file": "routes/deadLetterQueue.routes.js", "line": 16, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /{id}/retry", "file": "routes/deadLetterQueue.routes.js", "line": 19, "message": "缺少API文档"}, {"type": "missing_documentation", "endpoint": "POST /{id}/resolve", "file": "routes/deadLetterQueue.routes.js", "line": 22, "message": "缺少API文档"}, {"type": "swagger_missing", "endpoint": "GET /plan/{planId}", "file": "routes/dailyContentV2.routes.js", "line": 36, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /{id}", "file": "routes/dailyContentV2.routes.js", "line": 64, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "PUT /{id}", "file": "routes/dailyContentV2.routes.js", "line": 111, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "DELETE /{id}/soft-delete", "file": "routes/dailyContentV2.routes.js", "line": 139, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /{id}/restore", "file": "routes/dailyContentV2.routes.js", "line": 169, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /plan/{planId}/deleted", "file": "routes/dailyContentV2.routes.js", "line": 209, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /config", "file": "routes/cleanup.routes.js", "line": 29, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "PUT /config", "file": "routes/cleanup.routes.js", "line": 80, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /run", "file": "routes/cleanup.routes.js", "line": 119, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /start", "file": "routes/cleanup.routes.js", "line": 140, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /stop", "file": "routes/cleanup.routes.js", "line": 161, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /tags/soft-delete", "file": "routes/batchOperation.routes.js", "line": 49, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /tags/restore", "file": "routes/batchOperation.routes.js", "line": 86, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /users/soft-delete", "file": "routes/batchOperation.routes.js", "line": 127, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /users/restore", "file": "routes/batchOperation.routes.js", "line": 164, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /notes/soft-delete", "file": "routes/batchOperation.routes.js", "line": 199, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /notes/restore", "file": "routes/batchOperation.routes.js", "line": 234, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /insights/soft-delete", "file": "routes/batchOperation.routes.js", "line": 269, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /insights/restore", "file": "routes/batchOperation.routes.js", "line": 304, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "GET /stats", "file": "routes/ai.routes.js", "line": 81, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /stats/reset", "file": "routes/ai.routes.js", "line": 114, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /test/tags", "file": "routes/ai.routes.js", "line": 212, "message": "Swagger规范中缺少此端点"}, {"type": "swagger_missing", "endpoint": "POST /learning-plans/generate", "file": "routes/ai.routes.js", "line": 310, "message": "Swagger规范中缺少此端点"}, {"type": "orphaned_documentation", "endpoint": "GET /ai/stats", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /ai/stats/reset", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /ai/test/tags", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /ai/learning-plans/generate", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/tags/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/tags/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/users/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/users/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/notes/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/notes/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/insights/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/batch/insights/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/cleanup/config", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "PUT /api/v2/cleanup/config", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/cleanup/run", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/cleanup/start", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/cleanup/stop", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/daily-contents/plan/{planId}", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/daily-contents/{id}", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "PUT /api/v2/daily-contents/{id}", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "DELETE /api/v2/daily-contents/{id}/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/daily-contents/{id}/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/daily-contents/plan/{planId}/deleted", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /health", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /health/liveness", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /health/readiness", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /health/database", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /health/redis", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /health/system", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /health/full", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/learning-plans", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/learning-plans", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/learning-plans/{id}", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "PUT /api/v2/learning-plans/{id}", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "DELETE /api/v2/learning-plans/{id}/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/learning-plans/{id}/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/learning-plans/deleted", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/statistics/monitor/metrics", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/statistics/monitor/endpoints", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/statistics/monitor/endpoints/{endpoint}", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/statistics/monitor/slow-requests", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/statistics/monitor/reset", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/statistics/learning", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/statistics/daily", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v1/statistics/activities", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/statistics/activities", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/statistics/overview", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/statistics/trend", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/themes", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/themes/{id}", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "DELETE /api/v2/themes/{id}/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/themes/{id}/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/themes/deleted", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "DELETE /api/v2/exercises/{id}/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/exercises/{id}/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/tags/{tagId}/exercises/deleted", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "DELETE /api/v2/insights/{id}/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/insights/{id}/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/tags/{tagId}/insights/deleted", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "DELETE /api/v2/notes/{id}/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/notes/{id}/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/notes/deleted", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/square/notes", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/square/tags", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v1/square/recommended-notes", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "DELETE /api/v2/tags/{id}/soft-delete", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "POST /api/v2/tags/{id}/restore", "message": "Swagger文档中存在但未实现的端点"}, {"type": "orphaned_documentation", "endpoint": "GET /api/v2/tags/deleted", "message": "Swagger文档中存在但未实现的端点"}], "recommendations": [{"type": "documentation", "priority": "high", "message": "有 30 个API端点缺少文档，建议添加Swagger注释"}, {"type": "consistency", "priority": "medium", "message": "有 65 个API端点文档不一致，需要同步更新"}, {"type": "coverage", "priority": "high", "message": "API文档覆盖率仅为 68%，建议提升至80%以上"}]}