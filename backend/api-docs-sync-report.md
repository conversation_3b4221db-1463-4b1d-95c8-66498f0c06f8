# API文档同步报告

**生成时间**: 2025/5/27 19:43:15

## 📊 概览

| 指标 | 数量 |
|------|------|
| 总API端点 | 95 |
| 已文档化 | 65 |
| 未文档化 | 30 |
| 不一致 | 65 |
| 已废弃 | 0 |
| 文档覆盖率 | 68% |
| 问题总数 | 163 |

## ❌ 发现的问题

### Swagger规范缺失 (65)

1. **GET /**
   - Swagger规范中缺少此端点
   - 文件: routes/themeV2.routes.js:29

2. **GET /{id}**
   - Swagger规范中缺少此端点
   - 文件: routes/themeV2.routes.js:53

3. **DELETE /{id}/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/themeV2.routes.js:83

4. **POST /{id}/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/themeV2.routes.js:115

5. **GET /deleted**
   - Swagger规范中缺少此端点
   - 文件: routes/themeV2.routes.js:149

6. **DELETE /tags/{id}/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/tagV2.routes.js:19

7. **POST /tags/{id}/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/tagV2.routes.js:31

8. **GET /tags/deleted**
   - Swagger规范中缺少此端点
   - 文件: routes/tagV2.routes.js:43

9. **GET /statistics/learning**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsV2.routes.js:87

10. **GET /statistics/daily**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsV2.routes.js:188

11. **POST /statistics/activities**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsV2.routes.js:273

12. **GET /statistics/activities**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsV2.routes.js:417

13. **GET /statistics/overview**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsV2.routes.js:484

14. **GET /statistics/trend**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsV2.routes.js:531

15. **GET /metrics**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsMonitor.routes.js:64

16. **GET /endpoints**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsMonitor.routes.js:120

17. **GET /endpoints/{endpoint}**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsMonitor.routes.js:183

18. **GET /slow-requests**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsMonitor.routes.js:242

19. **POST /reset**
   - Swagger规范中缺少此端点
   - 文件: routes/statisticsMonitor.routes.js:286

20. **DELETE /notes/{id}/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/noteV2.routes.js:85

21. **POST /notes/{id}/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/noteV2.routes.js:96

22. **GET /notes/deleted**
   - Swagger规范中缺少此端点
   - 文件: routes/noteV2.routes.js:107

23. **GET /**
   - Swagger规范中缺少此端点
   - 文件: routes/learningPlanV2.routes.js:61

24. **GET /{id}**
   - Swagger规范中缺少此端点
   - 文件: routes/learningPlanV2.routes.js:89

25. **POST /**
   - Swagger规范中缺少此端点
   - 文件: routes/learningPlanV2.routes.js:131

26. **PUT /{id}**
   - Swagger规范中缺少此端点
   - 文件: routes/learningPlanV2.routes.js:177

27. **DELETE /{id}/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/learningPlanV2.routes.js:205

28. **POST /{id}/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/learningPlanV2.routes.js:235

29. **GET /deleted**
   - Swagger规范中缺少此端点
   - 文件: routes/learningPlanV2.routes.js:267

30. **DELETE /insights/{id}/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/insightV2.routes.js:66

31. **POST /insights/{id}/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/insightV2.routes.js:77

32. **GET /tags/{tagId}/insights/deleted**
   - Swagger规范中缺少此端点
   - 文件: routes/insightV2.routes.js:88

33. **GET /**
   - Swagger规范中缺少此端点
   - 文件: routes/health.routes.js:78

34. **GET /liveness**
   - Swagger规范中缺少此端点
   - 文件: routes/health.routes.js:172

35. **GET /readiness**
   - Swagger规范中缺少此端点
   - 文件: routes/health.routes.js:208

36. **GET /database**
   - Swagger规范中缺少此端点
   - 文件: routes/health.routes.js:252

37. **GET /redis**
   - Swagger规范中缺少此端点
   - 文件: routes/health.routes.js:267

38. **GET /system**
   - Swagger规范中缺少此端点
   - 文件: routes/health.routes.js:282

39. **GET /full**
   - Swagger规范中缺少此端点
   - 文件: routes/health.routes.js:297

40. **DELETE /exercises/{id}/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/exerciseV2.routes.js:76

41. **POST /exercises/{id}/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/exerciseV2.routes.js:87

42. **GET /tags/{tagId}/exercises/deleted**
   - Swagger规范中缺少此端点
   - 文件: routes/exerciseV2.routes.js:98

43. **GET /plan/{planId}**
   - Swagger规范中缺少此端点
   - 文件: routes/dailyContentV2.routes.js:36

44. **GET /{id}**
   - Swagger规范中缺少此端点
   - 文件: routes/dailyContentV2.routes.js:64

45. **PUT /{id}**
   - Swagger规范中缺少此端点
   - 文件: routes/dailyContentV2.routes.js:111

46. **DELETE /{id}/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/dailyContentV2.routes.js:139

47. **POST /{id}/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/dailyContentV2.routes.js:169

48. **GET /plan/{planId}/deleted**
   - Swagger规范中缺少此端点
   - 文件: routes/dailyContentV2.routes.js:209

49. **GET /config**
   - Swagger规范中缺少此端点
   - 文件: routes/cleanup.routes.js:29

50. **PUT /config**
   - Swagger规范中缺少此端点
   - 文件: routes/cleanup.routes.js:80

51. **POST /run**
   - Swagger规范中缺少此端点
   - 文件: routes/cleanup.routes.js:119

52. **POST /start**
   - Swagger规范中缺少此端点
   - 文件: routes/cleanup.routes.js:140

53. **POST /stop**
   - Swagger规范中缺少此端点
   - 文件: routes/cleanup.routes.js:161

54. **POST /tags/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:49

55. **POST /tags/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:86

56. **POST /users/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:127

57. **POST /users/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:164

58. **POST /notes/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:199

59. **POST /notes/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:234

60. **POST /insights/soft-delete**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:269

61. **POST /insights/restore**
   - Swagger规范中缺少此端点
   - 文件: routes/batchOperation.routes.js:304

62. **GET /stats**
   - Swagger规范中缺少此端点
   - 文件: routes/ai.routes.js:81

63. **POST /stats/reset**
   - Swagger规范中缺少此端点
   - 文件: routes/ai.routes.js:114

64. **POST /test/tags**
   - Swagger规范中缺少此端点
   - 文件: routes/ai.routes.js:212

65. **POST /learning-plans/generate**
   - Swagger规范中缺少此端点
   - 文件: routes/ai.routes.js:310

### 缺少文档 (30)

1. **GET /square/notes**
   - 缺少API文档
   - 文件: routes/squareV2.routes.js:13

2. **GET /square/tags**
   - 缺少API文档
   - 文件: routes/squareV2.routes.js:25

3. **GET /square/recommended-notes**
   - 缺少API文档
   - 文件: routes/squareV2.routes.js:30

4. **GET /tags/{tagId}/notes**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:14

5. **GET /notes/user**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:25

6. **GET /notes/{id}**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:36

7. **POST /notes**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:42

8. **PUT /notes/{id}**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:58

9. **DELETE /notes/{id}**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:74

10. **POST /notes/{noteId}/like**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:118

11. **POST /notes/{noteId}/comments**
   - 缺少API文档
   - 文件: routes/noteV2.routes.js:124

12. **POST /users/{userId}**
   - 缺少API文档
   - 文件: routes/mockData.routes.js:6

13. **GET /tags/{tagId}/insights**
   - 缺少API文档
   - 文件: routes/insightV2.routes.js:13

14. **GET /insights/{id}**
   - 缺少API文档
   - 文件: routes/insightV2.routes.js:24

15. **POST /insights**
   - 缺少API文档
   - 文件: routes/insightV2.routes.js:30

16. **PUT /insights/{id}**
   - 缺少API文档
   - 文件: routes/insightV2.routes.js:43

17. **DELETE /insights/{id}**
   - 缺少API文档
   - 文件: routes/insightV2.routes.js:55

18. **GET /tags/{tagId}/exercises**
   - 缺少API文档
   - 文件: routes/exerciseV2.routes.js:13

19. **GET /exercises/{id}**
   - 缺少API文档
   - 文件: routes/exerciseV2.routes.js:26

20. **POST /exercises**
   - 缺少API文档
   - 文件: routes/exerciseV2.routes.js:32

21. **PUT /exercises/{id}**
   - 缺少API文档
   - 文件: routes/exerciseV2.routes.js:49

22. **DELETE /exercises/{id}**
   - 缺少API文档
   - 文件: routes/exerciseV2.routes.js:65

23. **GET /error-monitor/stats**
   - 缺少API文档
   - 文件: routes/errorMonitor.routes.js:9

24. **POST /error-monitor/reset**
   - 缺少API文档
   - 文件: routes/errorMonitor.routes.js:15

25. **GET /error-monitor/dates**
   - 缺少API文档
   - 文件: routes/errorMonitor.routes.js:21

26. **GET /error-monitor/logs/{date}**
   - 缺少API文档
   - 文件: routes/errorMonitor.routes.js:27

27. **GET /**
   - 缺少API文档
   - 文件: routes/deadLetterQueue.routes.js:13

28. **GET /{id}**
   - 缺少API文档
   - 文件: routes/deadLetterQueue.routes.js:16

29. **POST /{id}/retry**
   - 缺少API文档
   - 文件: routes/deadLetterQueue.routes.js:19

30. **POST /{id}/resolve**
   - 缺少API文档
   - 文件: routes/deadLetterQueue.routes.js:22

### 孤立文档 (68)

1. **GET /ai/stats**
   - Swagger文档中存在但未实现的端点

2. **POST /ai/stats/reset**
   - Swagger文档中存在但未实现的端点

3. **POST /ai/test/tags**
   - Swagger文档中存在但未实现的端点

4. **POST /ai/learning-plans/generate**
   - Swagger文档中存在但未实现的端点

5. **POST /api/v2/batch/tags/soft-delete**
   - Swagger文档中存在但未实现的端点

6. **POST /api/v2/batch/tags/restore**
   - Swagger文档中存在但未实现的端点

7. **POST /api/v2/batch/users/soft-delete**
   - Swagger文档中存在但未实现的端点

8. **POST /api/v2/batch/users/restore**
   - Swagger文档中存在但未实现的端点

9. **POST /api/v2/batch/notes/soft-delete**
   - Swagger文档中存在但未实现的端点

10. **POST /api/v2/batch/notes/restore**
   - Swagger文档中存在但未实现的端点

11. **POST /api/v2/batch/insights/soft-delete**
   - Swagger文档中存在但未实现的端点

12. **POST /api/v2/batch/insights/restore**
   - Swagger文档中存在但未实现的端点

13. **GET /api/v2/cleanup/config**
   - Swagger文档中存在但未实现的端点

14. **PUT /api/v2/cleanup/config**
   - Swagger文档中存在但未实现的端点

15. **POST /api/v2/cleanup/run**
   - Swagger文档中存在但未实现的端点

16. **POST /api/v2/cleanup/start**
   - Swagger文档中存在但未实现的端点

17. **POST /api/v2/cleanup/stop**
   - Swagger文档中存在但未实现的端点

18. **GET /api/v2/daily-contents/plan/{planId}**
   - Swagger文档中存在但未实现的端点

19. **GET /api/v2/daily-contents/{id}**
   - Swagger文档中存在但未实现的端点

20. **PUT /api/v2/daily-contents/{id}**
   - Swagger文档中存在但未实现的端点

21. **DELETE /api/v2/daily-contents/{id}/soft-delete**
   - Swagger文档中存在但未实现的端点

22. **POST /api/v2/daily-contents/{id}/restore**
   - Swagger文档中存在但未实现的端点

23. **GET /api/v2/daily-contents/plan/{planId}/deleted**
   - Swagger文档中存在但未实现的端点

24. **GET /health**
   - Swagger文档中存在但未实现的端点

25. **GET /health/liveness**
   - Swagger文档中存在但未实现的端点

26. **GET /health/readiness**
   - Swagger文档中存在但未实现的端点

27. **GET /health/database**
   - Swagger文档中存在但未实现的端点

28. **GET /health/redis**
   - Swagger文档中存在但未实现的端点

29. **GET /health/system**
   - Swagger文档中存在但未实现的端点

30. **GET /health/full**
   - Swagger文档中存在但未实现的端点

31. **GET /api/v2/learning-plans**
   - Swagger文档中存在但未实现的端点

32. **POST /api/v2/learning-plans**
   - Swagger文档中存在但未实现的端点

33. **GET /api/v2/learning-plans/{id}**
   - Swagger文档中存在但未实现的端点

34. **PUT /api/v2/learning-plans/{id}**
   - Swagger文档中存在但未实现的端点

35. **DELETE /api/v2/learning-plans/{id}/soft-delete**
   - Swagger文档中存在但未实现的端点

36. **POST /api/v2/learning-plans/{id}/restore**
   - Swagger文档中存在但未实现的端点

37. **GET /api/v2/learning-plans/deleted**
   - Swagger文档中存在但未实现的端点

38. **GET /api/v2/statistics/monitor/metrics**
   - Swagger文档中存在但未实现的端点

39. **GET /api/v2/statistics/monitor/endpoints**
   - Swagger文档中存在但未实现的端点

40. **GET /api/v2/statistics/monitor/endpoints/{endpoint}**
   - Swagger文档中存在但未实现的端点

41. **GET /api/v2/statistics/monitor/slow-requests**
   - Swagger文档中存在但未实现的端点

42. **POST /api/v2/statistics/monitor/reset**
   - Swagger文档中存在但未实现的端点

43. **GET /api/v1/statistics/learning**
   - Swagger文档中存在但未实现的端点

44. **GET /api/v1/statistics/daily**
   - Swagger文档中存在但未实现的端点

45. **POST /api/v1/statistics/activities**
   - Swagger文档中存在但未实现的端点

46. **GET /api/v1/statistics/activities**
   - Swagger文档中存在但未实现的端点

47. **GET /api/v1/statistics/overview**
   - Swagger文档中存在但未实现的端点

48. **GET /api/v1/statistics/trend**
   - Swagger文档中存在但未实现的端点

49. **GET /api/v2/themes**
   - Swagger文档中存在但未实现的端点

50. **GET /api/v2/themes/{id}**
   - Swagger文档中存在但未实现的端点

51. **DELETE /api/v2/themes/{id}/soft-delete**
   - Swagger文档中存在但未实现的端点

52. **POST /api/v2/themes/{id}/restore**
   - Swagger文档中存在但未实现的端点

53. **GET /api/v2/themes/deleted**
   - Swagger文档中存在但未实现的端点

54. **DELETE /api/v2/exercises/{id}/soft-delete**
   - Swagger文档中存在但未实现的端点

55. **POST /api/v2/exercises/{id}/restore**
   - Swagger文档中存在但未实现的端点

56. **GET /api/v2/tags/{tagId}/exercises/deleted**
   - Swagger文档中存在但未实现的端点

57. **DELETE /api/v2/insights/{id}/soft-delete**
   - Swagger文档中存在但未实现的端点

58. **POST /api/v2/insights/{id}/restore**
   - Swagger文档中存在但未实现的端点

59. **GET /api/v2/tags/{tagId}/insights/deleted**
   - Swagger文档中存在但未实现的端点

60. **DELETE /api/v2/notes/{id}/soft-delete**
   - Swagger文档中存在但未实现的端点

61. **POST /api/v2/notes/{id}/restore**
   - Swagger文档中存在但未实现的端点

62. **GET /api/v2/notes/deleted**
   - Swagger文档中存在但未实现的端点

63. **GET /api/v1/square/notes**
   - Swagger文档中存在但未实现的端点

64. **GET /api/v1/square/tags**
   - Swagger文档中存在但未实现的端点

65. **GET /api/v1/square/recommended-notes**
   - Swagger文档中存在但未实现的端点

66. **DELETE /api/v2/tags/{id}/soft-delete**
   - Swagger文档中存在但未实现的端点

67. **POST /api/v2/tags/{id}/restore**
   - Swagger文档中存在但未实现的端点

68. **GET /api/v2/tags/deleted**
   - Swagger文档中存在但未实现的端点

## 💡 改进建议

1. 🔴 **documentation**: 有 30 个API端点缺少文档，建议添加Swagger注释
2. 🟡 **consistency**: 有 65 个API端点文档不一致，需要同步更新
3. 🔴 **coverage**: API文档覆盖率仅为 68%，建议提升至80%以上
