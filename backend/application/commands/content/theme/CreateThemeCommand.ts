/**
 * 创建主题命令
 */
export interface CreateThemeCommand {
  /**
   * 名称
   */
  name: string;
  
  /**
   * 英文名称
   */
  englishName?: string;
  
  /**
   * 描述
   */
  description?: string;
  
  /**
   * 图标
   */
  icon?: string;
  
  /**
   * 颜色
   */
  color?: string;
  
  /**
   * 封面图片URL
   */
  coverImageUrl?: string;
  
  /**
   * 排序顺序
   */
  sortOrder?: number;
  
  /**
   * 是否激活
   */
  isActive?: boolean;
  
  /**
   * 父主题ID
   */
  parentId?: number;
}
