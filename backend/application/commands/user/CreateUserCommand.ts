/**
 * CreateUserCommand
 * 创建用户命令
 */
export class CreateUserCommand {
  /**
   * 构造函数
   * @param username 用户名
   * @param nickname 昵称
   * @param email 邮箱
   * @param emailVerified 邮箱是否已验证
   * @param phoneNumber 手机号
   * @param phoneVerified 手机号是否已验证
   * @param password 密码
   * @param wechatOpenId 微信OpenID
   * @param avatar 头像
   * @param gender 性别
   * @param birthday 生日
   * @param status 状态
   * @param roleIds 角色ID列表
   * @param privacySettings 隐私设置
   */
  constructor(
    readonly username: string,
    readonly nickname?: string,
    readonly email?: string,
    readonly emailVerified?: boolean,
    readonly phoneNumber?: string,
    readonly phoneVerified?: boolean,
    readonly password?: string,
    readonly wechatOpenId?: string,
    readonly avatar?: string,
    readonly gender?: string,
    readonly birthday?: Date,
    readonly status?: string,
    readonly roleIds?: number[],
    readonly privacySettings?: any
  ) {}
}
