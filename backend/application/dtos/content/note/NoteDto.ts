import { ContentStatus } from '../../../../domain/models/content/ContentStatus';
import { Visibility } from '../../../../domain/models/content/Visibility';

/**
 * NoteDto
 * 笔记数据传输对象
 */
export interface NoteDto {
  /**
   * 笔记ID
   */
  id: number;

  /**
   * 笔记标题
   */
  title: string;

  /**
   * 笔记内容
   */
  content: string;

  /**
   * 用户ID
   */
  userId: string;

  /**
   * 配图URL
   */
  imageUrl: string | null;

  /**
   * 笔记状态
   */
  status: ContentStatus;

  /**
   * 笔记可见性
   */
  visibility: Visibility;

  /**
   * 点赞数
   */
  likeCount: number;

  /**
   * 评论数
   */
  commentCount: number;

  /**
   * 查看次数
   */
  viewCount: number;

  /**
   * 是否AI生成
   */
  isAiGenerated: boolean;

  /**
   * 关联的学习计划ID
   */
  planId: number | null;

  /**
   * 标签列表
   */
  tags: string[];

  /**
   * 创建时间
   */
  createdAt: Date;

  /**
   * 更新时间
   */
  updatedAt: Date;

  /**
   * 删除时间
   */
  deletedAt: Date | null;

  /**
   * 是否已删除
   */
  isDeleted: boolean;

  /**
   * 是否已发布
   */
  isPublished: boolean;
}
