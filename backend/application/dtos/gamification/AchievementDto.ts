/**
 * 成就DTO
 * 用于在应用层和表示层之间传输成就数据
 */
export interface AchievementDto {
  /**
   * 成就ID
   */
  id: number;

  /**
   * 成就名称
   */
  name: string;

  /**
   * 成就描述
   */
  description: string;

  /**
   * 成就图标
   */
  icon: string | null;

  /**
   * 成就类别
   */
  category: string;

  /**
   * 成就类别显示名称
   */
  categoryDisplayName: string;

  /**
   * 成就难度
   */
  difficulty: string;

  /**
   * 成就难度显示名称
   */
  difficultyDisplayName: string;

  /**
   * 获得点数
   */
  points: number;

  /**
   * 触发条件
   */
  criteria: {
    /**
     * 条件类型
     */
    type: string;

    /**
     * 条件值
     */
    value: any;

    /**
     * 条件描述
     */
    description: string;
  };

  /**
   * 是否隐藏成就
   */
  isHidden: boolean;

  /**
   * 是否激活
   */
  isActive: boolean;

  /**
   * 关联的徽章ID
   */
  badgeId: number | null;

  /**
   * 创建时间
   */
  createdAt: Date;

  /**
   * 更新时间
   */
  updatedAt: Date;

  /**
   * 删除时间
   */
  deletedAt: Date | null;

  /**
   * 是否已删除
   */
  isDeleted: boolean;
}
