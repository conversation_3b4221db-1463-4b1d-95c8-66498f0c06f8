/**
 * 用户成就摘要DTO
 * 包含用户的等级、经验值、成就和徽章等信息
 */

import { AchievementDto } from '../gamification/AchievementDto';
import { BadgeDto } from '../gamification/BadgeDto';
import { LevelDto } from '../gamification/LevelDto';

export interface UserAchievementSummaryDto {
  /**
   * 用户当前等级
   */
  level: LevelDto | null;

  /**
   * 用户当前经验值
   */
  currentExp: number;

  /**
   * 下一级所需经验值
   */
  nextLevelExp: number | null;

  /**
   * 用户已获得的成就列表
   */
  achievements: AchievementDto[];

  /**
   * 用户已获得的徽章列表
   */
  badges: BadgeDto[];

  /**
   * 连续学习天数
   */
  streakDays: number;

  /**
   * 已获得成就总数
   */
  totalAchievements: number;

  /**
   * 已获得徽章总数
   */
  totalBadges: number;
}
