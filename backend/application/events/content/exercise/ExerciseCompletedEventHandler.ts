import { EventHandlerBase } from '../../../../infrastructure/events/EventHandlerBase';
import { ExerciseCompletedEvent } from '../../../../domain/events/content/exercise/ExerciseCompletedEvent';
import { WebSocketService } from '../../../../infrastructure/services/notification/WebSocketService';
import { Logger } from '../../../../infrastructure/logging/Logger';
import { DeadLetterQueueService } from '../../../../infrastructure/events/DeadLetterQueueServiceInterface';

/**
 * ExerciseCompletedEventHandler类
 * 处理练习完成事件
 */
export class ExerciseCompletedEventHandler extends EventHandlerBase<ExerciseCompletedEvent> {
  /**
   * 构造函数
   * @param webSocketService WebSocket服务
   * @param logger 日志记录器
   * @param deadLetterQueueService 死信队列服务（可选）
   */
  constructor(
    webSocketService: WebSocketService,
    logger: Logger,
    deadLetterQueueService?: DeadLetterQueueService
  ) {
    super(webSocketService, logger, deadLetterQueueService);
  }
  /**
   * 处理事件的具体逻辑
   * @param event 练习完成事件
   */
  protected async processEvent(event: ExerciseCompletedEvent): Promise<void> {
    console.log(`处理练习完成事件: 用户 ${event.userId} 完成了练习 ${event.aggregateId}`);

    // 这里可以添加其他业务逻辑，如更新统计数据、触发成就等
  }

  /**
   * 发送WebSocket通知
   * @param event 练习完成事件
   */
  protected async sendNotification(event: ExerciseCompletedEvent): Promise<void> {
    // 向用户发送通知
    await this.sendUserNotification(event.userId.toString(), {
      type: 'exerciseCompleted',
      exerciseId: event.aggregateId,
      title: event.title,
      timestamp: new Date().toISOString(),
      points: event.points || 0,
      message: `恭喜你完成了练习: ${event.title}`
    });

    // 如果有特殊成就或里程碑，可以发送广播
    if (event.isSpecialAchievement) {
      await this.sendBroadcastNotification({
        type: 'specialAchievement',
        userId: event.userId,
        exerciseId: event.aggregateId,
        title: event.title,
        timestamp: new Date().toISOString(),
        message: `用户完成了特殊练习: ${event.title}`
      });
    }
  }
}
