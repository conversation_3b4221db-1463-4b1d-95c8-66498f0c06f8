import { EventHandlerBase } from '../../../../infrastructure/events/EventHandlerBase';
import { ExerciseCreatedEvent } from '../../../../domain/events/content/exercise/ExerciseCreatedEvent';
import { WebSocketService } from '../../../../infrastructure/services/notification/WebSocketService';
import { Logger } from '../../../../infrastructure/logging/Logger';
import { DeadLetterQueueService } from '../../../../infrastructure/events/DeadLetterQueueServiceInterface';

/**
 * ExerciseCreatedEventHandler类
 * 处理练习创建事件
 */
export class ExerciseCreatedEventHandler extends EventHandlerBase<ExerciseCreatedEvent> {
  /**
   * 构造函数
   * @param webSocketService WebSocket服务
   * @param logger 日志记录器
   * @param deadLetterQueueService 死信队列服务（可选）
   */
  constructor(
    webSocketService: WebSocketService,
    logger: Logger,
    deadLetterQueueService?: DeadLetterQueueService
  ) {
    super(webSocketService, logger, deadLetterQueueService);
  }
  /**
   * 处理事件的具体逻辑
   * @param event 练习创建事件
   */
  protected async processEvent(event: ExerciseCreatedEvent): Promise<void> {
    this.logger.info(`处理练习创建事件: 练习 ${event.aggregateId} - ${event.title} 已创建`);

    // 这里可以添加其他业务逻辑，如更新统计数据、创建相关资源等
  }

  /**
   * 发送WebSocket通知
   * @param event 练习创建事件
   */
  protected async sendNotification(event: ExerciseCreatedEvent): Promise<void> {
    // 向创建者发送通知
    if (event.creatorId) {
      await this.sendUserNotification(event.creatorId.toString(), {
        type: 'exerciseCreated',
        exerciseId: event.aggregateId,
        title: event.title,
        timestamp: new Date().toISOString(),
        message: `你的练习 "${event.title}" 已成功创建`
      });
    }

    // 如果是公开练习，发送广播通知
    if (event.isPublic) {
      await this.sendBroadcastNotification({
        type: 'newPublicExercise',
        exerciseId: event.aggregateId,
        title: event.title,
        creatorId: event.creatorId,
        timestamp: new Date().toISOString(),
        message: `新的公开练习已发布: ${event.title}`
      });
    }
  }
}
