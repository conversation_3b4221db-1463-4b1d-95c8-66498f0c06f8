import { EventHandlerBase } from '../../../../infrastructure/events/EventHandlerBase';
import { LearningPlanCreatedEvent } from '../../../../domain/events/content/learningPlan/LearningPlanCreatedEvent';
import { WebSocketService } from '../../../../infrastructure/services/notification/WebSocketService';
import { Logger } from '../../../../infrastructure/logging/Logger';
import { DeadLetterQueueService } from '../../../../infrastructure/events/DeadLetterQueueServiceInterface';

/**
 * LearningPlanCreatedEventHandler类
 * 处理学习计划创建事件
 */
export class LearningPlanCreatedEventHandler extends EventHandlerBase<LearningPlanCreatedEvent> {
  /**
   * 构造函数
   * @param webSocketService WebSocket服务
   * @param logger 日志记录器
   * @param deadLetterQueueService 死信队列服务（可选）
   */
  constructor(
    webSocketService: WebSocketService,
    logger: Logger,
    deadLetterQueueService?: DeadLetterQueueService
  ) {
    super(webSocketService, logger, deadLetterQueueService);
  }
  /**
   * 处理事件的具体逻辑
   * @param event 学习计划创建事件
   */
  protected async processEvent(event: LearningPlanCreatedEvent): Promise<void> {
    this.logger.info(`处理学习计划创建事件: 学习计划 ${event.aggregateId} - ${event.title} 已创建`);

    // 这里可以添加其他业务逻辑，如更新统计数据、创建相关资源等
  }

  /**
   * 发送WebSocket通知
   * @param event 学习计划创建事件
   */
  protected async sendNotification(event: LearningPlanCreatedEvent): Promise<void> {
    // 向创建者发送通知
    if (event.userId) {
      await this.sendUserNotification(event.userId.toString(), {
        type: 'learningPlanCreated',
        learningPlanId: event.aggregateId,
        title: event.title,
        timestamp: new Date().toISOString(),
        message: `你的学习计划 "${event.title}" 已成功创建`
      });
    }

    // 如果是公开学习计划，发送广播通知
    if (event.isPublic) {
      await this.sendBroadcastNotification({
        type: 'newPublicLearningPlan',
        learningPlanId: event.aggregateId,
        title: event.title,
        userId: event.userId,
        timestamp: new Date().toISOString(),
        message: `新的公开学习计划已发布: ${event.title}`
      });
    }
  }
}
