import { EventHandlerBase } from '../../../../infrastructure/events/EventHandlerBase';
import { NoteCreatedEvent } from '../../../../domain/events/content/note/NoteCreatedEvent';
import { WebSocketService } from '../../../../infrastructure/services/notification/WebSocketService';
import { Logger } from '../../../../infrastructure/logging/Logger';
import { DeadLetterQueueService } from '../../../../infrastructure/events/DeadLetterQueueServiceInterface';

/**
 * NoteCreatedEventHandler类
 * 处理笔记创建事件
 */
export class NoteCreatedEventHandler extends EventHandlerBase<NoteCreatedEvent> {
  /**
   * 构造函数
   * @param webSocketService WebSocket服务
   * @param logger 日志记录器
   * @param deadLetterQueueService 死信队列服务（可选）
   */
  constructor(
    webSocketService: WebSocketService,
    logger: Logger,
    deadLetterQueueService?: DeadLetterQueueService
  ) {
    super(webSocketService, logger, deadLetterQueueService);
  }
  /**
   * 处理事件的具体逻辑
   * @param event 笔记创建事件
   */
  protected async processEvent(event: NoteCreatedEvent): Promise<void> {
    this.logger.info(`处理笔记创建事件: 笔记 ${event.aggregateId} - ${event.title} 已创建`);

    // 这里可以添加其他业务逻辑，如更新统计数据、创建相关资源等
  }

  /**
   * 发送WebSocket通知
   * @param event 笔记创建事件
   */
  protected async sendNotification(event: NoteCreatedEvent): Promise<void> {
    // 向创建者发送通知
    if (event.userId) {
      await this.sendUserNotification(event.userId.toString(), {
        type: 'noteCreated',
        noteId: event.aggregateId,
        title: event.title,
        timestamp: new Date().toISOString(),
        message: `你的笔记 "${event.title}" 已成功创建`
      });
    }

    // 笔记创建通常是私人行为，不发送广播通知
    // 只有在笔记发布时才发送广播通知，那是由NotePublishedEventHandler处理的
  }
}
