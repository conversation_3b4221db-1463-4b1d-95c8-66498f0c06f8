import { EventHandlerBase } from '../../../../infrastructure/events/EventHandlerBase';
import { NotePublishedEvent } from '../../../../domain/events/content/note/NotePublishedEvent';
import { WebSocketService } from '../../../../infrastructure/services/notification/WebSocketService';
import { Logger } from '../../../../infrastructure/logging/Logger';
import { DeadLetterQueueService } from '../../../../infrastructure/events/DeadLetterQueueServiceInterface';

/**
 * NotePublishedEventHandler类
 * 处理笔记发布事件
 */
export class NotePublishedEventHandler extends EventHandlerBase<NotePublishedEvent> {
  /**
   * 构造函数
   * @param webSocketService WebSocket服务
   * @param logger 日志记录器
   * @param deadLetterQueueService 死信队列服务（可选）
   */
  constructor(
    webSocketService: WebSocketService,
    logger: Logger,
    deadLetterQueueService?: DeadLetterQueueService
  ) {
    super(webSocketService, logger, deadLetterQueueService);
  }
  /**
   * 处理事件的具体逻辑
   * @param event 笔记发布事件
   */
  protected async processEvent(event: NotePublishedEvent): Promise<void> {
    console.log(`处理笔记发布事件: 用户 ${event.userId} 发布了笔记 ${event.aggregateId} - ${event.title}`);

    // 这里可以添加其他业务逻辑，如更新统计数据、触发推荐等
  }

  /**
   * 发送WebSocket通知
   * @param event 笔记发布事件
   */
  protected async sendNotification(event: NotePublishedEvent): Promise<void> {
    // 向用户发送通知
    await this.sendUserNotification(event.userId.toString(), {
      type: 'notePublished',
      noteId: event.aggregateId,
      title: event.title,
      timestamp: new Date().toISOString(),
      message: `你的笔记 "${event.title}" 已成功发布`
    });

    // 如果是公开笔记，发送广播通知
    if (event.payload.isPublic) {
      await this.sendBroadcastNotification({
        type: 'newPublicNote',
        userId: event.userId,
        noteId: event.aggregateId,
        title: event.title,
        timestamp: new Date().toISOString(),
        message: `新的公开笔记: ${event.title}`
      });
    }
  }
}
