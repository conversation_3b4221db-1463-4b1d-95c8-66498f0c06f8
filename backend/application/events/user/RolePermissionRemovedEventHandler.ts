import { EventHandlerBase } from '../../../infrastructure/events/EventHandlerBase';
import { RolePermissionRemovedEvent } from '../../../domain/events/user/RoleEvents';
import { CacheService } from '../../../services/cache.service';
import { UserRepository } from '../../../domain/repositories/user/UserRepository';
import { container } from '../../../infrastructure/di/container';

/**
 * RolePermissionRemovedEventHandler类
 * 处理角色权限移除事件
 * 当角色移除权限时，清除所有拥有该角色的用户的缓存，确保权限信息及时更新
 */
export class RolePermissionRemovedEventHandler extends EventHandlerBase<RolePermissionRemovedEvent> {
  private userRepository: UserRepository;

  constructor() {
    super();
    this.userRepository = container.get<UserRepository>('UserRepository');
  }

  /**
   * 处理事件的具体逻辑
   * @param event 角色权限移除事件
   */
  protected async processEvent(event: RolePermissionRemovedEvent): Promise<void> {
    try {
      this.logger.info(`处理角色权限移除事件: 角色 ${event.role.name}(${event.role.id}) 移除了权限 ${event.permission.name}(${event.permission.id})`);
      
      // 查找拥有该角色的所有用户
      const users = await this.userRepository.findByRole(event.role.id);
      
      if (users.length === 0) {
        this.logger.info(`没有用户拥有角色 ${event.role.name}(${event.role.id})`);
        return;
      }
      
      this.logger.info(`找到 ${users.length} 个拥有角色 ${event.role.name}(${event.role.id}) 的用户，开始清除缓存`);
      
      // 清除所有相关用户的缓存
      for (const user of users) {
        const result = await CacheService.clearUserCache(user.id);
        
        if (result) {
          this.logger.debug(`成功清除用户 ${user.id} 的缓存`);
        } else {
          this.logger.warn(`清除用户 ${user.id} 的缓存失败`);
        }
      }
      
      this.logger.info(`已完成所有用户缓存的清除`);
    } catch (error) {
      this.logger.error(`处理角色权限移除事件时发生错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送WebSocket通知
   * @param event 角色权限移除事件
   */
  protected async sendNotification(event: RolePermissionRemovedEvent): Promise<void> {
    // 这里可以实现通知逻辑，如通知管理员权限变更成功
    // 由于这是管理操作，通常不需要向普通用户发送通知
  }
}
