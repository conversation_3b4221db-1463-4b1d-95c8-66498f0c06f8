import { EventHandlerBase } from '../../../infrastructure/events/EventHandlerBase';
import { UserLeveledUpEvent } from '../../../domain/events/user/UserLeveledUpEvent';
import { WebSocketService } from '../../../infrastructure/services/notification/WebSocketService';
import { Logger } from '../../../infrastructure/logging/Logger';
import { DeadLetterQueueService } from '../../../infrastructure/events/DeadLetterQueueServiceInterface';

/**
 * UserLeveledUpEventHandler类
 * 处理用户升级事件
 */
export class UserLeveledUpEventHandler extends EventHandlerBase<UserLeveledUpEvent> {
  /**
   * 构造函数
   * @param webSocketService WebSocket服务
   * @param logger 日志记录器
   * @param deadLetterQueueService 死信队列服务（可选）
   */
  constructor(
    webSocketService: WebSocketService,
    logger: Logger,
    deadLetterQueueService?: DeadLetterQueueService
  ) {
    super(webSocketService, logger, deadLetterQueueService);
  }
  /**
   * 处理事件的具体逻辑
   * @param event 用户升级事件
   */
  protected async processEvent(event: UserLeveledUpEvent): Promise<void> {
    console.log(`处理用户升级事件: 用户 ${event.userId} 从 ${event.previousLevel} 升级到 ${event.newLevel}`);

    // 这里可以添加其他业务逻辑，如解锁新功能、发放奖励等
  }

  /**
   * 发送WebSocket通知
   * @param event 用户升级事件
   */
  protected async sendNotification(event: UserLeveledUpEvent): Promise<void> {
    // 向用户发送通知
    await this.sendUserNotification(event.userId.toString(), {
      type: 'levelUp',
      userId: event.userId,
      previousLevel: event.previousLevel,
      newLevel: event.newLevel,
      timestamp: new Date().toISOString(),
      rewards: event.rewards || [],
      message: `恭喜你升级到 ${event.newLevel} 级!`
    });

    // 发送广播通知
    if (event.newLevel >= 10) { // 只有达到一定级别才广播
      await this.sendBroadcastNotification({
        type: 'userLevelUp',
        userId: event.userId,
        newLevel: event.newLevel,
        timestamp: new Date().toISOString(),
        message: `有用户升级到 ${event.newLevel} 级!`
      });
    }
  }
}
