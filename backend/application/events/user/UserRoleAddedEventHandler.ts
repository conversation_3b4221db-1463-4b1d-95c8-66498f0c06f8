import { EventHandlerBase } from '../../../infrastructure/events/EventHandlerBase';
import { UserRoleAddedEvent } from '../../../domain/events/user/UserEvents';
import { CacheService } from '../../../services/cache.service';

/**
 * UserRoleAddedEventHandler类
 * 处理用户角色添加事件
 * 当用户被添加新角色时，清除该用户的缓存，确保权限信息及时更新
 */
export class UserRoleAddedEventHandler extends EventHandlerBase<UserRoleAddedEvent> {
  /**
   * 处理事件的具体逻辑
   * @param event 用户角色添加事件
   */
  protected async processEvent(event: UserRoleAddedEvent): Promise<void> {
    try {
      this.logger.info(`处理用户角色添加事件: 用户 ${event.user.id} 添加了角色 ${event.role.name}(${event.role.id})`);
      
      // 清除用户缓存，确保权限信息及时更新
      const result = await CacheService.clearUserCache(event.user.id);
      
      if (result) {
        this.logger.info(`成功清除用户 ${event.user.id} 的缓存`);
      } else {
        this.logger.warn(`清除用户 ${event.user.id} 的缓存失败`);
      }
    } catch (error) {
      this.logger.error(`处理用户角色添加事件时发生错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送WebSocket通知
   * @param event 用户角色添加事件
   */
  protected async sendNotification(event: UserRoleAddedEvent): Promise<void> {
    // 向用户发送通知
    await this.sendUserNotification(event.user.id.toString(), {
      type: 'roleAdded',
      userId: event.user.id,
      roleId: event.role.id,
      roleName: event.role.name,
      timestamp: new Date().toISOString(),
      message: `你获得了新角色: ${event.role.name}`
    });
  }
}
