import { Query } from '../../Query';

/**
 * SearchNotesQuery
 * 搜索笔记的查询
 */
export interface SearchNotesQuery extends Query {
  /**
   * 搜索关键字
   */
  keyword?: string;

  /**
   * 标签ID列表
   */
  tagIds?: number[];

  /**
   * 用户ID
   */
  userId?: string;

  /**
   * 学习计划ID
   */
  planId?: number;

  /**
   * 是否AI生成
   */
  isAiGenerated?: boolean;

  /**
   * 是否包含已删除的笔记
   */
  includeDeleted?: boolean;

  /**
   * 排序方式
   */
  sortBy?: 'relevance' | 'date' | 'popularity';

  /**
   * 页码
   */
  page?: number;

  /**
   * 每页数量
   */
  pageSize?: number;
}
