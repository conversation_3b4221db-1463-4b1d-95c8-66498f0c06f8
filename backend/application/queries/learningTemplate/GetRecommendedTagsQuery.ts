import { Query } from '../Query';

/**
 * GetRecommendedTagsQuery
 * 获取推荐标签的查询
 * 支持两种模式：
 * 1. 基于现有模板推荐标签：提供templateId参数
 * 2. 基于模板内容推荐标签：提供title、description和themeId参数
 */
export interface GetRecommendedTagsQuery extends Query {
  /**
   * 学习模板ID
   * 用于基于现有模板推荐标签
   */
  templateId?: number;

  /**
   * 主题ID
   * 用于指定模板所属的主题
   */
  themeId?: number;

  /**
   * 模板标题
   * 用于基于内容推荐标签
   */
  title?: string;

  /**
   * 模板描述
   * 用于基于内容推荐标签
   */
  description?: string;

  /**
   * 返回的最大数量
   * 默认为10
   */
  limit?: number;
}
