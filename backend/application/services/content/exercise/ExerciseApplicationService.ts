import { Exercise } from '../../../../domain/models/content/exercise/Exercise';
import { ExerciseRepository } from '../../../../domain/repositories/content/exercise/ExerciseRepository';
import { ContentRecommendationService } from '../../../../domain/services/content/ContentRecommendationService';
import { UnitOfWork } from '../../../../infrastructure/persistence/UnitOfWork';
import { CreateExerciseCommand } from '../../../commands/content/exercise/CreateExerciseCommand';
import { UpdateExerciseCommand } from '../../../commands/content/exercise/UpdateExerciseCommand';
import { DeleteExerciseCommand } from '../../../commands/content/exercise/DeleteExerciseCommand';
import { RestoreExerciseCommand } from '../../../commands/content/exercise/RestoreExerciseCommand';
import { PublishExerciseCommand } from '../../../commands/content/exercise/PublishExerciseCommand';
import { AddExerciseTagCommand } from '../../../commands/content/exercise/AddExerciseTagCommand';
import { RemoveExerciseTagCommand } from '../../../commands/content/exercise/RemoveExerciseTagCommand';
import { GetExerciseQuery } from '../../../queries/content/exercise/GetExerciseQuery';
import { SearchExercisesQuery } from '../../../queries/content/exercise/SearchExercisesQuery';
import { ExerciseDto } from '../../../dtos/content/exercise/ExerciseDto';

/**
 * ExerciseApplicationService
 * 练习应用服务，协调领域对象完成用例
 */
export class ExerciseApplicationService {
  /**
   * 构造函数
   * @param exerciseRepository 练习仓库
   * @param contentRecommendationService 内容推荐服务
   * @param unitOfWork 工作单元
   */
  constructor(
    private readonly exerciseRepository: ExerciseRepository,
    private readonly contentRecommendationService: ContentRecommendationService,
    private readonly unitOfWork: UnitOfWork
  ) {}

  /**
   * 创建练习
   * @param command 创建练习命令
   * @returns 创建的练习DTO
   */
  async createExercise(command: CreateExerciseCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = Exercise.create(
        command.title,
        command.description,
        command.expectedResult || null,
        command.difficulty,
        command.timeEstimateMinutes,
        command.creatorId,
        command.visibility,
        command.isOfficial
      );

      // 添加标签
      if (command.tags && command.tags.length > 0) {
        command.tags.forEach(tag => exercise.addTag(tag));
      }

      const savedExercise = await this.exerciseRepository.save(exercise);
      
      return this.toExerciseDto(savedExercise);
    });
  }

  /**
   * 更新练习
   * @param command 更新练习命令
   * @returns 更新后的练习DTO
   */
  async updateExercise(command: UpdateExerciseCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = await this.exerciseRepository.findById(command.exerciseId);
      if (!exercise) throw new Error(`练习ID ${command.exerciseId} 不存在`);

      if (command.title !== undefined && command.title !== exercise.title) {
        exercise.updateTitle(command.title);
      }

      if (command.description !== undefined && command.description !== exercise.description) {
        exercise.updateDescription(command.description);
      }

      if (command.expectedResult !== undefined && command.expectedResult !== exercise.expectedResult) {
        exercise.updateExpectedResult(command.expectedResult);
      }

      if (command.difficulty !== undefined && command.difficulty !== exercise.difficulty) {
        exercise.updateDifficulty(command.difficulty);
      }

      if (command.timeEstimateMinutes !== undefined && command.timeEstimateMinutes !== exercise.timeEstimateMinutes) {
        exercise.updateTimeEstimate(command.timeEstimateMinutes);
      }

      if (command.visibility !== undefined && command.visibility !== exercise.visibility) {
        exercise.updateVisibility(command.visibility);
      }

      // 更新标签
      if (command.tags !== undefined) {
        const currentTags = exercise.tags;
        const newTags = command.tags;
        
        // 移除不在新标签列表中的标签
        currentTags.forEach(tag => {
          if (!newTags.includes(tag)) {
            exercise.removeTag(tag);
          }
        });
        
        // 添加不在当前标签列表中的新标签
        newTags.forEach(tag => {
          if (!currentTags.includes(tag)) {
            exercise.addTag(tag);
          }
        });
      }

      const savedExercise = await this.exerciseRepository.save(exercise);
      
      return this.toExerciseDto(savedExercise);
    });
  }

  /**
   * 删除练习
   * @param command 删除练习命令
   */
  async deleteExercise(command: DeleteExerciseCommand): Promise<void> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = await this.exerciseRepository.findById(command.exerciseId);
      if (!exercise) throw new Error(`练习ID ${command.exerciseId} 不存在`);

      exercise.softDelete();
      await this.exerciseRepository.save(exercise);
    });
  }

  /**
   * 恢复练习
   * @param command 恢复练习命令
   * @returns 恢复后的练习DTO
   */
  async restoreExercise(command: RestoreExerciseCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = await this.exerciseRepository.findById(command.exerciseId);
      if (!exercise) throw new Error(`练习ID ${command.exerciseId} 不存在`);

      exercise.restore();
      const savedExercise = await this.exerciseRepository.save(exercise);
      
      return this.toExerciseDto(savedExercise);
    });
  }

  /**
   * 发布练习
   * @param command 发布练习命令
   * @returns 发布后的练习DTO
   */
  async publishExercise(command: PublishExerciseCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = await this.exerciseRepository.findById(command.exerciseId);
      if (!exercise) throw new Error(`练习ID ${command.exerciseId} 不存在`);

      exercise.publish();
      const savedExercise = await this.exerciseRepository.save(exercise);
      
      return this.toExerciseDto(savedExercise);
    });
  }

  /**
   * 添加练习标签
   * @param command 添加练习标签命令
   * @returns 更新后的练习DTO
   */
  async addExerciseTag(command: AddExerciseTagCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = await this.exerciseRepository.findById(command.exerciseId);
      if (!exercise) throw new Error(`练习ID ${command.exerciseId} 不存在`);

      exercise.addTag(command.tag);
      const savedExercise = await this.exerciseRepository.save(exercise);
      
      return this.toExerciseDto(savedExercise);
    });
  }

  /**
   * 移除练习标签
   * @param command 移除练习标签命令
   * @returns 更新后的练习DTO
   */
  async removeExerciseTag(command: RemoveExerciseTagCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = await this.exerciseRepository.findById(command.exerciseId);
      if (!exercise) throw new Error(`练习ID ${command.exerciseId} 不存在`);

      exercise.removeTag(command.tag);
      const savedExercise = await this.exerciseRepository.save(exercise);
      
      return this.toExerciseDto(savedExercise);
    });
  }

  /**
   * 获取练习
   * @param query 获取练习查询
   * @returns 练习DTO，如果不存在则返回null
   */
  async getExercise(query: GetExerciseQuery): Promise<ExerciseDto | null> {
    const exercise = await this.exerciseRepository.findById(query.exerciseId);
    return exercise ? this.toExerciseDto(exercise) : null;
  }

  /**
   * 搜索练习
   * @param query 搜索练习查询
   * @returns 练习DTO列表
   */
  async searchExercises(query: SearchExercisesQuery): Promise<ExerciseDto[]> {
    let exercises: Exercise[] = [];
    
    if (query.keyword) {
      exercises = await this.exerciseRepository.searchByKeyword(query.keyword);
    } else if (query.tagIds && query.tagIds.length > 0) {
      // 这里简化实现，实际可能需要更复杂的逻辑
      const tagId = query.tagIds[0];
      if (query.difficulty) {
        exercises = await this.exerciseRepository.findByTagIdAndDifficulty(tagId, query.difficulty);
      } else {
        exercises = await this.exerciseRepository.findByTagId(tagId);
      }
    } else if (query.creatorId) {
      exercises = await this.exerciseRepository.findByCreatorId(query.creatorId);
    } else {
      exercises = await this.exerciseRepository.findAll();
    }
    
    // 应用过滤条件
    exercises = exercises.filter(exercise => {
      // 过滤已删除的练习
      if (!query.includeDeleted && exercise.isDeleted) {
        return false;
      }
      
      // 按难度过滤
      if (query.difficulty !== undefined && exercise.difficulty !== query.difficulty) {
        return false;
      }
      
      // 按是否官方内容过滤
      if (query.isOfficial !== undefined && exercise.isOfficial !== query.isOfficial) {
        return false;
      }
      
      return true;
    });
    
    // 应用排序
    if (query.sortBy === 'date') {
      exercises = exercises.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    } else if (query.sortBy === 'difficulty') {
      exercises = exercises.sort((a, b) => a.difficulty.localeCompare(b.difficulty));
    }
    
    // 应用分页
    const page = query.page || 1;
    const pageSize = query.pageSize || 20;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    exercises = exercises.slice(start, end);
    
    return exercises.map(exercise => this.toExerciseDto(exercise));
  }

  /**
   * 获取相似练习
   * @param exerciseId 练习ID
   * @param limit 返回的最大数量
   * @returns 相似练习DTO列表
   */
  async getSimilarExercises(exerciseId: number, limit: number = 3): Promise<ExerciseDto[]> {
    const similarExercises = await this.contentRecommendationService.recommendSimilarExercises(exerciseId, limit);
    return similarExercises.map(exercise => this.toExerciseDto(exercise));
  }

  /**
   * 将练习实体转换为练习DTO
   * @param exercise 练习实体
   * @returns 练习DTO
   */
  private toExerciseDto(exercise: Exercise): ExerciseDto {
    return {
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expectedResult,
      difficulty: exercise.difficulty,
      timeEstimateMinutes: exercise.timeEstimateMinutes,
      creatorId: exercise.creatorId,
      status: exercise.status,
      visibility: exercise.visibility,
      isOfficial: exercise.isOfficial,
      tags: exercise.tags,
      createdAt: exercise.createdAt,
      updatedAt: exercise.updatedAt,
      deletedAt: exercise.deletedAt,
      isDeleted: exercise.isDeleted,
      isPublished: exercise.isPublished
    };
  }
}
