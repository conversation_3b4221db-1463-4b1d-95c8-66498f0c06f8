import { Theme } from '../../../../domain/models/content/theme/Theme';
import { ThemeRepository } from '../../../../domain/repositories/content/theme/ThemeRepository';
import { UnitOfWork } from '../../../../infrastructure/persistence/UnitOfWork';
import { CreateThemeCommand } from '../../../commands/content/theme/CreateThemeCommand';
import { UpdateThemeCommand } from '../../../commands/content/theme/UpdateThemeCommand';
import { DeleteThemeCommand } from '../../../commands/content/theme/DeleteThemeCommand';
import { RestoreThemeCommand } from '../../../commands/content/theme/RestoreThemeCommand';
import { UpdateThemeStatusCommand } from '../../../commands/content/theme/UpdateThemeStatusCommand';
import { UpdateThemeSortOrderCommand } from '../../../commands/content/theme/UpdateThemeSortOrderCommand';
import { GetThemeQuery } from '../../../queries/content/theme/GetThemeQuery';
import { SearchThemesQuery } from '../../../queries/content/theme/SearchThemesQuery';
import { ThemeDto } from '../../../dtos/content/theme/ThemeDto';

/**
 * ThemeApplicationService
 * 主题应用服务，协调领域对象完成用例
 */
export class ThemeApplicationService {
  /**
   * 构造函数
   * @param themeRepository 主题仓库
   * @param unitOfWork 工作单元
   */
  constructor(
    private readonly themeRepository: ThemeRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}

  /**
   * 创建主题
   * @param command 创建主题命令
   * @returns 创建的主题DTO
   */
  async createTheme(command: CreateThemeCommand): Promise<ThemeDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 检查名称是否已存在
      const existingTheme = await this.themeRepository.findByName(command.name);
      if (existingTheme) {
        throw new Error(`主题名称 "${command.name}" 已存在`);
      }

      // 检查英文名称是否已存在
      if (command.englishName) {
        const existingThemeByEnglishName = await this.themeRepository.findByEnglishName(command.englishName);
        if (existingThemeByEnglishName) {
          throw new Error(`主题英文名称 "${command.englishName}" 已存在`);
        }
      }

      const theme = Theme.create(
        command.name,
        command.englishName,
        command.description,
        command.icon,
        command.color,
        command.coverImageUrl,
        command.sortOrder,
        command.isActive,
        command.parentId
      );

      const savedTheme = await this.themeRepository.save(theme);
      
      return this.toThemeDto(savedTheme);
    });
  }

  /**
   * 更新主题
   * @param command 更新主题命令
   * @returns 更新后的主题DTO
   */
  async updateTheme(command: UpdateThemeCommand): Promise<ThemeDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const theme = await this.themeRepository.findById(command.themeId);
      if (!theme) throw new Error(`主题ID ${command.themeId} 不存在`);

      // 检查名称是否已存在
      if (command.name !== undefined && command.name !== theme.name) {
        const existingTheme = await this.themeRepository.findByName(command.name);
        if (existingTheme && existingTheme.id !== theme.id) {
          throw new Error(`主题名称 "${command.name}" 已存在`);
        }
        theme.updateName(command.name);
      }

      // 检查英文名称是否已存在
      if (command.englishName !== undefined && command.englishName !== theme.englishName) {
        if (command.englishName) {
          const existingTheme = await this.themeRepository.findByEnglishName(command.englishName);
          if (existingTheme && existingTheme.id !== theme.id) {
            throw new Error(`主题英文名称 "${command.englishName}" 已存在`);
          }
        }
        theme.updateEnglishName(command.englishName);
      }

      if (command.description !== undefined) {
        theme.updateDescription(command.description);
      }

      if (command.icon !== undefined) {
        theme.updateIcon(command.icon);
      }

      if (command.color !== undefined) {
        theme.updateColor(command.color);
      }

      if (command.coverImageUrl !== undefined) {
        theme.updateCoverImageUrl(command.coverImageUrl);
      }

      if (command.sortOrder !== undefined) {
        theme.updateSortOrder(command.sortOrder);
      }

      if (command.isActive !== undefined) {
        theme.updateIsActive(command.isActive);
      }

      const savedTheme = await this.themeRepository.save(theme);
      
      return this.toThemeDto(savedTheme);
    });
  }

  /**
   * 删除主题
   * @param command 删除主题命令
   */
  async deleteTheme(command: DeleteThemeCommand): Promise<void> {
    return this.unitOfWork.runInTransaction(async () => {
      const theme = await this.themeRepository.findById(command.themeId);
      if (!theme) throw new Error(`主题ID ${command.themeId} 不存在`);

      // 检查是否有子主题
      const childThemes = await this.themeRepository.findByParentId(command.themeId);
      if (childThemes.length > 0) {
        throw new Error('无法删除有子主题的主题，请先删除所有子主题');
      }

      theme.softDelete();
      await this.themeRepository.save(theme);
    });
  }

  /**
   * 恢复主题
   * @param command 恢复主题命令
   * @returns 恢复后的主题DTO
   */
  async restoreTheme(command: RestoreThemeCommand): Promise<ThemeDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const theme = await this.themeRepository.findById(command.themeId);
      if (!theme) throw new Error(`主题ID ${command.themeId} 不存在`);

      // 如果有父主题，检查父主题是否已删除
      if (theme.parentId) {
        const parentTheme = await this.themeRepository.findById(theme.parentId);
        if (parentTheme && parentTheme.isDeleted) {
          throw new Error('无法恢复主题，因为其父主题已被删除');
        }
      }

      theme.restore();
      const savedTheme = await this.themeRepository.save(theme);
      
      return this.toThemeDto(savedTheme);
    });
  }

  /**
   * 更新主题状态
   * @param command 更新主题状态命令
   * @returns 更新后的主题DTO
   */
  async updateThemeStatus(command: UpdateThemeStatusCommand): Promise<ThemeDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const theme = await this.themeRepository.findById(command.themeId);
      if (!theme) throw new Error(`主题ID ${command.themeId} 不存在`);

      theme.updateIsActive(command.isActive);
      const savedTheme = await this.themeRepository.save(theme);
      
      return this.toThemeDto(savedTheme);
    });
  }

  /**
   * 更新主题排序顺序
   * @param command 更新主题排序顺序命令
   * @returns 更新后的主题DTO
   */
  async updateThemeSortOrder(command: UpdateThemeSortOrderCommand): Promise<ThemeDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const theme = await this.themeRepository.findById(command.themeId);
      if (!theme) throw new Error(`主题ID ${command.themeId} 不存在`);

      theme.updateSortOrder(command.sortOrder);
      const savedTheme = await this.themeRepository.save(theme);
      
      return this.toThemeDto(savedTheme);
    });
  }

  /**
   * 获取主题
   * @param query 获取主题查询
   * @returns 主题DTO，如果不存在则返回null
   */
  async getTheme(query: GetThemeQuery): Promise<ThemeDto | null> {
    const theme = await this.themeRepository.findById(query.themeId);
    return theme ? this.toThemeDto(theme) : null;
  }

  /**
   * 搜索主题
   * @param query 搜索主题查询
   * @returns 主题DTO列表
   */
  async searchThemes(query: SearchThemesQuery): Promise<ThemeDto[]> {
    let themes: Theme[] = [];
    
    if (query.keyword) {
      themes = await this.themeRepository.searchByKeyword(query.keyword, query.limit);
    } else if (query.parentId !== undefined) {
      if (query.parentId === null) {
        themes = await this.themeRepository.findTopLevel();
      } else {
        themes = await this.themeRepository.findByParentId(query.parentId);
      }
    } else if (query.isActive !== undefined) {
      if (query.isActive) {
        themes = await this.themeRepository.findActive();
      } else {
        // 获取所有主题，然后过滤出非激活的
        const allThemes = await this.themeRepository.findAll();
        themes = allThemes.filter(theme => !theme.isActive);
      }
    } else {
      themes = await this.themeRepository.findAll();
    }
    
    // 应用过滤条件
    themes = themes.filter(theme => {
      // 过滤已删除的主题
      if (!query.includeDeleted && theme.isDeleted) {
        return false;
      }
      
      return true;
    });
    
    // 应用排序
    if (query.sortBy === 'name') {
      themes = themes.sort((a, b) => a.name.localeCompare(b.name));
    } else if (query.sortBy === 'sortOrder') {
      themes = themes.sort((a, b) => a.sortOrder - b.sortOrder);
    }
    
    // 应用分页
    if (query.limit) {
      themes = themes.slice(0, query.limit);
    }
    
    return themes.map(theme => this.toThemeDto(theme));
  }

  /**
   * 获取顶级主题
   * @returns 顶级主题DTO列表
   */
  async getTopLevelThemes(): Promise<ThemeDto[]> {
    const themes = await this.themeRepository.findTopLevel();
    return themes.map(theme => this.toThemeDto(theme));
  }

  /**
   * 获取激活的主题
   * @returns 激活的主题DTO列表
   */
  async getActiveThemes(): Promise<ThemeDto[]> {
    const themes = await this.themeRepository.findActive();
    return themes.map(theme => this.toThemeDto(theme));
  }

  /**
   * 获取已删除的主题
   * @returns 已删除的主题DTO列表
   */
  async getDeletedThemes(): Promise<ThemeDto[]> {
    const themes = await this.themeRepository.findDeleted();
    return themes.map(theme => this.toThemeDto(theme));
  }

  /**
   * 获取按排序顺序排序的主题
   * @returns 按排序顺序排序的主题DTO列表
   */
  async getThemesBySortOrder(): Promise<ThemeDto[]> {
    const themes = await this.themeRepository.findBySortOrder();
    return themes.map(theme => this.toThemeDto(theme));
  }

  /**
   * 将领域模型转换为DTO
   * @param theme 主题领域模型
   * @returns 主题DTO
   */
  private toThemeDto(theme: Theme): ThemeDto {
    return {
      id: theme.id,
      name: theme.name,
      englishName: theme.englishName,
      description: theme.description,
      icon: theme.icon,
      color: theme.color,
      coverImageUrl: theme.coverImageUrl,
      sortOrder: theme.sortOrder,
      isActive: theme.isActive,
      parentId: theme.parentId,
      createdAt: theme.createdAt,
      updatedAt: theme.updatedAt,
      deletedAt: theme.deletedAt,
      isDeleted: theme.isDeleted
    };
  }
}
