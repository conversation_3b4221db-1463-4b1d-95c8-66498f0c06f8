import { AchievementRepository } from '../../../domain/repositories/gamification/AchievementRepository';
import { Achievement } from '../../../domain/models/gamification/Achievement';
import { AchievementCategory } from '../../../domain/models/gamification/AchievementCategory';
import { AchievementDifficulty } from '../../../domain/models/gamification/AchievementDifficulty';
import { AchievementCriteria } from '../../../domain/models/gamification/AchievementCriteria';
import { UnitOfWork } from '../../../domain/UnitOfWork';
import { CreateAchievementCommand } from '../../commands/gamification/CreateAchievementCommand';
import { UpdateAchievementCommand } from '../../commands/gamification/UpdateAchievementCommand';
import { AchievementDto } from '../../dtos/gamification/AchievementDto';
import { AwardAchievementCommand } from '../../commands/gamification/AwardAchievementCommand';
import { GetAchievementQuery } from '../../queries/gamification/GetAchievementQuery';
import { ListAchievementsQuery } from '../../queries/gamification/ListAchievementsQuery';
import { GetUserAchievementsQuery } from '../../queries/gamification/GetUserAchievementsQuery';

/**
 * 成就应用服务
 * 协调领域对象完成成就相关用例
 */
export class AchievementApplicationService {
  /**
   * 构造函数
   * @param achievementRepository 成就仓库
   * @param unitOfWork 工作单元
   */
  constructor(
    private readonly achievementRepository: AchievementRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}

  /**
   * 创建成就
   * @param command 创建成就命令
   * @returns 成就DTO
   */
  async createAchievement(command: CreateAchievementCommand): Promise<AchievementDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 检查名称是否已存在
      const existingAchievement = await this.achievementRepository.findByName(command.name);
      if (existingAchievement) {
        throw new Error(`成就名称 "${command.name}" 已存在`);
      }

      // 创建成就实体
      const achievement = Achievement.create(
        command.name,
        command.description,
        command.icon,
        AchievementCategory.fromString(command.category),
        AchievementDifficulty.fromString(command.difficulty),
        command.points,
        AchievementCriteria.fromRaw(command.criteria.type, command.criteria.value),
        command.isHidden,
        command.isActive,
        command.badgeId
      );

      // 保存成就
      const savedAchievement = await this.achievementRepository.save(achievement);
      
      // 返回DTO
      return this.toAchievementDto(savedAchievement);
    });
  }

  /**
   * 更新成就
   * @param command 更新成就命令
   * @returns 成就DTO
   */
  async updateAchievement(command: UpdateAchievementCommand): Promise<AchievementDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找成就
      const achievement = await this.achievementRepository.findById(command.id);
      if (!achievement) {
        throw new Error(`成就ID ${command.id} 不存在`);
      }

      // 检查名称是否已被其他成就使用
      if (command.name !== achievement.name) {
        const existingAchievement = await this.achievementRepository.findByName(command.name);
        if (existingAchievement && existingAchievement.id !== command.id) {
          throw new Error(`成就名称 "${command.name}" 已被其他成就使用`);
        }
      }

      // 更新成就
      achievement.update(
        command.name,
        command.description,
        command.icon,
        AchievementCategory.fromString(command.category),
        AchievementDifficulty.fromString(command.difficulty),
        command.points,
        AchievementCriteria.fromRaw(command.criteria.type, command.criteria.value),
        command.isHidden,
        command.isActive,
        command.badgeId
      );

      // 保存成就
      const savedAchievement = await this.achievementRepository.save(achievement);
      
      // 返回DTO
      return this.toAchievementDto(savedAchievement);
    });
  }

  /**
   * 删除成就
   * @param id 成就ID
   * @returns 是否成功删除
   */
  async deleteAchievement(id: number): Promise<boolean> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找成就
      const achievement = await this.achievementRepository.findById(id);
      if (!achievement) {
        throw new Error(`成就ID ${id} 不存在`);
      }

      // 软删除成就
      achievement.softDelete();
      await this.achievementRepository.save(achievement);
      
      return true;
    });
  }

  /**
   * 恢复成就
   * @param id 成就ID
   * @returns 是否成功恢复
   */
  async restoreAchievement(id: number): Promise<boolean> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找成就
      const achievement = await this.achievementRepository.findById(id);
      if (!achievement) {
        throw new Error(`成就ID ${id} 不存在`);
      }

      // 恢复成就
      achievement.restore();
      await this.achievementRepository.save(achievement);
      
      return true;
    });
  }

  /**
   * 授予成就给用户
   * @param command 授予成就命令
   * @returns 是否成功授予
   */
  async awardAchievement(command: AwardAchievementCommand): Promise<boolean> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找成就
      const achievement = await this.achievementRepository.findById(command.achievementId);
      if (!achievement) {
        throw new Error(`成就ID ${command.achievementId} 不存在`);
      }

      // 检查用户是否已获得该成就
      const hasAchieved = await this.achievementRepository.hasUserAchieved(
        command.userId,
        command.achievementId
      );
      if (hasAchieved) {
        return false; // 用户已获得该成就，无需重复授予
      }

      // 授予成就
      achievement.awardToUser(command.userId);
      await this.achievementRepository.awardToUser(command.userId, command.achievementId);
      
      return true;
    });
  }

  /**
   * 获取成就
   * @param query 获取成就查询
   * @returns 成就DTO
   */
  async getAchievement(query: GetAchievementQuery): Promise<AchievementDto> {
    const achievement = await this.achievementRepository.findById(query.id);
    if (!achievement) {
      throw new Error(`成就ID ${query.id} 不存在`);
    }
    
    return this.toAchievementDto(achievement);
  }

  /**
   * 列出成就
   * @param query 列出成就查询
   * @returns 成就DTO数组
   */
  async listAchievements(query: ListAchievementsQuery): Promise<AchievementDto[]> {
    let achievements: Achievement[] = [];
    
    if (query.category) {
      achievements = await this.achievementRepository.findByCategory(
        AchievementCategory.fromString(query.category),
        query.includeDeleted
      );
    } else if (query.difficulty) {
      achievements = await this.achievementRepository.findByDifficulty(
        AchievementDifficulty.fromString(query.difficulty),
        query.includeDeleted
      );
    } else if (query.isActive !== undefined) {
      achievements = query.isActive
        ? await this.achievementRepository.findActive()
        : await this.achievementRepository.findAll(query.includeDeleted);
    } else if (query.isHidden !== undefined) {
      achievements = query.isHidden
        ? await this.achievementRepository.findHidden()
        : await this.achievementRepository.findAll(query.includeDeleted);
    } else {
      achievements = await this.achievementRepository.findAll(query.includeDeleted);
    }
    
    return achievements.map(achievement => this.toAchievementDto(achievement));
  }

  /**
   * 获取用户成就
   * @param query 获取用户成就查询
   * @returns 成就DTO数组
   */
  async getUserAchievements(query: GetUserAchievementsQuery): Promise<AchievementDto[]> {
    let achievements: Achievement[] = [];
    
    if (query.achieved) {
      achievements = await this.achievementRepository.findByUserId(query.userId);
    } else {
      achievements = await this.achievementRepository.findNotAchievedByUserId(
        query.userId,
        query.includeHidden
      );
    }
    
    return achievements.map(achievement => this.toAchievementDto(achievement));
  }

  /**
   * 将成就实体转换为DTO
   * @param achievement 成就实体
   * @returns 成就DTO
   */
  private toAchievementDto(achievement: Achievement): AchievementDto {
    return {
      id: achievement.id,
      name: achievement.name,
      description: achievement.description,
      icon: achievement.icon,
      category: achievement.category.value,
      categoryDisplayName: achievement.category.getDisplayName(),
      difficulty: achievement.difficulty.value,
      difficultyDisplayName: achievement.difficulty.getDisplayName(),
      points: achievement.points,
      criteria: {
        type: achievement.criteria.type,
        value: achievement.criteria.value,
        description: achievement.criteria.getDescription()
      },
      isHidden: achievement.isHidden,
      isActive: achievement.isActive,
      badgeId: achievement.badgeId,
      createdAt: achievement.createdAt,
      updatedAt: achievement.updatedAt,
      deletedAt: achievement.deletedAt,
      isDeleted: achievement.isDeleted
    };
  }
}
