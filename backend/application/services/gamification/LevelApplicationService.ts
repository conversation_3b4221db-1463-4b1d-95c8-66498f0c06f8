import { LevelRepository } from '../../../domain/repositories/gamification/LevelRepository';
import { Level } from '../../../domain/models/gamification/Level';
import { LevelReward } from '../../../domain/models/gamification/LevelReward';
import { UnitOfWork } from '../../../domain/UnitOfWork';
import { CreateLevelCommand } from '../../commands/gamification/CreateLevelCommand';
import { UpdateLevelCommand } from '../../commands/gamification/UpdateLevelCommand';
import { LevelDto } from '../../dtos/gamification/LevelDto';
import { AddUserExpCommand } from '../../commands/gamification/AddUserExpCommand';
import { GetLevelQuery } from '../../queries/gamification/GetLevelQuery';
import { ListLevelsQuery } from '../../queries/gamification/ListLevelsQuery';
import { GetUserLevelQuery } from '../../queries/gamification/GetUserLevelQuery';
import { UserLevelInfoDto } from '../../dtos/gamification/UserLevelInfoDto';

/**
 * 等级应用服务
 * 协调领域对象完成等级相关用例
 */
export class LevelApplicationService {
  /**
   * 构造函数
   * @param levelRepository 等级仓库
   * @param unitOfWork 工作单元
   */
  constructor(
    private readonly levelRepository: LevelRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}

  /**
   * 创建等级
   * @param command 创建等级命令
   * @returns 等级DTO
   */
  async createLevel(command: CreateLevelCommand): Promise<LevelDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 检查等级数值是否已存在
      const existingLevel = await this.levelRepository.findByLevelNumber(command.levelNumber);
      if (existingLevel) {
        throw new Error(`等级数值 ${command.levelNumber} 已存在`);
      }

      // 解析奖励
      const rewards = command.rewards.map(rewardData => {
        switch (rewardData.type) {
          case 'badge':
            return LevelReward.badge(rewardData.value, rewardData.description);
          case 'points':
            return LevelReward.points(rewardData.value);
          case 'feature_unlock':
            return LevelReward.featureUnlock(rewardData.value, rewardData.description);
          case 'theme_unlock':
            return LevelReward.themeUnlock(rewardData.value, rewardData.description);
          case 'template_unlock':
            return LevelReward.templateUnlock(rewardData.value, rewardData.description);
          default:
            return LevelReward.custom(rewardData.type, rewardData.value, rewardData.description);
        }
      });

      // 创建等级实体
      const level = Level.create(
        command.levelNumber,
        command.name,
        command.requiredExp,
        command.icon,
        rewards,
        command.description
      );

      // 保存等级
      const savedLevel = await this.levelRepository.save(level);
      
      // 返回DTO
      return this.toLevelDto(savedLevel);
    });
  }

  /**
   * 更新等级
   * @param command 更新等级命令
   * @returns 等级DTO
   */
  async updateLevel(command: UpdateLevelCommand): Promise<LevelDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找等级
      const level = await this.levelRepository.findById(command.id);
      if (!level) {
        throw new Error(`等级ID ${command.id} 不存在`);
      }

      // 检查等级数值是否已被其他等级使用
      if (command.levelNumber !== level.levelNumber) {
        const existingLevel = await this.levelRepository.findByLevelNumber(command.levelNumber);
        if (existingLevel && existingLevel.id !== command.id) {
          throw new Error(`等级数值 ${command.levelNumber} 已被其他等级使用`);
        }
      }

      // 解析奖励
      const rewards = command.rewards.map(rewardData => {
        switch (rewardData.type) {
          case 'badge':
            return LevelReward.badge(rewardData.value, rewardData.description);
          case 'points':
            return LevelReward.points(rewardData.value);
          case 'feature_unlock':
            return LevelReward.featureUnlock(rewardData.value, rewardData.description);
          case 'theme_unlock':
            return LevelReward.themeUnlock(rewardData.value, rewardData.description);
          case 'template_unlock':
            return LevelReward.templateUnlock(rewardData.value, rewardData.description);
          default:
            return LevelReward.custom(rewardData.type, rewardData.value, rewardData.description);
        }
      });

      // 更新等级
      level.update(
        command.name,
        command.requiredExp,
        command.icon,
        rewards,
        command.description
      );

      // 保存等级
      const savedLevel = await this.levelRepository.save(level);
      
      // 返回DTO
      return this.toLevelDto(savedLevel);
    });
  }

  /**
   * 删除等级
   * @param id 等级ID
   * @returns 是否成功删除
   */
  async deleteLevel(id: number): Promise<boolean> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找等级
      const level = await this.levelRepository.findById(id);
      if (!level) {
        throw new Error(`等级ID ${id} 不存在`);
      }

      // 软删除等级
      level.softDelete();
      await this.levelRepository.save(level);
      
      return true;
    });
  }

  /**
   * 恢复等级
   * @param id 等级ID
   * @returns 是否成功恢复
   */
  async restoreLevel(id: number): Promise<boolean> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找等级
      const level = await this.levelRepository.findById(id);
      if (!level) {
        throw new Error(`等级ID ${id} 不存在`);
      }

      // 恢复等级
      level.restore();
      await this.levelRepository.save(level);
      
      return true;
    });
  }

  /**
   * 增加用户经验值
   * @param command 增加用户经验值命令
   * @returns 用户等级信息DTO
   */
  async addUserExp(command: AddUserExpCommand): Promise<UserLevelInfoDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 增加用户经验值
      const newExp = await this.levelRepository.addUserExp(command.userId, command.exp);
      
      // 获取用户当前等级
      const currentLevel = await this.levelRepository.getUserLevel(command.userId);
      if (!currentLevel) {
        throw new Error('无法获取用户当前等级');
      }
      
      // 获取下一级等级
      const nextLevel = await this.levelRepository.findNextLevel(currentLevel.levelNumber);
      
      // 计算到下一级所需经验值
      let expToNextLevel = 0;
      let progress = 100;
      
      if (nextLevel) {
        expToNextLevel = nextLevel.requiredExp - currentLevel.requiredExp;
        const expInCurrentLevel = newExp - currentLevel.requiredExp;
        progress = Math.min(Math.floor((expInCurrentLevel / expToNextLevel) * 100), 100);
      }
      
      // 返回用户等级信息DTO
      return {
        userId: command.userId,
        currentExp: newExp,
        currentLevel: this.toLevelDto(currentLevel),
        nextLevel: nextLevel ? this.toLevelDto(nextLevel) : null,
        expToNextLevel,
        progress
      };
    });
  }

  /**
   * 获取等级
   * @param query 获取等级查询
   * @returns 等级DTO
   */
  async getLevel(query: GetLevelQuery): Promise<LevelDto> {
    let level: Level | null;
    
    if (query.id) {
      level = await this.levelRepository.findById(query.id);
    } else if (query.levelNumber) {
      level = await this.levelRepository.findByLevelNumber(query.levelNumber);
    } else {
      throw new Error('必须提供等级ID或等级数值');
    }
    
    if (!level) {
      throw new Error('等级不存在');
    }
    
    return this.toLevelDto(level);
  }

  /**
   * 列出等级
   * @param query 列出等级查询
   * @returns 等级DTO数组
   */
  async listLevels(query: ListLevelsQuery): Promise<LevelDto[]> {
    const levels = await this.levelRepository.findAll(query.includeDeleted);
    return levels.map(level => this.toLevelDto(level));
  }

  /**
   * 获取用户等级信息
   * @param query 获取用户等级查询
   * @returns 用户等级信息DTO
   */
  async getUserLevelInfo(query: GetUserLevelQuery): Promise<UserLevelInfoDto> {
    // 获取用户当前经验值
    const currentExp = await this.levelRepository.getUserExp(query.userId);
    
    // 获取用户当前等级
    const currentLevel = await this.levelRepository.getUserLevel(query.userId);
    if (!currentLevel) {
      throw new Error('无法获取用户当前等级');
    }
    
    // 获取下一级等级
    const nextLevel = await this.levelRepository.findNextLevel(currentLevel.levelNumber);
    
    // 计算到下一级所需经验值
    let expToNextLevel = 0;
    let progress = 100;
    
    if (nextLevel) {
      expToNextLevel = nextLevel.requiredExp - currentLevel.requiredExp;
      const expInCurrentLevel = currentExp - currentLevel.requiredExp;
      progress = Math.min(Math.floor((expInCurrentLevel / expToNextLevel) * 100), 100);
    }
    
    // 返回用户等级信息DTO
    return {
      userId: query.userId,
      currentExp,
      currentLevel: this.toLevelDto(currentLevel),
      nextLevel: nextLevel ? this.toLevelDto(nextLevel) : null,
      expToNextLevel,
      progress
    };
  }

  /**
   * 将等级实体转换为DTO
   * @param level 等级实体
   * @returns 等级DTO
   */
  private toLevelDto(level: Level): LevelDto {
    return {
      id: level.id,
      levelNumber: level.levelNumber,
      name: level.name,
      requiredExp: level.requiredExp,
      icon: level.icon,
      rewards: level.rewards.map(reward => ({
        type: reward.type,
        value: reward.value,
        description: reward.description
      })),
      description: level.description,
      createdAt: level.createdAt,
      updatedAt: level.updatedAt,
      deletedAt: level.deletedAt,
      isDeleted: level.isDeleted
    };
  }
}
