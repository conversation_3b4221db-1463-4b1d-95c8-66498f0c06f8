import { TagCategory } from '../../../domain/models/tag/TagCategory';
import { TagCategoryRepository } from '../../../domain/repositories/tag/TagCategoryRepository';
import { TagRepository } from '../../../domain/repositories/tag/TagRepository';
import { UnitOfWork } from '../../../infrastructure/persistence/UnitOfWork';
import { CreateTagCategoryCommand } from '../../commands/tag/CreateTagCategoryCommand';
import { UpdateTagCategoryCommand } from '../../commands/tag/UpdateTagCategoryCommand';
import { DeleteTagCategoryCommand } from '../../commands/tag/DeleteTagCategoryCommand';
import { RestoreTagCategoryCommand } from '../../commands/tag/RestoreTagCategoryCommand';
import { GetTagCategoryQuery } from '../../queries/tag/GetTagCategoryQuery';
import { SearchTagCategoriesQuery } from '../../queries/tag/SearchTagCategoriesQuery';
import { GetTagsByCategoryQuery } from '../../queries/tag/GetTagsByCategoryQuery';
import { GetCategoryWithChildrenQuery } from '../../queries/tag/GetCategoryWithChildrenQuery';
import { TagCategoryDto } from '../../dtos/tag/TagCategoryDto';
import { TagDto } from '../../dtos/tag/TagDto';

/**
 * TagCategoryApplicationService
 * 标签分类应用服务，协调领域对象完成用例
 */
export class TagCategoryApplicationService {
  /**
   * 构造函数
   * @param tagCategoryRepository 标签分类仓库
   * @param tagRepository 标签仓库
   * @param unitOfWork 工作单元
   */
  constructor(
    private readonly tagCategoryRepository: TagCategoryRepository,
    private readonly tagRepository: TagRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}

  /**
   * 创建标签分类
   * @param command 创建标签分类命令
   * @returns 创建的标签分类DTO
   */
  async createTagCategory(command: CreateTagCategoryCommand): Promise<TagCategoryDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 检查父分类是否存在
      if (command.parentId) {
        const parentCategory = await this.tagCategoryRepository.findById(command.parentId);
        if (!parentCategory) {
          throw new Error(`父分类ID ${command.parentId} 不存在`);
        }
      }

      // 检查名称是否已存在
      const existingCategory = await this.tagCategoryRepository.findByName(command.name);
      if (existingCategory) {
        throw new Error(`分类名称 "${command.name}" 已存在`);
      }

      const category = TagCategory.create(
        command.name,
        command.description || '',
        command.parentId
      );

      const savedCategory = await this.tagCategoryRepository.save(category);
      
      return this.toTagCategoryDto(savedCategory);
    });
  }

  /**
   * 更新标签分类
   * @param command 更新标签分类命令
   * @returns 更新后的标签分类DTO
   */
  async updateTagCategory(command: UpdateTagCategoryCommand): Promise<TagCategoryDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const category = await this.tagCategoryRepository.findById(command.categoryId);
      if (!category) {
        throw new Error(`分类ID ${command.categoryId} 不存在`);
      }

      // 检查父分类是否存在
      if (command.parentId !== undefined && command.parentId !== category.parentId) {
        if (command.parentId) {
          const parentCategory = await this.tagCategoryRepository.findById(command.parentId);
          if (!parentCategory) {
            throw new Error(`父分类ID ${command.parentId} 不存在`);
          }
        }
        category.updateParent(command.parentId);
      }

      // 检查名称是否已存在
      if (command.name !== undefined && command.name !== category.name) {
        const existingCategory = await this.tagCategoryRepository.findByName(command.name);
        if (existingCategory && existingCategory.id !== category.id) {
          throw new Error(`分类名称 "${command.name}" 已存在`);
        }
        category.updateName(command.name);
      }

      // 更新描述
      if (command.description !== undefined && command.description !== category.description) {
        category.updateDescription(command.description);
      }

      const savedCategory = await this.tagCategoryRepository.save(category);
      
      return this.toTagCategoryDto(savedCategory);
    });
  }

  /**
   * 删除标签分类
   * @param command 删除标签分类命令
   */
  async deleteTagCategory(command: DeleteTagCategoryCommand): Promise<void> {
    return this.unitOfWork.runInTransaction(async () => {
      const category = await this.tagCategoryRepository.findById(command.categoryId);
      if (!category) {
        throw new Error(`分类ID ${command.categoryId} 不存在`);
      }

      // 检查是否有子分类
      const children = await this.tagCategoryRepository.findByParentId(category.id);
      if (children.length > 0) {
        throw new Error(`分类 "${category.name}" 有子分类，无法删除`);
      }

      // 检查是否有标签
      const tags = await this.tagRepository.findByCategoryId(category.id);
      if (tags.length > 0) {
        throw new Error(`分类 "${category.name}" 有标签，无法删除`);
      }

      category.softDelete();
      await this.tagCategoryRepository.save(category);
    });
  }

  /**
   * 恢复标签分类
   * @param command 恢复标签分类命令
   */
  async restoreTagCategory(command: RestoreTagCategoryCommand): Promise<void> {
    return this.unitOfWork.runInTransaction(async () => {
      const category = await this.tagCategoryRepository.findById(command.categoryId);
      if (!category) {
        throw new Error(`分类ID ${command.categoryId} 不存在`);
      }

      // 检查父分类是否已删除
      if (category.parentId) {
        const parentCategory = await this.tagCategoryRepository.findById(category.parentId);
        if (parentCategory && parentCategory.isDeleted) {
          throw new Error(`父分类 "${parentCategory.name}" 已删除，无法恢复子分类`);
        }
      }

      category.restore();
      await this.tagCategoryRepository.save(category);
    });
  }

  /**
   * 获取标签分类
   * @param query 获取标签分类查询
   * @returns 标签分类DTO，如果不存在则返回null
   */
  async getTagCategory(query: GetTagCategoryQuery): Promise<TagCategoryDto | null> {
    const category = await this.tagCategoryRepository.findById(query.categoryId);
    return category ? this.toTagCategoryDto(category) : null;
  }

  /**
   * 搜索标签分类
   * @param query 搜索标签分类查询
   * @returns 标签分类DTO列表
   */
  async searchTagCategories(query: SearchTagCategoriesQuery): Promise<TagCategoryDto[]> {
    let categories: TagCategory[] = [];
    
    if (query.keyword) {
      // 实现关键字搜索
      categories = await this.tagCategoryRepository.findAll();
      categories = categories.filter(category => 
        category.name.includes(query.keyword!) || 
        category.description.includes(query.keyword!)
      );
    } else if (query.parentId !== undefined) {
      categories = await this.tagCategoryRepository.findByParentId(query.parentId);
    } else {
      categories = await this.tagCategoryRepository.findAll();
    }
    
    // 应用过滤条件
    if (query.includeDeleted === false) {
      categories = categories.filter(category => !category.isDeleted);
    }
    
    // 应用排序
    if (query.sortBy === 'level') {
      categories = categories.sort((a, b) => a.level - b.level);
    } else if (query.sortBy === 'name') {
      categories = categories.sort((a, b) => a.name.localeCompare(b.name));
    }
    
    // 应用分页
    if (query.limit) {
      categories = categories.slice(0, query.limit);
    }
    
    return categories.map(category => this.toTagCategoryDto(category));
  }

  /**
   * 获取分类下的标签
   * @param query 获取分类下的标签查询
   * @returns 标签DTO列表
   */
  async getTagsByCategory(query: GetTagsByCategoryQuery): Promise<TagDto[]> {
    const category = await this.tagCategoryRepository.findById(query.categoryId);
    if (!category) {
      throw new Error(`分类ID ${query.categoryId} 不存在`);
    }

    const tags = await this.tagRepository.findByCategoryId(query.categoryId);
    
    // 应用过滤条件
    const filteredTags = query.includeDeleted ? 
      tags : 
      tags.filter(tag => !tag.isDeleted);
    
    return filteredTags.map(tag => this.toTagDto(tag));
  }

  /**
   * 获取根分类
   * @returns 根分类DTO列表
   */
  async getRootCategories(): Promise<TagCategoryDto[]> {
    const categories = await this.tagCategoryRepository.findRootCategories();
    return categories.map(category => this.toTagCategoryDto(category));
  }

  /**
   * 获取分类及其子分类
   * @param query 获取分类及其子分类查询
   * @returns 分类及其子分类DTO
   */
  async getCategoryWithChildren(query: GetCategoryWithChildrenQuery): Promise<{ category: TagCategoryDto, children: TagCategoryDto[] } | null> {
    try {
      const result = await this.tagCategoryRepository.findWithChildren(query.categoryId);
      
      return {
        category: this.toTagCategoryDto(result.category),
        children: result.children.map(child => this.toTagCategoryDto(child))
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * 将标签分类实体转换为标签分类DTO
   * @param category 标签分类实体
   * @returns 标签分类DTO
   */
  private toTagCategoryDto(category: TagCategory): TagCategoryDto {
    return {
      id: category.id,
      name: category.name,
      description: category.description,
      parentId: category.parentId,
      level: category.level,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      deletedAt: category.deletedAt,
      isDeleted: category.isDeleted
    };
  }

  /**
   * 将标签实体转换为标签DTO
   * @param tag 标签实体
   * @returns 标签DTO
   */
  private toTagDto(tag: any): TagDto {
    return {
      id: tag.id,
      name: tag.name,
      categoryId: tag.categoryId,
      description: tag.description,
      popularity: tag.popularity,
      creatorId: tag.creatorId,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
      deletedAt: tag.deletedAt,
      isDeleted: tag.isDeleted,
      synonyms: tag.synonyms
    };
  }
}
