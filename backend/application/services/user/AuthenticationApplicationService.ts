import { UserRepository } from '../../../domain/repositories/user/UserRepository';
import { AuthTokenRepository } from '../../../domain/repositories/user/AuthTokenRepository';
import { UnitOfWork } from '../../../infrastructure/persistence/UnitOfWork';
import { LoginCommand } from '../../commands/user/LoginCommand';
import { LogoutCommand } from '../../commands/user/LogoutCommand';
import { RefreshTokenCommand } from '../../commands/user/RefreshTokenCommand';
import { VerifyEmailCommand } from '../../commands/user/VerifyEmailCommand';
import { VerifyPhoneCommand } from '../../commands/user/VerifyPhoneCommand';
import { AuthTokenDto } from '../../dtos/user/AuthTokenDto';
import { UserDto } from '../../dtos/user/UserDto';
import { AuthToken } from '../../../domain/models/user/AuthToken';
import { RefreshToken } from '../../../domain/models/user/RefreshToken';
import { TokenStatus } from '../../../domain/models/user/TokenStatus';
import { Email } from '../../../domain/models/user/Email';
import { PhoneNumber } from '../../../domain/models/user/PhoneNumber';
import { UserStatus } from '../../../domain/models/user/UserStatus';
import { Password } from '../../../domain/models/user/Password';

/**
 * AuthenticationApplicationService类
 * 认证应用服务，协调领域对象完成认证相关用例
 */
export class AuthenticationApplicationService {
  /**
   * 构造函数
   * @param userRepository 用户仓库
   * @param authTokenRepository 认证令牌仓库
   * @param unitOfWork 工作单元
   */
  constructor(
    private readonly userRepository: UserRepository,
    private readonly authTokenRepository: AuthTokenRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}

  /**
   * 登录
   * @param command 登录命令
   * @returns 认证令牌DTO和用户DTO
   */
  async login(command: LoginCommand): Promise<{ token: AuthTokenDto, user: UserDto }> {
    return this.unitOfWork.runInTransaction(async () => {
      // 根据登录类型查找用户
      let user = null;
      if (command.username) {
        user = await this.userRepository.findByUsername(command.username);
      } else if (command.email) {
        user = await this.userRepository.findByEmail(command.email);
      } else if (command.phoneNumber) {
        user = await this.userRepository.findByPhoneNumber(command.phoneNumber);
      } else if (command.wechatOpenId) {
        user = await this.userRepository.findByWechatOpenId(command.wechatOpenId);
      }

      // 检查用户是否存在
      if (!user) {
        throw new Error('用户不存在');
      }

      // 检查用户状态
      if (user.status.value !== UserStatus.ACTIVE.value) {
        throw new Error('用户状态不正常');
      }

      // 验证密码（如果是密码登录）
      if (command.password) {
        if (!user.password || !user.password.verify(command.password)) {
          throw new Error('密码不正确');
        }
      }

      // 更新最后登录时间
      user.updateLastLoginTime(new Date());
      await this.userRepository.save(user);

      // 创建认证令牌
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 令牌有效期24小时

      const refreshExpiresAt = new Date();
      refreshExpiresAt.setDate(refreshExpiresAt.getDate() + 30); // 刷新令牌有效期30天

      const authToken = AuthToken.create(
        user.id,
        expiresAt,
        command.deviceInfo || 'unknown'
      );

      const refreshToken = RefreshToken.create(
        authToken.id,
        refreshExpiresAt
      );

      authToken.setRefreshToken(refreshToken);

      // 保存认证令牌
      const savedAuthToken = await this.authTokenRepository.save(authToken);

      return {
        token: this.toAuthTokenDto(savedAuthToken),
        user: this.toUserDto(user)
      };
    });
  }

  /**
   * 登出
   * @param command 登出命令
   */
  async logout(command: LogoutCommand): Promise<void> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找认证令牌
      const authToken = await this.authTokenRepository.findByToken(command.token);
      if (!authToken) {
        throw new Error('令牌不存在');
      }

      // 吊销令牌
      authToken.revoke();
      await this.authTokenRepository.save(authToken);
    });
  }

  /**
   * 刷新令牌
   * @param command 刷新令牌命令
   * @returns 新的认证令牌DTO
   */
  async refreshToken(command: RefreshTokenCommand): Promise<AuthTokenDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找认证令牌
      const authToken = await this.authTokenRepository.findByRefreshToken(command.refreshToken);
      if (!authToken) {
        throw new Error('刷新令牌不存在');
      }

      // 检查刷新令牌状态
      if (!authToken.refreshToken || authToken.refreshToken.status.value !== TokenStatus.ACTIVE.value) {
        throw new Error('刷新令牌已失效');
      }

      // 检查刷新令牌是否过期
      if (authToken.refreshToken.isExpired()) {
        throw new Error('刷新令牌已过期');
      }

      // 吊销旧令牌
      authToken.revoke();
      await this.authTokenRepository.save(authToken);

      // 创建新令牌
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 令牌有效期24小时

      const refreshExpiresAt = new Date();
      refreshExpiresAt.setDate(refreshExpiresAt.getDate() + 30); // 刷新令牌有效期30天

      const newAuthToken = AuthToken.create(
        authToken.userId,
        expiresAt,
        authToken.deviceInfo
      );

      const newRefreshToken = RefreshToken.create(
        newAuthToken.id,
        refreshExpiresAt
      );

      newAuthToken.setRefreshToken(newRefreshToken);

      // 保存新令牌
      const savedAuthToken = await this.authTokenRepository.save(newAuthToken);

      return this.toAuthTokenDto(savedAuthToken);
    });
  }

  /**
   * 验证邮箱
   * @param command 验证邮箱命令
   * @returns 更新后的用户DTO
   */
  async verifyEmail(command: VerifyEmailCommand): Promise<UserDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 检查邮箱
      if (!user.email || user.email.value !== command.email) {
        throw new Error('邮箱不匹配');
      }

      // 验证邮箱
      const verifiedEmail = new Email(user.email.value, true);
      user.updateEmail(verifiedEmail);

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 验证手机号
   * @param command 验证手机号命令
   * @returns 更新后的用户DTO
   */
  async verifyPhone(command: VerifyPhoneCommand): Promise<UserDto> {
    return this.unitOfWork.runInTransaction(async () => {
      // 查找用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 检查手机号
      if (!user.phoneNumber || user.phoneNumber.value !== command.phoneNumber) {
        throw new Error('手机号不匹配');
      }

      // 验证手机号
      const verifiedPhoneNumber = new PhoneNumber(user.phoneNumber.value, true);
      user.updatePhoneNumber(verifiedPhoneNumber);

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 将认证令牌实体转换为认证令牌DTO
   * @param authToken 认证令牌实体
   * @returns 认证令牌DTO
   */
  private toAuthTokenDto(authToken: AuthToken): AuthTokenDto {
    return {
      id: authToken.id,
      userId: authToken.userId,
      token: authToken.token,
      status: authToken.status.value,
      expiresAt: authToken.expiresAt,
      deviceInfo: authToken.deviceInfo,
      refreshToken: authToken.refreshToken ? {
        id: authToken.refreshToken.id,
        token: authToken.refreshToken.token,
        status: authToken.refreshToken.status.value,
        expiresAt: authToken.refreshToken.expiresAt
      } : null,
      createdAt: authToken.createdAt,
      updatedAt: authToken.updatedAt
    };
  }

  /**
   * 将用户实体转换为用户DTO
   * @param user 用户实体
   * @returns 用户DTO
   */
  private toUserDto(user: User): UserDto {
    return {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      email: user.email ? user.email.value : null,
      emailVerified: user.email ? user.email.isVerified : false,
      phoneNumber: user.phoneNumber ? user.phoneNumber.value : null,
      phoneVerified: user.phoneNumber ? user.phoneNumber.isVerified : false,
      wechatOpenId: user.wechatOpenId,
      avatar: user.avatar,
      gender: user.gender.value,
      birthday: user.birthday,
      status: user.status.value,
      roles: user.roles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description
      })),
      userSetting: {
        id: user.userSetting.id,
        theme: user.userSetting.theme,
        fontSize: user.userSetting.fontSize,
        dailyGoalMinutes: user.userSetting.dailyGoalMinutes,
        reminderTime: user.userSetting.reminderTime,
        privacySettings: user.userSetting.privacySettings.toJSON(),
        notificationSettings: user.userSetting.notificationSettings.map(setting => ({
          id: setting.id,
          notificationType: setting.notificationType.value,
          notificationChannel: setting.notificationChannel.value,
          enabled: setting.enabled
        }))
      },
      registeredAt: user.registeredAt,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      deletedAt: user.deletedAt
    };
  }}
