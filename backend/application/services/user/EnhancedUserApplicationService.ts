/**
 * 增强版用户应用服务
 * 使用增强版仓库实现
 */
import { User } from '../../../domain/models/user/User';
import { UserRepository } from '../../../domain/repositories/user/UserRepository';
import { RoleRepository } from '../../../domain/repositories/user/RoleRepository';
import { UnitOfWork } from '../../../infrastructure/persistence/UnitOfWork';
import { EventPublisher } from '../../../infrastructure/events/EventPublisher';
import { Logger } from '../../../infrastructure/logging/Logger';
import { CreateUserCommand } from '../../commands/user/CreateUserCommand';
import { UpdateUserCommand } from '../../commands/user/UpdateUserCommand';
import { DeleteUserCommand } from '../../commands/user/DeleteUserCommand';
import { RestoreUserCommand } from '../../commands/user/RestoreUserCommand';
import { UpdateUserProfileCommand } from '../../commands/user/UpdateUserProfileCommand';
import { UpdateUserPasswordCommand } from '../../commands/user/UpdateUserPasswordCommand';
import { AddUserRoleCommand } from '../../commands/user/AddUserRoleCommand';
import { RemoveUserRoleCommand } from '../../commands/user/RemoveUserRoleCommand';
import { GetUserQuery } from '../../queries/user/GetUserQuery';
import { SearchUsersQuery } from '../../queries/user/SearchUsersQuery';
import { UserDto } from '../../dtos/user/UserDto';
import { Email } from '../../../domain/models/user/Email';
import { PhoneNumber } from '../../../domain/models/user/PhoneNumber';
import { Password } from '../../../domain/models/user/Password';
import { Gender } from '../../../domain/models/user/Gender';
import { UserStatus } from '../../../domain/models/user/UserStatus';
import { UserSetting } from '../../../domain/models/user/UserSetting';
import { PrivacySettings } from '../../../domain/models/user/PrivacySettings';

/**
 * EnhancedUserApplicationService类
 * 增强版用户应用服务，使用增强版仓库实现
 */
export class EnhancedUserApplicationService {
  /**
   * 构造函数
   * @param userRepository 用户仓库
   * @param roleRepository 角色仓库
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param logger 日志记录器
   */
  constructor(
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository,
    private readonly unitOfWork: UnitOfWork,
    private readonly eventPublisher: EventPublisher,
    private readonly logger: Logger
  ) {}

  /**
   * 创建用户
   * @param command 创建用户命令
   * @returns 创建的用户DTO
   */
  async createUser(command: CreateUserCommand): Promise<UserDto> {
    this.logger.debug(`创建用户: ${command.username}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 检查用户名是否已存在
      const existingUserByUsername = await this.userRepository.findByUsername(command.username);
      if (existingUserByUsername) {
        throw new Error(`用户名 ${command.username} 已存在`);
      }

      // 检查邮箱是否已存在
      if (command.email) {
        const existingUserByEmail = await this.userRepository.findByEmail(command.email);
        if (existingUserByEmail) {
          throw new Error(`邮箱 ${command.email} 已存在`);
        }
      }

      // 检查手机号是否已存在
      if (command.phoneNumber) {
        const existingUserByPhoneNumber = await this.userRepository.findByPhoneNumber(command.phoneNumber);
        if (existingUserByPhoneNumber) {
          throw new Error(`手机号 ${command.phoneNumber} 已存在`);
        }
      }

      // 检查微信OpenID是否已存在
      if (command.wechatOpenId) {
        const existingUserByWechatOpenId = await this.userRepository.findByWechatOpenId(command.wechatOpenId);
        if (existingUserByWechatOpenId) {
          throw new Error(`微信OpenID ${command.wechatOpenId} 已存在`);
        }
      }

      // 创建值对象
      const email = command.email ? new Email(command.email, command.emailVerified || false) : null;
      const phoneNumber = command.phoneNumber ? new PhoneNumber(command.phoneNumber, command.phoneVerified || false) : null;
      const password = command.password ? new Password(command.password) : null;
      const gender = new Gender(command.gender || 'unknown');
      const status = new UserStatus(command.status || 'active');

      // 获取角色
      const roles = [];
      if (command.roleIds && command.roleIds.length > 0) {
        for (const roleId of command.roleIds) {
          const role = await this.roleRepository.findById(roleId);
          if (!role) {
            throw new Error(`角色ID ${roleId} 不存在`);
          }
          roles.push(role);
        }
      } else {
        // 默认角色
        const defaultRole = await this.roleRepository.findByName('user');
        if (defaultRole) {
          roles.push(defaultRole);
        }
      }

      // 创建隐私设置
      const privacySettings = new PrivacySettings(
        command.privacySettings?.publicProfile || false,
        command.privacySettings?.publicLearningPlan || false,
        command.privacySettings?.publicNotes || false,
        command.privacySettings?.publicExercises || false
      );

      // 创建用户设置
      const userSetting = UserSetting.createDefault(0, 0);
      userSetting.updatePrivacySettings(privacySettings);

      // 创建用户
      const user = new User(
        0, // ID将由数据库生成
        command.username,
        command.nickname || command.username,
        email,
        phoneNumber,
        password,
        command.wechatOpenId || null,
        command.avatar || null,
        gender,
        command.birthday || null,
        status,
        roles,
        new Date(),
        null,
        userSetting
      );

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 更新用户
   * @param command 更新用户命令
   * @returns 更新后的用户DTO
   */
  async updateUser(command: UpdateUserCommand): Promise<UserDto> {
    this.logger.debug(`更新用户: ID=${command.id}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.id);
      if (!user) {
        throw new Error(`用户ID ${command.id} 不存在`);
      }

      // 更新用户名
      if (command.username && command.username !== user.username) {
        // 检查用户名是否已存在
        const existingUser = await this.userRepository.findByUsername(command.username);
        if (existingUser && existingUser.id !== user.id) {
          throw new Error(`用户名 ${command.username} 已存在`);
        }
        user.updateUsername(command.username);
      }

      // 更新昵称
      if (command.nickname && command.nickname !== user.nickname) {
        user.updateNickname(command.nickname);
      }

      // 更新邮箱
      if (command.email && (!user.email || command.email !== user.email.value)) {
        // 检查邮箱是否已存在
        const existingUser = await this.userRepository.findByEmail(command.email);
        if (existingUser && existingUser.id !== user.id) {
          throw new Error(`邮箱 ${command.email} 已存在`);
        }
        const email = new Email(command.email, command.emailVerified || false);
        user.updateEmail(email);
      }

      // 更新手机号
      if (command.phoneNumber && (!user.phoneNumber || command.phoneNumber !== user.phoneNumber.value)) {
        // 检查手机号是否已存在
        const existingUser = await this.userRepository.findByPhoneNumber(command.phoneNumber);
        if (existingUser && existingUser.id !== user.id) {
          throw new Error(`手机号 ${command.phoneNumber} 已存在`);
        }
        const phoneNumber = new PhoneNumber(command.phoneNumber, command.phoneVerified || false);
        user.updatePhoneNumber(phoneNumber);
      }

      // 更新密码
      if (command.password) {
        const password = new Password(command.password);
        user.updatePassword(password);
      }

      // 更新微信OpenID
      if (command.wechatOpenId && command.wechatOpenId !== user.wechatOpenId) {
        // 检查微信OpenID是否已存在
        const existingUser = await this.userRepository.findByWechatOpenId(command.wechatOpenId);
        if (existingUser && existingUser.id !== user.id) {
          throw new Error(`微信OpenID ${command.wechatOpenId} 已存在`);
        }
        user.updateWechatOpenId(command.wechatOpenId);
      }

      // 更新头像
      if (command.avatar !== undefined && command.avatar !== user.avatar) {
        user.updateAvatar(command.avatar);
      }

      // 更新性别
      if (command.gender && command.gender !== user.gender.value) {
        const gender = new Gender(command.gender);
        user.updateGender(gender);
      }

      // 更新生日
      if (command.birthday !== undefined && command.birthday !== user.birthday) {
        user.updateBirthday(command.birthday);
      }

      // 更新状态
      if (command.status && command.status !== user.status.value) {
        const status = new UserStatus(command.status);
        user.updateStatus(status);
      }

      // 更新角色
      if (command.roleIds) {
        // 获取新角色
        const newRoles = [];
        for (const roleId of command.roleIds) {
          const role = await this.roleRepository.findById(roleId);
          if (!role) {
            throw new Error(`角色ID ${roleId} 不存在`);
          }
          newRoles.push(role);
        }

        // 移除不在新角色列表中的角色
        for (const role of user.roles) {
          if (!command.roleIds.includes(role.id)) {
            user.removeRole(role.id);
          }
        }

        // 添加不在当前角色列表中的新角色
        for (const role of newRoles) {
          if (!user.roles.some(r => r.id === role.id)) {
            user.addRole(role);
          }
        }
      }

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 删除用户
   * @param command 删除用户命令
   */
  async deleteUser(command: DeleteUserCommand): Promise<void> {
    this.logger.debug(`删除用户: ID=${command.id}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.id);
      if (!user) {
        throw new Error(`用户ID ${command.id} 不存在`);
      }

      // 软删除用户
      user.softDelete();
      await this.userRepository.save(user);
    });
  }

  /**
   * 恢复用户
   * @param command 恢复用户命令
   * @returns 恢复后的用户DTO
   */
  async restoreUser(command: RestoreUserCommand): Promise<UserDto> {
    this.logger.debug(`恢复用户: ID=${command.id}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 恢复用户
      const user = await this.userRepository.restore(command.id);
      if (!user) {
        throw new Error(`用户ID ${command.id} 不存在或未被删除`);
      }

      return this.toUserDto(user);
    });
  }

  /**
   * 更新用户资料
   * @param command 更新用户资料命令
   * @returns 更新后的用户DTO
   */
  async updateUserProfile(command: UpdateUserProfileCommand): Promise<UserDto> {
    this.logger.debug(`更新用户资料: ID=${command.id}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.id);
      if (!user) {
        throw new Error(`用户ID ${command.id} 不存在`);
      }

      // 更新用户资料
      const gender = command.gender ? new Gender(command.gender) : user.gender;
      user.updateProfile(
        command.nickname || user.nickname,
        command.avatar !== undefined ? command.avatar : user.avatar,
        gender,
        command.birthday !== undefined ? command.birthday : user.birthday
      );

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 更新用户密码
   * @param command 更新用户密码命令
   * @returns 更新后的用户DTO
   */
  async updateUserPassword(command: UpdateUserPasswordCommand): Promise<UserDto> {
    this.logger.debug(`更新用户密码: ID=${command.id}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.id);
      if (!user) {
        throw new Error(`用户ID ${command.id} 不存在`);
      }

      // 验证旧密码
      if (!user.password || !user.password.verify(command.oldPassword)) {
        throw new Error('旧密码不正确');
      }

      // 更新密码
      const password = new Password(command.newPassword);
      user.updatePassword(password);

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 添加用户角色
   * @param command 添加用户角色命令
   * @returns 更新后的用户DTO
   */
  async addUserRole(command: AddUserRoleCommand): Promise<UserDto> {
    this.logger.debug(`添加用户角色: userId=${command.userId}, roleId=${command.roleId}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 获取角色
      const role = await this.roleRepository.findById(command.roleId);
      if (!role) {
        throw new Error(`角色ID ${command.roleId} 不存在`);
      }

      // 添加角色
      user.addRole(role);

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 移除用户角色
   * @param command 移除用户角色命令
   * @returns 更新后的用户DTO
   */
  async removeUserRole(command: RemoveUserRoleCommand): Promise<UserDto> {
    this.logger.debug(`移除用户角色: userId=${command.userId}, roleId=${command.roleId}`);
    
    return this.unitOfWork.runInTransaction(async () => {
      // 获取用户
      const user = await this.userRepository.findById(command.userId);
      if (!user) {
        throw new Error(`用户ID ${command.userId} 不存在`);
      }

      // 移除角色
      user.removeRole(command.roleId);

      // 保存用户
      const savedUser = await this.userRepository.save(user);

      return this.toUserDto(savedUser);
    });
  }

  /**
   * 获取用户
   * @param query 获取用户查询
   * @returns 用户DTO，如果不存在则返回null
   */
  async getUser(query: GetUserQuery): Promise<UserDto | null> {
    this.logger.debug(`获取用户: ${JSON.stringify(query)}`);
    
    let user: User | null = null;

    if (query.id) {
      user = await this.userRepository.findById(query.id);
    } else if (query.username) {
      user = await this.userRepository.findByUsername(query.username);
    } else if (query.email) {
      user = await this.userRepository.findByEmail(query.email);
    } else if (query.phoneNumber) {
      user = await this.userRepository.findByPhoneNumber(query.phoneNumber);
    } else if (query.wechatOpenId) {
      user = await this.userRepository.findByWechatOpenId(query.wechatOpenId);
    }

    return user ? this.toUserDto(user) : null;
  }

  /**
   * 搜索用户
   * @param query 搜索用户查询
   * @returns 用户DTO列表和总数
   */
  async searchUsers(query: SearchUsersQuery): Promise<{ items: UserDto[], total: number }> {
    this.logger.debug(`搜索用户: ${JSON.stringify(query)}`);
    
    // 分页查询
    if (query.page && query.pageSize) {
      const { items, total } = await this.userRepository.findWithPagination(query.page, query.pageSize);
      return {
        items: items.map(user => this.toUserDto(user)),
        total
      };
    }

    // 根据角色查询
    if (query.roleId) {
      const users = await this.userRepository.findByRole(query.roleId);
      return {
        items: users.map(user => this.toUserDto(user)),
        total: users.length
      };
    }

    // 查询已删除的用户
    if (query.deleted) {
      const users = await this.userRepository.findDeleted();
      return {
        items: users.map(user => this.toUserDto(user)),
        total: users.length
      };
    }

    // 查询所有用户
    const users = await this.userRepository.findAll();
    return {
      items: users.map(user => this.toUserDto(user)),
      total: users.length
    };
  }

  /**
   * 将用户实体转换为用户DTO
   * @param user 用户实体
   * @returns 用户DTO
   */
  private toUserDto(user: User): UserDto {
    return {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      email: user.email ? user.email.value : null,
      emailVerified: user.email ? user.email.isVerified : false,
      phoneNumber: user.phoneNumber ? user.phoneNumber.value : null,
      phoneVerified: user.phoneNumber ? user.phoneNumber.isVerified : false,
      wechatOpenId: user.wechatOpenId,
      avatar: user.avatar,
      gender: user.gender.value,
      birthday: user.birthday,
      status: user.status.value,
      roles: user.roles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description
      })),
      registeredAt: user.registeredAt,
      lastLoginAt: user.lastLoginAt,
      userSetting: {
        id: user.userSetting.id,
        theme: user.userSetting.theme,
        fontSize: user.userSetting.fontSize,
        dailyGoalMinutes: user.userSetting.dailyGoalMinutes,
        reminderTime: user.userSetting.reminderTime,
        privacySettings: {
          publicProfile: user.userSetting.privacySettings.publicProfile,
          publicLearningPlan: user.userSetting.privacySettings.publicLearningPlan,
          publicNotes: user.userSetting.privacySettings.publicNotes,
          publicExercises: user.userSetting.privacySettings.publicExercises
        },
        notificationSettings: user.userSetting.notificationSettings.map(notificationSetting => ({
          id: notificationSetting.id,
          notificationType: notificationSetting.notificationType.value,
          enabled: notificationSetting.enabled
        }))
      },
      isDeleted: user.isDeleted
    };
  }
}
