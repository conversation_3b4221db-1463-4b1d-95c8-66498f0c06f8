/**
 * 端到端测试设置
 * 在所有测试运行前执行
 */

// 设置环境变量
process.env.NODE_ENV = 'test';
process.env.E2E_TEST_MODE = 'true';

// 增加测试超时时间
jest.setTimeout(30000);

// 全局变量
global.E2E_TEST_API_URL = process.env.E2E_TEST_API_URL || 'http://localhost:9093/api/v2';

// 导入测试工具
const request = require('supertest');
const axios = require('axios');
const { URL } = require('url');

// 创建API客户端
const apiUrl = new URL(global.E2E_TEST_API_URL);
const baseURL = `${apiUrl.protocol}//${apiUrl.host}`;
const apiClient = axios.create({
  baseURL,
  validateStatus: () => true // 不抛出HTTP错误
});

// 全局辅助函数
global.getApiClient = () => apiClient;

// 全局测试数据
global.testData = {
  users: {
    admin: {
      email: '<EMAIL>',
      password: 'admin123'
    },
    user: {
      email: '<EMAIL>',
      password: 'user123'
    }
  }
};

// 全局辅助函数：获取认证令牌
global.getAuthToken = async (userType = 'user') => {
  const user = global.testData.users[userType];
  
  if (!user) {
    throw new Error(`未知的用户类型: ${userType}`);
  }
  
  const response = await apiClient.post('/api/v2/auth/login', {
    email: user.email,
    password: user.password
  });
  
  if (response.status !== 200 || !response.data.token) {
    throw new Error(`登录失败: ${response.status} ${JSON.stringify(response.data)}`);
  }
  
  return response.data.token;
};

// 全局辅助函数：创建认证请求
global.authRequest = async (userType = 'user') => {
  const token = await global.getAuthToken(userType);
  return request(baseURL).set('Authorization', `Bearer ${token}`);
};

// 在所有测试完成后清理资源
afterAll(async () => {
  // 关闭数据库连接等资源
  await new Promise(resolve => setTimeout(resolve, 500)); // 等待资源释放
});
