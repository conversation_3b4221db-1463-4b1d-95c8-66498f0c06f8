const { BubbleInteraction, BubbleContent, Tag, Exercise, Insight, Note, User, LearningActivity, LearningPlan, PlanTag } = require('../models');
const { sequelize } = require('../config/database');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const transactionManager = require('../utils/transaction-manager');
const {
  handleApiError,
  handleNotFoundError,
  handleBadRequestError
} = require('../utils/errorHandler');
const jsonQueryService = require('../services/json-query.service');

/**
 * 记录泡泡互动
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const recordInteraction = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId, interactionType, duration, positionX, positionY, deviceInfo, contextData } = req.body;

    // 使用事务管理器处理事务
    const result = await transactionManager.runInTransaction(async (transaction) => {
      // 验证标签是否存在
      const tag = await Tag.findByPk(tagId, { transaction });

      if (!tag) {
        throw new Error('标签不存在');
      }

      // 获取当前激活的学习计划
      const currentPlan = await LearningPlan.findOne({
        where: {
          user_id: userId,
          is_current: true
        },
        transaction
      });

      if (!currentPlan) {
        throw new Error('未找到当前激活的学习计划');
      }

      // 验证标签是否属于当前学习计划
      const planTag = await PlanTag.findOne({
        where: {
          plan_id: currentPlan.id,
          tag_id: tagId
        },
        transaction
      });

      if (!planTag) {
        throw new Error('该标签不属于当前学习计划');
      }

      // 创建互动记录
      const interaction = await BubbleInteraction.create({
        user_id: userId,
        tag_id: tagId,
        interaction_type: interactionType,
        duration: duration || 0,
        position_x: positionX,
        position_y: positionY,
        device_info: deviceInfo,
        context_data: contextData
      }, { transaction });

      // 增加标签使用次数
      await Tag.update(
        { usage_count: sequelize.literal('usage_count + 1') },
        {
          where: { id: tagId },
          transaction
        }
      );

      // 记录学习活动
      await LearningActivity.create({
        user_id: userId,
        plan_id: currentPlan.id, // 使用当前计划ID
        activity_type: 'bubble_interaction',
        content_type: 'tag',
        content_id: tagId,
        duration: duration || 0,
        details: {
          interactionType,
          positionX,
          positionY
        }
      }, { transaction });

      // 返回结果
      return {
        interactionId: interaction.id,
        tagId,
        interactionType,
        createdAt: interaction.created_at
      };
    }, { context: 'recordInteraction' });

    return apiResponse.success(res, result);
  } catch (error) {
    // 根据错误类型返回不同的响应
    if (error.message === '标签不存在') {
      return handleNotFoundError(res, error.message);
    } else if (error.message === '未找到当前激活的学习计划') {
      return handleNotFoundError(res, error.message);
    } else if (error.message === '该标签不属于当前学习计划') {
      return handleBadRequestError(res, error.message);
    } else {
      return handleApiError(error, res, 'recordInteraction');
    }
  }
};

/**
 * 获取泡泡内容
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getBubbleContent = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId } = req.query;

    // 验证标签是否存在
    const tag = await Tag.findByPk(tagId);

    if (!tag) {
      return handleNotFoundError(res, '标签不存在');
    }

    // 获取当前激活的学习计划
    const currentPlan = await LearningPlan.findOne({
      where: {
        user_id: userId,
        is_current: true
      }
    });

    if (!currentPlan) {
      return handleNotFoundError(res, '未找到当前激活的学习计划');
    }

    // 验证标签是否属于当前学习计划
    const planTag = await PlanTag.findOne({
      where: {
        plan_id: currentPlan.id,
        tag_id: tagId
      }
    });

    if (!planTag) {
      return handleBadRequestError(res, '该标签不属于当前学习计划');
    }

    // 查询泡泡内容
    const bubbleContent = await BubbleContent.findOne({
      where: {
        tag_id: tagId,
        is_active: true
      },
      order: [
        ['priority', 'DESC'],
        ['display_count', 'ASC'],
        ['created_at', 'DESC']
      ]
    });

    let content;
    let contentType;

    // 如果没有预设的泡泡内容，则随机获取一个内容
    if (!bubbleContent) {
      // 随机选择内容类型
      const contentTypes = ['exercise', 'insight', 'note'];
      contentType = contentTypes[Math.floor(Math.random() * contentTypes.length)];

      // 根据内容类型获取内容
      switch (contentType) {
        case 'exercise':
          content = await Exercise.findOne({
            where: { tag_id: tagId },
            order: sequelize.random()
          });
          break;
        case 'insight':
          content = await Insight.findOne({
            where: { tag_id: tagId },
            order: sequelize.random()
          });
          break;
        case 'note':
          content = await Note.findOne({
            where: {
              tag_id: tagId,
              status: 'published'
            },
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'nickname', 'avatar_url']
              }
            ],
            order: sequelize.random()
          });
          break;
      }
    } else {
      // 使用预设的泡泡内容
      contentType = bubbleContent.content_type;

      // 根据内容类型获取内容
      switch (contentType) {
        case 'exercise':
          content = await Exercise.findByPk(bubbleContent.content_id);
          break;
        case 'insight':
          content = await Insight.findByPk(bubbleContent.content_id);
          break;
        case 'note':
          content = await Note.findByPk(bubbleContent.content_id, {
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'nickname', 'avatar_url']
              }
            ]
          });
          break;
      }

      // 更新显示次数
      await bubbleContent.update({ display_count: bubbleContent.display_count + 1 });
    }

    // 如果没有找到内容
    if (!content) {
      return handleNotFoundError(res, `没有找到与标签相关的${contentType}内容`);
    }

    // 记录学习活动
    // 假设用户的活动计划ID已在认证中间件中获取并存放在 req.user.activePlanId
    const activePlanId = req.user.activePlanId;
    if (!activePlanId) {
       // 如果没有活动计划，可能需要记录一个特殊的 plan_id 或抛出错误
       // 这里暂时记录为 null，具体行为取决于业务逻辑
       logger.warn(`用户 ${userId} 在记录泡泡活动时没有活动的学习计划`);
    }

    await LearningActivity.create({
      user_id: userId,
      plan_id: activePlanId || null, // 使用当前用户的活动计划 ID
      activity_type: contentType === 'exercise' ? 'view_exercise' :
                    contentType === 'insight' ? 'view_insight' : 'view_note',
      content_type: contentType,
      content_id: content.id,
      details: {
        tagId,
        fromBubble: true
      }
    });

    // 格式化响应数据
    let formattedContent;

    switch (contentType) {
      case 'exercise':
        formattedContent = {
          id: content.id,
          title: content.title,
          description: content.description,
          difficulty: content.difficulty,
          timeEstimate: content.time_estimate,
          isCompleted: false // 这里需要查询用户是否完成了练习
        };
        break;
      case 'insight':
        formattedContent = {
          id: content.id,
          title: content.title,
          content: content.content,
          source: content.source,
          author: content.author
        };
        break;
      case 'note':
        formattedContent = {
          id: content.id,
          title: content.title,
          content: content.content,
          imageUrl: content.image_url,
          likes: content.likes,
          comments: content.comments,
          userId: content.user_id,
          userName: content.user ? content.user.nickname : null,
          userAvatar: content.user ? content.user.avatar_url : null,
          createdAt: content.created_at
        };
        break;
    }

    return apiResponse.success(res, {
      contentType,
      content: formattedContent
    });
  } catch (error) {
    return handleApiError(error, res, 'getBubbleContent');
  }
};

/**
 * 获取泡泡互动统计
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getInteractionStats = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId, startDate, endDate } = req.query;

    const whereClause = { user_id: userId };

    // 如果指定了标签ID
    if (tagId) {
      whereClause.tag_id = tagId;
    }

    // 如果指定了日期范围
    if (startDate || endDate) {
      whereClause.created_at = {};

      if (startDate) {
        whereClause.created_at[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.created_at[sequelize.Op.lte] = new Date(endDate);
      }
    }

    // 按互动类型统计
    const typeStats = await BubbleInteraction.findAll({
      attributes: [
        'interaction_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: whereClause,
      group: ['interaction_type']
    });

    // 按标签统计
    const tagStats = await BubbleInteraction.findAll({
      attributes: [
        'tag_id',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: whereClause,
      group: ['tag_id'],
      include: [
        {
          model: Tag,
          as: 'tag',
          attributes: ['name']
        }
      ],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
      limit: 10
    });

    // 按日期统计
    const dateStats = await BubbleInteraction.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: whereClause,
      group: [sequelize.fn('DATE', sequelize.col('created_at'))],
      order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']]
    });

    // 格式化响应数据
    const formattedTypeStats = {};
    typeStats.forEach(stat => {
      formattedTypeStats[stat.interaction_type] = parseInt(stat.getDataValue('count'));
    });

    const formattedTagStats = tagStats.map(stat => ({
      tagId: stat.tag_id,
      tagName: stat.tag ? stat.tag.name : null,
      count: parseInt(stat.getDataValue('count'))
    }));

    const formattedDateStats = dateStats.map(stat => ({
      date: stat.getDataValue('date'),
      count: parseInt(stat.getDataValue('count'))
    }));

    return apiResponse.success(res, {
      byType: formattedTypeStats,
      byTag: formattedTagStats,
      byDate: formattedDateStats,
      total: await BubbleInteraction.count({ where: whereClause })
    });
  } catch (error) {
    return handleApiError(error, res, 'getInteractionStats');
  }
};

/**
 * 创建或更新泡泡内容
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const createOrUpdateBubbleContent = async (req, res) => {
  try {
    const { tagId, contentType, contentId, priority, isActive } = req.body;

    // 使用事务管理器处理事务
    const result = await transactionManager.runInTransaction(async (transaction) => {
      // 验证标签是否存在
      const tag = await Tag.findByPk(tagId);

      if (!tag) {
        throw new Error('标签不存在');
      }

      // 验证内容是否存在
      let content;
      switch (contentType) {
        case 'exercise':
          content = await Exercise.findByPk(contentId);
          break;
        case 'insight':
          content = await Insight.findByPk(contentId);
          break;
        case 'note':
          content = await Note.findByPk(contentId);
          break;
        default:
          throw new Error('无效的内容类型');
      }

      if (!content) {
        throw new Error('内容不存在');
      }

      // 查找是否已存在相同的泡泡内容
      let bubbleContent = await BubbleContent.findOne({
        where: {
          tag_id: tagId,
          content_type: contentType,
          content_id: contentId
        }
      });

      if (bubbleContent) {
        // 更新现有泡泡内容
        await bubbleContent.update({
          priority: priority !== undefined ? priority : bubbleContent.priority,
          is_active: isActive !== undefined ? isActive : bubbleContent.is_active
        }, { transaction });
      } else {
        // 创建新的泡泡内容
        bubbleContent = await BubbleContent.create({
          tag_id: tagId,
          content_type: contentType,
          content_id: contentId,
          priority: priority || 0,
          display_count: 0,
          interaction_count: 0,
          is_active: isActive !== undefined ? isActive : true
        }, { transaction });
      }

      // 返回结果
      return {
        id: bubbleContent.id,
        tagId: bubbleContent.tag_id,
        contentType: bubbleContent.content_type,
        contentId: bubbleContent.content_id,
        priority: bubbleContent.priority,
        displayCount: bubbleContent.display_count,
        interactionCount: bubbleContent.interaction_count,
        isActive: bubbleContent.is_active,
        createdAt: bubbleContent.created_at,
        updatedAt: bubbleContent.updated_at
      };
    }, { context: 'createOrUpdateBubbleContent' });

    return apiResponse.success(res, result, result.id ? '泡泡内容已更新' : '泡泡内容已创建');
  } catch (error) {
    // 根据错误类型返回不同的响应
    if (error.message === '标签不存在' || error.message === '内容不存在') {
      return handleNotFoundError(res, error.message);
    } else if (error.message === '无效的内容类型') {
      return handleBadRequestError(res, error.message);
    } else {
      return handleApiError(error, res, 'createOrUpdateBubbleContent');
    }
  }
};

/**
 * 按设备信息查询互动记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getInteractionsByDeviceInfo = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { os, platform, model, appVersion, startDate, endDate, page = 1, pageSize = 20 } = req.query;

    // 构建查询条件
    const where = { user_id: userId };

    // 如果指定了日期范围
    if (startDate || endDate) {
      where.created_at = {};

      if (startDate) {
        where.created_at[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        where.created_at[sequelize.Op.lte] = new Date(endDate);
      }
    }

    // 使用JSON查询服务查询互动记录
    const interactions = await jsonQueryService.queryByDeviceInfo(BubbleInteraction, {
      os,
      platform,
      model,
      appVersion,
      where,
      include: [
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize)
    });

    // 获取总记录数
    const total = await BubbleInteraction.count({
      where: {
        ...where,
        ...(os ? { device_os: os } : {}),
        ...(platform ? { device_platform: platform } : {}),
        ...(model ? { device_model: model } : {}),
        ...(appVersion ? { app_version: appVersion } : {})
      }
    });

    // 格式化响应数据
    const formattedInteractions = interactions.map(interaction => ({
      id: interaction.id,
      userId: interaction.user_id,
      tagId: interaction.tag_id,
      tagName: interaction.tag ? interaction.tag.name : null,
      interactionType: interaction.interaction_type,
      duration: interaction.duration_ms,
      positionX: interaction.position_x,
      positionY: interaction.position_y,
      deviceInfo: interaction.device_info,
      contextData: interaction.context_data,
      createdAt: interaction.created_at
    }));

    return apiResponse.success(res, {
      interactions: formattedInteractions,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'getInteractionsByDeviceInfo');
  }
};

/**
 * 按上下文数据查询互动记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getInteractionsByContextData = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { view, sessionDuration, activeTags, startDate, endDate, page = 1, pageSize = 20 } = req.query;

    // 构建查询条件
    const where = { user_id: userId };

    // 如果指定了日期范围
    if (startDate || endDate) {
      where.created_at = {};

      if (startDate) {
        where.created_at[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        where.created_at[sequelize.Op.lte] = new Date(endDate);
      }
    }

    // 处理activeTags参数
    let parsedActiveTags;
    if (activeTags) {
      try {
        parsedActiveTags = JSON.parse(activeTags);
      } catch (error) {
        return handleBadRequestError(res, '无效的activeTags参数');
      }
    }

    // 使用JSON查询服务查询互动记录
    const interactions = await jsonQueryService.queryByContextData(BubbleInteraction, {
      view,
      sessionDuration: sessionDuration ? parseInt(sessionDuration) : undefined,
      activeTags: parsedActiveTags,
      where,
      include: [
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize)
    });

    // 获取总记录数
    const total = await BubbleInteraction.count({
      where: {
        ...where,
        ...(view ? { view_name: view } : {}),
        ...(sessionDuration ? { session_duration: parseInt(sessionDuration) } : {})
      }
    });

    // 格式化响应数据
    const formattedInteractions = interactions.map(interaction => ({
      id: interaction.id,
      userId: interaction.user_id,
      tagId: interaction.tag_id,
      tagName: interaction.tag ? interaction.tag.name : null,
      interactionType: interaction.interaction_type,
      duration: interaction.duration_ms,
      positionX: interaction.position_x,
      positionY: interaction.position_y,
      deviceInfo: interaction.device_info,
      contextData: interaction.context_data,
      createdAt: interaction.created_at
    }));

    return apiResponse.success(res, {
      interactions: formattedInteractions,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'getInteractionsByContextData');
  }
};

/**
 * 获取设备统计信息
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getDeviceStats = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { groupBy = 'device_platform', startDate, endDate } = req.query;

    // 构建查询条件
    const where = { user_id: userId };

    // 如果指定了日期范围
    if (startDate || endDate) {
      where.created_at = {};

      if (startDate) {
        where.created_at[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        where.created_at[sequelize.Op.lte] = new Date(endDate);
      }
    }

    // 使用JSON查询服务统计设备信息
    const stats = await jsonQueryService.countByDeviceInfo(BubbleInteraction, {
      groupBy,
      where,
      useCache: true,
      cacheTtl: 300
    });

    return apiResponse.success(res, {
      stats,
      total: stats.reduce((sum, stat) => sum + stat.count, 0)
    });
  } catch (error) {
    return handleApiError(error, res, 'getDeviceStats');
  }
};

module.exports = {
  recordInteraction,
  getBubbleContent,
  getInteractionStats,
  createOrUpdateBubbleContent,
  getInteractionsByDeviceInfo,
  getInteractionsByContextData,
  getDeviceStats
};
