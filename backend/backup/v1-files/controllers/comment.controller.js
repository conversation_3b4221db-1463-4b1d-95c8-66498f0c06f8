const { Note, NoteComment, User, sequelize } = require('../models');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const {
  handleApiError,
  handleNotFoundError,
  handleForbiddenError
} = require('../utils/errorHandler');

/**
 * 获取笔记评论列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getNoteComments = async (req, res) => {
  try {
    const { noteId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    // 验证笔记是否存在
    const note = await Note.findByPk(noteId);

    if (!note) {
      return handleNotFoundError(res, '笔记不存在');
    }

    // 计算分页
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询评论
    const { count, rows } = await NoteComment.findAndCountAll({
      where: { note_id: noteId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar_url']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit
    });

    // 格式化响应数据
    const comments = rows.map(comment => ({
      id: comment.id,
      content: comment.content,
      likes: comment.likes,
      createdAt: comment.created_at,
      user: comment.user ? {
        id: comment.user.id,
        nickname: comment.user.nickname,
        avatarUrl: comment.user.avatar_url
      } : null
    }));

    return apiResponse.success(res, {
      comments,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: limit,
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'getNoteComments');
  }
};

/**
 * 添加笔记评论
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const addNoteComment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const userId = req.user.userId;
    const { noteId } = req.params;
    const { content } = req.body;

    // 验证笔记是否存在
    const note = await Note.findByPk(noteId);

    if (!note) {
      await transaction.rollback();
      return handleNotFoundError(res, '笔记不存在');
    }

    // 创建评论
    const comment = await NoteComment.create({
      note_id: noteId,
      user_id: userId,
      content,
      likes: 0
    }, { transaction });

    // 更新笔记评论数
    await Note.update(
      { comments: sequelize.literal('comments + 1') },
      {
        where: { id: noteId },
        transaction
      }
    );

    await transaction.commit();

    // 查询用户信息
    const user = await User.findByPk(userId, {
      attributes: ['id', 'nickname', 'avatar_url']
    });

    return apiResponse.created(res, {
      id: comment.id,
      content: comment.content,
      likes: comment.likes,
      createdAt: comment.created_at,
      user: user ? {
        id: user.id,
        nickname: user.nickname,
        avatarUrl: user.avatar_url
      } : null
    });
  } catch (error) {
    await transaction.rollback();
    return handleApiError(error, res, 'addNoteComment');
  }
};

/**
 * 删除笔记评论
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteNoteComment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const userId = req.user.userId;
    const { commentId } = req.params;

    // 查询评论
    const comment = await NoteComment.findByPk(commentId);

    if (!comment) {
      await transaction.rollback();
      return handleNotFoundError(res, '评论不存在');
    }

    // 验证评论是否属于当前用户
    if (comment.user_id !== userId) {
      await transaction.rollback();
      return handleForbiddenError(res, '无权删除该评论');
    }

    // 获取笔记ID
    const noteId = comment.note_id;

    // 删除评论
    await comment.destroy({ transaction });

    // 更新笔记评论数
    await Note.update(
      { comments: sequelize.literal('GREATEST(comments - 1, 0)') }, // 确保不会小于0
      {
        where: { id: noteId },
        transaction
      }
    );

    await transaction.commit();

    return apiResponse.success(res, null, '评论已删除');
  } catch (error) {
    await transaction.rollback();
    return handleApiError(error, res, 'deleteNoteComment');
  }
};

/**
 * 获取用户的评论列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getUserComments = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { page = 1, pageSize = 10 } = req.query;

    // 计算分页
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询用户的评论
    const { count, rows } = await NoteComment.findAndCountAll({
      where: { user_id: userId },
      include: [
        {
          model: Note,
          as: 'note',
          attributes: ['id', 'title', 'content']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit
    });

    // 格式化响应数据
    const comments = rows.map(comment => ({
      id: comment.id,
      content: comment.content,
      likes: comment.likes,
      createdAt: comment.created_at,
      note: comment.note ? {
        id: comment.note.id,
        title: comment.note.title,
        content: comment.note.content.substring(0, 100) + (comment.note.content.length > 100 ? '...' : '') // 截取内容前100个字符
      } : null
    }));

    return apiResponse.success(res, {
      comments,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: limit,
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'getUserComments');
  }
};

module.exports = {
  getNoteComments,
  addNoteComment,
  deleteNoteComment,
  getUserComments
};
