/**
 * 示例控制器
 * 展示如何使用统一的错误处理工具
 */
const { 
  handleApiError, 
  handleValidationError, 
  handleDatabaseError,
  handleNotFoundError,
  handleUnauthorizedError
} = require('../utils/errorHandler');
const { LearningPlan, Tag, DailyContent, User } = require('../models');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');

/**
 * 获取学习计划示例
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getLearningPlanExample = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;
    
    // 查询学习计划
    const plan = await LearningPlan.findByPk(id);
    
    // 使用handleNotFoundError处理不存在的资源
    if (!plan) {
      return handleNotFoundError(res, '学习计划不存在');
    }
    
    // 使用handleUnauthorizedError处理未授权访问
    if (plan.user_id !== userId && !plan.is_system_default) {
      return handleUnauthorizedError(res, '无权访问此学习计划');
    }
    
    // 查询关联的标签
    const tags = await Tag.findAll({
      include: [
        {
          model: LearningPlan,
          as: 'associatedPlans',
          where: { id: plan.id },
          through: { attributes: [] }
        }
      ]
    });
    
    // 查询每日内容
    const dailyContents = await DailyContent.findAll({
      where: { plan_id: plan.id },
      order: [['day_number', 'ASC']]
    });
    
    // 格式化响应数据
    const result = {
      id: plan.id,
      title: plan.title,
      description: plan.description,
      targetDays: plan.target_days,
      progress: plan.progress,
      status: plan.status,
      startDate: plan.start_date,
      endDate: plan.end_date,
      isCurrent: plan.is_current,
      isSystemDefault: plan.is_system_default,
      createdAt: plan.created_at,
      tags: tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        relevanceScore: tag.relevance_score,
        weight: tag.weight,
        isVerified: tag.is_verified
      })),
      dailyContents: dailyContents.map(content => ({
        day: content.day_number,
        title: content.title,
        content: content.content,
        isCompleted: content.is_completed,
        completionDate: content.completion_date
      }))
    };
    
    return apiResponse.success(res, result);
  } catch (error) {
    // 使用handleApiError处理API错误
    return handleApiError(error, res, 'getLearningPlanExample');
  }
};

/**
 * 创建标签示例
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const createTagExample = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { planId, name, relevanceScore, weight, isVerified } = req.body;
    
    // 验证请求数据
    const errors = [];
    if (!planId) errors.push({ msg: '学习计划ID不能为空' });
    if (!name) errors.push({ msg: '标签名称不能为空' });
    if (name && name.length > 10) errors.push({ msg: '标签名称不能超过10个字符' });
    
    // 使用handleValidationError处理验证错误
    if (errors.length > 0) {
      await transaction.rollback();
      return handleValidationError(errors, res);
    }
    
    // 查询学习计划
    const plan = await LearningPlan.findByPk(planId, { transaction });
    
    if (!plan) {
      await transaction.rollback();
      return handleNotFoundError(res, '学习计划不存在');
    }
    
    // 创建标签
    const tag = await Tag.create({
      name,
      relevance_score: relevanceScore || 1.0,
      weight: weight || 1.0,
      is_verified: isVerified || false
    }, { transaction });
    
    // 创建标签关联
    await PlanTag.create({
      plan_id: planId,
      tag_id: tag.id,
      relevance_score: relevanceScore || 1.0,
      weight: weight || 1.0,
      is_primary: false,
      sort_order: 0
    }, { transaction });
    
    await transaction.commit();
    
    return apiResponse.created(res, {
      id: tag.id,
      name: tag.name,
      relevanceScore: tag.relevance_score,
      weight: tag.weight,
      isVerified: tag.is_verified
    });
  } catch (error) {
    await transaction.rollback();
    
    // 使用handleDatabaseError处理数据库错误
    if (error.name && error.name.startsWith('Sequelize')) {
      return handleDatabaseError(error, res, 'createTagExample');
    }
    
    // 使用handleApiError处理其他API错误
    return handleApiError(error, res, 'createTagExample');
  }
};

module.exports = {
  getLearningPlanExample,
  createTagExample
};
