const { Exercise, Tag, LearningPlan } = require('../models');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const {
  handleApiError,
  handleNotFoundError
} = require('../utils/errorHandler');

/**
 * 获取标签下的练习列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getExercisesByTagId = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId } = req.params;
    const { difficulty, page = 1, pageSize = 10 } = req.query;

    // 验证标签是否存在且属于当前用户
    const tag = await Tag.findOne({
      where: { id: tagId },
      include: [
        {
          model: LearningPlan,
          as: 'learningPlan',
          where: { user_id: userId },
          attributes: ['id', 'user_id']
        }
      ]
    });

    if (!tag) {
      return handleNotFoundError(res, '标签不存在或不属于当前用户');
    }

    // 构建查询条件
    const where = { tag_id: tagId };

    if (difficulty) {
      where.difficulty = difficulty;
    }

    // 计算分页
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询练习
    const { count, rows } = await Exercise.findAndCountAll({
      where,
      order: [['difficulty', 'ASC'], ['created_at', 'DESC']],
      offset,
      limit
    });

    // 格式化响应数据
    const exercises = rows.map(exercise => ({
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      createdAt: exercise.created_at
    }));

    return apiResponse.success(res, {
      exercises,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: limit,
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'getExercisesByTagId');
  }
};

/**
 * 获取练习详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getExerciseById = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 查询练习
    const exercise = await Exercise.findOne({
      where: { id },
      include: [
        {
          model: Tag,
          as: 'tag',
          include: [
            {
              model: LearningPlan,
              as: 'learningPlan',
              where: { user_id: userId },
              attributes: ['id', 'user_id', 'title']
            }
          ]
        }
      ]
    });

    if (!exercise) {
      return handleNotFoundError(res, '练习不存在或不属于当前用户');
    }

    return apiResponse.success(res, {
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      tagId: exercise.tag_id,
      tagName: exercise.tag ? exercise.tag.name : null,
      planId: exercise.tag && exercise.tag.learningPlan ? exercise.tag.learningPlan.id : null,
      planTitle: exercise.tag && exercise.tag.learningPlan ? exercise.tag.learningPlan.title : null,
      createdAt: exercise.created_at,
      updatedAt: exercise.updated_at
    });
  } catch (error) {
    return handleApiError(error, res, 'getExerciseById');
  }
};

/**
 * 创建练习
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const createExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId, title, description, expectedResult, difficulty, timeEstimate } = req.body;

    // 验证标签是否存在且属于当前用户
    const tag = await Tag.findOne({
      where: { id: tagId },
      include: [
        {
          model: LearningPlan,
          as: 'learningPlan',
          where: { user_id: userId },
          attributes: ['id', 'user_id']
        }
      ]
    });

    if (!tag) {
      return handleNotFoundError(res, '标签不存在或不属于当前用户');
    }

    // 创建练习
    const exercise = await Exercise.create({
      tag_id: tagId,
      title,
      description,
      expected_result: expectedResult,
      difficulty: difficulty || 'beginner',
      time_estimate: timeEstimate || 15
    });

    return apiResponse.created(res, {
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      tagId: exercise.tag_id,
      createdAt: exercise.created_at
    });
  } catch (error) {
    return handleApiError(error, res, 'createExercise');
  }
};

/**
 * 更新练习
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;
    const { title, description, expectedResult, difficulty, timeEstimate } = req.body;

    // 查询练习
    const exercise = await Exercise.findOne({
      where: { id },
      include: [
        {
          model: Tag,
          as: 'tag',
          include: [
            {
              model: LearningPlan,
              as: 'learningPlan',
              where: { user_id: userId },
              attributes: ['id', 'user_id']
            }
          ]
        }
      ]
    });

    if (!exercise) {
      return handleNotFoundError(res, '练习不存在或不属于当前用户');
    }

    // 更新练习
    await exercise.update({
      title: title || exercise.title,
      description: description || exercise.description,
      expected_result: expectedResult !== undefined ? expectedResult : exercise.expected_result,
      difficulty: difficulty || exercise.difficulty,
      time_estimate: timeEstimate !== undefined ? timeEstimate : exercise.time_estimate
    });

    return apiResponse.success(res, {
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      tagId: exercise.tag_id,
      updatedAt: exercise.updated_at
    }, '练习已更新');
  } catch (error) {
    return handleApiError(error, res, 'updateExercise');
  }
};

/**
 * 删除练习
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 查询练习
    const exercise = await Exercise.findOne({
      where: { id },
      include: [
        {
          model: Tag,
          as: 'tag',
          include: [
            {
              model: LearningPlan,
              as: 'learningPlan',
              where: { user_id: userId },
              attributes: ['id', 'user_id']
            }
          ]
        }
      ]
    });

    if (!exercise) {
      return handleNotFoundError(res, '练习不存在或不属于当前用户');
    }

    // 删除练习
    await exercise.destroy();

    return apiResponse.success(res, null, '练习已删除');
  } catch (error) {
    return handleApiError(error, res, 'deleteExercise');
  }
};

module.exports = {
  getExercisesByTagId,
  getExerciseById,
  createExercise,
  updateExercise,
  deleteExercise
};
