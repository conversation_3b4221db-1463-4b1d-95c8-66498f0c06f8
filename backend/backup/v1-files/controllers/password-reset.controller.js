/**
 * 密码重置控制器
 * 处理密码重置相关的HTTP请求
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const passwordResetService = require('../services/password-reset.service');
const { handleApiError } = require('../utils/errorHandler');
const rateLimit = require('express-rate-limit');

/**
 * 请求密码重置
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.body;
    const ipAddress = req.ip;
    const userAgent = req.headers['user-agent'];

    // 生成重置令牌
    const token = await passwordResetService.generateResetToken(email, ipAddress, userAgent);

    // 发送重置邮件
    await passwordResetService.sendResetEmail(email, token);

    return apiResponse.success(res, {
      message: '密码重置邮件已发送，请检查您的邮箱'
    });
  } catch (error) {
    // 即使用户不存在，也返回成功响应，以防止用户枚举
    if (error.code === 'USER_NOT_FOUND') {
      return apiResponse.success(res, {
        message: '密码重置邮件已发送，请检查您的邮箱'
      });
    }

    return handleApiError(error, res, 'requestPasswordReset');
  }
};

/**
 * 请求通过手机号重置密码
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const requestPasswordResetByPhone = async (req, res) => {
  try {
    const { phoneNumber } = req.body;
    const ipAddress = req.ip;
    const userAgent = req.headers['user-agent'];

    // 生成重置令牌
    const token = await passwordResetService.generateResetToken(phoneNumber, ipAddress, userAgent);

    // 发送重置短信
    await passwordResetService.sendResetSMS(phoneNumber, token);

    return apiResponse.success(res, {
      message: '密码重置验证码已发送，请检查您的手机'
    });
  } catch (error) {
    // 即使用户不存在，也返回成功响应，以防止用户枚举
    if (error.code === 'USER_NOT_FOUND') {
      return apiResponse.success(res, {
        message: '密码重置验证码已发送，请检查您的手机'
      });
    }

    return handleApiError(error, res, 'requestPasswordResetByPhone');
  }
};

/**
 * 验证密码重置令牌
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const verifyResetToken = async (req, res) => {
  try {
    const { token } = req.params;

    // 验证令牌
    const userInfo = await passwordResetService.verifyResetToken(token);

    return apiResponse.success(res, {
      valid: true,
      userInfo
    });
  } catch (error) {
    if (error.code === 'INVALID_TOKEN') {
      return apiResponse.badRequest(res, '无效或已过期的令牌');
    }

    return handleApiError(error, res, 'verifyResetToken');
  }
};

/**
 * 重置密码
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const resetPassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;

    // 重置密码
    await passwordResetService.resetPassword(token, newPassword);

    return apiResponse.success(res, {
      message: '密码重置成功，请使用新密码登录'
    });
  } catch (error) {
    if (error.code === 'INVALID_TOKEN') {
      return apiResponse.badRequest(res, '无效或已过期的令牌');
    }

    return handleApiError(error, res, 'resetPassword');
  }
};

// 创建密码重置请求的速率限制器
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 5, // 每个IP最多5次请求
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_REQUESTS',
      message: '请求过于频繁，请稍后再试'
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = {
  requestPasswordReset,
  requestPasswordResetByPhone,
  verifyResetToken,
  resetPassword,
  passwordResetLimiter
};
