const { Note, Tag, User, NoteLike, LearningPlan, LearningActivity, PlanTag, sequelize } = require('../models');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const {
  handleApiError,
  handleNotFoundError
} = require('../utils/errorHandler');

/**
 * 获取广场笔记列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getSquareNotes = async (req, res) => {
  try {
    // 用户可能未登录，所以从req.user中获取userId可能为空
    const userId = req.user ? req.user.userId : null;
    const { tagId, page = 1, pageSize = 10, sortBy = 'latest' } = req.query;

    // 计算分页参数
    const limit = parseInt(pageSize);
    const offset = (parseInt(page) - 1) * limit;

    // 构建查询条件
    const whereClause = {
      status: 'published'
    };

    // 如果指定了标签ID且不是"all"
    if (tagId && tagId !== 'all') {
      whereClause.tag_id = tagId;
    }

    // 构建排序条件
    let order;
    switch (sortBy) {
      case 'popular':
        order = [['likes', 'DESC'], ['comments', 'DESC'], ['created_at', 'DESC']];
        break;
      case 'comments':
        order = [['comments', 'DESC'], ['created_at', 'DESC']];
        break;
      case 'oldest':
        order = [['created_at', 'ASC']];
        break;
      case 'latest':
      default:
        order = [['created_at', 'DESC']];
        break;
    }

    // 查询笔记列表
    const { count, rows } = await Note.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar_url']
        },
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name']
        }
      ],
      order,
      limit,
      offset
    });

    // 查询用户点赞状态（如果用户已登录）
    let likedNoteIds = [];
    if (userId) {
      const noteIds = rows.map(note => note.id);
      const likedNotes = noteIds.length > 0 ? await NoteLike.findAll({
        where: {
          user_id: userId,
          note_id: noteIds
        },
        attributes: ['note_id']
      }) : [];

      likedNoteIds = likedNotes.map(like => like.note_id);

      // 暂时禁用学习活动记录功能，避免错误
      // 记录学习活动（仅对已登录用户）
      // if (rows.length > 0) {
      //   // 获取当前用户的活跃学习计划
      //   const activePlan = await LearningPlan.findOne({
      //     where: {
      //       user_id: userId,
      //       status: 'active'
      //     },
      //     order: [['updated_at', 'DESC']]
      //   });
      //
      //   // 记录浏览广场的活动
      //   await LearningActivity.create({
      //     user_id: userId,
      //     plan_id: activePlan ? activePlan.id : null,
      //     activity_type: 'view_insight', // 将浏览广场视为查看观点
      //     content_type: 'note',
      //     details: {
      //       tagId: tagId || 'all',
      //       page,
      //       noteCount: rows.length
      //     }
      //   });
      // }
    }

    // 格式化响应数据
    const notes = rows.map(note => ({
      id: note.id,
      tagId: note.tag_id,
      tagName: note.tag ? note.tag.name : null,
      userId: note.user_id,
      userName: note.user ? note.user.nickname : null,
      userAvatar: note.user ? note.user.avatar_url : null,
      title: note.title,
      content: note.content,
      imageUrl: note.image_url,
      likes: note.likes,
      comments: note.comments,
      isLiked: likedNoteIds.includes(note.id),
      isAiGenerated: note.is_ai_generated,
      createdAt: note.created_at
    }));

    return apiResponse.success(res, {
      notes,
      pagination: {
        total: count,
        page: parseInt(page),
        pageSize: limit,
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'getSquareNotes');
  }
};

/**
 * 获取广场标签列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getSquareTags = async (req, res) => {
  try {
    // 用户可能未登录，所以从req.user中获取userId可能为空
    const userId = req.user ? req.user.userId : null;

    // 获取系统默认学习计划或当前用户激活的学习计划
    let currentPlan;

    if (userId) {
      // 如果用户已登录，获取其当前激活的学习计划
      currentPlan = await LearningPlan.findOne({
        where: {
          user_id: userId,
          is_current: true
        }
      });
    }

    // 如果未找到用户的学习计划或用户未登录，获取系统默认学习计划
    if (!currentPlan) {
      currentPlan = await LearningPlan.findOne({
        where: {
          is_system_default: true
        }
      });

      if (!currentPlan) {
        return handleNotFoundError(res, '未找到系统默认学习计划');
      }
    }

    // 使用PlanTag关联模型查询与当前计划关联的标签
    const planTags = await PlanTag.findAll({
      where: {
        plan_id: currentPlan.id
      },
      include: [
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name', 'usage_count', 'is_verified']
        }
      ],
      order: [
        ['is_primary', 'DESC'],
        ['weight', 'DESC'],
        ['sort_order', 'ASC']
      ]
    });

    // 提取标签信息
    const tagIds = planTags.map(planTag => planTag.tag_id);

    // 查询每个标签下的笔记数量
    const noteCounts = tagIds.length > 0 ? await Note.findAll({
      attributes: [
        'tag_id',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        tag_id: tagIds,
        status: 'published'
      },
      group: ['tag_id']
    }) : [];

    // 创建标签ID到笔记数量的映射
    const noteCountMap = {};
    noteCounts.forEach(item => {
      noteCountMap[item.tag_id] = parseInt(item.getDataValue('count'));
    });

    // 格式化响应数据
    const formattedTags = planTags.map(planTag => ({
      id: planTag.tag_id,
      name: planTag.tag ? planTag.tag.name : '',
      weight: planTag.weight,
      isPrimary: planTag.is_primary,
      relevanceScore: planTag.relevance_score,
      sortOrder: planTag.sort_order,
      usageCount: planTag.tag ? planTag.tag.usage_count : 0,
      isVerified: planTag.tag ? planTag.tag.is_verified : false,
      noteCount: noteCountMap[planTag.tag_id] || 0
    }));

    // 添加"推荐"标签
    const recommendedTag = {
      id: 'all',
      name: '推荐',
      weight: 1,
      isPrimary: true,
      relevanceScore: 1,
      sortOrder: 0,
      usageCount: 0,
      isVerified: true,
      noteCount: await Note.count({
        where: {
          status: 'published'
        }
      })
    };

    return apiResponse.success(res, {
      planId: currentPlan.id,
      planTitle: currentPlan.title,
      tags: [recommendedTag, ...formattedTags]
    });
  } catch (error) {
    return handleApiError(error, res, 'getSquareTags');
  }
};

/**
 * 获取推荐笔记
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getRecommendedNotes = async (req, res) => {
  try {
    // 用户可能未登录，所以从req.user中获取userId可能为空
    const userId = req.user ? req.user.userId : null;
    const { limit = 5 } = req.query;

    // 获取系统默认学习计划或当前用户激活的学习计划
    let currentPlan;

    if (userId) {
      // 如果用户已登录，获取其当前激活的学习计划
      currentPlan = await LearningPlan.findOne({
        where: {
          user_id: userId,
          is_current: true
        }
      });
    }

    // 如果未找到用户的学习计划或用户未登录，获取系统默认学习计划
    if (!currentPlan) {
      currentPlan = await LearningPlan.findOne({
        where: {
          is_system_default: true
        }
      });

      if (!currentPlan) {
        return handleNotFoundError(res, '未找到系统默认学习计划');
      }
    }

    // 使用PlanTag获取与当前计划关联的标签
    const planTags = await PlanTag.findAll({
      where: {
        plan_id: currentPlan.id
      },
      order: [
        ['is_primary', 'DESC'],
        ['weight', 'DESC'],
        ['sort_order', 'ASC']
      ],
      limit: 5
    });

    const tagIds = planTags.map(planTag => planTag.tag_id);

    // 如果没有标签，返回空数组
    if (tagIds.length === 0) {
      return apiResponse.success(res, {
        notes: []
      });
    }

    // 查询推荐笔记
    const notes = await Note.findAll({
      where: {
        tag_id: tagIds,
        status: 'published'
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar_url']
        },
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name']
        }
      ],
      order: [
        ['likes', 'DESC'],
        ['comments', 'DESC'],
        ['created_at', 'DESC']
      ],
      limit: parseInt(limit)
    });

    // 查询用户点赞状态（如果用户已登录）
    let likedNoteIds = [];
    if (userId) {
      const noteIds = notes.map(note => note.id);
      const likedNotes = noteIds.length > 0 ? await NoteLike.findAll({
        where: {
          user_id: userId,
          note_id: noteIds
        },
        attributes: ['note_id']
      }) : [];

      likedNoteIds = likedNotes.map(like => like.note_id);
    }

    // 格式化响应数据
    const formattedNotes = notes.map(note => ({
      id: note.id,
      tagId: note.tag_id,
      tagName: note.tag ? note.tag.name : null,
      userId: note.user_id,
      userName: note.user ? note.user.nickname : null,
      userAvatar: note.user ? note.user.avatar_url : null,
      title: note.title,
      content: note.content,
      imageUrl: note.image_url,
      likes: note.likes,
      comments: note.comments,
      isLiked: likedNoteIds.includes(note.id),
      isAiGenerated: note.is_ai_generated,
      createdAt: note.created_at
    }));

    return apiResponse.success(res, {
      notes: formattedNotes
    });
  } catch (error) {
    return handleApiError(error, res, 'getRecommendedNotes');
  }
};

module.exports = {
  getSquareNotes,
  getSquareTags,
  getRecommendedNotes
};
