/**
 * 系统标签控制器
 * 处理系统默认标签相关的请求
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const { handleApiError } = require('../utils/errorHandler');

/**
 * 获取系统默认学习计划的标签
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getSystemDefaultTags = (req, res) => {
  try {
    // 记录请求信息
    const userInfo = req.user ? `用户ID: ${req.user.userId}` : '未登录用户';
    console.log(`系统标签控制器: 开始获取系统默认标签, ${userInfo}`);

    // 直接返回确定的系统默认标签数据
    const defaultTags = [
      { id: 21, name: '平台介绍', relevanceScore: 0.95, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 0, category: null },
      { id: 22, name: '泡泡功能', relevanceScore: 0.90, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 1, category: null },
      { id: 23, name: '广场探索', relevanceScore: 0.85, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 2, category: null },
      { id: 24, name: '学习计划', relevanceScore: 0.80, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 3, category: null },
      { id: 25, name: '笔记技巧', relevanceScore: 0.75, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 4, category: null }
    ];

    console.log(`系统标签控制器: 返回${defaultTags.length}个标签`);
    return apiResponse.success(res, { tags: defaultTags });
  } catch (error) {
    logger.error(`系统标签控制器: 获取系统默认标签失败: ${error.message}`);
    logger.error('系统标签控制器: 错误堆栈:', error.stack);

    // 即使出错也尝试返回默认数据
    try {
      const defaultTags = [
        { id: 21, name: '平台介绍', relevanceScore: 0.95, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 0, category: null },
        { id: 22, name: '泡泡功能', relevanceScore: 0.90, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 1, category: null },
        { id: 23, name: '广场探索', relevanceScore: 0.85, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 2, category: null },
        { id: 24, name: '学习计划', relevanceScore: 0.80, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 3, category: null },
        { id: 25, name: '笔记技巧', relevanceScore: 0.75, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 4, category: null }
      ];
      return apiResponse.success(res, { tags: defaultTags });
    } catch (fallbackError) {
      logger.error('系统标签控制器: 返回默认标签数据失败:', fallbackError);
      return handleApiError(fallbackError, res, 'getSystemDefaultTags');
    }
  }
};

module.exports = {
  getSystemDefaultTags
};
