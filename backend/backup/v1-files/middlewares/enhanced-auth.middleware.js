const enhancedJWT = require('../utils/enhanced-jwt');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const authConfig = require('../config/auth-config');
const { User, Role, Permission } = require('../models');
const enhancedCache = require('../services/enhanced-cache.service');

/**
 * 检查路径是否匹配配置中的路径模式
 * @param {string} path - 请求路径
 * @param {string} pattern - 路径模式
 * @returns {boolean} 是否匹配
 */
const pathMatches = (path, pattern) => {
  // 将路径模式转换为正则表达式
  const regexPattern = pattern
    .replace(/\//g, '\\/') // 转义斜杠
    .replace(/:\w+/g, '[^/]+'); // 将:param替换为正则

  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(path);
};

/**
 * 检查请求是否匹配路由配置
 * @param {Object} req - 请求对象
 * @param {Array} routes - 路由配置数组
 * @returns {boolean} 是否匹配
 */
const requestMatchesRoutes = (req, routes) => {
  return routes.some(route => {
    return pathMatches(req.path, route.path) &&
           route.methods.includes(req.method);
  });
};

/**
 * 验证JWT令牌的中间件
 */
const authenticateJWT = async (req, res, next) => {
  try {
    // 从请求头中提取令牌
    const token = enhancedJWT.extractTokenFromHeader(req);

    if (!token) {
      return apiResponse.unauthorized(res, '未提供认证令牌');
    }

    // 验证令牌
    const decoded = await enhancedJWT.verifyAccessToken(token);

    if (!decoded) {
      return apiResponse.unauthorized(res, '无效或过期的令牌');
    }

    // 将解码后的用户信息添加到请求对象
    req.user = decoded;

    // 加载用户完整信息（可选，根据需要）
    if (req.path.startsWith('/api/v2/') || req.query.loadUserInfo === 'true') {
      await loadUserInfo(req);
    }

    next();
  } catch (error) {
    logger.error(`认证中间件错误: ${error.message}`);
    return apiResponse.unauthorized(res, '认证失败');
  }
};

/**
 * 可选的JWT认证中间件
 * 如果提供了有效的令牌，则解码并添加到请求对象
 * 如果没有提供令牌或令牌无效，则继续处理请求
 */
const optionalAuthJWT = async (req, res, next) => {
  try {
    // 从请求头中提取令牌
    const token = enhancedJWT.extractTokenFromHeader(req);

    if (token) {
      // 验证令牌
      const decoded = await enhancedJWT.verifyAccessToken(token);

      if (decoded) {
        // 将解码后的用户信息添加到请求对象
        req.user = decoded;

        // 加载用户完整信息（可选，根据需要）
        if (req.path.startsWith('/api/v2/') || req.query.loadUserInfo === 'true') {
          await loadUserInfo(req);
        }
      }
    }

    next();
  } catch (error) {
    logger.error(`可选认证中间件错误: ${error.message}`);
    next();
  }
};

/**
 * 加载用户完整信息
 * @param {Object} req - 请求对象
 */
const loadUserInfo = async (req) => {
  if (!req.user || !req.user.id) return;

  try {
    // 尝试从缓存获取用户信息
    const cacheKey = `user:${req.user.id}:info`;
    let userInfo = await enhancedCache.get(cacheKey);

    if (!userInfo) {
      // 从数据库加载用户信息
      const user = await User.findByPk(req.user.id, {
        attributes: ['id', 'username', 'email', 'is_admin', 'status', 'level_id'],
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'description'],
            through: { attributes: [] },
            include: [
              {
                model: Permission,
                as: 'permissions',
                attributes: ['id', 'name', 'resource', 'action'],
                through: { attributes: [] }
              }
            ]
          }
        ]
      });

      if (user) {
        // 格式化用户信息
        userInfo = {
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: user.is_admin,
          status: user.status,
          levelId: user.level_id,
          roles: user.roles.map(role => ({
            id: role.id,
            name: role.name,
            description: role.description,
            permissions: role.permissions.map(perm => ({
              id: perm.id,
              name: perm.name,
              resource: perm.resource,
              action: perm.action
            }))
          }))
        };

        // 缓存用户信息（减少缓存时间以降低不一致风险）
        await enhancedCache.set(cacheKey, userInfo, {
          ttl: 300 // 5分钟
        });
      }
    }

    if (userInfo) {
      req.userInfo = userInfo;
    }
  } catch (error) {
    logger.error(`加载用户信息失败: ${error.message}`);
  }
};

/**
 * 检查用户是否有特定权限
 * @param {String} resource - 资源名称
 * @param {String} action - 操作名称
 * @returns {Function} 中间件函数
 */
const hasPermission = (resource, action) => {
  return async (req, res, next) => {
    try {
      // 如果用户是管理员，直接通过
      if (req.user && req.user.isAdmin) {
        return next();
      }

      // 如果没有加载用户信息，先加载
      if (!req.userInfo) {
        await loadUserInfo(req);
      }

      // 如果仍然没有用户信息，拒绝访问
      if (!req.userInfo) {
        return apiResponse.forbidden(res, '无法验证用户权限');
      }

      // 检查用户是否有所需权限
      const hasRequiredPermission = req.userInfo.roles.some(role =>
        role.permissions.some(perm =>
          perm.resource === resource && perm.action === action
        )
      );

      if (hasRequiredPermission) {
        return next();
      }

      return apiResponse.forbidden(res, '没有足够的权限执行此操作');
    } catch (error) {
      logger.error(`权限检查失败: ${error.message}`);
      return apiResponse.serverError(res, '权限检查过程中发生错误');
    }
  };
};

/**
 * 检查用户是否是管理员
 */
const isAdmin = (req, res, next) => {
  if (req.user && req.user.isAdmin) {
    return next();
  }

  return apiResponse.forbidden(res, '需要管理员权限');
};

/**
 * 统一认证中间件
 * 根据请求路径和方法决定使用哪种认证策略
 */
const authMiddleware = (req, res, next) => {
  // 检查是否是公开路由
  if (requestMatchesRoutes(req, authConfig.publicRoutes)) {
    return next();
  }

  // 检查是否是可选认证路由
  if (requestMatchesRoutes(req, authConfig.optionalAuthRoutes)) {
    return optionalAuthJWT(req, res, next);
  }

  // 默认使用强制认证
  return authenticateJWT(req, res, next);
};

module.exports = {
  authenticateJWT,
  optionalAuthJWT,
  authMiddleware,
  hasPermission,
  isAdmin
};
