/**
 * JSON验证中间件
 * 用于验证请求中的JSON字段结构
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const Joi = require('joi');

/**
 * 设备信息JSON结构验证模式
 */
const deviceInfoSchema = Joi.object({
  model: Joi.string().max(100).allow(null, '')
    .description('设备型号，如iPhone 12, Samsung Galaxy S21'),
  os: Joi.string().max(50).required()
    .description('操作系统，如iOS, Android, Windows, macOS'),
  osVersion: Joi.string().max(20).allow(null, '')
    .description('操作系统版本，如15.4, 12.0'),
  platform: Joi.string().required().valid('ios', 'android', 'web', 'desktop')
    .description('平台类型，ios/android/web/desktop'),
  screenWidth: Joi.number().integer().min(0).max(10000)
    .description('屏幕宽度（像素）'),
  screenHeight: Joi.number().integer().min(0).max(10000)
    .description('屏幕高度（像素）'),
  appVersion: Joi.string().max(20).allow(null, '')
    .description('应用版本，如1.0.0, 2.3.1'),
  deviceId: Joi.string().max(100).allow(null, '')
    .description('设备唯一标识符'),
  browser: Joi.string().max(50).allow(null, '')
    .description('浏览器名称，如Chrome, Safari, Firefox'),
  browserVersion: Joi.string().max(20).allow(null, '')
    .description('浏览器版本，如91.0.4472.124'),
  language: Joi.string().max(10).allow(null, '')
    .description('设备语言设置，如zh-CN, en-US'),
  timeZone: Joi.string().max(50).allow(null, '')
    .description('设备时区，如Asia/Shanghai, America/New_York')
}).unknown(true) // 允许其他字段
  .label('设备信息');

/**
 * 上下文数据JSON结构验证模式
 */
const contextDataSchema = Joi.object({
  view: Joi.string().max(100).required()
    .description('当前视图名称，如home, profile, learning-plan'),
  activeTags: Joi.array().items(Joi.number().integer().positive()).allow(null)
    .description('当前活动的标签ID数组'),
  scrollPosition: Joi.number().min(0).max(100000).allow(null)
    .description('滚动位置（像素）'),
  zoomLevel: Joi.number().min(0).max(10).precision(2).allow(null)
    .description('缩放级别，通常在1.0-5.0之间'),
  sessionDuration: Joi.number().integer().min(0).max(86400000).allow(null)
    .description('会话持续时间（毫秒），最大24小时'),
  previousView: Joi.string().max(100).allow(null, '')
    .description('上一个视图名称'),
  referrer: Joi.string().max(500).uri().allow(null, '')
    .description('引荐来源URL'),
  screenOrientation: Joi.string().valid('portrait', 'landscape').allow(null, '')
    .description('屏幕方向：portrait（竖屏）或landscape（横屏）'),
  networkType: Joi.string().valid('wifi', 'cellular', 'none', 'unknown').allow(null, '')
    .description('网络连接类型：wifi, cellular, none, unknown'),
  batteryLevel: Joi.number().min(0).max(100).precision(2).allow(null)
    .description('设备电池电量百分比（0-100）'),
  memoryUsage: Joi.number().min(0).max(100).precision(2).allow(null)
    .description('应用内存使用百分比（0-100）'),
  timestamp: Joi.date().iso().allow(null)
    .description('上下文数据记录时间戳，ISO格式')
}).unknown(true) // 允许其他字段
  .label('上下文数据');

/**
 * 验证设备信息JSON结构
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const validateDeviceInfo = (req, res, next) => {
  try {
    // 检查请求体中是否有deviceInfo字段
    if (!req.body.deviceInfo) {
      logger.debug('请求中没有deviceInfo字段，跳过验证');
      return next();
    }

    // 验证deviceInfo字段结构
    const { error, value } = deviceInfoSchema.validate(req.body.deviceInfo, {
      abortEarly: false, // 收集所有错误
      stripUnknown: false, // 不删除未知字段
      convert: true // 自动转换类型
    });

    if (error) {
      // 格式化错误信息
      const errorDetails = error.details.map(detail => {
        return {
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        };
      });

      logger.warn(`设备信息JSON结构验证失败: ${JSON.stringify(errorDetails)}`);

      // 返回详细的错误信息
      return apiResponse.badRequest(res, {
        message: '设备信息格式无效',
        errors: errorDetails
      });
    }

    // 记录验证成功
    logger.debug(`设备信息JSON结构验证成功: ${JSON.stringify({
      os: value.os,
      platform: value.platform,
      model: value.model || 'N/A',
      appVersion: value.appVersion || 'N/A'
    })}`);

    // 更新请求体中的deviceInfo字段
    req.body.deviceInfo = value;

    next();
  } catch (error) {
    logger.error(`设备信息JSON结构验证异常: ${error.message}`, { stack: error.stack });
    return apiResponse.serverError(res, '设备信息验证过程中发生错误');
  }
};

/**
 * 验证上下文数据JSON结构
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const validateContextData = (req, res, next) => {
  try {
    // 检查请求体中是否有contextData字段
    if (!req.body.contextData) {
      logger.debug('请求中没有contextData字段，跳过验证');
      return next();
    }

    // 验证contextData字段结构
    const { error, value } = contextDataSchema.validate(req.body.contextData, {
      abortEarly: false, // 收集所有错误
      stripUnknown: false, // 不删除未知字段
      convert: true // 自动转换类型
    });

    if (error) {
      // 格式化错误信息
      const errorDetails = error.details.map(detail => {
        return {
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        };
      });

      logger.warn(`上下文数据JSON结构验证失败: ${JSON.stringify(errorDetails)}`);

      // 返回详细的错误信息
      return apiResponse.badRequest(res, {
        message: '上下文数据格式无效',
        errors: errorDetails
      });
    }

    // 记录验证成功
    logger.debug(`上下文数据JSON结构验证成功: ${JSON.stringify({
      view: value.view,
      sessionDuration: value.sessionDuration || 'N/A',
      activeTags: Array.isArray(value.activeTags) ? `${value.activeTags.length}个标签` : 'N/A'
    })}`);

    // 更新请求体中的contextData字段
    req.body.contextData = value;

    next();
  } catch (error) {
    logger.error(`上下文数据JSON结构验证异常: ${error.message}`, { stack: error.stack });
    return apiResponse.serverError(res, '上下文数据验证过程中发生错误');
  }
};

/**
 * 验证泡泡互动JSON字段结构
 * 同时验证deviceInfo和contextData字段
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const validateBubbleInteractionJson = (req, res, next) => {
  try {
    const errors = [];

    // 验证deviceInfo字段
    if (req.body.deviceInfo) {
      const { error: deviceInfoError, value: deviceInfoValue } = deviceInfoSchema.validate(req.body.deviceInfo, {
        abortEarly: false,
        stripUnknown: false,
        convert: true
      });

      if (deviceInfoError) {
        // 格式化错误信息
        const deviceInfoErrorDetails = deviceInfoError.details.map(detail => {
          return {
            field: 'deviceInfo',
            path: detail.path.join('.'),
            message: detail.message,
            type: detail.type
          };
        });

        errors.push(...deviceInfoErrorDetails);
        logger.warn(`设备信息JSON结构验证失败: ${JSON.stringify(deviceInfoErrorDetails)}`);
      } else {
        // 验证成功，更新请求体中的deviceInfo字段
        req.body.deviceInfo = deviceInfoValue;

        // 记录验证成功
        logger.debug(`设备信息JSON结构验证成功: ${JSON.stringify({
          os: deviceInfoValue.os,
          platform: deviceInfoValue.platform,
          model: deviceInfoValue.model || 'N/A',
          appVersion: deviceInfoValue.appVersion || 'N/A'
        })}`);
      }
    }

    // 验证contextData字段
    if (req.body.contextData) {
      const { error: contextDataError, value: contextDataValue } = contextDataSchema.validate(req.body.contextData, {
        abortEarly: false,
        stripUnknown: false,
        convert: true
      });

      if (contextDataError) {
        // 格式化错误信息
        const contextDataErrorDetails = contextDataError.details.map(detail => {
          return {
            field: 'contextData',
            path: detail.path.join('.'),
            message: detail.message,
            type: detail.type
          };
        });

        errors.push(...contextDataErrorDetails);
        logger.warn(`上下文数据JSON结构验证失败: ${JSON.stringify(contextDataErrorDetails)}`);
      } else {
        // 验证成功，更新请求体中的contextData字段
        req.body.contextData = contextDataValue;

        // 记录验证成功
        logger.debug(`上下文数据JSON结构验证成功: ${JSON.stringify({
          view: contextDataValue.view,
          sessionDuration: contextDataValue.sessionDuration || 'N/A',
          activeTags: Array.isArray(contextDataValue.activeTags) ? `${contextDataValue.activeTags.length}个标签` : 'N/A'
        })}`);
      }
    }

    // 如果有错误，返回错误响应
    if (errors.length > 0) {
      return apiResponse.badRequest(res, {
        message: 'JSON字段格式无效',
        errors: errors
      });
    }

    // 记录验证成功
    logger.info(`泡泡互动JSON字段结构验证成功，用户ID: ${req.user?.userId || 'unknown'}, 标签ID: ${req.body.tagId || 'unknown'}`);

    next();
  } catch (error) {
    logger.error(`泡泡互动JSON字段结构验证异常: ${error.message}`, { stack: error.stack });
    return apiResponse.serverError(res, 'JSON字段验证过程中发生错误');
  }
};

module.exports = {
  validateDeviceInfo,
  validateContextData,
  validateBubbleInteractionJson,
  deviceInfoSchema,
  contextDataSchema
};
