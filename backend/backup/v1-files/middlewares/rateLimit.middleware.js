const rateLimit = require('express-rate-limit');
const { ApiError } = require('./error.middleware');
const logger = require('../config/logger');
const config = require('../config/config');

/**
 * 创建通用的请求频率限制中间件
 * @param {Object} options - 限制选项
 * @returns {Function} Express中间件
 */
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: config.security.rateLimit.windowMs, // 默认15分钟
    max: config.security.rateLimit.max, // 默认每个IP在windowMs时间内最多100个请求
    standardHeaders: true, // 返回标准的RateLimit头
    legacyHeaders: false, // 禁用X-RateLimit-*头
    message: '请求过于频繁，请稍后再试',
    handler: (req, res, next, options) => {
      logger.warn(`请求频率限制触发: ${req.ip} - ${req.method} ${req.originalUrl}`);
      next(new ApiError(options.message, 'RATE_LIMIT_EXCEEDED', 429));
    }
  };

  return rateLimit({
    ...defaultOptions,
    ...options
  });
};

/**
 * API通用请求限制
 * 使用配置中的默认值
 */
const apiLimiter = createRateLimiter();

/**
 * 登录请求限制
 * 使用配置中的登录限制值
 */
const loginLimiter = createRateLimiter({
  windowMs: config.security.rateLimit.loginWindowMs,
  max: config.security.rateLimit.loginMax,
  message: '登录尝试过多，请稍后再试'
});

/**
 * 创建学习计划限制
 * 每个IP每天最多创建10个学习计划
 */
const createPlanLimiter = createRateLimiter({
  windowMs: 24 * 60 * 60 * 1000, // 24小时
  max: 10, // 每个IP每天最多创建10个学习计划
  message: '创建学习计划过于频繁，请稍后再试'
});

module.exports = {
  apiLimiter,
  loginLimiter,
  createPlanLimiter
};
