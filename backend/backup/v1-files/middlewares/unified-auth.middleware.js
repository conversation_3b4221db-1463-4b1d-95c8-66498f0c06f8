/**
 * 统一授权中间件
 * 提供集中化的授权逻辑，支持基于角色和权限的细粒度控制
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const { User, Role, Permission } = require('../models');
const enhancedCache = require('../services/enhanced-cache.service');
const authorizationConfig = require('../config/authorization-config');

/**
 * 授权规则
 * 从配置文件中获取
 */
const authorizationRules = authorizationConfig.resources;

/**
 * 资源所有者检查函数映射
 * 定义如何检查用户是否是资源的所有者
 */
const ownerCheckFunctions = {
  // 笔记所有者检查
  note: async (req, resourceId) => {
    const note = await req.app.get('db').Note.findByPk(resourceId);
    if (!note) return false;
    return note.user_id === req.user.userId;
  },

  // 标签所有者检查
  tag: async (req, resourceId) => {
    const tag = await req.app.get('db').Tag.findByPk(resourceId);
    if (!tag) return false;
    return tag.creator_id === req.user.userId;
  },

  // 学习计划所有者检查
  learningPlan: async (req, resourceId) => {
    const plan = await req.app.get('db').LearningPlan.findByPk(resourceId);
    if (!plan) return false;
    return plan.user_id === req.user.userId;
  },

  // 用户所有者检查
  user: async (req, resourceId) => {
    return req.user.userId === resourceId;
  }
};

/**
 * 公开资源检查函数映射
 * 定义如何检查资源是否是公开的
 */
const publicCheckFunctions = {
  // 笔记公开检查
  note: async (req, resourceId) => {
    const note = await req.app.get('db').Note.findByPk(resourceId);
    if (!note) return false;
    return note.status === 'published';
  },

  // 标签公开检查
  tag: async (req, resourceId) => {
    return true; // 默认所有标签都是公开的
  },

  // 学习计划公开检查
  learningPlan: async (req, resourceId) => {
    const plan = await req.app.get('db').LearningPlan.findByPk(resourceId);
    if (!plan) return false;
    return plan.is_public === true;
  }
};

/**
 * 统一授权中间件
 * @param {String} resource - 资源名称
 * @param {String} action - 操作名称
 * @param {Object} options - 额外选项
 * @returns {Function} 中间件函数
 */
const authorize = (resource, action, options = {}) => {
  return async (req, res, next) => {
    try {
      // 获取授权规则
      const rule = authorizationRules[resource]?.[action];
      if (!rule) {
        logger.error(`未定义的授权规则: ${resource}:${action}`);
        return apiResponse.serverError(res, '服务器配置错误');
      }

      // 检查是否需要管理员权限
      if (rule.adminOnly) {
        if (req.user && req.user.isAdmin) {
          return next();
        }
        return apiResponse.forbidden(res, '需要管理员权限');
      }

      // 获取资源ID
      const resourceId = options.resourceIdParam
        ? req.params[options.resourceIdParam]
        : req.params.id;

      // 检查资源所有者
      if (rule.ownerCheck && resourceId) {
        const ownerCheckFn = ownerCheckFunctions[resource];
        if (ownerCheckFn) {
          const isOwner = await ownerCheckFn(req, resourceId);
          if (isOwner) {
            return next();
          }
        }
      }

      // 检查资源是否公开
      if (rule.publicCheck && resourceId) {
        const publicCheckFn = publicCheckFunctions[resource];
        if (publicCheckFn) {
          const isPublic = await publicCheckFn(req, resourceId);
          if (isPublic) {
            return next();
          }
        }
      }

      // 管理员覆盖检查
      if (rule.adminOverride && req.user && req.user.isAdmin) {
        return next();
      }

      // 检查权限
      if (rule.permissions && rule.permissions.length > 0) {
        // 如果没有加载用户信息，先加载
        if (!req.userInfo) {
          await loadUserInfo(req);
        }

        // 如果仍然没有用户信息，拒绝访问
        if (!req.userInfo) {
          return apiResponse.forbidden(res, '无法验证用户权限');
        }

        // 检查用户是否有所需权限
        const hasRequiredPermission = req.userInfo.roles.some(role =>
          role.permissions.some(perm =>
            rule.permissions.includes(`${perm.resource}:${perm.action}`)
          )
        );

        if (hasRequiredPermission) {
          return next();
        }
      }

      // 如果所有检查都失败，拒绝访问
      return apiResponse.forbidden(res, '没有足够的权限执行此操作');
    } catch (error) {
      logger.error(`授权检查失败: ${error.message}`);
      return apiResponse.serverError(res, '授权检查过程中发生错误');
    }
  };
};

/**
 * 加载用户完整信息
 * @param {Object} req - 请求对象
 */
const loadUserInfo = async (req) => {
  if (!req.user || !req.user.userId) return;

  try {
    // 尝试从缓存获取用户信息
    const cacheKey = `user:${req.user.userId}:info`;
    let userInfo = await enhancedCache.get(cacheKey);

    if (!userInfo) {
      // 从数据库加载用户信息
      const user = await User.findByPk(req.user.userId, {
        attributes: ['id', 'username', 'email', 'is_admin', 'status', 'level_id'],
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'description'],
            through: { attributes: [] },
            include: [
              {
                model: Permission,
                as: 'permissions',
                attributes: ['id', 'name', 'resource', 'action'],
                through: { attributes: [] }
              }
            ]
          }
        ]
      });

      if (user) {
        // 格式化用户信息
        userInfo = {
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: user.is_admin,
          status: user.status,
          levelId: user.level_id,
          roles: user.roles.map(role => ({
            id: role.id,
            name: role.name,
            description: role.description,
            permissions: role.permissions.map(perm => ({
              id: perm.id,
              name: perm.name,
              resource: perm.resource,
              action: perm.action
            }))
          }))
        };

        // 缓存用户信息
        await enhancedCache.set(cacheKey, userInfo, {
          ttl: 300 // 5分钟
        });
      }
    }

    if (userInfo) {
      req.userInfo = userInfo;
    }
  } catch (error) {
    logger.error(`加载用户信息失败: ${error.message}`);
  }
};

module.exports = {
  authorize,
  loadUserInfo
};
