/**
 * 统一错误处理中间件
 * 合并了error.middleware.js和errorHandler.js的功能
 */
const logger = require('../config/logger');
const apiResponse = require('../utils/apiResponse');

/**
 * 自定义API错误类
 */
class ApiError extends Error {
  constructor(message, code, status, details = {}) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.status = status;
    this.details = details;
  }
}

/**
 * 检测并修复编码错误
 * @param {Error} error - 错误对象
 * @returns {string} - 处理后的错误消息
 */
const handleEncodingError = (error) => {
  if (!error.message) return 'Unknown error';

  // 检查是否为编码相关错误
  if (error.message.includes('encode') || error.message.includes('decode') ||
      error.message.includes('character') || error.message.includes('charset')) {
    logger.error('检测到可能的编码错误:', error.message);

    try {
      // 尝试修复编码错误
      const buffer = Buffer.from(error.message, 'utf8');
      return buffer.toString('utf8');
    } catch (e) {
      logger.error('修复编码错误失败:', e);
      return error.message;
    }
  }

  return error.message;
};

/**
 * 404错误处理中间件
 */
const notFoundHandler = (req, res, next) => {
  const error = new ApiError(
    `找不到路径: ${req.originalUrl}`,
    'NOT_FOUND',
    404
  );
  next(error);
};

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  // 记录错误
  logger.error(`${err.status || 500} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);
  
  if (err.status === 500 || !err.status) {
    logger.error(err.stack);
  }
  
  // 处理可能的编码错误
  const errorMessage = handleEncodingError(err);
  
  // 根据错误类型返回适当的响应
  if (err.name === 'ValidationError') {
    return apiResponse.validationError(res, errorMessage, err.details || {});
  }

  if (err.name === 'UnauthorizedError' || err.status === 401) {
    return apiResponse.unauthorized(res, errorMessage, err.details || {});
  }

  if (err.name === 'ForbiddenError' || err.status === 403) {
    return apiResponse.forbidden(res, errorMessage, err.details || {});
  }

  if (err.name === 'NotFoundError' || err.status === 404) {
    return apiResponse.notFound(res, errorMessage, err.details || {});
  }
  
  if (err.name === 'ConflictError' || err.status === 409) {
    return apiResponse.conflict(res, errorMessage, err.details || {});
  }
  
  if (err.name === 'BadRequestError' || err.status === 400) {
    return apiResponse.badRequest(res, errorMessage, err.details || {});
  }

  // 在生产环境中隐藏500错误的详细信息
  const message = process.env.NODE_ENV === 'production' && (err.status === 500 || !err.status)
    ? '服务器内部错误'
    : errorMessage;

  // 使用自定义错误代码和状态码
  const code = err.code || 'SERVER_ERROR';
  const status = err.status || 500;
  const details = err.details || {};

  // 返回错误响应
  return apiResponse.error(res, message, code, status, details);
};

/**
 * 创建自定义错误
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @param {number} status - HTTP状态码
 * @param {Object} details - 错误详情
 * @returns {ApiError} 自定义错误对象
 */
const createError = (message, code = 'SERVER_ERROR', status = 500, details = {}) => {
  return new ApiError(message, code, status, details);
};

module.exports = {
  ApiError,
  notFoundHandler,
  errorHandler,
  createError,
  handleEncodingError
};
