const express = require('express');
const { body, query } = require('express-validator');
const bubbleController = require('../controllers/bubble.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @swagger
 * /bubble/interactions:
 *   post:
 *     summary: 记录泡泡互动
 *     tags: [Bubble]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tagId
 *               - interactionType
 *             properties:
 *               tagId:
 *                 type: integer
 *                 description: 标签ID
 *               interactionType:
 *                 type: string
 *                 enum: [click, drag, view, hold]
 *                 description: 互动类型
 *               duration:
 *                 type: integer
 *                 description: 互动持续时间(毫秒)
 *               positionX:
 *                 type: number
 *                 description: 互动位置X坐标
 *               positionY:
 *                 type: number
 *                 description: 互动位置Y坐标
 *               deviceInfo:
 *                 type: object
 *                 description: 设备信息
 *     responses:
 *       200:
 *         description: 成功记录互动
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     interactionId:
 *                       type: integer
 *                       example: 6001
 *                     tagId:
 *                       type: integer
 *                       example: 1001
 *                     interactionType:
 *                       type: string
 *                       example: click
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         $ref: '#/components/responses/BadRequestError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post(
  '/bubble/interactions',
  authMiddleware,
  [
    body('tagId').isInt().withMessage('标签ID必须是整数'),
    body('interactionType').isIn(['click', 'drag', 'view', 'hold']).withMessage('无效的互动类型'),
    body('duration').optional().isInt({ min: 0 }).withMessage('持续时间必须是非负整数'),
    body('positionX').optional().isFloat().withMessage('X坐标必须是数字'),
    body('positionY').optional().isFloat().withMessage('Y坐标必须是数字'),
    validate
  ],
  bubbleController.recordInteraction
);

/**
 * @swagger
 * /bubble/content:
 *   get:
 *     summary: 获取泡泡内容
 *     tags: [Bubble]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: tagId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 标签ID
 *     responses:
 *       200:
 *         description: 成功获取泡泡内容
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     contentType:
 *                       type: string
 *                       enum: [exercise, insight, note]
 *                       example: exercise
 *                     content:
 *                       type: object
 *                       description: 根据contentType返回不同结构的内容
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/bubble/content',
  authMiddleware,
  [
    query('tagId').isInt().withMessage('标签ID必须是整数'),
    validate
  ],
  bubbleController.getBubbleContent
);

/**
 * @swagger
 * /bubble/interactions/stats:
 *   get:
 *     summary: 获取泡泡互动统计
 *     tags: [Bubble]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: tagId
 *         required: false
 *         schema:
 *           type: integer
 *         description: 标签ID
 *       - in: query
 *         name: startDate
 *         required: false
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         required: false
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 成功获取互动统计
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     byType:
 *                       type: object
 *                       example: {"click": 10, "drag": 5}
 *                     byTag:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           tagId:
 *                             type: integer
 *                           tagName:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     byDate:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           date:
 *                             type: string
 *                             format: date
 *                           count:
 *                             type: integer
 *                     total:
 *                       type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/bubble/interactions/stats',
  authMiddleware,
  [
    query('tagId').optional().isInt().withMessage('标签ID必须是整数'),
    query('startDate').optional().isDate().withMessage('开始日期格式无效'),
    query('endDate').optional().isDate().withMessage('结束日期格式无效'),
    validate
  ],
  bubbleController.getInteractionStats
);

/**
 * @swagger
 * /bubble/content:
 *   post:
 *     summary: 创建或更新泡泡内容
 *     tags: [Bubble]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tagId
 *               - contentType
 *               - contentId
 *             properties:
 *               tagId:
 *                 type: integer
 *                 description: 标签ID
 *               contentType:
 *                 type: string
 *                 enum: [exercise, insight, note]
 *                 description: 内容类型
 *               contentId:
 *                 type: integer
 *                 description: 内容ID
 *               priority:
 *                 type: integer
 *                 description: 优先级
 *               isActive:
 *                 type: boolean
 *                 description: 是否激活
 *     responses:
 *       200:
 *         description: 成功创建或更新泡泡内容
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     tagId:
 *                       type: integer
 *                     contentType:
 *                       type: string
 *                     contentId:
 *                       type: integer
 *                     priority:
 *                       type: integer
 *                     displayCount:
 *                       type: integer
 *                     interactionCount:
 *                       type: integer
 *                     isActive:
 *                       type: boolean
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                 message:
 *                   type: string
 *       400:
 *         $ref: '#/components/responses/BadRequestError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post(
  '/bubble/content',
  authMiddleware,
  [
    body('tagId').isInt().withMessage('标签ID必须是整数'),
    body('contentType').isIn(['exercise', 'insight', 'note']).withMessage('无效的内容类型'),
    body('contentId').isInt().withMessage('内容ID必须是整数'),
    body('priority').optional().isInt().withMessage('优先级必须是整数'),
    body('isActive').optional().isBoolean().withMessage('是否激活必须是布尔值'),
    validate
  ],
  bubbleController.createOrUpdateBubbleContent
);

module.exports = router;
