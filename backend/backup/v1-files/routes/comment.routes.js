const express = require('express');
const { body, query } = require('express-validator');
const commentController = require('../controllers/comment.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @route GET /api/v1/notes/:noteId/comments
 * @desc 获取笔记评论列表
 * @access Private
 */
router.get(
  '/notes/:noteId/comments',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  commentController.getNoteComments
);

/**
 * @route POST /api/v1/notes/:noteId/comments
 * @desc 添加笔记评论
 * @access Private
 */
router.post(
  '/notes/:noteId/comments',
  authMiddleware,
  [
    body('content').notEmpty().withMessage('评论内容不能为空'),
    validate
  ],
  commentController.addNoteComment
);

/**
 * @route DELETE /api/v1/comments/:commentId
 * @desc 删除笔记评论
 * @access Private
 */
router.delete(
  '/comments/:commentId',
  authMiddleware,
  commentController.deleteNoteComment
);

/**
 * @route GET /api/v1/user/comments
 * @desc 获取用户的评论列表
 * @access Private
 */
router.get(
  '/user/comments',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  commentController.getUserComments
);

module.exports = router;
