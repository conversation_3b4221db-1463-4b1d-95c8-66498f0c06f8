const express = require('express');
const { body, query } = require('express-validator');
const insightController = require('../controllers/insight.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @route GET /api/v1/tags/:tagId/insights
 * @desc 获取标签下的观点列表
 * @access Private
 */
router.get(
  '/tags/:tagId/insights',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  insightController.getInsightsByTagId
);

/**
 * @route GET /api/v1/insights/:id
 * @desc 获取观点详情
 * @access Private
 */
router.get(
  '/insights/:id',
  authMiddleware,
  insightController.getInsightById
);

/**
 * @route POST /api/v1/insights
 * @desc 创建观点
 * @access Private
 */
router.post(
  '/insights',
  authMiddleware,
  [
    body('tagId').isInt().withMessage('标签ID必须是整数'),
    body('content').notEmpty().withMessage('观点内容不能为空'),
    body('source').optional(),
    body('background').optional(),
    validate
  ],
  insightController.createInsight
);

/**
 * @route PUT /api/v1/insights/:id
 * @desc 更新观点
 * @access Private
 */
router.put(
  '/insights/:id',
  authMiddleware,
  [
    body('content').optional().notEmpty().withMessage('观点内容不能为空'),
    body('source').optional(),
    body('background').optional(),
    validate
  ],
  insightController.updateInsight
);

/**
 * @route DELETE /api/v1/insights/:id
 * @desc 删除观点
 * @access Private
 */
router.delete(
  '/insights/:id',
  authMiddleware,
  insightController.deleteInsight
);

module.exports = router;
