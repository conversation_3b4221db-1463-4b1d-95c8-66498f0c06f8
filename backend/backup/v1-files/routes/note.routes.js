const express = require('express');
const { body, query } = require('express-validator');
const noteController = require('../controllers/note.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @route GET /api/v1/tags/:tagId/notes
 * @desc 获取标签下的笔记列表
 * @access Private
 */
router.get(
  '/tags/:tagId/notes',
  authMiddleware,
  [
    query('status').optional().isIn(['draft', 'published', 'hidden'])
      .withMessage('状态值无效'),
    query('isAiGenerated').optional().isBoolean().withMessage('是否AI生成必须是布尔值'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  noteController.getNotesByTagId
);

/**
 * @route GET /api/v1/notes/user
 * @desc 获取当前用户的笔记列表
 * @access Private
 */
router.get(
  '/notes/user',
  authMiddleware,
  [
    query('status').optional().isIn(['draft', 'published', 'hidden'])
      .withMessage('状态值无效'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  noteController.getUserNotes
);

/**
 * @route GET /api/v1/notes/:id
 * @desc 获取笔记详情
 * @access Private
 */
router.get(
  '/notes/:id',
  authMiddleware,
  noteController.getNoteById
);

/**
 * @route POST /api/v1/notes
 * @desc 创建笔记
 * @access Private
 */
router.post(
  '/notes',
  authMiddleware,
  [
    body('tagId').isInt().withMessage('标签ID必须是整数'),
    body('title').notEmpty().withMessage('笔记标题不能为空')
      .isLength({ max: 100 }).withMessage('笔记标题最多100个字符'),
    body('content').notEmpty().withMessage('笔记内容不能为空'),
    body('imageUrl').optional(),
    body('status').optional().isIn(['draft', 'published', 'hidden'])
      .withMessage('状态值无效'),
    validate
  ],
  noteController.createNote
);

/**
 * @route PUT /api/v1/notes/:id
 * @desc 更新笔记
 * @access Private
 */
router.put(
  '/notes/:id',
  authMiddleware,
  [
    body('title').optional().notEmpty().withMessage('笔记标题不能为空')
      .isLength({ max: 100 }).withMessage('笔记标题最多100个字符'),
    body('content').optional().notEmpty().withMessage('笔记内容不能为空'),
    body('imageUrl').optional(),
    body('status').optional().isIn(['draft', 'published', 'hidden'])
      .withMessage('状态值无效'),
    validate
  ],
  noteController.updateNote
);

/**
 * @route DELETE /api/v1/notes/:id
 * @desc 删除笔记
 * @access Private
 */
router.delete(
  '/notes/:id',
  authMiddleware,
  noteController.deleteNote
);

module.exports = router;
