const express = require('express');
const { body } = require('express-validator');
const shareController = require('../controllers/share.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @route GET /api/v1/share/note/:noteId
 * @desc 生成笔记分享信息
 * @access Private
 */
router.get(
  '/share/note/:noteId',
  authMiddleware,
  shareController.generateNoteShareInfo
);

/**
 * @route GET /api/v1/share/tag/:tagId
 * @desc 生成标签分享信息
 * @access Private
 */
router.get(
  '/share/tag/:tagId',
  authMiddleware,
  shareController.generateTagShareInfo
);

/**
 * @route GET /api/v1/share/plan/:planId
 * @desc 生成学习计划分享信息
 * @access Private
 */
router.get(
  '/share/plan/:planId',
  authMiddleware,
  shareController.generatePlanShareInfo
);

/**
 * @route POST /api/v1/share/record
 * @desc 记录分享事件
 * @access Private
 */
router.post(
  '/share/record',
  authMiddleware,
  [
    body('type').isIn(['note', 'tag', 'plan']).withMessage('无效的分享类型'),
    body('id').isInt().withMessage('ID必须是整数'),
    body('platform').isIn(['wechat', 'moments', 'qq', 'weibo', 'copy']).withMessage('无效的分享平台'),
    validate
  ],
  shareController.recordShareEvent
);

module.exports = router;
