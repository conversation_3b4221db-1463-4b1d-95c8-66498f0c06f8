const express = require('express');
const { query } = require('express-validator');
const squareController = require('../controllers/square.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @swagger
 * /square/notes:
 *   get:
 *     summary: 获取广场笔记列表
 *     tags: [Square]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: tagId
 *         schema:
 *           type: string
 *         description: 标签ID或"all"
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [latest, popular, comments, oldest]
 *           default: latest
 *         description: 排序方式
 *     responses:
 *       200:
 *         description: 成功获取广场笔记列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     notes:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           tagId:
 *                             type: integer
 *                           tagName:
 *                             type: string
 *                           userId:
 *                             type: string
 *                           userName:
 *                             type: string
 *                           userAvatar:
 *                             type: string
 *                           title:
 *                             type: string
 *                           content:
 *                             type: string
 *                           imageUrl:
 *                             type: string
 *                           likes:
 *                             type: integer
 *                           comments:
 *                             type: integer
 *                           isLiked:
 *                             type: boolean
 *                           isAiGenerated:
 *                             type: boolean
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         pageSize:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/square/notes',
  authMiddleware,
  [
    query('tagId').optional(),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    query('sortBy').optional().isIn(['latest', 'popular', 'comments', 'oldest']).withMessage('无效的排序方式'),
    validate
  ],
  squareController.getSquareNotes
);

/**
 * @swagger
 * /square/tags:
 *   get:
 *     summary: 获取广场标签列表
 *     tags: [Square]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取广场标签列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     planId:
 *                       type: integer
 *                     planTitle:
 *                       type: string
 *                     tags:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           weight:
 *                             type: number
 *                           usageCount:
 *                             type: integer
 *                           isVerified:
 *                             type: boolean
 *                           noteCount:
 *                             type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/square/tags',
  authMiddleware,
  squareController.getSquareTags
);

/**
 * @swagger
 * /square/recommended:
 *   get:
 *     summary: 获取推荐笔记
 *     tags: [Square]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *           default: 5
 *         description: 返回数量
 *     responses:
 *       200:
 *         description: 成功获取推荐笔记
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     notes:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           tagId:
 *                             type: integer
 *                           tagName:
 *                             type: string
 *                           userId:
 *                             type: string
 *                           userName:
 *                             type: string
 *                           userAvatar:
 *                             type: string
 *                           title:
 *                             type: string
 *                           content:
 *                             type: string
 *                           imageUrl:
 *                             type: string
 *                           likes:
 *                             type: integer
 *                           comments:
 *                             type: integer
 *                           isLiked:
 *                             type: boolean
 *                           isAiGenerated:
 *                             type: boolean
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/square/recommended',
  authMiddleware,
  [
    query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('返回数量必须是1-20之间的整数'),
    validate
  ],
  squareController.getRecommendedNotes
);

module.exports = router;
