/**
 * 系统标签路由
 * 处理系统默认标签相关的路由
 */
const express = require('express');
const systemTagController = require('../controllers/systemTag.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');

const router = express.Router();

/**
 * @route GET /api/v1/tags/system/default/tags
 * @desc 获取系统默认学习计划的标签（所有用户可见）
 * @access Public
 */
router.get(
  '/tags/system/default/tags',
  authMiddleware,
  systemTagController.getSystemDefaultTags
);

module.exports = router;
