const express = require('express');
const { body } = require('express-validator');
const tagController = require('../controllers/tag.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @route GET /api/v1/learning-plans/:id/tags
 * @desc 获取学习计划的标签
 * @access Private
 */
router.get(
  '/learning-plans/:id/tags',
  authMiddleware,
  tagController.getTagsByPlanId
);

/**
 * @route GET /api/v1/current-plan/tags
 * @desc 获取当前学习计划的标签
 * @access Private
 */
router.get(
  '/current-plan/tags',
  authMiddleware,
  tagController.getCurrentPlanTags
);

/**
 * @route PUT /api/v1/tags/reorder
 * @desc 调整标签顺序
 * @access Private
 */
router.put(
  '/reorder',
  authMiddleware,
  [
    body('tagOrders').isArray().withMessage('标签顺序必须是数组'),
    body('tagOrders.*.id').isInt().withMessage('标签ID必须是整数'),
    body('tagOrders.*.sortOrder').isInt({ min: 0 }).withMessage('排序顺序必须是非负整数'),
    validate
  ],
  tagController.reorderTags
);

/**
 * @route GET /api/v1/tags/:id
 * @desc 获取标签详情
 * @access Private
 */
router.get(
  '/tags/:id',
  authMiddleware,
  tagController.getTagById
);

/**
 * @route PUT /api/v1/tags/:id/weight
 * @desc 更新标签权重
 * @access Private
 */
router.put(
  '/tags/:id/weight',
  authMiddleware,
  [
    body('weight').isFloat({ min: 0, max: 1 }).withMessage('权重必须在0-1之间'),
    validate
  ],
  tagController.updateTagWeight
);

/**
 * @route PUT /api/v1/tags/:id/verify
 * @desc 更新标签验证状态
 * @access Private
 */
router.put(
  '/tags/:id/verify',
  authMiddleware,
  [
    body('isVerified').isBoolean().withMessage('验证状态必须是布尔值'),
    validate
  ],
  tagController.updateTagVerificationStatus
);

/**
 * @route POST /api/v1/tags/:id/increment-usage
 * @desc 增加标签使用次数
 * @access Private
 */
router.post(
  '/tags/:id/increment-usage',
  authMiddleware,
  tagController.incrementTagUsage
);

/**
 * @route GET /api/v1/system/default/tags
 * @desc 获取系统默认学习计划的标签（所有用户可见）
 * @access Public
 */
router.get(
  '/system/default/tags',
  tagController.getSystemDefaultPlanTags
);

module.exports = router;
