const express = require('express');
const { body, query } = require('express-validator');
const tagCategoryController = require('../controllers/tagCategory.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @swagger
 * /themes/{themeId}/categories:
 *   get:
 *     summary: 获取主题下的标签分类列表
 *     tags: [标签分类]
 *     parameters:
 *       - in: path
 *         name: themeId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 主题ID
 *       - in: query
 *         name: level
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 分类层级
 *     responses:
 *       200:
 *         description: 成功获取标签分类列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     categories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           description:
 *                             type: string
 *                           parentId:
 *                             type: integer
 *                           level:
 *                             type: integer
 *                           sortOrder:
 *                             type: integer
 *                           hasChildren:
 *                             type: boolean
 *                           childrenCount:
 *                             type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/themes/:themeId/categories',
  authMiddleware,
  [
    query('level').optional().isInt({ min: 1 }).withMessage('层级必须是正整数'),
    validate
  ],
  tagCategoryController.getCategoriesByTheme
);

/**
 * @route GET /api/v1/categories/:id
 * @desc 获取标签分类详情
 * @access Private
 */
router.get(
  '/categories/:id',
  authMiddleware,
  tagCategoryController.getCategoryById
);

/**
 * @route POST /api/v1/categories
 * @desc 创建标签分类
 * @access Private
 */
router.post(
  '/categories',
  authMiddleware,
  [
    body('themeId').isInt().withMessage('主题ID必须是整数'),
    body('name').notEmpty().withMessage('分类名称不能为空')
      .isLength({ max: 50 }).withMessage('分类名称最多50个字符'),
    body('description').optional(),
    body('parentId').optional().isInt().withMessage('父分类ID必须是整数'),
    body('level').optional().isInt({ min: 1 }).withMessage('层级必须是正整数'),
    body('sortOrder').optional().isInt().withMessage('排序顺序必须是整数'),
    validate
  ],
  tagCategoryController.createCategory
);

/**
 * @route PUT /api/v1/categories/:id
 * @desc 更新标签分类
 * @access Private
 */
router.put(
  '/categories/:id',
  authMiddleware,
  [
    body('name').optional().notEmpty().withMessage('分类名称不能为空')
      .isLength({ max: 50 }).withMessage('分类名称最多50个字符'),
    body('description').optional(),
    body('parentId').optional().isInt().withMessage('父分类ID必须是整数'),
    body('level').optional().isInt({ min: 1 }).withMessage('层级必须是正整数'),
    body('sortOrder').optional().isInt().withMessage('排序顺序必须是整数'),
    validate
  ],
  tagCategoryController.updateCategory
);

/**
 * @route DELETE /api/v1/categories/:id
 * @desc 删除标签分类
 * @access Private
 */
router.delete(
  '/categories/:id',
  authMiddleware,
  tagCategoryController.deleteCategory
);

/**
 * @route POST /api/v1/categories/:categoryId/tags/:tagId
 * @desc 将标签添加到分类
 * @access Private
 */
router.post(
  '/categories/:categoryId/tags/:tagId',
  authMiddleware,
  tagCategoryController.addTagToCategory
);

/**
 * @route DELETE /api/v1/categories/:categoryId/tags/:tagId
 * @desc 从分类中移除标签
 * @access Private
 */
router.delete(
  '/categories/:categoryId/tags/:tagId',
  authMiddleware,
  tagCategoryController.removeTagFromCategory
);

module.exports = router;
