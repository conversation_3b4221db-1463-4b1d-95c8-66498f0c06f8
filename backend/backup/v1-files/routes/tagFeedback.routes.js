const express = require('express');
const { body, query } = require('express-validator');
const tagFeedbackController = require('../controllers/tagFeedback.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @route POST /api/v1/tags/:tagId/feedback
 * @desc 提交标签反馈
 * @access Private
 */
router.post(
  '/tags/:tagId/feedback',
  authMiddleware,
  [
    body('feedbackType').isIn(['irrelevant', 'duplicate', 'inappropriate', 'suggestion', 'other'])
      .withMessage('无效的反馈类型'),
    body('content').optional(),
    validate
  ],
  tagFeedbackController.submitFeedback
);

/**
 * @route GET /api/v1/tags/:tagId/feedback
 * @desc 获取标签的反馈列表
 * @access Private
 */
router.get(
  '/tags/:tagId/feedback',
  authMiddleware,
  [
    query('status').optional().isIn(['pending', 'reviewed', 'resolved', 'rejected'])
      .withMessage('无效的状态'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  tagFeedbackController.getFeedbacksByTagId
);

/**
 * @route GET /api/v1/user/feedback
 * @desc 获取用户的反馈列表
 * @access Private
 */
router.get(
  '/user/feedback',
  authMiddleware,
  [
    query('status').optional().isIn(['pending', 'reviewed', 'resolved', 'rejected'])
      .withMessage('无效的状态'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  tagFeedbackController.getUserFeedbacks
);

/**
 * @route PUT /api/v1/feedback/:feedbackId/status
 * @desc 更新反馈状态
 * @access Private
 */
router.put(
  '/feedback/:feedbackId/status',
  authMiddleware,
  [
    body('status').isIn(['pending', 'reviewed', 'resolved', 'rejected'])
      .withMessage('无效的状态'),
    validate
  ],
  tagFeedbackController.updateFeedbackStatus
);

/**
 * @route GET /api/v1/feedback/stats
 * @desc 获取反馈统计信息
 * @access Private
 */
router.get(
  '/feedback/stats',
  authMiddleware,
  tagFeedbackController.getFeedbackStats
);

module.exports = router;
