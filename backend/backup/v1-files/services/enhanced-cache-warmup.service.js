/**
 * 增强版缓存预热服务
 * 提供更全面的缓存预热功能，支持分级预热和增量预热
 */

const enhancedCache = require('./enhanced-cache.service');
const {
  Theme,
  Tag,
  TagCategory,
  LearningPlan,
  Exercise,
  Note,
  Insight,
  LearningTemplate,
  User,
  sequelize
} = require('../models');
const logger = require('../config/logger');
const config = require('../config/config');
const { Op } = require('sequelize');

// 预热配置
const WARMUP_CONFIG = {
  // 预热级别
  levels: {
    minimal: ['themes', 'systemTags', 'tagCategories'],
    standard: ['themes', 'systemTags', 'tagCategories', 'popularTags', 'popularExercises', 'popularTemplates'],
    full: ['themes', 'systemTags', 'tagCategories', 'popularTags', 'popularExercises', 'popularTemplates', 'popularInsights', 'popularNotes']
  },

  // 数据量配置
  limits: {
    popularTags: 100,
    popularExercises: 50,
    popularTemplates: 20,
    popularInsights: 50,
    popularNotes: 50
  },

  // 缓存TTL配置
  ttl: {
    themes: 60 * 60 * 24, // 24小时
    systemTags: 60 * 60 * 12, // 12小时
    tagCategories: 60 * 60 * 12, // 12小时
    popularTags: 60 * 60 * 6, // 6小时
    popularExercises: 60 * 60 * 3, // 3小时
    popularTemplates: 60 * 60 * 6, // 6小时
    popularInsights: 60 * 60 * 3, // 3小时
    popularNotes: 60 * 60 * 3 // 3小时
  }
};

/**
 * 增强版缓存预热服务
 */
class EnhancedCacheWarmupService {
  /**
   * 执行缓存预热
   * @param {Object} options - 预热选项
   * @param {String} options.level - 预热级别（minimal, standard, full）
   * @param {Boolean} options.incremental - 是否增量预热
   * @param {Boolean} options.background - 是否后台预热
   * @returns {Promise<Object>} 预热结果
   */
  async warmup(options = {}) {
    const level = options.level || 'standard';
    const incremental = options.incremental !== false;
    const background = options.background !== false;

    // 获取预热项目列表
    const warmupItems = WARMUP_CONFIG.levels[level] || WARMUP_CONFIG.levels.standard;

    logger.info(`开始执行缓存预热，级别: ${level}, 增量: ${incremental}, 后台: ${background}`);

    // 预热结果
    const results = {
      success: true,
      level,
      incremental,
      background,
      items: {},
      startTime: new Date(),
      endTime: null,
      duration: 0
    };

    try {
      // 如果是后台预热，使用Promise.all并行执行
      if (background) {
        // 创建预热任务
        const warmupTasks = warmupItems.map(item => {
          return this._warmupItem(item, incremental)
            .then(result => {
              results.items[item] = result;
              return result;
            })
            .catch(error => {
              logger.error(`预热项目 ${item} 失败: ${error.message}`);
              results.items[item] = { success: false, error: error.message };
              return { success: false, error: error.message };
            });
        });

        // 并行执行预热任务
        await Promise.all(warmupTasks);
      } else {
        // 顺序执行预热任务
        for (const item of warmupItems) {
          try {
            results.items[item] = await this._warmupItem(item, incremental);
          } catch (error) {
            logger.error(`预热项目 ${item} 失败: ${error.message}`);
            results.items[item] = { success: false, error: error.message };
            results.success = false;
          }
        }
      }

      // 计算预热时间
      results.endTime = new Date();
      results.duration = results.endTime - results.startTime;

      logger.info(`缓存预热完成，耗时: ${results.duration}ms`);
      return results;
    } catch (error) {
      logger.error(`缓存预热失败: ${error.message}`);

      results.success = false;
      results.error = error.message;
      results.endTime = new Date();
      results.duration = results.endTime - results.startTime;

      return results;
    }
  }

  /**
   * 预热单个项目
   * @param {String} item - 预热项目名称
   * @param {Boolean} incremental - 是否增量预热
   * @returns {Promise<Object>} 预热结果
   * @private
   */
  async _warmupItem(item, incremental) {
    const startTime = Date.now();

    try {
      let result;

      // 根据项目类型执行不同的预热方法
      switch (item) {
        case 'themes':
          result = await this.warmupThemes();
          break;
        case 'systemTags':
          result = await this.warmupSystemTags();
          break;
        case 'tagCategories':
          result = await this.warmupTagCategories();
          break;
        case 'popularTags':
          result = await this.warmupPopularTags(WARMUP_CONFIG.limits.popularTags);
          break;
        case 'popularExercises':
          result = await this.warmupPopularExercises(WARMUP_CONFIG.limits.popularExercises);
          break;
        case 'popularTemplates':
          result = await this.warmupPopularTemplates(WARMUP_CONFIG.limits.popularTemplates);
          break;
        case 'popularInsights':
          result = await this.warmupPopularInsights(WARMUP_CONFIG.limits.popularInsights);
          break;
        case 'popularNotes':
          result = await this.warmupPopularNotes(WARMUP_CONFIG.limits.popularNotes);
          break;
        default:
          throw new Error(`未知的预热项目: ${item}`);
      }

      // 计算预热时间
      const duration = Date.now() - startTime;

      return {
        success: true,
        count: result.count,
        duration,
        ...result
      };
    } catch (error) {
      logger.error(`预热项目 ${item} 失败: ${error.message}`);

      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * 预热主题数据
   * @returns {Promise<Object>} 预热结果
   */
  async warmupThemes() {
    // 从数据库获取所有激活的主题
    const themes = await Theme.findAll({
      where: { is_active: true, deleted_at: null },
      order: [['sort_order', 'ASC']]
    });

    // 格式化主题数据
    const formattedThemes = themes.map(theme => ({
      id: theme.id,
      name: theme.name,
      englishName: theme.english_name,
      description: theme.description,
      icon: theme.icon,
      color: theme.color,
      coverImageUrl: theme.cover_image_url,
      sortOrder: theme.sort_order,
      isActive: theme.is_active
    }));

    // 缓存主题数据
    await enhancedCache.set('themes', formattedThemes, {
      ttl: WARMUP_CONFIG.ttl.themes
    });

    logger.info(`已预热主题数据缓存，共${formattedThemes.length}个主题`);

    return {
      count: formattedThemes.length
    };
  }

  /**
   * 预热系统标签
   * @returns {Promise<Object>} 预热结果
   */
  async warmupSystemTags() {
    // 从数据库获取系统标签
    const systemTags = await Tag.findAll({
      where: { is_official: true, deleted_at: null },
      order: [['name', 'ASC']]
    });

    // 格式化标签数据
    const formattedTags = systemTags.map(tag => ({
      id: tag.id,
      name: tag.name,
      categoryId: tag.category_id,
      relevanceScore: tag.relevance_score,
      weight: tag.weight,
      usageCount: tag.usage_count,
      isVerified: tag.is_verified,
      isOfficial: tag.is_official
    }));

    // 缓存系统标签
    await enhancedCache.set('system:tags', formattedTags, {
      ttl: WARMUP_CONFIG.ttl.systemTags
    });

    logger.info(`已预热系统标签缓存，共${formattedTags.length}个标签`);

    return {
      count: formattedTags.length
    };
  }

  /**
   * 预热标签分类
   * @returns {Promise<Object>} 预热结果
   */
  async warmupTagCategories() {
    // 从数据库获取标签分类
    const categories = await TagCategory.findAll({
      where: { deleted_at: null },
      order: [['sort_order', 'ASC']]
    });

    // 格式化分类数据
    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      parentId: category.parent_id,
      level: category.level,
      sortOrder: category.sort_order
    }));

    // 缓存标签分类
    await enhancedCache.set('system:categories', formattedCategories, {
      ttl: WARMUP_CONFIG.ttl.tagCategories
    });

    logger.info(`已预热标签分类缓存，共${formattedCategories.length}个分类`);

    return {
      count: formattedCategories.length
    };
  }

  /**
   * 预热热门标签
   * @param {Number} limit - 标签数量限制
   * @returns {Promise<Object>} 预热结果
   */
  async warmupPopularTags(limit = 100) {
    // 从数据库获取热门标签
    const popularTags = await Tag.findAll({
      where: { deleted_at: null },
      order: [['usage_count', 'DESC']],
      limit
    });

    // 格式化标签数据
    const formattedTags = popularTags.map(tag => ({
      id: tag.id,
      name: tag.name,
      categoryId: tag.category_id,
      relevanceScore: tag.relevance_score,
      weight: tag.weight,
      usageCount: tag.usage_count,
      isVerified: tag.is_verified,
      isOfficial: tag.is_official
    }));

    // 缓存热门标签
    await enhancedCache.set('popular:tags', formattedTags, {
      ttl: WARMUP_CONFIG.ttl.popularTags
    });

    logger.info(`已预热热门标签缓存，共${formattedTags.length}个标签`);

    return {
      count: formattedTags.length
    };
  }

  /**
   * 预热热门练习
   * @param {Number} limit - 练习数量限制
   * @returns {Promise<Object>} 预热结果
   */
  async warmupPopularExercises(limit = 50) {
    // 从数据库获取热门练习
    const popularExercises = await Exercise.findAll({
      where: {
        status: 'published',
        deleted_at: null,
        is_official: true
      },
      order: [['view_count', 'DESC']],
      limit
    });

    // 格式化练习数据
    const formattedExercises = popularExercises.map(exercise => ({
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimateMinutes: exercise.time_estimate_minutes,
      tagId: exercise.tag_id,
      viewCount: exercise.view_count,
      isOfficial: exercise.is_official
    }));

    // 缓存热门练习
    await enhancedCache.set('popular:exercises', formattedExercises, {
      ttl: WARMUP_CONFIG.ttl.popularExercises
    });

    logger.info(`已预热热门练习缓存，共${formattedExercises.length}个练习`);

    return {
      count: formattedExercises.length
    };
  }

  /**
   * 预热热门学习模板
   * @param {Number} limit - 模板数量限制
   * @returns {Promise<Object>} 预热结果
   */
  async warmupPopularTemplates(limit = 20) {
    // 从数据库获取热门学习模板
    const popularTemplates = await LearningTemplate.findAll({
      where: {
        status: 'published',
        deleted_at: null,
        is_official: true
      },
      order: [['popularity', 'DESC']],
      limit
    });

    // 格式化模板数据
    const formattedTemplates = popularTemplates.map(template => ({
      id: template.id,
      title: template.title,
      description: template.description,
      themeId: template.theme_id,
      difficulty: template.difficulty,
      estimatedDays: template.estimated_days,
      dailyGoalMinutes: template.daily_goal_minutes,
      popularity: template.popularity,
      rating: template.rating,
      ratingCount: template.rating_count,
      isOfficial: template.is_official
    }));

    // 缓存热门学习模板
    await enhancedCache.set('popular:templates', formattedTemplates, {
      ttl: WARMUP_CONFIG.ttl.popularTemplates
    });

    logger.info(`已预热热门学习模板缓存，共${formattedTemplates.length}个模板`);

    return {
      count: formattedTemplates.length
    };
  }

  /**
   * 预热热门观点
   * @param {Number} limit - 观点数量限制
   * @returns {Promise<Object>} 预热结果
   */
  async warmupPopularInsights(limit = 50) {
    try {
      // 从数据库获取热门观点
      const popularInsights = await Insight.findAll({
        where: {
          status: 'published',
          deleted_at: null,
          is_official: true
        },
        order: [['view_count', 'DESC']],
        limit
      });

      // 格式化观点数据
      const formattedInsights = popularInsights.map(insight => ({
        id: insight.id,
        title: insight.title,
        content: insight.content,
        summary: insight.summary,
        tagId: insight.tag_id,
        viewCount: insight.view_count,
        likeCount: insight.like_count,
        isOfficial: insight.is_official
      }));

      // 缓存热门观点
      await enhancedCache.set('popular:insights', formattedInsights, {
        ttl: WARMUP_CONFIG.ttl.popularInsights
      });

      logger.info(`已预热热门观点缓存，共${formattedInsights.length}个观点`);

      return {
        count: formattedInsights.length
      };
    } catch (error) {
      logger.error(`预热热门观点失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 预热热门笔记
   * @param {Number} limit - 笔记数量限制
   * @returns {Promise<Object>} 预热结果
   */
  async warmupPopularNotes(limit = 50) {
    try {
      // 从数据库获取热门笔记
      const popularNotes = await Note.findAll({
        where: {
          status: 'published',
          deleted_at: null,
          is_public: true
        },
        order: [['view_count', 'DESC']],
        limit
      });

      // 格式化笔记数据
      const formattedNotes = popularNotes.map(note => ({
        id: note.id,
        title: note.title,
        content: note.content,
        userId: note.user_id,
        tagId: note.tag_id,
        viewCount: note.view_count,
        likeCount: note.like_count,
        isPublic: note.is_public
      }));

      // 缓存热门笔记
      await enhancedCache.set('popular:notes', formattedNotes, {
        ttl: WARMUP_CONFIG.ttl.popularNotes
      });

      logger.info(`已预热热门笔记缓存，共${formattedNotes.length}个笔记`);

      return {
        count: formattedNotes.length
      };
    } catch (error) {
      logger.error(`预热热门笔记失败: ${error.message}`);
      throw error;
    }
  }
}

// 创建单例实例
const enhancedCacheWarmupService = new EnhancedCacheWarmupService();

module.exports = enhancedCacheWarmupService;
