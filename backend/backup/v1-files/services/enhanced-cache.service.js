const { redisClient } = require('../config/redis');
const config = require('../config/config');
const logger = require('../config/logger');
const NodeCache = require('node-cache');

/**
 * 增强版缓存服务
 * 提供多级缓存和高效缓存管理功能
 */
class EnhancedCacheService {
  constructor() {
    // 初始化内存缓存
    this.memoryCache = new NodeCache({
      stdTTL: 300, // 默认缓存时间5分钟
      checkperiod: 60, // 每分钟检查过期缓存
      useClones: false // 不克隆对象，提高性能
    });

    // 缓存统计信息
    this.stats = {
      hits: 0,
      misses: 0,
      memoryHits: 0,
      redisHits: 0
    };

    // 缓存键前缀
    this.keyPrefix = 'aibubb:';

    // 热点数据键列表（用于缓存预热）
    this.hotKeys = [
      'themes',
      'system:tags',
      'system:categories'
    ];
  }

  /**
   * 生成带前缀的缓存键
   * @param {String} key - 原始缓存键
   * @returns {String} 带前缀的缓存键
   */
  _generateKey(key) {
    return `${this.keyPrefix}${key}`;
  }

  /**
   * 设置缓存（多级缓存）
   * @param {String} key - 缓存键
   * @param {Object} data - 要缓存的数据
   * @param {Object} options - 缓存选项
   * @param {Number} options.ttl - Redis缓存过期时间（秒）
   * @param {Number} options.localTtl - 内存缓存过期时间（秒），默认为ttl的一半
   * @param {Boolean} options.onlyRedis - 是否只缓存到Redis
   * @param {Boolean} options.onlyMemory - 是否只缓存到内存
   * @returns {Promise<Boolean>} 是否成功
   */
  async set(key, data, options = {}) {
    try {
      const prefixedKey = this._generateKey(key);
      const ttl = options.ttl || config.redis.ttl.default;
      const localTtl = options.localTtl || Math.floor(ttl / 2);

      // 如果不是只缓存到Redis，则缓存到内存
      if (!options.onlyRedis) {
        this.memoryCache.set(prefixedKey, data, localTtl);
        logger.debug(`内存缓存已设置: ${prefixedKey}, TTL: ${localTtl}秒`);
      }

      // 如果不是只缓存到内存，且Redis可用，则缓存到Redis
      if (!options.onlyMemory && redisClient.isOpen) {
        await redisClient.set(prefixedKey, JSON.stringify(data), {
          EX: ttl
        });
        logger.debug(`Redis缓存已设置: ${prefixedKey}, TTL: ${ttl}秒`);
        return true;
      } else if (options.onlyMemory) {
        return true;
      } else if (!redisClient.isOpen) {
        logger.warn('Redis客户端未连接，仅使用内存缓存');
        return true;
      }

      return true;
    } catch (error) {
      logger.error(`设置缓存失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取缓存（多级缓存）
   * @param {String} key - 缓存键
   * @param {Object} options - 缓存选项
   * @param {Boolean} options.skipMemory - 是否跳过内存缓存
   * @param {Boolean} options.skipRedis - 是否跳过Redis缓存
   * @param {Boolean} options.updateStats - 是否更新统计信息
   * @returns {Promise<Object|null>} 缓存的数据或null
   */
  async get(key, options = {}) {
    try {
      const prefixedKey = this._generateKey(key);
      let data = null;

      // 如果不跳过内存缓存，则尝试从内存获取
      if (!options.skipMemory) {
        data = this.memoryCache.get(prefixedKey);
        if (data !== undefined) {
          if (options.updateStats) {
            this.stats.hits++;
            this.stats.memoryHits++;
          }
          logger.debug(`从内存缓存获取: ${prefixedKey}`);
          return data;
        }
      }

      // 如果不跳过Redis缓存，且Redis可用，则尝试从Redis获取
      if (!options.skipRedis && redisClient.isOpen) {
        const redisData = await redisClient.get(prefixedKey);
        if (redisData) {
          data = JSON.parse(redisData);
          
          // 如果从Redis获取到数据，且不跳过内存缓存，则更新内存缓存
          if (!options.skipMemory) {
            const localTtl = Math.floor(config.redis.ttl.default / 2);
            this.memoryCache.set(prefixedKey, data, localTtl);
            logger.debug(`从Redis获取并更新内存缓存: ${prefixedKey}`);
          }
          
          if (options.updateStats) {
            this.stats.hits++;
            this.stats.redisHits++;
          }
          
          return data;
        }
      }

      // 未找到缓存
      if (options.updateStats) {
        this.stats.misses++;
      }
      
      return null;
    } catch (error) {
      logger.error(`获取缓存失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 删除缓存（多级缓存）
   * @param {String} key - 缓存键
   * @returns {Promise<Boolean>} 是否成功
   */
  async del(key) {
    try {
      const prefixedKey = this._generateKey(key);
      
      // 删除内存缓存
      this.memoryCache.del(prefixedKey);
      logger.debug(`内存缓存已删除: ${prefixedKey}`);
      
      // 如果Redis可用，则删除Redis缓存
      if (redisClient.isOpen) {
        await redisClient.del(prefixedKey);
        logger.debug(`Redis缓存已删除: ${prefixedKey}`);
      }
      
      return true;
    } catch (error) {
      logger.error(`删除缓存失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 删除匹配模式的所有缓存
   * @param {String} pattern - 匹配模式
   * @returns {Promise<Boolean>} 是否成功
   */
  async delByPattern(pattern) {
    try {
      const prefixedPattern = `${this.keyPrefix}${pattern}`;
      
      // 删除内存缓存
      const memoryKeys = this.memoryCache.keys();
      const matchedMemoryKeys = memoryKeys.filter(key => 
        new RegExp(prefixedPattern.replace('*', '.*')).test(key)
      );
      
      if (matchedMemoryKeys.length > 0) {
        matchedMemoryKeys.forEach(key => this.memoryCache.del(key));
        logger.info(`已删除${matchedMemoryKeys.length}个匹配"${prefixedPattern}"的内存缓存`);
      }
      
      // 如果Redis可用，则删除Redis缓存
      if (redisClient.isOpen) {
        // 使用SCAN命令查找匹配的键
        let cursor = 0;
        let keys = [];
        
        do {
          const result = await redisClient.scan(cursor, {
            MATCH: prefixedPattern,
            COUNT: 100
          });
          
          cursor = result.cursor;
          keys = keys.concat(result.keys);
        } while (cursor !== 0);
        
        // 如果有匹配的键，删除它们
        if (keys.length > 0) {
          await redisClient.del(keys);
          logger.info(`已删除${keys.length}个匹配"${prefixedPattern}"的Redis缓存`);
        }
      }
      
      return true;
    } catch (error) {
      logger.error(`删除匹配缓存失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   */
  getStats() {
    const memoryCacheStats = this.memoryCache.getStats();
    
    return {
      ...this.stats,
      memory: {
        keys: this.memoryCache.keys().length,
        ...memoryCacheStats
      }
    };
  }

  /**
   * 重置缓存统计信息
   */
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      memoryHits: 0,
      redisHits: 0
    };
    
    logger.info('缓存统计信息已重置');
  }

  /**
   * 缓存预热
   * @returns {Promise<Boolean>} 是否成功
   */
  async warmup() {
    try {
      logger.info('开始缓存预热...');
      
      // 预热热点数据
      for (const key of this.hotKeys) {
        const prefixedKey = this._generateKey(key);
        
        // 如果Redis可用，则从Redis获取数据
        if (redisClient.isOpen) {
          const redisData = await redisClient.get(prefixedKey);
          
          if (redisData) {
            const data = JSON.parse(redisData);
            const localTtl = Math.floor(config.redis.ttl.default / 2);
            
            // 更新内存缓存
            this.memoryCache.set(prefixedKey, data, localTtl);
            logger.info(`缓存预热: ${prefixedKey}`);
          }
        }
      }
      
      logger.info('缓存预热完成');
      return true;
    } catch (error) {
      logger.error(`缓存预热失败: ${error.message}`);
      return false;
    }
  }
}

// 创建单例实例
const enhancedCacheService = new EnhancedCacheService();

module.exports = enhancedCacheService;
