/**
 * 观点服务
 * 处理观点相关的业务逻辑
 */
const logger = require('../config/logger');
const serviceContainer = require('../config/serviceContainer');

class InsightService {
  constructor() {
    this.insightRepository = serviceContainer.getRepository('insightRepository');
  }

  /**
   * 获取标签下的观点列表
   * @param {number} tagId - 标签ID
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {boolean} includeDeleted - 是否包含已删除的观点
   * @returns {Promise<Object>} 观点列表和分页信息
   */
  async getInsightsByTagId(tagId, userId, page = 1, pageSize = 10, includeDeleted = false) {
    try {
      // 验证标签是否属于用户
      const isTagValid = await this.insightRepository.validateTagOwnership(tagId, userId);

      if (!isTagValid) {
        throw new Error('标签不存在或不属于当前用户');
      }

      // 获取观点列表
      return await this.insightRepository.getInsightsByTagId(tagId, page, pageSize, {
        withDeleted: includeDeleted
      });
    } catch (error) {
      logger.error(`获取标签下的观点列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取观点详情
   * @param {number} insightId - 观点ID
   * @param {string} userId - 用户ID
   * @param {boolean} includeDeleted - 是否包含已删除的观点
   * @returns {Promise<Object>} 观点详情
   */
  async getInsightDetails(insightId, userId, includeDeleted = false) {
    try {
      const insight = await this.insightRepository.getInsightDetails(insightId, userId, {
        withDeleted: includeDeleted
      });

      if (!insight) {
        throw new Error('观点不存在或不属于当前用户');
      }

      return insight;
    } catch (error) {
      logger.error(`获取观点详情失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建观点
   * @param {string} userId - 用户ID
   * @param {Object} insightData - 观点数据
   * @returns {Promise<Object>} 创建的观点
   */
  async createInsight(userId, insightData) {
    try {
      // 验证标签是否属于用户
      const isTagValid = await this.insightRepository.validateTagOwnership(insightData.tagId, userId);

      if (!isTagValid) {
        throw new Error('标签不存在或不属于当前用户');
      }

      // 创建观点
      const insight = await this.insightRepository.createInsight({
        tag_id: insightData.tagId,
        content: insightData.content,
        source: insightData.source,
        background: insightData.background
      });

      return insight;
    } catch (error) {
      logger.error(`创建观点失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新观点
   * @param {number} insightId - 观点ID
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新后的观点
   */
  async updateInsight(insightId, userId, updateData) {
    try {
      // 验证观点是否存在且属于用户
      const insight = await this.insightRepository.getInsightDetails(insightId, userId);

      if (!insight) {
        throw new Error('观点不存在或不属于当前用户');
      }

      // 准备更新数据
      const dataToUpdate = {};

      if (updateData.content) dataToUpdate.content = updateData.content;
      if (updateData.source !== undefined) dataToUpdate.source = updateData.source;
      if (updateData.background !== undefined) dataToUpdate.background = updateData.background;

      // 更新观点
      await this.insightRepository.updateInsight(insightId, dataToUpdate);

      // 获取更新后的观点
      return await this.insightRepository.getInsightDetails(insightId, userId);
    } catch (error) {
      logger.error(`更新观点失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除观点
   * @param {number} insightId - 观点ID
   * @param {string} userId - 用户ID
   * @param {boolean} force - 是否强制删除（硬删除）
   * @returns {Promise<boolean>} 操作结果
   */
  async deleteInsight(insightId, userId, force = false) {
    try {
      // 验证观点是否存在且属于用户
      const insight = await this.insightRepository.getInsightDetails(insightId, userId);

      if (!insight) {
        throw new Error('观点不存在或不属于当前用户');
      }

      // 删除观点
      await this.insightRepository.deleteInsight(insightId, force);

      return true;
    } catch (error) {
      logger.error(`删除观点失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 软删除观点
   * @param {number} insightId - 观点ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 操作结果
   */
  async softDeleteInsight(insightId, userId) {
    try {
      // 验证观点是否存在且属于用户
      const insight = await this.insightRepository.getInsightDetails(insightId, userId);

      if (!insight) {
        throw new Error('观点不存在或不属于当前用户');
      }

      // 软删除观点
      await this.insightRepository.softDeleteInsight(insightId);

      return true;
    } catch (error) {
      logger.error(`软删除观点失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 恢复已软删除的观点
   * @param {number} insightId - 观点ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 操作结果
   */
  async restoreInsight(insightId, userId) {
    try {
      // 验证观点是否存在且属于用户（包括已删除的）
      const insight = await this.insightRepository.getInsightDetails(insightId, userId, true);

      if (!insight) {
        throw new Error('观点不存在或不属于当前用户');
      }

      if (!insight.deleted_at) {
        throw new Error('观点未被删除，无需恢复');
      }

      // 恢复观点
      await this.insightRepository.restoreInsight(insightId);

      return true;
    } catch (error) {
      logger.error(`恢复观点失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取已删除的观点列表
   * @param {number} tagId - 标签ID
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 观点列表和分页信息
   */
  async getDeletedInsights(tagId, userId, page = 1, pageSize = 10) {
    try {
      // 验证标签是否属于用户
      const isTagValid = await this.insightRepository.validateTagOwnership(tagId, userId);

      if (!isTagValid) {
        throw new Error('标签不存在或不属于当前用户');
      }

      // 获取已删除的观点列表
      return await this.insightRepository.getDeletedInsights(page, pageSize);
    } catch (error) {
      logger.error(`获取已删除的观点列表失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = InsightService;
