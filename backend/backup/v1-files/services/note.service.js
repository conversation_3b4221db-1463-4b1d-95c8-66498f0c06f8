/**
 * 笔记服务
 * 处理笔记相关的业务逻辑
 */
const logger = require('../config/logger');
const serviceContainer = require('../config/serviceContainer');
const { createError } = require('../utils/errorHandler');

class NoteService {
  constructor() {
    this.noteRepository = serviceContainer.getRepository('noteRepository');
    this.tagRepository = serviceContainer.getRepository('tagRepository');
  }

  /**
   * 获取用户的笔记列表
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {boolean} includeDeleted - 是否包含已删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getUserNotes(userId, page = 1, pageSize = 10, includeDeleted = false) {
    try {
      return await this.noteRepository.getUserNotes(userId, page, pageSize, {
        withDeleted: includeDeleted
      });
    } catch (error) {
      logger.error(`获取用户笔记列表失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`获取用户笔记列表失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 获取标签下的笔记列表
   * @param {number} tagId - 标签ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {boolean} includeDeleted - 是否包含已删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getNotesByTagId(tagId, page = 1, pageSize = 10, includeDeleted = false) {
    try {
      return await this.noteRepository.getNotesByTagId(tagId, page, pageSize, {
        withDeleted: includeDeleted
      });
    } catch (error) {
      logger.error(`获取标签下的笔记列表失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`获取标签下的笔记列表失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 获取笔记详情
   * @param {number} noteId - 笔记ID
   * @param {boolean} includeDeleted - 是否包含已删除的笔记
   * @returns {Promise<Object>} 笔记详情
   */
  async getNoteDetails(noteId, includeDeleted = false) {
    try {
      const note = await this.noteRepository.getNoteDetails(noteId, {
        withDeleted: includeDeleted
      });

      if (!note) {
        throw createError('笔记不存在', 'NOTE_NOT_FOUND', 404);
      }

      // 获取笔记的点赞数和评论数
      const [likesCount, commentsCount] = await Promise.all([
        this.noteRepository.getLikesCount(noteId),
        this.noteRepository.getCommentsCount(noteId)
      ]);

      return {
        ...note.toJSON(),
        likesCount,
        commentsCount
      };
    } catch (error) {
      logger.error(`获取笔记详情失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`获取笔记详情失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 创建笔记
   * @param {string} userId - 用户ID
   * @param {Object} noteData - 笔记数据
   * @returns {Promise<Object>} 创建的笔记
   */
  async createNote(userId, noteData) {
    try {
      const { tagId, title, content, imageUrl, isPublic = false } = noteData;

      // 验证标签是否存在
      const tag = await this.tagRepository.findById(tagId);

      if (!tag) {
        throw createError('标签不存在', 'TAG_NOT_FOUND', 404);
      }

      // 创建笔记
      const note = await this.noteRepository.create({
        user_id: userId,
        tag_id: tagId,
        title,
        content,
        image_url: imageUrl,
        is_public: isPublic
      });

      // 增加标签使用次数
      await this.tagRepository.incrementUsageCount(tagId, userId);

      return note;
    } catch (error) {
      logger.error(`创建笔记失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`创建笔记失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 更新笔记
   * @param {number} noteId - 笔记ID
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新后的笔记
   */
  async updateNote(noteId, userId, updateData) {
    try {
      // 验证笔记是否存在且属于当前用户
      const note = await this.noteRepository.findOne({ id: noteId, user_id: userId });

      if (!note) {
        throw createError('笔记不存在或不属于当前用户', 'NOTE_NOT_FOUND', 404);
      }

      // 如果更新了标签，验证标签是否存在
      if (updateData.tagId) {
        const tag = await this.tagRepository.findById(updateData.tagId);

        if (!tag) {
          throw createError('标签不存在', 'TAG_NOT_FOUND', 404);
        }

        // 更新标签ID
        updateData.tag_id = updateData.tagId;
        delete updateData.tagId;
      }

      // 更新笔记
      await this.noteRepository.update(updateData, { id: noteId, user_id: userId });

      // 获取更新后的笔记
      return await this.getNoteDetails(noteId);
    } catch (error) {
      logger.error(`更新笔记失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`更新笔记失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 删除笔记
   * @param {number} noteId - 笔记ID
   * @param {string} userId - 用户ID
   * @param {boolean} force - 是否强制删除（硬删除）
   * @returns {Promise<boolean>} 操作结果
   */
  async deleteNote(noteId, userId, force = false) {
    try {
      // 验证笔记是否存在且属于当前用户
      const note = await this.noteRepository.findOne({ id: noteId, user_id: userId });

      if (!note) {
        throw createError('笔记不存在或不属于当前用户', 'NOTE_NOT_FOUND', 404);
      }

      // 删除笔记
      if (force) {
        await this.noteRepository.hardDeleteNote(noteId);
      } else {
        await this.noteRepository.softDeleteNote(noteId);
      }

      return true;
    } catch (error) {
      logger.error(`删除笔记失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`删除笔记失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 软删除笔记
   * @param {number} noteId - 笔记ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 操作结果
   */
  async softDeleteNote(noteId, userId) {
    try {
      // 验证笔记是否存在且属于当前用户
      const note = await this.noteRepository.findOne({ id: noteId, user_id: userId });

      if (!note) {
        throw createError('笔记不存在或不属于当前用户', 'NOTE_NOT_FOUND', 404);
      }

      // 软删除笔记
      await this.noteRepository.softDeleteNote(noteId);

      return true;
    } catch (error) {
      logger.error(`软删除笔记失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`软删除笔记失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 恢复已软删除的笔记
   * @param {number} noteId - 笔记ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 操作结果
   */
  async restoreNote(noteId, userId) {
    try {
      // 验证笔记是否存在且属于当前用户（包括已删除的）
      const note = await this.noteRepository.findOne(
        { id: noteId, user_id: userId },
        { withDeleted: true, paranoid: false }
      );

      if (!note) {
        throw createError('笔记不存在或不属于当前用户', 'NOTE_NOT_FOUND', 404);
      }

      if (!note.deleted_at) {
        throw createError('笔记未被删除，无需恢复', 'NOTE_NOT_DELETED', 400);
      }

      // 恢复笔记
      await this.noteRepository.restoreNote(noteId);

      return true;
    } catch (error) {
      logger.error(`恢复笔记失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`恢复笔记失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 获取已删除的笔记列表
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getDeletedNotes(userId, page = 1, pageSize = 10) {
    try {
      return await this.noteRepository.getDeletedNotes(userId, page, pageSize);
    } catch (error) {
      logger.error(`获取已删除的笔记列表失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`获取已删除的笔记列表失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 获取广场笔记列表
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {boolean} includeDeleted - 是否包含已删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getSquareNotes(page = 1, pageSize = 10, includeDeleted = false) {
    try {
      return await this.noteRepository.getSquareNotes(page, pageSize, {
        withDeleted: includeDeleted
      });
    } catch (error) {
      logger.error(`获取广场笔记列表失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`获取广场笔记列表失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 获取标签下的广场笔记列表
   * @param {number} tagId - 标签ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {boolean} includeDeleted - 是否包含已删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getSquareNotesByTagId(tagId, page = 1, pageSize = 10, includeDeleted = false) {
    try {
      return await this.noteRepository.getSquareNotesByTagId(tagId, page, pageSize, {
        withDeleted: includeDeleted
      });
    } catch (error) {
      logger.error(`获取标签下的广场笔记列表失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`获取标签下的广场笔记列表失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 点赞笔记
   * @param {number} noteId - 笔记ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 操作结果
   */
  async likeNote(noteId, userId) {
    try {
      const { NoteLike } = require('../models');

      // 验证笔记是否存在
      const note = await this.noteRepository.findById(noteId);

      if (!note) {
        throw createError('笔记不存在', 'NOTE_NOT_FOUND', 404);
      }

      // 检查用户是否已点赞
      const existingLike = await NoteLike.findOne({
        where: { note_id: noteId, user_id: userId }
      });

      if (existingLike) {
        // 如果已点赞，则取消点赞
        await existingLike.destroy();
        return false;
      } else {
        // 如果未点赞，则添加点赞
        await NoteLike.create({
          note_id: noteId,
          user_id: userId
        });
        return true;
      }
    } catch (error) {
      logger.error(`点赞笔记失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`点赞笔记失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }

  /**
   * 评论笔记
   * @param {number} noteId - 笔记ID
   * @param {string} userId - 用户ID
   * @param {string} content - 评论内容
   * @returns {Promise<Object>} 创建的评论
   */
  async commentNote(noteId, userId, content) {
    try {
      const { NoteComment, User } = require('../models');

      // 验证笔记是否存在
      const note = await this.noteRepository.findById(noteId);

      if (!note) {
        throw new Error('笔记不存在');
      }

      // 创建评论
      const comment = await NoteComment.create({
        note_id: noteId,
        user_id: userId,
        content
      });

      // 获取用户信息
      const user = await User.findByPk(userId, {
        attributes: ['id', 'nickname', 'avatar_url']
      });

      return {
        id: comment.id,
        content: comment.content,
        createdAt: comment.created_at,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatarUrl: user.avatar_url
        }
      };
    } catch (error) {
      logger.error(`评论笔记失败: ${error.message}`);
      if (error.code) {
        throw error; // 如果已经是格式化的错误，直接抛出
      }
      throw createError(`评论笔记失败: ${error.message}`, 'NOTE_ERROR', 500);
    }
  }
}

module.exports = NoteService;
