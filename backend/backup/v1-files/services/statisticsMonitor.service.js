/**
 * 统计模块监控服务
 * 负责监控统计模块的性能和使用情况
 */
const logger = require('../config/logger');
const { createError } = require('../utils/errorHandler');
const { redisClient } = require('../config/redis');

class StatisticsMonitorService {
  constructor() {
    this.metricsPrefix = 'statistics:metrics:';
    this.performanceMetrics = {
      requestCount: 0,
      totalResponseTime: 0,
      maxResponseTime: 0,
      errorCount: 0,
      lastResetTime: Date.now()
    };
    
    // 按端点存储的性能指标
    this.endpointMetrics = {};
    
    // 初始化监控
    this.init();
  }
  
  /**
   * 初始化监控服务
   */
  init() {
    // 每小时重置内存中的性能指标
    setInterval(() => {
      this.resetMemoryMetrics();
    }, 60 * 60 * 1000); // 1小时
    
    logger.info('统计模块监控服务已初始化');
  }
  
  /**
   * 记录请求开始
   * @param {string} endpoint - API端点
   * @returns {number} 开始时间戳
   */
  recordRequestStart(endpoint) {
    // 确保端点指标存在
    if (!this.endpointMetrics[endpoint]) {
      this.endpointMetrics[endpoint] = {
        requestCount: 0,
        totalResponseTime: 0,
        maxResponseTime: 0,
        errorCount: 0
      };
    }
    
    return Date.now();
  }
  
  /**
   * 记录请求结束
   * @param {string} endpoint - API端点
   * @param {number} startTime - 开始时间戳
   * @param {boolean} hasError - 是否发生错误
   */
  recordRequestEnd(endpoint, startTime, hasError = false) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // 更新全局指标
    this.performanceMetrics.requestCount++;
    this.performanceMetrics.totalResponseTime += responseTime;
    this.performanceMetrics.maxResponseTime = Math.max(this.performanceMetrics.maxResponseTime, responseTime);
    
    if (hasError) {
      this.performanceMetrics.errorCount++;
    }
    
    // 更新端点指标
    if (this.endpointMetrics[endpoint]) {
      this.endpointMetrics[endpoint].requestCount++;
      this.endpointMetrics[endpoint].totalResponseTime += responseTime;
      this.endpointMetrics[endpoint].maxResponseTime = Math.max(
        this.endpointMetrics[endpoint].maxResponseTime,
        responseTime
      );
      
      if (hasError) {
        this.endpointMetrics[endpoint].errorCount++;
      }
    }
    
    // 记录慢请求
    if (responseTime > 1000) { // 超过1秒的请求
      logger.warn(`慢请求: ${endpoint}, 响应时间: ${responseTime}ms`);
      this.recordSlowRequest(endpoint, responseTime);
    }
    
    // 异步保存指标到Redis
    this.saveMetricsToRedis().catch(err => {
      logger.error(`保存指标到Redis失败: ${err.message}`);
    });
  }
  
  /**
   * 记录慢请求
   * @param {string} endpoint - API端点
   * @param {number} responseTime - 响应时间
   */
  async recordSlowRequest(endpoint, responseTime) {
    try {
      if (!redisClient) return;
      
      const key = `${this.metricsPrefix}slow_requests`;
      const data = JSON.stringify({
        endpoint,
        responseTime,
        timestamp: Date.now()
      });
      
      // 使用Redis列表存储最近的慢请求
      await redisClient.lPush(key, data);
      await redisClient.lTrim(key, 0, 99); // 只保留最近100条
    } catch (error) {
      logger.error(`记录慢请求失败: ${error.message}`);
    }
  }
  
  /**
   * 保存指标到Redis
   */
  async saveMetricsToRedis() {
    try {
      if (!redisClient) return;
      
      // 保存全局指标
      const globalKey = `${this.metricsPrefix}global`;
      await redisClient.set(globalKey, JSON.stringify(this.performanceMetrics));
      
      // 保存端点指标
      for (const [endpoint, metrics] of Object.entries(this.endpointMetrics)) {
        const endpointKey = `${this.metricsPrefix}endpoint:${endpoint}`;
        await redisClient.set(endpointKey, JSON.stringify(metrics));
      }
    } catch (error) {
      logger.error(`保存指标到Redis失败: ${error.message}`);
    }
  }
  
  /**
   * 重置内存中的性能指标
   */
  resetMemoryMetrics() {
    // 保存旧指标用于报告
    const oldMetrics = { ...this.performanceMetrics };
    
    // 重置全局指标
    this.performanceMetrics = {
      requestCount: 0,
      totalResponseTime: 0,
      maxResponseTime: 0,
      errorCount: 0,
      lastResetTime: Date.now()
    };
    
    // 重置端点指标
    this.endpointMetrics = {};
    
    // 记录重置
    logger.info(`性能指标已重置，上一周期统计: 请求数=${oldMetrics.requestCount}, 平均响应时间=${oldMetrics.requestCount > 0 ? (oldMetrics.totalResponseTime / oldMetrics.requestCount).toFixed(2) : 0}ms, 最大响应时间=${oldMetrics.maxResponseTime}ms, 错误数=${oldMetrics.errorCount}`);
  }
  
  /**
   * 获取性能指标
   * @returns {Object} 性能指标
   */
  getPerformanceMetrics() {
    const avgResponseTime = this.performanceMetrics.requestCount > 0
      ? this.performanceMetrics.totalResponseTime / this.performanceMetrics.requestCount
      : 0;
    
    const errorRate = this.performanceMetrics.requestCount > 0
      ? (this.performanceMetrics.errorCount / this.performanceMetrics.requestCount) * 100
      : 0;
    
    return {
      ...this.performanceMetrics,
      avgResponseTime,
      errorRate
    };
  }
  
  /**
   * 获取端点性能指标
   * @param {string} endpoint - API端点
   * @returns {Object} 端点性能指标
   */
  getEndpointMetrics(endpoint) {
    if (!this.endpointMetrics[endpoint]) {
      return null;
    }
    
    const metrics = this.endpointMetrics[endpoint];
    const avgResponseTime = metrics.requestCount > 0
      ? metrics.totalResponseTime / metrics.requestCount
      : 0;
    
    const errorRate = metrics.requestCount > 0
      ? (metrics.errorCount / metrics.requestCount) * 100
      : 0;
    
    return {
      ...metrics,
      avgResponseTime,
      errorRate
    };
  }
  
  /**
   * 获取所有端点性能指标
   * @returns {Object} 所有端点性能指标
   */
  getAllEndpointMetrics() {
    const result = {};
    
    for (const [endpoint, metrics] of Object.entries(this.endpointMetrics)) {
      const avgResponseTime = metrics.requestCount > 0
        ? metrics.totalResponseTime / metrics.requestCount
        : 0;
      
      const errorRate = metrics.requestCount > 0
        ? (metrics.errorCount / metrics.requestCount) * 100
        : 0;
      
      result[endpoint] = {
        ...metrics,
        avgResponseTime,
        errorRate
      };
    }
    
    return result;
  }
  
  /**
   * 获取最近的慢请求
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 慢请求列表
   */
  async getSlowRequests(limit = 10) {
    try {
      if (!redisClient) return [];
      
      const key = `${this.metricsPrefix}slow_requests`;
      const data = await redisClient.lRange(key, 0, limit - 1);
      
      return data.map(item => JSON.parse(item));
    } catch (error) {
      logger.error(`获取慢请求失败: ${error.message}`);
      return [];
    }
  }
}

module.exports = new StatisticsMonitorService();
