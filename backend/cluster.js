/**
 * Node.js 集群配置
 * 用于生产环境的多进程部署
 */

const cluster = require('cluster');
const os = require('os');
const path = require('path');
const logger = require('./config/logger');

// 集群配置
const CLUSTER_CONFIG = {
  // 工作进程数量（默认为CPU核心数）
  workers: parseInt(process.env.CLUSTER_WORKERS) || os.cpus().length,

  // 最大工作进程数量
  maxWorkers: parseInt(process.env.CLUSTER_MAX_WORKERS) || os.cpus().length * 2,

  // 最小工作进程数量
  minWorkers: parseInt(process.env.CLUSTER_MIN_WORKERS) || 1,

  // 重启延迟（毫秒）
  restartDelay: parseInt(process.env.CLUSTER_RESTART_DELAY) || 1000,

  // 最大重启次数
  maxRestarts: parseInt(process.env.CLUSTER_MAX_RESTARTS) || 10,

  // 重启时间窗口（毫秒）
  restartWindow: parseInt(process.env.CLUSTER_RESTART_WINDOW) || 60000,

  // 优雅关闭超时（毫秒）
  gracefulShutdownTimeout: parseInt(process.env.CLUSTER_SHUTDOWN_TIMEOUT) || 30000,

  // 是否启用集群模式
  enabled: process.env.CLUSTER_ENABLED !== 'false' && process.env.NODE_ENV === 'production',
};

class ClusterManager {
  constructor() {
    this.workers = new Map();
    this.restartCounts = new Map();
    this.isShuttingDown = false;
    this.startTime = Date.now();
  }

  /**
   * 启动集群
   */
  start() {
    if (!CLUSTER_CONFIG.enabled) {
      logger.info('集群模式未启用，使用单进程模式');
      require('./server.js');
      return;
    }

    if (cluster.isMaster) {
      this.startMaster();
    } else {
      this.startWorker();
    }
  }

  /**
   * 启动主进程
   */
  startMaster() {
    logger.info(`🚀 启动集群主进程 (PID: ${process.pid})`);
    logger.info(`📊 CPU核心数: ${os.cpus().length}`);
    logger.info(`👥 计划启动工作进程数: ${CLUSTER_CONFIG.workers}`);

    // 设置集群调度策略
    cluster.schedulingPolicy = cluster.SCHED_RR; // Round-robin

    // 监听工作进程事件
    this.setupWorkerEventListeners();

    // 启动工作进程
    this.forkWorkers();

    // 设置进程信号处理
    this.setupSignalHandlers();

    // 启动监控
    this.startMonitoring();

    logger.info('✅ 集群主进程启动完成');
  }

  /**
   * 启动工作进程
   */
  startWorker() {
    try {
      require('./server.js');
      logger.info(`👷 工作进程启动 (PID: ${process.pid})`);
    } catch (error) {
      logger.error(`❌ 工作进程启动失败 (PID: ${process.pid}):`, error);
      process.exit(1);
    }
  }

  /**
   * 创建工作进程
   */
  forkWorkers() {
    const workerCount = Math.min(
      Math.max(CLUSTER_CONFIG.workers, CLUSTER_CONFIG.minWorkers),
      CLUSTER_CONFIG.maxWorkers,
    );

    for (let i = 0; i < workerCount; i++) {
      this.forkWorker();
    }
  }

  /**
   * 创建单个工作进程
   */
  forkWorker() {
    const worker = cluster.fork();
    const workerId = worker.id;

    this.workers.set(workerId, {
      worker,
      startTime: Date.now(),
      restarts: 0,
    });

    logger.info(`🆕 创建工作进程 ${workerId} (PID: ${worker.process.pid})`);

    // 设置工作进程超时
    const timeout = setTimeout(() => {
      logger.warn(`⏰ 工作进程 ${workerId} 启动超时`);
      this.restartWorker(workerId);
    }, 30000);

    worker.on('listening', () => {
      clearTimeout(timeout);
      logger.info(`✅ 工作进程 ${workerId} 开始监听`);
    });

    return worker;
  }

  /**
   * 设置工作进程事件监听
   */
  setupWorkerEventListeners() {
    cluster.on('exit', (worker, code, signal) => {
      const workerId = worker.id;
      const workerInfo = this.workers.get(workerId);

      if (workerInfo) {
        const uptime = Date.now() - workerInfo.startTime;
        logger.warn(
          `💀 工作进程 ${workerId} 退出 (PID: ${worker.process.pid}, 代码: ${code}, 信号: ${signal}, 运行时间: ${uptime}ms)`,
        );

        this.workers.delete(workerId);

        if (!this.isShuttingDown) {
          this.handleWorkerExit(workerId, code, signal);
        }
      }
    });

    cluster.on('disconnect', worker => {
      logger.info(`🔌 工作进程 ${worker.id} 断开连接`);
    });

    cluster.on('online', worker => {
      logger.info(`🌐 工作进程 ${worker.id} 上线`);
    });
  }

  /**
   * 处理工作进程退出
   */
  handleWorkerExit(workerId, code, signal) {
    // 检查重启限制
    const now = Date.now();
    const restartKey = `${workerId}-${Math.floor(now / CLUSTER_CONFIG.restartWindow)}`;
    const restartCount = this.restartCounts.get(restartKey) || 0;

    if (restartCount >= CLUSTER_CONFIG.maxRestarts) {
      logger.error(`🚫 工作进程 ${workerId} 重启次数超限，停止重启`);
      return;
    }

    // 更新重启计数
    this.restartCounts.set(restartKey, restartCount + 1);

    // 延迟重启
    setTimeout(() => {
      if (!this.isShuttingDown) {
        logger.info(`🔄 重启工作进程 ${workerId} (第 ${restartCount + 1} 次)`);
        this.forkWorker();
      }
    }, CLUSTER_CONFIG.restartDelay);
  }

  /**
   * 重启工作进程
   */
  restartWorker(workerId) {
    const workerInfo = this.workers.get(workerId);
    if (!workerInfo) {
      logger.warn(`⚠️ 工作进程 ${workerId} 不存在，无法重启`);
      return;
    }

    logger.info(`🔄 正在重启工作进程 ${workerId}`);

    // 优雅关闭
    workerInfo.worker.disconnect();

    // 强制关闭超时
    const forceKillTimeout = setTimeout(() => {
      logger.warn(`💥 强制关闭工作进程 ${workerId}`);
      workerInfo.worker.kill('SIGKILL');
    }, CLUSTER_CONFIG.gracefulShutdownTimeout);

    workerInfo.worker.on('exit', () => {
      clearTimeout(forceKillTimeout);
    });
  }

  /**
   * 设置信号处理
   */
  setupSignalHandlers() {
    // 优雅关闭
    const gracefulShutdown = signal => {
      logger.info(`📡 接收到信号 ${signal}，开始优雅关闭...`);
      this.shutdown();
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 重载配置
    process.on('SIGUSR2', () => {
      logger.info('🔄 接收到重载信号，重启所有工作进程');
      this.reloadAllWorkers();
    });

    // 处理未捕获异常
    process.on('uncaughtException', error => {
      logger.error('💥 主进程未捕获异常:', error);
      this.shutdown();
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('💥 主进程未处理的Promise拒绝:', reason);
    });
  }

  /**
   * 重载所有工作进程
   */
  async reloadAllWorkers() {
    const workers = Array.from(this.workers.values());

    for (const workerInfo of workers) {
      await this.reloadWorker(workerInfo.worker.id);
      // 等待一段时间再重载下一个
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    logger.info('✅ 所有工作进程重载完成');
  }

  /**
   * 重载单个工作进程
   */
  async reloadWorker(workerId) {
    return new Promise(resolve => {
      const workerInfo = this.workers.get(workerId);
      if (!workerInfo) {
        resolve();
        return;
      }

      logger.info(`🔄 重载工作进程 ${workerId}`);

      // 先创建新的工作进程
      const newWorker = this.forkWorker();

      // 等待新进程准备就绪
      newWorker.on('listening', () => {
        // 关闭旧进程
        workerInfo.worker.disconnect();

        const timeout = setTimeout(() => {
          workerInfo.worker.kill('SIGKILL');
        }, CLUSTER_CONFIG.gracefulShutdownTimeout);

        workerInfo.worker.on('exit', () => {
          clearTimeout(timeout);
          logger.info(`✅ 工作进程 ${workerId} 重载完成`);
          resolve();
        });
      });
    });
  }

  /**
   * 启动监控
   */
  startMonitoring() {
    // 定期检查工作进程状态
    setInterval(() => {
      this.checkWorkerHealth();
    }, 30000); // 30秒检查一次

    // 定期清理重启计数
    setInterval(() => {
      this.cleanupRestartCounts();
    }, CLUSTER_CONFIG.restartWindow);

    // 输出集群状态
    setInterval(() => {
      this.logClusterStatus();
    }, 300000); // 5分钟输出一次状态
  }

  /**
   * 检查工作进程健康状态
   */
  checkWorkerHealth() {
    const activeWorkers = this.workers.size;
    const expectedWorkers = CLUSTER_CONFIG.workers;

    if (activeWorkers < expectedWorkers && !this.isShuttingDown) {
      logger.warn(`⚠️ 工作进程数量不足: ${activeWorkers}/${expectedWorkers}`);

      // 补充工作进程
      const needed = expectedWorkers - activeWorkers;
      for (let i = 0; i < needed; i++) {
        this.forkWorker();
      }
    }
  }

  /**
   * 清理重启计数
   */
  cleanupRestartCounts() {
    const now = Date.now();
    const cutoff = now - CLUSTER_CONFIG.restartWindow;

    for (const [key, timestamp] of this.restartCounts.entries()) {
      if (timestamp < cutoff) {
        this.restartCounts.delete(key);
      }
    }
  }

  /**
   * 输出集群状态
   */
  logClusterStatus() {
    const uptime = Date.now() - this.startTime;
    const workers = Array.from(this.workers.values());

    logger.info(`📊 集群状态报告:`);
    logger.info(`   主进程运行时间: ${Math.floor(uptime / 1000)}秒`);
    logger.info(`   活跃工作进程: ${workers.length}`);
    logger.info(`   内存使用: ${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`);

    workers.forEach((workerInfo, index) => {
      const workerUptime = Date.now() - workerInfo.startTime;
      logger.info(
        `   工作进程 ${workerInfo.worker.id}: PID ${workerInfo.worker.process.pid}, 运行时间 ${Math.floor(workerUptime / 1000)}秒`,
      );
    });
  }

  /**
   * 关闭集群
   */
  async shutdown() {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    logger.info('🛑 开始关闭集群...');

    const workers = Array.from(this.workers.values());
    const shutdownPromises = workers.map(workerInfo => {
      return new Promise(resolve => {
        const worker = workerInfo.worker;

        // 发送断开连接信号
        worker.disconnect();

        // 设置强制关闭超时
        const timeout = setTimeout(() => {
          logger.warn(`💥 强制关闭工作进程 ${worker.id}`);
          worker.kill('SIGKILL');
          resolve();
        }, CLUSTER_CONFIG.gracefulShutdownTimeout);

        worker.on('exit', () => {
          clearTimeout(timeout);
          logger.info(`✅ 工作进程 ${worker.id} 已关闭`);
          resolve();
        });
      });
    });

    // 等待所有工作进程关闭
    await Promise.all(shutdownPromises);

    logger.info('✅ 集群关闭完成');
    process.exit(0);
  }

  /**
   * 获取集群统计信息
   */
  getStats() {
    const workers = Array.from(this.workers.values());
    const uptime = Date.now() - this.startTime;

    return {
      master: {
        pid: process.pid,
        uptime: uptime,
        memory: process.memoryUsage(),
      },
      workers: workers.map(workerInfo => ({
        id: workerInfo.worker.id,
        pid: workerInfo.worker.process.pid,
        uptime: Date.now() - workerInfo.startTime,
        restarts: workerInfo.restarts,
      })),
      config: CLUSTER_CONFIG,
      isShuttingDown: this.isShuttingDown,
    };
  }
}

// 创建并启动集群管理器
const clusterManager = new ClusterManager();

// 导出集群管理器（用于测试和监控）
module.exports = clusterManager;

// 如果直接运行此文件，启动集群
if (require.main === module) {
  clusterManager.start();
}
