/**
 * 授权配置
 * 定义资源和操作的授权规则
 */

module.exports = {
  /**
   * 资源授权规则
   * 每个资源定义了不同操作所需的权限和检查
   */
  resources: {
    // 笔记资源
    note: {
      // 创建笔记需要'note:create'权限
      create: { permissions: ['note:create'] },
      // 更新笔记需要'note:update'权限或者是资源所有者
      update: { permissions: ['note:update'], ownerCheck: true },
      // 删除笔记需要'note:delete'权限或者是资源所有者
      delete: { permissions: ['note:delete'], ownerCheck: true },
      // 查看笔记需要'note:read'权限或者是资源所有者或者笔记是公开的
      read: { permissions: ['note:read'], ownerCheck: true, publicCheck: true },
      // 恢复笔记需要'note:restore'权限或者是资源所有者
      restore: { permissions: ['note:restore'], ownerCheck: true },
      // 软删除笔记需要'note:softDelete'权限或者是资源所有者
      softDelete: { permissions: ['note:softDelete'], ownerCheck: true },
      // 点赞笔记需要'note:like'权限
      like: { permissions: ['note:like'] },
      // 评论笔记需要'note:comment'权限
      comment: { permissions: ['note:comment'] }
    },

    // 标签资源
    tag: {
      create: { permissions: ['tag:create'] },
      update: { permissions: ['tag:update'], ownerCheck: true },
      delete: { permissions: ['tag:delete'], ownerCheck: true },
      read: { permissions: ['tag:read'], publicCheck: true },
      restore: { permissions: ['tag:restore'], ownerCheck: true }
    },

    // 学习计划资源
    learningPlan: {
      create: { permissions: ['learningPlan:create'] },
      update: { permissions: ['learningPlan:update'], ownerCheck: true },
      delete: { permissions: ['learningPlan:delete'], ownerCheck: true },
      read: { permissions: ['learningPlan:read'], ownerCheck: true, publicCheck: true },
      restore: { permissions: ['learningPlan:restore'], ownerCheck: true }
    },

    // 用户资源
    user: {
      create: { permissions: ['user:create'], adminOnly: true },
      update: { permissions: ['user:update'], ownerCheck: true, adminOverride: true },
      delete: { permissions: ['user:delete'], adminOnly: true },
      read: { permissions: ['user:read'], ownerCheck: true, adminOverride: true },
      restore: { permissions: ['user:restore'], adminOnly: true }
    },

    // 观点资源
    insight: {
      create: { permissions: ['insight:create'] },
      update: { permissions: ['insight:update'], ownerCheck: true },
      delete: { permissions: ['insight:delete'], ownerCheck: true },
      read: { permissions: ['insight:read'], ownerCheck: true, publicCheck: true },
      restore: { permissions: ['insight:restore'], ownerCheck: true }
    },

    // 练习资源
    exercise: {
      create: { permissions: ['exercise:create'] },
      update: { permissions: ['exercise:update'], ownerCheck: true },
      delete: { permissions: ['exercise:delete'], ownerCheck: true },
      read: { permissions: ['exercise:read'], ownerCheck: true, publicCheck: true },
      restore: { permissions: ['exercise:restore'], ownerCheck: true }
    },

    // 成就资源
    achievement: {
      create: { permissions: ['achievement:create'], adminOnly: true },
      update: { permissions: ['achievement:update'], adminOnly: true },
      delete: { permissions: ['achievement:delete'], adminOnly: true },
      read: { permissions: ['achievement:read'] },
      award: { permissions: ['achievement:award'], adminOnly: true }
    },

    // 徽章资源
    badge: {
      create: { permissions: ['badge:create'], adminOnly: true },
      update: { permissions: ['badge:update'], adminOnly: true },
      delete: { permissions: ['badge:delete'], adminOnly: true },
      read: { permissions: ['badge:read'] },
      award: { permissions: ['badge:award'], adminOnly: true }
    },

    // 泡泡互动资源
    bubble: {
      create: { permissions: ['bubble:create'] },
      update: { permissions: ['bubble:update'], ownerCheck: true, adminOverride: true },
      delete: { permissions: ['bubble:delete'], ownerCheck: true, adminOverride: true },
      read: { permissions: ['bubble:read'] },
      stats: { permissions: ['bubble:stats'], adminOnly: true }
    }
  },

  /**
   * 角色权限映射
   * 定义每个角色拥有的权限
   */
  roles: {
    // 管理员角色
    admin: [
      'user:create', 'user:update', 'user:delete', 'user:read', 'user:restore',
      'note:create', 'note:update', 'note:delete', 'note:read', 'note:restore', 'note:softDelete', 'note:like', 'note:comment',
      'tag:create', 'tag:update', 'tag:delete', 'tag:read', 'tag:restore',
      'learningPlan:create', 'learningPlan:update', 'learningPlan:delete', 'learningPlan:read', 'learningPlan:restore',
      'insight:create', 'insight:update', 'insight:delete', 'insight:read', 'insight:restore',
      'exercise:create', 'exercise:update', 'exercise:delete', 'exercise:read', 'exercise:restore',
      'achievement:create', 'achievement:update', 'achievement:delete', 'achievement:read', 'achievement:award',
      'badge:create', 'badge:update', 'badge:delete', 'badge:read', 'badge:award',
      'bubble:create', 'bubble:update', 'bubble:delete', 'bubble:read', 'bubble:stats'
    ],

    // 普通用户角色
    user: [
      'user:read',
      'note:create', 'note:update', 'note:delete', 'note:read', 'note:restore', 'note:softDelete', 'note:like', 'note:comment',
      'tag:create', 'tag:read',
      'learningPlan:create', 'learningPlan:update', 'learningPlan:delete', 'learningPlan:read', 'learningPlan:restore',
      'insight:create', 'insight:update', 'insight:delete', 'insight:read', 'insight:restore',
      'exercise:create', 'exercise:update', 'exercise:delete', 'exercise:read', 'exercise:restore',
      'achievement:read',
      'badge:read',
      'bubble:create', 'bubble:read'
    ],

    // 访客角色
    guest: [
      'note:read',
      'tag:read',
      'learningPlan:read',
      'insight:read',
      'exercise:read',
      'achievement:read',
      'badge:read',
      'bubble:read'
    ]
  }
};
