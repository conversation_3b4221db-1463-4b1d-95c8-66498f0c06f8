/**
 * 备份调度配置
 */

const cron = require('node-cron');
const BackupService = require('../scripts/backup');
const logger = require('./logger');

class BackupScheduler {
  constructor() {
    this.backupService = new BackupService();
    this.jobs = new Map();
    this.isRunning = false;
  }

  /**
   * 启动备份调度
   */
  start() {
    if (this.isRunning) {
      logger.warn('备份调度已经在运行');
      return;
    }

    logger.info('🕐 启动备份调度服务...');

    // 每日完整备份 - 凌晨2点
    this.scheduleJob('daily-full-backup', '0 2 * * *', async () => {
      try {
        logger.info('开始执行每日完整备份...');
        await this.backupService.performFullBackup();
        logger.info('每日完整备份完成');
      } catch (error) {
        logger.error('每日完整备份失败:', error);
      }
    });

    // 每周数据库备份 - 周日凌晨1点
    this.scheduleJob('weekly-db-backup', '0 1 * * 0', async () => {
      try {
        logger.info('开始执行每周数据库备份...');
        const backupPath = await this.backupService.backupDatabase(
          require('path').join(this.backupService.backupDir, 'weekly')
        );
        logger.info(`每周数据库备份完成: ${backupPath}`);
      } catch (error) {
        logger.error('每周数据库备份失败:', error);
      }
    });

    // 每小时增量备份（仅备份日志和关键文件）
    this.scheduleJob('hourly-incremental', '0 * * * *', async () => {
      try {
        await this.performIncrementalBackup();
      } catch (error) {
        logger.error('增量备份失败:', error);
      }
    });

    // 每日清理旧备份 - 凌晨3点
    this.scheduleJob('daily-cleanup', '0 3 * * *', async () => {
      try {
        logger.info('开始清理旧备份...');
        await this.backupService.cleanupOldBackups();
        logger.info('旧备份清理完成');
      } catch (error) {
        logger.error('备份清理失败:', error);
      }
    });

    // 每周备份验证 - 周一凌晨4点
    this.scheduleJob('weekly-validation', '0 4 * * 1', async () => {
      try {
        logger.info('开始验证备份文件...');
        await this.validateBackups();
        logger.info('备份验证完成');
      } catch (error) {
        logger.error('备份验证失败:', error);
      }
    });

    // 每月备份报告 - 每月1号凌晨5点
    this.scheduleJob('monthly-report', '0 5 1 * *', async () => {
      try {
        logger.info('生成月度备份报告...');
        await this.generateMonthlyReport();
        logger.info('月度备份报告生成完成');
      } catch (error) {
        logger.error('月度备份报告生成失败:', error);
      }
    });

    this.isRunning = true;
    logger.info('✅ 备份调度服务启动完成');
  }

  /**
   * 停止备份调度
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    logger.info('🛑 停止备份调度服务...');

    // 停止所有定时任务
    for (const [name, job] of this.jobs) {
      job.stop();
      logger.info(`停止定时任务: ${name}`);
    }

    this.jobs.clear();
    this.isRunning = false;
    logger.info('✅ 备份调度服务已停止');
  }

  /**
   * 调度任务
   */
  scheduleJob(name, schedule, task) {
    if (this.jobs.has(name)) {
      logger.warn(`定时任务 ${name} 已存在，跳过创建`);
      return;
    }

    const job = cron.schedule(schedule, task, {
      scheduled: false,
      timezone: process.env.TZ || 'Asia/Shanghai'
    });

    job.start();
    this.jobs.set(name, job);
    logger.info(`📅 创建定时任务: ${name} (${schedule})`);
  }

  /**
   * 执行增量备份
   */
  async performIncrementalBackup() {
    const fs = require('fs');
    const path = require('path');
    const archiver = require('archiver');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `incremental-backup-${timestamp}.tar.gz`;
    const backupPath = path.join(this.backupService.backupDir, backupName);

    // 备份关键文件
    const filesToBackup = [
      'logs',
      '.env.production',
      'package.json',
    ];

    const output = fs.createWriteStream(backupPath);
    const archive = archiver('tar', { gzip: true });

    return new Promise((resolve, reject) => {
      output.on('close', () => {
        const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
        logger.info(`增量备份完成: ${backupPath} (${sizeInMB} MB)`);
        resolve(backupPath);
      });

      archive.on('error', reject);
      archive.pipe(output);

      // 添加文件到归档
      for (const item of filesToBackup) {
        const sourcePath = path.join(process.cwd(), item);
        if (fs.existsSync(sourcePath)) {
          const stat = fs.statSync(sourcePath);
          if (stat.isDirectory()) {
            archive.directory(sourcePath, item);
          } else {
            archive.file(sourcePath, { name: item });
          }
        }
      }

      archive.finalize();
    });
  }

  /**
   * 验证备份文件
   */
  async validateBackups() {
    const backups = this.backupService.listBackups();
    const validationResults = [];

    for (const backup of backups.slice(0, 5)) { // 验证最新的5个备份
      try {
        await this.backupService.validateBackup(backup.path);
        validationResults.push({ name: backup.name, status: 'valid' });
        logger.info(`✅ 备份验证通过: ${backup.name}`);
      } catch (error) {
        validationResults.push({ name: backup.name, status: 'invalid', error: error.message });
        logger.error(`❌ 备份验证失败: ${backup.name} - ${error.message}`);
      }
    }

    return validationResults;
  }

  /**
   * 生成月度备份报告
   */
  async generateMonthlyReport() {
    const stats = this.backupService.getBackupStats();
    const backups = this.backupService.listBackups();
    
    const report = {
      timestamp: new Date().toISOString(),
      period: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
      },
      statistics: stats,
      backups: {
        total: backups.length,
        totalSize: stats.totalSizeMB,
        oldest: stats.oldestBackup,
        newest: stats.newestBackup,
      },
      health: {
        validBackups: backups.length,
        failedBackups: 0, // 可以通过验证结果计算
        averageSize: backups.length > 0 ? (stats.totalSize / backups.length / 1024 / 1024).toFixed(2) : 0,
      },
      recommendations: this.generateRecommendations(stats, backups),
    };

    // 保存报告
    const fs = require('fs');
    const path = require('path');
    const reportPath = path.join(this.backupService.backupDir, `backup-report-${report.period.year}-${report.period.month.toString().padStart(2, '0')}.json`);
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    logger.info(`月度备份报告已保存: ${reportPath}`);

    return report;
  }

  /**
   * 生成备份建议
   */
  generateRecommendations(stats, backups) {
    const recommendations = [];

    // 检查备份数量
    if (backups.length < 7) {
      recommendations.push('建议保留至少7天的备份');
    }

    // 检查备份大小
    const avgSize = stats.totalSize / backups.length;
    if (avgSize > 1024 * 1024 * 1024) { // 1GB
      recommendations.push('备份文件较大，考虑优化备份策略');
    }

    // 检查备份频率
    if (backups.length > 0) {
      const latestBackup = new Date(stats.newestBackup);
      const daysSinceLastBackup = (Date.now() - latestBackup.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceLastBackup > 1) {
        recommendations.push('最近备份时间超过1天，建议检查备份调度');
      }
    }

    // 检查存储空间
    if (stats.totalSize > 10 * 1024 * 1024 * 1024) { // 10GB
      recommendations.push('备份占用存储空间较大，考虑清理旧备份或压缩策略');
    }

    return recommendations;
  }

  /**
   * 获取调度状态
   */
  getStatus() {
    const jobs = Array.from(this.jobs.entries()).map(([name, job]) => ({
      name,
      running: job.running,
      scheduled: job.scheduled,
    }));

    return {
      isRunning: this.isRunning,
      jobCount: this.jobs.size,
      jobs,
      backupStats: this.backupService.getBackupStats(),
    };
  }

  /**
   * 手动触发备份
   */
  async triggerBackup(type = 'full') {
    logger.info(`手动触发${type}备份...`);

    try {
      switch (type) {
        case 'full':
          return await this.backupService.performFullBackup();
        case 'incremental':
          return await this.performIncrementalBackup();
        case 'database':
          return await this.backupService.backupDatabase(
            require('path').join(this.backupService.backupDir, 'manual')
          );
        default:
          throw new Error(`未知的备份类型: ${type}`);
      }
    } catch (error) {
      logger.error(`手动备份失败 (${type}):`, error);
      throw error;
    }
  }
}

// 创建全局备份调度器实例
const backupScheduler = new BackupScheduler();

module.exports = backupScheduler;