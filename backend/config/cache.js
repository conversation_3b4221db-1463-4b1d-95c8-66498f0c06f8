/**
 * 缓存配置
 */

const config = require('./config');

const cacheConfig = {
  // Redis配置
  redis: {
    url: config.redis.url,
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    db: config.redis.db,
    keyPrefix: 'aibubb:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
  },

  // 缓存策略配置
  strategies: {
    // 用户会话缓存
    session: {
      ttl: 24 * 60 * 60, // 24小时
      prefix: 'session:',
    },

    // API响应缓存
    api: {
      ttl: 5 * 60, // 5分钟
      prefix: 'api:',
    },

    // 用户数据缓存
    user: {
      ttl: 30 * 60, // 30分钟
      prefix: 'user:',
    },

    // 学习计划缓存
    learningPlan: {
      ttl: 15 * 60, // 15分钟
      prefix: 'plan:',
    },

    // 主题数据缓存
    theme: {
      ttl: 60 * 60, // 1小时
      prefix: 'theme:',
    },

    // 标签数据缓存
    tag: {
      ttl: 30 * 60, // 30分钟
      prefix: 'tag:',
    },

    // 每日内容缓存
    dailyContent: {
      ttl: 60 * 60, // 1小时
      prefix: 'daily:',
    },

    // 统计数据缓存
    statistics: {
      ttl: 10 * 60, // 10分钟
      prefix: 'stats:',
    },

    // 配置数据缓存（长期缓存）
    config: {
      ttl: 24 * 60 * 60, // 24小时
      prefix: 'config:',
    },

    // 临时数据缓存
    temp: {
      ttl: 5 * 60, // 5分钟
      prefix: 'temp:',
    },
  },

  // 缓存选项
  options: {
    // 是否启用缓存
    enabled: process.env.CACHE_ENABLED !== 'false',

    // 默认TTL
    defaultTtl: 15 * 60, // 15分钟

    // 最大缓存大小（字节）
    maxSize: parseInt(process.env.CACHE_MAX_SIZE) || 100 * 1024 * 1024, // 100MB

    // 缓存命中率统计
    enableStats: process.env.NODE_ENV === 'production',

    // 缓存预热
    enableWarmup: true,

    // 缓存压缩
    enableCompression: true,

    // 缓存序列化
    serialization: {
      enabled: true,
      format: 'json', // json, msgpack
    },

    // 缓存失效策略
    eviction: {
      policy: 'lru', // lru, lfu, fifo
      maxMemoryPolicy: 'allkeys-lru',
    },

    // 缓存监控
    monitoring: {
      enabled: true,
      logLevel: 'info',
      metricsInterval: 60000, // 1分钟
    },
  },

  // 缓存键生成器
  keyGenerator: {
    // 用户相关键
    user: userId => `user:${userId}`,
    userProfile: userId => `user:profile:${userId}`,
    userPlans: (userId, page = 1, limit = 10) => `user:plans:${userId}:${page}:${limit}`,

    // 学习计划相关键
    plan: planId => `plan:${planId}`,
    planDetails: (planId, userId) => `plan:details:${planId}:${userId}`,
    planProgress: (planId, userId) => `plan:progress:${planId}:${userId}`,

    // 主题相关键
    themes: () => 'themes:all',
    theme: themeId => `theme:${themeId}`,
    themeActive: () => 'themes:active',

    // 标签相关键
    tags: () => 'tags:all',
    tag: tagId => `tag:${tagId}`,
    planTags: planId => `plan:tags:${planId}`,

    // 每日内容相关键
    dailyContent: (planId, dayNumber) => `daily:${planId}:${dayNumber}`,
    dailyContents: planId => `daily:all:${planId}`,

    // 统计相关键
    userStats: userId => `stats:user:${userId}`,
    planStats: planId => `stats:plan:${planId}`,
    systemStats: () => 'stats:system',

    // API响应缓存键
    apiResponse: (method, path, query = '') => {
      const queryStr = typeof query === 'object' ? JSON.stringify(query) : query;
      return `api:${method}:${path}:${Buffer.from(queryStr).toString('base64')}`;
    },
  },

  // 缓存工具函数
  utils: {
    // 生成缓存键
    generateKey: (prefix, ...parts) => {
      return `${prefix}:${parts.filter(p => p !== null && p !== undefined).join(':')}`;
    },

    // 解析TTL
    parseTtl: ttl => {
      if (typeof ttl === 'number') return ttl;
      if (typeof ttl === 'string') {
        const units = { s: 1, m: 60, h: 3600, d: 86400 };
        const match = ttl.match(/^(\d+)([smhd])$/);
        if (match) {
          return parseInt(match[1]) * units[match[2]];
        }
      }
      return cacheConfig.options.defaultTtl;
    },

    // 检查缓存键是否有效
    isValidKey: key => {
      return typeof key === 'string' && key.length > 0 && key.length <= 250;
    },

    // 获取缓存统计信息
    getStats: () => {
      return {
        enabled: cacheConfig.options.enabled,
        strategies: Object.keys(cacheConfig.strategies).length,
        defaultTtl: cacheConfig.options.defaultTtl,
        maxSize: cacheConfig.options.maxSize,
      };
    },
  },
};

module.exports = cacheConfig;
