/**
 * CORS配置
 */

const config = require('./config');

// 允许的域名列表
const allowedOrigins = [
  'https://aibubb.com',
  'https://www.aibubb.com',
  'https://app.aibubb.com',
  'https://admin.aibubb.com',
  'https://api.aibubb.com',
];

// 开发环境允许的域名
const developmentOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:8080',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:3001',
  'http://127.0.0.1:8080',
];

// 根据环境变量添加额外的允许域名
if (process.env.ALLOWED_ORIGINS) {
  const extraOrigins = process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
  allowedOrigins.push(...extraOrigins);
}

// 开发环境添加本地域名
if (config.server.env === 'development') {
  allowedOrigins.push(...developmentOrigins);
}

const corsConfig = {
  // 动态origin检查
  origin: function (origin, callback) {
    // 允许没有origin的请求（如移动应用、Postman等）
    if (!origin) {
      return callback(null, true);
    }

    // 检查是否在允许列表中
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    // 开发环境允许所有localhost和127.0.0.1
    if (config.server.env === 'development') {
      const isLocalhost = /^https?:\/\/(localhost|127\.0\.0\.1)(:\d+)?$/.test(origin);
      if (isLocalhost) {
        return callback(null, true);
      }
    }

    // 生产环境严格检查
    if (config.server.env === 'production') {
      console.warn(`CORS blocked origin: ${origin}`);
      return callback(new Error('Not allowed by CORS'), false);
    }

    // 其他环境允许
    return callback(null, true);
  },

  // 允许的HTTP方法
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],

  // 允许的请求头
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Client-Version',
    'X-Request-ID',
    'X-Forwarded-For',
    'User-Agent',
  ],

  // 暴露的响应头
  exposedHeaders: [
    'X-Total-Count',
    'X-Page-Count',
    'X-Current-Page',
    'X-Per-Page',
    'X-Rate-Limit-Limit',
    'X-Rate-Limit-Remaining',
    'X-Rate-Limit-Reset',
    'X-Request-ID',
    'X-Response-Time',
  ],

  // 是否允许发送Cookie
  credentials: true,

  // 预检请求缓存时间（秒）
  maxAge: config.server.env === 'production' ? 86400 : 3600, // 生产环境24小时，其他1小时

  // 是否成功时返回204状态码
  optionsSuccessStatus: 204,

  // 预检请求失败时的状态码
  preflightContinue: false,
};

// CORS中间件配置
const corsMiddleware = {
  // 基础CORS配置
  basic: corsConfig,

  // 严格CORS配置（用于敏感API）
  strict: {
    ...corsConfig,
    origin: function (origin, callback) {
      // 严格模式只允许明确配置的域名
      if (!origin || allowedOrigins.includes(origin)) {
        return callback(null, true);
      }
      console.warn(`Strict CORS blocked origin: ${origin}`);
      return callback(new Error('Not allowed by strict CORS'), false);
    },
    credentials: true,
    maxAge: 3600, // 1小时
  },

  // 宽松CORS配置（用于公开API）
  permissive: {
    ...corsConfig,
    origin: true, // 允许所有域名
    credentials: false,
  },

  // API专用CORS配置
  api: {
    ...corsConfig,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: [...corsConfig.allowedHeaders, 'X-API-Version', 'X-Client-ID'],
  },

  // 文件上传CORS配置
  upload: {
    ...corsConfig,
    methods: ['POST', 'PUT'],
    allowedHeaders: [
      ...corsConfig.allowedHeaders,
      'Content-Length',
      'Content-Range',
      'X-File-Name',
      'X-File-Size',
    ],
  },
};

// CORS工具函数
const corsUtils = {
  // 检查origin是否被允许
  isOriginAllowed: origin => {
    if (!origin) return true;
    return allowedOrigins.includes(origin);
  },

  // 获取允许的域名列表
  getAllowedOrigins: () => {
    return [...allowedOrigins];
  },

  // 添加允许的域名
  addAllowedOrigin: origin => {
    if (origin && !allowedOrigins.includes(origin)) {
      allowedOrigins.push(origin);
    }
  },

  // 移除允许的域名
  removeAllowedOrigin: origin => {
    const index = allowedOrigins.indexOf(origin);
    if (index > -1) {
      allowedOrigins.splice(index, 1);
    }
  },

  // 验证CORS配置
  validateConfig: () => {
    const errors = [];

    if (allowedOrigins.length === 0) {
      errors.push('No allowed origins configured');
    }

    if (
      config.server.env === 'production' &&
      allowedOrigins.some(origin => origin.includes('localhost'))
    ) {
      errors.push('Production environment should not allow localhost origins');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  },

  // 获取CORS统计信息
  getStats: () => {
    return {
      allowedOrigins: allowedOrigins.length,
      environment: config.server.env,
      credentialsEnabled: corsConfig.credentials,
      maxAge: corsConfig.maxAge,
    };
  },
};

module.exports = {
  corsConfig,
  corsMiddleware,
  corsUtils,
  allowedOrigins,
};
