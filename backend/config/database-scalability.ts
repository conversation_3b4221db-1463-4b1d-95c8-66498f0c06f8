/**
 * 数据库可伸缩性配置
 * 提供读写分离、分片和连接池配置
 */
import { Sequelize, Options } from 'sequelize';
import { Logger } from '../infrastructure/logging/Logger';

/**
 * 数据库节点类型
 */
export enum DatabaseNodeType {
  MASTER = 'master',
  SLAVE = 'slave',
  SHARD = 'shard'
}

/**
 * 数据库节点接口
 */
export interface DatabaseNode {
  type: DatabaseNodeType;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  weight?: number; // 负载均衡权重
  maxConnections?: number; // 最大连接数
  isActive?: boolean; // 是否激活
}

/**
 * 分片配置接口
 */
export interface ShardConfig {
  enabled: boolean;
  strategy: 'user_id' | 'time_range' | 'custom';
  shardCount: number;
  shardMap?: Record<string, DatabaseNode>;
}

/**
 * 读写分离配置接口
 */
export interface ReadWriteSplitConfig {
  enabled: boolean;
  readPreference: 'random' | 'round_robin' | 'weighted';
  autoFailover: boolean;
  failoverRetryInterval: number; // 毫秒
}

/**
 * 数据库可伸缩性配置接口
 */
export interface DatabaseScalabilityConfig {
  master: DatabaseNode;
  slaves: DatabaseNode[];
  shards: DatabaseNode[];
  readWriteSplit: ReadWriteSplitConfig;
  sharding: ShardConfig;
  connectionPooling: {
    maxPoolSize: number;
    minPoolSize: number;
    acquireTimeout: number;
    idleTimeout: number;
  };
}

/**
 * 默认数据库可伸缩性配置
 */
export const defaultScalabilityConfig: DatabaseScalabilityConfig = {
  master: {
    type: DatabaseNodeType.MASTER,
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aibubb_db',
    maxConnections: 50,
    isActive: true
  },
  slaves: [
    {
      type: DatabaseNodeType.SLAVE,
      host: process.env.DB_SLAVE_HOST || process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_SLAVE_PORT || process.env.DB_PORT || '3306'),
      username: process.env.DB_SLAVE_USER || process.env.DB_USER || 'root',
      password: process.env.DB_SLAVE_PASSWORD || process.env.DB_PASSWORD || '',
      database: process.env.DB_SLAVE_NAME || process.env.DB_NAME || 'aibubb_db',
      weight: 100,
      maxConnections: 100,
      isActive: process.env.DB_SLAVE_ENABLED === 'true'
    }
  ],
  shards: [],
  readWriteSplit: {
    enabled: process.env.DB_READ_WRITE_SPLIT === 'true',
    readPreference: 'round_robin',
    autoFailover: true,
    failoverRetryInterval: 30000 // 30秒
  },
  sharding: {
    enabled: process.env.DB_SHARDING === 'true',
    strategy: 'user_id',
    shardCount: parseInt(process.env.DB_SHARD_COUNT || '0')
  },
  connectionPooling: {
    maxPoolSize: parseInt(process.env.DB_POOL_MAX || '10'),
    minPoolSize: parseInt(process.env.DB_POOL_MIN || '0'),
    acquireTimeout: 30000,
    idleTimeout: 10000
  }
};

/**
 * 创建主数据库连接
 * @param config 数据库可伸缩性配置
 * @param logger 日志记录器
 * @returns Sequelize实例
 */
export function createMasterConnection(
  config: DatabaseScalabilityConfig = defaultScalabilityConfig,
  logger?: Logger
): Sequelize {
  const master = config.master;

  const options: Options = {
    host: master.host,
    port: master.port,
    dialect: 'mysql',
    logging: logger ? (msg) => logger.debug(msg) : false,
    pool: {
      max: config.connectionPooling.maxPoolSize,
      min: config.connectionPooling.minPoolSize,
      acquire: config.connectionPooling.acquireTimeout,
      idle: config.connectionPooling.idleTimeout
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      underscored: true,
      timestamps: true
    },
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      supportBigNumbers: true,
      bigNumberStrings: true
    }
  };

  return new Sequelize(
    master.database,
    master.username,
    master.password,
    options
  );
}

/**
 * 创建从数据库连接
 * @param config 数据库可伸缩性配置
 * @param logger 日志记录器
 * @returns Sequelize实例数组
 */
export function createSlaveConnections(
  config: DatabaseScalabilityConfig = defaultScalabilityConfig,
  logger?: Logger
): Sequelize[] {
  // 如果读写分离未启用或没有从节点，返回空数组
  if (!config.readWriteSplit.enabled || config.slaves.length === 0) {
    return [];
  }

  return config.slaves
    .filter(slave => slave.isActive)
    .map(slave => {
      const options: Options = {
        host: slave.host,
        port: slave.port,
        dialect: 'mysql',
        logging: logger ? (msg) => logger.debug(msg) : false,
        pool: {
          max: slave.maxConnections || config.connectionPooling.maxPoolSize,
          min: config.connectionPooling.minPoolSize,
          acquire: config.connectionPooling.acquireTimeout,
          idle: config.connectionPooling.idleTimeout
        },
        define: {
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci',
          underscored: true,
          timestamps: true
        },
        dialectOptions: {
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci',
          supportBigNumbers: true,
          bigNumberStrings: true
        }
      };

      return new Sequelize(
        slave.database,
        slave.username,
        slave.password,
        options
      );
    });
}

/**
 * 创建分片数据库连接
 * @param config 数据库可伸缩性配置
 * @param logger 日志记录器
 * @returns 分片ID到Sequelize实例的映射
 */
export function createShardConnections(
  config: DatabaseScalabilityConfig = defaultScalabilityConfig,
  logger?: Logger
): Record<string, Sequelize> {
  // 如果分片未启用或没有分片节点，返回空对象
  if (!config.sharding.enabled || config.shards.length === 0) {
    return {};
  }

  const shardConnections: Record<string, Sequelize> = {};

  config.shards
    .filter(shard => shard.isActive)
    .forEach((shard, index) => {
      const shardId = `shard_${index}`;

      const options: Options = {
        host: shard.host,
        port: shard.port,
        dialect: 'mysql',
        logging: logger ? (msg) => logger.debug(msg) : false,
        pool: {
          max: shard.maxConnections || config.connectionPooling.maxPoolSize,
          min: config.connectionPooling.minPoolSize,
          acquire: config.connectionPooling.acquireTimeout,
          idle: config.connectionPooling.idleTimeout
        },
        define: {
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci',
          underscored: true,
          timestamps: true
        },
        dialectOptions: {
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci',
          supportBigNumbers: true,
          bigNumberStrings: true
        }
      };

      shardConnections[shardId] = new Sequelize(
        shard.database,
        shard.username,
        shard.password,
        options
      );
    });

  return shardConnections;
}