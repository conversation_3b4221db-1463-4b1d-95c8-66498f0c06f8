/**
 * 端到端测试环境配置
 * 用于设置端到端测试的环境参数
 */

const path = require('path');
const fs = require('fs');

// 默认配置
const defaultConfig = {
  // 服务器配置
  server: {
    port: process.env.E2E_TEST_PORT || 9093,
    host: process.env.E2E_TEST_HOST || 'localhost',
    protocol: process.env.E2E_TEST_PROTOCOL || 'http',
    apiPrefix: process.env.E2E_TEST_API_PREFIX || '/api/v2'
  },
  
  // 数据库配置 (使用测试数据库)
  database: {
    host: process.env.E2E_TEST_DB_HOST || 'localhost',
    port: process.env.E2E_TEST_DB_PORT || 3306,
    name: process.env.E2E_TEST_DB_NAME || 'aibubb_e2e_test',
    user: process.env.E2E_TEST_DB_USER || 'root',
    password: process.env.E2E_TEST_DB_PASSWORD || 'secret',
    dialect: 'mysql',
    logging: false
  },
  
  // Redis配置
  redis: {
    url: process.env.E2E_TEST_REDIS_URL || 'redis://localhost:6379',
    password: process.env.E2E_TEST_REDIS_PASSWORD || '',
    enabled: process.env.E2E_TEST_REDIS_ENABLED === 'true'
  },
  
  // 测试配置
  test: {
    // 测试报告输出目录
    reportDir: process.env.E2E_TEST_REPORT_DIR || path.resolve(__dirname, '../test-reports/e2e-tests'),
    
    // 测试超时时间 (毫秒)
    timeout: parseInt(process.env.E2E_TEST_TIMEOUT || '30000'),
    
    // 测试数据目录
    dataDir: process.env.E2E_TEST_DATA_DIR || path.resolve(__dirname, '../tests/e2e/data'),
    
    // 测试用户凭据 (用于认证测试)
    testUser: {
      username: process.env.E2E_TEST_USER || '<EMAIL>',
      password: process.env.E2E_TEST_PASSWORD || 'testpassword'
    }
  },
  
  // 浏览器测试配置 (用于前端测试)
  browser: {
    // 是否启用浏览器测试
    enabled: process.env.E2E_TEST_BROWSER_ENABLED === 'true',
    
    // 浏览器类型 (chrome, firefox, webkit)
    type: process.env.E2E_TEST_BROWSER_TYPE || 'chrome',
    
    // 是否使用无头模式
    headless: process.env.E2E_TEST_BROWSER_HEADLESS !== 'false',
    
    // 浏览器窗口大小
    viewport: {
      width: parseInt(process.env.E2E_TEST_BROWSER_WIDTH || '1280'),
      height: parseInt(process.env.E2E_TEST_BROWSER_HEIGHT || '720')
    },
    
    // 截图目录
    screenshotDir: process.env.E2E_TEST_SCREENSHOT_DIR || path.resolve(__dirname, '../test-reports/e2e-tests/screenshots')
  },
  
  // CI/CD集成配置
  ci: {
    // 是否在CI环境中运行
    isCI: process.env.CI === 'true',
    
    // CI环境名称 (如 'github-actions', 'jenkins')
    ciName: process.env.CI_NAME || '',
    
    // 构建ID
    buildId: process.env.CI_BUILD_ID || '',
    
    // 是否在测试失败时阻止构建
    failBuildOnTestFailure: process.env.E2E_TEST_FAIL_BUILD !== 'false'
  }
};

// 加载环境特定配置
let envConfig = {};
const envConfigPath = path.resolve(__dirname, `e2e-test-${process.env.NODE_ENV}.js`);

if (fs.existsSync(envConfigPath)) {
  try {
    envConfig = require(envConfigPath);
    console.log(`已加载环境特定配置: ${envConfigPath}`);
  } catch (error) {
    console.error(`加载环境特定配置失败: ${error.message}`);
  }
}

// 合并配置
const config = {
  ...defaultConfig,
  ...envConfig
};

// 创建测试报告目录
if (!fs.existsSync(config.test.reportDir)) {
  try {
    fs.mkdirSync(config.test.reportDir, { recursive: true });
    console.log(`已创建测试报告目录: ${config.test.reportDir}`);
  } catch (error) {
    console.error(`创建测试报告目录失败: ${error.message}`);
  }
}

// 创建截图目录
if (config.browser.enabled && !fs.existsSync(config.browser.screenshotDir)) {
  try {
    fs.mkdirSync(config.browser.screenshotDir, { recursive: true });
    console.log(`已创建截图目录: ${config.browser.screenshotDir}`);
  } catch (error) {
    console.error(`创建截图目录失败: ${error.message}`);
  }
}

module.exports = config;
