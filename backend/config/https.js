/**
 * HTTPS配置
 */

const fs = require('fs');
const path = require('path');
const config = require('./config');

const httpsConfig = {
  // HTTPS配置
  enabled: process.env.HTTPS_ENABLED === 'true',
  port: parseInt(process.env.HTTPS_PORT) || 443,
  
  // SSL证书配置
  ssl: {
    // 证书文件路径
    keyPath: process.env.SSL_KEY_PATH || path.join(process.cwd(), 'ssl', 'private.key'),
    certPath: process.env.SSL_CERT_PATH || path.join(process.cwd(), 'ssl', 'certificate.crt'),
    caPath: process.env.SSL_CA_PATH || path.join(process.cwd(), 'ssl', 'ca_bundle.crt'),
    
    // 证书选项
    options: {
      // 最小TLS版本
      secureProtocol: 'TLSv1_2_method',
      
      // 密码套件
      ciphers: [
        'ECDHE-RSA-AES128-GCM-SHA256',
        'ECDHE-RSA-AES256-GCM-SHA384',
        'ECDHE-RSA-AES128-SHA256',
        'ECDHE-RSA-AES256-SHA384',
        'ECDHE-RSA-AES256-SHA256',
        'ECDHE-RSA-AES128-SHA',
        'ECDHE-RSA-AES256-SHA',
        'AES128-GCM-SHA256',
        'AES256-GCM-SHA384',
        'AES128-SHA256',
        'AES256-SHA256',
        'AES128-SHA',
        'AES256-SHA',
        'DES-CBC3-SHA'
      ].join(':'),
      
      // 优先使用服务器密码套件
      honorCipherOrder: true,
      
      // 禁用不安全的重新协商
      secureRenegotiation: false,
    }
  },

  // HTTP重定向到HTTPS
  redirect: {
    enabled: process.env.HTTPS_REDIRECT === 'true',
    permanent: true, // 301重定向
    excludePaths: [
      '/health',
      '/health-detailed',
      '/.well-known',
    ]
  },

  // HSTS配置
  hsts: {
    enabled: process.env.HSTS_ENABLED !== 'false',
    maxAge: parseInt(process.env.HSTS_MAX_AGE) || 31536000, // 1年
    includeSubDomains: process.env.HSTS_INCLUDE_SUBDOMAINS === 'true',
    preload: process.env.HSTS_PRELOAD === 'true',
  },

  // 证书自动更新配置
  autoRenew: {
    enabled: process.env.SSL_AUTO_RENEW === 'true',
    provider: process.env.SSL_PROVIDER || 'letsencrypt', // letsencrypt, custom
    email: process.env.SSL_EMAIL,
    domains: process.env.SSL_DOMAINS ? process.env.SSL_DOMAINS.split(',') : [],
    renewDays: parseInt(process.env.SSL_RENEW_DAYS) || 30, // 30天前续期
  }
};

/**
 * 获取SSL证书选项
 */
function getSSLOptions() {
  const options = { ...httpsConfig.ssl.options };

  try {
    // 读取证书文件
    if (fs.existsSync(httpsConfig.ssl.keyPath)) {
      options.key = fs.readFileSync(httpsConfig.ssl.keyPath);
    }

    if (fs.existsSync(httpsConfig.ssl.certPath)) {
      options.cert = fs.readFileSync(httpsConfig.ssl.certPath);
    }

    if (fs.existsSync(httpsConfig.ssl.caPath)) {
      options.ca = fs.readFileSync(httpsConfig.ssl.caPath);
    }

    return options;
  } catch (error) {
    console.error('读取SSL证书失败:', error);
    return null;
  }
}

/**
 * 检查SSL证书是否存在
 */
function checkSSLCertificates() {
  const checks = {
    keyExists: fs.existsSync(httpsConfig.ssl.keyPath),
    certExists: fs.existsSync(httpsConfig.ssl.certPath),
    caExists: fs.existsSync(httpsConfig.ssl.caPath),
  };

  checks.valid = checks.keyExists && checks.certExists;
  
  return checks;
}

/**
 * 创建HTTPS重定向中间件
 */
function createHTTPSRedirectMiddleware() {
  return (req, res, next) => {
    // 检查是否启用重定向
    if (!httpsConfig.redirect.enabled) {
      return next();
    }

    // 检查是否已经是HTTPS
    if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
      return next();
    }

    // 检查排除路径
    const isExcluded = httpsConfig.redirect.excludePaths.some(path => 
      req.path.startsWith(path)
    );

    if (isExcluded) {
      return next();
    }

    // 构建HTTPS URL
    const httpsUrl = `https://${req.get('host')}${req.originalUrl}`;
    
    // 重定向到HTTPS
    const statusCode = httpsConfig.redirect.permanent ? 301 : 302;
    res.redirect(statusCode, httpsUrl);
  };
}

/**
 * 创建HSTS中间件
 */
function createHSTSMiddleware() {
  return (req, res, next) => {
    if (!httpsConfig.hsts.enabled) {
      return next();
    }

    // 只在HTTPS连接上设置HSTS
    if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
      let hstsValue = `max-age=${httpsConfig.hsts.maxAge}`;
      
      if (httpsConfig.hsts.includeSubDomains) {
        hstsValue += '; includeSubDomains';
      }
      
      if (httpsConfig.hsts.preload) {
        hstsValue += '; preload';
      }
      
      res.setHeader('Strict-Transport-Security', hstsValue);
    }

    next();
  };
}

/**
 * 生成自签名证书（仅用于开发环境）
 */
function generateSelfSignedCertificate() {
  const { execSync } = require('child_process');
  const sslDir = path.join(process.cwd(), 'ssl');

  try {
    // 创建SSL目录
    if (!fs.existsSync(sslDir)) {
      fs.mkdirSync(sslDir, { recursive: true });
    }

    // 生成私钥
    execSync(`openssl genrsa -out ${path.join(sslDir, 'private.key')} 2048`);

    // 生成证书
    execSync(`openssl req -new -x509 -key ${path.join(sslDir, 'private.key')} -out ${path.join(sslDir, 'certificate.crt')} -days 365 -subj "/C=CN/ST=Beijing/L=Beijing/O=AIBUBB/CN=localhost"`);

    console.log('✅ 自签名证书生成成功');
    return true;
  } catch (error) {
    console.error('❌ 自签名证书生成失败:', error);
    return false;
  }
}

/**
 * 验证HTTPS配置
 */
function validateHTTPSConfig() {
  const errors = [];
  const warnings = [];

  if (httpsConfig.enabled) {
    const certCheck = checkSSLCertificates();
    
    if (!certCheck.valid) {
      errors.push('SSL证书文件不存在或不完整');
    }

    if (httpsConfig.port === config.server.port) {
      warnings.push('HTTPS端口与HTTP端口相同');
    }

    if (httpsConfig.autoRenew.enabled && !httpsConfig.autoRenew.email) {
      warnings.push('启用了自动续期但未配置邮箱');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}

module.exports = {
  httpsConfig,
  getSSLOptions,
  checkSSLCertificates,
  createHTTPSRedirectMiddleware,
  createHSTSMiddleware,
  generateSelfSignedCertificate,
  validateHTTPSConfig,
};