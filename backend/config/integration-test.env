# 集成测试环境配置

# 服务器配置
PORT=3001
NODE_ENV=test
API_VERSION=v2

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=aibubb_test
DB_USER=test_user
DB_PASSWORD=test_password
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=integration_test_secret_key
JWT_EXPIRES_IN=24h

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/integration-test.log

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=300

# 速率限制配置
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# CORS配置
CORS_ORIGIN=*

# 集成测试特定配置
INTEGRATION_TEST_MODE=true
MOCK_EXTERNAL_SERVICES=true
AUTO_SEED_DATA=true
