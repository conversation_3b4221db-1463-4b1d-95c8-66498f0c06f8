/**
 * 数据库配置模块
 */
const { validateEnvVar } = require('../utils/env-utils');

const databaseConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  name: process.env.DB_NAME || 'aibubb_db',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql',
  logging: process.env.NODE_ENV === 'development',
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    acquire: 30000,
    idle: 10000
  },

  /**
   * 验证配置
   * @param {Array} errors - 错误数组
   * @returns {boolean} 验证是否通过
   */
  validate(errors = []) {
    let isValid = true;

    // 验证主机
    if (!validateEnvVar('DB_HOST', this.host, errors)) {
      isValid = false;
    }

    // 验证端口
    if (!this.port || isNaN(this.port)) {
      errors.push('DB_PORT 未设置或不是有效的数字');
      isValid = false;
    }

    // 验证数据库名
    if (!validateEnvVar('DB_NAME', this.name, errors)) {
      isValid = false;
    }

    // 验证用户名
    if (!validateEnvVar('DB_USER', this.user, errors)) {
      isValid = false;
    }

    // 验证密码
    if (!validateEnvVar('DB_PASSWORD', this.password, errors)) {
      isValid = false;
    }

    return isValid;
  }
};

module.exports = databaseConfig;
