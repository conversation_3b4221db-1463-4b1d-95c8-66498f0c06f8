/**
 * 邮件配置模块
 */
const { validateEnvVar } = require('../utils/env-utils');

const emailConfig = {
  // 邮件服务器配置
  host: process.env.EMAIL_HOST || 'smtp.example.com',
  port: parseInt(process.env.EMAIL_PORT) || 587,
  secure: process.env.EMAIL_SECURE === 'true',
  user: process.env.EMAIL_USER || '<EMAIL>',
  password: process.env.EMAIL_PASSWORD || 'password',
  senderName: process.env.EMAIL_SENDER_NAME || 'AIBUBB',

  // 邮件发送配置
  retryCount: parseInt(process.env.EMAIL_RETRY_COUNT) || 3,
  retryDelay: parseInt(process.env.EMAIL_RETRY_DELAY) || 1000,
  timeout: parseInt(process.env.EMAIL_TIMEOUT) || 10000,

  // 邮件模板配置
  templates: {
    welcome: {
      subject: '欢迎加入AIBUBB',
      template: 'welcome'
    },
    verification: {
      subject: 'AIBUBB - 邮箱验证',
      template: 'verification'
    },
    passwordReset: {
      subject: 'AIBUBB - 密码重置',
      template: 'password-reset'
    }
  }
};

// 验证必要的环境变量
const validateEmailConfig = () => {
  if (process.env.NODE_ENV === 'production') {
    validateEnvVar('EMAIL_HOST', '邮件服务器主机');
    validateEnvVar('EMAIL_PORT', '邮件服务器端口');
    validateEnvVar('EMAIL_USER', '邮件服务器用户名');
    validateEnvVar('EMAIL_PASSWORD', '邮件服务器密码');
  }
};

module.exports = {
  emailConfig,
  validateEmailConfig
};
