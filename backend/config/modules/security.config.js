/**
 * 安全配置模块
 */

const securityConfig = {
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // 每个IP最多100个请求
    loginWindowMs: parseInt(process.env.RATE_LIMIT_LOGIN_WINDOW_MS) || 3600000, // 1小时
    loginMax: parseInt(process.env.RATE_LIMIT_LOGIN_MAX) || 20 // 每个IP最多20次登录尝试
  },

  /**
   * 验证配置
   * @param {Array} errors - 错误数组
   * @returns {boolean} 验证是否通过
   */
  validate(errors = []) {
    let isValid = true;

    // 验证速率限制窗口
    if (!this.rateLimit.windowMs || isNaN(this.rateLimit.windowMs)) {
      errors.push('RATE_LIMIT_WINDOW_MS 未设置或不是有效的数字');
      isValid = false;
    }

    // 验证速率限制最大值
    if (!this.rateLimit.max || isNaN(this.rateLimit.max)) {
      errors.push('RATE_LIMIT_MAX 未设置或不是有效的数字');
      isValid = false;
    }

    // 验证登录速率限制窗口
    if (!this.rateLimit.loginWindowMs || isNaN(this.rateLimit.loginWindowMs)) {
      errors.push('RATE_LIMIT_LOGIN_WINDOW_MS 未设置或不是有效的数字');
      isValid = false;
    }

    // 验证登录速率限制最大值
    if (!this.rateLimit.loginMax || isNaN(this.rateLimit.loginMax)) {
      errors.push('RATE_LIMIT_LOGIN_MAX 未设置或不是有效的数字');
      isValid = false;
    }

    return isValid;
  }
};

module.exports = securityConfig;
