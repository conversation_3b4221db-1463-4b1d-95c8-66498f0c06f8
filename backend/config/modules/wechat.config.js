/**
 * 微信配置模块
 */
const { validateEnvVar } = require('../utils/env-utils');

const wechatConfig = {
  appId: process.env.WECHAT_APP_ID || '',
  appSecret: process.env.WECHAT_APP_SECRET || '',
  token: process.env.WECHAT_TOKEN || '',
  encodingAESKey: process.env.WECHAT_ENCODING_AES_KEY || '',

  /**
   * 验证配置
   * @param {Array} errors - 错误数组
   * @returns {boolean} 验证是否通过
   */
  validate(errors = []) {
    // 微信配置是可选的，如果没有设置，则跳过验证
    if (!this.appId && !this.appSecret) {
      return true;
    }

    let isValid = true;

    // 验证AppID
    if (!validateEnvVar('WECHAT_APP_ID', this.appId, errors)) {
      isValid = false;
    }

    // 验证AppSecret
    if (!validateEnvVar('WECHAT_APP_SECRET', this.appSecret, errors)) {
      isValid = false;
    }

    // 验证Token
    if (!validateEnvVar('WECHAT_TOKEN', this.token, errors)) {
      isValid = false;
    }

    // 验证EncodingAESKey
    if (!validateEnvVar('WECHAT_ENCODING_AES_KEY', this.encodingAESKey, errors)) {
      isValid = false;
    }

    return isValid;
  }
};

module.exports = wechatConfig;
