/**
 * 性能测试环境配置
 * 用于设置性能测试的环境参数
 */

const path = require('path');
const fs = require('fs');

// 默认配置
const defaultConfig = {
  // 服务器配置
  server: {
    port: process.env.PERF_TEST_PORT || 9094,
    host: process.env.PERF_TEST_HOST || 'localhost',
    protocol: process.env.PERF_TEST_PROTOCOL || 'http',
    apiPrefix: process.env.PERF_TEST_API_PREFIX || '/api/v2'
  },
  
  // 数据库配置 (使用性能测试数据库)
  database: {
    host: process.env.PERF_TEST_DB_HOST || 'localhost',
    port: process.env.PERF_TEST_DB_PORT || 3306,
    name: process.env.PERF_TEST_DB_NAME || 'aibubb_perf_test',
    user: process.env.PERF_TEST_DB_USER || 'root',
    password: process.env.PERF_TEST_DB_PASSWORD || 'secret',
    dialect: 'mysql',
    logging: false,
    // 性能优化配置
    pool: {
      max: 20,
      min: 5,
      acquire: 30000,
      idle: 10000
    }
  },
  
  // Redis配置
  redis: {
    url: process.env.PERF_TEST_REDIS_URL || 'redis://localhost:6379',
    password: process.env.PERF_TEST_REDIS_PASSWORD || '',
    enabled: process.env.PERF_TEST_REDIS_ENABLED === 'true',
    // 性能优化配置
    maxClients: 50
  },
  
  // 测试配置
  test: {
    // 测试报告输出目录
    reportDir: process.env.PERF_TEST_REPORT_DIR || path.resolve(__dirname, '../test-reports/performance-tests'),
    
    // 测试数据目录
    dataDir: process.env.PERF_TEST_DATA_DIR || path.resolve(__dirname, '../tests/performance/data'),
    
    // 测试用户凭据 (用于认证测试)
    testUser: {
      username: process.env.PERF_TEST_USER || '<EMAIL>',
      password: process.env.PERF_TEST_PASSWORD || 'perftest123'
    },
    
    // 测试场景
    scenarios: {
      // 负载测试配置
      load: {
        vus: parseInt(process.env.PERF_TEST_LOAD_VUS) || 50,
        duration: process.env.PERF_TEST_LOAD_DURATION || '5m',
        rampUp: process.env.PERF_TEST_LOAD_RAMP_UP || '1m',
        rampDown: process.env.PERF_TEST_LOAD_RAMP_DOWN || '1m'
      },
      
      // 压力测试配置
      stress: {
        vus: parseInt(process.env.PERF_TEST_STRESS_VUS) || 200,
        duration: process.env.PERF_TEST_STRESS_DURATION || '10m',
        rampUp: process.env.PERF_TEST_STRESS_RAMP_UP || '2m',
        rampDown: process.env.PERF_TEST_STRESS_RAMP_DOWN || '1m'
      },
      
      // 长稳测试配置
      soak: {
        vus: parseInt(process.env.PERF_TEST_SOAK_VUS) || 30,
        duration: process.env.PERF_TEST_SOAK_DURATION || '1h',
        rampUp: process.env.PERF_TEST_SOAK_RAMP_UP || '5m',
        rampDown: process.env.PERF_TEST_SOAK_RAMP_DOWN || '5m'
      },
      
      // 并发测试配置
      concurrency: {
        vus: parseInt(process.env.PERF_TEST_CONCURRENCY_VUS) || 100,
        duration: process.env.PERF_TEST_CONCURRENCY_DURATION || '5m',
        rampUp: process.env.PERF_TEST_CONCURRENCY_RAMP_UP || '30s',
        rampDown: process.env.PERF_TEST_CONCURRENCY_RAMP_DOWN || '30s'
      }
    },
    
    // 性能阈值
    thresholds: {
      // 响应时间阈值 (毫秒)
      responseTime: {
        p95: parseInt(process.env.PERF_TEST_THRESHOLD_RESPONSE_P95) || 500,
        p99: parseInt(process.env.PERF_TEST_THRESHOLD_RESPONSE_P99) || 1000
      },
      
      // 错误率阈值 (百分比)
      errorRate: parseFloat(process.env.PERF_TEST_THRESHOLD_ERROR_RATE) || 1.0,
      
      // 吞吐量阈值 (每秒请求数)
      throughput: parseInt(process.env.PERF_TEST_THRESHOLD_THROUGHPUT) || 100
    }
  },
  
  // k6配置
  k6: {
    // k6可执行文件路径
    binary: process.env.K6_BINARY || 'k6',
    
    // 输出格式
    outputFormat: process.env.K6_OUTPUT_FORMAT || 'json',
    
    // 是否显示进度条
    showProgress: process.env.K6_SHOW_PROGRESS !== 'false',
    
    // 是否显示摘要
    showSummary: process.env.K6_SHOW_SUMMARY !== 'false',
    
    // 是否启用HTTP调试
    httpDebug: process.env.K6_HTTP_DEBUG === 'true',
    
    // 是否禁用保持活动连接
    noConnectionReuse: process.env.K6_NO_CONNECTION_REUSE === 'true',
    
    // 是否启用批处理
    batch: process.env.K6_BATCH === 'true',
    
    // 用户代理
    userAgent: process.env.K6_USER_AGENT || 'k6/1.0.0 (AIBUBB Performance Test)'
  }
};

// 加载环境特定配置
let envConfig = {};
const envConfigPath = path.resolve(__dirname, `performance-test-${process.env.NODE_ENV}.js`);

if (fs.existsSync(envConfigPath)) {
  try {
    envConfig = require(envConfigPath);
    console.log(`已加载环境特定配置: ${envConfigPath}`);
  } catch (error) {
    console.error(`加载环境特定配置失败: ${error.message}`);
  }
}

// 合并配置
const config = {
  ...defaultConfig,
  ...envConfig
};

// 创建测试报告目录
if (!fs.existsSync(config.test.reportDir)) {
  try {
    fs.mkdirSync(config.test.reportDir, { recursive: true });
    console.log(`已创建测试报告目录: ${config.test.reportDir}`);
  } catch (error) {
    console.error(`创建测试报告目录失败: ${error.message}`);
  }
}

// 创建测试数据目录
if (!fs.existsSync(config.test.dataDir)) {
  try {
    fs.mkdirSync(config.test.dataDir, { recursive: true });
    console.log(`已创建测试数据目录: ${config.test.dataDir}`);
  } catch (error) {
    console.error(`创建测试数据目录失败: ${error.message}`);
  }
}

module.exports = config;
