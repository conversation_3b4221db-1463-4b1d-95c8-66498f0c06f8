/**
 * 服务容器
 * 管理应用中的服务和仓库实例
 */

// 导入仓库
const LearningPlanRepository = require('../repositories/learningPlan.repository');
const TagRepository = require('../repositories/tag.repository');
const NoteRepository = require('../repositories/note.repository');
const ExerciseRepository = require('../repositories/exercise.repository');
const InsightRepository = require('../repositories/insight.repository');
const SquareRepository = require('../repositories/square.repository');
const UserRepository = require('../repositories/user.repository');
const StatisticsRepository = require('../repositories/statistics.repository');
const ThemeRepository = require('../repositories/theme.repository');
const DailyContentRepository = require('../repositories/dailyContent.repository');
const UserSettingRepository = require('../repositories/userSetting.repository');
const TagCategoryRepository = require('../repositories/tagCategory.repository');
const TagSynonymRepository = require('../repositories/tagSynonym.repository');
const TagFeedbackRepository = require('../repositories/tagFeedback.repository');

// 导入安全工具
const safeObject = require('../utils/safe-object');

// 导入服务
const AIService = require('../services/ai.service');
const LearningPlanService = require('../services/learningPlanV2.service');
const TagService = require('../services/tag.service');
const NoteService = require('../services/note.service');
const ExerciseService = require('../services/exercise.service');
const InsightService = require('../services/insight.service');
const SquareService = require('../services/square.service');
const UserService = require('../services/user.service');
const StatisticsService = require('../services/statistics.service');
const ThemeService = require('../services/theme.service');
const DailyContentService = require('../services/dailyContent.service');
const CleanupService = require('../services/cleanup.service');

// 创建仓库实例
const repositories = {
  learningPlanRepository: new LearningPlanRepository(),
  tagRepository: new TagRepository(),
  noteRepository: new NoteRepository(),
  exerciseRepository: new ExerciseRepository(),
  insightRepository: new InsightRepository(),
  squareRepository: new SquareRepository(),
  userRepository: new UserRepository(),
  statisticsRepository: new StatisticsRepository(),
  themeRepository: new ThemeRepository(),
  dailyContentRepository: new DailyContentRepository(),
  userSettingRepository: new UserSettingRepository(),
  tagCategoryRepository: new TagCategoryRepository(),
  tagSynonymRepository: new TagSynonymRepository(),
  tagFeedbackRepository: new TagFeedbackRepository()
};

// 存储服务类 (使用小驼峰命名作为键)
const serviceClasses = {
  aiService: AIService,
  learningPlanService: LearningPlanService,
  tagService: TagService,
  noteService: NoteService,
  exerciseService: ExerciseService,
  insightService: InsightService,
  squareService: SquareService,
  userService: UserService,
  statisticsService: StatisticsService,
  themeService: ThemeService,
  dailyContentService: DailyContentService,
  cleanupService: CleanupService
};

// 存储已实例化的服务
const instantiatedServices = {};

// 服务容器
const serviceContainer = {
  // 获取仓库实例
  getRepository: (name) => {
    // 使用安全的对象访问
    if (!Object.prototype.hasOwnProperty.call(repositories, name)) {
      throw new Error(`仓库 ${name} 不存在`);
    }
    return safeObject.safeGet(repositories, name);
  },

  // 获取所有仓库实例
  getAllRepositories: () => {
    return repositories;
  },

  // 获取服务实例 (实现延迟实例化和依赖注入)
  getService: (name) => {
    // 如果已实例化，直接返回
    if (Object.prototype.hasOwnProperty.call(instantiatedServices, name)) {
      return safeObject.safeGet(instantiatedServices, name);
    }

    // 检查服务类是否存在
    const ServiceClass = safeObject.safeGet(serviceClasses, name);
    if (!ServiceClass) {
      throw new Error(`服务 ${name} 不存在`);
    }

    // 实例化服务
    let instance;
    // 特殊处理需要注入依赖的服务
    if (name === 'statisticsService') {
      instance = new ServiceClass(serviceContainer.getRepository('statisticsRepository'));
    } else {
      // 其他服务（假设构造函数不需要参数，或者其内部通过 serviceContainer 获取依赖）
      instance = new ServiceClass();
    }

    // 缓存实例
    safeObject.safeSet(instantiatedServices, name, instance);
    return instance;
  },

  // 注册仓库实例
  registerRepository: (name, instance) => {
    // 验证名称格式
    if (typeof name !== 'string' || !name.match(/^[a-zA-Z0-9_]+$/)) {
      throw new Error(`仓库名称格式不正确: ${name}`);
    }
    safeObject.safeSet(repositories, name, instance);
  },

  // 注册服务实例
  registerService: (name, instance) => {
    // 验证名称格式
    if (typeof name !== 'string' || !name.match(/^[a-zA-Z0-9_]+$/)) {
      throw new Error(`服务名称格式不正确: ${name}`);
    }
    safeObject.safeSet(instantiatedServices, name, instance);
  }
};

module.exports = serviceContainer;
