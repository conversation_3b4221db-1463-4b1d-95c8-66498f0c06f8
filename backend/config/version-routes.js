/**
 * 版本路由配置
 * 集中管理所有版本的路由
 */
const config = require('./config');

module.exports = {
  // 版本配置
  config: {
    defaultVersion: 'v2',
    versions: ['v2'],
    deprecatedVersions: [],
    versionExtractor: 'url',
    headerName: 'accept-version',
    queryParam: 'version',
    enableCompatibilityLayer: false
  },

  // 路由配置
  routes: [
    {
      path: '/auth',
      versions: {
        v2: require('../routes/v2/auth.routes') // 使用增强版认证路由
      }
    },
    {
      path: '/users',
      versions: {
        v2: require('../routes/v2/user.routes') // 使用增强版用户路由
      }
    },
    {
      path: '/tags',
      versions: {
        v2: require('../routes/tagV2.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/insightV2.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/exerciseV2.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/noteV2.routes')
      }
    },
    {
      path: '/themes',
      versions: {
        v2: require('../routes/themeV2.routes')
      }
    },
    {
      path: '/learning-plans',
      versions: {
        v2: require('../routes/learningPlanV2.routes')
      }
    },
    {
      path: '/daily-contents',
      versions: {
        v2: require('../routes/dailyContentV2.routes')
      }
    },
    {
      path: '/cleanup',
      versions: {
        v2: require('../routes/cleanup.routes')
      }
    },
    {
      path: '/batch',
      versions: {
        v2: require('../routes/batchOperation.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/statisticsV2.routes')
      }
    },
    {
      path: '/statistics/monitor',
      versions: {
        v2: require('../routes/statisticsMonitor.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/v2/bubble.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/square.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/systemTag.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/like.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/comment.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/follow.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/share.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/tagCategory.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/tagSynonym.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/tagFeedback.routes')
      }
    },
    {
      path: '/ai',
      versions: {
        v2: require('../routes/ai.routes')
      }
    },
    {
      path: '',
      versions: {
        v2: require('../routes/testData.routes')
      }
    },
    {
      path: '/dead-letter-queue',
      versions: {
        v2: require('../routes/deadLetterQueue.routes')
      }
    },

    // 遥测演示路由
    {
      path: '/telemetry-demo',
      versions: {
        v2: require('../routes/telemetry-demo.routes')
      }
    }
  ]
};
