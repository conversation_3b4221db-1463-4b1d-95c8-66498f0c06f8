/**
 * 统一DI容器入口
 * 使用统一的DI容器，确保所有模块共享相同的服务实例
 */

import { container } from '../infrastructure/di';
import { configureLearningTemplateContainer } from './learningTemplate.container';
import { configureGamificationContainer } from './gamification.container';

// 配置各个领域的依赖注入
// 注意：基础设施（如UnitOfWork和EventPublisher）已在统一容器中注册
configureLearningTemplateContainer(container);
configureGamificationContainer(container);

export { container };
