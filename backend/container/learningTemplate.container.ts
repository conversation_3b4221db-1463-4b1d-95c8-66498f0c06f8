import { Container } from '../infrastructure/config/Container';
import { LearningTemplateRepository } from '../domain/repositories/learningTemplate/LearningTemplateRepository';
import { TemplateTagRepository } from '../domain/repositories/learningTemplate/TemplateTagRepository';
import { SequelizeLearningTemplateRepository } from '../infrastructure/persistence/repositories/learningTemplate/SequelizeLearningTemplateRepository';
import { SequelizeTemplateTagRepository } from '../infrastructure/persistence/repositories/learningTemplate/SequelizeTemplateTagRepository';
import { LearningTemplateApplicationService } from '../application/services/learningTemplate/LearningTemplateApplicationService';
import { LearningTemplateController } from '../controllers/v2/learningTemplate.controller';
import { sequelize } from '../config/database';
import { models } from '../models';

/**
 * 配置学习模板领域的依赖注入
 * @param container 依赖注入容器
 */
export function configureLearningTemplateContainer(container: Container): void {
  // 注册仓库
  container.bind<LearningTemplateRepository>('learningTemplateRepository', (c) => {
    return new SequelizeLearningTemplateRepository(
      c.get('unitOfWork'),
      c.get('eventPublisher'),
      sequelize,
      models.LearningTemplate,
      models.TemplateTag
    );
  }, { singleton: true });

  // 注册模板标签关联仓库
  container.bind<TemplateTagRepository>('templateTagRepository', (c) => {
    return new SequelizeTemplateTagRepository(
      c.get('unitOfWork'),
      c.get('eventPublisher'),
      sequelize,
      models.TemplateTag
    );
  }, { singleton: true });

  // 注册应用服务
  container.bind<LearningTemplateApplicationService>('learningTemplateApplicationService', (c) => {
    return new LearningTemplateApplicationService(
      c.get('unitOfWork'),
      c.get('learningTemplateRepository'),
      c.get('themeRepository'),
      c.get('templateTagRepository'),
      c.get('tagRepository')
    );
  }, { singleton: true });

  // 注册控制器
  container.bind<LearningTemplateController>('learningTemplateController', (c) => {
    return new LearningTemplateController(
      c.get('learningTemplateApplicationService')
    );
  }, { singleton: true });
}
