const aiService = require('../services/ai.service');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const {
  handleApiError,
  handleBadRequestError,
  handleAIServiceError
} = require('../utils/errorHandler');

/**
 * 获取AI使用统计信息
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getAIStats = async (req, res) => {
  try {
    const stats = aiService.getUsageStats();

    // 计算估计成本（根据实际计费方式调整）
    const estimatedCost = calculateEstimatedCost(stats.tokenUsage);

    return apiResponse.success(res, {
      provider: aiService.provider,
      model: aiService.model,
      stats: {
        ...stats,
        estimatedCost
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'getAIStats');
  }
};

/**
 * 重置AI使用统计信息
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const resetAIStats = async (req, res) => {
  try {
    aiService.resetUsageStats();
    return apiResponse.success(res, null, 'AI统计信息已重置');
  } catch (error) {
    return handleApiError(error, res, 'resetAIStats');
  }
};

/**
 * 测试AI标签生成功能
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const testTagGeneration = async (req, res) => {
  try {
    const { title, description, themeId } = req.body;

    if (!title) {
      return handleBadRequestError(res, '学习计划标题不能为空');
    }

    // 创建模拟学习计划对象
    const mockPlan = {
      title,
      description: description || '',
      theme: {
        id: themeId || 1,
        name: '人际沟通'
      }
    };

    // 生成标签
    const tags = await aiService.generateTags(mockPlan);

    return apiResponse.success(res, {
      plan: mockPlan,
      tags,
      stats: aiService.getUsageStats()
    });
  } catch (error) {
    // 检查是否是AI服务特定错误
    if (error.code && ['RATE_LIMIT_EXCEEDED', 'INVALID_API_KEY', 'SERVICE_UNAVAILABLE', 'REQUEST_TIMEOUT', 'CONNECTION_REFUSED', 'AI_SERVICE_ERROR'].includes(error.code)) {
      return handleAIServiceError(error, res, 'testTagGeneration');
    }
    return handleApiError(error, res, 'testTagGeneration');
  }
};

/**
 * 生成学习计划内容
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const generateLearningPlanContent = async (req, res) => {
  try {
    const { title, trouble, learningIntensity, learningDuration } = req.body;

    if (!title) {
      return handleBadRequestError(res, '学习计划标题不能为空');
    }

    // 准备AI请求数据
    const planData = {
      title,
      trouble: trouble || '',
      learningIntensity: learningIntensity || 'medium',
      learningDuration: parseInt(learningDuration) || 7
    };

    // 生成学习计划内容
    const planContent = await aiService.generateLearningPlanContent(planData);

    return apiResponse.success(res, {
      planContent,
      stats: aiService.getUsageStats()
    });
  } catch (error) {
    // 检查是否是AI服务特定错误
    if (error.code && ['RATE_LIMIT_EXCEEDED', 'INVALID_API_KEY', 'SERVICE_UNAVAILABLE', 'REQUEST_TIMEOUT', 'CONNECTION_REFUSED', 'AI_SERVICE_ERROR'].includes(error.code)) {
      return handleAIServiceError(error, res, 'generateLearningPlanContent');
    }
    return handleApiError(error, res, 'generateLearningPlanContent');
  }
};

/**
 * 计算估计成本
 * @private
 * @param {Object} tokenUsage - token使用情况
 * @returns {Object} 估计成本
 */
const calculateEstimatedCost = (tokenUsage) => {
  // DeepSeek的计费标准（根据实际情况调整）
  const ratePerThousandPromptTokens = 0.002; // 美元/千tokens
  const ratePerThousandCompletionTokens = 0.002; // 美元/千tokens

  // 计算成本（美元）
  const promptCost = (tokenUsage.prompt / 1000) * ratePerThousandPromptTokens;
  const completionCost = (tokenUsage.completion / 1000) * ratePerThousandCompletionTokens;
  const totalCost = promptCost + completionCost;

  return {
    promptCost: parseFloat(promptCost.toFixed(6)),
    completionCost: parseFloat(completionCost.toFixed(6)),
    totalCost: parseFloat(totalCost.toFixed(6)),
    currency: 'USD'
  };
};

module.exports = {
  getAIStats,
  resetAIStats,
  testTagGeneration,
  generateLearningPlanContent
};
