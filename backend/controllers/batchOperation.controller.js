/**
 * 批量操作控制器
 * 处理批量软删除和批量恢复操作
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const serviceContainer = require('../config/serviceContainer');
const {
  handleApiError,
  handleNotFoundError,
  handleBadRequestError,
  createError
} = require('../utils/errorHandler');

/**
 * 批量软删除标签
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchSoftDeleteTags = async (req, res) => {
  try {
    const { ids, cascade = false } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取标签仓库
    const tagRepository = serviceContainer.getRepository('tagRepository');

    // 执行批量软删除
    let result;
    if (cascade) {
      result = await tagRepository.batchCascadeSoftDelete(ids);
    } else {
      result = await tagRepository.batchSoftDeleteTags(ids);
    }

    return apiResponse.success(res, {
      message: `已成功软删除 ${result} 个标签`,
      count: result
    });
  } catch (error) {
    logger.error(`批量软删除标签失败: ${error.message}`);
    return handleApiError(error, res, 'batchSoftDeleteTags');
  }
};

/**
 * 批量恢复标签
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchRestoreTags = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取标签仓库
    const tagRepository = serviceContainer.getRepository('tagRepository');

    // 执行批量恢复
    const result = await tagRepository.batchRestoreTags(ids);

    return apiResponse.success(res, {
      message: `已成功恢复 ${result} 个标签`,
      count: result
    });
  } catch (error) {
    logger.error(`批量恢复标签失败: ${error.message}`);
    return handleApiError(error, res, 'batchRestoreTags');
  }
};

/**
 * 批量软删除用户
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchSoftDeleteUsers = async (req, res) => {
  try {
    const { ids, cascade = false } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取用户仓库
    const userRepository = serviceContainer.getRepository('userRepository');

    // 执行批量软删除
    let result;
    if (cascade) {
      result = await userRepository.batchCascadeSoftDelete(ids);
    } else {
      result = await userRepository.batchSoftDeleteUsers(ids);
    }

    return apiResponse.success(res, {
      message: `已成功软删除 ${result} 个用户`,
      count: result
    });
  } catch (error) {
    logger.error(`批量软删除用户失败: ${error.message}`);
    return handleApiError(error, res, 'batchSoftDeleteUsers');
  }
};

/**
 * 批量恢复用户
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchRestoreUsers = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取用户仓库
    const userRepository = serviceContainer.getRepository('userRepository');

    // 执行批量恢复
    const result = await userRepository.batchRestoreUsers(ids);

    return apiResponse.success(res, {
      message: `已成功恢复 ${result} 个用户`,
      count: result
    });
  } catch (error) {
    logger.error(`批量恢复用户失败: ${error.message}`);
    return handleApiError(error, res, 'batchRestoreUsers');
  }
};

/**
 * 批量软删除笔记
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchSoftDeleteNotes = async (req, res) => {
  try {
    const { ids } = req.body;
    const userId = req.user.id;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取笔记服务
    const noteService = serviceContainer.getService('noteService');

    // 执行批量软删除
    const result = await noteService.batchSoftDeleteNotes(ids, userId);

    return apiResponse.success(res, {
      message: `已成功软删除 ${result.count} 个笔记`,
      count: result.count,
      failedIds: result.failedIds
    });
  } catch (error) {
    logger.error(`批量软删除笔记失败: ${error.message}`);
    return handleApiError(error, res, 'batchSoftDeleteNotes');
  }
};

/**
 * 批量恢复笔记
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchRestoreNotes = async (req, res) => {
  try {
    const { ids } = req.body;
    const userId = req.user.id;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取笔记服务
    const noteService = serviceContainer.getService('noteService');

    // 执行批量恢复
    const result = await noteService.batchRestoreNotes(ids, userId);

    return apiResponse.success(res, {
      message: `已成功恢复 ${result.count} 个笔记`,
      count: result.count,
      failedIds: result.failedIds
    });
  } catch (error) {
    logger.error(`批量恢复笔记失败: ${error.message}`);
    return handleApiError(error, res, 'batchRestoreNotes');
  }
};

/**
 * 批量软删除观点
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchSoftDeleteInsights = async (req, res) => {
  try {
    const { ids } = req.body;
    const userId = req.user.id;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取观点服务
    const insightService = serviceContainer.getService('insightService');

    // 执行批量软删除
    const result = await insightService.batchSoftDeleteInsights(ids, userId);

    return apiResponse.success(res, {
      message: `已成功软删除 ${result.count} 个观点`,
      count: result.count,
      failedIds: result.failedIds
    });
  } catch (error) {
    logger.error(`批量软删除观点失败: ${error.message}`);
    return handleApiError(error, res, 'batchSoftDeleteInsights');
  }
};

/**
 * 批量恢复观点
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const batchRestoreInsights = async (req, res) => {
  try {
    const { ids } = req.body;
    const userId = req.user.id;

    if (!Array.isArray(ids) || ids.length === 0) {
      return handleBadRequestError(res, 'ID数组不能为空');
    }

    // 获取观点服务
    const insightService = serviceContainer.getService('insightService');

    // 执行批量恢复
    const result = await insightService.batchRestoreInsights(ids, userId);

    return apiResponse.success(res, {
      message: `已成功恢复 ${result.count} 个观点`,
      count: result.count,
      failedIds: result.failedIds
    });
  } catch (error) {
    logger.error(`批量恢复观点失败: ${error.message}`);
    return handleApiError(error, res, 'batchRestoreInsights');
  }
};

module.exports = {
  batchSoftDeleteTags,
  batchRestoreTags,
  batchSoftDeleteUsers,
  batchRestoreUsers,
  batchSoftDeleteNotes,
  batchRestoreNotes,
  batchSoftDeleteInsights,
  batchRestoreInsights
};
