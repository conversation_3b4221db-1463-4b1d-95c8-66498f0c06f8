/**
 * 每日内容控制器 V2
 * 支持软删除功能
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const serviceContainer = require('../config/serviceContainer');
const {
  handleApiError,
  handleNotFoundError
} = require('../utils/errorHandler');

// 获取每日内容服务
const dailyContentService = serviceContainer.getService('dailyContentService');

/**
 * 获取学习计划的每日内容列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getPlanContents = async (req, res) => {
  try {
    const { planId } = req.params;
    const userId = req.user.id;

    // 使用服务层获取每日内容列表
    const contents = await dailyContentService.getPlanContents(planId, userId);

    return apiResponse.success(res, { contents });
  } catch (error) {
    if (error.code === 'PLAN_NOT_FOUND') {
      return handleNotFoundError(res, '学习计划不存在或不属于当前用户');
    }
    return handleApiError(error, res, 'getPlanContents');
  }
};

/**
 * 获取单个每日内容详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getContentById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 使用服务层获取每日内容详情
    const content = await dailyContentService.getContentDetails(id, userId);

    return apiResponse.success(res, content);
  } catch (error) {
    if (error.code === 'CONTENT_NOT_FOUND') {
      return handleNotFoundError(res, '每日内容不存在或不属于当前用户');
    }
    return handleApiError(error, res, 'getContentById');
  }
};

/**
 * 更新每日内容
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateContent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;

    // 使用服务层更新每日内容
    const updatedContent = await dailyContentService.updateContent(id, userId, updateData);

    return apiResponse.success(res, updatedContent);
  } catch (error) {
    if (error.code === 'CONTENT_NOT_FOUND') {
      return handleNotFoundError(res, '每日内容不存在或不属于当前用户');
    }
    return handleApiError(error, res, 'updateContent');
  }
};

/**
 * 软删除每日内容
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const softDeleteContent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 使用服务层软删除每日内容
    await dailyContentService.softDeleteContent(id, userId);

    return apiResponse.success(res, {
      message: '每日内容已被软删除'
    });
  } catch (error) {
    logger.error(`软删除每日内容失败: ${error.message}`);

    if (error.code === 'CONTENT_NOT_FOUND') {
      return handleNotFoundError(res, '每日内容不存在或不属于当前用户');
    }

    return handleApiError(error, res, 'softDeleteContent');
  }
};

/**
 * 恢复已软删除的每日内容
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const restoreContent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 使用服务层恢复每日内容
    await dailyContentService.restoreContent(id, userId);

    return apiResponse.success(res, {
      message: '每日内容已恢复'
    });
  } catch (error) {
    logger.error(`恢复每日内容失败: ${error.message}`);

    if (error.code === 'CONTENT_NOT_FOUND') {
      return handleNotFoundError(res, '每日内容不存在或不属于当前用户');
    }

    if (error.code === 'CONTENT_NOT_DELETED') {
      return apiResponse.badRequest(res, '每日内容未被删除，无需恢复');
    }

    return handleApiError(error, res, 'restoreContent');
  }
};

/**
 * 获取已删除的每日内容列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getDeletedContents = async (req, res) => {
  try {
    const { planId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;
    const userId = req.user.id;

    // 使用服务层获取已删除的每日内容列表
    const result = await dailyContentService.getDeletedContents(planId, userId, page, pageSize);

    return apiResponse.success(res, result);
  } catch (error) {
    if (error.code === 'PLAN_NOT_FOUND') {
      return handleNotFoundError(res, '学习计划不存在或不属于当前用户');
    }
    return handleApiError(error, res, 'getDeletedContents');
  }
};

module.exports = {
  getPlanContents,
  getContentById,
  updateContent,
  softDeleteContent,
  restoreContent,
  getDeletedContents
};
