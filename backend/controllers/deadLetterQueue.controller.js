/**
 * 死信队列管理控制器
 * 提供死信队列的查询、重试和解决功能
 */
const DeadLetterQueue = require('../models/DeadLetterQueue');
const container = require('../infrastructure/di/container').default;
const logger = require('../config/logger');

/**
 * 获取死信队列列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeadLetterQueue = async (req, res) => {
  try {
    const { status, page = 1, limit = 10, eventType } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const where = {};
    if (status) {
      where.status = status;
    }
    if (eventType) {
      where.eventType = eventType;
    }
    
    // 查询死信队列
    const { count, rows } = await DeadLetterQueue.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });
    
    // 返回结果
    res.json({
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit),
      data: rows
    });
  } catch (error) {
    logger.error(`获取死信队列失败: ${error.message}`, {
      error: error.stack
    });
    res.status(500).json({ message: '获取死信队列失败', error: error.message });
  }
};

/**
 * 获取死信队列项详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeadLetterQueueItem = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询死信队列项
    const item = await DeadLetterQueue.findByPk(id);
    
    if (!item) {
      return res.status(404).json({ message: '死信队列项不存在' });
    }
    
    // 返回结果
    res.json(item);
  } catch (error) {
    logger.error(`获取死信队列项详情失败: ${error.message}`, {
      error: error.stack,
      id: req.params.id
    });
    res.status(500).json({ message: '获取死信队列项详情失败', error: error.message });
  }
};

/**
 * 重试死信队列项
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.retryDeadLetterQueueItem = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询死信队列项
    const item = await DeadLetterQueue.findByPk(id);
    
    if (!item) {
      return res.status(404).json({ message: '死信队列项不存在' });
    }
    
    // 检查状态
    if (item.status === 'resolved') {
      return res.status(400).json({ message: '该项已解决，无需重试' });
    }
    
    // 获取死信队列服务
    const deadLetterQueueService = container.get('deadLetterQueueService');
    
    // 重试事件
    await deadLetterQueueService.retryEvent(item);
    
    // 返回结果
    res.json({ message: '重试成功', item: await DeadLetterQueue.findByPk(id) });
  } catch (error) {
    logger.error(`重试死信队列项失败: ${error.message}`, {
      error: error.stack,
      id: req.params.id
    });
    res.status(500).json({ message: '重试死信队列项失败', error: error.message });
  }
};

/**
 * 解决死信队列项（标记为已解决）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.resolveDeadLetterQueueItem = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询死信队列项
    const item = await DeadLetterQueue.findByPk(id);
    
    if (!item) {
      return res.status(404).json({ message: '死信队列项不存在' });
    }
    
    // 检查状态
    if (item.status === 'resolved') {
      return res.status(400).json({ message: '该项已解决' });
    }
    
    // 更新状态
    await item.update({
      status: 'resolved',
      resolvedAt: new Date()
    });
    
    // 返回结果
    res.json({ message: '解决成功', item });
  } catch (error) {
    logger.error(`解决死信队列项失败: ${error.message}`, {
      error: error.stack,
      id: req.params.id
    });
    res.status(500).json({ message: '解决死信队列项失败', error: error.message });
  }
};
