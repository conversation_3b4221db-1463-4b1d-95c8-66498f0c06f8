/**
 * 练习控制器 V2
 * 使用服务层处理业务逻辑
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const exerciseService = require('../services/exercise.service');

/**
 * 获取标签下的练习列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getExercisesByTagId = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId } = req.params;
    const { difficulty, page = 1, pageSize = 10 } = req.query;

    // 构建过滤条件
    const filters = {};
    if (difficulty) {
      filters.difficulty = difficulty;
    }

    // 使用服务层获取练习列表
    const result = await exerciseService.getExercisesByTagId(tagId, userId, filters, page, pageSize);

    // 格式化响应数据
    const exercises = result.rows.map(exercise => ({
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      createdAt: exercise.created_at
    }));

    return apiResponse.success(res, {
      exercises,
      pagination: {
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(result.count / parseInt(pageSize))
      }
    });
  } catch (error) {
    logger.error(`获取练习列表失败: ${error.message}`);
    return apiResponse.error(res, '获取练习列表失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 获取练习详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getExerciseById = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层获取练习详情
    const exercise = await exerciseService.getExerciseDetails(id, userId);

    return apiResponse.success(res, {
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      tagId: exercise.tag_id,
      tagName: exercise.tag ? exercise.tag.name : null,
      planId: exercise.tag && exercise.tag.learningPlan ? exercise.tag.learningPlan.id : null,
      planTitle: exercise.tag && exercise.tag.learningPlan ? exercise.tag.learningPlan.title : null,
      createdAt: exercise.created_at,
      updatedAt: exercise.updated_at
    });
  } catch (error) {
    logger.error(`获取练习详情失败: ${error.message}`);
    return apiResponse.error(res, '获取练习详情失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 创建练习
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const createExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId, title, description, expectedResult, difficulty, timeEstimate } = req.body;

    // 使用服务层创建练习
    const exerciseData = {
      tagId,
      title,
      description,
      expectedResult,
      difficulty,
      timeEstimate
    };

    const exercise = await exerciseService.createExercise(userId, exerciseData);

    return apiResponse.created(res, {
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      tagId: exercise.tag_id,
      createdAt: exercise.created_at
    });
  } catch (error) {
    logger.error(`创建练习失败: ${error.message}`);
    return apiResponse.error(res, '创建练习失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 更新练习
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;
    const { title, description, expectedResult, difficulty, timeEstimate } = req.body;

    // 使用服务层更新练习
    const updateData = {
      title,
      description,
      expectedResult,
      difficulty,
      timeEstimate
    };

    const exercise = await exerciseService.updateExercise(id, userId, updateData);

    return apiResponse.success(res, {
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      tagId: exercise.tag_id,
      updatedAt: exercise.updated_at
    }, '练习已更新');
  } catch (error) {
    logger.error(`更新练习失败: ${error.message}`);
    return apiResponse.error(res, '更新练习失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 删除练习
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层删除练习
    const success = await exerciseService.deleteExercise(id, userId);

    if (!success) {
      return apiResponse.notFound(res, '练习不存在或无权删除');
    }

    return apiResponse.success(res, null, '练习已删除');
  } catch (error) {
    logger.error(`删除练习失败: ${error.message}`);
    return apiResponse.error(res, '删除练习失败', 'SERVER_ERROR', 500);
  }
};

/**
 * @swagger
 * /api/v2/exercises/{id}/soft-delete:
 *   delete:
 *     summary: 软删除练习
 *     description: 软删除指定练习（练习仍然存在，但在大多数查询中不可见）
 *     tags: [Exercises]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 练习ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: 练习已被软删除
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const softDeleteExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层软删除练习
    await exerciseService.softDeleteExercise(id, userId);

    return apiResponse.success(res, {
      message: '练习已被软删除'
    });
  } catch (error) {
    logger.error(`软删除练习失败: ${error.message}`);

    if (error.message.includes('不存在或不属于当前用户')) {
      return apiResponse.notFound(res, '练习不存在或不属于当前用户');
    }

    return apiResponse.error(res, '软删除练习失败', 'SERVER_ERROR', 500);
  }
};

/**
 * @swagger
 * /api/v2/exercises/{id}/restore:
 *   post:
 *     summary: 恢复已软删除的练习
 *     description: 恢复之前软删除的练习
 *     tags: [Exercises]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 练习ID
 *     responses:
 *       200:
 *         description: 恢复成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: 练习已恢复
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const restoreExercise = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层恢复练习
    await exerciseService.restoreExercise(id, userId);

    return apiResponse.success(res, {
      message: '练习已恢复'
    });
  } catch (error) {
    logger.error(`恢复练习失败: ${error.message}`);

    if (error.message.includes('不存在或不属于当前用户')) {
      return apiResponse.notFound(res, '练习不存在或不属于当前用户');
    }

    if (error.message.includes('未被删除')) {
      return apiResponse.badRequest(res, '练习未被删除，无需恢复');
    }

    return apiResponse.error(res, '恢复练习失败', 'SERVER_ERROR', 500);
  }
};

/**
 * @swagger
 * /api/v2/tags/{tagId}/exercises/deleted:
 *   get:
 *     summary: 获取已删除的练习列表
 *     description: 获取指定标签下已软删除的练习列表
 *     tags: [Exercises]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取已删除的练习列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     exercises:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           title:
 *                             type: string
 *                           description:
 *                             type: string
 *                           difficulty:
 *                             type: string
 *                           deletedAt:
 *                             type: string
 *                             format: date-time
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         pageSize:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const getDeletedExercises = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    // 使用服务层获取已删除的练习列表
    const result = await exerciseService.getDeletedExercises(tagId, userId, page, pageSize);

    // 格式化响应数据
    const exercises = result.rows.map(exercise => ({
      id: exercise.id,
      title: exercise.title,
      description: exercise.description,
      expectedResult: exercise.expected_result,
      difficulty: exercise.difficulty,
      timeEstimate: exercise.time_estimate,
      deletedAt: exercise.deleted_at,
      createdAt: exercise.created_at
    }));

    return apiResponse.success(res, {
      exercises,
      pagination: {
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(result.count / parseInt(pageSize))
      }
    });
  } catch (error) {
    logger.error(`获取已删除的练习列表失败: ${error.message}`);

    if (error.message.includes('标签不存在或不属于当前用户')) {
      return apiResponse.notFound(res, '标签不存在或不属于当前用户');
    }

    return apiResponse.error(res, '获取已删除的练习列表失败', 'SERVER_ERROR', 500);
  }
};

module.exports = {
  getExercisesByTagId,
  getExerciseById,
  createExercise,
  updateExercise,
  deleteExercise,
  softDeleteExercise,
  restoreExercise,
  getDeletedExercises
};
