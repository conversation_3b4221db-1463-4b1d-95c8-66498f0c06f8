import { Request, Response } from 'express';
import { AchievementApplicationService } from '../../application/services/gamification/AchievementApplicationService';
import { CreateAchievementCommand } from '../../application/commands/gamification/CreateAchievementCommand';
import { UpdateAchievementCommand } from '../../application/commands/gamification/UpdateAchievementCommand';
import { AwardAchievementCommand } from '../../application/commands/gamification/AwardAchievementCommand';
import { GetAchievementQuery } from '../../application/queries/gamification/GetAchievementQuery';
import { ListAchievementsQuery } from '../../application/queries/gamification/ListAchievementsQuery';
import { GetUserAchievementsQuery } from '../../application/queries/gamification/GetUserAchievementsQuery';
import { validateRequest } from '../../middlewares/validation.middleware';
import { errorHandler } from '../../utils/errorHandler';

/**
 * 成就控制器
 * 处理成就相关的HTTP请求
 */
export class AchievementV2Controller {
  /**
   * 构造函数
   * @param achievementApplicationService 成就应用服务
   */
  constructor(
    private readonly achievementApplicationService: AchievementApplicationService
  ) {}

  /**
   * 创建成就
   * @param req 请求对象
   * @param res 响应对象
   */
  async createAchievement(req: Request, res: Response): Promise<void> {
    try {
      // 验证请求
      validateRequest(req);

      // 构造命令
      const command: CreateAchievementCommand = {
        name: req.body.name,
        description: req.body.description,
        icon: req.body.icon,
        category: req.body.category,
        difficulty: req.body.difficulty,
        points: req.body.points,
        criteria: req.body.criteria,
        isHidden: req.body.isHidden ?? false,
        isActive: req.body.isActive ?? true,
        badgeId: req.body.badgeId
      };

      // 执行用例
      const achievement = await this.achievementApplicationService.createAchievement(command);

      // 返回响应
      res.status(201).json({
        success: true,
        message: '成就创建成功',
        data: achievement
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }

  /**
   * 更新成就
   * @param req 请求对象
   * @param res 响应对象
   */
  async updateAchievement(req: Request, res: Response): Promise<void> {
    try {
      // 验证请求
      validateRequest(req);

      // 获取成就ID
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '无效的成就ID'
          }
        });
        return;
      }

      // 构造命令
      const command: UpdateAchievementCommand = {
        id,
        name: req.body.name,
        description: req.body.description,
        icon: req.body.icon,
        category: req.body.category,
        difficulty: req.body.difficulty,
        points: req.body.points,
        criteria: req.body.criteria,
        isHidden: req.body.isHidden ?? false,
        isActive: req.body.isActive ?? true,
        badgeId: req.body.badgeId
      };

      // 执行用例
      const achievement = await this.achievementApplicationService.updateAchievement(command);

      // 返回响应
      res.status(200).json({
        success: true,
        message: '成就更新成功',
        data: achievement
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }

  /**
   * 删除成就
   * @param req 请求对象
   * @param res 响应对象
   */
  async deleteAchievement(req: Request, res: Response): Promise<void> {
    try {
      // 获取成就ID
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '无效的成就ID'
          }
        });
        return;
      }

      // 执行用例
      const success = await this.achievementApplicationService.deleteAchievement(id);

      // 返回响应
      res.status(200).json({
        success: true,
        message: '成就删除成功',
        data: { success }
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }

  /**
   * 恢复成就
   * @param req 请求对象
   * @param res 响应对象
   */
  async restoreAchievement(req: Request, res: Response): Promise<void> {
    try {
      // 获取成就ID
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '无效的成就ID'
          }
        });
        return;
      }

      // 执行用例
      const success = await this.achievementApplicationService.restoreAchievement(id);

      // 返回响应
      res.status(200).json({
        success: true,
        message: '成就恢复成功',
        data: { success }
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }

  /**
   * 授予成就给用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async awardAchievement(req: Request, res: Response): Promise<void> {
    try {
      // 验证请求
      validateRequest(req);

      // 获取成就ID
      const achievementId = parseInt(req.params.id, 10);
      if (isNaN(achievementId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '无效的成就ID'
          }
        });
        return;
      }

      // 构造命令
      const command: AwardAchievementCommand = {
        userId: req.body.userId,
        achievementId
      };

      // 执行用例
      const success = await this.achievementApplicationService.awardAchievement(command);

      // 返回响应
      res.status(200).json({
        success: true,
        message: success ? '成就授予成功' : '用户已获得该成就',
        data: { success }
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }

  /**
   * 获取成就
   * @param req 请求对象
   * @param res 响应对象
   */
  async getAchievement(req: Request, res: Response): Promise<void> {
    try {
      // 获取成就ID
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '无效的成就ID'
          }
        });
        return;
      }

      // 构造查询
      const query: GetAchievementQuery = { id };

      // 执行用例
      const achievement = await this.achievementApplicationService.getAchievement(query);

      // 返回响应
      res.status(200).json({
        success: true,
        data: achievement
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }

  /**
   * 列出成就
   * @param req 请求对象
   * @param res 响应对象
   */
  async listAchievements(req: Request, res: Response): Promise<void> {
    try {
      // 构造查询
      const query: ListAchievementsQuery = {
        category: req.query.category as string | undefined,
        difficulty: req.query.difficulty as string | undefined,
        isActive: req.query.isActive !== undefined 
          ? req.query.isActive === 'true' 
          : undefined,
        isHidden: req.query.isHidden !== undefined 
          ? req.query.isHidden === 'true' 
          : undefined,
        includeDeleted: req.query.includeDeleted === 'true'
      };

      // 执行用例
      const achievements = await this.achievementApplicationService.listAchievements(query);

      // 返回响应
      res.status(200).json({
        success: true,
        data: achievements
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }

  /**
   * 获取用户成就
   * @param req 请求对象
   * @param res 响应对象
   */
  async getUserAchievements(req: Request, res: Response): Promise<void> {
    try {
      // 获取用户ID
      const userId = req.params.userId;
      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: '无效的用户ID'
          }
        });
        return;
      }

      // 构造查询
      const query: GetUserAchievementsQuery = {
        userId,
        achieved: req.query.achieved === 'true',
        includeHidden: req.query.includeHidden === 'true'
      };

      // 执行用例
      const achievements = await this.achievementApplicationService.getUserAchievements(query);

      // 返回响应
      res.status(200).json({
        success: true,
        data: achievements
      });
    } catch (error) {
      errorHandler(error, req, res);
    }
  }
}
