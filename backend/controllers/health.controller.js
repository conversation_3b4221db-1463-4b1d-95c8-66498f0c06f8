/**
 * 健康检查控制器
 * 提供系统健康状态检查的API
 */
const { sequelize } = require('../config/database');
const { redisClient } = require('../config/redis');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const os = require('os');
const { handleApiError } = require('../utils/errorHandler');

/**
 * 基本健康检查
 * 检查API服务是否正常运行
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const basicHealthCheck = async (req, res) => {
  try {
    return apiResponse.success(res, {
      status: 'ok',
      service: 'api',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return handleApiError(error, res, 'basicHealthCheck');
  }
};

/**
 * 数据库健康检查
 * 检查数据库连接是否正常
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const databaseHealthCheck = async (req, res) => {
  try {
    // 检查数据库连接
    await sequelize.authenticate();
    
    // 获取数据库状态信息
    const dbInfo = {
      dialect: sequelize.getDialect(),
      host: sequelize.config.host,
      database: sequelize.config.database,
      port: sequelize.config.port,
      pool: {
        max: sequelize.config.pool.max,
        min: sequelize.config.pool.min,
        idle: sequelize.config.pool.idle
      }
    };
    
    return apiResponse.success(res, {
      status: 'ok',
      service: 'database',
      info: dbInfo,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`数据库健康检查失败: ${error.message}`);
    
    return apiResponse.error(res, {
      status: 'error',
      service: 'database',
      error: error.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
};

/**
 * Redis健康检查
 * 检查Redis连接是否正常
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const redisHealthCheck = async (req, res) => {
  try {
    // 检查Redis连接
    if (!redisClient || !redisClient.isOpen) {
      throw new Error('Redis客户端未连接');
    }
    
    // 执行PING命令
    const pingResult = await redisClient.ping();
    
    // 获取Redis信息
    const info = await redisClient.info();
    const infoLines = info.split('\r\n');
    const redisInfo = {};
    
    // 解析Redis信息
    infoLines.forEach(line => {
      if (line && !line.startsWith('#')) {
        const parts = line.split(':');
        if (parts.length === 2) {
          redisInfo[parts[0]] = parts[1];
        }
      }
    });
    
    return apiResponse.success(res, {
      status: 'ok',
      service: 'redis',
      ping: pingResult,
      info: {
        version: redisInfo.redis_version,
        mode: redisInfo.redis_mode,
        os: redisInfo.os,
        uptime: redisInfo.uptime_in_seconds,
        clients: redisInfo.connected_clients,
        memory: {
          used: redisInfo.used_memory_human,
          peak: redisInfo.used_memory_peak_human
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Redis健康检查失败: ${error.message}`);
    
    return apiResponse.error(res, {
      status: 'error',
      service: 'redis',
      error: error.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
};

/**
 * 系统健康检查
 * 检查系统资源使用情况
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const systemHealthCheck = async (req, res) => {
  try {
    // 获取系统信息
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsage = (usedMemory / totalMemory) * 100;
    
    // 获取CPU信息
    const cpus = os.cpus();
    const cpuCount = cpus.length;
    const cpuModel = cpus[0].model;
    const cpuSpeed = cpus[0].speed;
    
    // 获取负载信息
    const loadAvg = os.loadavg();
    
    // 获取进程信息
    const processMemory = process.memoryUsage();
    const processUptime = process.uptime();
    
    return apiResponse.success(res, {
      status: 'ok',
      service: 'system',
      os: {
        platform: os.platform(),
        release: os.release(),
        type: os.type(),
        arch: os.arch(),
        uptime: os.uptime()
      },
      memory: {
        total: formatBytes(totalMemory),
        free: formatBytes(freeMemory),
        used: formatBytes(usedMemory),
        usagePercent: memoryUsage.toFixed(2) + '%'
      },
      cpu: {
        count: cpuCount,
        model: cpuModel,
        speed: cpuSpeed + 'MHz',
        loadAvg: loadAvg
      },
      process: {
        pid: process.pid,
        uptime: processUptime,
        memory: {
          rss: formatBytes(processMemory.rss),
          heapTotal: formatBytes(processMemory.heapTotal),
          heapUsed: formatBytes(processMemory.heapUsed),
          external: formatBytes(processMemory.external)
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`系统健康检查失败: ${error.message}`);
    
    return apiResponse.error(res, {
      status: 'error',
      service: 'system',
      error: error.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
};

/**
 * 综合健康检查
 * 检查所有服务的健康状态
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const fullHealthCheck = async (req, res) => {
  try {
    const results = {
      status: 'ok',
      services: {},
      timestamp: new Date().toISOString()
    };
    
    // 检查API服务
    results.services.api = {
      status: 'ok'
    };
    
    // 检查数据库
    try {
      await sequelize.authenticate();
      results.services.database = {
        status: 'ok',
        dialect: sequelize.getDialect()
      };
    } catch (error) {
      results.services.database = {
        status: 'error',
        error: error.message
      };
      results.status = 'degraded';
    }
    
    // 检查Redis
    try {
      if (!redisClient || !redisClient.isOpen) {
        throw new Error('Redis客户端未连接');
      }
      
      const pingResult = await redisClient.ping();
      results.services.redis = {
        status: 'ok',
        ping: pingResult
      };
    } catch (error) {
      results.services.redis = {
        status: 'error',
        error: error.message
      };
      results.status = 'degraded';
    }
    
    // 检查系统资源
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;
    const loadAvg = os.loadavg()[0];
    const cpuCount = os.cpus().length;
    
    results.services.system = {
      status: 'ok',
      memory: {
        usagePercent: memoryUsage.toFixed(2) + '%'
      },
      cpu: {
        loadAvg: loadAvg,
        count: cpuCount
      }
    };
    
    // 如果内存使用率超过90%或负载过高，标记为降级
    if (memoryUsage > 90 || loadAvg > cpuCount * 0.8) {
      results.services.system.status = 'warning';
      results.status = 'degraded';
    }
    
    // 设置响应状态码
    const statusCode = results.status === 'ok' ? 200 : 
                       results.status === 'degraded' ? 200 : 500;
    
    return apiResponse.custom(res, results, statusCode);
  } catch (error) {
    logger.error(`综合健康检查失败: ${error.message}`);
    
    return apiResponse.error(res, {
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
};

/**
 * 格式化字节数为人类可读格式
 * @param {Number} bytes - 字节数
 * @returns {String} 格式化后的字符串
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

module.exports = {
  basicHealthCheck,
  databaseHealthCheck,
  redisHealthCheck,
  systemHealthCheck,
  fullHealthCheck
};
