/**
 * 观点控制器 V2
 * 使用服务层处理业务逻辑
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const insightService = require('../services/insight.service');

/**
 * 获取标签下的观点列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getInsightsByTagId = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    // 使用服务层获取观点列表
    const result = await insightService.getInsightsByTagId(tagId, userId, page, pageSize);

    // 格式化响应数据
    const insights = result.rows.map(insight => ({
      id: insight.id,
      content: insight.content,
      source: insight.source,
      background: insight.background,
      createdAt: insight.created_at
    }));

    return apiResponse.success(res, {
      insights,
      pagination: {
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(result.count / parseInt(pageSize))
      }
    });
  } catch (error) {
    logger.error(`获取观点列表失败: ${error.message}`);
    return apiResponse.error(res, '获取观点列表失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 获取观点详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getInsightById = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层获取观点详情
    const insight = await insightService.getInsightDetails(id, userId);

    return apiResponse.success(res, {
      id: insight.id,
      content: insight.content,
      source: insight.source,
      background: insight.background,
      tagId: insight.tag_id,
      tagName: insight.tag ? insight.tag.name : null,
      planId: insight.tag && insight.tag.learningPlan ? insight.tag.learningPlan.id : null,
      planTitle: insight.tag && insight.tag.learningPlan ? insight.tag.learningPlan.title : null,
      createdAt: insight.created_at
    });
  } catch (error) {
    logger.error(`获取观点详情失败: ${error.message}`);
    return apiResponse.error(res, '获取观点详情失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 创建观点
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const createInsight = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId, content, source, background } = req.body;

    // 使用服务层创建观点
    const insightData = {
      tagId,
      content,
      source,
      background
    };

    const insight = await insightService.createInsight(userId, insightData);

    return apiResponse.created(res, {
      id: insight.id,
      content: insight.content,
      source: insight.source,
      background: insight.background,
      tagId: insight.tag_id,
      createdAt: insight.created_at
    });
  } catch (error) {
    logger.error(`创建观点失败: ${error.message}`);
    return apiResponse.error(res, '创建观点失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 更新观点
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateInsight = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;
    const { content, source, background } = req.body;

    // 使用服务层更新观点
    const updateData = {
      content,
      source,
      background
    };

    const insight = await insightService.updateInsight(id, userId, updateData);

    return apiResponse.success(res, {
      id: insight.id,
      content: insight.content,
      source: insight.source,
      background: insight.background,
      tagId: insight.tag_id
    }, '观点已更新');
  } catch (error) {
    logger.error(`更新观点失败: ${error.message}`);
    return apiResponse.error(res, '更新观点失败', 'SERVER_ERROR', 500);
  }
};

/**
 * 删除观点
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteInsight = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层删除观点
    const success = await insightService.deleteInsight(id, userId);

    if (!success) {
      return apiResponse.notFound(res, '观点不存在或无权删除');
    }

    return apiResponse.success(res, null, '观点已删除');
  } catch (error) {
    logger.error(`删除观点失败: ${error.message}`);
    return apiResponse.error(res, '删除观点失败', 'SERVER_ERROR', 500);
  }
};

/**
 * @swagger
 * /api/v2/insights/{id}/soft-delete:
 *   delete:
 *     summary: 软删除观点
 *     description: 软删除指定观点（观点仍然存在，但在大多数查询中不可见）
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 观点ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: 观点已被软删除
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const softDeleteInsight = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层软删除观点
    await insightService.softDeleteInsight(id, userId);

    return apiResponse.success(res, {
      message: '观点已被软删除'
    });
  } catch (error) {
    logger.error(`软删除观点失败: ${error.message}`);

    if (error.message.includes('不存在或不属于当前用户')) {
      return apiResponse.notFound(res, '观点不存在或不属于当前用户');
    }

    return apiResponse.error(res, '软删除观点失败', 'SERVER_ERROR', 500);
  }
};

/**
 * @swagger
 * /api/v2/insights/{id}/restore:
 *   post:
 *     summary: 恢复已软删除的观点
 *     description: 恢复之前软删除的观点
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 观点ID
 *     responses:
 *       200:
 *         description: 恢复成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: 观点已恢复
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const restoreInsight = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用服务层恢复观点
    await insightService.restoreInsight(id, userId);

    return apiResponse.success(res, {
      message: '观点已恢复'
    });
  } catch (error) {
    logger.error(`恢复观点失败: ${error.message}`);

    if (error.message.includes('不存在或不属于当前用户')) {
      return apiResponse.notFound(res, '观点不存在或不属于当前用户');
    }

    if (error.message.includes('未被删除')) {
      return apiResponse.badRequest(res, '观点未被删除，无需恢复');
    }

    return apiResponse.error(res, '恢复观点失败', 'SERVER_ERROR', 500);
  }
};

/**
 * @swagger
 * /api/v2/tags/{tagId}/insights/deleted:
 *   get:
 *     summary: 获取已删除的观点列表
 *     description: 获取指定标签下已软删除的观点列表
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取已删除的观点列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     insights:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           content:
 *                             type: string
 *                           source:
 *                             type: string
 *                           background:
 *                             type: string
 *                           deletedAt:
 *                             type: string
 *                             format: date-time
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         pageSize:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const getDeletedInsights = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tagId } = req.params;
    const { page = 1, pageSize = 10 } = req.query;

    // 使用服务层获取已删除的观点列表
    const result = await insightService.getDeletedInsights(tagId, userId, page, pageSize);

    // 格式化响应数据
    const insights = result.rows.map(insight => ({
      id: insight.id,
      content: insight.content,
      source: insight.source,
      background: insight.background,
      deletedAt: insight.deleted_at,
      createdAt: insight.created_at
    }));

    return apiResponse.success(res, {
      insights,
      pagination: {
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(result.count / parseInt(pageSize))
      }
    });
  } catch (error) {
    logger.error(`获取已删除的观点列表失败: ${error.message}`);

    if (error.message.includes('标签不存在或不属于当前用户')) {
      return apiResponse.notFound(res, '标签不存在或不属于当前用户');
    }

    return apiResponse.error(res, '获取已删除的观点列表失败', 'SERVER_ERROR', 500);
  }
};

module.exports = {
  getInsightsByTagId,
  getInsightById,
  createInsight,
  updateInsight,
  deleteInsight,
  softDeleteInsight,
  restoreInsight,
  getDeletedInsights
};
