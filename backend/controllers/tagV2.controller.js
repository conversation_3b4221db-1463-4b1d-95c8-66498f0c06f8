/**
 * 标签控制器 V2
 * 处理标签相关的请求，支持软删除
 */
const apiResponse = require('../utils/apiResponse');
const serviceContainer = require('../config/serviceContainer');
const logger = require('../config/logger');
const { handleApiError, handleNotFoundError, handleBadRequestError } = require('../utils/errorHandler');

// 获取标签服务
const tagService = serviceContainer.getService('tagService');

/**
 * @swagger
 * /api/v2/tags/{id}/soft-delete:
 *   delete:
 *     summary: 软删除标签
 *     description: 软删除指定标签（标签仍然存在，但在大多数查询中不可见）
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: 标签已被软删除
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const softDeleteTag = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用标签服务软删除标签
    await tagService.softDeleteTag(id, userId);

    return apiResponse.success(res, {
      message: '标签已被软删除'
    });
  } catch (error) {
    if (error.message === '标签不存在或不属于当前用户') {
      return handleNotFoundError(res, '标签不存在或不属于当前用户');
    }

    return handleApiError(error, res, 'softDeleteTag');
  }
};

/**
 * @swagger
 * /api/v2/tags/{id}/restore:
 *   post:
 *     summary: 恢复已软删除的标签
 *     description: 恢复已软删除的标签
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签ID
 *     responses:
 *       200:
 *         description: 恢复成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: 标签已被恢复
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const restoreTag = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    // 使用标签服务恢复标签
    await tagService.restoreTag(id, userId);

    return apiResponse.success(res, {
      message: '标签已被恢复'
    });
  } catch (error) {
    if (error.message === '标签不存在或不属于当前用户') {
      return handleNotFoundError(res, '标签不存在或不属于当前用户');
    }

    if (error.message === '标签未被删除，无需恢复') {
      return handleBadRequestError(res, '标签未被删除，无需恢复');
    }

    return handleApiError(error, res, 'restoreTag');
  }
};

/**
 * @swagger
 * /api/v2/tags/deleted:
 *   get:
 *     summary: 获取已删除的标签列表
 *     description: 获取当前用户已软删除的标签列表
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取已删除标签列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     tags:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DeletedTag'
 *                     count:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
const getDeletedTags = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { page = 1, pageSize = 10 } = req.query;

    // 使用标签服务获取已删除的标签列表
    const result = await tagService.getDeletedTags(
      userId,
      parseInt(page),
      parseInt(pageSize)
    );

    // 格式化响应数据
    const formattedTags = result.rows.map(tag => ({
      id: tag.id,
      name: tag.name,
      relevanceScore: tag.relevance_score,
      weight: tag.weight,
      usageCount: tag.usage_count,
      isVerified: tag.is_verified,
      sortOrder: tag.sort_order,
      deletedAt: tag.deleted_at,
      category: tag.category ? {
        id: tag.category.id,
        name: tag.category.name
      } : null
    }));

    return apiResponse.success(res, {
      tags: formattedTags,
      count: result.count,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: result.totalPages
    });
  } catch (error) {
    return handleApiError(error, res, 'getDeletedTags');
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     DeletedTag:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 标签ID
 *         name:
 *           type: string
 *           description: 标签名称
 *         relevanceScore:
 *           type: number
 *           format: float
 *           description: 相关性得分
 *         weight:
 *           type: number
 *           format: float
 *           description: 权重
 *         usageCount:
 *           type: integer
 *           description: 使用次数
 *         isVerified:
 *           type: boolean
 *           description: 是否已验证
 *         sortOrder:
 *           type: integer
 *           description: 排序顺序
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           description: 删除时间
 *         category:
 *           type: object
 *           properties:
 *             id:
 *               type: string
 *             name:
 *               type: string
 */

module.exports = {
  softDeleteTag,
  restoreTag,
  getDeletedTags
};
