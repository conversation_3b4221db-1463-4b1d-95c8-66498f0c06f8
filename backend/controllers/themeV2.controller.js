/**
 * 主题控制器 V2
 * 支持软删除功能
 */
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const serviceContainer = require('../config/serviceContainer');
const {
  handleApiError,
  handleNotFoundError
} = require('../utils/errorHandler');

// 获取主题服务
const themeService = serviceContainer.getService('themeService');

/**
 * 获取所有可用主题
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getThemes = async (req, res) => {
  try {
    const { includeInactive } = req.query;
    const includeInactiveFlag = includeInactive === 'true';

    // 使用服务层获取主题列表
    const themes = await themeService.getAllThemes(includeInactiveFlag);

    return apiResponse.success(res, { themes });
  } catch (error) {
    return handleApiError(error, res, 'getThemes');
  }
};

/**
 * 获取单个主题详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getThemeById = async (req, res) => {
  try {
    const { id } = req.params;

    // 使用服务层获取主题详情
    const theme = await themeService.getThemeById(id);

    return apiResponse.success(res, theme);
  } catch (error) {
    if (error.code === 'THEME_NOT_FOUND') {
      return handleNotFoundError(res, '主题不存在或未激活');
    }
    return handleApiError(error, res, 'getThemeById');
  }
};

/**
 * 软删除主题
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const softDeleteTheme = async (req, res) => {
  try {
    const { id } = req.params;

    // 使用服务层软删除主题
    await themeService.softDeleteTheme(id);

    return apiResponse.success(res, {
      message: '主题已被软删除'
    });
  } catch (error) {
    logger.error(`软删除主题失败: ${error.message}`);

    if (error.code === 'THEME_NOT_FOUND') {
      return handleNotFoundError(res, '主题不存在');
    }

    return handleApiError(error, res, 'softDeleteTheme');
  }
};

/**
 * 恢复已软删除的主题
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const restoreTheme = async (req, res) => {
  try {
    const { id } = req.params;

    // 使用服务层恢复主题
    await themeService.restoreTheme(id);

    return apiResponse.success(res, {
      message: '主题已恢复'
    });
  } catch (error) {
    logger.error(`恢复主题失败: ${error.message}`);

    if (error.code === 'THEME_NOT_FOUND') {
      return handleNotFoundError(res, '主题不存在');
    }

    if (error.code === 'THEME_NOT_DELETED') {
      return apiResponse.badRequest(res, '主题未被删除，无需恢复');
    }

    return handleApiError(error, res, 'restoreTheme');
  }
};

/**
 * 获取已删除的主题列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getDeletedThemes = async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;

    // 使用服务层获取已删除的主题列表
    const result = await themeService.getDeletedThemes(page, pageSize);

    return apiResponse.success(res, result);
  } catch (error) {
    return handleApiError(error, res, 'getDeletedThemes');
  }
};

module.exports = {
  getThemes,
  getThemeById,
  softDeleteTheme,
  restoreTheme,
  getDeletedThemes
};
