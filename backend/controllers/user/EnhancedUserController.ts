/**
 * 增强版用户控制器
 * 使用增强版应用服务实现
 */
import { Request, Response } from 'express';
import { EnhancedUserApplicationService } from '../../application/services/user/EnhancedUserApplicationService';
import { CreateUserCommand } from '../../application/commands/user/CreateUserCommand';
import { UpdateUserCommand } from '../../application/commands/user/UpdateUserCommand';
import { DeleteUserCommand } from '../../application/commands/user/DeleteUserCommand';
import { RestoreUserCommand } from '../../application/commands/user/RestoreUserCommand';
import { UpdateUserProfileCommand } from '../../application/commands/user/UpdateUserProfileCommand';
import { UpdateUserPasswordCommand } from '../../application/commands/user/UpdateUserPasswordCommand';
import { AddUserRoleCommand } from '../../application/commands/user/AddUserRoleCommand';
import { RemoveUserRoleCommand } from '../../application/commands/user/RemoveUserRoleCommand';
import { GetUserQuery } from '../../application/queries/user/GetUserQuery';
import { SearchUsersQuery } from '../../application/queries/user/SearchUsersQuery';
import { Logger } from '../../infrastructure/logging/Logger';

/**
 * EnhancedUserController类
 * 增强版用户控制器，使用增强版应用服务实现
 */
export class EnhancedUserController {
  /**
   * 构造函数
   * @param userApplicationService 用户应用服务
   * @param logger 日志记录器
   */
  constructor(
    private readonly userApplicationService: EnhancedUserApplicationService,
    private readonly logger: Logger
  ) {}

  /**
   * 创建用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async createUser(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`创建用户请求: ${JSON.stringify(req.body)}`);
      
      const command = new CreateUserCommand(
        req.body.username,
        req.body.nickname,
        req.body.email,
        req.body.emailVerified,
        req.body.phoneNumber,
        req.body.phoneVerified,
        req.body.password,
        req.body.wechatOpenId,
        req.body.avatar,
        req.body.gender,
        req.body.birthday,
        req.body.status,
        req.body.roleIds,
        req.body.privacySettings
      );

      const user = await this.userApplicationService.createUser(command);
      
      res.status(201).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`创建用户失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 更新用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async updateUser(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`更新用户请求: ID=${req.params.id}, ${JSON.stringify(req.body)}`);
      
      const command = new UpdateUserCommand(
        parseInt(req.params.id),
        req.body.username,
        req.body.nickname,
        req.body.email,
        req.body.emailVerified,
        req.body.phoneNumber,
        req.body.phoneVerified,
        req.body.password,
        req.body.wechatOpenId,
        req.body.avatar,
        req.body.gender,
        req.body.birthday,
        req.body.status,
        req.body.roleIds
      );

      const user = await this.userApplicationService.updateUser(command);
      
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`更新用户失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 删除用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`删除用户请求: ID=${req.params.id}`);
      
      const command = new DeleteUserCommand(parseInt(req.params.id));
      await this.userApplicationService.deleteUser(command);
      
      res.status(200).json({
        success: true,
        message: '用户删除成功'
      });
    } catch (error) {
      this.logger.error(`删除用户失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 恢复用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async restoreUser(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`恢复用户请求: ID=${req.params.id}`);
      
      const command = new RestoreUserCommand(parseInt(req.params.id));
      const user = await this.userApplicationService.restoreUser(command);
      
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`恢复用户失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 更新用户资料
   * @param req 请求对象
   * @param res 响应对象
   */
  async updateUserProfile(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`更新用户资料请求: ID=${req.params.id}, ${JSON.stringify(req.body)}`);
      
      const command = new UpdateUserProfileCommand(
        parseInt(req.params.id),
        req.body.nickname,
        req.body.avatar,
        req.body.gender,
        req.body.birthday
      );

      const user = await this.userApplicationService.updateUserProfile(command);
      
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`更新用户资料失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 更新用户密码
   * @param req 请求对象
   * @param res 响应对象
   */
  async updateUserPassword(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`更新用户密码请求: ID=${req.params.id}`);
      
      const command = new UpdateUserPasswordCommand(
        parseInt(req.params.id),
        req.body.oldPassword,
        req.body.newPassword
      );

      const user = await this.userApplicationService.updateUserPassword(command);
      
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`更新用户密码失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 添加用户角色
   * @param req 请求对象
   * @param res 响应对象
   */
  async addUserRole(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`添加用户角色请求: userId=${req.params.id}, roleId=${req.body.roleId}`);
      
      const command = new AddUserRoleCommand(
        parseInt(req.params.id),
        req.body.roleId
      );

      const user = await this.userApplicationService.addUserRole(command);
      
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`添加用户角色失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 移除用户角色
   * @param req 请求对象
   * @param res 响应对象
   */
  async removeUserRole(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`移除用户角色请求: userId=${req.params.id}, roleId=${req.params.roleId}`);
      
      const command = new RemoveUserRoleCommand(
        parseInt(req.params.id),
        parseInt(req.params.roleId)
      );

      const user = await this.userApplicationService.removeUserRole(command);
      
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`移除用户角色失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 获取用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async getUser(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`获取用户请求: ID=${req.params.id}`);
      
      const query = new GetUserQuery(
        req.params.id ? parseInt(req.params.id) : undefined,
        req.query.username as string,
        req.query.email as string,
        req.query.phoneNumber as string,
        req.query.wechatOpenId as string
      );

      const user = await this.userApplicationService.getUser(query);
      
      if (!user) {
        res.status(404).json({
          success: false,
          message: '用户不存在'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`获取用户失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 搜索用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async searchUsers(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug(`搜索用户请求: ${JSON.stringify(req.query)}`);
      
      const query = new SearchUsersQuery(
        req.query.page ? parseInt(req.query.page as string) : undefined,
        req.query.pageSize ? parseInt(req.query.pageSize as string) : undefined,
        req.query.roleId ? parseInt(req.query.roleId as string) : undefined,
        req.query.deleted === 'true',
        req.query.keyword as string
      );

      const result = await this.userApplicationService.searchUsers(query);
      
      res.status(200).json({
        success: true,
        data: result.items,
        total: result.total,
        page: query.page || 1,
        pageSize: query.pageSize || result.items.length
      });
    } catch (error) {
      this.logger.error(`搜索用户失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 获取当前用户
   * @param req 请求对象
   * @param res 响应对象
   */
  async getCurrentUser(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug('获取当前用户请求');
      
      // 从请求中获取当前用户ID
      const userId = (req as any).user?.id;
      
      if (!userId) {
        res.status(401).json({
          success: false,
          message: '未授权'
        });
        return;
      }

      const query = new GetUserQuery(userId);
      const user = await this.userApplicationService.getUser(query);
      
      if (!user) {
        res.status(404).json({
          success: false,
          message: '用户不存在'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error(`获取当前用户失败: ${error.message}`);
      
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
}
