import { Request, Response } from 'express';
import { PasswordResetService } from '../../infrastructure/services/security/PasswordResetService';
import { UserRepository } from '../../domain/repositories/user/UserRepository';
import { Logger } from '../../infrastructure/logging/Logger';
import { EmailService } from '../../infrastructure/services/communication/EmailService';
import { SmsService } from '../../infrastructure/services/communication/SmsService';

/**
 * 密码重置控制器
 * 处理密码重置相关的请求
 */
export class PasswordResetController {
  private logger: Logger;

  /**
   * 构造函数
   * @param passwordResetService 密码重置服务
   * @param userRepository 用户仓库
   * @param emailService 邮件服务
   * @param smsService 短信服务
   */
  constructor(
    private readonly passwordResetService: PasswordResetService,
    private readonly userRepository: UserRepository,
    private readonly emailService: EmailService,
    private readonly smsService: SmsService
  ) {
    this.logger = new Logger('PasswordResetController');
  }

  /**
   * 请求密码重置（通过邮箱）
   * @param req 请求对象
   * @param res 响应对象
   */
  async requestResetByEmail(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;
      const ipAddress = req.ip;
      const userAgent = req.headers['user-agent'] || '';

      this.logger.info(`收到邮箱密码重置请求: ${email}`);

      // 查找用户
      const user = await this.userRepository.findByEmail(email);
      
      // 即使用户不存在，也返回成功响应，以防止用户枚举
      if (!user) {
        this.logger.warn(`尝试为不存在的邮箱重置密码: ${email}`);
        res.status(200).json({
          success: true,
          message: '如果该邮箱存在，密码重置邮件已发送，请检查您的邮箱'
        });
        return;
      }

      // 生成重置令牌
      const resetToken = await this.passwordResetService.createResetToken(user.id.toString(), email);

      // 发送重置邮件
      const emailSent = await this.passwordResetService.sendResetEmail(
        email,
        resetToken,
        user.username || user.nickname || '用户'
      );

      if (!emailSent) {
        this.logger.error(`发送密码重置邮件失败: ${email}`);
        res.status(500).json({
          success: false,
          error: {
            code: 'EMAIL_SEND_FAILED',
            message: '发送密码重置邮件失败，请稍后再试',
            details: {}
          }
        });
        return;
      }

      // 记录请求信息
      await this.passwordResetService.logResetRequest(user.id.toString(), 'email', ipAddress, userAgent);

      res.status(200).json({
        success: true,
        message: '密码重置邮件已发送，请检查您的邮箱'
      });
    } catch (error) {
      this.logger.error(`请求密码重置失败: ${error.message}`);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '服务器错误，请稍后再试',
          details: { error: error.message }
        }
      });
    }
  }

  /**
   * 请求密码重置（通过手机号）
   * @param req 请求对象
   * @param res 响应对象
   */
  async requestResetByPhone(req: Request, res: Response): Promise<void> {
    try {
      const { phoneNumber } = req.body;
      const ipAddress = req.ip;
      const userAgent = req.headers['user-agent'] || '';

      this.logger.info(`收到手机号密码重置请求: ${phoneNumber}`);

      // 查找用户
      const user = await this.userRepository.findByPhoneNumber(phoneNumber);
      
      // 即使用户不存在，也返回成功响应，以防止用户枚举
      if (!user) {
        this.logger.warn(`尝试为不存在的手机号重置密码: ${phoneNumber}`);
        res.status(200).json({
          success: true,
          message: '如果该手机号存在，密码重置验证码已发送，请检查您的手机'
        });
        return;
      }

      // 生成重置令牌（短验证码）
      const resetToken = await this.passwordResetService.createResetToken(user.id.toString(), phoneNumber);
      const verificationCode = resetToken.substring(0, 6); // 使用前6位作为验证码

      // 发送重置短信
      const smsSent = await this.passwordResetService.sendResetSMS(phoneNumber, verificationCode);

      if (!smsSent) {
        this.logger.error(`发送密码重置短信失败: ${phoneNumber}`);
        res.status(500).json({
          success: false,
          error: {
            code: 'SMS_SEND_FAILED',
            message: '发送密码重置短信失败，请稍后再试',
            details: {}
          }
        });
        return;
      }

      // 记录请求信息
      await this.passwordResetService.logResetRequest(user.id.toString(), 'phone', ipAddress, userAgent);

      res.status(200).json({
        success: true,
        message: '密码重置验证码已发送，请检查您的手机'
      });
    } catch (error) {
      this.logger.error(`请求密码重置失败: ${error.message}`);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '服务器错误，请稍后再试',
          details: { error: error.message }
        }
      });
    }
  }

  /**
   * 验证重置令牌
   * @param req 请求对象
   * @param res 响应对象
   */
  async verifyResetToken(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.params;

      this.logger.info(`验证密码重置令牌: ${token.substring(0, 8)}...`);

      // 解析令牌获取用户ID
      const userId = await this.passwordResetService.getUserIdFromToken(token);
      
      if (!userId) {
        this.logger.warn(`无效的密码重置令牌: ${token.substring(0, 8)}...`);
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: '无效或已过期的令牌',
            details: {}
          }
        });
        return;
      }

      // 验证令牌
      const isValid = await this.passwordResetService.verifyResetToken(userId, token);

      if (!isValid) {
        this.logger.warn(`无效的密码重置令牌: ${token.substring(0, 8)}...`);
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: '无效或已过期的令牌',
            details: {}
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: '令牌有效',
        data: {
          userId
        }
      });
    } catch (error) {
      this.logger.error(`验证密码重置令牌失败: ${error.message}`);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '服务器错误，请稍后再试',
          details: { error: error.message }
        }
      });
    }
  }

  /**
   * 重置密码
   * @param req 请求对象
   * @param res 响应对象
   */
  async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token, newPassword } = req.body;

      this.logger.info(`重置密码请求: ${token.substring(0, 8)}...`);

      // 解析令牌获取用户ID
      const userId = await this.passwordResetService.getUserIdFromToken(token);
      
      if (!userId) {
        this.logger.warn(`无效的密码重置令牌: ${token.substring(0, 8)}...`);
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: '无效或已过期的令牌',
            details: {}
          }
        });
        return;
      }

      // 重置密码
      const success = await this.passwordResetService.resetPassword(userId, token, newPassword);

      if (!success) {
        this.logger.warn(`重置密码失败: ${token.substring(0, 8)}...`);
        res.status(400).json({
          success: false,
          error: {
            code: 'RESET_FAILED',
            message: '密码重置失败，请重试',
            details: {}
          }
        });
        return;
      }

      // 查找用户
      const user = await this.userRepository.findById(userId);
      
      // 发送密码已重置通知
      if (user && user.email) {
        await this.emailService.sendEmail(
          user.email,
          'AIBUBB - 密码已重置',
          `
            <h2>密码已重置</h2>
            <p>您好 ${user.username || user.nickname || '用户'}，</p>
            <p>您的密码已成功重置。</p>
            <p>如果这不是您本人操作，请立即联系我们。</p>
            <p>谢谢！</p>
            <p>AIBUBB团队</p>
          `
        );
      }

      res.status(200).json({
        success: true,
        message: '密码重置成功，请使用新密码登录'
      });
    } catch (error) {
      this.logger.error(`重置密码失败: ${error.message}`);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '服务器错误，请稍后再试',
          details: { error: error.message }
        }
      });
    }
  }
}
