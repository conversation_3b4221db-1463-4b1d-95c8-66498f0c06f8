/**
 * 创建bubble_interaction表
 */
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'secret',
  database: 'aibubb_db'
};

// 创建表
async function createTable() {
  let connection;
  try {
    console.log('正在连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 检查表是否存在
    console.log('检查bubble_interaction表是否存在...');
    const [tables] = await connection.query(`
      SHOW TABLES LIKE 'bubble_interaction'
    `);

    if (tables.length > 0) {
      console.log('bubble_interaction表已存在，跳过创建');
      return;
    }

    // 创建表
    console.log('创建bubble_interaction表...');
    await connection.query(`
      CREATE TABLE bubble_interaction (
        id BIGINT NOT NULL AUTO_INCREMENT,
        user_id BIGINT,
        bubble_id VARCHAR(36),
        interaction_type VARCHAR(50) NOT NULL,
        device_info JSON,
        context_data JSON,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        INDEX idx_user_id (user_id),
        INDEX idx_bubble_id (bubble_id),
        INDEX idx_interaction_type (interaction_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
    console.log('bubble_interaction表创建成功');

    // 插入测试数据
    console.log('插入测试数据...');
    await connection.query(`
      INSERT INTO bubble_interaction 
        (user_id, bubble_id, interaction_type, device_info, context_data) 
      VALUES 
        (1, 'bubble-001', 'view', '{"os": "iOS", "platform": "mobile", "model": "iPhone 13", "appVersion": "1.2.3"}', '{"view": "home", "sessionDuration": 120, "referrer": "notification"}'),
        (1, 'bubble-002', 'click', '{"os": "Android", "platform": "mobile", "model": "Samsung S21", "appVersion": "1.2.3"}', '{"view": "detail", "sessionDuration": 45, "referrer": "search"}'),
        (2, 'bubble-001', 'share', '{"os": "Windows", "platform": "desktop", "model": "Chrome", "appVersion": "1.2.3"}', '{"view": "profile", "sessionDuration": 300, "referrer": "direct"}');
    `);
    console.log('测试数据插入成功');

    return true;
  } catch (error) {
    console.error(`创建表失败: ${error.message}`);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 执行
createTable()
  .then(() => {
    console.log('创建表完成');
    process.exit(0);
  })
  .catch(error => {
    console.error(`创建表失败: ${error.message}`);
    process.exit(1);
  });
