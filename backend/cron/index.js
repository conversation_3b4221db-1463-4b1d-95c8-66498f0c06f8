/**
 * 定时任务注册中心
 * 
 * 此文件用于注册和启动所有定时任务
 */

const logger = require('../config/logger');
const deadLetterQueueCron = require('./process-dead-letter-queue.cron');

/**
 * 启动所有定时任务
 */
function startAllCronJobs() {
  logger.info('开始启动所有定时任务...');
  
  // 启动死信队列处理定时任务
  const dlqSchedule = process.env.DLQ_CRON_SCHEDULE || '*/5 * * * *'; // 默认每5分钟
  deadLetterQueueCron.startCronJob(dlqSchedule);
  
  // 在这里添加其他定时任务
  
  logger.info('所有定时任务已启动');
}

module.exports = {
  startAllCronJobs
};
