# AIBUBB API 使用示例（基于实际实现）

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.1 |
| 状态 | 已完成 |
| 创建日期 | 2025-07-07 |
| 最后更新 | 2025-05-04 |
| 作者 | AIBUBB技术团队 |

## 目录

> **重要说明**：本文档提供的所有API使用示例都基于实际代码分析和实现，确保与项目的真实状态保持一致。所有示例都经过验证，确保可以正常工作。

1. [认证](#1-认证)
2. [用户管理](#2-用户管理)
3. [标签管理](#3-标签管理)
4. [学习计划](#4-学习计划)
5. [学习内容](#5-学习内容)

## 1. 认证

### 1.1 用户登录

**请求**:

```http
POST /api/v2/auth/login
Content-Type: application/json

{
  "code": "wx_code_from_wechat",
  "loginType": "wechat"
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 123456789,
      "nickname": "用户昵称",
      "avatarUrl": "https://example.com/avatar.jpg",
      "gender": 1,
      "studyDays": 10,
      "level": 2,
      "createdAt": "2023-06-15T08:00:00Z",
      "updatedAt": "2023-06-15T08:00:00Z"
    }
  }
}
```

### 1.2 刷新令牌

**请求**:

```http
POST /api/v2/auth/refresh-token
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 1.3 用户登出

**请求**:

```http
POST /api/v2/auth/logout
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "message": "登出成功"
  }
}
```

## 2. 用户管理

### 2.1 获取当前用户信息

**请求**:

```http
GET /api/v2/users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 123456789,
    "nickname": "用户昵称",
    "avatarUrl": "https://example.com/avatar.jpg",
    "gender": 1,
    "studyDays": 10,
    "level": 2,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z"
  }
}
```

### 2.2 更新用户信息

**请求**:

```http
PATCH /api/v2/users/me
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "nickname": "新昵称",
  "avatarUrl": "https://example.com/new-avatar.jpg"
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 123456789,
    "nickname": "新昵称",
    "avatarUrl": "https://example.com/new-avatar.jpg",
    "gender": 1,
    "studyDays": 10,
    "level": 2,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z"
  }
}
```

## 3. 标签管理

### 3.1 获取标签列表

**请求**:

```http
GET /api/v2/tags?page=1&pageSize=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "tags": [
      {
        "id": 1001,
        "name": "倾听",
        "relevanceScore": 0.85,
        "weight": 1.0,
        "usageCount": 15,
        "isVerified": true,
        "sortOrder": 1,
        "category": {
          "id": 1,
          "name": "沟通技能"
        }
      },
      {
        "id": 1002,
        "name": "共情",
        "relevanceScore": 0.8,
        "weight": 1.0,
        "usageCount": 12,
        "isVerified": true,
        "sortOrder": 2,
        "category": {
          "id": 1,
          "name": "沟通技能"
        }
      }
    ],
    "count": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

### 3.2 创建标签

**请求**:

```http
POST /api/v2/tags
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "name": "表达",
  "categoryId": 1,
  "relevanceScore": 0.75,
  "weight": 1.0
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 1003,
    "name": "表达",
    "relevanceScore": 0.75,
    "weight": 1.0,
    "usageCount": 0,
    "isVerified": false,
    "sortOrder": 0,
    "category": {
      "id": 1,
      "name": "沟通技能"
    }
  }
}
```

### 3.3 软删除标签

**请求**:

```http
DELETE /api/v2/tags/1003/soft-delete
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "message": "标签已被软删除"
  }
}
```

### 3.4 恢复标签

**请求**:

```http
POST /api/v2/tags/1003/restore
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "message": "标签已被恢复"
  }
}
```

## 4. 学习计划

### 4.1 创建学习计划

**请求**:

```http
POST /api/v2/learning-plans
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "title": "提升与伴侣的沟通能力",
  "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
  "themeId": 1,
  "targetDays": 14,
  "tagIds": [1001, 1002],
  "setAsCurrent": true,
  "dailyGoalMinutes": 30,
  "dailyGoalExercises": 2,
  "isPublic": false
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "提升与伴侣的沟通能力",
    "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
    "targetDays": 14,
    "completedDays": 0,
    "progress": 0,
    "status": "not_started",
    "startDate": "2023-06-15",
    "endDate": "2023-06-29",
    "isCurrent": true,
    "dailyGoalMinutes": 30,
    "dailyGoalExercises": 2,
    "isPublic": false,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听"
      },
      {
        "id": 1002,
        "name": "共情"
      }
    ]
  }
}
```

### 4.2 获取学习计划列表

**请求**:

```http
GET /api/v2/learning-plans?status=in_progress&page=1&pageSize=10&sortBy=updatedAt&sortOrder=desc
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": 101,
        "userId": 123456789,
        "themeId": 1,
        "title": "提升与伴侣的沟通能力",
        "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
        "targetDays": 14,
        "completedDays": 5,
        "progress": 35,
        "status": "in_progress",
        "startDate": "2023-06-15",
        "endDate": "2023-06-29",
        "isCurrent": true,
        "createdAt": "2023-06-15T08:00:00Z",
        "updatedAt": "2023-06-15T08:00:00Z",
        "tags": [
          {
            "id": 1001,
            "name": "倾听"
          },
          {
            "id": 1002,
            "name": "共情"
          }
        ]
      }
    ],
    "count": 5,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 4.3 获取学习计划详情

**请求**:

```http
GET /api/v2/learning-plans/101
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "提升与伴侣的沟通能力",
    "description": "我希望能更好地与伴侣沟通，减少误解和冲突",
    "targetDays": 14,
    "completedDays": 5,
    "progress": 35,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-06-29",
    "isCurrent": true,
    "dailyGoalMinutes": 30,
    "dailyGoalExercises": 2,
    "isPublic": false,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听",
        "relevanceScore": 0.85
      },
      {
        "id": 1002,
        "name": "共情",
        "relevanceScore": 0.75
      }
    ],
    "dailyContents": [
      {
        "day": 1,
        "title": "了解倾听的重要性",
        "content": "今天我们将学习倾听的重要性...",
        "isCompleted": true,
        "completionDate": "2023-06-15T10:30:00Z"
      },
      {
        "day": 2,
        "title": "练习积极倾听",
        "content": "今天我们将练习积极倾听技巧...",
        "isCompleted": true,
        "completionDate": "2023-06-16T11:15:00Z"
      }
    ]
  }
}
```

### 4.4 更新学习计划

**请求**:

```http
PUT /api/v2/learning-plans/101
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "title": "改善与伴侣的沟通",
  "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
  "targetDays": 21,
  "dailyGoalMinutes": 45
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "改善与伴侣的沟通",
    "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
    "targetDays": 21,
    "completedDays": 5,
    "progress": 24,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-07-06",
    "isCurrent": true,
    "dailyGoalMinutes": 45,
    "dailyGoalExercises": 2,
    "isPublic": false,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-17T09:30:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听"
      },
      {
        "id": 1002,
        "name": "共情"
      }
    ]
  }
}
```

### 4.5 开始学习计划

**请求**:

```http
POST /api/v2/learning-plans/101/start
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "改善与伴侣的沟通",
    "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
    "targetDays": 21,
    "completedDays": 0,
    "progress": 0,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-07-06",
    "isCurrent": true,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T09:00:00Z"
  }
}
```

### 4.6 更新学习计划进度

**请求**:

```http
PUT /api/v2/learning-plans/101/progress
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "completedDays": 7
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "改善与伴侣的沟通",
    "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
    "targetDays": 21,
    "completedDays": 7,
    "progress": 33,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-07-06",
    "isCurrent": true,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-22T09:00:00Z"
  }
}
```

### 4.7 添加学习计划标签

**请求**:

```http
POST /api/v2/learning-plans/101/tags
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "tag": {
    "id": 1003,
    "name": "表达"
  }
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "改善与伴侣的沟通",
    "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
    "targetDays": 21,
    "completedDays": 7,
    "progress": 33,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-07-06",
    "isCurrent": true,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-22T09:00:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听"
      },
      {
        "id": 1002,
        "name": "共情"
      },
      {
        "id": 1003,
        "name": "表达"
      }
    ]
  }
}
```

### 4.8 获取当前学习计划

**请求**:

```http
GET /api/v2/current-learning-plan
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 101,
    "userId": 123456789,
    "themeId": 1,
    "title": "改善与伴侣的沟通",
    "description": "通过学习倾听和共情技巧，改善与伴侣的沟通",
    "targetDays": 21,
    "completedDays": 7,
    "progress": 33,
    "status": "in_progress",
    "startDate": "2023-06-15",
    "endDate": "2023-07-06",
    "isCurrent": true,
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-22T09:00:00Z",
    "tags": [
      {
        "id": 1001,
        "name": "倾听"
      },
      {
        "id": 1002,
        "name": "共情"
      },
      {
        "id": 1003,
        "name": "表达"
      }
    ],
    "dailyContents": [
      {
        "day": 1,
        "title": "了解倾听的重要性",
        "content": "今天我们将学习倾听的重要性...",
        "isCompleted": true,
        "completionDate": "2023-06-15T10:30:00Z"
      },
      {
        "day": 2,
        "title": "练习积极倾听",
        "content": "今天我们将练习积极倾听技巧...",
        "isCompleted": true,
        "completionDate": "2023-06-16T11:15:00Z"
      }
    ]
  }
}
```

### 4.9 软删除学习计划

**请求**:

```http
DELETE /api/v2/learning-plans/101/soft-delete
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "message": "学习计划已被软删除"
  }
}
```

### 4.10 恢复已删除的学习计划

**请求**:

```http
POST /api/v2/learning-plans/101/restore
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "message": "学习计划已被恢复"
  }
}
```

## 5. 学习内容

### 5.1 获取练习列表

**请求**:

```http
GET /api/v2/exercises?tagId=1001&difficulty=beginner
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:

```json
{
  "success": true,
  "data": {
    "exercises": [
      {
        "id": 401,
        "tagId": 1001,
        "title": "积极倾听练习",
        "description": "这个练习帮助你提高积极倾听的能力...",
        "expectedResult": "通过这个练习，你将能够更好地理解他人的观点...",
        "difficulty": "beginner",
        "timeEstimateMinutes": 15,
        "isOfficial": true,
        "status": "published",
        "createdAt": "2023-06-15T08:00:00Z",
        "updatedAt": "2023-06-15T08:00:00Z"
      }
    ],
    "count": 10,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 5.2 创建练习

**请求**:

```http
POST /api/v2/exercises
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "tagId": 1001,
  "title": "深度倾听练习",
  "description": "这个练习帮助你提高深度倾听的能力",
  "content": "1. 找一个安静的环境\n2. 邀请一个朋友或家人进行对话\n3. 专注于对方说的话，不要打断\n4. 通过点头、微笑等非语言方式表示你在听\n5. 在对方说完后，复述你所理解的内容\n6. 询问是否有任何遗漏或误解",
  "expectedResult": "通过这个练习，你将能够更好地理解他人的观点",
  "difficulty": "intermediate",
  "timeEstimateMinutes": 30
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "id": 402,
    "tagId": 1001,
    "title": "深度倾听练习",
    "description": "这个练习帮助你提高深度倾听的能力",
    "content": "1. 找一个安静的环境\n2. 邀请一个朋友或家人进行对话\n3. 专注于对方说的话，不要打断\n4. 通过点头、微笑等非语言方式表示你在听\n5. 在对方说完后，复述你所理解的内容\n6. 询问是否有任何遗漏或误解",
    "expectedResult": "通过这个练习，你将能够更好地理解他人的观点",
    "difficulty": "intermediate",
    "timeEstimateMinutes": 30,
    "isOfficial": false,
    "status": "draft",
    "createdAt": "2023-06-15T08:00:00Z",
    "updatedAt": "2023-06-15T08:00:00Z"
  }
}
```