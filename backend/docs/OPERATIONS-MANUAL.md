# AIBUBB 运维手册

## 📋 目录

1. [部署指南](#部署指南)
2. [服务管理](#服务管理)
3. [监控与告警](#监控与告警)
4. [备份与恢复](#备份与恢复)
5. [性能优化](#性能优化)
6. [故障排除](#故障排除)
7. [安全管理](#安全管理)
8. [日常维护](#日常维护)

---

## 🚀 部署指南

### 生产环境部署

#### 1. 环境准备

```bash
# 检查系统要求
node --version  # >= 18.0.0
npm --version   # >= 8.0.0
mysql --version # >= 8.0
redis-server --version # >= 6.0

# 创建应用用户
sudo useradd -m -s /bin/bash aibubb
sudo usermod -aG sudo aibubb
```

#### 2. 应用部署

```bash
# 克隆代码
git clone <repository-url> /opt/aibubb
cd /opt/aibubb/backend

# 安装依赖
npm ci --production

# 配置环境变量
cp .env.production.example .env.production
# 编辑 .env.production 文件，配置实际的数据库密码、JWT密钥等

# 运行部署检查
npm run deployment:check

# 数据库迁移
npm run migration:run

# 启动服务
npm run start:pm2
```

#### 3. HTTPS配置

```bash
# 生产环境SSL证书配置
# 1. 获取SSL证书（Let's Encrypt推荐）
sudo certbot certonly --standalone -d your-domain.com

# 2. 配置证书路径
echo "HTTPS_ENABLED=true" >> .env.production
echo "SSL_CERT_PATH=/etc/letsencrypt/live/your-domain.com/fullchain.pem" >> .env.production
echo "SSL_KEY_PATH=/etc/letsencrypt/live/your-domain.com/privkey.pem" >> .env.production

# 3. 启动HTTPS服务
npm run start:https
```

---

## 🔧 服务管理

### PM2 进程管理

#### 基本命令

```bash
# 启动服务
npm run start:pm2

# 查看状态
npm run monit:pm2

# 查看日志
npm run logs:pm2

# 重启服务
npm run restart:pm2

# 优雅重载
npm run reload:pm2

# 停止服务
npm run stop:pm2
```

#### 高级管理

```bash
# 查看详细信息
npx pm2 describe aibubb-api

# 监控资源使用
npx pm2 monit

# 保存PM2配置
npx pm2 save

# 设置开机自启
npx pm2 startup
npx pm2 save

# 删除所有进程
npx pm2 delete all
```

### 集群模式

```bash
# 使用内置集群
npm run start:cluster

# 使用PM2集群
PM2_INSTANCES=4 npm run start:pm2

# 动态调整实例数
npx pm2 scale aibubb-api 6
```

---

## 📊 监控与告警

### 系统监控

#### 健康检查

```bash
# 基础健康检查
curl http://localhost:3000/health

# 详细健康检查
node scripts/health-check.js --verbose

# JSON格式输出
node scripts/health-check.js --json
```

#### 监控服务

```bash
# 启动监控服务
npm run monitoring:start

# 查看监控数据
curl http://localhost:3000/api/v2/monitoring/metrics

# 查看告警历史
curl http://localhost:3000/api/v2/monitoring/alerts
```

### 性能监控

```bash
# 建立性能基线
npm run perf:baseline

# 运行性能测试
npm run perf:test:load

# 分析性能结果
npm run perf:analyze
```

### 日志管理

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看PM2日志
tail -f logs/pm2/combined.log

# 日志轮转（手动）
logrotate -f /etc/logrotate.d/aibubb
```

---

## 💾 备份与恢复

### 自动备份

```bash
# 启动备份调度
npm run backup:start

# 查看备份状态
npm run backup:status

# 手动触发备份
npm run backup:trigger
```

### 手动备份

```bash
# 完整备份
npm run backup:manual

# 仅数据库备份
node scripts/backup.js database

# 查看备份列表
node scripts/backup.js list

# 备份统计
node scripts/backup.js stats
```

### 数据恢复

```bash
# 恢复数据库
mysql -u root -p aibubb < backups/database-backup-YYYY-MM-DD.sql

# 恢复文件
tar -xzf backups/full-backup-YYYY-MM-DD.tar.gz -C /

# 验证恢复
npm run deployment:check
```

---

## ⚡ 性能优化

### 数据库优化

```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 分析查询性能
EXPLAIN SELECT * FROM learning_plans WHERE user_id = 1;

-- 优化索引
SHOW INDEX FROM learning_plans;
```

### 缓存优化

```bash
# 查看Redis状态
redis-cli info memory

# 清理缓存
redis-cli flushdb

# 监控缓存命中率
redis-cli info stats | grep keyspace
```

### 应用优化

```bash
# 分析内存使用
node --inspect server.js

# 生成堆快照
kill -USR2 <pid>

# 性能分析
npm run perf:test:all
```

---

## 🔍 故障排除

### 常见问题

#### 1. 服务无法启动

```bash
# 检查端口占用
lsof -i :3000

# 检查环境变量
node -e "console.log(process.env)"

# 检查依赖
npm ls

# 查看详细错误
npm start 2>&1 | tee startup.log
```

#### 2. 数据库连接失败

```bash
# 测试数据库连接
mysql -h localhost -u aibubb_user -p aibubb

# 检查数据库状态
systemctl status mysql

# 查看数据库日志
tail -f /var/log/mysql/error.log
```

#### 3. Redis连接失败

```bash
# 测试Redis连接
redis-cli ping

# 检查Redis状态
systemctl status redis

# 查看Redis日志
tail -f /var/log/redis/redis-server.log
```

#### 4. 内存泄漏

```bash
# 监控内存使用
npx pm2 monit

# 生成内存报告
node --inspect --inspect-port=9229 server.js

# 分析堆快照
node --max-old-space-size=4096 server.js
```

### 日志分析

```bash
# 错误日志分析
grep -i error logs/app.log | tail -20

# 性能日志分析
grep "slow" logs/app.log

# 访问日志分析
awk '{print $1}' logs/access.log | sort | uniq -c | sort -nr
```

---

## 🔒 安全管理

### SSL证书管理

```bash
# 检查证书有效期
openssl x509 -in ssl/certificate.crt -text -noout | grep "Not After"

# 自动续期（Let's Encrypt）
certbot renew --dry-run

# 手动续期
certbot renew
```

### 安全扫描

```bash
# 依赖安全扫描
npm audit

# 修复安全漏洞
npm audit fix

# 代码安全扫描
npm run security:all
```

### 访问控制

```bash
# 查看活跃连接
netstat -an | grep :3000

# 防火墙配置
ufw allow 3000/tcp
ufw allow 443/tcp
ufw enable
```

---

## 🔄 日常维护

### 每日检查

```bash
#!/bin/bash
# daily-check.sh

echo "=== 每日系统检查 ==="

# 1. 服务状态检查
echo "检查服务状态..."
npm run deployment:check

# 2. 健康检查
echo "执行健康检查..."
node scripts/health-check.js

# 3. 备份状态检查
echo "检查备份状态..."
npm run backup:status

# 4. 磁盘空间检查
echo "检查磁盘空间..."
df -h

# 5. 内存使用检查
echo "检查内存使用..."
free -h

# 6. 日志检查
echo "检查错误日志..."
grep -i error logs/app.log | tail -5
```

### 每周维护

```bash
#!/bin/bash
# weekly-maintenance.sh

echo "=== 每周系统维护 ==="

# 1. 系统更新
sudo apt update && sudo apt upgrade -y

# 2. 清理日志
find logs/ -name "*.log" -mtime +7 -delete

# 3. 清理备份
node scripts/backup.js cleanup

# 4. 性能测试
npm run perf:test:quick

# 5. 安全扫描
npm audit
```

### 每月维护

```bash
#!/bin/bash
# monthly-maintenance.sh

echo "=== 每月系统维护 ==="

# 1. 完整备份
npm run backup:trigger

# 2. 性能基线更新
npm run perf:baseline

# 3. 证书检查
openssl x509 -in ssl/certificate.crt -checkend 2592000

# 4. 数据库优化
mysql -u root -p -e "OPTIMIZE TABLE learning_plans, users, themes, tags;"

# 5. 生成月度报告
node scripts/generate-monthly-report.js
```

---

## 📞 紧急联系

### 故障响应流程

1. **立即响应** (0-15分钟)
   - 确认故障范围
   - 启动应急预案
   - 通知相关人员

2. **问题诊断** (15-30分钟)
   - 查看监控数据
   - 分析日志文件
   - 确定根本原因

3. **故障修复** (30分钟-2小时)
   - 实施修复方案
   - 验证修复效果
   - 恢复服务正常

4. **事后总结** (24小时内)
   - 编写故障报告
   - 优化监控告警
   - 更新应急预案

### 联系方式

- **技术负责人**: <EMAIL>
- **运维团队**: <EMAIL>
- **紧急热线**: +86-xxx-xxxx-xxxx

---

## 📚 相关文档

- [部署检查清单](./DEPLOYMENT-CHECKLIST.md)
- [API文档](./API-DOCUMENTATION-REAL.md)
- [安全编码指南](./SECURITY-CODING-GUIDE.md)
- [代码审查清单](./CODE-REVIEW-CHECKLIST.md)

---

**最后更新**: 2025年5月27日
**版本**: v1.0.0
**维护者**: AIBUBB运维团队