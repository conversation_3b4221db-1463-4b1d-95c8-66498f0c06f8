# AIBUBB安全编码指南

## 概述

本文档提供了AIBUBB项目的安全编码指南，旨在帮助开发人员编写安全的代码，防止常见的安全漏洞。所有开发人员都应遵循这些指南，以确保系统的安全性。

## 1. 输入验证

### 1.1 始终验证用户输入

所有来自用户的输入都应该被视为不可信的，必须进行验证。使用Joi或类似的验证库来验证输入数据。

```javascript
// 使用Joi验证用户输入
const schema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).required(),
  password: Joi.string().pattern(new RegExp('^[a-zA-Z0-9]{8,30}$')).required(),
  email: Joi.string().email().required()
});

const { error, value } = schema.validate(req.body);
if (error) {
  return res.status(400).json({ error: error.details[0].message });
}
```

### 1.2 使用白名单而非黑名单

在验证输入时，应该使用白名单方法（指定允许的内容），而不是黑名单方法（指定不允许的内容）。

```javascript
// 好的做法：使用白名单
const allowedRoles = ['user', 'admin', 'editor'];
if (!allowedRoles.includes(role)) {
  return res.status(400).json({ error: '无效的角色' });
}

// 不好的做法：使用黑名单
if (role === 'superadmin' || role === 'root') {
  return res.status(400).json({ error: '无效的角色' });
}
```

## 2. SQL注入防护

### 2.1 使用参数化查询

始终使用参数化查询或ORM（如Sequelize）来防止SQL注入攻击。

```javascript
// 好的做法：使用Sequelize
const user = await User.findOne({
  where: { username: username }
});

// 好的做法：使用参数化查询
const [user] = await db.query(
  'SELECT * FROM users WHERE username = ?',
  [username]
);

// 不好的做法：直接拼接SQL
const user = await db.query(
  `SELECT * FROM users WHERE username = '${username}'` // 危险！
);
```

### 2.2 限制数据库用户权限

确保应用程序使用的数据库用户只有必要的权限，遵循最小权限原则。

## 3. 跨站脚本(XSS)防护

### 3.1 输出编码

在将数据输出到HTML、JavaScript、CSS或URL时，始终进行适当的编码。

```javascript
// 使用DOMPurify清理HTML内容
const clean = DOMPurify.sanitize(dirtyHTML);

// 对于API响应，确保内容类型正确
res.setHeader('Content-Type', 'application/json');
```

### 3.2 使用内容安全策略(CSP)

配置内容安全策略头，限制可执行的脚本来源。

```javascript
// 在Express中使用helmet设置CSP
app.use(helmet.contentSecurityPolicy({
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "trusted-cdn.com"]
  }
}));
```

## 4. 跨站请求伪造(CSRF)防护

### 4.1 使用CSRF令牌

对所有修改数据的请求使用CSRF令牌。

```javascript
// 在Express中使用csurf中间件
const csrf = require('csurf');
const csrfProtection = csrf({ cookie: true });

app.post('/api/profile', csrfProtection, (req, res) => {
  // 处理请求
});
```

### 4.2 验证来源

检查请求的Origin或Referer头，确保请求来自预期的来源。

```javascript
// 验证请求来源
const allowedOrigins = ['https://aibubb.com', 'https://www.aibubb.com'];
const origin = req.headers.origin;
if (!allowedOrigins.includes(origin)) {
  return res.status(403).json({ error: '禁止访问' });
}
```

## 5. 敏感数据处理

### 5.1 密码存储

始终使用强哈希算法（如bcrypt）存储密码，而不是明文或简单哈希。

```javascript
// 使用bcrypt哈希密码
const bcrypt = require('bcrypt');
const saltRounds = 10;

// 哈希密码
const hashedPassword = await bcrypt.hash(password, saltRounds);

// 验证密码
const match = await bcrypt.compare(password, hashedPassword);
```

### 5.2 敏感数据传输

确保敏感数据通过HTTPS传输，并在日志中屏蔽敏感信息。

```javascript
// 在日志中屏蔽敏感信息
const logUser = { ...user };
delete logUser.password;
delete logUser.creditCard;
logger.info('用户登录', logUser);
```

## 6. 会话管理

### 6.1 安全的会话配置

配置安全的会话选项，包括HttpOnly、Secure和SameSite属性。

```javascript
// 在Express中配置安全的会话
app.use(session({
  secret: process.env.SESSION_SECRET,
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 3600000 // 1小时
  },
  resave: false,
  saveUninitialized: false
}));
```

### 6.2 会话过期和轮换

实现会话过期和定期轮换，以减少会话劫持的风险。

## 7. 错误处理和日志

### 7.1 安全的错误处理

不要向用户暴露敏感的错误详情，使用通用错误消息。

```javascript
// 好的做法：通用错误消息
app.use((err, req, res, next) => {
  logger.error('应用错误', { error: err.stack });
  res.status(500).json({ error: '服务器内部错误' });
});

// 不好的做法：暴露详细错误
app.use((err, req, res, next) => {
  res.status(500).json({ error: err.stack }); // 危险！
});
```

### 7.2 安全日志记录

记录安全相关事件，但不要记录敏感数据。

```javascript
// 记录安全事件
logger.info('用户登录', { userId: user.id, ip: req.ip });

// 记录安全警告
logger.warn('多次登录失败', { username: username, ip: req.ip, attempts: attempts });
```

## 8. 依赖管理

### 8.1 定期更新依赖

定期更新依赖包，修复已知的安全漏洞。

```bash
# 检查过时的依赖
npm outdated

# 更新依赖
npm update

# 检查安全漏洞
npm audit

# 修复安全漏洞
npm audit fix
```

### 8.2 使用锁定文件

使用package-lock.json或yarn.lock锁定依赖版本，确保一致的依赖。

## 9. API安全

### 9.1 使用速率限制

实现API速率限制，防止暴力攻击和DoS攻击。

```javascript
// 使用express-rate-limit实现速率限制
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: '请求过多，请稍后再试'
});

app.use('/api/', apiLimiter);
```

### 9.2 实现适当的认证和授权

确保API端点有适当的认证和授权机制。

```javascript
// 验证用户是否有权限
function checkPermission(permission) {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      return res.status(403).json({ error: '没有权限' });
    }
    next();
  };
}

app.delete('/api/users/:id', authenticate, checkPermission('delete:users'), (req, res) => {
  // 处理请求
});
```

## 10. 安全代码审查清单

在提交代码前，使用以下清单进行自我审查：

- [ ] 所有用户输入都经过验证
- [ ] 使用参数化查询或ORM防止SQL注入
- [ ] 输出数据经过适当编码，防止XSS
- [ ] 实现了CSRF保护措施
- [ ] 敏感数据（如密码）使用强哈希算法存储
- [ ] 不在日志中记录敏感信息
- [ ] 会话配置安全（HttpOnly、Secure、SameSite）
- [ ] 错误处理不暴露敏感信息
- [ ] 实现了适当的认证和授权
- [ ] 使用最新的依赖版本，没有已知安全漏洞

## 参考资料

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Node.js安全最佳实践](https://nodejs.org/en/security/)
- [Express安全最佳实践](https://expressjs.com/en/advanced/best-practice-security.html)
- [OWASP安全编码实践](https://owasp.org/www-project-secure-coding-practices-quick-reference-guide/)
