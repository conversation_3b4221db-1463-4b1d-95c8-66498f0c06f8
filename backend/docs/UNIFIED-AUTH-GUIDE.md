# 统一授权中间件使用指南

## 1. 概述

统一授权中间件提供了集中化的授权逻辑，支持基于角色和权限的细粒度控制。它解决了授权逻辑分散在控制器中的问题，提供了一种声明式的授权方式，使授权逻辑更易于维护和审计。

## 2. 主要特性

- **基于角色和权限的授权**：支持基于角色和权限的细粒度控制
- **资源所有者检查**：支持检查用户是否是资源的所有者
- **公开资源检查**：支持检查资源是否是公开的
- **管理员覆盖**：支持管理员覆盖普通用户的权限限制
- **声明式授权**：通过配置文件定义授权规则，使授权逻辑更易于维护

## 3. 使用方法

### 3.1 基本用法

在路由中使用统一授权中间件：

```javascript
const { authorize } = require('../middlewares/unified-auth.middleware');

// 获取笔记详情
router.get(
  '/notes/:id',
  authMiddleware,
  authorize('note', 'read'),
  noteController.getNoteById
);

// 更新笔记
router.put(
  '/notes/:id',
  authMiddleware,
  authorize('note', 'update'),
  noteController.updateNote
);
```

### 3.2 参数说明

`authorize(resource, action, options)`函数接受以下参数：

- `resource`：资源名称，如'note'、'user'、'tag'等
- `action`：操作名称，如'create'、'read'、'update'、'delete'等
- `options`：额外选项，包括：
  - `resourceIdParam`：资源ID参数名，默认为'id'

### 3.3 授权规则配置

授权规则在`backend/config/authorization-config.js`文件中定义，包括：

- **资源授权规则**：定义每个资源的不同操作所需的权限和检查
- **角色权限映射**：定义每个角色拥有的权限

示例：

```javascript
// 资源授权规则
note: {
  // 创建笔记需要'note:create'权限
  create: { permissions: ['note:create'] },
  // 更新笔记需要'note:update'权限或者是资源所有者
  update: { permissions: ['note:update'], ownerCheck: true },
  // 删除笔记需要'note:delete'权限或者是资源所有者
  delete: { permissions: ['note:delete'], ownerCheck: true },
  // 查看笔记需要'note:read'权限或者是资源所有者或者笔记是公开的
  read: { permissions: ['note:read'], ownerCheck: true, publicCheck: true }
}
```

### 3.4 授权规则选项

每个授权规则可以包含以下选项：

- `permissions`：所需的权限列表
- `ownerCheck`：是否检查用户是否是资源的所有者
- `publicCheck`：是否检查资源是否是公开的
- `adminOnly`：是否只允许管理员访问
- `adminOverride`：是否允许管理员覆盖普通用户的权限限制

## 4. 资源所有者检查

资源所有者检查函数定义在`ownerCheckFunctions`对象中，用于检查用户是否是资源的所有者。

示例：

```javascript
// 笔记所有者检查
note: async (req, resourceId) => {
  const note = await req.app.get('db').Note.findByPk(resourceId);
  if (!note) return false;
  return note.user_id === req.user.userId;
}
```

## 5. 公开资源检查

公开资源检查函数定义在`publicCheckFunctions`对象中，用于检查资源是否是公开的。

示例：

```javascript
// 笔记公开检查
note: async (req, resourceId) => {
  const note = await req.app.get('db').Note.findByPk(resourceId);
  if (!note) return false;
  return note.status === 'published';
}
```

## 6. 添加新的资源和操作

要添加新的资源和操作，需要：

1. 在`authorization-config.js`文件中添加新的资源和操作
2. 在`ownerCheckFunctions`对象中添加新的资源所有者检查函数（如果需要）
3. 在`publicCheckFunctions`对象中添加新的公开资源检查函数（如果需要）

## 7. 最佳实践

- **使用声明式授权**：尽量使用声明式授权，而不是在控制器中编写授权逻辑
- **定义清晰的资源和操作**：为每个资源定义清晰的操作，避免混淆
- **使用资源所有者检查**：对于用户自己的资源，应该使用资源所有者检查
- **使用公开资源检查**：对于可能公开的资源，应该使用公开资源检查
- **避免重复授权检查**：避免在控制器中重复授权检查，统一使用授权中间件

## 8. 示例

### 8.1 笔记路由示例

```javascript
// 获取笔记详情
router.get(
  '/notes/:id',
  authMiddleware,
  authorize('note', 'read'),
  noteController.getNoteById
);

// 更新笔记
router.put(
  '/notes/:id',
  authMiddleware,
  authorize('note', 'update'),
  noteController.updateNote
);

// 删除笔记
router.delete(
  '/notes/:id',
  authMiddleware,
  authorize('note', 'delete'),
  noteController.deleteNote
);
```

### 8.2 用户路由示例

```javascript
// 获取用户详情
router.get(
  '/users/:id',
  authMiddleware,
  authorize('user', 'read'),
  userController.getUserById
);

// 创建用户（仅管理员）
router.post(
  '/users',
  authMiddleware,
  authorize('user', 'create'),
  userController.createUser
);

// 更新用户信息
router.put(
  '/users/:id',
  authMiddleware,
  authorize('user', 'update'),
  userController.updateUser
);
```

## 9. 故障排除

如果授权检查失败，可能的原因包括：

- 用户没有所需的权限
- 用户不是资源的所有者
- 资源不是公开的
- 授权规则配置错误
- 资源所有者检查函数或公开资源检查函数出错

检查日志中的错误信息，可以帮助定位问题。
