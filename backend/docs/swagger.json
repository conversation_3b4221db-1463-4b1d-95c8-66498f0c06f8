{"openapi": "3.0.0", "info": {"title": "AIBUBB API", "version": "2.1.1", "description": "AIBUBB AI辅助学习平台API文档"}, "servers": [{"url": "http://localhost:3000", "description": "开发环境"}, {"url": "https://api.aibubb.com", "description": "生产环境"}], "paths": {"/ai/stats": {"get": {"summary": "获取AI使用统计信息", "tags": ["AI"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功获取AI统计信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"provider": {"type": "string", "example": "deepseek"}, "model": {"type": "string", "example": "deepseek-r1-250120"}, "stats": {"type": "object", "properties": {"requestCount": {"type": "integer", "example": 10}, "errorCount": {"type": "integer", "example": 1}, "tokenUsage": {"type": "object", "properties": {"prompt": {"type": "integer", "example": 1200}, "completion": {"type": "integer", "example": 800}, "total": {"type": "integer", "example": 2000}}}, "errorRate": {"type": "number", "example": 0.1}, "estimatedCost": {"type": "object", "properties": {"promptCost": {"type": "number", "example": 0.0024}, "completionCost": {"type": "number", "example": 0.0016}, "totalCost": {"type": "number", "example": 0.004}, "currency": {"type": "string", "example": "USD"}}}}}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/ai/stats/reset": {"post": {"summary": "重置AI使用统计信息", "tags": ["AI"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功重置AI统计信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "AI统计信息已重置"}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/ai/test/tags": {"post": {"summary": "测试AI标签生成功能", "tags": ["AI"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "description": "学习计划标题", "example": "提升与伴侣的沟通能力"}, "description": {"type": "string", "description": "学习计划描述", "example": "我希望能更好地与伴侣沟通，减少误解和冲突"}, "themeId": {"type": "integer", "description": "主题ID", "example": 1}}}}}}, "responses": {"200": {"description": "成功生成标签", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"plan": {"type": "object", "properties": {"title": {"type": "string", "example": "提升与伴侣的沟通能力"}, "description": {"type": "string", "example": "我希望能更好地与伴侣沟通，减少误解和冲突"}, "theme": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "人际沟通"}}}}}, "tags": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "倾听"}, "relevanceScore": {"type": "number", "example": 0.95}, "sortOrder": {"type": "integer", "example": 0}}}}, "stats": {"type": "object", "properties": {"requestCount": {"type": "integer", "example": 1}}}}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/ai/learning-plans/generate": {"post": {"summary": "生成学习计划内容", "tags": ["AI"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "description": "学习计划标题", "example": "如何提高沟通效率"}, "trouble": {"type": "string", "description": "学习者的困扰/问题", "example": "在团队讨论中经常无法清晰表达自己的想法"}, "learningIntensity": {"type": "string", "enum": ["easy", "medium", "hard"], "description": "学习强度", "example": "medium"}, "learningDuration": {"type": "integer", "description": "学习天数", "example": 7}}}}}}, "responses": {"200": {"description": "成功生成学习计划内容", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"planContent": {"type": "object", "properties": {"enhancedTitle": {"type": "string", "example": "高效团队沟通技巧掌握"}, "designPrinciple": {"type": "string", "example": "本学习计划采用循序渐进的方式..."}, "contentPlan": {"type": "array", "items": {"type": "object", "properties": {"day": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "沟通基础与自我认知"}, "content": {"type": "string", "example": "今天我们将学习沟通的基本模型..."}}}}, "tags": {"type": "array", "items": {"type": "string", "example": "表达力"}}}}}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/batch/tags/soft-delete": {"post": {"summary": "批量软删除标签", "description": "批量软删除指定ID的标签", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "标签ID数组"}, "cascade": {"type": "boolean", "description": "是否级联删除相关数据", "default": false}}}}}}, "responses": {"200": {"description": "标签已被批量软删除"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/batch/tags/restore": {"post": {"summary": "批量恢复标签", "description": "批量恢复已软删除的标签", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "标签ID数组"}}}}}}, "responses": {"200": {"description": "标签已被批量恢复"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/batch/users/soft-delete": {"post": {"summary": "批量软删除用户", "description": "批量软删除指定ID的用户", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "string"}, "description": "用户ID数组"}, "cascade": {"type": "boolean", "description": "是否级联删除相关数据", "default": false}}}}}}, "responses": {"200": {"description": "用户已被批量软删除"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/batch/users/restore": {"post": {"summary": "批量恢复用户", "description": "批量恢复已软删除的用户", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "string"}, "description": "用户ID数组"}}}}}}, "responses": {"200": {"description": "用户已被批量恢复"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/batch/notes/soft-delete": {"post": {"summary": "批量软删除笔记", "description": "批量软删除指定ID的笔记", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "笔记ID数组"}}}}}}, "responses": {"200": {"description": "笔记已被批量软删除"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "500": {"description": "服务器错误"}}}}, "/api/v2/batch/notes/restore": {"post": {"summary": "批量恢复笔记", "description": "批量恢复已软删除的笔记", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "笔记ID数组"}}}}}}, "responses": {"200": {"description": "笔记已被批量恢复"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "500": {"description": "服务器错误"}}}}, "/api/v2/batch/insights/soft-delete": {"post": {"summary": "批量软删除观点", "description": "批量软删除指定ID的观点", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "观点ID数组"}}}}}}, "responses": {"200": {"description": "观点已被批量软删除"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "500": {"description": "服务器错误"}}}}, "/api/v2/batch/insights/restore": {"post": {"summary": "批量恢复观点", "description": "批量恢复已软删除的观点", "tags": ["BatchOperations"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "观点ID数组"}}}}}}, "responses": {"200": {"description": "观点已被批量恢复"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "500": {"description": "服务器错误"}}}}, "/api/v2/cleanup/config": {"get": {"summary": "获取清理配置", "description": "获取软删除数据的清理配置", "tags": ["Cleanup"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功获取清理配置"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}, "put": {"summary": "更新清理配置", "description": "更新软删除数据的清理配置", "tags": ["Cleanup"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"retentionDays": {"type": "integer", "description": "保留已删除数据的天数", "minimum": 1, "maximum": 365}, "batchSize": {"type": "integer", "description": "每次批处理的记录数", "minimum": 10, "maximum": 1000}, "autoCleanupEnabled": {"type": "boolean", "description": "是否启用自动清理"}, "cleanupInterval": {"type": "integer", "description": "自动清理的时间间隔（毫秒）", "minimum": 3600000, "maximum": 2592000000}, "modelConfigs": {"type": "object", "description": "模型特定配置"}}}}}}, "responses": {"200": {"description": "成功更新清理配置"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/cleanup/run": {"post": {"summary": "手动执行清理", "description": "手动执行软删除数据的清理", "tags": ["Cleanup"], "security": [{"bearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"modelName": {"type": "string", "description": "要清理的模型名称，不指定则清理所有模型"}, "retentionDays": {"type": "integer", "description": "保留已删除数据的天数，不指定则使用配置值", "minimum": 1, "maximum": 365}}}}}}, "responses": {"200": {"description": "清理成功"}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "模型不存在"}, "500": {"description": "服务器错误"}}}}, "/api/v2/cleanup/start": {"post": {"summary": "启动自动清理", "description": "启动软删除数据的自动清理", "tags": ["Cleanup"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "自动清理已启动"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/cleanup/stop": {"post": {"summary": "停止自动清理", "description": "停止软删除数据的自动清理", "tags": ["Cleanup"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "自动清理已停止"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/daily-contents/plan/{planId}": {"get": {"summary": "获取学习计划的每日内容列表", "description": "获取指定学习计划的所有每日内容", "tags": ["DailyContents"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "planId", "required": true, "schema": {"type": "integer"}, "description": "学习计划ID"}], "responses": {"200": {"description": "成功获取每日内容列表"}, "401": {"description": "未授权"}, "404": {"description": "学习计划不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/api/v2/daily-contents/{id}": {"get": {"summary": "获取单个每日内容", "description": "根据ID获取每日内容详情", "tags": ["DailyContents"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "每日内容ID"}], "responses": {"200": {"description": "成功获取每日内容详情"}, "401": {"description": "未授权"}, "404": {"description": "每日内容不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}, "put": {"summary": "更新每日内容", "description": "更新指定ID的每日内容", "tags": ["DailyContents"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "每日内容ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "content": {"type": "string"}, "summary": {"type": "string"}, "difficulty": {"type": "integer"}, "isCompleted": {"type": "boolean"}, "status": {"type": "string"}}}}}}, "responses": {"200": {"description": "成功更新每日内容"}, "401": {"description": "未授权"}, "404": {"description": "每日内容不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/api/v2/daily-contents/{id}/soft-delete": {"delete": {"summary": "软删除每日内容", "description": "软删除指定ID的每日内容", "tags": ["DailyContents"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "每日内容ID"}], "responses": {"200": {"description": "每日内容已被软删除"}, "401": {"description": "未授权"}, "404": {"description": "每日内容不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/api/v2/daily-contents/{id}/restore": {"post": {"summary": "恢复已删除的每日内容", "description": "恢复已软删除的每日内容", "tags": ["DailyContents"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "每日内容ID"}], "responses": {"200": {"description": "每日内容已恢复"}, "400": {"description": "每日内容未被删除，无需恢复"}, "401": {"description": "未授权"}, "404": {"description": "每日内容不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/api/v2/daily-contents/plan/{planId}/deleted": {"get": {"summary": "获取已删除的每日内容列表", "description": "获取指定学习计划的所有已软删除的每日内容", "tags": ["DailyContents"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "planId", "required": true, "schema": {"type": "integer"}, "description": "学习计划ID"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "default": 10}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取已删除的每日内容列表"}, "401": {"description": "未授权"}, "404": {"description": "学习计划不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/health": {"get": {"summary": "健康检查端点", "description": "返回服务健康状态信息，用于监控和容器健康检查", "tags": ["System"], "responses": {"200": {"description": "服务健康", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "version": {"type": "string", "example": "1.0.0"}, "timestamp": {"type": "string", "format": "date-time"}, "uptime": {"type": "number", "example": 3600}, "services": {"type": "object", "properties": {"database": {"type": "string", "example": "connected"}, "redis": {"type": "string", "example": "connected"}}}}}}}}, "503": {"description": "服务不健康", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "message": {"type": "string", "example": "Service unhealthy"}, "timestamp": {"type": "string", "format": "date-time"}, "services": {"type": "object", "properties": {"database": {"type": "string", "example": "disconnected"}, "redis": {"type": "string", "example": "connected"}}}}}}}}}}}, "/health/liveness": {"get": {"summary": "存活检查端点", "description": "简单的存活检查，用于Kubernetes liveness probe", "tags": ["System"], "responses": {"200": {"description": "服务存活", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}}}}}}}}}, "/health/readiness": {"get": {"summary": "就绪检查端点", "description": "检查服务是否准备好处理请求，用于Kubernetes readiness probe", "tags": ["System"], "responses": {"200": {"description": "服务就绪", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}}}}}}, "503": {"description": "服务未就绪", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "message": {"type": "string", "example": "Service not ready"}}}}}}}}}, "/health/database": {"get": {"summary": "数据库健康检查", "description": "检查数据库连接状态和性能", "tags": ["System"], "responses": {"200": {"description": "数据库健康"}, "500": {"description": "数据库不健康"}}}}, "/health/redis": {"get": {"summary": "Redis健康检查", "description": "检查Redis连接状态和性能", "tags": ["System"], "responses": {"200": {"description": "Redis健康"}, "500": {"description": "Redis不健康"}}}}, "/health/system": {"get": {"summary": "系统健康检查", "description": "检查系统资源使用情况", "tags": ["System"], "responses": {"200": {"description": "系统健康"}, "500": {"description": "系统不健康"}}}}, "/health/full": {"get": {"summary": "综合健康检查", "description": "检查所有服务的健康状态", "tags": ["System"], "responses": {"200": {"description": "所有服务健康或部分服务降级"}, "500": {"description": "服务不健康"}}}}, "/api/v2/learning-plans": {"get": {"summary": "获取用户的学习计划列表", "description": "获取当前用户的所有学习计划", "tags": ["LearningPlans"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "default": 10}, "description": "每页记录数"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["active", "completed", "archived"]}, "description": "学习计划状态"}, {"in": "query", "name": "sortBy", "schema": {"type": "string", "enum": ["createdAt", "updatedAt", "title"], "default": "updatedAt"}, "description": "排序字段"}, {"in": "query", "name": "sortOrder", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}, "description": "排序方向"}], "responses": {"200": {"description": "成功获取学习计划列表"}, "401": {"description": "未授权"}, "500": {"description": "服务器错误"}}}, "post": {"summary": "创建学习计划", "description": "创建新的学习计划", "tags": ["LearningPlans"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title", "description", "themeId"], "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "themeId": {"type": "integer"}, "duration": {"type": "integer"}, "status": {"type": "string", "enum": ["active", "completed", "archived"], "default": "active"}}}}}}, "responses": {"201": {"description": "成功创建学习计划"}, "401": {"description": "未授权"}, "500": {"description": "服务器错误"}}}}, "/api/v2/learning-plans/{id}": {"get": {"summary": "获取单个学习计划", "description": "根据ID获取学习计划详情", "tags": ["LearningPlans"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "学习计划ID"}], "responses": {"200": {"description": "成功获取学习计划详情"}, "401": {"description": "未授权"}, "404": {"description": "学习计划不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}, "put": {"summary": "更新学习计划", "description": "更新指定ID的学习计划", "tags": ["LearningPlans"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "学习计划ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "themeId": {"type": "integer"}, "duration": {"type": "integer"}, "status": {"type": "string", "enum": ["active", "completed", "archived"]}}}}}}, "responses": {"200": {"description": "成功更新学习计划"}, "401": {"description": "未授权"}, "404": {"description": "学习计划不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/api/v2/learning-plans/{id}/soft-delete": {"delete": {"summary": "软删除学习计划", "description": "软删除指定ID的学习计划", "tags": ["LearningPlans"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "学习计划ID"}], "responses": {"200": {"description": "学习计划已被软删除"}, "401": {"description": "未授权"}, "404": {"description": "学习计划不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/api/v2/learning-plans/{id}/restore": {"post": {"summary": "恢复已删除的学习计划", "description": "恢复已软删除的学习计划", "tags": ["LearningPlans"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "学习计划ID"}], "responses": {"200": {"description": "学习计划已恢复"}, "400": {"description": "学习计划未被删除，无需恢复"}, "401": {"description": "未授权"}, "404": {"description": "学习计划不存在或不属于当前用户"}, "500": {"description": "服务器错误"}}}}, "/api/v2/learning-plans/deleted": {"get": {"summary": "获取已删除的学习计划列表", "description": "获取当前用户的所有已软删除的学习计划", "tags": ["LearningPlans"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "default": 10}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取已删除的学习计划列表"}, "401": {"description": "未授权"}, "500": {"description": "服务器错误"}}}}, "/api/v2/statistics/monitor/metrics": {"get": {"summary": "获取统计模块性能指标", "description": "获取统计模块的整体性能指标", "tags": ["Statistics Monitor"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功获取性能指标", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"requestCount": {"type": "integer", "example": 1000}, "totalResponseTime": {"type": "integer", "example": 50000}, "maxResponseTime": {"type": "integer", "example": 500}, "errorCount": {"type": "integer", "example": 10}, "lastResetTime": {"type": "integer", "example": 1620000000000}, "avgResponseTime": {"type": "number", "example": 50}, "errorRate": {"type": "number", "example": 1}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/statistics/monitor/endpoints": {"get": {"summary": "获取所有端点性能指标", "description": "获取统计模块所有端点的性能指标", "tags": ["Statistics Monitor"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功获取所有端点性能指标", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "additionalProperties": {"type": "object", "properties": {"requestCount": {"type": "integer", "example": 500}, "totalResponseTime": {"type": "integer", "example": 25000}, "maxResponseTime": {"type": "integer", "example": 300}, "errorCount": {"type": "integer", "example": 5}, "avgResponseTime": {"type": "number", "example": 50}, "errorRate": {"type": "number", "example": 1}}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/statistics/monitor/endpoints/{endpoint}": {"get": {"summary": "获取特定端点性能指标", "description": "获取统计模块特定端点的性能指标", "tags": ["Statistics Monitor"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "endpoint", "schema": {"type": "string"}, "required": true, "description": "端点名称"}], "responses": {"200": {"description": "成功获取端点性能指标", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"requestCount": {"type": "integer", "example": 500}, "totalResponseTime": {"type": "integer", "example": 25000}, "maxResponseTime": {"type": "integer", "example": 300}, "errorCount": {"type": "integer", "example": 5}, "avgResponseTime": {"type": "number", "example": 50}, "errorRate": {"type": "number", "example": 1}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/statistics/monitor/slow-requests": {"get": {"summary": "获取慢请求列表", "description": "获取统计模块的慢请求列表", "tags": ["Statistics Monitor"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "limit", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "返回的慢请求数量"}], "responses": {"200": {"description": "成功获取慢请求列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"slowRequests": {"type": "array", "items": {"type": "object", "properties": {"endpoint": {"type": "string", "example": "GET /statistics/learning"}, "responseTime": {"type": "integer", "example": 1500}, "timestamp": {"type": "integer", "example": 1620000000000}}}}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/statistics/monitor/reset": {"post": {"summary": "重置性能指标", "description": "重置统计模块的性能指标", "tags": ["Statistics Monitor"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功重置性能指标", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "性能指标已重置"}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/statistics/learning": {"get": {"summary": "获取学习统计数据", "description": "获取用户的学习统计数据，包括学习天数、连续天数、完成练习数等", "tags": ["Statistics"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功获取学习统计数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"totalStudyDays": {"type": "integer", "example": 15}, "currentStreak": {"type": "integer", "example": 3}, "longestStreak": {"type": "integer", "example": 7}, "completedExercises": {"type": "integer", "example": 25}, "viewedInsights": {"type": "integer", "example": 42}, "createdNotes": {"type": "integer", "example": 10}, "totalTimeSpent": {"type": "integer", "example": 320}, "activePlans": {"type": "integer", "example": 2}, "completedPlans": {"type": "integer", "example": 1}, "activityStats": {"type": "object", "example": {"login": 30, "view_exercise": 50, "complete_exercise": 25, "view_insight": 42, "create_note": 10}}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/statistics/daily": {"get": {"summary": "获取每日学习记录", "description": "获取用户的每日学习记录，包括学习时间、完成练习数等", "tags": ["Statistics"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "startDate", "schema": {"type": "string", "format": "date"}, "description": "开始日期（YYYY-MM-DD）"}, {"in": "query", "name": "endDate", "schema": {"type": "string", "format": "date"}, "description": "结束日期（YYYY-MM-DD）"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 30}, "description": "每页记录数"}, {"in": "query", "name": "offset", "schema": {"type": "integer", "minimum": 0, "default": 0}, "description": "偏移量"}], "responses": {"200": {"description": "成功获取每日学习记录", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date", "example": "2023-06-15"}, "timeSpent": {"type": "integer", "example": 30}, "exercisesCompleted": {"type": "integer", "example": 2}, "insightsViewed": {"type": "integer", "example": 5}, "notesCreated": {"type": "integer", "example": 1}, "bubbleInteractions": {"type": "integer", "example": 3}, "hasActivity": {"type": "boolean", "example": true}}}}, "count": {"type": "integer", "example": 30}, "limit": {"type": "integer", "example": 30}, "offset": {"type": "integer", "example": 0}, "totalPages": {"type": "integer", "example": 1}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/statistics/activities": {"post": {"summary": "记录学习活动", "description": "记录用户的学习活动，如查看练习、完成练习等", "tags": ["Statistics"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["activityType"], "properties": {"activityType": {"type": "string", "enum": ["login", "view_exercise", "complete_exercise", "view_insight", "create_note", "like_note", "comment_note", "bubble_interaction", "share_content"], "description": "活动类型"}, "planId": {"type": "integer", "description": "学习计划ID"}, "contentType": {"type": "string", "enum": ["exercise", "insight", "note", "tag", "plan", "bubble"], "description": "内容类型"}, "contentId": {"type": "integer", "description": "内容ID"}, "duration": {"type": "integer", "description": "持续时间（秒）"}, "details": {"type": "object", "description": "活动详情"}}}}}}, "responses": {"200": {"description": "学习活动已记录", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"activityId": {"type": "integer", "example": 123}, "activityType": {"type": "string", "example": "complete_exercise"}, "createdAt": {"type": "string", "format": "date-time", "example": "2023-06-15T08:00:00Z"}}}, "message": {"type": "string", "example": "学习活动已记录"}}}}}}, "400": {"$ref": "#/components/responses/BadRequestError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}, "get": {"summary": "获取学习活动列表", "description": "获取用户的学习活动列表，支持分页和过滤", "tags": ["Statistics"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "activityType", "schema": {"type": "string", "enum": ["login", "view_exercise", "complete_exercise", "view_insight", "create_note", "like_note", "comment_note", "bubble_interaction", "share_content"]}, "description": "活动类型"}, {"in": "query", "name": "startDate", "schema": {"type": "string", "format": "date"}, "description": "开始日期（YYYY-MM-DD）"}, {"in": "query", "name": "endDate", "schema": {"type": "string", "format": "date"}, "description": "结束日期（YYYY-MM-DD）"}, {"in": "query", "name": "contentType", "schema": {"type": "string", "enum": ["exercise", "insight", "note", "tag", "plan", "bubble"]}, "description": "内容类型"}, {"in": "query", "name": "planId", "schema": {"type": "integer"}, "description": "学习计划ID"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "default": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取学习活动列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"activities": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "activityType": {"type": "string", "example": "complete_exercise"}, "contentType": {"type": "string", "example": "exercise"}, "contentId": {"type": "integer", "example": 456}, "planId": {"type": "integer", "example": 789}, "duration": {"type": "integer", "example": 300}, "createdAt": {"type": "string", "format": "date-time", "example": "2023-06-15T08:00:00Z"}}}}, "count": {"type": "integer", "example": 50}, "page": {"type": "integer", "example": 1}, "pageSize": {"type": "integer", "example": 20}, "totalPages": {"type": "integer", "example": 3}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/statistics/overview": {"get": {"summary": "获取学习概览", "description": "获取用户的学习概览，包括统计数据和最近趋势", "tags": ["Statistics"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功获取学习概览", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"statistics": {"$ref": "#/components/schemas/LearningStatistics"}, "recentTrend": {"type": "array", "items": {"$ref": "#/components/schemas/DailyRecord"}}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/statistics/trend": {"get": {"summary": "获取学习趋势", "description": "获取用户的学习趋势数据", "tags": ["Statistics"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "days", "schema": {"type": "integer", "minimum": 1, "maximum": 90, "default": 30}, "description": "天数"}], "responses": {"200": {"description": "成功获取学习趋势", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"trend": {"type": "array", "items": {"$ref": "#/components/schemas/DailyRecord"}}}}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/themes": {"get": {"summary": "获取所有主题", "description": "获取所有可用的主题", "tags": ["Themes"], "parameters": [{"in": "query", "name": "includeInactive", "schema": {"type": "boolean"}, "description": "是否包含未激活的主题"}], "responses": {"200": {"description": "成功获取主题列表"}, "500": {"description": "服务器错误"}}}}, "/api/v2/themes/{id}": {"get": {"summary": "获取单个主题", "description": "根据ID获取主题详情", "tags": ["Themes"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "主题ID"}], "responses": {"200": {"description": "成功获取主题详情"}, "404": {"description": "主题不存在"}, "500": {"description": "服务器错误"}}}}, "/api/v2/themes/{id}/soft-delete": {"delete": {"summary": "软删除主题", "description": "软删除指定ID的主题", "tags": ["Themes"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "主题ID"}], "responses": {"200": {"description": "主题已被软删除"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "主题不存在"}, "500": {"description": "服务器错误"}}}}, "/api/v2/themes/{id}/restore": {"post": {"summary": "恢复已删除的主题", "description": "恢复已软删除的主题", "tags": ["Themes"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "主题ID"}], "responses": {"200": {"description": "主题已恢复"}, "400": {"description": "主题未被删除，无需恢复"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "404": {"description": "主题不存在"}, "500": {"description": "服务器错误"}}}}, "/api/v2/themes/deleted": {"get": {"summary": "获取已删除的主题列表", "description": "获取所有已软删除的主题", "tags": ["Themes"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "default": 10}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取已删除的主题列表"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}, "500": {"description": "服务器错误"}}}}, "/api/v2/exercises/{id}/soft-delete": {"delete": {"summary": "软删除练习", "description": "软删除指定练习（练习仍然存在，但在大多数查询中不可见）", "tags": ["Exercises"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "练习ID"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "练习已被软删除"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/exercises/{id}/restore": {"post": {"summary": "恢复已软删除的练习", "description": "恢复之前软删除的练习", "tags": ["Exercises"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "练习ID"}], "responses": {"200": {"description": "恢复成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "练习已恢复"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/tags/{tagId}/exercises/deleted": {"get": {"summary": "获取已删除的练习列表", "description": "获取指定标签下已软删除的练习列表", "tags": ["Exercises"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tagId", "required": true, "schema": {"type": "string"}, "description": "标签ID"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "minimum": 1, "maximum": 50}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取已删除的练习列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"exercises": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "difficulty": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/insights/{id}/soft-delete": {"delete": {"summary": "软删除观点", "description": "软删除指定观点（观点仍然存在，但在大多数查询中不可见）", "tags": ["Insights"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "观点ID"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "观点已被软删除"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/insights/{id}/restore": {"post": {"summary": "恢复已软删除的观点", "description": "恢复之前软删除的观点", "tags": ["Insights"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "观点ID"}], "responses": {"200": {"description": "恢复成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "观点已恢复"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/tags/{tagId}/insights/deleted": {"get": {"summary": "获取已删除的观点列表", "description": "获取指定标签下已软删除的观点列表", "tags": ["Insights"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tagId", "required": true, "schema": {"type": "string"}, "description": "标签ID"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "minimum": 1, "maximum": 50}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取已删除的观点列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"insights": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "content": {"type": "string"}, "source": {"type": "string"}, "background": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/notes/{id}/soft-delete": {"delete": {"summary": "软删除笔记", "description": "软删除指定笔记（笔记仍然存在，但在大多数查询中不可见）", "tags": ["Notes"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "笔记ID"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "笔记已被软删除"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/notes/{id}/restore": {"post": {"summary": "恢复已软删除的笔记", "description": "恢复之前软删除的笔记", "tags": ["Notes"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "笔记ID"}], "responses": {"200": {"description": "恢复成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "笔记已恢复"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/notes/deleted": {"get": {"summary": "获取已删除的笔记列表", "description": "获取当前用户已软删除的笔记列表", "tags": ["Notes"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "minimum": 1, "maximum": 50}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取已删除的笔记列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"notes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "content": {"type": "string"}, "imageUrl": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/square/notes": {"get": {"summary": "获取广场笔记列表", "description": "获取广场笔记列表，支持分页、标签筛选和排序", "tags": ["Square"], "security": [], "parameters": [{"in": "query", "name": "tagId", "schema": {"type": "string"}, "description": "标签ID，使用\"all\"获取所有标签的笔记"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "default": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10}, "description": "每页记录数"}, {"in": "query", "name": "sortBy", "schema": {"type": "string", "enum": ["latest", "popular", "comments", "oldest"], "default": "latest"}, "description": "排序方式"}], "responses": {"200": {"description": "成功获取广场笔记列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"notes": {"type": "array", "items": {"$ref": "#/components/schemas/SquareNote"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/square/tags": {"get": {"summary": "获取广场标签列表", "description": "获取广场标签列表，包括系统默认标签和用户当前学习计划的标签", "tags": ["Square"], "security": [], "responses": {"200": {"description": "成功获取广场标签列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"planId": {"type": "integer", "description": "学习计划ID"}, "planTitle": {"type": "string", "description": "学习计划标题"}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/SquareTag"}}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v1/square/recommended-notes": {"get": {"summary": "获取推荐笔记", "description": "获取基于当前学习计划的推荐笔记", "tags": ["Square"], "security": [], "parameters": [{"in": "query", "name": "limit", "schema": {"type": "integer", "minimum": 1, "maximum": 20, "default": 5}, "description": "返回的笔记数量"}], "responses": {"200": {"description": "成功获取推荐笔记", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"notes": {"type": "array", "items": {"$ref": "#/components/schemas/SquareNote"}}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/tags/{id}/soft-delete": {"delete": {"summary": "软删除标签", "description": "软删除指定标签（标签仍然存在，但在大多数查询中不可见）", "tags": ["Tags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "标签ID"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "标签已被软删除"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/tags/{id}/restore": {"post": {"summary": "恢复已软删除的标签", "description": "恢复已软删除的标签", "tags": ["Tags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "标签ID"}], "responses": {"200": {"description": "恢复成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "标签已被恢复"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/ServerError"}}}}, "/api/v2/tags/deleted": {"get": {"summary": "获取已删除的标签列表", "description": "获取当前用户已软删除的标签列表", "tags": ["Tags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "default": 1}, "description": "页码"}, {"in": "query", "name": "pageSize", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10}, "description": "每页记录数"}], "responses": {"200": {"description": "成功获取已删除标签列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tags": {"type": "array", "items": {"$ref": "#/components/schemas/DeletedTag"}}, "count": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/ServerError"}}}}}, "components": {"schemas": {"SquareNote": {"type": "object", "properties": {"id": {"type": "integer", "description": "笔记ID"}, "tagId": {"type": "integer", "description": "标签ID"}, "tagName": {"type": "string", "description": "标签名称"}, "userId": {"type": "string", "description": "用户ID"}, "userName": {"type": "string", "description": "用户昵称"}, "userAvatar": {"type": "string", "format": "uri", "description": "用户头像URL"}, "title": {"type": "string", "description": "笔记标题"}, "content": {"type": "string", "description": "笔记内容"}, "imageUrl": {"type": "string", "format": "uri", "description": "配图URL"}, "likes": {"type": "integer", "description": "点赞数"}, "comments": {"type": "integer", "description": "评论数"}, "isLiked": {"type": "boolean", "description": "当前用户是否已点赞"}, "isAiGenerated": {"type": "boolean", "description": "是否由AI生成"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}}}, "SquareTag": {"type": "object", "properties": {"id": {"type": "string", "description": "标签ID"}, "name": {"type": "string", "description": "标签名称"}, "weight": {"type": "number", "format": "float", "description": "标签权重"}, "isPrimary": {"type": "boolean", "description": "是否为主要标签"}, "relevanceScore": {"type": "number", "format": "float", "description": "相关性分数"}, "sortOrder": {"type": "integer", "description": "排序顺序"}, "usageCount": {"type": "integer", "description": "使用次数"}, "isVerified": {"type": "boolean", "description": "是否已验证"}, "noteCount": {"type": "integer", "description": "笔记数量"}}}, "DeletedTag": {"type": "object", "properties": {"id": {"type": "string", "description": "标签ID"}, "name": {"type": "string", "description": "标签名称"}, "relevanceScore": {"type": "number", "format": "float", "description": "相关性得分"}, "weight": {"type": "number", "format": "float", "description": "权重"}, "usageCount": {"type": "integer", "description": "使用次数"}, "isVerified": {"type": "boolean", "description": "是否已验证"}, "sortOrder": {"type": "integer", "description": "排序顺序"}, "deletedAt": {"type": "string", "format": "date-time", "description": "删除时间"}, "category": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}}}, "tags": []}