import { DomainEvent } from './DomainEvent';

/**
 * 领域事件基类
 */
export abstract class DomainEventBase implements DomainEvent {
  private readonly _occurredOn: Date;

  /**
   * 创建领域事件
   */
  constructor() {
    this._occurredOn = new Date();
  }

  /**
   * 获取事件发生时间
   */
  get occurredOn(): Date {
    return this._occurredOn;
  }

  /**
   * 获取事件类型
   */
  get eventType(): string {
    return this.constructor.name;
  }
}
