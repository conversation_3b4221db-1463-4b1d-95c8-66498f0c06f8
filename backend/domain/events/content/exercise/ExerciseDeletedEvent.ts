import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * ExerciseDeletedEvent
 * 练习删除事件，当练习被软删除时触发
 */
export class ExerciseDeletedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'ExerciseDeleted';
  readonly aggregateType: string = 'Exercise';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 练习ID
   * @param title 练习标题
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      deletedAt: this.occurredOn
    };
  }
}
