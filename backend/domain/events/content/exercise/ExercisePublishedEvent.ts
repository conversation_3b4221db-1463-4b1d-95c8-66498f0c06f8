import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * ExercisePublishedEvent
 * 练习发布事件，当练习被发布时触发
 */
export class ExercisePublishedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'ExercisePublished';
  readonly aggregateType: string = 'Exercise';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 练习ID
   * @param title 练习标题
   * @param creatorId 创建者ID
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly creatorId: string,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      creatorId: this.creatorId,
      publishedAt: this.occurredOn
    };
  }
}
