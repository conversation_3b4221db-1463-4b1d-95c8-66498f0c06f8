import { DomainEventBase } from '../../../events/DomainEventBase';

/**
 * InsightRestoredEvent类
 * 表示洞察恢复事件
 */
export class InsightRestoredEvent extends DomainEventBase {
  /**
   * 构造函数
   * @param insightId 洞察ID
   * @param insightTitle 洞察标题
   * @param restoredAt 恢复时间
   */
  constructor(
    public readonly insightId: number,
    public readonly insightTitle: string,
    public readonly restoredAt: Date
  ) {
    super('InsightRestoredEvent');
  }
}
