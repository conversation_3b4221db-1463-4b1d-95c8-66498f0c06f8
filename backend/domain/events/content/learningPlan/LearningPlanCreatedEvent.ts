import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * LearningPlanCreatedEvent
 * 学习计划创建事件，当学习计划被创建时触发
 */
export class LearningPlanCreatedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'LearningPlanCreated';
  readonly aggregateType: string = 'LearningPlan';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 学习计划ID
   * @param title 学习计划标题
   * @param userId 用户ID
   * @param isPublic 是否公开
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly userId: number | string,
    readonly isPublic: boolean = false,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      userId: this.userId,
      isPublic: this.isPublic
    };
  }
}
