import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * LearningPlanDeletedEvent
 * 学习计划删除事件，当学习计划被软删除时触发
 */
export class LearningPlanDeletedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'LearningPlanDeleted';
  readonly aggregateType: string = 'LearningPlan';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 学习计划ID
   * @param title 学习计划标题
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      deletedAt: this.occurredOn
    };
  }
}
