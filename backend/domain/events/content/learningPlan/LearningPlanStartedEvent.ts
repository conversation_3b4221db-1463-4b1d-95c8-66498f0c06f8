import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * LearningPlanStartedEvent
 * 学习计划开始事件，当学习计划开始时触发
 */
export class LearningPlanStartedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'LearningPlanStarted';
  readonly aggregateType: string = 'LearningPlan';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 学习计划ID
   * @param title 学习计划标题
   * @param userId 用户ID
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly userId: string,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      userId: this.userId,
      startedAt: this.occurredOn
    };
  }
}
