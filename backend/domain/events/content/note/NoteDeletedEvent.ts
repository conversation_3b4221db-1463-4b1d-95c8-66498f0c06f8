import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * NoteDeletedEvent
 * 笔记删除事件，当笔记被软删除时触发
 */
export class NoteDeletedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'NoteDeleted';
  readonly aggregateType: string = 'Note';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 笔记ID
   * @param title 笔记标题
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      deletedAt: this.occurredOn
    };
  }
}
