import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * NotePublishedEvent
 * 笔记发布事件，当笔记被发布时触发
 */
export class NotePublishedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'NotePublished';
  readonly aggregateType: string = 'Note';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 笔记ID
   * @param title 笔记标题
   * @param userId 用户ID
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly userId: string,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      userId: this.userId,
      publishedAt: this.occurredOn
    };
  }
}
