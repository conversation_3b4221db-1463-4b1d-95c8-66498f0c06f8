import { DomainEvent } from '../../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * ThemeDeletedEvent
 * 主题删除事件，当主题被软删除时触发
 */
export class ThemeDeletedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'ThemeDeleted';
  readonly aggregateType: string = 'Theme';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 主题ID
   * @param name 主题名称
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly name: string,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      name: this.name,
      deletedAt: this.occurredOn
    };
  }
}
