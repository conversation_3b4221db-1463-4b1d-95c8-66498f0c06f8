import { DomainEvent } from '../DomainEvent';
import { Achievement } from '../../models/gamification/Achievement';

/**
 * 成就创建事件
 * 当新成就被创建时触发
 */
export class AchievementCreatedEvent implements DomainEvent {
  readonly eventType = 'achievement.created';
  readonly occurredOn: Date;

  /**
   * 构造函数
   * @param achievement 成就实体
   */
  constructor(
    public readonly achievement: Achievement
  ) {
    this.occurredOn = new Date();
  }
}
