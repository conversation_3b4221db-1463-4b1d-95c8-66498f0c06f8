import { DomainEvent } from '../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * AchievementUnlockedEvent
 * 成就解锁事件，当用户解锁成就时触发
 */
export class AchievementUnlockedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'AchievementUnlocked';
  readonly aggregateType: string = 'Achievement';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 成就ID
   * @param achievementId 成就ID（与aggregateId相同）
   * @param userId 用户ID
   * @param achievementName 成就名称
   * @param description 成就描述
   * @param iconUrl 成就图标URL
   * @param points 获得的积分
   * @param isRare 是否是稀有成就
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly achievementId: number,
    readonly userId: number,
    readonly achievementName: string,
    readonly description: string,
    readonly iconUrl: string,
    readonly points: number = 0,
    readonly isRare: boolean = false,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      achievementId: this.achievementId,
      userId: this.userId,
      achievementName: this.achievementName,
      description: this.description,
      iconUrl: this.iconUrl,
      points: this.points,
      isRare: this.isRare
    };
  }
}
