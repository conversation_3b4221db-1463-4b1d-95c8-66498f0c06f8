import { DomainEvent } from '../DomainEvent';

/**
 * 等级删除事件
 * 当等级被软删除时触发
 */
export class LevelDeletedEvent implements DomainEvent {
  readonly eventType = 'level.deleted';
  readonly occurredOn: Date;

  /**
   * 构造函数
   * @param levelId 等级ID
   * @param levelNumber 等级数值
   * @param levelName 等级名称
   */
  constructor(
    public readonly levelId: number,
    public readonly levelNumber: number,
    public readonly levelName: string
  ) {
    this.occurredOn = new Date();
  }
}
