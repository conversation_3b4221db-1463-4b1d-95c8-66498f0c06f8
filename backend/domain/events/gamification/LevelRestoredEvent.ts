import { DomainEvent } from '../DomainEvent';

/**
 * 等级恢复事件
 * 当等级被恢复时触发
 */
export class LevelRestoredEvent implements DomainEvent {
  readonly eventType = 'level.restored';
  readonly occurredOn: Date;

  /**
   * 构造函数
   * @param levelId 等级ID
   * @param levelNumber 等级数值
   * @param levelName 等级名称
   */
  constructor(
    public readonly levelId: number,
    public readonly levelNumber: number,
    public readonly levelName: string
  ) {
    this.occurredOn = new Date();
  }
}
