import { DomainEvent } from '../DomainEvent';
import { Level } from '../../models/gamification/Level';

/**
 * 用户升级事件
 * 当用户升级时触发
 */
export class UserLevelUpEvent implements DomainEvent {
  readonly eventType = 'user.level_up';
  readonly occurredOn: Date;

  /**
   * 构造函数
   * @param userId 用户ID
   * @param previousLevel 之前的等级
   * @param newLevel 新的等级
   * @param currentExp 当前经验值
   */
  constructor(
    public readonly userId: string,
    public readonly previousLevel: Level,
    public readonly newLevel: Level,
    public readonly currentExp: number
  ) {
    this.occurredOn = new Date();
  }
}
