import { DomainEvent } from '../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * LearningTemplateCreatedEvent
 * 学习模板创建事件，当学习模板被创建时触发
 */
export class LearningTemplateCreatedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'LearningTemplateCreated';
  readonly aggregateType: string = 'LearningTemplate';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 学习模板ID
   * @param themeId 主题ID
   * @param title 学习模板标题
   * @param creatorId 创建者ID
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly themeId: number,
    readonly title: string,
    readonly creatorId: string | null,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      themeId: this.themeId,
      title: this.title,
      creatorId: this.creatorId
    };
  }
}
