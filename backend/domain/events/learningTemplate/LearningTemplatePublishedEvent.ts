import { DomainEvent } from '../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * LearningTemplatePublishedEvent
 * 学习模板发布事件，当学习模板被发布时触发
 */
export class LearningTemplatePublishedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'LearningTemplatePublished';
  readonly aggregateType: string = 'LearningTemplate';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 学习模板ID
   * @param title 学习模板标题
   * @param themeId 主题ID
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly title: string,
    readonly themeId: number,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      title: this.title,
      themeId: this.themeId,
      publishedAt: this.occurredOn
    };
  }
}
