import { DomainEvent } from '../DomainEvent';
import { v4 as uuidv4 } from 'uuid';

/**
 * LearningTemplateUpdatedEvent
 * 学习模板更新事件，当学习模板被更新时触发
 */
export class LearningTemplateUpdatedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'LearningTemplateUpdated';
  readonly aggregateType: string = 'LearningTemplate';
  readonly version: number = 1;

  /**
   * 构造函数
   * @param aggregateId 学习模板ID
   * @param changes 更新的字段和值
   * @param occurredOn 事件发生时间
   */
  constructor(
    readonly aggregateId: number,
    readonly changes: any,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  /**
   * 获取事件负载
   */
  get payload(): any {
    return {
      changes: this.changes
    };
  }
}
