import { EntityBase } from './EntityBase';
import { AggregateRoot } from './AggregateRoot';
import { DomainEvent } from '../events/DomainEvent';

/**
 * AggregateRootBase抽象类
 * 聚合根的基类，继承自EntityBase，实现了AggregateRoot接口
 */
export abstract class AggregateRootBase<T> extends EntityBase<T> implements AggregateRoot<T> {
  /**
   * 聚合根发布的领域事件列表
   */
  private _domainEvents: DomainEvent[] = [];

  /**
   * 获取聚合根发布的领域事件列表
   */
  get domainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  /**
   * 清除所有未提交的领域事件
   */
  clearEvents(): void {
    this._domainEvents = [];
  }

  /**
   * 添加领域事件
   * @param event 要添加的领域事件
   */
  addEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }
}
