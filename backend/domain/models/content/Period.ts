/**
 * Period类
 * 表示时间段值对象
 */
export class Period {
  /**
   * 构造函数
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  constructor(
    private readonly _startTime: Date,
    private readonly _endTime: Date
  ) {
    if (_startTime > _endTime) {
      throw new Error('开始时间不能晚于结束时间');
    }
  }

  /**
   * 获取开始时间
   */
  get startTime(): Date {
    return this._startTime;
  }

  /**
   * 获取结束时间
   */
  get endTime(): Date {
    return this._endTime;
  }

  /**
   * 获取时间段的持续时间（毫秒）
   */
  get duration(): number {
    return this._endTime.getTime() - this._startTime.getTime();
  }

  /**
   * 创建时间段
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 时间段对象
   */
  static create(startTime: Date, endTime: Date): Period {
    return new Period(startTime, endTime);
  }

  /**
   * 检查时间点是否在时间段内
   * @param time 时间点
   * @returns 如果时间点在时间段内，则返回true，否则返回false
   */
  includes(time: Date): boolean {
    return time >= this._startTime && time <= this._endTime;
  }

  /**
   * 检查时间段是否与另一个时间段重叠
   * @param other 另一个时间段
   * @returns 如果两个时间段重叠，则返回true，否则返回false
   */
  overlaps(other: Period): boolean {
    return this._startTime <= other._endTime && this._endTime >= other._startTime;
  }

  /**
   * 比较两个时间段是否相等
   * @param other 另一个时间段
   * @returns 如果两个时间段相等，则返回true，否则返回false
   */
  equals(other: Period): boolean {
    return this._startTime.getTime() === other._startTime.getTime() &&
           this._endTime.getTime() === other._endTime.getTime();
  }

  /**
   * 转换为字符串
   * @returns 时间段的字符串表示
   */
  toString(): string {
    return `${this._startTime.toISOString()} - ${this._endTime.toISOString()}`;
  }
}
