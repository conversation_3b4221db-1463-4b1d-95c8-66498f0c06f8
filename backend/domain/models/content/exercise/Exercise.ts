import { AggregateRootBase } from '../../AggregateRootBase';
import { ContentStatus } from '../ContentStatus';
import { Visibility } from '../Visibility';
import { Difficulty } from '../Difficulty';
import { ExerciseCreatedEvent } from '../../../events/content/exercise/ExerciseCreatedEvent';
import { ExerciseUpdatedEvent } from '../../../events/content/exercise/ExerciseUpdatedEvent';
import { ExerciseDeletedEvent } from '../../../events/content/exercise/ExerciseDeletedEvent';
import { ExerciseRestoredEvent } from '../../../events/content/exercise/ExerciseRestoredEvent';
import { ExercisePublishedEvent } from '../../../events/content/exercise/ExercisePublishedEvent';

/**
 * Exercise类
 * 表示练习实体，是学习内容的实践形式
 */
export class Exercise extends AggregateRootBase<number> {
  /**
   * 标签列表
   */
  private _tags: string[] = [];

  /**
   * 构造函数
   * @param id 练习ID
   * @param title 练习标题
   * @param description 练习描述
   * @param expectedResult 预期结果
   * @param difficulty 难度级别
   * @param timeEstimateMinutes 预计完成时间(分钟)
   * @param creatorId 创建者ID
   * @param status 练习状态
   * @param visibility 练习可见性
   * @param isOfficial 是否官方内容
   * @param createdAt 创建时间
   * @param updatedAt 更新时间
   * @param deletedAt 删除时间
   */
  constructor(
    id: number,
    private _title: string,
    private _description: string,
    private _expectedResult: string | null,
    private _difficulty: Difficulty = Difficulty.EASY,
    private _timeEstimateMinutes: number = 5,
    private readonly _creatorId: string,
    private _status: ContentStatus = ContentStatus.DRAFT,
    private _visibility: Visibility = Visibility.PRIVATE,
    private _isOfficial: boolean = false,
    private readonly _createdAt: Date = new Date(),
    private _updatedAt: Date = new Date(),
    private _deletedAt: Date | null = null
  ) {
    super(id);
    this.validateTitle(_title);
    this.validateDescription(_description);
  }

  /**
   * 获取练习标题
   */
  get title(): string {
    return this._title;
  }

  /**
   * 获取练习描述
   */
  get description(): string {
    return this._description;
  }

  /**
   * 获取预期结果
   */
  get expectedResult(): string | null {
    return this._expectedResult;
  }

  /**
   * 获取难度级别
   */
  get difficulty(): Difficulty {
    return this._difficulty;
  }

  /**
   * 获取预计完成时间(分钟)
   */
  get timeEstimateMinutes(): number {
    return this._timeEstimateMinutes;
  }

  /**
   * 获取创建者ID
   */
  get creatorId(): string {
    return this._creatorId;
  }

  /**
   * 获取练习状态
   */
  get status(): ContentStatus {
    return this._status;
  }

  /**
   * 获取练习可见性
   */
  get visibility(): Visibility {
    return this._visibility;
  }

  /**
   * 获取是否官方内容
   */
  get isOfficial(): boolean {
    return this._isOfficial;
  }

  /**
   * 获取创建时间
   */
  get createdAt(): Date {
    return this._createdAt;
  }

  /**
   * 获取更新时间
   */
  get updatedAt(): Date {
    return this._updatedAt;
  }

  /**
   * 获取删除时间
   */
  get deletedAt(): Date | null {
    return this._deletedAt;
  }

  /**
   * 获取标签列表
   */
  get tags(): string[] {
    return [...this._tags];
  }

  /**
   * 判断练习是否已删除
   */
  get isDeleted(): boolean {
    return this._status === ContentStatus.DELETED;
  }

  /**
   * 判断练习是否已发布
   */
  get isPublished(): boolean {
    return this._status === ContentStatus.PUBLISHED;
  }

  /**
   * 创建练习
   * @param title 练习标题
   * @param description 练习描述
   * @param expectedResult 预期结果
   * @param difficulty 难度级别
   * @param timeEstimateMinutes 预计完成时间(分钟)
   * @param creatorId 创建者ID
   * @param visibility 练习可见性
   * @param isOfficial 是否官方内容
   * @returns 练习实体
   */
  static create(
    title: string,
    description: string,
    expectedResult: string | null,
    difficulty: Difficulty,
    timeEstimateMinutes: number,
    creatorId: string,
    visibility: Visibility = Visibility.PRIVATE,
    isOfficial: boolean = false
  ): Exercise {
    const exercise = new Exercise(
      0, // 临时ID，保存时会被替换
      title,
      description,
      expectedResult,
      difficulty,
      timeEstimateMinutes,
      creatorId,
      ContentStatus.DRAFT,
      visibility,
      isOfficial
    );

    exercise.addEvent(new ExerciseCreatedEvent(
      0,
      title,
      creatorId,
      new Date()
    ));

    return exercise;
  }

  /**
   * 更新标题
   * @param title 新标题
   */
  updateTitle(title: string): void {
    this.validateTitle(title);
    
    if (this._title === title) {
      return;
    }

    const oldTitle = this._title;
    this._title = title;
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        title: {
          from: oldTitle,
          to: title
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 更新描述
   * @param description 新描述
   */
  updateDescription(description: string): void {
    this.validateDescription(description);
    
    if (this._description === description) {
      return;
    }

    this._description = description;
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        description: {
          from: '(previous description)',
          to: '(new description)'
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 更新预期结果
   * @param expectedResult 新预期结果
   */
  updateExpectedResult(expectedResult: string | null): void {
    if (this._expectedResult === expectedResult) {
      return;
    }

    this._expectedResult = expectedResult;
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        expectedResult: {
          from: this._expectedResult,
          to: expectedResult
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 更新难度级别
   * @param difficulty 新难度级别
   */
  updateDifficulty(difficulty: Difficulty): void {
    if (this._difficulty === difficulty) {
      return;
    }

    const oldDifficulty = this._difficulty;
    this._difficulty = difficulty;
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        difficulty: {
          from: oldDifficulty,
          to: difficulty
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 更新预计完成时间
   * @param timeEstimateMinutes 新预计完成时间(分钟)
   */
  updateTimeEstimate(timeEstimateMinutes: number): void {
    if (timeEstimateMinutes <= 0) {
      throw new Error('预计完成时间必须大于0');
    }

    if (this._timeEstimateMinutes === timeEstimateMinutes) {
      return;
    }

    const oldTimeEstimate = this._timeEstimateMinutes;
    this._timeEstimateMinutes = timeEstimateMinutes;
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        timeEstimateMinutes: {
          from: oldTimeEstimate,
          to: timeEstimateMinutes
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 更新可见性
   * @param visibility 新可见性
   */
  updateVisibility(visibility: Visibility): void {
    if (this._visibility === visibility) {
      return;
    }

    const oldVisibility = this._visibility;
    this._visibility = visibility;
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        visibility: {
          from: oldVisibility,
          to: visibility
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 发布练习
   */
  publish(): void {
    if (this.isDeleted) {
      throw new Error('已删除的练习不能发布');
    }

    if (this.isPublished) {
      return;
    }

    this._status = ContentStatus.PUBLISHED;
    this._updatedAt = new Date();

    this.addEvent(new ExercisePublishedEvent(
      this.id,
      this._title,
      this._creatorId,
      this._updatedAt
    ));
  }

  /**
   * 软删除练习
   */
  softDelete(): void {
    if (this.isDeleted) {
      throw new Error('练习已被删除');
    }

    this._status = ContentStatus.DELETED;
    this._deletedAt = new Date();
    this._updatedAt = new Date();

    this.addEvent(new ExerciseDeletedEvent(
      this.id,
      this._title,
      this._deletedAt
    ));
  }

  /**
   * 恢复练习
   */
  restore(): void {
    if (!this.isDeleted) {
      throw new Error('练习未被删除');
    }

    this._status = ContentStatus.DRAFT;
    this._deletedAt = null;
    this._updatedAt = new Date();

    this.addEvent(new ExerciseRestoredEvent(
      this.id,
      this._title,
      this._updatedAt
    ));
  }

  /**
   * 添加标签
   * @param tag 标签
   */
  addTag(tag: string): void {
    if (this._tags.includes(tag)) {
      return;
    }

    this._tags.push(tag);
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        tags: {
          from: this._tags.filter(t => t !== tag),
          to: [...this._tags]
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 移除标签
   * @param tag 标签
   */
  removeTag(tag: string): void {
    if (!this._tags.includes(tag)) {
      return;
    }

    this._tags = this._tags.filter(t => t !== tag);
    this._updatedAt = new Date();

    this.addEvent(new ExerciseUpdatedEvent(
      this.id,
      {
        tags: {
          from: [...this._tags, tag],
          to: [...this._tags]
        }
      },
      this._updatedAt
    ));
  }

  /**
   * 验证标题
   * @param title 标题
   */
  private validateTitle(title: string): void {
    if (!title || title.trim().length === 0) {
      throw new Error('练习标题不能为空');
    }

    if (title.length > 100) {
      throw new Error('练习标题不能超过100个字符');
    }
  }

  /**
   * 验证描述
   * @param description 描述
   */
  private validateDescription(description: string): void {
    if (!description || description.trim().length === 0) {
      throw new Error('练习描述不能为空');
    }
  }
}
