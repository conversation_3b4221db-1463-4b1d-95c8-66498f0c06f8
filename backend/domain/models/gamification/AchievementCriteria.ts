/**
 * 成就条件值对象
 * 表示获得成就的条件
 */
export class AchievementCriteria {
  /**
   * 构造函数
   * @param type 条件类型
   * @param value 条件值
   */
  private constructor(
    private readonly _type: string,
    private readonly _value: Record<string, any>
  ) {}

  // Getters
  get type(): string {
    return this._type;
  }

  get value(): Record<string, any> {
    return { ...this._value }; // 返回副本以防止修改
  }

  /**
   * 创建完成练习次数条件
   * @param count 需要完成的练习次数
   * @returns 成就条件值对象
   */
  static completeExercises(count: number): AchievementCriteria {
    if (count <= 0) {
      throw new Error('练习次数必须为正数');
    }
    return new AchievementCriteria('complete_exercises', { count });
  }

  /**
   * 创建连续学习天数条件
   * @param days 需要连续学习的天数
   * @returns 成就条件值对象
   */
  static consecutiveDays(days: number): AchievementCriteria {
    if (days <= 0) {
      throw new Error('天数必须为正数');
    }
    return new AchievementCriteria('consecutive_days', { days });
  }

  /**
   * 创建完成学习计划条件
   * @param count 需要完成的学习计划数量
   * @returns 成就条件值对象
   */
  static completeLearningPlans(count: number): AchievementCriteria {
    if (count <= 0) {
      throw new Error('学习计划数量必须为正数');
    }
    return new AchievementCriteria('complete_learning_plans', { count });
  }

  /**
   * 创建获得点赞数条件
   * @param count 需要获得的点赞数
   * @returns 成就条件值对象
   */
  static receiveLikes(count: number): AchievementCriteria {
    if (count <= 0) {
      throw new Error('点赞数必须为正数');
    }
    return new AchievementCriteria('receive_likes', { count });
  }

  /**
   * 创建发表评论数条件
   * @param count 需要发表的评论数
   * @returns 成就条件值对象
   */
  static postComments(count: number): AchievementCriteria {
    if (count <= 0) {
      throw new Error('评论数必须为正数');
    }
    return new AchievementCriteria('post_comments', { count });
  }

  /**
   * 创建分享内容次数条件
   * @param count 需要分享的次数
   * @returns 成就条件值对象
   */
  static shareContent(count: number): AchievementCriteria {
    if (count <= 0) {
      throw new Error('分享次数必须为正数');
    }
    return new AchievementCriteria('share_content', { count });
  }

  /**
   * 创建创建内容数量条件
   * @param count 需要创建的内容数量
   * @param contentType 内容类型（可选）
   * @returns 成就条件值对象
   */
  static createContent(count: number, contentType?: string): AchievementCriteria {
    if (count <= 0) {
      throw new Error('内容数量必须为正数');
    }
    return new AchievementCriteria('create_content', { count, contentType });
  }

  /**
   * 创建特殊事件条件
   * @param eventName 事件名称
   * @param params 事件参数
   * @returns 成就条件值对象
   */
  static specialEvent(eventName: string, params: Record<string, any> = {}): AchievementCriteria {
    if (!eventName) {
      throw new Error('事件名称不能为空');
    }
    return new AchievementCriteria('special_event', { eventName, ...params });
  }

  /**
   * 从原始数据创建成就条件
   * @param type 条件类型
   * @param value 条件值
   * @returns 成就条件值对象
   */
  static fromRaw(type: string, value: Record<string, any>): AchievementCriteria {
    switch (type) {
      case 'complete_exercises':
        return AchievementCriteria.completeExercises(value.count);
      case 'consecutive_days':
        return AchievementCriteria.consecutiveDays(value.days);
      case 'complete_learning_plans':
        return AchievementCriteria.completeLearningPlans(value.count);
      case 'receive_likes':
        return AchievementCriteria.receiveLikes(value.count);
      case 'post_comments':
        return AchievementCriteria.postComments(value.count);
      case 'share_content':
        return AchievementCriteria.shareContent(value.count);
      case 'create_content':
        return AchievementCriteria.createContent(value.count, value.contentType);
      case 'special_event':
        return AchievementCriteria.specialEvent(value.eventName, value);
      default:
        return new AchievementCriteria(type, value);
    }
  }

  /**
   * 获取条件的描述文本
   * @returns 条件描述文本
   */
  getDescription(): string {
    switch (this._type) {
      case 'complete_exercises':
        return `完成${this._value.count}个练习`;
      case 'consecutive_days':
        return `连续学习${this._value.days}天`;
      case 'complete_learning_plans':
        return `完成${this._value.count}个学习计划`;
      case 'receive_likes':
        return `获得${this._value.count}个点赞`;
      case 'post_comments':
        return `发表${this._value.count}条评论`;
      case 'share_content':
        return `分享内容${this._value.count}次`;
      case 'create_content':
        const contentType = this._value.contentType 
          ? this.getContentTypeDisplayName(this._value.contentType) 
          : '内容';
        return `创建${this._value.count}个${contentType}`;
      case 'special_event':
        return `完成特殊事件: ${this._value.eventName}`;
      default:
        return `满足条件: ${this._type}`;
    }
  }

  /**
   * 获取内容类型的显示名称
   * @param contentType 内容类型
   * @returns 内容类型显示名称
   */
  private getContentTypeDisplayName(contentType: string): string {
    switch (contentType) {
      case 'exercise':
        return '练习';
      case 'note':
        return '笔记';
      case 'insight':
        return '观点';
      case 'learning_plan':
        return '学习计划';
      default:
        return contentType;
    }
  }

  /**
   * 比较两个条件值对象是否相等
   * @param other 另一个条件值对象
   * @returns 是否相等
   */
  equals(other: AchievementCriteria): boolean {
    return this._type === other._type && 
           JSON.stringify(this._value) === JSON.stringify(other._value);
  }

  /**
   * 转换为JSON对象
   * @returns JSON对象
   */
  toJSON(): Record<string, any> {
    return {
      type: this._type,
      value: this._value
    };
  }
}
