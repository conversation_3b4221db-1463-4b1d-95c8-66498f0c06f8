/**
 * 徽章类别值对象
 * 表示徽章的类别
 */
export class BadgeCategory {
  private constructor(private readonly _value: string) {}

  // 预定义的徽章类别
  static readonly THEME = new BadgeCategory('theme');
  static readonly SKILL = new BadgeCategory('skill');
  static readonly EVENT = new BadgeCategory('event');
  static readonly SPECIAL = new BadgeCategory('special');

  // Getter
  get value(): string {
    return this._value;
  }

  /**
   * 从字符串创建类别值对象
   * @param value 类别字符串
   * @returns 类别值对象
   */
  static fromString(value: string): BadgeCategory {
    switch (value.toLowerCase()) {
      case 'theme':
        return BadgeCategory.THEME;
      case 'skill':
        return BadgeCategory.SKILL;
      case 'event':
        return BadgeCategory.EVENT;
      case 'special':
        return BadgeCategory.SPECIAL;
      default:
        throw new Error(`无效的徽章类别: ${value}`);
    }
  }

  /**
   * 获取所有类别
   * @returns 所有类别数组
   */
  static getAll(): BadgeCategory[] {
    return [
      BadgeCategory.THEME,
      BadgeCategory.SKILL,
      BadgeCategory.EVENT,
      BadgeCategory.SPECIAL
    ];
  }

  /**
   * 获取类别的显示名称
   * @returns 类别的显示名称
   */
  getDisplayName(): string {
    switch (this._value) {
      case 'theme':
        return '主题';
      case 'skill':
        return '技能';
      case 'event':
        return '活动';
      case 'special':
        return '特殊';
      default:
        return this._value;
    }
  }

  /**
   * 比较两个类别值对象是否相等
   * @param other 另一个类别值对象
   * @returns 是否相等
   */
  equals(other: BadgeCategory): boolean {
    return this._value === other._value;
  }

  /**
   * 转换为字符串
   * @returns 字符串表示
   */
  toString(): string {
    return this._value;
  }
}
