import { Entity } from '../Entity';

/**
 * TemplateTag实体
 * 表示学习模板和标签之间的关联
 */
export class TemplateTag implements Entity<number> {
  /**
   * 构造函数
   * @param id 关联ID
   * @param templateId 学习模板ID
   * @param tagId 标签ID
   * @param createdAt 创建时间
   */
  constructor(
    private readonly _id: number,
    private readonly _templateId: number,
    private readonly _tagId: number,
    private readonly _createdAt: Date
  ) {}

  /**
   * 创建模板标签关联
   * @param templateId 学习模板ID
   * @param tagId 标签ID
   * @returns 模板标签关联实体
   */
  static create(templateId: number, tagId: number): TemplateTag {
    return new TemplateTag(
      0, // 临时ID，保存时会被替换
      templateId,
      tagId,
      new Date()
    );
  }

  // Getters
  get id(): number { return this._id; }
  get templateId(): number { return this._templateId; }
  get tagId(): number { return this._tagId; }
  get createdAt(): Date { return new Date(this._createdAt); }

  /**
   * 比较两个实体是否相等
   * @param entity 要比较的实体
   * @returns 如果两个实体的ID相等，则返回true
   */
  equals(entity: Entity<number>): boolean {
    if (!(entity instanceof TemplateTag)) return false;
    if (this._id === 0 || entity.id === 0) {
      return this._templateId === entity.templateId && this._tagId === entity.tagId;
    }
    return this._id === entity.id;
  }
}
