import { ValueObject } from '../ValueObject';

/**
 * 标签反馈类型
 * 表示标签反馈的类型，如错误报告、改进建议、问题等
 */
export enum FeedbackTypeEnum {
  ERROR = 'error',           // 错误报告
  SUGGESTION = 'suggestion', // 改进建议
  QUESTION = 'question'      // 问题
}

/**
 * FeedbackType值对象
 * 表示标签反馈的类型
 */
export class FeedbackType implements ValueObject {
  /**
   * 构造函数
   * @param value 反馈类型的值
   */
  constructor(private readonly value: FeedbackTypeEnum) {}

  /**
   * 获取反馈类型的值
   */
  getValue(): FeedbackTypeEnum {
    return this.value;
  }

  /**
   * 比较两个反馈类型是否相等
   * @param valueObject 要比较的值对象
   * @returns 如果两个反馈类型的值相等，则返回true
   */
  equals(valueObject: ValueObject): boolean {
    if (!(valueObject instanceof FeedbackType)) return false;
    return this.value === valueObject.getValue();
  }

  /**
   * 将反馈类型转换为字符串
   * @returns 反馈类型的字符串表示
   */
  toString(): string {
    return this.value;
  }

  /**
   * 从字符串创建反馈类型
   * @param value 反馈类型的字符串表示
   * @returns 反馈类型值对象
   * @throws 如果字符串不是有效的反馈类型，则抛出错误
   */
  static fromString(value: string): FeedbackType {
    if (!Object.values(FeedbackTypeEnum).includes(value as FeedbackTypeEnum)) {
      throw new Error(`无效的反馈类型: ${value}`);
    }
    return new FeedbackType(value as FeedbackTypeEnum);
  }

  /**
   * 创建错误报告反馈类型
   * @returns 错误报告反馈类型值对象
   */
  static error(): FeedbackType {
    return new FeedbackType(FeedbackTypeEnum.ERROR);
  }

  /**
   * 创建改进建议反馈类型
   * @returns 改进建议反馈类型值对象
   */
  static suggestion(): FeedbackType {
    return new FeedbackType(FeedbackTypeEnum.SUGGESTION);
  }

  /**
   * 创建问题反馈类型
   * @returns 问题反馈类型值对象
   */
  static question(): FeedbackType {
    return new FeedbackType(FeedbackTypeEnum.QUESTION);
  }
}
