import { Entity } from '../common/Entity';

/**
 * 权限实体
 */
export class Permission extends Entity {
  private _name: string;
  private _description: string;
  private _resourceType: string;

  /**
   * 创建权限
   * @param id 权限ID
   * @param name 权限名称
   * @param description 权限描述
   * @param resourceType 资源类型
   */
  constructor(
    id: number,
    name: string,
    description: string,
    resourceType: string
  ) {
    super(id);
    this._name = name;
    this._description = description;
    this._resourceType = resourceType;
  }

  /**
   * 更新权限名称
   * @param name 权限名称
   */
  updateName(name: string): void {
    this._name = name;
  }

  /**
   * 更新权限描述
   * @param description 权限描述
   */
  updateDescription(description: string): void {
    this._description = description;
  }

  /**
   * 更新资源类型
   * @param resourceType 资源类型
   */
  updateResourceType(resourceType: string): void {
    this._resourceType = resourceType;
  }

  /**
   * 获取权限名称
   */
  get name(): string {
    return this._name;
  }

  /**
   * 获取权限描述
   */
  get description(): string {
    return this._description;
  }

  /**
   * 获取资源类型
   */
  get resourceType(): string {
    return this._resourceType;
  }
}
