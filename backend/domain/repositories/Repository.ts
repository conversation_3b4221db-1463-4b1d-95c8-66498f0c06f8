/**
 * Repository接口
 * 仓库负责持久化和检索聚合根
 * 它隐藏了持久化机制的细节，提供了一个面向领域的接口
 */
export interface Repository<T, ID> {
  /**
   * 根据ID查找实体
   * @param id 实体的ID
   * @returns 如果找到实体，则返回实体，否则返回null
   */
  findById(id: ID): Promise<T | null>;

  /**
   * 查找所有实体
   * @returns 实体列表
   */
  findAll(): Promise<T[]>;

  /**
   * 保存实体
   * @param entity 要保存的实体
   * @returns 保存后的实体
   */
  save(entity: T): Promise<T>;

  /**
   * 删除实体
   * @param entity 要删除的实体
   */
  delete(entity: T): Promise<void>;
}
