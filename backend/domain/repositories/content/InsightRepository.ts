import { Repository } from '../Repository';
import { Insight } from '../../models/content/insight/Insight';
import { ContentStatus } from '../../models/content/ContentStatus';
import { Visibility } from '../../models/content/Visibility';

/**
 * InsightRepository接口
 * 洞察仓库接口，定义了洞察的持久化和检索操作
 */
export interface InsightRepository extends Repository<Insight, number> {
  /**
   * 根据标题查找洞察
   * @param title 洞察标题
   * @returns 如果找到洞察，则返回洞察，否则返回null
   */
  findByTitle(title: string): Promise<Insight | null>;

  /**
   * 根据创建者ID查找洞察
   * @param creatorId 创建者ID
   * @param status 洞察状态
   * @returns 洞察列表
   */
  findByCreatorId(creatorId: string, status?: ContentStatus): Promise<Insight[]>;

  /**
   * 根据标签查找洞察
   * @param tag 标签
   * @param status 洞察状态
   * @returns 洞察列表
   */
  findByTag(tag: string, status?: ContentStatus): Promise<Insight[]>;

  /**
   * 根据可见性查找洞察
   * @param visibility 可见性
   * @param status 洞察状态
   * @returns 洞察列表
   */
  findByVisibility(visibility: Visibility, status?: ContentStatus): Promise<Insight[]>;

  /**
   * 根据状态查找洞察
   * @param status 洞察状态
   * @returns 洞察列表
   */
  findByStatus(status: ContentStatus): Promise<Insight[]>;

  /**
   * 根据关键字搜索洞察
   * @param keyword 关键字
   * @param status 洞察状态
   * @returns 洞察列表
   */
  searchByKeyword(keyword: string, status?: ContentStatus): Promise<Insight[]>;

  /**
   * 获取热门洞察
   * @param limit 返回的最大数量
   * @returns 洞察列表
   */
  findPopular(limit?: number): Promise<Insight[]>;

  /**
   * 获取最新洞察
   * @param limit 返回的最大数量
   * @returns 洞察列表
   */
  findLatest(limit?: number): Promise<Insight[]>;

  /**
   * 获取用户可见的洞察
   * @param userId 用户ID
   * @param status 洞察状态
   * @returns 洞察列表
   */
  findVisibleToUser(userId: string, status?: ContentStatus): Promise<Insight[]>;
}
