import { Repository } from '../../Repository';
import { LearningPlan } from '../../../models/content/learningPlan/LearningPlan';

/**
 * LearningPlanRepository接口
 * 学习计划仓库接口，定义学习计划的持久化和检索操作
 */
export interface LearningPlanRepository extends Repository<LearningPlan, number> {
  /**
   * 根据用户ID查找学习计划
   * @param userId 用户ID
   * @returns 学习计划列表
   */
  findByUserId(userId: string): Promise<LearningPlan[]>;

  /**
   * 根据模板ID查找学习计划
   * @param templateId 模板ID
   * @returns 学习计划列表
   */
  findByTemplateId(templateId: number): Promise<LearningPlan[]>;

  /**
   * 根据主题ID查找学习计划
   * @param themeId 主题ID
   * @returns 学习计划列表
   */
  findByThemeId(themeId: number): Promise<LearningPlan[]>;

  /**
   * 根据状态查找学习计划
   * @param status 状态
   * @returns 学习计划列表
   */
  findByStatus(status: string): Promise<LearningPlan[]>;

  /**
   * 查找用户的当前活跃学习计划
   * @param userId 用户ID
   * @returns 当前活跃学习计划，如果没有则返回null
   */
  findCurrentByUserId(userId: string): Promise<LearningPlan | null>;

  /**
   * 查找系统默认学习计划
   * @returns 系统默认学习计划列表
   */
  findSystemDefault(): Promise<LearningPlan[]>;

  /**
   * 查找公开的学习计划
   * @param limit 返回的最大数量
   * @returns 公开的学习计划列表
   */
  findPublic(limit: number): Promise<LearningPlan[]>;

  /**
   * 查找已删除的学习计划
   * @param userId 用户ID
   * @returns 已删除的学习计划列表
   */
  findDeleted(userId: string): Promise<LearningPlan[]>;

  /**
   * 查找最近创建的学习计划
   * @param limit 返回的最大数量
   * @returns 最近创建的学习计划列表
   */
  findRecent(limit: number): Promise<LearningPlan[]>;

  /**
   * 更新学习计划进度
   * @param planId 学习计划ID
   * @param completedDays 已完成天数
   * @param progress 进度百分比
   */
  updateProgress(planId: number, completedDays: number, progress: number): Promise<void>;

  /**
   * 重置其他学习计划的当前活跃状态
   * @param userId 用户ID
   * @param exceptPlanId 排除的学习计划ID
   */
  resetCurrentStatus(userId: string, exceptPlanId: number): Promise<void>;
}
