import { LearningTemplate } from '../../models/learningTemplate/LearningTemplate';
import { Difficulty } from '../../models/learningTemplate/Difficulty';
import { Repository } from '../Repository';

/**
 * LearningTemplateRepository接口
 * 定义学习模板的持久化和检索操作
 */
export interface LearningTemplateRepository extends Repository<LearningTemplate, number> {
  /**
   * 根据主题ID查找学习模板
   * @param themeId 主题ID
   * @returns 学习模板列表
   */
  findByThemeId(themeId: number): Promise<LearningTemplate[]>;

  /**
   * 根据创建者ID查找学习模板
   * @param creatorId 创建者ID
   * @returns 学习模板列表
   */
  findByCreatorId(creatorId: string): Promise<LearningTemplate[]>;

  /**
   * 查找已发布的学习模板
   * @returns 已发布的学习模板列表
   */
  findPublished(): Promise<LearningTemplate[]>;

  /**
   * 查找热门学习模板
   * @param limit 限制数量
   * @returns 热门学习模板列表
   */
  findPopular(limit: number): Promise<LearningTemplate[]>;

  /**
   * 根据难度查找学习模板
   * @param difficulty 难度级别
   * @returns 学习模板列表
   */
  findByDifficulty(difficulty: Difficulty): Promise<LearningTemplate[]>;

  /**
   * 根据标签ID查找学习模板
   * @param tagId 标签ID
   * @returns 学习模板列表
   */
  findByTagId(tagId: number): Promise<LearningTemplate[]>;

  /**
   * 查找官方学习模板
   * @returns 官方学习模板列表
   */
  findOfficial(): Promise<LearningTemplate[]>;

  /**
   * 搜索学习模板
   * @param keyword 关键字
   * @param options 搜索选项
   * @returns 学习模板列表
   */
  search(keyword: string, options?: {
    themeId?: number;
    difficulty?: Difficulty;
    isOfficial?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<LearningTemplate[]>;
}
