import { Repository } from '../Repository';
import { TagCategory } from '../../models/tag/TagCategory';

/**
 * TagCategoryRepository接口
 * 标签分类仓库接口，定义了标签分类的持久化和检索方法
 */
export interface TagCategoryRepository extends Repository<TagCategory, number> {
  /**
   * 根据名称查找分类
   * @param name 分类名称
   * @returns 如果找到分类，则返回分类，否则返回null
   */
  findByName(name: string): Promise<TagCategory | null>;

  /**
   * 根据父分类ID查找分类
   * @param parentId 父分类ID
   * @returns 分类列表
   */
  findByParentId(parentId: number): Promise<TagCategory[]>;

  /**
   * 查找根分类（没有父分类的分类）
   * @returns 根分类列表
   */
  findRootCategories(): Promise<TagCategory[]>;

  /**
   * 查找分类及其子分类
   * @param categoryId 分类ID
   * @returns 分类及其子分类
   */
  findWithChildren(categoryId: number): Promise<{ category: TagCategory, children: TagCategory[] }>;
}
