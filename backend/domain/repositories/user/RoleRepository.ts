import { Repository } from '../common/Repository';
import { Role } from '../../models/user/Role';

/**
 * 角色仓库接口
 */
export interface RoleRepository extends Repository<Role> {
  /**
   * 根据名称查找角色
   * @param name 角色名称
   * @returns 角色或null
   */
  findByName(name: string): Promise<Role | null>;

  /**
   * 查找所有角色及其权限
   * @returns 角色列表
   */
  findWithPermissions(): Promise<Role[]>;

  /**
   * 根据ID查找角色及其权限
   * @param id 角色ID
   * @returns 角色或null
   */
  findWithPermissionsById(id: number): Promise<Role | null>;
}
