import { Repository } from '../common/Repository';
import { User } from '../../models/user/User';

/**
 * 用户仓库接口
 */
export interface UserRepository extends Repository<User> {
  /**
   * 根据用户名查找用户
   * @param username 用户名
   * @returns 用户或null
   */
  findByUsername(username: string): Promise<User | null>;

  /**
   * 根据邮箱查找用户
   * @param email 邮箱
   * @returns 用户或null
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * 根据手机号查找用户
   * @param phoneNumber 手机号
   * @returns 用户或null
   */
  findByPhoneNumber(phoneNumber: string): Promise<User | null>;

  /**
   * 根据微信OpenID查找用户
   * @param wechatOpenId 微信OpenID
   * @returns 用户或null
   */
  findByWechatOpenId(wechatOpenId: string): Promise<User | null>;

  /**
   * 分页查找用户
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 用户列表和总数
   */
  findWithPagination(page: number, pageSize: number): Promise<{ items: User[], total: number }>;

  /**
   * 根据角色查找用户
   * @param roleId 角色ID
   * @returns 用户列表
   */
  findByRole(roleId: number): Promise<User[]>;

  /**
   * 查找已删除的用户
   * @returns 已删除的用户列表
   */
  findDeleted(): Promise<User[]>;

  /**
   * 恢复已删除的用户
   * @param id 用户ID
   * @returns 恢复后的用户或null
   */
  restore(id: number): Promise<User | null>;
}
