import { ExerciseRepository } from '../../repositories/content/exercise/ExerciseRepository';
import { NoteRepository } from '../../repositories/content/note/NoteRepository';
import { ThemeRepository } from '../../repositories/content/theme/ThemeRepository';
import { Exercise } from '../../models/content/exercise/Exercise';
import { Note } from '../../models/content/note/Note';
import { Theme } from '../../models/content/theme/Theme';
import { Difficulty } from '../../models/content/Difficulty';
import { ContentStatus } from '../../models/content/ContentStatus';

/**
 * 搜索结果接口
 */
export interface SearchResult {
  exercises: Exercise[];
  notes: Note[];
  themes: Theme[];
  totalCount: number;
}

/**
 * 搜索过滤器接口
 */
export interface SearchFilter {
  keyword?: string;
  tagIds?: number[];
  themeId?: number;
  difficulty?: Difficulty;
  creatorId?: string;
  isOfficial?: boolean;
  includeDeleted?: boolean;
  sortBy?: 'relevance' | 'date' | 'popularity';
  page?: number;
  pageSize?: number;
}

/**
 * ContentSearchService
 * 内容搜索服务，提供高级搜索功能
 */
export class ContentSearchService {
  /**
   * 构造函数
   * @param exerciseRepository 练习仓库
   * @param noteRepository 笔记仓库
   * @param themeRepository 主题仓库
   */
  constructor(
    private readonly exerciseRepository: ExerciseRepository,
    private readonly noteRepository: NoteRepository,
    private readonly themeRepository: ThemeRepository
  ) {}

  /**
   * 搜索内容
   * @param filter 搜索过滤器
   * @returns 搜索结果
   */
  async searchContent(filter: SearchFilter): Promise<SearchResult> {
    // 设置默认值
    const {
      keyword = '',
      tagIds = [],
      themeId,
      difficulty,
      creatorId,
      isOfficial,
      includeDeleted = false,
      sortBy = 'relevance',
      page = 1,
      pageSize = 20
    } = filter;

    // 并行搜索练习、笔记和主题
    const [exercises, notes, themes] = await Promise.all([
      this.searchExercises(filter),
      this.searchNotes(filter),
      this.searchThemes(filter)
    ]);

    // 计算总数
    const totalCount = exercises.length + notes.length + themes.length;

    // 应用分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;

    // 根据排序方式合并结果
    let allResults: (Exercise | Note | Theme)[] = [];
    
    if (sortBy === 'date') {
      // 按日期排序
      allResults = [...exercises, ...notes, ...themes].sort((a, b) => 
        b.createdAt.getTime() - a.createdAt.getTime()
      );
    } else if (sortBy === 'popularity') {
      // 按热度排序（练习和笔记有热度指标，主题没有）
      allResults = [
        ...exercises,
        ...notes.sort((a, b) => (b.likeCount + b.viewCount) - (a.likeCount + a.viewCount)),
        ...themes
      ];
    } else {
      // 按相关性排序（默认）
      // 这里简单地按类型分组：练习 > 笔记 > 主题
      allResults = [...exercises, ...notes, ...themes];
    }

    // 应用分页并分类结果
    const paginatedResults = allResults.slice(start, end);
    
    const paginatedExercises = paginatedResults.filter(item => item instanceof Exercise) as Exercise[];
    const paginatedNotes = paginatedResults.filter(item => item instanceof Note) as Note[];
    const paginatedThemes = paginatedResults.filter(item => item instanceof Theme) as Theme[];

    return {
      exercises: paginatedExercises,
      notes: paginatedNotes,
      themes: paginatedThemes,
      totalCount
    };
  }

  /**
   * 搜索练习
   * @param filter 搜索过滤器
   * @returns 练习列表
   */
  private async searchExercises(filter: SearchFilter): Promise<Exercise[]> {
    const {
      keyword = '',
      tagIds = [],
      difficulty,
      creatorId,
      isOfficial,
      includeDeleted = false
    } = filter;

    let exercises: Exercise[] = [];

    // 根据关键字搜索
    if (keyword) {
      exercises = await this.exerciseRepository.searchByKeyword(keyword);
    } else {
      exercises = await this.exerciseRepository.findAll();
    }

    // 应用过滤条件
    return exercises.filter(exercise => {
      // 过滤已删除的内容
      if (!includeDeleted && exercise.isDeleted) {
        return false;
      }

      // 按标签过滤
      if (tagIds.length > 0) {
        // 这里假设练习的标签是字符串，实际实现可能需要转换
        const hasMatchingTag = exercise.tags.some(tag => tagIds.includes(parseInt(tag)));
        if (!hasMatchingTag) {
          return false;
        }
      }

      // 按难度过滤
      if (difficulty !== undefined && exercise.difficulty !== difficulty) {
        return false;
      }

      // 按创建者过滤
      if (creatorId !== undefined && exercise.creatorId !== creatorId) {
        return false;
      }

      // 按官方内容过滤
      if (isOfficial !== undefined && exercise.isOfficial !== isOfficial) {
        return false;
      }

      return true;
    });
  }

  /**
   * 搜索笔记
   * @param filter 搜索过滤器
   * @returns 笔记列表
   */
  private async searchNotes(filter: SearchFilter): Promise<Note[]> {
    const {
      keyword = '',
      tagIds = [],
      creatorId,
      includeDeleted = false
    } = filter;

    let notes: Note[] = [];

    // 根据关键字搜索
    if (keyword) {
      notes = await this.noteRepository.searchByKeyword(keyword);
    } else if (creatorId) {
      notes = await this.noteRepository.findByUserId(creatorId);
    } else {
      notes = await this.noteRepository.findAll();
    }

    // 应用过滤条件
    return notes.filter(note => {
      // 过滤已删除的内容
      if (!includeDeleted && note.isDeleted) {
        return false;
      }

      // 按标签过滤
      if (tagIds.length > 0) {
        // 这里假设笔记的标签是字符串，实际实现可能需要转换
        const hasMatchingTag = note.tags.some(tag => tagIds.includes(parseInt(tag)));
        if (!hasMatchingTag) {
          return false;
        }
      }

      // 按创建者过滤
      if (creatorId !== undefined && note.userId !== creatorId) {
        return false;
      }

      return true;
    });
  }

  /**
   * 搜索主题
   * @param filter 搜索过滤器
   * @returns 主题列表
   */
  private async searchThemes(filter: SearchFilter): Promise<Theme[]> {
    const {
      keyword = '',
      themeId,
      includeDeleted = false
    } = filter;

    let themes: Theme[] = [];

    // 根据关键字搜索
    if (keyword) {
      themes = await this.themeRepository.searchByKeyword(keyword);
    } else if (themeId) {
      // 如果指定了主题ID，查找其子主题
      themes = await this.themeRepository.findByParentId(themeId);
    } else {
      // 否则查找顶级主题
      themes = await this.themeRepository.findTopLevel();
    }

    // 应用过滤条件
    return themes.filter(theme => {
      // 过滤已删除的内容
      if (!includeDeleted && theme.isDeleted) {
        return false;
      }

      // 过滤非激活的主题
      if (!theme.isActive) {
        return false;
      }

      return true;
    });
  }

  /**
   * 搜索相关标签
   * @param keyword 关键字
   * @param limit 返回的最大数量
   * @returns 相关标签列表
   */
  async searchRelatedTags(keyword: string, limit: number = 10): Promise<string[]> {
    // 搜索包含关键字的练习和笔记
    const [exercises, notes] = await Promise.all([
      this.exerciseRepository.searchByKeyword(keyword, 50),
      this.noteRepository.searchByKeyword(keyword, 50)
    ]);

    // 提取所有标签
    const tagMap = new Map<string, number>();
    
    // 从练习中提取标签
    exercises.forEach(exercise => {
      exercise.tags.forEach(tag => {
        tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
      });
    });
    
    // 从笔记中提取标签
    notes.forEach(note => {
      note.tags.forEach(tag => {
        tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
      });
    });
    
    // 按出现频率排序
    const sortedTags = Array.from(tagMap.entries())
      .sort((a, b) => b[1] - a[1])
      .map(entry => entry[0])
      .slice(0, limit);
    
    return sortedTags;
  }
}
