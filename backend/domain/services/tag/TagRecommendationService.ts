import { Tag } from '../../models/tag/Tag';
import { TagRepository } from '../../repositories/tag/TagRepository';
import { LearningTemplateRepository } from '../../repositories/learningTemplate/LearningTemplateRepository';
import { ThemeRepository } from '../../repositories/content/theme/ThemeRepository';
import { TemplateTagRepository } from '../../repositories/learningTemplate/TemplateTagRepository';

/**
 * TagRecommendationService
 * 标签推荐服务，提供基于模板内容和主题的标签推荐功能
 */
export class TagRecommendationService {
  /**
   * 构造函数
   * @param tagRepository 标签仓库
   * @param learningTemplateRepository 学习模板仓库
   * @param themeRepository 主题仓库
   * @param templateTagRepository 模板标签关联仓库
   */
  constructor(
    private readonly tagRepository: TagRepository,
    private readonly learningTemplateRepository: LearningTemplateRepository,
    private readonly themeRepository: ThemeRepository,
    private readonly templateTagRepository?: TemplateTagRepository
  ) {}

  /**
   * 基于模板内容和主题推荐相关标签
   * @param templateId 学习模板ID
   * @param themeId 主题ID（可选，如果不提供则从模板中获取）
   * @param limit 返回的最大数量
   * @returns 推荐的标签列表
   */
  async recommendTagsForTemplate(templateId: number, themeId?: number, limit: number = 10): Promise<Tag[]> {
    // 获取模板信息
    const template = await this.learningTemplateRepository.findById(templateId);
    if (!template) {
      throw new Error(`学习模板ID ${templateId} 不存在`);
    }

    // 如果没有提供主题ID，则使用模板的主题ID
    const effectiveThemeId = themeId || template.themeId;

    // 获取模板当前的标签
    const currentTagIds: number[] = [];
    if (this.templateTagRepository) {
      const templateTags = await this.templateTagRepository.findByTemplateId(templateId);
      templateTags.forEach(tt => currentTagIds.push(tt.tagId));
    }

    // 获取同一主题下的其他模板
    const themeTemplates = await this.learningTemplateRepository.findByThemeId(effectiveThemeId);
    
    // 收集同主题下其他模板的标签
    const tagFrequencyMap = new Map<number, number>();
    
    // 如果有模板标签关联仓库，则获取标签使用频率
    if (this.templateTagRepository) {
      for (const t of themeTemplates) {
        // 跳过当前模板
        if (t.id === templateId) continue;
        
        const templateTags = await this.templateTagRepository.findByTemplateId(t.id);
        for (const tt of templateTags) {
          // 跳过当前模板已有的标签
          if (currentTagIds.includes(tt.tagId)) continue;
          
          const currentFreq = tagFrequencyMap.get(tt.tagId) || 0;
          tagFrequencyMap.set(tt.tagId, currentFreq + 1);
        }
      }
    }
    
    // 将标签ID按使用频率排序
    const sortedTagIds = Array.from(tagFrequencyMap.entries())
      .sort((a, b) => b[1] - a[1])
      .map(entry => entry[0]);
    
    // 如果没有足够的标签，添加热门标签
    if (sortedTagIds.length < limit) {
      const popularTags = await this.tagRepository.findByPopularity(10, limit);
      for (const tag of popularTags) {
        // 跳过已有的标签
        if (currentTagIds.includes(tag.id) || sortedTagIds.includes(tag.id)) continue;
        
        sortedTagIds.push(tag.id);
        if (sortedTagIds.length >= limit) break;
      }
    }
    
    // 获取标签详细信息
    const recommendedTags: Tag[] = [];
    for (const tagId of sortedTagIds.slice(0, limit)) {
      const tag = await this.tagRepository.findById(tagId);
      if (tag) recommendedTags.push(tag);
    }
    
    return recommendedTags;
  }

  /**
   * 基于模板内容推荐相关标签
   * @param title 模板标题
   * @param description 模板描述
   * @param themeId 主题ID
   * @param limit 返回的最大数量
   * @returns 推荐的标签列表
   */
  async recommendTagsForContent(title: string, description: string, themeId: number, limit: number = 10): Promise<Tag[]> {
    // 从标题和描述中提取关键词
    const keywords = this.extractKeywords(title, description);
    
    // 根据关键词搜索相关标签
    const tagMap = new Map<number, number>();
    
    for (const keyword of keywords) {
      // 跳过太短的关键词
      if (keyword.length < 2) continue;
      
      const tags = await this.tagRepository.searchByKeyword(keyword, 20);
      for (const tag of tags) {
        const score = tagMap.get(tag.id) || 0;
        // 根据标签在关键词搜索结果中的位置给予不同的分数
        tagMap.set(tag.id, score + 1);
      }
    }
    
    // 获取同主题下的热门标签
    const themeTemplates = await this.learningTemplateRepository.findByThemeId(themeId);
    
    // 如果有模板标签关联仓库，则获取标签使用频率
    if (this.templateTagRepository && themeTemplates.length > 0) {
      for (const template of themeTemplates) {
        const templateTags = await this.templateTagRepository.findByTemplateId(template.id);
        for (const tt of templateTags) {
          const score = tagMap.get(tt.tagId) || 0;
          // 同主题下的标签得分加权
          tagMap.set(tt.tagId, score + 0.5);
        }
      }
    }
    
    // 将标签ID按分数排序
    const sortedTagIds = Array.from(tagMap.entries())
      .sort((a, b) => b[1] - a[1])
      .map(entry => entry[0]);
    
    // 如果没有足够的标签，添加热门标签
    if (sortedTagIds.length < limit) {
      const popularTags = await this.tagRepository.findByPopularity(10, limit);
      for (const tag of popularTags) {
        // 跳过已有的标签
        if (sortedTagIds.includes(tag.id)) continue;
        
        sortedTagIds.push(tag.id);
        if (sortedTagIds.length >= limit) break;
      }
    }
    
    // 获取标签详细信息
    const recommendedTags: Tag[] = [];
    for (const tagId of sortedTagIds.slice(0, limit)) {
      const tag = await this.tagRepository.findById(tagId);
      if (tag) recommendedTags.push(tag);
    }
    
    return recommendedTags;
  }

  /**
   * 从文本中提取关键词
   * @param title 标题
   * @param description 描述
   * @returns 关键词列表
   */
  private extractKeywords(title: string, description: string): string[] {
    // 合并标题和描述
    const text = `${title} ${description}`;
    
    // 移除标点符号和特殊字符
    const cleanText = text.replace(/[^\w\s\u4e00-\u9fa5]/g, ' ');
    
    // 分词（简单实现，实际应用中可以使用更复杂的分词算法）
    const words = cleanText.split(/\s+/).filter(word => word.length > 0);
    
    // 去除停用词（简单实现，实际应用中可以使用更完整的停用词表）
    const stopWords = ['的', '了', '和', '与', '或', '在', '是', '有', '这', '那', '我', '你', '他', '她', '它', '们'];
    const filteredWords = words.filter(word => !stopWords.includes(word.toLowerCase()));
    
    return filteredWords;
  }
}
