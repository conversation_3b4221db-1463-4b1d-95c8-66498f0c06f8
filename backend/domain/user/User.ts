/**
 * 用户实体
 * 表示系统中的用户
 */
import { Entity } from '../Entity';

/**
 * 用户属性接口
 */
export interface UserProps {
  id: number;
  username: string;
  email: string;
  phone?: string;
  nickname?: string;
  avatar?: string;
  bio?: string;
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt?: Date | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  deletedAt?: Date | null;
  settings?: any;
  notificationSettings?: any;
  roles?: string[];
}

/**
 * 用户实体类
 */
export class User extends Entity {
  readonly username: string;
  readonly email: string;
  readonly phone?: string;
  readonly nickname?: string;
  readonly avatar?: string;
  readonly bio?: string;
  readonly isActive: boolean;
  readonly isVerified: boolean;
  readonly lastLoginAt?: Date | null;
  readonly createdAt?: Date | null;
  readonly updatedAt?: Date | null;
  readonly deletedAt?: Date | null;
  readonly settings?: any;
  readonly notificationSettings?: any;
  readonly roles: string[];

  /**
   * 构造函数
   * @param props 用户属性
   */
  constructor(props: UserProps) {
    super(props.id);
    this.username = props.username;
    this.email = props.email;
    this.phone = props.phone;
    this.nickname = props.nickname;
    this.avatar = props.avatar;
    this.bio = props.bio;
    this.isActive = props.isActive;
    this.isVerified = props.isVerified;
    this.lastLoginAt = props.lastLoginAt;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
    this.deletedAt = props.deletedAt;
    this.settings = props.settings;
    this.notificationSettings = props.notificationSettings;
    this.roles = props.roles || [];
  }

  /**
   * 判断用户是否为管理员
   * @returns 如果用户是管理员，则返回true，否则返回false
   */
  isAdmin(): boolean {
    return this.roles.includes('admin');
  }

  /**
   * 判断用户是否有指定角色
   * @param role 角色名称
   * @returns 如果用户有指定角色，则返回true，否则返回false
   */
  hasRole(role: string): boolean {
    return this.roles.includes(role);
  }

  /**
   * 判断用户是否已删除
   * @returns 如果用户已删除，则返回true，否则返回false
   */
  isDeleted(): boolean {
    return !!this.deletedAt;
  }

  /**
   * 获取用户的显示名称
   * @returns 用户的显示名称
   */
  getDisplayName(): string {
    return this.nickname || this.username;
  }
}
