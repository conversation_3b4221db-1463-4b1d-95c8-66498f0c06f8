/**
 * 事务管理示例代码
 * 展示如何使用不同的事务管理模式
 */
const { User, Profile, Tag, Note, sequelize } = require('../models');
const transactionManager = require('../utils/transaction-manager');
const { container } = require('../infrastructure/di');
const logger = require('../config/logger');

/**
 * 示例1：使用TransactionManager
 * 推荐用于传统分层架构
 */
async function createUserWithProfile(userData, profileData) {
  return transactionManager.runInTransaction(async (transaction) => {
    // 创建用户
    const user = await User.create(userData, { transaction });
    
    // 创建用户资料
    const profile = await Profile.create({
      ...profileData,
      user_id: user.id
    }, { transaction });
    
    // 返回结果
    return {
      user,
      profile
    };
  }, { context: 'createUserWithProfile' });
}

/**
 * 示例2：使用UnitOfWork
 * 推荐用于DDD架构
 */
async function createUserWithProfileDDD(userData, profileData) {
  // 获取UnitOfWork实例
  const unitOfWork = container.get('unitOfWork');
  
  // 获取仓库实例
  const userRepository = container.get('userRepository');
  const profileRepository = container.get('profileRepository');
  
  return unitOfWork.runInTransaction(async () => {
    // 创建用户
    const user = await userRepository.create(userData);
    
    // 创建用户资料
    const profile = await profileRepository.create({
      ...profileData,
      userId: user.id
    });
    
    // 返回结果
    return {
      user,
      profile
    };
  });
}

/**
 * 示例3：批量操作
 * 使用TransactionManager处理批量操作
 */
async function createTagsInBatch(tags) {
  return transactionManager.runBatchOperationInTransaction(
    tags,
    async (tagData, transaction) => {
      return Tag.create(tagData, { transaction });
    },
    {
      batchSize: 50,
      continueOnError: true,
      context: 'createTagsInBatch'
    }
  );
}

/**
 * 示例4：多个操作
 * 使用TransactionManager在一个事务中执行多个操作
 */
async function createNoteWithTags(noteData, tags) {
  return transactionManager.runInTransaction(async (transaction) => {
    // 创建笔记
    const note = await Note.create(noteData, { transaction });
    
    // 创建标签
    const createdTags = await Promise.all(
      tags.map(tagData => Tag.create({
        ...tagData,
        user_id: noteData.user_id
      }, { transaction }))
    );
    
    // 关联标签和笔记
    await Promise.all(
      createdTags.map(tag => note.addTag(tag, { transaction }))
    );
    
    // 返回结果
    return {
      note,
      tags: createdTags
    };
  }, { context: 'createNoteWithTags' });
}

/**
 * 示例5：只读事务
 * 使用TransactionManager的只读事务
 */
async function getUserWithNotes(userId) {
  return transactionManager.runInReadOnlyTransaction(async (transaction) => {
    // 查询用户
    const user = await User.findByPk(userId, {
      include: [
        {
          model: Note,
          as: 'notes'
        }
      ],
      transaction
    });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 返回结果
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      notes: user.notes.map(note => ({
        id: note.id,
        title: note.title,
        content: note.content,
        createdAt: note.created_at
      }))
    };
  }, { context: 'getUserWithNotes' });
}

/**
 * 示例6：错误处理
 * 展示如何处理事务中的错误
 */
async function transferPoints(fromUserId, toUserId, points) {
  return transactionManager.runInTransaction(async (transaction) => {
    // 查询用户
    const fromUser = await User.findByPk(fromUserId, { transaction });
    const toUser = await User.findByPk(toUserId, { transaction });
    
    if (!fromUser || !toUser) {
      throw new Error('用户不存在');
    }
    
    // 检查积分是否足够
    if (fromUser.points < points) {
      throw new Error('积分不足');
    }
    
    // 扣除积分
    await fromUser.update({
      points: fromUser.points - points
    }, { transaction });
    
    // 增加积分
    await toUser.update({
      points: toUser.points + points
    }, { transaction });
    
    // 记录积分转移
    await PointsTransaction.create({
      from_user_id: fromUserId,
      to_user_id: toUserId,
      points,
      type: 'transfer',
      status: 'completed'
    }, { transaction });
    
    // 返回结果
    return {
      fromUser: {
        id: fromUser.id,
        points: fromUser.points
      },
      toUser: {
        id: toUser.id,
        points: toUser.points
      },
      transferredPoints: points
    };
  }, { context: 'transferPoints' });
}

module.exports = {
  createUserWithProfile,
  createUserWithProfileDDD,
  createTagsInBatch,
  createNoteWithTags,
  getUserWithNotes,
  transferPoints
};
