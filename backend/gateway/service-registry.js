const { redisClient } = require('../config/redis');
const logger = require('../config/logger');
const axios = require('axios');

/**
 * 服务注册表
 * 管理微服务的注册、发现和健康检查
 */
class ServiceRegistry {
  constructor() {
    // 服务注册表前缀
    this.registryPrefix = 'service-registry:';
    
    // 服务健康检查间隔（毫秒）
    this.healthCheckInterval = 30000;
    
    // 服务健康检查超时（毫秒）
    this.healthCheckTimeout = 5000;
    
    // 服务注册过期时间（秒）
    this.serviceExpiry = 60;
    
    // 健康检查定时器
    this.healthCheckTimer = null;
    
    // 已注册的服务缓存
    this.servicesCache = new Map();
    
    // 服务缓存过期时间（毫秒）
    this.cacheExpiry = 10000;
    
    // 服务缓存最后更新时间
    this.cacheLastUpdated = 0;
  }
  
  /**
   * 初始化服务注册表
   */
  async init() {
    if (!redisClient.isOpen) {
      logger.warn('Redis客户端未连接，服务注册表将不可用');
      return false;
    }
    
    // 启动健康检查
    this.startHealthCheck();
    
    logger.info('服务注册表已初始化');
    return true;
  }
  
  /**
   * 注册服务
   * @param {Object} service - 服务信息
   * @param {String} service.id - 服务ID
   * @param {String} service.name - 服务名称
   * @param {String} service.version - 服务版本
   * @param {String} service.url - 服务URL
   * @param {String} service.healthCheckUrl - 健康检查URL
   * @returns {Promise<Boolean>} 是否成功
   */
  async register(service) {
    if (!redisClient.isOpen) {
      logger.warn('Redis客户端未连接，无法注册服务');
      return false;
    }
    
    try {
      // 验证服务信息
      if (!service.id || !service.name || !service.url) {
        logger.error('服务注册失败：缺少必要信息');
        return false;
      }
      
      // 构建服务键
      const serviceKey = `${this.registryPrefix}${service.id}`;
      
      // 添加注册时间和状态
      const serviceInfo = {
        ...service,
        registeredAt: Date.now(),
        status: 'registered',
        lastHealthCheck: null,
        healthStatus: 'unknown'
      };
      
      // 注册服务
      await redisClient.set(serviceKey, JSON.stringify(serviceInfo), {
        EX: this.serviceExpiry
      });
      
      // 添加到服务列表
      await redisClient.sAdd(`${this.registryPrefix}services`, service.id);
      
      // 添加到服务名称索引
      await redisClient.sAdd(`${this.registryPrefix}name:${service.name}`, service.id);
      
      // 如果有版本，添加到版本索引
      if (service.version) {
        await redisClient.sAdd(`${this.registryPrefix}version:${service.name}:${service.version}`, service.id);
      }
      
      // 清除缓存
      this.invalidateCache();
      
      logger.info(`服务已注册: ${service.name} (${service.id})`);
      return true;
    } catch (error) {
      logger.error(`服务注册失败: ${error.message}`);
      return false;
    }
  }
  
  /**
   * 注销服务
   * @param {String} serviceId - 服务ID
   * @returns {Promise<Boolean>} 是否成功
   */
  async deregister(serviceId) {
    if (!redisClient.isOpen) {
      logger.warn('Redis客户端未连接，无法注销服务');
      return false;
    }
    
    try {
      // 获取服务信息
      const serviceKey = `${this.registryPrefix}${serviceId}`;
      const serviceJson = await redisClient.get(serviceKey);
      
      if (!serviceJson) {
        logger.warn(`注销失败：服务不存在 ${serviceId}`);
        return false;
      }
      
      const service = JSON.parse(serviceJson);
      
      // 删除服务信息
      await redisClient.del(serviceKey);
      
      // 从服务列表移除
      await redisClient.sRem(`${this.registryPrefix}services`, serviceId);
      
      // 从服务名称索引移除
      await redisClient.sRem(`${this.registryPrefix}name:${service.name}`, serviceId);
      
      // 如果有版本，从版本索引移除
      if (service.version) {
        await redisClient.sRem(`${this.registryPrefix}version:${service.name}:${service.version}`, serviceId);
      }
      
      // 清除缓存
      this.invalidateCache();
      
      logger.info(`服务已注销: ${service.name} (${serviceId})`);
      return true;
    } catch (error) {
      logger.error(`服务注销失败: ${error.message}`);
      return false;
    }
  }
  
  /**
   * 获取服务信息
   * @param {String} serviceId - 服务ID
   * @returns {Promise<Object|null>} 服务信息
   */
  async getService(serviceId) {
    if (!redisClient.isOpen) {
      logger.warn('Redis客户端未连接，无法获取服务信息');
      return null;
    }
    
    try {
      const serviceKey = `${this.registryPrefix}${serviceId}`;
      const serviceJson = await redisClient.get(serviceKey);
      
      if (!serviceJson) {
        return null;
      }
      
      return JSON.parse(serviceJson);
    } catch (error) {
      logger.error(`获取服务信息失败: ${error.message}`);
      return null;
    }
  }
  
  /**
   * 获取所有服务
   * @returns {Promise<Array>} 服务列表
   */
  async getAllServices() {
    // 如果缓存有效，直接返回缓存
    if (this.isCacheValid()) {
      return Array.from(this.servicesCache.values());
    }
    
    if (!redisClient.isOpen) {
      logger.warn('Redis客户端未连接，无法获取服务列表');
      return [];
    }
    
    try {
      // 获取所有服务ID
      const serviceIds = await redisClient.sMembers(`${this.registryPrefix}services`);
      
      if (!serviceIds || serviceIds.length === 0) {
        return [];
      }
      
      // 获取所有服务信息
      const services = [];
      const newCache = new Map();
      
      for (const serviceId of serviceIds) {
        const serviceKey = `${this.registryPrefix}${serviceId}`;
        const serviceJson = await redisClient.get(serviceKey);
        
        if (serviceJson) {
          const service = JSON.parse(serviceJson);
          services.push(service);
          newCache.set(serviceId, service);
        }
      }
      
      // 更新缓存
      this.servicesCache = newCache;
      this.cacheLastUpdated = Date.now();
      
      return services;
    } catch (error) {
      logger.error(`获取服务列表失败: ${error.message}`);
      return [];
    }
  }
  
  /**
   * 根据名称获取服务
   * @param {String} name - 服务名称
   * @param {String} version - 服务版本（可选）
   * @returns {Promise<Array>} 服务列表
   */
  async getServicesByName(name, version = null) {
    if (!redisClient.isOpen) {
      logger.warn('Redis客户端未连接，无法获取服务');
      return [];
    }
    
    try {
      let serviceIds;
      
      if (version) {
        // 获取特定版本的服务
        serviceIds = await redisClient.sMembers(`${this.registryPrefix}version:${name}:${version}`);
      } else {
        // 获取所有版本的服务
        serviceIds = await redisClient.sMembers(`${this.registryPrefix}name:${name}`);
      }
      
      if (!serviceIds || serviceIds.length === 0) {
        return [];
      }
      
      // 获取服务信息
      const services = [];
      
      for (const serviceId of serviceIds) {
        const serviceKey = `${this.registryPrefix}${serviceId}`;
        const serviceJson = await redisClient.get(serviceKey);
        
        if (serviceJson) {
          services.push(JSON.parse(serviceJson));
        }
      }
      
      return services;
    } catch (error) {
      logger.error(`获取服务失败: ${error.message}`);
      return [];
    }
  }
  
  /**
   * 启动健康检查
   */
  startHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    
    this.healthCheckTimer = setInterval(async () => {
      await this.checkServicesHealth();
    }, this.healthCheckInterval);
    
    logger.info(`服务健康检查已启动，间隔: ${this.healthCheckInterval}ms`);
  }
  
  /**
   * 停止健康检查
   */
  stopHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
      logger.info('服务健康检查已停止');
    }
  }
  
  /**
   * 检查所有服务的健康状态
   */
  async checkServicesHealth() {
    if (!redisClient.isOpen) {
      return;
    }
    
    try {
      const services = await this.getAllServices();
      
      for (const service of services) {
        if (service.healthCheckUrl) {
          this.checkServiceHealth(service);
        }
      }
    } catch (error) {
      logger.error(`健康检查失败: ${error.message}`);
    }
  }
  
  /**
   * 检查单个服务的健康状态
   * @param {Object} service - 服务信息
   */
  async checkServiceHealth(service) {
    try {
      const healthCheckUrl = service.healthCheckUrl || `${service.url}/health`;
      
      // 执行健康检查
      const response = await axios.get(healthCheckUrl, {
        timeout: this.healthCheckTimeout
      });
      
      // 更新服务状态
      const isHealthy = response.status === 200;
      const healthStatus = isHealthy ? 'healthy' : 'unhealthy';
      
      await this.updateServiceHealth(service.id, healthStatus);
    } catch (error) {
      // 健康检查失败
      await this.updateServiceHealth(service.id, 'unhealthy');
      logger.warn(`服务健康检查失败: ${service.name} (${service.id}) - ${error.message}`);
    }
  }
  
  /**
   * 更新服务健康状态
   * @param {String} serviceId - 服务ID
   * @param {String} healthStatus - 健康状态
   */
  async updateServiceHealth(serviceId, healthStatus) {
    if (!redisClient.isOpen) {
      return;
    }
    
    try {
      const serviceKey = `${this.registryPrefix}${serviceId}`;
      const serviceJson = await redisClient.get(serviceKey);
      
      if (!serviceJson) {
        return;
      }
      
      const service = JSON.parse(serviceJson);
      
      // 更新健康状态
      service.healthStatus = healthStatus;
      service.lastHealthCheck = Date.now();
      
      // 更新服务信息
      await redisClient.set(serviceKey, JSON.stringify(service), {
        EX: this.serviceExpiry
      });
      
      // 更新缓存
      if (this.servicesCache.has(serviceId)) {
        this.servicesCache.set(serviceId, service);
      }
      
      logger.debug(`服务健康状态已更新: ${service.name} (${serviceId}) - ${healthStatus}`);
    } catch (error) {
      logger.error(`更新服务健康状态失败: ${error.message}`);
    }
  }
  
  /**
   * 检查缓存是否有效
   * @returns {Boolean} 是否有效
   */
  isCacheValid() {
    return (
      this.servicesCache.size > 0 &&
      Date.now() - this.cacheLastUpdated < this.cacheExpiry
    );
  }
  
  /**
   * 使缓存失效
   */
  invalidateCache() {
    this.cacheLastUpdated = 0;
  }
}

// 创建单例实例
const serviceRegistry = new ServiceRegistry();

module.exports = serviceRegistry;
