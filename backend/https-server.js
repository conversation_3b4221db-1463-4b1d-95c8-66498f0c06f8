/**
 * HTTPS服务器启动脚本
 */

require('dotenv').config();

const app = require('./server');
const { startHTTPSServer } = require('./middlewares/https');
const { httpsConfig, validateHTTPSConfig, generateSelfSignedCertificate, checkSSLCertificates } = require('./config/https');
const logger = require('./config/logger');

async function startHTTPS() {
  try {
    // 验证HTTPS配置
    const validation = validateHTTPSConfig();
    if (!validation.valid) {
      logger.error('HTTPS配置验证失败:');
      validation.errors.forEach(error => logger.error(`- ${error}`));
      return;
    }

    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => logger.warn(`- ${warning}`));
    }

    // 检查SSL证书
    const certCheck = checkSSLCertificates();
    if (!certCheck.valid) {
      logger.warn('SSL证书不存在，尝试生成自签名证书...');
      
      if (process.env.NODE_ENV === 'development') {
        const generated = generateSelfSignedCertificate();
        if (!generated) {
          logger.error('无法生成自签名证书，HTTPS服务器启动失败');
          return;
        }
      } else {
        logger.error('生产环境需要有效的SSL证书');
        return;
      }
    }

    // 启动HTTPS服务器
    const httpsServer = startHTTPSServer(app);
    
    if (httpsServer) {
      logger.info('🔒 HTTPS服务器启动成功');
      
      // 优雅关闭处理
      process.on('SIGTERM', () => {
        logger.info('接收到SIGTERM信号，正在关闭HTTPS服务器...');
        httpsServer.close(() => {
          logger.info('HTTPS服务器已关闭');
          process.exit(0);
        });
      });

      process.on('SIGINT', () => {
        logger.info('接收到SIGINT信号，正在关闭HTTPS服务器...');
        httpsServer.close(() => {
          logger.info('HTTPS服务器已关闭');
          process.exit(0);
        });
      });
    } else {
      logger.error('HTTPS服务器启动失败');
      process.exit(1);
    }
  } catch (error) {
    logger.error('HTTPS服务器启动异常:', error);
    process.exit(1);
  }
}

// 仅当直接运行此文件时才启动HTTPS服务器
if (require.main === module) {
  startHTTPS();
}

module.exports = { startHTTPS };