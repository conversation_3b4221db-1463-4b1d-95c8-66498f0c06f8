/**
 * 增量切换入口文件
 * 导出所有增量切换相关的功能
 */
const createSwitchMiddleware = require('./switch-middleware');
const { recordSwitchResult, getSwitchResults, getSwitchSummary, clearSwitchResults } = require('./switch-monitor');
const switchRoutes = require('./switch-routes');
const switchConfig = require('./switch-config');

/**
 * 注册增量切换路由
 * @param {Object} app - Express应用实例
 * @param {string} prefix - 路由前缀
 */
const registerSwitchRoutes = (app, prefix = '/api/v2/incremental-switch') => {
  app.use(prefix, switchRoutes);
};

module.exports = {
  // 中间件
  createSwitchMiddleware,
  
  // 路由
  registerSwitchRoutes,
  switchRoutes,
  
  // 工具
  recordSwitchResult,
  getSwitchResults,
  getSwitchSummary,
  clearSwitchResults,
  
  // 配置
  config: switchConfig
};
