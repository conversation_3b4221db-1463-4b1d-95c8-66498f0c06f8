/**
 * 增量切换控制器
 * 提供增量切换的API端点
 */
const switchConfig = require('./switch-config');
const { getSwitchResults, getSwitchSummary, clearSwitchResults } = require('./switch-monitor');

/**
 * 获取切换配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getSwitchConfig = (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        enabled: switchConfig.enabled,
        switchMode: switchConfig.switchMode,
        pathSwitch: switchConfig.pathSwitch,
        userSwitch: switchConfig.userSwitch,
        percentageSwitch: switchConfig.percentageSwitch,
        errorStrategy: switchConfig.errorStrategy,
        monitoring: {
          enabled: switchConfig.monitoring.enabled
        },
        priorityPaths: switchConfig.priorityPaths
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'SWITCH_CONFIG_ERROR',
        message: `获取切换配置失败: ${error.message}`
      }
    });
  }
};

/**
 * 更新切换配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const updateSwitchConfig = (req, res) => {
  try {
    const { 
      enabled, 
      switchMode, 
      pathSwitch, 
      userSwitch, 
      percentageSwitch,
      errorStrategy
    } = req.body;

    // 更新配置
    if (enabled !== undefined) {
      switchConfig.enabled = enabled;
    }

    if (switchMode) {
      switchConfig.switchMode = switchMode;
    }

    if (pathSwitch) {
      if (pathSwitch.realPaths) {
        switchConfig.pathSwitch.realPaths = pathSwitch.realPaths;
      }
      if (pathSwitch.stubPaths) {
        switchConfig.pathSwitch.stubPaths = pathSwitch.stubPaths;
      }
      if (pathSwitch.mixedPaths) {
        switchConfig.pathSwitch.mixedPaths = pathSwitch.mixedPaths;
      }
    }

    if (userSwitch) {
      if (userSwitch.realUserIds) {
        switchConfig.userSwitch.realUserIds = userSwitch.realUserIds;
      }
      if (userSwitch.stubUserIds) {
        switchConfig.userSwitch.stubUserIds = userSwitch.stubUserIds;
      }
      if (userSwitch.percentage !== undefined) {
        switchConfig.userSwitch.percentage = userSwitch.percentage;
      }
    }

    if (percentageSwitch) {
      if (percentageSwitch.percentage !== undefined) {
        switchConfig.percentageSwitch.percentage = percentageSwitch.percentage;
      }
    }

    if (errorStrategy) {
      switchConfig.errorStrategy = errorStrategy;
    }

    res.json({
      success: true,
      message: '切换配置已更新',
      data: {
        enabled: switchConfig.enabled,
        switchMode: switchConfig.switchMode,
        pathSwitch: switchConfig.pathSwitch,
        userSwitch: switchConfig.userSwitch,
        percentageSwitch: switchConfig.percentageSwitch,
        errorStrategy: switchConfig.errorStrategy
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'SWITCH_CONFIG_UPDATE_ERROR',
        message: `更新切换配置失败: ${error.message}`
      }
    });
  }
};

/**
 * 获取切换结果列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getSwitchResultsList = (req, res) => {
  try {
    const options = {
      path: req.query.path,
      method: req.query.method,
      useRealApi: req.query.useRealApi === 'true' ? true : req.query.useRealApi === 'false' ? false : undefined,
      success: req.query.success === 'true' ? true : req.query.success === 'false' ? false : undefined,
      limit: parseInt(req.query.limit) || 100,
      offset: parseInt(req.query.offset) || 0,
      sortBy: req.query.sortBy || 'timestamp',
      sortOrder: req.query.sortOrder || 'desc'
    };

    const results = getSwitchResults(options);

    res.json({
      success: true,
      data: {
        total: results.length,
        items: results
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'SWITCH_RESULTS_ERROR',
        message: `获取切换结果列表失败: ${error.message}`
      }
    });
  }
};

/**
 * 获取切换摘要
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getSwitchSummaryData = (req, res) => {
  try {
    const summary = getSwitchSummary();

    // 计算路径统计排序
    const pathStats = Object.entries(summary.pathStats).map(([path, stats]) => ({
      path,
      ...stats,
      realApiPercentage: stats.total > 0 ? (stats.realApi / stats.total) * 100 : 0,
      realApiSuccessRate: stats.realApi > 0 ? (stats.realApiSuccesses / stats.realApi) * 100 : 0,
      stubApiSuccessRate: stats.stubApi > 0 ? (stats.stubApiSuccesses / stats.stubApi) * 100 : 0
    }));

    // 按照真实API使用率排序
    pathStats.sort((a, b) => b.realApiPercentage - a.realApiPercentage);

    res.json({
      success: true,
      data: {
        ...summary,
        realApiPercentage: summary.totalRequests > 0 ? (summary.realApiRequests / summary.totalRequests) * 100 : 0,
        realApiSuccessRate: summary.realApiRequests > 0 ? (summary.realApiSuccesses / summary.realApiRequests) * 100 : 0,
        stubApiSuccessRate: summary.stubApiRequests > 0 ? (summary.stubApiSuccesses / summary.stubApiRequests) * 100 : 0,
        pathStats: pathStats.slice(0, 20) // 只返回前20个路径
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'SWITCH_SUMMARY_ERROR',
        message: `获取切换摘要失败: ${error.message}`
      }
    });
  }
};

/**
 * 清除切换结果
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const clearSwitchResultsData = (req, res) => {
  try {
    clearSwitchResults();

    res.json({
      success: true,
      message: '切换结果已清除'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'SWITCH_CLEAR_ERROR',
        message: `清除切换结果失败: ${error.message}`
      }
    });
  }
};

module.exports = {
  getSwitchConfig,
  updateSwitchConfig,
  getSwitchResultsList,
  getSwitchSummaryData,
  clearSwitchResultsData
};
