/**
 * 增量切换中间件
 * 根据配置将请求路由到模拟API或真实API
 */
const axios = require('axios');
const logger = require('../config/logger');
const switchConfig = require('./switch-config');
const { recordSwitchResult } = require('./switch-monitor');

/**
 * 创建增量切换中间件
 * @param {Object} options - 中间件选项
 * @returns {Function} Express中间件
 */
const createSwitchMiddleware = (options = {}) => {
  const config = { ...switchConfig, ...options };

  // 如果未启用增量切换，则返回一个空中间件
  if (!config.enabled) {
    return (req, res, next) => next();
  }

  // 创建用于发送请求的axios实例
  const stubApiClient = axios.create({
    baseURL: config.stubApiBaseUrl,
    timeout: config.timeout,
    headers: {
      'X-Incremental-Switch': 'true',
      'X-Switch-Target': 'stub'
    }
  });

  const realApiClient = axios.create({
    baseURL: config.realApiBaseUrl,
    timeout: config.timeout,
    headers: {
      'X-Incremental-Switch': 'true',
      'X-Switch-Target': 'real'
    }
  });

  /**
   * 判断是否应该使用真实API
   * @param {Object} req - Express请求对象
   * @returns {boolean} 是否应该使用真实API
   */
  const shouldUseRealApi = (req) => {
    // 如果是增量切换请求，则不进行切换（避免无限循环）
    if (req.headers['x-incremental-switch']) {
      return false;
    }

    // 根据切换模式判断
    switch (config.switchMode) {
      case 'path':
        return shouldUseRealApiByPath(req);
      case 'user':
        return shouldUseRealApiByUser(req);
      case 'percentage':
        return shouldUseRealApiByPercentage();
      default:
        return false;
    }
  };

  /**
   * 根据路径判断是否应该使用真实API
   * @param {Object} req - Express请求对象
   * @returns {boolean} 是否应该使用真实API
   */
  const shouldUseRealApiByPath = (req) => {
    const path = req.path;

    // 检查是否在完全切换到真实API的路径列表中
    if (config.pathSwitch.realPaths.some(p => path.startsWith(p))) {
      return true;
    }

    // 检查是否在完全使用模拟API的路径列表中
    if (config.pathSwitch.stubPaths.some(p => path.startsWith(p))) {
      return false;
    }

    // 检查是否在混合模式的路径列表中
    for (const mixedPath of config.pathSwitch.mixedPaths) {
      if (path.startsWith(mixedPath.path)) {
        return Math.random() * 100 < mixedPath.percentage;
      }
    }

    // 默认使用模拟API
    return false;
  };

  /**
   * 根据用户判断是否应该使用真实API
   * @param {Object} req - Express请求对象
   * @returns {boolean} 是否应该使用真实API
   */
  const shouldUseRealApiByUser = (req) => {
    // 尝试从请求中获取用户ID
    const userId = getUserIdFromRequest(req);

    // 如果无法获取用户ID，则使用模拟API
    if (!userId) {
      return false;
    }

    // 检查是否在完全切换到真实API的用户ID列表中
    if (config.userSwitch.realUserIds.includes(userId)) {
      return true;
    }

    // 检查是否在完全使用模拟API的用户ID列表中
    if (config.userSwitch.stubUserIds.includes(userId)) {
      return false;
    }

    // 根据用户ID的哈希值判断是否使用真实API
    // 这样可以确保同一用户的请求始终路由到同一个API
    const userHash = hashUserId(userId);
    return userHash % 100 < config.userSwitch.percentage;
  };

  /**
   * 根据百分比判断是否应该使用真实API
   * @returns {boolean} 是否应该使用真实API
   */
  const shouldUseRealApiByPercentage = () => {
    return Math.random() * 100 < config.percentageSwitch.percentage;
  };

  /**
   * 从请求中获取用户ID
   * @param {Object} req - Express请求对象
   * @returns {string|null} 用户ID或null
   */
  const getUserIdFromRequest = (req) => {
    // 尝试从JWT令牌中获取用户ID
    if (req.user && req.user.id) {
      return req.user.id.toString();
    }

    // 尝试从查询参数中获取用户ID
    if (req.query.userId) {
      return req.query.userId.toString();
    }

    // 尝试从请求体中获取用户ID
    if (req.body && req.body.userId) {
      return req.body.userId.toString();
    }

    // 无法获取用户ID
    return null;
  };

  /**
   * 计算用户ID的哈希值
   * @param {string} userId - 用户ID
   * @returns {number} 哈希值
   */
  const hashUserId = (userId) => {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash) % 100;
  };

  /**
   * 发送请求到目标API
   * @param {Object} req - Express请求对象
   * @param {boolean} useRealApi - 是否使用真实API
   * @returns {Promise<Object>} 响应对象
   */
  const sendRequest = async (req, useRealApi) => {
    try {
      // 构建请求配置
      const requestConfig = {
        method: req.method,
        url: req.path,
        params: req.query,
        data: req.body,
        headers: {
          ...req.headers,
          host: undefined, // 移除host头，避免冲突
          'content-length': undefined // 移除content-length头，让axios自动计算
        }
      };

      // 如果有认证头，则复制
      if (req.headers.authorization) {
        requestConfig.headers.authorization = req.headers.authorization;
      }

      // 发送请求到目标API
      const client = useRealApi ? realApiClient : stubApiClient;
      const startTime = Date.now();
      const response = await client(requestConfig);
      const endTime = Date.now();

      // 记录切换结果
      recordSwitchResult({
        path: req.path,
        method: req.method,
        useRealApi,
        success: true,
        responseTime: endTime - startTime,
        statusCode: response.status,
        timestamp: new Date().toISOString()
      });

      return response;
    } catch (error) {
      // 记录切换结果
      recordSwitchResult({
        path: req.path,
        method: req.method,
        useRealApi,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      // 根据错误处理策略处理错误
      if (config.errorStrategy === 'stub' && useRealApi) {
        // 如果使用真实API出错，则尝试使用模拟API
        logger.warn(`真实API请求失败，尝试使用模拟API: ${error.message}`);
        return sendRequest(req, false);
      } else if (config.errorStrategy === 'real' && !useRealApi) {
        // 如果使用模拟API出错，则尝试使用真实API
        logger.warn(`模拟API请求失败，尝试使用真实API: ${error.message}`);
        return sendRequest(req, true);
      } else {
        // 直接抛出错误
        throw error;
      }
    }
  };

  // 返回中间件函数
  return async (req, res, next) => {
    // 判断是否应该使用真实API
    const useRealApi = shouldUseRealApi(req);

    try {
      // 发送请求到目标API
      const response = await sendRequest(req, useRealApi);

      // 返回响应
      res.status(response.status);
      for (const [key, value] of Object.entries(response.headers)) {
        // 跳过某些头，避免冲突
        if (['transfer-encoding', 'connection'].includes(key.toLowerCase())) {
          continue;
        }
        res.set(key, value);
      }
      res.send(response.data);
    } catch (error) {
      // 处理错误
      logger.error(`增量切换请求失败: ${error.message}`);
      next(error);
    }
  };
};

module.exports = createSwitchMiddleware;
