/**
 * 切换效果监控工具
 * 用于监控增量切换的效果
 */
const fs = require('fs');
const path = require('path');
const logger = require('../config/logger');
const switchConfig = require('./switch-config');
const safeFs = require('../utils/safe-fs');
const safeObject = require('../utils/safe-object');

// 创建存储目录
const BASE_DIR = path.join(__dirname, '../');
const STORAGE_DIR_NAME = switchConfig.monitoring.dataPath;
const STORAGE_DIR = path.join(BASE_DIR, STORAGE_DIR_NAME);

// 使用安全的文件系统操作
if (!safeFs.existsSync(BASE_DIR, STORAGE_DIR_NAME)) {
  try {
    safeFs.mkdirSync(BASE_DIR, STORAGE_DIR_NAME, { recursive: true });
  } catch (error) {
    logger.error(`无法创建增量切换监控存储目录: ${error.message}`);
  }
}

// 监控数据文件路径
const RESULTS_FILE_NAME = 'switch-results.json';
const SUMMARY_FILE_NAME = 'switch-summary.json';
const RESULTS_FILE = path.join(STORAGE_DIR, RESULTS_FILE_NAME);
const SUMMARY_FILE = path.join(STORAGE_DIR, SUMMARY_FILE_NAME);

// 内存中的监控数据缓存
let switchResults = [];
let switchSummary = {
  totalRequests: 0,
  realApiRequests: 0,
  stubApiRequests: 0,
  realApiSuccesses: 0,
  stubApiSuccesses: 0,
  realApiFailures: 0,
  stubApiFailures: 0,
  avgRealApiResponseTime: 0,
  avgStubApiResponseTime: 0,
  pathStats: {},
  lastUpdated: new Date().toISOString()
};

// 加载现有的监控数据
try {
  // 使用安全的文件系统操作
  if (safeFs.existsSync(STORAGE_DIR, RESULTS_FILE_NAME)) {
    const resultsContent = safeFs.readFileSync(STORAGE_DIR, RESULTS_FILE_NAME, 'utf8');
    if (resultsContent) {
      switchResults = safeObject.safeJsonParse(resultsContent);
    }
  }

  if (safeFs.existsSync(STORAGE_DIR, SUMMARY_FILE_NAME)) {
    const summaryContent = safeFs.readFileSync(STORAGE_DIR, SUMMARY_FILE_NAME, 'utf8');
    if (summaryContent) {
      switchSummary = safeObject.safeJsonParse(summaryContent);
    }
  }
} catch (error) {
  logger.error(`加载增量切换监控数据失败: ${error.message}`);
}

/**
 * 记录切换结果
 * @param {Object} data - 切换数据
 */
const recordSwitchResult = (data) => {
  // 如果未启用监控，则不记录
  if (!switchConfig.monitoring.enabled) {
    return;
  }

  try {
    // 创建切换记录
    const result = {
      id: Date.now().toString(36) + Math.random().toString(36).substr(2, 5),
      path: data.path,
      method: data.method,
      useRealApi: data.useRealApi,
      success: data.success,
      responseTime: data.responseTime,
      statusCode: data.statusCode,
      error: data.error,
      timestamp: data.timestamp || new Date().toISOString()
    };

    // 更新摘要
    switchSummary.totalRequests++;
    if (data.useRealApi) {
      switchSummary.realApiRequests++;
      if (data.success) {
        switchSummary.realApiSuccesses++;
      } else {
        switchSummary.realApiFailures++;
      }
      if (data.responseTime) {
        const totalTime = switchSummary.avgRealApiResponseTime * (switchSummary.realApiSuccesses - 1) + data.responseTime;
        switchSummary.avgRealApiResponseTime = totalTime / switchSummary.realApiSuccesses;
      }
    } else {
      switchSummary.stubApiRequests++;
      if (data.success) {
        switchSummary.stubApiSuccesses++;
      } else {
        switchSummary.stubApiFailures++;
      }
      if (data.responseTime) {
        const totalTime = switchSummary.avgStubApiResponseTime * (switchSummary.stubApiSuccesses - 1) + data.responseTime;
        switchSummary.avgStubApiResponseTime = totalTime / switchSummary.stubApiSuccesses;
      }
    }

    // 更新路径统计
    // 使用安全的对象访问
    const path = data.path;
    // 验证路径格式
    if (typeof path !== 'string') {
      logger.warn(`无效的路径格式: ${path}`);
      return;
    }

    // 使用安全的对象访问
    if (!safeObject.safeGet(switchSummary.pathStats, path)) {
      const newPathStat = {
        total: 0,
        realApi: 0,
        stubApi: 0,
        realApiSuccesses: 0,
        stubApiSuccesses: 0,
        realApiFailures: 0,
        stubApiFailures: 0,
        avgRealApiResponseTime: 0,
        avgStubApiResponseTime: 0
      };
      safeObject.safeSet(switchSummary.pathStats, path, newPathStat);
    }

    const pathStats = safeObject.safeGet(switchSummary.pathStats, path);

    // 使用安全的对象操作
    safeObject.safeSet(pathStats, 'total', pathStats.total + 1);

    if (data.useRealApi) {
      safeObject.safeSet(pathStats, 'realApi', pathStats.realApi + 1);
      if (data.success) {
        safeObject.safeSet(pathStats, 'realApiSuccesses', pathStats.realApiSuccesses + 1);
      } else {
        safeObject.safeSet(pathStats, 'realApiFailures', pathStats.realApiFailures + 1);
      }
      if (data.responseTime) {
        const totalTime = pathStats.avgRealApiResponseTime *
                         (pathStats.realApiSuccesses - 1) +
                         data.responseTime;
        safeObject.safeSet(pathStats, 'avgRealApiResponseTime', totalTime / pathStats.realApiSuccesses);
      }
    } else {
      safeObject.safeSet(pathStats, 'stubApi', pathStats.stubApi + 1);
      if (data.success) {
        safeObject.safeSet(pathStats, 'stubApiSuccesses', pathStats.stubApiSuccesses + 1);
      } else {
        safeObject.safeSet(pathStats, 'stubApiFailures', pathStats.stubApiFailures + 1);
      }
      if (data.responseTime) {
        const totalTime = pathStats.avgStubApiResponseTime *
                         (pathStats.stubApiSuccesses - 1) +
                         data.responseTime;
        safeObject.safeSet(pathStats, 'avgStubApiResponseTime', totalTime / pathStats.stubApiSuccesses);
      }
    }

    switchSummary.lastUpdated = new Date().toISOString();

    // 添加到切换结果数组
    switchResults.push(result);

    // 限制切换结果数量，避免内存溢出
    const maxResults = 1000;
    if (switchResults.length > maxResults) {
      switchResults = switchResults.slice(-maxResults);
    }

    // 定期保存切换结果
    if (switchResults.length % 10 === 0) {
      saveSwitchResults();
    }
  } catch (error) {
    logger.error(`记录增量切换结果失败: ${error.message}`);
  }
};

/**
 * 保存切换结果到文件
 */
const saveSwitchResults = () => {
  try {
    // 使用安全的文件系统操作
    safeFs.writeFileSync(
      STORAGE_DIR,
      RESULTS_FILE_NAME,
      JSON.stringify(switchResults, null, 2),
      'utf8'
    );

    safeFs.writeFileSync(
      STORAGE_DIR,
      SUMMARY_FILE_NAME,
      JSON.stringify(switchSummary, null, 2),
      'utf8'
    );
  } catch (error) {
    logger.error(`保存增量切换监控数据失败: ${error.message}`);
  }
};

/**
 * 获取切换结果
 * @param {Object} options - 查询选项
 * @returns {Array} 切换结果数组
 */
const getSwitchResults = (options = {}) => {
  const {
    path,
    method,
    useRealApi,
    success,
    limit = 100,
    offset = 0,
    sortBy = 'timestamp',
    sortOrder = 'desc'
  } = options;

  // 过滤切换结果
  let filteredResults = [...switchResults];

  if (path) {
    filteredResults = filteredResults.filter(r => r.path === path);
  }

  if (method) {
    filteredResults = filteredResults.filter(r => r.method === method);
  }

  if (useRealApi !== undefined) {
    filteredResults = filteredResults.filter(r => r.useRealApi === useRealApi);
  }

  if (success !== undefined) {
    filteredResults = filteredResults.filter(r => r.success === success);
  }

  // 排序切换结果
  filteredResults.sort((a, b) => {
    // 使用安全的对象访问
    const valueA = safeObject.safeGet(a, sortBy);
    const valueB = safeObject.safeGet(b, sortBy);
    const order = sortOrder === 'asc' ? 1 : -1;

    if (valueA < valueB) return -1 * order;
    if (valueA > valueB) return 1 * order;
    return 0;
  });

  // 分页
  return filteredResults.slice(offset, offset + limit);
};

/**
 * 获取切换摘要
 * @returns {Object} 摘要对象
 */
const getSwitchSummary = () => {
  return switchSummary;
};

/**
 * 清除切换结果
 */
const clearSwitchResults = () => {
  switchResults = [];
  switchSummary = {
    totalRequests: 0,
    realApiRequests: 0,
    stubApiRequests: 0,
    realApiSuccesses: 0,
    stubApiSuccesses: 0,
    realApiFailures: 0,
    stubApiFailures: 0,
    avgRealApiResponseTime: 0,
    avgStubApiResponseTime: 0,
    pathStats: {},
    lastUpdated: new Date().toISOString()
  };
  saveSwitchResults();
};

// 定期保存切换结果
setInterval(saveSwitchResults, switchConfig.monitoring.saveInterval);

// 进程退出时保存切换结果
process.on('exit', () => {
  saveSwitchResults();
});

module.exports = {
  recordSwitchResult,
  getSwitchResults,
  getSwitchSummary,
  clearSwitchResults
};
