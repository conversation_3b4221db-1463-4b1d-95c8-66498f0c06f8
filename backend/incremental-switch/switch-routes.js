/**
 * 增量切换路由
 * 注册增量切换的API端点
 */
const express = require('express');
const router = express.Router();
const controller = require('./switch-controller');

/**
 * @swagger
 * /api/v2/incremental-switch/config:
 *   get:
 *     summary: 获取切换配置
 *     description: 获取增量切换的配置信息
 *     tags: [Incremental Switch]
 *     responses:
 *       200:
 *         description: 成功获取切换配置
 *       500:
 *         description: 服务器错误
 */
router.get('/config', controller.getSwitchConfig);

/**
 * @swagger
 * /api/v2/incremental-switch/config:
 *   put:
 *     summary: 更新切换配置
 *     description: 更新增量切换的配置信息
 *     tags: [Incremental Switch]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               enabled:
 *                 type: boolean
 *                 description: 是否启用增量切换
 *               switchMode:
 *                 type: string
 *                 enum: [path, user, percentage]
 *                 description: 切换模式
 *               pathSwitch:
 *                 type: object
 *                 description: 路径切换配置
 *               userSwitch:
 *                 type: object
 *                 description: 用户切换配置
 *               percentageSwitch:
 *                 type: object
 *                 description: 百分比切换配置
 *               errorStrategy:
 *                 type: string
 *                 enum: [stub, real, error]
 *                 description: 错误处理策略
 *     responses:
 *       200:
 *         description: 成功更新切换配置
 *       500:
 *         description: 服务器错误
 */
router.put('/config', controller.updateSwitchConfig);

/**
 * @swagger
 * /api/v2/incremental-switch/results:
 *   get:
 *     summary: 获取切换结果列表
 *     description: 获取增量切换的结果列表
 *     tags: [Incremental Switch]
 *     parameters:
 *       - in: query
 *         name: path
 *         schema:
 *           type: string
 *         description: 按路径筛选
 *       - in: query
 *         name: method
 *         schema:
 *           type: string
 *         description: 按HTTP方法筛选
 *       - in: query
 *         name: useRealApi
 *         schema:
 *           type: boolean
 *         description: 按是否使用真实API筛选
 *       - in: query
 *         name: success
 *         schema:
 *           type: boolean
 *         description: 按是否成功筛选
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: 返回结果数量限制
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: 返回结果偏移量
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: timestamp
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取切换结果列表
 *       500:
 *         description: 服务器错误
 */
router.get('/results', controller.getSwitchResultsList);

/**
 * @swagger
 * /api/v2/incremental-switch/summary:
 *   get:
 *     summary: 获取切换摘要
 *     description: 获取增量切换的摘要信息
 *     tags: [Incremental Switch]
 *     responses:
 *       200:
 *         description: 成功获取切换摘要
 *       500:
 *         description: 服务器错误
 */
router.get('/summary', controller.getSwitchSummaryData);

/**
 * @swagger
 * /api/v2/incremental-switch/clear:
 *   post:
 *     summary: 清除切换结果
 *     description: 清除所有切换结果数据
 *     tags: [Incremental Switch]
 *     responses:
 *       200:
 *         description: 成功清除切换结果
 *       500:
 *         description: 服务器错误
 */
router.post('/clear', controller.clearSwitchResultsData);

module.exports = router;
