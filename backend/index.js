import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import morgan from 'morgan';
import helmet from 'helmet';
import compression from 'compression';
import { configureRoutes } from './interfaces/api/routes';
// 导入统一DI容器
import './infrastructure/di';

// 创建Express应用
const app = express();

// 注意：统一DI容器已在导入时自动初始化

// 配置中间件
app.use(helmet()); // 安全头
app.use(compression()); // 压缩响应
app.use(morgan('dev')); // 日志
app.use(cors()); // CORS
app.use(bodyParser.json()); // 解析JSON请求体
app.use(bodyParser.urlencoded({ extended: true })); // 解析URL编码的请求体

// 设置字符编码中间件
app.use((req, res, next) => {
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  next();
});

// 配置路由
configureRoutes(app);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(err.status || 500).json({
    error: {
      message: err.message,
      status: err.status || 500
    }
  });
});

// 导出Express应用
export default app;
