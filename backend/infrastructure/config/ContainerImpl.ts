import { Container, BindOptions } from './Container';

/**
 * 工厂函数类型
 */
type Factory<T> = (container: Container) => T;

/**
 * 注册类型
 */
type Registration<T> = {
  factory: Factory<T>;
  singleton: boolean;
  instance?: T;
};

/**
 * ContainerImpl类
 * 依赖注入容器的实现，使用Map存储注册
 */
export class ContainerImpl implements Container {
  /**
   * 注册映射，键为标识符，值为注册
   */
  private registrations = new Map<string | symbol, Registration<any>>();

  /**
   * 绑定工厂函数
   * @param token 标识符
   * @param factory 工厂函数
   * @param options 绑定选项
   */
  bind<T>(token: string | symbol, factory: Factory<T>, options: BindOptions = {}): void {
    const registration: Registration<T> = {
      factory,
      singleton: options.singleton !== false
    };

    this.registrations.set(token, registration);

    if (options.eager && options.singleton) {
      this.get(token);
    }
  }

  /**
   * 绑定类
   * @param token 标识符
   * @param constructor 构造函数
   * @param options 绑定选项
   */
  bindClass<T>(token: string | symbol, constructor: new (...args: any[]) => T, options: BindOptions = {}): void {
    this.bind(token, (container) => this.resolve(constructor), options);
  }

  /**
   * 绑定接口到实现
   * @param token 接口标识符
   * @param implementation 实现标识符
   * @param options 绑定选项
   */
  bindInterface<T>(token: string | symbol, implementation: string | symbol, options: BindOptions = {}): void {
    this.bind(token, (container) => container.get(implementation), options);
  }

  /**
   * 获取实例
   * @param token 标识符
   * @returns 实例
   */
  get<T>(token: string | symbol): T {
    const registration = this.registrations.get(token);
    if (!registration) {
      throw new Error(`未找到标识符的注册: ${String(token)}`);
    }

    if (registration.singleton) {
      if (!registration.instance) {
        registration.instance = registration.factory(this);
      }
      return registration.instance;
    }

    return registration.factory(this);
  }

  /**
   * 解析类
   * @param constructor 构造函数
   * @returns 实例
   */
  resolve<T>(constructor: new (...args: any[]) => T): T {
    const paramTypes = Reflect.getMetadata('design:paramtypes', constructor) || [];
    const params = paramTypes.map((paramType: any) => this.get(paramType));
    return new constructor(...params);
  }

  /**
   * 检查是否已绑定
   * @param token 标识符
   * @returns 是否已绑定
   */
  has(token: string | symbol): boolean {
    return this.registrations.has(token);
  }
}
