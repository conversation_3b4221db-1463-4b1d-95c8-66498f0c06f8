import { ContainerImpl } from './ContainerImpl';
import { Sequelize } from 'sequelize';
import { UnitOfWork } from '../persistence/UnitOfWork';
import { SequelizeUnitOfWork } from '../persistence/SequelizeUnitOfWork';
import { EventBus } from '../events/EventBus';
import { EventBusImpl } from '../events/EventBusImpl';
import { EventPublisher } from '../events/EventPublisher';
import { EventPublisherImpl } from '../events/EventPublisherImpl';
import { EventHandlerRegistry } from '../events/EventHandlerRegistry';
import { EventStore } from '../events/EventStore';
import { DatabaseEventStore } from '../events/DatabaseEventStore';
import { EventMonitoringService } from '../monitoring/EventMonitoringService';
import { WebSocketService } from '../services/notification/WebSocketService';
import { Logger } from '../logging/Logger';
import { ConsoleLogger } from '../logging/ConsoleLogger';
import { DeadLetterQueueService } from '../events/DeadLetterQueueService';

// 创建容器实例
const container = new ContainerImpl();

// 配置基础设施
function configureInfrastructure() {
  // 配置日志记录器
  if (!container.has('logger')) {
    container.bind<Logger>('logger', () => {
      const logger = require('../../config/logger');
      return logger;
    });
  }

  // 配置WebSocketService
  if (!container.has('webSocketService')) {
    container.bind<WebSocketService>('webSocketService', (c) => {
      // 注意：这里只是获取已经初始化的WebSocketService实例
      // 实际初始化应该在服务启动时进行
      const webSocketService = require('../../services/websocket.service');
      return webSocketService;
    });
  }

  // 配置Sequelize
  if (!container.has('sequelize')) {
    container.bind('sequelize', () => {
      const sequelize = require('../../config/database').sequelize;
      return sequelize;
    });
  }

  // 配置工作单元
  if (!container.has('unitOfWork')) {
    container.bind<UnitOfWork>('unitOfWork', (c) => new SequelizeUnitOfWork(c.get('sequelize')));
  }

  // 配置事件总线
  if (!container.has('eventBus')) {
    container.bind<EventBus>('eventBus', () => new EventBusImpl());
  }

  // 配置事件存储
  if (!container.has('eventStore')) {
    container.bind<EventStore>('eventStore', (c) => new DatabaseEventStore(c.get('sequelize')));
  }

  // 配置事件发布者
  if (!container.has('eventPublisher')) {
    container.bind<EventPublisher>('eventPublisher', (c) => new EventPublisherImpl(
      c.get('eventBus'),
      c.get('eventStore')
    ));
  }

  // 配置事件监控服务
  if (!container.has('eventMonitoringService')) {
    container.bind('eventMonitoringService', (c) => {
      const logger = require('../../config/logger');
      const monitoringService = new EventMonitoringService(
        c.get('eventBus'),
        logger,
        c.get('eventPublisher')
      );

      // 设置处理告警回调
      monitoringService.setProcessingAlertCallback((eventType, processingTime, event) => {
        logger.error(`事件处理时间过长: ${eventType}, ${processingTime}ms`, {
          eventId: event.eventId,
          eventType,
          processingTime,
          aggregateId: event.aggregateId,
          aggregateType: event.aggregateType
        });
      });

      // 设置发布告警回调
      monitoringService.setPublishingAlertCallback((eventType, publishingTime, event) => {
        logger.error(`事件发布时间过长: ${eventType}, ${publishingTime}ms`, {
          eventId: event.eventId,
          eventType,
          publishingTime,
          aggregateId: event.aggregateId,
          aggregateType: event.aggregateType
        });
      });

      return monitoringService;
    });
  }
}

// 配置标签领域
function configureTagDomain() {
  // 导入标签领域的模型
  const Tag = require('../../models/tag.model');
  const TagCategory = require('../../models/tagCategory.model');
  const TagSynonym = require('../../models/tagSynonym.model');
  const TagFeedback = require('../../models/tagFeedback.model');

  // 导入标签领域的仓库实现
  const { SequelizeTagRepository } = require('../persistence/repositories/tag/SequelizeTagRepository');
  const { SequelizeTagCategoryRepository } = require('../persistence/repositories/tag/SequelizeTagCategoryRepository');

  // 导入标签领域的服务
  const { TagDomainService } = require('../../domain/services/tag/TagDomainService');
  const { TagApplicationService } = require('../../application/services/tag/TagApplicationService');
  const { TagCategoryApplicationService } = require('../../application/services/tag/TagCategoryApplicationService');

  // 配置标签仓库
  container.bind('tagRepository', (c) => new SequelizeTagRepository(
    c.get('unitOfWork'),
    c.get('eventPublisher'),
    c.get('sequelize'),
    Tag,
    TagSynonym,
    TagFeedback
  ));

  // 配置标签分类仓库
  container.bind('tagCategoryRepository', (c) => new SequelizeTagCategoryRepository(
    c.get('unitOfWork'),
    c.get('eventPublisher'),
    c.get('sequelize'),
    TagCategory
  ));

  // 配置标签领域服务
  container.bind('tagDomainService', (c) => new TagDomainService(
    c.get('tagRepository')
  ));

  // 配置标签应用服务
  container.bind('tagApplicationService', (c) => new TagApplicationService(
    c.get('tagRepository'),
    c.get('tagDomainService'),
    c.get('unitOfWork')
  ));

  // 配置标签分类应用服务
  container.bind('tagCategoryApplicationService', (c) => new TagCategoryApplicationService(
    c.get('tagCategoryRepository'),
    c.get('tagRepository'),
    c.get('unitOfWork')
  ));
}

// 配置学习内容领域
function configureContentDomain() {
  // 导入学习内容领域的模型
  const LearningPlan = require('../../models/learningPlan.model');
  const Theme = require('../../models/theme.model');
  const Exercise = require('../../models/exercise.model');
  const Note = require('../../models/note.model');

  // 导入学习内容领域的仓库实现
  const { SequelizeLearningPlanRepository } = require('../persistence/repositories/content/learningPlan/SequelizeLearningPlanRepository');
  const { SequelizeThemeRepository } = require('../persistence/repositories/content/theme/SequelizeThemeRepository');
  const { SequelizeExerciseRepository } = require('../persistence/repositories/content/exercise/SequelizeExerciseRepository');
  const { SequelizeNoteRepository } = require('../persistence/repositories/content/note/SequelizeNoteRepository');

  // 导入学习内容领域的服务
  const { LearningPathService } = require('../../domain/services/content/LearningPathService');
  const { ContentRecommendationService } = require('../../domain/services/content/ContentRecommendationService');
  const { LearningPlanApplicationService } = require('../../application/services/content/learningPlan/LearningPlanApplicationService');
  const { ThemeApplicationService } = require('../../application/services/content/theme/ThemeApplicationService');
  const { ExerciseApplicationService } = require('../../application/services/content/exercise/ExerciseApplicationService');
  const { NoteApplicationService } = require('../../application/services/content/note/NoteApplicationService');

  // 配置学习计划仓库
  container.bind('learningPlanRepository', (c) => new SequelizeLearningPlanRepository(
    c.get('unitOfWork'),
    c.get('eventPublisher'),
    c.get('sequelize'),
    LearningPlan
  ));

  // 配置主题仓库
  container.bind('themeRepository', (c) => new SequelizeThemeRepository(
    c.get('unitOfWork'),
    c.get('eventPublisher'),
    c.get('sequelize'),
    Theme
  ));

  // 配置练习仓库
  container.bind('exerciseRepository', (c) => new SequelizeExerciseRepository(
    c.get('unitOfWork'),
    c.get('eventPublisher'),
    c.get('sequelize'),
    Exercise
  ));

  // 配置笔记仓库
  container.bind('noteRepository', (c) => new SequelizeNoteRepository(
    c.get('unitOfWork'),
    c.get('eventPublisher'),
    c.get('sequelize'),
    Note
  ));

  // 配置学习路径服务
  container.bind('learningPathService', (c) => new LearningPathService(
    c.get('themeRepository')
  ));

  // 配置内容推荐服务
  container.bind('contentRecommendationService', (c) => new ContentRecommendationService(
    c.get('exerciseRepository'),
    c.get('noteRepository')
  ));

  // 配置学习计划应用服务
  container.bind('learningPlanApplicationService', (c) => new LearningPlanApplicationService(
    c.get('learningPlanRepository'),
    c.get('learningPathService'),
    c.get('unitOfWork')
  ));

  // 配置主题应用服务
  container.bind('themeApplicationService', (c) => new ThemeApplicationService(
    c.get('themeRepository'),
    c.get('unitOfWork')
  ));

  // 配置练习应用服务
  container.bind('exerciseApplicationService', (c) => new ExerciseApplicationService(
    c.get('exerciseRepository'),
    c.get('contentRecommendationService'),
    c.get('unitOfWork')
  ));

  // 配置笔记应用服务
  container.bind('noteApplicationService', (c) => new NoteApplicationService(
    c.get('noteRepository'),
    c.get('contentRecommendationService'),
    c.get('unitOfWork')
  ));
}

// 配置事件处理器
function configureEventHandlers() {
  // 导入事件处理器
  const { ExerciseCreatedEventHandler } = require('../../application/events/content/exercise/ExerciseCreatedEventHandler');
  const { ExerciseCompletedEventHandler } = require('../../application/events/content/exercise/ExerciseCompletedEventHandler');
  const { UserLeveledUpEventHandler } = require('../../application/events/user/UserLeveledUpEventHandler');
  const { AchievementUnlockedEventHandler } = require('../../application/events/gamification/AchievementUnlockedEventHandler');
  const { NoteCreatedEventHandler } = require('../../application/events/content/note/NoteCreatedEventHandler');
  const { NotePublishedEventHandler } = require('../../application/events/content/note/NotePublishedEventHandler');
  const { LearningPlanCreatedEventHandler } = require('../../application/events/content/learningPlan/LearningPlanCreatedEventHandler');
  const { LearningPlanCompletedEventHandler } = require('../../application/events/content/learningPlan/LearningPlanCompletedEventHandler');
  const { BadgeAwardedEventHandler } = require('../../application/events/gamification/BadgeAwardedEventHandler');
  const { ThemeCreatedEventHandler } = require('../../application/events/content/theme/ThemeCreatedEventHandler');

  // 注册事件处理器
  container.bind('exerciseCreatedEventHandler', (c) => new ExerciseCreatedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('exerciseCompletedEventHandler', (c) => new ExerciseCompletedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('userLeveledUpEventHandler', (c) => new UserLeveledUpEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('achievementUnlockedEventHandler', (c) => new AchievementUnlockedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('noteCreatedEventHandler', (c) => new NoteCreatedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('notePublishedEventHandler', (c) => new NotePublishedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('learningPlanCreatedEventHandler', (c) => new LearningPlanCreatedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('learningPlanCompletedEventHandler', (c) => new LearningPlanCompletedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('badgeAwardedEventHandler', (c) => new BadgeAwardedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));
  container.bind('themeCreatedEventHandler', (c) => new ThemeCreatedEventHandler(
    c.get('webSocketService'),
    c.get('logger'),
    c.has('deadLetterQueueService') ? c.get('deadLetterQueueService') : undefined
  ));

  // 创建并配置事件处理器注册表
  container.bind('eventHandlerRegistry', (c) => new EventHandlerRegistry(
    c.get('eventBus'),
    c
  ));
}

// 导入集成领域的配置
import { configureIntegrationContainer } from '../../container/integration.container';

// 初始化容器
function initializeContainer() {
  configureInfrastructure();
  configureTagDomain();
  configureContentDomain();
  configureEventHandlers();
  configureIntegrationContainer(container);

  // 注册事件处理器
  const eventHandlerRegistry = container.get<EventHandlerRegistry>('eventHandlerRegistry');
  eventHandlerRegistry.registerHandlers();
}

// 导出容器
export { container, initializeContainer };
