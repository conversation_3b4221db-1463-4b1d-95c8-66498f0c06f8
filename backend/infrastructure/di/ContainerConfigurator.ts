/**
 * 容器配置器
 * 用于配置统一的容器
 */

import { Container } from '../config/Container';
import { Sequelize } from 'sequelize';
import { UnitOfWork } from '../persistence/UnitOfWork';
import { SequelizeUnitOfWork } from '../persistence/SequelizeUnitOfWork';
import { EventBus } from '../events/EventBus';
import { EventBusImpl } from '../events/EventBusImpl';
import { EventPublisher } from '../events/EventPublisher';
import { EventPublisherImpl } from '../events/EventPublisherImpl';
import { EventHandlerRegistry } from '../events/EventHandlerRegistry';
import { EventStore } from '../events/EventStore';
import { DatabaseEventStore } from '../events/DatabaseEventStore';
import { EventMonitoringService } from '../monitoring/EventMonitoringService';
import { DeadLetterQueueService } from '../events/DeadLetterQueueService';

/**
 * 容器配置器类
 * 用于配置统一的容器
 */
export class ContainerConfigurator {
  /**
   * 配置基础设施
   * @param container 容器实例
   */
  static configureInfrastructure(container: Container): void {
    // 配置Sequelize
    if (!container.has('sequelize')) {
      container.bind('sequelize', () => {
        const sequelize = require('../../config/database').sequelize;
        return sequelize;
      });
    }

    // 配置工作单元
    if (!container.has('unitOfWork')) {
      container.bind<UnitOfWork>('unitOfWork', (c) => new SequelizeUnitOfWork(c.get('sequelize')));
    }

    // 配置事件总线
    if (!container.has('eventBus')) {
      container.bind<EventBus>('eventBus', () => new EventBusImpl());
    }

    // 配置事件存储
    if (!container.has('eventStore')) {
      container.bind<EventStore>('eventStore', (c) => new DatabaseEventStore(c.get('sequelize')));
    }

    // 配置事件发布者
    if (!container.has('eventPublisher')) {
      container.bind<EventPublisher>('eventPublisher', (c) => new EventPublisherImpl(
        c.get('eventBus'),
        c.get('eventStore')
      ));
    }

    // 配置事件监控服务
    if (!container.has('eventMonitoringService')) {
      container.bind('eventMonitoringService', (c) => {
        const logger = require('../../config/logger');
        const monitoringService = new EventMonitoringService(
          c.get('eventBus'),
          logger,
          c.get('eventPublisher')
        );

        // 设置处理告警回调
        monitoringService.setProcessingAlertCallback((eventType, processingTime, event) => {
          logger.error(`事件处理时间过长: ${eventType}, ${processingTime}ms`, {
            eventId: event.eventId,
            eventType,
            processingTime,
            aggregateId: event.aggregateId,
            aggregateType: event.aggregateType
          });
        });

        // 设置发布告警回调
        monitoringService.setPublishingAlertCallback((eventType, publishingTime, event) => {
          logger.error(`事件发布时间过长: ${eventType}, ${publishingTime}ms`, {
            eventId: event.eventId,
            eventType,
            publishingTime,
            aggregateId: event.aggregateId,
            aggregateType: event.aggregateType
          });
        });

        return monitoringService;
      });
    }

    // 配置死信队列服务
    if (!container.has('deadLetterQueueService')) {
      container.bind('deadLetterQueueService', (c) => {
        const DeadLetterQueueModel = require('../../models/DeadLetterQueue');
        return new DeadLetterQueueService(
          c.get('eventBus'),
          DeadLetterQueueModel
        );
      });
    }
  }

  /**
   * 配置事件处理器
   * @param container 容器实例
   */
  static configureEventHandlers(container: Container): void {
    // 导入事件处理器
    const { ExerciseCreatedEventHandler } = require('../../application/events/content/exercise/ExerciseCreatedEventHandler');
    const { ExerciseCompletedEventHandler } = require('../../application/events/content/exercise/ExerciseCompletedEventHandler');
    const { UserLeveledUpEventHandler } = require('../../application/events/user/UserLeveledUpEventHandler');
    const { AchievementUnlockedEventHandler } = require('../../application/events/gamification/AchievementUnlockedEventHandler');
    const { NoteCreatedEventHandler } = require('../../application/events/content/note/NoteCreatedEventHandler');
    const { NotePublishedEventHandler } = require('../../application/events/content/note/NotePublishedEventHandler');
    const { LearningPlanCreatedEventHandler } = require('../../application/events/content/learningPlan/LearningPlanCreatedEventHandler');
    const { LearningPlanCompletedEventHandler } = require('../../application/events/content/learningPlan/LearningPlanCompletedEventHandler');
    const { BadgeAwardedEventHandler } = require('../../application/events/gamification/BadgeAwardedEventHandler');
    const { ThemeCreatedEventHandler } = require('../../application/events/content/theme/ThemeCreatedEventHandler');

    // 导入用户权限相关事件处理器
    const { UserRoleAddedEventHandler } = require('../../application/events/user/UserRoleAddedEventHandler');
    const { UserRoleRemovedEventHandler } = require('../../application/events/user/UserRoleRemovedEventHandler');
    const { RolePermissionAddedEventHandler } = require('../../application/events/user/RolePermissionAddedEventHandler');
    const { RolePermissionRemovedEventHandler } = require('../../application/events/user/RolePermissionRemovedEventHandler');
    const { UserStatusChangedEventHandler } = require('../../application/events/user/UserStatusChangedEventHandler');

    // 注册事件处理器
    container.bind('exerciseCreatedEventHandler', () => new ExerciseCreatedEventHandler());
    container.bind('exerciseCompletedEventHandler', () => new ExerciseCompletedEventHandler());
    container.bind('userLeveledUpEventHandler', () => new UserLeveledUpEventHandler());
    container.bind('achievementUnlockedEventHandler', () => new AchievementUnlockedEventHandler());
    container.bind('noteCreatedEventHandler', () => new NoteCreatedEventHandler());
    container.bind('notePublishedEventHandler', () => new NotePublishedEventHandler());
    container.bind('learningPlanCreatedEventHandler', () => new LearningPlanCreatedEventHandler());
    container.bind('learningPlanCompletedEventHandler', () => new LearningPlanCompletedEventHandler());
    container.bind('badgeAwardedEventHandler', () => new BadgeAwardedEventHandler());
    container.bind('themeCreatedEventHandler', () => new ThemeCreatedEventHandler());

    // 注册用户权限相关事件处理器
    container.bind('userRoleAddedEventHandler', () => new UserRoleAddedEventHandler());
    container.bind('userRoleRemovedEventHandler', () => new UserRoleRemovedEventHandler());
    container.bind('rolePermissionAddedEventHandler', () => new RolePermissionAddedEventHandler());
    container.bind('rolePermissionRemovedEventHandler', () => new RolePermissionRemovedEventHandler());
    container.bind('userStatusChangedEventHandler', () => new UserStatusChangedEventHandler());

    // 创建并配置事件处理器注册表
    container.bind('eventHandlerRegistry', (c) => new EventHandlerRegistry(
      c.get('eventBus'),
      c
    ));
  }

  /**
   * 初始化容器
   * @param container 容器实例
   */
  static initializeContainer(container: Container): void {
    // 配置基础设施
    this.configureInfrastructure(container);

    // 配置事件处理器
    this.configureEventHandlers(container);

    // 导入并配置其他领域
    // 这里可以导入其他领域的配置函数，如configureTagDomain, configureContentDomain等
    // 或者直接调用现有的配置函数

    // 注册事件处理器
    const eventHandlerRegistry = container.get<EventHandlerRegistry>('eventHandlerRegistry');
    eventHandlerRegistry.registerHandlers();
  }
}
