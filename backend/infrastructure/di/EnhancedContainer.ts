/**
 * 增强版容器接口
 * 提供更强大的依赖注入功能
 */
import { Container } from '../config/Container';

export interface EnhancedContainer extends Container {
  /**
   * 绑定接口到实现
   * @param token 接口标识符
   * @param implementation 实现标识符
   * @param options 绑定选项
   */
  bindInterface<T>(token: string | symbol, implementation: string | symbol, options?: any): void;

  /**
   * 绑定工厂函数
   * @param token 标识符
   * @param factory 工厂函数
   * @param options 绑定选项
   */
  bindFactory<T>(token: string | symbol, factory: (container: EnhancedContainer) => T, options?: any): void;

  /**
   * 绑定常量值
   * @param token 标识符
   * @param value 常量值
   */
  bindConstant<T>(token: string | symbol, value: T): void;

  /**
   * 创建子容器
   * 子容器继承父容器的所有绑定，但可以覆盖或添加新的绑定
   * @returns 子容器
   */
  createChildContainer(): EnhancedContainer;

  /**
   * 获取所有绑定的标识符
   * @returns 标识符数组
   */
  getAllBindings(): (string | symbol)[];

  /**
   * 解析类型
   * 自动注入构造函数参数
   * @param constructor 构造函数
   * @returns 实例
   */
  resolve<T>(constructor: new (...args: any[]) => T): T;

  /**
   * 获取标识符的绑定类型
   * @param token 标识符
   * @returns 绑定类型（'value'、'factory'、'interface'、'constant'）
   */
  getBindingType(token: string | symbol): string | null;

  /**
   * 获取标识符的绑定选项
   * @param token 标识符
   * @returns 绑定选项
   */
  getBindingOptions(token: string | symbol): any;

  /**
   * 获取标识符的绑定描述
   * @param token 标识符
   * @returns 绑定描述
   */
  getBindingDescription(token: string | symbol): string;

  /**
   * 获取容器的唯一标识符
   * @returns 容器ID
   */
  getId(): string;

  /**
   * 获取容器的名称
   * @returns 容器名称
   */
  getName(): string;

  /**
   * 设置容器的名称
   * @param name 容器名称
   */
  setName(name: string): void;

  /**
   * 获取容器的父容器
   * @returns 父容器或null
   */
  getParent(): EnhancedContainer | null;

  /**
   * 获取容器的所有子容器
   * @returns 子容器数组
   */
  getChildren(): EnhancedContainer[];

  /**
   * 销毁容器
   * 清除所有绑定和子容器
   */
  dispose(): void;
}
