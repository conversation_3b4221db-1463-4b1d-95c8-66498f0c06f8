/**
 * 增强版容器实现
 * 提供更强大的依赖注入功能
 */
import { v4 as uuidv4 } from 'uuid';
import { EnhancedContainer } from './EnhancedContainer';
import { Logger } from '../logging/Logger';

type BindingType = 'value' | 'factory' | 'interface' | 'constant';

interface Binding<T = any> {
  type: BindingType;
  value?: T;
  factory?: (container: EnhancedContainer) => T;
  implementation?: string | symbol;
  options?: any;
  instance?: T;
}

export class EnhancedContainerImpl implements EnhancedContainer {
  private id: string;
  private name: string;
  private parent: EnhancedContainerImpl | null = null;
  private children: EnhancedContainerImpl[] = [];
  private bindings: Map<string | symbol, Binding> = new Map();
  private logger?: Logger;

  /**
   * 构造函数
   * @param name 容器名称
   * @param logger 日志记录器（可选）
   */
  constructor(name: string = 'root', logger?: Logger) {
    this.id = uuidv4();
    this.name = name;
    this.logger = logger;
    this.log('info', `创建容器: ${name} (${this.id})`);
  }

  /**
   * 绑定值
   * @param token 标识符
   * @param valueOrFactory 值或工厂函数
   * @param options 绑定选项
   */
  bind<T>(token: string | symbol, valueOrFactory: T | ((container: EnhancedContainer) => T), options: any = {}): void {
    if (typeof valueOrFactory === 'function' && !this.isClass(valueOrFactory)) {
      this.bindFactory(token, valueOrFactory as (container: EnhancedContainer) => T, options);
    } else {
      const binding: Binding<T> = {
        type: 'value',
        value: valueOrFactory as T,
        options
      };
      this.bindings.set(token, binding);
      this.log('debug', `绑定值: ${String(token)}`);
    }
  }

  /**
   * 绑定接口到实现
   * @param token 接口标识符
   * @param implementation 实现标识符
   * @param options 绑定选项
   */
  bindInterface<T>(token: string | symbol, implementation: string | symbol, options: any = {}): void {
    const binding: Binding<T> = {
      type: 'interface',
      implementation,
      options
    };
    this.bindings.set(token, binding);
    this.log('debug', `绑定接口: ${String(token)} -> ${String(implementation)}`);
  }

  /**
   * 绑定工厂函数
   * @param token 标识符
   * @param factory 工厂函数
   * @param options 绑定选项
   */
  bindFactory<T>(token: string | symbol, factory: (container: EnhancedContainer) => T, options: any = {}): void {
    const binding: Binding<T> = {
      type: 'factory',
      factory,
      options
    };
    this.bindings.set(token, binding);
    this.log('debug', `绑定工厂: ${String(token)}`);
  }

  /**
   * 绑定常量值
   * @param token 标识符
   * @param value 常量值
   */
  bindConstant<T>(token: string | symbol, value: T): void {
    const binding: Binding<T> = {
      type: 'constant',
      value,
      options: { singleton: true }
    };
    this.bindings.set(token, binding);
    this.log('debug', `绑定常量: ${String(token)}`);
  }

  /**
   * 检查是否有绑定
   * @param token 标识符
   * @returns 是否有绑定
   */
  has(token: string | symbol): boolean {
    return this.bindings.has(token) || (this.parent?.has(token) ?? false);
  }

  /**
   * 获取实例
   * @param token 标识符
   * @returns 实例
   */
  get<T>(token: string | symbol): T {
    // 首先检查本地绑定
    if (this.bindings.has(token)) {
      return this.resolveBinding<T>(token);
    }
    
    // 然后检查父容器
    if (this.parent) {
      return this.parent.get<T>(token);
    }
    
    // 如果都没有找到，抛出错误
    throw new Error(`未找到标识符的注册: ${String(token)}`);
  }

  /**
   * 解析类型
   * 自动注入构造函数参数
   * @param constructor 构造函数
   * @returns 实例
   */
  resolve<T>(constructor: new (...args: any[]) => T): T {
    // 获取构造函数的参数类型
    const paramTypes = Reflect.getMetadata('design:paramtypes', constructor) || [];
    
    // 解析每个参数
    const params = paramTypes.map((paramType: any, index: number) => {
      // 获取参数的注入标识符
      const paramToken = Reflect.getMetadata('inject', constructor, `param:${index}`) || paramType;
      
      // 如果有绑定，则使用绑定的值
      if (this.has(paramToken)) {
        return this.get(paramToken);
      }
      
      // 如果参数类型是一个类，则递归解析
      if (typeof paramType === 'function' && this.isClass(paramType)) {
        return this.resolve(paramType);
      }
      
      // 如果无法解析参数，抛出错误
      throw new Error(`无法解析 ${constructor.name} 的参数 ${index}: ${paramType?.name || '未知类型'}`);
    });
    
    // 创建实例
    return new constructor(...params);
  }

  /**
   * 创建子容器
   * @returns 子容器
   */
  createChildContainer(): EnhancedContainer {
    const child = new EnhancedContainerImpl(`${this.name}.child${this.children.length + 1}`, this.logger);
    child.parent = this;
    this.children.push(child);
    this.log('debug', `创建子容器: ${child.name} (${child.id})`);
    return child;
  }

  /**
   * 获取所有绑定的标识符
   * @returns 标识符数组
   */
  getAllBindings(): (string | symbol)[] {
    const localBindings = Array.from(this.bindings.keys());
    if (!this.parent) {
      return localBindings;
    }
    
    // 合并父容器的绑定，但本地绑定优先
    const parentBindings = this.parent.getAllBindings().filter(
      token => !this.bindings.has(token)
    );
    
    return [...localBindings, ...parentBindings];
  }

  /**
   * 获取标识符的绑定类型
   * @param token 标识符
   * @returns 绑定类型
   */
  getBindingType(token: string | symbol): string | null {
    if (this.bindings.has(token)) {
      return this.bindings.get(token)!.type;
    }
    
    if (this.parent) {
      return this.parent.getBindingType(token);
    }
    
    return null;
  }

  /**
   * 获取标识符的绑定选项
   * @param token 标识符
   * @returns 绑定选项
   */
  getBindingOptions(token: string | symbol): any {
    if (this.bindings.has(token)) {
      return this.bindings.get(token)!.options || {};
    }
    
    if (this.parent) {
      return this.parent.getBindingOptions(token);
    }
    
    return {};
  }

  /**
   * 获取标识符的绑定描述
   * @param token 标识符
   * @returns 绑定描述
   */
  getBindingDescription(token: string | symbol): string {
    if (!this.has(token)) {
      return `未绑定: ${String(token)}`;
    }
    
    const type = this.getBindingType(token);
    const options = this.getBindingOptions(token);
    
    switch (type) {
      case 'value':
        return `值绑定: ${String(token)} (${options.singleton ? '单例' : '瞬态'})`;
      case 'factory':
        return `工厂绑定: ${String(token)} (${options.singleton ? '单例' : '瞬态'})`;
      case 'interface':
        const binding = this.bindings.get(token);
        return `接口绑定: ${String(token)} -> ${String(binding?.implementation)} (${options.singleton ? '单例' : '瞬态'})`;
      case 'constant':
        return `常量绑定: ${String(token)}`;
      default:
        return `未知绑定类型: ${String(token)}`;
    }
  }

  /**
   * 获取容器的唯一标识符
   * @returns 容器ID
   */
  getId(): string {
    return this.id;
  }

  /**
   * 获取容器的名称
   * @returns 容器名称
   */
  getName(): string {
    return this.name;
  }

  /**
   * 设置容器的名称
   * @param name 容器名称
   */
  setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取容器的父容器
   * @returns 父容器或null
   */
  getParent(): EnhancedContainer | null {
    return this.parent;
  }

  /**
   * 获取容器的所有子容器
   * @returns 子容器数组
   */
  getChildren(): EnhancedContainer[] {
    return [...this.children];
  }

  /**
   * 销毁容器
   * 清除所有绑定和子容器
   */
  dispose(): void {
    // 销毁所有子容器
    for (const child of this.children) {
      child.dispose();
    }
    
    // 清除所有绑定
    this.bindings.clear();
    this.children = [];
    
    // 从父容器中移除自己
    if (this.parent) {
      const index = this.parent.children.indexOf(this);
      if (index !== -1) {
        this.parent.children.splice(index, 1);
      }
      this.parent = null;
    }
    
    this.log('info', `销毁容器: ${this.name} (${this.id})`);
  }

  /**
   * 解析绑定
   * @param token 标识符
   * @returns 实例
   */
  private resolveBinding<T>(token: string | symbol): T {
    const binding = this.bindings.get(token)!;
    
    // 如果是单例模式且已经有实例，则直接返回
    if (binding.options?.singleton && binding.instance !== undefined) {
      return binding.instance as T;
    }
    
    let instance: T;
    
    switch (binding.type) {
      case 'value':
        instance = binding.value as T;
        break;
      
      case 'factory':
        instance = binding.factory!(this);
        break;
      
      case 'interface':
        instance = this.get<T>(binding.implementation!);
        break;
      
      case 'constant':
        instance = binding.value as T;
        break;
      
      default:
        throw new Error(`未知的绑定类型: ${binding.type}`);
    }
    
    // 如果是单例模式，则缓存实例
    if (binding.options?.singleton) {
      binding.instance = instance;
    }
    
    return instance;
  }

  /**
   * 检查是否是类
   * @param func 函数
   * @returns 是否是类
   */
  private isClass(func: any): boolean {
    return typeof func === 'function' && /^\s*class\s+/.test(func.toString());
  }

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   * @param meta 元数据
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, meta: any = {}): void {
    if (this.logger) {
      this.logger[level](`[Container] ${message}`, {
        containerId: this.id,
        containerName: this.name,
        ...meta
      });
    }
  }
}
