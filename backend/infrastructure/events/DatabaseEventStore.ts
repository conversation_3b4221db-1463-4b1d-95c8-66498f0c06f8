import { EventStore } from './EventStore';
import { DomainEvent } from '../../domain/events/DomainEvent';
import { Sequelize, Model, DataTypes } from 'sequelize';

/**
 * 事件模型
 */
class EventModel extends Model {
  public id!: number;
  public eventId!: string;
  public eventType!: string;
  public aggregateId!: string;
  public aggregateType!: string;
  public occurredOn!: Date;
  public version!: number;
  public payload!: any;
  public metadata!: any;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

/**
 * DatabaseEventStore类
 * 基于数据库的事件存储实现
 */
export class DatabaseEventStore implements EventStore {
  /**
   * 构造函数
   * @param sequelize Sequelize实例
   */
  constructor(private readonly sequelize: Sequelize) {
    this.initializeModel();
  }

  /**
   * 初始化事件模型
   */
  private initializeModel(): void {
    EventModel.init(
      {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true
        },
        eventId: {
          type: DataTypes.STRING(36),
          allowNull: false,
          unique: true
        },
        eventType: {
          type: DataTypes.STRING(100),
          allowNull: false
        },
        aggregateId: {
          type: DataTypes.STRING(36),
          allowNull: false
        },
        aggregateType: {
          type: DataTypes.STRING(100),
          allowNull: false
        },
        occurredOn: {
          type: DataTypes.DATE,
          allowNull: false
        },
        version: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 1
        },
        payload: {
          type: DataTypes.JSON,
          allowNull: true
        },
        metadata: {
          type: DataTypes.JSON,
          allowNull: true
        }
      },
      {
        sequelize: this.sequelize,
        tableName: 'domain_events',
        timestamps: true,
        indexes: [
          {
            name: 'idx_event_type',
            fields: ['eventType']
          },
          {
            name: 'idx_aggregate',
            fields: ['aggregateType', 'aggregateId']
          },
          {
            name: 'idx_occurred_on',
            fields: ['occurredOn']
          }
        ]
      }
    );
  }

  /**
   * 存储事件
   * @param event 要存储的事件
   */
  async store(event: DomainEvent): Promise<void> {
    await EventModel.create({
      eventId: event.eventId,
      eventType: event.eventType,
      aggregateId: event.aggregateId.toString(),
      aggregateType: event.aggregateType,
      occurredOn: event.occurredOn,
      version: event.version || 1,
      payload: event.payload || {},
      metadata: {
        storedAt: new Date()
      }
    });
  }

  /**
   * 获取特定聚合的事件
   * @param aggregateId 聚合ID
   * @param aggregateType 聚合类型
   * @returns 事件列表
   */
  async getEvents(aggregateId: string | number, aggregateType: string): Promise<DomainEvent[]> {
    const events = await EventModel.findAll({
      where: {
        aggregateId: aggregateId.toString(),
        aggregateType
      },
      order: [['occurredOn', 'ASC']]
    });

    return events.map(this.mapToDomainEvent);
  }

  /**
   * 获取所有事件
   * @param limit 限制返回的事件数量
   * @param offset 偏移量
   * @returns 事件列表
   */
  async getAllEvents(limit: number = 100, offset: number = 0): Promise<DomainEvent[]> {
    const events = await EventModel.findAll({
      limit,
      offset,
      order: [['occurredOn', 'DESC']]
    });

    return events.map(this.mapToDomainEvent);
  }

  /**
   * 获取特定类型的事件
   * @param eventType 事件类型
   * @param limit 限制返回的事件数量
   * @param offset 偏移量
   * @returns 事件列表
   */
  async getEventsByType(eventType: string, limit: number = 100, offset: number = 0): Promise<DomainEvent[]> {
    const events = await EventModel.findAll({
      where: {
        eventType
      },
      limit,
      offset,
      order: [['occurredOn', 'DESC']]
    });

    return events.map(this.mapToDomainEvent);
  }

  /**
   * 获取特定时间范围内的事件
   * @param startDate 开始时间
   * @param endDate 结束时间
   * @param limit 限制返回的事件数量
   * @param offset 偏移量
   * @returns 事件列表
   */
  async getEventsByTimeRange(
    startDate: Date,
    endDate: Date,
    limit: number = 100,
    offset: number = 0
  ): Promise<DomainEvent[]> {
    const events = await EventModel.findAll({
      where: {
        occurredOn: {
          [this.sequelize.Op.between]: [startDate, endDate]
        }
      },
      limit,
      offset,
      order: [['occurredOn', 'DESC']]
    });

    return events.map(this.mapToDomainEvent);
  }

  /**
   * 将事件模型映射为领域事件
   * @param eventModel 事件模型
   * @returns 领域事件
   */
  private mapToDomainEvent(eventModel: EventModel): DomainEvent {
    return {
      eventId: eventModel.eventId,
      eventType: eventModel.eventType,
      aggregateId: eventModel.aggregateId,
      aggregateType: eventModel.aggregateType,
      occurredOn: eventModel.occurredOn,
      version: eventModel.version,
      payload: eventModel.payload
    };
  }
}
