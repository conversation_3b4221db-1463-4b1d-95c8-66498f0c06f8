import { DomainEvent } from '../../domain/events/DomainEvent';
import { EventBus } from './EventBus';
import { EventHandler } from './EventHandler';
import logger from '../../config/logger';
import { DeadLetterQueueService as DeadLetterQueueServiceInterface } from './DeadLetterQueueServiceInterface';

/**
 * 重试配置
 */
interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  factor: number;
}

/**
 * 死信队列服务
 * 负责管理处理失败的事件
 */
export class DeadLetterQueueService implements DeadLetterQueueServiceInterface {
  /**
   * 默认重试配置
   */
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    initialDelay: 1000, // 毫秒
    maxDelay: 60000, // 毫秒
    factor: 2 // 指数退避因子
  };

  /**
   * 构造函数
   * @param eventBus 事件总线
   * @param deadLetterQueueModel 死信队列模型
   * @param retryConfig 重试配置（可选）
   */
  constructor(
    private readonly eventBus: EventBus,
    private readonly deadLetterQueueModel: any,
    private readonly retryConfig: RetryConfig = {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 60000,
      factor: 2
    }
  ) {}

  /**
   * 添加失败事件到死信队列
   * @param event 事件
   * @param handlerName 处理器名称
   * @param error 错误
   */
  async addToDeadLetterQueue(
    event: DomainEvent,
    handlerName: string,
    error: Error
  ): Promise<void> {
    try {
      // 计算下次重试时间
      const nextRetryAt = this.calculateNextRetryTime(0);

      // 创建死信队列记录
      await this.deadLetterQueueModel.create({
        eventId: event.eventId,
        eventType: event.eventType,
        handlerName,
        aggregateId: event.aggregateId.toString(),
        aggregateType: event.aggregateType,
        payload: event,
        errorMessage: error.message,
        errorStack: error.stack,
        retryCount: 0,
        maxRetries: this.retryConfig.maxRetries,
        nextRetryAt,
        status: 'pending'
      });

      logger.info(`事件 ${event.eventType} (${event.eventId}) 已添加到死信队列，处理器: ${handlerName}`);
    } catch (error) {
      logger.error(`添加事件到死信队列失败: ${error.message}`, {
        eventId: event.eventId,
        eventType: event.eventType,
        handlerName,
        error: error.stack
      });
    }
  }

  /**
   * 处理死信队列中的事件
   * @param limit 处理的最大数量
   */
  async processDeadLetterQueue(limit: number = 10): Promise<void> {
    try {
      // 查找需要重试的事件
      const deadLetterItems = await this.deadLetterQueueModel.findAll({
        where: {
          status: 'pending',
          nextRetryAt: {
            [this.deadLetterQueueModel.sequelize.Op.lte]: new Date()
          }
        },
        order: [['nextRetryAt', 'ASC']],
        limit
      });

      logger.info(`找到 ${deadLetterItems.length} 个需要重试的死信队列项`);

      for (const item of deadLetterItems) {
        await this.retryEvent(item);
      }
    } catch (error) {
      logger.error(`处理死信队列失败: ${error.message}`, {
        error: error.stack
      });
    }
  }

  /**
   * 重试事件
   * @param deadLetterItem 死信队列项
   */
  async retryEvent(deadLetterItem: any): Promise<void> {
    try {
      // 更新状态为重试中
      await deadLetterItem.update({
        status: 'retrying'
      });

      const event = deadLetterItem.payload;
      const handlerName = deadLetterItem.handlerName;

      logger.info(`重试事件 ${event.eventType} (${event.eventId}), 处理器: ${handlerName}, 重试次数: ${deadLetterItem.retryCount + 1}/${deadLetterItem.maxRetries}`);

      // 获取处理器
      const handler = this.eventBus.getHandler(event.eventType, handlerName);

      if (!handler) {
        throw new Error(`找不到处理器: ${handlerName}`);
      }

      // 处理事件
      await handler.handle(event);

      // 更新为已解决
      await deadLetterItem.update({
        status: 'resolved',
        resolvedAt: new Date()
      });

      logger.info(`事件 ${event.eventType} (${event.eventId}) 重试成功，已从死信队列中移除`);
    } catch (error) {
      // 更新重试次数和下次重试时间
      const retryCount = deadLetterItem.retryCount + 1;
      const nextRetryAt = retryCount >= deadLetterItem.maxRetries
        ? null
        : this.calculateNextRetryTime(retryCount);
      const status = retryCount >= deadLetterItem.maxRetries ? 'failed' : 'pending';

      await deadLetterItem.update({
        retryCount,
        nextRetryAt,
        status,
        errorMessage: error.message,
        errorStack: error.stack
      });

      logger.error(`重试事件 ${deadLetterItem.eventType} (${deadLetterItem.eventId}) 失败: ${error.message}`, {
        eventId: deadLetterItem.eventId,
        eventType: deadLetterItem.eventType,
        handlerName: deadLetterItem.handlerName,
        retryCount,
        maxRetries: deadLetterItem.maxRetries,
        error: error.stack
      });
    }
  }

  /**
   * 计算下次重试时间
   * @param retryCount 当前重试次数
   * @returns 下次重试时间
   */
  private calculateNextRetryTime(retryCount: number): Date {
    const delay = Math.min(
      this.retryConfig.initialDelay * Math.pow(this.retryConfig.factor, retryCount),
      this.retryConfig.maxDelay
    );

    return new Date(Date.now() + delay);
  }
}
