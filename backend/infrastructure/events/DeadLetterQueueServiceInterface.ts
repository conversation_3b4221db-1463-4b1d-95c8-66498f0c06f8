import { DomainEvent } from '../../domain/events/DomainEvent';

/**
 * 死信队列服务接口
 * 用于处理事件处理失败的情况
 */
export interface DeadLetterQueueService {
  /**
   * 添加失败事件到死信队列
   * @param event 事件
   * @param handlerName 处理器名称
   * @param error 错误
   */
  addToDeadLetterQueue(
    event: DomainEvent,
    handlerName: string,
    error: Error
  ): Promise<void>;

  /**
   * 处理死信队列中的事件
   * @param limit 处理的最大数量
   */
  processDeadLetterQueue(limit?: number): Promise<void>;

  /**
   * 重试事件
   * @param deadLetterItem 死信队列项
   */
  retryEvent(deadLetterItem: any): Promise<void>;
}
