/**
 * 增强版事件发布者实现
 * 提供更可靠的事件发布机制，确保事件先持久化再发布
 */
import { EventPublisher } from './EventPublisher';
import { DomainEvent } from '../../domain/events/DomainEvent';
import { EventBus } from './EventBus';
import { EventStore } from './EventStore';
import { Logger } from '../logging/Logger';

export class EnhancedEventPublisherImpl implements EventPublisher {
  private readonly eventBus: EventBus;
  private readonly eventStore?: EventStore;
  private readonly logger: Logger;
  private readonly retryConfig: {
    maxRetries: number;
    initialDelay: number;
    maxDelay: number;
    backoffFactor: number;
  };

  /**
   * 构造函数
   * @param eventBus 事件总线
   * @param logger 日志记录器
   * @param eventStore 事件存储（可选）
   * @param retryConfig 重试配置（可选）
   */
  constructor(
    eventBus: EventBus,
    logger: Logger,
    eventStore?: EventStore,
    retryConfig?: Partial<{
      maxRetries: number;
      initialDelay: number;
      maxDelay: number;
      backoffFactor: number;
    }>
  ) {
    this.eventBus = eventBus;
    this.eventStore = eventStore;
    this.logger = logger;
    this.retryConfig = {
      maxRetries: retryConfig?.maxRetries || 3,
      initialDelay: retryConfig?.initialDelay || 100,
      maxDelay: retryConfig?.maxDelay || 5000,
      backoffFactor: retryConfig?.backoffFactor || 2
    };
  }

  /**
   * 发布单个事件
   * @param event 要发布的事件
   */
  async publish(event: DomainEvent): Promise<void> {
    // 如果有事件存储，先存储事件
    if (this.eventStore) {
      try {
        await this.retryOperation(
          () => this.eventStore!.store(event),
          `存储事件 ${event.eventType} (${(event as any).eventId})`
        );
      } catch (error) {
        this.logger.error(`存储事件失败，无法发布事件: ${error instanceof Error ? error.message : String(error)}`, {
          eventId: (event as any).eventId,
          eventType: event.eventType,
          error: error instanceof Error ? error.stack : String(error)
        });
        // 存储失败时，不继续发布事件，而是抛出异常
        throw new Error(`事件存储失败，无法发布事件: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // 发布事件到事件总线
    try {
      await this.retryOperation(
        () => this.eventBus.publish(event.eventType, event),
        `发布事件 ${event.eventType} (${(event as any).eventId})`
      );
    } catch (error) {
      this.logger.error(`发布事件到事件总线失败: ${error instanceof Error ? error.message : String(error)}`, {
        eventId: (event as any).eventId,
        eventType: event.eventType,
        error: error instanceof Error ? error.stack : String(error)
      });
      throw error; // 重新抛出异常，让调用者知道发布失败
    }
  }

  /**
   * 发布多个事件
   * @param events 要发布的事件列表
   */
  async publishAll(events: DomainEvent[]): Promise<void> {
    for (const event of events) {
      await this.publish(event);
    }
  }

  /**
   * 使用指数退避策略重试操作
   * @param operation 要重试的操作
   * @param operationName 操作名称（用于日志）
   * @returns 操作结果
   */
  private async retryOperation<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    let lastError: Error | null = null;
    let delay = this.retryConfig.initialDelay;
    
    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          this.logger.info(`重试${operationName} (尝试 ${attempt}/${this.retryConfig.maxRetries})`);
        }
        
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        this.logger.warn(`${operationName}失败 (尝试 ${attempt + 1}/${this.retryConfig.maxRetries + 1})`, {
          error: lastError.message,
          stack: lastError.stack
        });
        
        if (attempt < this.retryConfig.maxRetries) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, delay));
          // 使用指数退避策略增加延迟
          delay = Math.min(delay * this.retryConfig.backoffFactor, this.retryConfig.maxDelay);
        }
      }
    }
    
    // 如果达到这里，说明所有重试都失败了
    if (lastError) {
      throw lastError;
    }
    
    // TypeScript 需要这个返回语句，但实际上永远不会执行到这里
    throw new Error('所有重试都失败了');
  }
}
