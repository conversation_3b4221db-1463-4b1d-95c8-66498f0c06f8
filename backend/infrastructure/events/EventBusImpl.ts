import { EventBus } from './EventBus';
import { EventHandler } from './EventHandler';
import { DomainEvent } from '../../domain/events/DomainEvent';
import logger from '../../config/logger';

/**
 * EventBusImpl类
 * 事件总线的实现，使用内存中的Map存储事件处理器
 */
export class EventBusImpl implements EventBus {
  /**
   * 事件处理器映射，键为事件类型，值为事件处理器列表
   */
  private handlers: Map<string, EventHandler[]> = new Map();

  /**
   * 事件处理完成回调函数
   */
  private eventProcessedCallbacks: ((eventId: string, eventType: string, success: boolean, error?: Error) => void)[] = [];

  /**
   * 订阅事件
   * @param eventType 要订阅的事件类型
   * @param handler 事件处理器
   */
  subscribe(eventType: string, handler: EventHandler): void {
    const handlers = this.handlers.get(eventType) || [];
    handlers.push(handler);
    this.handlers.set(eventType, handlers);
  }

  /**
   * 发布事件
   * @param eventType 要发布的事件类型
   * @param event 要发布的事件
   */
  async publish(eventType: string, event: any): Promise<void> {
    const handlers = this.handlers.get(eventType) || [];

    // 如果有通配符处理器，也要包含
    const wildcardHandlers = this.handlers.get('*') || [];
    const allHandlers = [...handlers, ...wildcardHandlers];

    // 获取事件ID
    const eventId = (event as DomainEvent).eventId || `generated-${Date.now()}`;

    for (const handler of allHandlers) {
      try {
        await handler.handle(event);

        // 通知事件处理成功
        this.notifyEventProcessed(eventId, eventType, true);
      } catch (error) {
        logger.error(`处理事件 ${eventType} 时出错:`, {
          eventId,
          eventType,
          error: error.message,
          stack: error.stack
        });

        // 通知事件处理失败
        this.notifyEventProcessed(eventId, eventType, false, error);
      }
    }
  }

  /**
   * 添加事件处理完成回调
   * @param callback 回调函数
   */
  addEventProcessedCallback(callback: (eventId: string, eventType: string, success: boolean, error?: Error) => void): void {
    this.eventProcessedCallbacks.push(callback);
  }

  /**
   * 移除事件处理完成回调
   * @param callback 回调函数
   */
  removeEventProcessedCallback(callback: (eventId: string, eventType: string, success: boolean, error?: Error) => void): void {
    const index = this.eventProcessedCallbacks.indexOf(callback);
    if (index !== -1) {
      this.eventProcessedCallbacks.splice(index, 1);
    }
  }

  /**
   * 通知事件处理完成
   * @param eventId 事件ID
   * @param eventType 事件类型
   * @param success 是否成功
   * @param error 错误信息
   */
  private notifyEventProcessed(eventId: string, eventType: string, success: boolean, error?: Error): void {
    for (const callback of this.eventProcessedCallbacks) {
      try {
        callback(eventId, eventType, success, error);
      } catch (callbackError) {
        logger.error(`执行事件处理完成回调时出错:`, {
          eventId,
          eventType,
          error: callbackError.message,
          stack: callbackError.stack
        });
      }
    }
  }

  /**
   * 获取指定事件类型和处理器名称的处理器
   * @param eventType 事件类型
   * @param handlerName 处理器名称
   * @returns 处理器实例，如果未找到则返回undefined
   */
  getHandler(eventType: string, handlerName: string): EventHandler | undefined {
    const handlers = this.handlers.get(eventType) || [];
    return handlers.find(handler => {
      const constructorName = handler.constructor.name;
      return constructorName === handlerName;
    });
  }
}
