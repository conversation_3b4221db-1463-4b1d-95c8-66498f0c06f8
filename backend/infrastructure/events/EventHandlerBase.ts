import { EventHandler } from './EventHandler';
import { DomainEvent } from '../../domain/events/DomainEvent';
import { WebSocketService } from '../../infrastructure/services/notification/WebSocketService';
import { Logger } from '../../infrastructure/logging/Logger';
import { DeadLetterQueueService } from './DeadLetterQueueServiceInterface';

/**
 * 重试配置
 */
interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  factor: number;
}

/**
 * EventHandlerBase抽象类
 * 事件处理器的基类，实现了EventHandler接口
 * 提供了通用的事件处理逻辑和WebSocket通知功能
 */
export abstract class EventHandlerBase<T extends DomainEvent> implements EventHandler {
  /**
   * 重试配置
   */
  protected retryConfig: RetryConfig = {
    maxRetries: 3,
    initialDelay: 1000, // 毫秒
    maxDelay: 60000, // 毫秒
    factor: 2 // 指数退避因子
  };

  /**
   * 构造函数
   * @param webSocketService WebSocket服务
   * @param logger 日志记录器
   * @param deadLetterQueueService 死信队列服务（可选）
   */
  constructor(
    protected readonly webSocketService: WebSocketService,
    protected readonly logger: Logger,
    protected readonly deadLetterQueueService?: DeadLetterQueueService
  ) {}

  /**
   * 处理事件
   * @param event 要处理的事件
   */
  async handle(event: any): Promise<void> {
    const startTime = Date.now();
    const eventType = event.eventType || 'unknown';
    const eventId = event.eventId || 'unknown';

    this.logger.info(`开始处理事件: ${eventType} (ID: ${eventId})`);

    try {
      // 使用重试机制执行事件处理
      await this.executeWithRetry(async () => {
        // 执行具体的事件处理逻辑
        this.logger.debug(`执行事件处理逻辑: ${eventType}`);
        await this.processEvent(event);

        // 发送WebSocket通知（如果需要）
        this.logger.debug(`发送WebSocket通知: ${eventType}`);
        await this.sendNotification(event);
      }, event);

      const duration = Date.now() - startTime;
      this.logger.info(`事件处理完成: ${eventType} (ID: ${eventId}), 耗时: ${duration}ms`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`处理事件 ${eventType} (ID: ${eventId}) 最终失败, 耗时: ${duration}ms`, {
        error: error.message,
        stack: error.stack,
        eventType,
        eventId,
        payload: event.payload
      });

      // 添加到死信队列
      if (this.deadLetterQueueService) {
        try {
          await this.deadLetterQueueService.addToDeadLetterQueue(
            event,
            this.constructor.name,
            error
          );
          this.logger.info(`事件 ${eventType} (${eventId}) 已添加到死信队列`);
        } catch (dlqError) {
          this.logger.error(`添加事件到死信队列失败: ${dlqError.message}`, {
            eventId,
            eventType,
            error: dlqError.stack
          });
        }
      }

      // 重新抛出错误，让事件总线知道处理失败
      throw error;
    }
  }

  /**
   * 处理事件的具体逻辑，由子类实现
   * @param event 要处理的事件
   */
  protected abstract processEvent(event: T): Promise<void>;

  /**
   * 发送WebSocket通知，由子类根据需要覆盖
   * @param event 要处理的事件
   */
  protected async sendNotification(event: T): Promise<void> {
    // 默认实现为空，子类可以根据需要覆盖
  }

  /**
   * 发送广播通知
   * @param data 通知数据
   */
  protected async sendBroadcastNotification(data: any): Promise<boolean> {
    try {
      this.logger.debug(`发送广播通知: ${data.type}`);
      const result = await this.webSocketService.publish('broadcast', data);
      if (result) {
        this.logger.debug(`广播通知发送成功: ${data.type}`);
      } else {
        this.logger.warn(`广播通知发送失败: ${data.type}`);
      }
      return result;
    } catch (error) {
      this.logger.error(`发送广播通知时出错: ${data.type}`, {
        error: error.message,
        data
      });
      return false;
    }
  }

  /**
   * 发送用户通知
   * @param userId 用户ID
   * @param data 通知数据
   */
  protected async sendUserNotification(userId: string, data: any): Promise<boolean> {
    try {
      this.logger.debug(`发送用户通知: ${data.type}, 用户ID: ${userId}`);
      const result = await this.webSocketService.publish(`user:${userId}`, data);
      if (result) {
        this.logger.debug(`用户通知发送成功: ${data.type}, 用户ID: ${userId}`);
      } else {
        this.logger.warn(`用户通知发送失败: ${data.type}, 用户ID: ${userId}`);
      }
      return result;
    } catch (error) {
      this.logger.error(`发送用户通知时出错: ${data.type}, 用户ID: ${userId}`, {
        error: error.message,
        userId,
        data
      });
      return false;
    }
  }

  /**
   * 使用重试机制执行操作
   * @param operation 要执行的操作
   * @param event 事件对象（用于日志）
   */
  private async executeWithRetry(operation: () => Promise<void>, event: T): Promise<void> {
    const eventType = event.eventType || 'unknown';
    const eventId = event.eventId || 'unknown';
    let lastError: Error | null = null;
    let delay = this.retryConfig.initialDelay;

    for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        // 执行操作
        await operation();

        // 如果成功，直接返回
        return;
      } catch (error) {
        lastError = error;

        // 如果这是最后一次尝试，不再重试
        if (attempt >= this.retryConfig.maxRetries) {
          this.logger.error(`处理事件 ${eventType} (${eventId}) 失败，已达到最大重试次数 (${this.retryConfig.maxRetries})`, {
            attempt,
            maxRetries: this.retryConfig.maxRetries,
            error: error.message,
            stack: error.stack
          });
          break;
        }

        // 计算下一次重试的延迟时间
        delay = Math.min(
          delay * this.retryConfig.factor,
          this.retryConfig.maxDelay
        );

        this.logger.warn(`处理事件 ${eventType} (${eventId}) 失败，将在 ${delay}ms 后重试 (${attempt}/${this.retryConfig.maxRetries})`, {
          attempt,
          maxRetries: this.retryConfig.maxRetries,
          delay,
          error: error.message
        });

        // 等待指定的延迟时间
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // 如果所有重试都失败，抛出最后一个错误
    if (lastError) {
      throw lastError;
    }
  }
}
