import { EventBus } from './EventBus';
import { EventHandler } from './EventHandler';
import { Container } from '../config/Container';

/**
 * EventHandlerRegistry类
 * 事件处理器注册表，负责注册所有事件处理器
 */
export class EventHandlerRegistry {
  /**
   * 构造函数
   * @param eventBus 事件总线
   * @param container 依赖注入容器
   */
  /**
   * 日志记录器
   */
  private logger: any;

  constructor(
    private readonly eventBus: EventBus,
    private readonly container: Container
  ) {
    // 导入日志记录器
    this.logger = require('../../config/logger');
  }

  /**
   * 注册所有事件处理器
   */
  registerHandlers(): void {
    this.logger.info('开始注册事件处理器...');

    // 注册内容相关事件处理器
    this.logger.debug('注册内容相关事件处理器');
    this.registerHandler('ExerciseCreated', 'exerciseCreatedEventHandler');
    this.registerHandler('ExerciseCompleted', 'exerciseCompletedEventHandler');
    this.registerHandler('NoteCreated', 'noteCreatedEventHandler');
    this.registerHandler('NotePublished', 'notePublishedEventHandler');

    // 注册用户相关事件处理器
    this.logger.debug('注册用户相关事件处理器');
    this.registerHandler('UserLeveledUp', 'userLeveledUpEventHandler');
    this.registerHandler('AchievementUnlocked', 'achievementUnlockedEventHandler');
    this.registerHandler('BadgeAwarded', 'badgeAwardedEventHandler');

    // 注册用户权限相关事件处理器
    this.logger.debug('注册用户权限相关事件处理器');
    this.registerHandler('UserRoleAdded', 'userRoleAddedEventHandler');
    this.registerHandler('UserRoleRemoved', 'userRoleRemovedEventHandler');
    this.registerHandler('RolePermissionAdded', 'rolePermissionAddedEventHandler');
    this.registerHandler('RolePermissionRemoved', 'rolePermissionRemovedEventHandler');
    this.registerHandler('UserDisabled', 'userStatusChangedEventHandler');
    this.registerHandler('UserEnabled', 'userStatusChangedEventHandler');

    // 注册学习相关事件处理器
    this.logger.debug('注册学习相关事件处理器');
    this.registerHandler('LearningPlanCreated', 'learningPlanCreatedEventHandler');
    this.registerHandler('LearningPlanCompleted', 'learningPlanCompletedEventHandler');
    this.registerHandler('ThemeCreated', 'themeCreatedEventHandler');

    // 打印所有已注册的事件处理器
    this.logRegisteredHandlers();

    this.logger.info('事件处理器注册完成');
  }

  /**
   * 注册单个事件处理器
   * @param eventType 事件类型
   * @param handlerName 处理器名称
   */
  private registerHandler(eventType: string, handlerName: string): void {
    try {
      // 从容器中获取处理器实例
      if (this.container.has(handlerName)) {
        const handler = this.container.get<EventHandler>(handlerName);

        // 注册到事件总线
        this.eventBus.subscribe(eventType, handler);
        this.logger.info(`已注册事件处理器: ${eventType} -> ${handlerName}`);
      } else {
        this.logger.warn(`未找到事件处理器: ${handlerName}`, {
          eventType,
          handlerName,
          availableHandlers: this.getAvailableHandlers()
        });
      }
    } catch (error) {
      this.logger.error(`注册事件处理器 ${handlerName} 失败:`, {
        error: error.message,
        stack: error.stack,
        eventType,
        handlerName
      });
    }
  }

  /**
   * 获取容器中可用的处理器列表
   * @returns 处理器名称列表
   */
  private getAvailableHandlers(): string[] {
    return Object.keys(this.container)
      .filter(key => key.endsWith('EventHandler'))
      .sort();
  }

  /**
   * 打印所有已注册的事件处理器
   */
  private logRegisteredHandlers(): void {
    const handlers = this.getAvailableHandlers();

    if (handlers.length > 0) {
      this.logger.info(`容器中可用的事件处理器 (${handlers.length}):`);
      handlers.forEach(handler => {
        this.logger.debug(`- ${handler}`);
      });
    } else {
      this.logger.warn('容器中没有可用的事件处理器');
    }
  }
}
