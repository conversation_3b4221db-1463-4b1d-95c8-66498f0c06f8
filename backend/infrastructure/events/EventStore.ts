import { DomainEvent } from '../../domain/events/DomainEvent';

/**
 * EventStore接口
 * 事件存储负责存储和检索领域事件
 */
export interface EventStore {
  /**
   * 存储事件
   * @param event 要存储的事件
   */
  store(event: DomainEvent): Promise<void>;

  /**
   * 获取特定聚合的事件
   * @param aggregateId 聚合ID
   * @param aggregateType 聚合类型
   * @returns 事件列表
   */
  getEvents(aggregateId: string | number, aggregateType: string): Promise<DomainEvent[]>;

  /**
   * 获取所有事件
   * @param limit 限制返回的事件数量
   * @param offset 偏移量
   * @returns 事件列表
   */
  getAllEvents(limit?: number, offset?: number): Promise<DomainEvent[]>;

  /**
   * 获取特定类型的事件
   * @param eventType 事件类型
   * @param limit 限制返回的事件数量
   * @param offset 偏移量
   * @returns 事件列表
   */
  getEventsByType(eventType: string, limit?: number, offset?: number): Promise<DomainEvent[]>;

  /**
   * 获取特定时间范围内的事件
   * @param startDate 开始时间
   * @param endDate 结束时间
   * @param limit 限制返回的事件数量
   * @param offset 偏移量
   * @returns 事件列表
   */
  getEventsByTimeRange(startDate: Date, endDate: Date, limit?: number, offset?: number): Promise<DomainEvent[]>;
}
