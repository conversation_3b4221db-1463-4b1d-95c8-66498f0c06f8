/**
 * 生产级监控服务
 * 提供全面的应用监控、告警和性能分析功能
 */

const EventEmitter = require('events');
const os = require('os');
const fs = require('fs');
const path = require('path');
const logger = require('../../config/logger');

class ProductionMonitoringService extends EventEmitter {
  constructor(options = {}) {
    super();

    this.config = {
      // 监控间隔 (毫秒)
      interval: options.interval || 30000,

      // 告警阈值
      thresholds: {
        cpu: options.thresholds?.cpu || 80,
        memory: options.thresholds?.memory || 85,
        responseTime: options.thresholds?.responseTime || 1000,
        errorRate: options.thresholds?.errorRate || 0.05,
        diskSpace: options.thresholds?.diskSpace || 90,
        connectionCount: options.thresholds?.connectionCount || 1000,
      },

      // 告警配置
      alerting: {
        enabled: options.alerting?.enabled !== false,
        cooldown: options.alerting?.cooldown || 300000, // 5分钟冷却期
        channels: options.alerting?.channels || ['log', 'webhook'],
      },

      // 数据保留配置
      retention: {
        metrics: options.retention?.metrics || 7 * 24 * 60 * 60 * 1000, // 7天
        logs: options.retention?.logs || 30 * 24 * 60 * 60 * 1000, // 30天
      },
    };

    // 监控数据存储
    this.metrics = {
      system: [],
      application: [],
      business: [],
    };

    // 告警状态跟踪
    this.alertStates = new Map();

    // 监控定时器
    this.monitoringTimer = null;

    // 启动监控
    this.start();
  }

  /**
   * 启动监控服务
   */
  start() {
    if (this.monitoringTimer) {
      return;
    }

    logger.info('🔍 启动生产级监控服务');

    // 立即执行一次监控
    this.collectMetrics();

    // 设置定时监控
    this.monitoringTimer = setInterval(() => {
      this.collectMetrics();
    }, this.config.interval);

    // 设置数据清理定时器
    setInterval(
      () => {
        this.cleanupOldData();
      },
      60 * 60 * 1000,
    ); // 每小时清理一次

    this.emit('monitoring:started');
  }

  /**
   * 停止监控服务
   */
  stop() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
      logger.info('🛑 停止生产级监控服务');
      this.emit('monitoring:stopped');
    }
  }

  /**
   * 收集所有监控指标
   */
  async collectMetrics() {
    try {
      const timestamp = Date.now();

      // 收集系统指标
      const systemMetrics = await this.collectSystemMetrics();
      this.metrics.system.push({ timestamp, ...systemMetrics });

      // 收集应用指标
      const appMetrics = await this.collectApplicationMetrics();
      this.metrics.application.push({ timestamp, ...appMetrics });

      // 收集业务指标
      const businessMetrics = await this.collectBusinessMetrics();
      this.metrics.business.push({ timestamp, ...businessMetrics });

      // 检查告警条件
      this.checkAlerts({
        timestamp,
        system: systemMetrics,
        application: appMetrics,
        business: businessMetrics,
      });

      // 发送监控事件
      this.emit('metrics:collected', {
        timestamp,
        system: systemMetrics,
        application: appMetrics,
        business: businessMetrics,
      });
    } catch (error) {
      logger.error('监控指标收集失败:', error);
      this.emit('monitoring:error', error);
    }
  }

  /**
   * 收集系统指标
   */
  async collectSystemMetrics() {
    const metrics = {};

    // CPU使用率
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    metrics.cpu = {
      usage: Math.round((1 - totalIdle / totalTick) * 100),
      cores: cpus.length,
      model: cpus[0].model,
    };

    // 内存使用率
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    metrics.memory = {
      total: totalMem,
      used: usedMem,
      free: freeMem,
      usage: Math.round((usedMem / totalMem) * 100),
    };

    // 磁盘空间 (仅在Linux/Unix系统上)
    try {
      if (process.platform !== 'win32') {
        const stats = fs.statSync('/');
        // 这里简化处理，实际生产环境可能需要更复杂的磁盘监控
        metrics.disk = {
          usage: 0, // 占位符，实际需要使用系统命令获取
        };
      }
    } catch (error) {
      // 忽略磁盘监控错误
    }

    // 系统负载
    const loadAvg = os.loadavg();
    metrics.load = {
      '1m': loadAvg[0],
      '5m': loadAvg[1],
      '15m': loadAvg[2],
    };

    // 网络连接数 (需要外部工具支持)
    metrics.network = {
      connections: 0, // 占位符
    };

    return metrics;
  }

  /**
   * 收集应用指标
   */
  async collectApplicationMetrics() {
    const metrics = {};

    // Node.js 进程指标
    const memUsage = process.memoryUsage();
    metrics.process = {
      pid: process.pid,
      uptime: process.uptime(),
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
      },
      cpu: process.cpuUsage(),
    };

    // 事件循环延迟
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const delay = Number(process.hrtime.bigint() - start) / 1000000; // 转换为毫秒
      metrics.eventLoop = { delay };
    });

    // HTTP请求指标 (需要从请求监控中间件获取)
    metrics.http = this.getHttpMetrics();

    // 数据库连接池指标
    metrics.database = await this.getDatabaseMetrics();

    // Redis连接指标
    metrics.redis = await this.getRedisMetrics();

    return metrics;
  }

  /**
   * 收集业务指标
   */
  async collectBusinessMetrics() {
    const metrics = {};

    try {
      // 活跃用户数
      metrics.activeUsers = await this.getActiveUsersCount();

      // API调用统计
      metrics.apiCalls = await this.getApiCallsStats();

      // 错误率统计
      metrics.errorRate = await this.getErrorRateStats();

      // 业务操作统计
      metrics.businessOperations = await this.getBusinessOperationsStats();
    } catch (error) {
      logger.error('业务指标收集失败:', error);
    }

    return metrics;
  }

  /**
   * 获取HTTP请求指标
   */
  getHttpMetrics() {
    // 这里需要与请求监控中间件集成
    // 返回模拟数据
    return {
      requestCount: 0,
      responseTime: {
        avg: 0,
        p50: 0,
        p90: 0,
        p95: 0,
        p99: 0,
      },
      statusCodes: {
        '2xx': 0,
        '3xx': 0,
        '4xx': 0,
        '5xx': 0,
      },
    };
  }

  /**
   * 获取数据库指标
   */
  async getDatabaseMetrics() {
    try {
      const { sequelize } = require('../../models');

      // 获取连接池状态
      const pool = sequelize.connectionManager.pool;

      return {
        connections: {
          total: pool.options.max,
          active: pool.used.length,
          idle: pool.available.length,
          waiting: pool.pending.length,
        },
        queries: {
          total: 0, // 需要从查询监控中获取
          slow: 0, // 慢查询数量
        },
      };
    } catch (error) {
      logger.error('数据库指标获取失败:', error);
      return { error: error.message };
    }
  }

  /**
   * 获取Redis指标
   */
  async getRedisMetrics() {
    try {
      // 这里需要与Redis客户端集成
      return {
        connections: 0,
        memory: 0,
        keyspace: 0,
        operations: {
          gets: 0,
          sets: 0,
          dels: 0,
        },
      };
    } catch (error) {
      logger.error('Redis指标获取失败:', error);
      return { error: error.message };
    }
  }

  /**
   * 获取活跃用户数
   */
  async getActiveUsersCount() {
    try {
      // 这里需要查询数据库获取活跃用户数
      return {
        last5min: 0,
        last1hour: 0,
        last24hours: 0,
      };
    } catch (error) {
      logger.error('活跃用户数获取失败:', error);
      return 0;
    }
  }

  /**
   * 获取API调用统计
   */
  async getApiCallsStats() {
    try {
      // 这里需要从API监控数据中统计
      return {
        total: 0,
        success: 0,
        failed: 0,
        avgResponseTime: 0,
      };
    } catch (error) {
      logger.error('API调用统计获取失败:', error);
      return {};
    }
  }

  /**
   * 获取错误率统计
   */
  async getErrorRateStats() {
    try {
      // 这里需要从错误日志中统计
      return {
        rate: 0,
        count: 0,
        types: {},
      };
    } catch (error) {
      logger.error('错误率统计获取失败:', error);
      return { rate: 0 };
    }
  }

  /**
   * 获取业务操作统计
   */
  async getBusinessOperationsStats() {
    try {
      // 这里需要统计各种业务操作
      return {
        learningPlansCreated: 0,
        exercisesCompleted: 0,
        notesCreated: 0,
        tagsGenerated: 0,
      };
    } catch (error) {
      logger.error('业务操作统计获取失败:', error);
      return {};
    }
  }

  /**
   * 检查告警条件
   */
  checkAlerts(metrics) {
    const alerts = [];

    // 检查CPU使用率
    if (metrics.system.cpu?.usage > this.config.thresholds.cpu) {
      alerts.push({
        type: 'cpu_high',
        level: 'warning',
        message: `CPU使用率过高: ${metrics.system.cpu.usage}%`,
        value: metrics.system.cpu.usage,
        threshold: this.config.thresholds.cpu,
      });
    }

    // 检查内存使用率
    if (metrics.system.memory?.usage > this.config.thresholds.memory) {
      alerts.push({
        type: 'memory_high',
        level: 'warning',
        message: `内存使用率过高: ${metrics.system.memory.usage}%`,
        value: metrics.system.memory.usage,
        threshold: this.config.thresholds.memory,
      });
    }

    // 检查响应时间
    if (metrics.application.http?.responseTime?.avg > this.config.thresholds.responseTime) {
      alerts.push({
        type: 'response_time_high',
        level: 'warning',
        message: `平均响应时间过高: ${metrics.application.http.responseTime.avg}ms`,
        value: metrics.application.http.responseTime.avg,
        threshold: this.config.thresholds.responseTime,
      });
    }

    // 检查错误率
    if (metrics.business.errorRate?.rate > this.config.thresholds.errorRate) {
      alerts.push({
        type: 'error_rate_high',
        level: 'critical',
        message: `错误率过高: ${(metrics.business.errorRate.rate * 100).toFixed(2)}%`,
        value: metrics.business.errorRate.rate,
        threshold: this.config.thresholds.errorRate,
      });
    }

    // 处理告警
    alerts.forEach(alert => {
      this.handleAlert(alert, metrics.timestamp);
    });
  }

  /**
   * 处理告警
   */
  handleAlert(alert, timestamp) {
    const alertKey = `${alert.type}_${alert.level}`;
    const lastAlert = this.alertStates.get(alertKey);

    // 检查冷却期
    if (lastAlert && timestamp - lastAlert.timestamp < this.config.alerting.cooldown) {
      return;
    }

    // 记录告警状态
    this.alertStates.set(alertKey, { timestamp, alert });

    // 发送告警
    if (this.config.alerting.enabled) {
      this.sendAlert(alert, timestamp);
    }

    // 发送告警事件
    this.emit('alert:triggered', { alert, timestamp });
  }

  /**
   * 发送告警
   */
  async sendAlert(alert, timestamp) {
    const alertData = {
      ...alert,
      timestamp,
      hostname: os.hostname(),
      service: 'AIBUBB-Backend',
    };

    // 记录到日志
    if (this.config.alerting.channels.includes('log')) {
      logger.warn('🚨 监控告警:', alertData);
    }

    // 发送到Webhook
    if (this.config.alerting.channels.includes('webhook')) {
      await this.sendWebhookAlert(alertData);
    }

    // 发送邮件 (如果配置了)
    if (this.config.alerting.channels.includes('email')) {
      await this.sendEmailAlert(alertData);
    }

    // 发送短信 (如果配置了)
    if (this.config.alerting.channels.includes('sms')) {
      await this.sendSmsAlert(alertData);
    }
  }

  /**
   * 发送Webhook告警
   */
  async sendWebhookAlert(alertData) {
    try {
      const webhookUrl = process.env.ALERT_WEBHOOK_URL;
      if (!webhookUrl) return;

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(alertData),
      });

      if (!response.ok) {
        throw new Error(`Webhook请求失败: ${response.status}`);
      }
    } catch (error) {
      logger.error('Webhook告警发送失败:', error);
    }
  }

  /**
   * 发送邮件告警
   */
  async sendEmailAlert(alertData) {
    try {
      // 这里需要集成邮件服务
      logger.info('邮件告警发送 (未实现):', alertData);
    } catch (error) {
      logger.error('邮件告警发送失败:', error);
    }
  }

  /**
   * 发送短信告警
   */
  async sendSmsAlert(alertData) {
    try {
      // 这里需要集成短信服务
      logger.info('短信告警发送 (未实现):', alertData);
    } catch (error) {
      logger.error('短信告警发送失败:', error);
    }
  }

  /**
   * 获取监控数据
   */
  getMetrics(type = 'all', timeRange = 3600000) {
    // 默认1小时
    const now = Date.now();
    const startTime = now - timeRange;

    const result = {};

    if (type === 'all' || type === 'system') {
      result.system = this.metrics.system.filter(m => m.timestamp >= startTime);
    }

    if (type === 'all' || type === 'application') {
      result.application = this.metrics.application.filter(m => m.timestamp >= startTime);
    }

    if (type === 'all' || type === 'business') {
      result.business = this.metrics.business.filter(m => m.timestamp >= startTime);
    }

    return result;
  }

  /**
   * 获取告警历史
   */
  getAlertHistory(timeRange = 86400000) {
    // 默认24小时
    const now = Date.now();
    const startTime = now - timeRange;

    const alerts = [];

    for (const [key, alertState] of this.alertStates.entries()) {
      if (alertState.timestamp >= startTime) {
        alerts.push({
          key,
          ...alertState.alert,
          timestamp: alertState.timestamp,
        });
      }
    }

    return alerts.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 清理旧数据
   */
  cleanupOldData() {
    const now = Date.now();

    // 清理指标数据
    ['system', 'application', 'business'].forEach(type => {
      const cutoff = now - this.config.retention.metrics;
      this.metrics[type] = this.metrics[type].filter(m => m.timestamp >= cutoff);
    });

    // 清理告警状态
    for (const [key, alertState] of this.alertStates.entries()) {
      if (now - alertState.timestamp > this.config.retention.logs) {
        this.alertStates.delete(key);
      }
    }

    logger.debug('监控数据清理完成');
  }

  /**
   * 生成监控报告
   */
  generateReport(timeRange = 86400000) {
    // 默认24小时
    const metrics = this.getMetrics('all', timeRange);
    const alerts = this.getAlertHistory(timeRange);

    const report = {
      timeRange: {
        start: Date.now() - timeRange,
        end: Date.now(),
        duration: timeRange,
      },
      summary: {
        totalAlerts: alerts.length,
        criticalAlerts: alerts.filter(a => a.level === 'critical').length,
        warningAlerts: alerts.filter(a => a.level === 'warning').length,
      },
      metrics: {
        system: this.summarizeMetrics(metrics.system),
        application: this.summarizeMetrics(metrics.application),
        business: this.summarizeMetrics(metrics.business),
      },
      alerts: alerts.slice(0, 10), // 最近10个告警
      recommendations: this.generateRecommendations(metrics, alerts),
    };

    return report;
  }

  /**
   * 汇总指标数据
   */
  summarizeMetrics(metrics) {
    if (!metrics || metrics.length === 0) {
      return {};
    }

    // 这里可以计算平均值、最大值、最小值等统计信息
    return {
      count: metrics.length,
      latest: metrics[metrics.length - 1],
      // 可以添加更多统计信息
    };
  }

  /**
   * 生成改进建议
   */
  generateRecommendations(metrics, alerts) {
    const recommendations = [];

    // 基于告警生成建议
    if (alerts.some(a => a.type === 'cpu_high')) {
      recommendations.push('考虑优化CPU密集型操作或增加服务器资源');
    }

    if (alerts.some(a => a.type === 'memory_high')) {
      recommendations.push('检查内存泄漏或考虑增加内存容量');
    }

    if (alerts.some(a => a.type === 'response_time_high')) {
      recommendations.push('优化数据库查询或实施缓存策略');
    }

    if (alerts.some(a => a.type === 'error_rate_high')) {
      recommendations.push('检查错误日志并修复导致错误的问题');
    }

    if (recommendations.length === 0) {
      recommendations.push('系统运行良好，继续保持监控');
    }

    return recommendations;
  }
}

module.exports = ProductionMonitoringService;
