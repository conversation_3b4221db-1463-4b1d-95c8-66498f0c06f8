/**
 * 增强版Sequelize工作单元实现
 * 提供更强大的工作单元功能，支持聚合根跟踪和批量提交
 */
import { v4 as uuidv4 } from 'uuid';
import { Sequelize, Transaction } from 'sequelize';
import { EnhancedUnitOfWork } from './EnhancedUnitOfWork';
import { AggregateRoot } from '../../domain/models/common/AggregateRoot';
import { Repository } from '../../domain/repositories/common/Repository';
import { Logger } from '../logging/Logger';
import { EventPublisher } from '../events/EventPublisher';

export class EnhancedSequelizeUnitOfWork implements EnhancedUnitOfWork {
  private id: string;
  private transaction: Transaction | null = null;
  private newAggregates: Set<AggregateRoot> = new Set();
  private dirtyAggregates: Set<AggregateRoot> = new Set();
  private removedAggregates: Set<AggregateRoot> = new Set();
  private repositories: Set<Repository<any>> = new Set();
  private isActive: boolean = false;

  /**
   * 构造函数
   * @param sequelize Sequelize实例
   * @param logger 日志记录器
   * @param eventPublisher 事件发布者
   */
  constructor(
    private readonly sequelize: Sequelize,
    private readonly logger: Logger,
    private readonly eventPublisher: EventPublisher
  ) {
    this.id = uuidv4();
  }

  /**
   * 开始事务
   */
  async begin(): Promise<void> {
    if (this.transaction) {
      throw new Error('事务已经开始');
    }
    this.transaction = await this.sequelize.transaction();
    this.isActive = true;
    this.logger.debug(`工作单元 ${this.id} 开始事务`);
  }

  /**
   * 提交事务
   */
  async commit(): Promise<void> {
    if (!this.transaction) {
      throw new Error('没有事务可提交');
    }
    await this.transaction.commit();
    this.transaction = null;
    this.isActive = false;
    this.logger.debug(`工作单元 ${this.id} 提交事务`);
  }

  /**
   * 回滚事务
   */
  async rollback(): Promise<void> {
    if (!this.transaction) {
      throw new Error('没有事务可回滚');
    }
    await this.transaction.rollback();
    this.transaction = null;
    this.isActive = false;
    this.logger.debug(`工作单元 ${this.id} 回滚事务`);
  }

  /**
   * 在事务中运行工作
   * @param work 要在事务中运行的工作函数
   * @returns 工作函数的结果
   */
  async runInTransaction<T>(work: () => Promise<T>): Promise<T> {
    const isOuterTransaction = !this.transaction;
    
    if (isOuterTransaction) {
      await this.begin();
    }

    try {
      const result = await work();
      
      if (isOuterTransaction) {
        // 提交所有注册的变更
        await this.commitChanges();
        // 提交事务
        await this.commit();
      }
      
      return result;
    } catch (error) {
      if (isOuterTransaction && this.transaction) {
        await this.rollback();
      }
      throw error;
    } finally {
      if (isOuterTransaction) {
        this.clear();
      }
    }
  }

  /**
   * 获取当前事务
   * @returns 当前事务，如果没有事务，则返回null
   */
  getTransaction(): Transaction | null {
    return this.transaction;
  }

  /**
   * 注册新建的聚合根
   * @param aggregateRoot 聚合根实例
   */
  registerNew<T extends AggregateRoot>(aggregateRoot: T): void {
    if (!this.isActive) {
      throw new Error('工作单元未激活，请先调用begin()');
    }
    
    if (this.newAggregates.has(aggregateRoot) || 
        this.dirtyAggregates.has(aggregateRoot) || 
        this.removedAggregates.has(aggregateRoot)) {
      throw new Error('聚合根已经被注册');
    }
    
    this.newAggregates.add(aggregateRoot);
    this.logger.debug(`工作单元 ${this.id} 注册新建聚合根: ${aggregateRoot.constructor.name}#${aggregateRoot.id}`);
  }

  /**
   * 注册修改的聚合根
   * @param aggregateRoot 聚合根实例
   */
  registerDirty<T extends AggregateRoot>(aggregateRoot: T): void {
    if (!this.isActive) {
      throw new Error('工作单元未激活，请先调用begin()');
    }
    
    if (this.removedAggregates.has(aggregateRoot)) {
      throw new Error('不能修改已删除的聚合根');
    }
    
    if (!this.newAggregates.has(aggregateRoot)) {
      this.dirtyAggregates.add(aggregateRoot);
      this.logger.debug(`工作单元 ${this.id} 注册修改聚合根: ${aggregateRoot.constructor.name}#${aggregateRoot.id}`);
    }
  }

  /**
   * 注册删除的聚合根
   * @param aggregateRoot 聚合根实例
   */
  registerRemoved<T extends AggregateRoot>(aggregateRoot: T): void {
    if (!this.isActive) {
      throw new Error('工作单元未激活，请先调用begin()');
    }
    
    if (this.newAggregates.has(aggregateRoot)) {
      this.newAggregates.delete(aggregateRoot);
    } else {
      this.dirtyAggregates.delete(aggregateRoot);
      this.removedAggregates.add(aggregateRoot);
    }
    
    this.logger.debug(`工作单元 ${this.id} 注册删除聚合根: ${aggregateRoot.constructor.name}#${aggregateRoot.id}`);
  }

  /**
   * 注册仓库
   * @param repository 仓库实例
   */
  registerRepository<T extends AggregateRoot>(repository: Repository<T>): void {
    this.repositories.add(repository);
    this.logger.debug(`工作单元 ${this.id} 注册仓库: ${repository.constructor.name}`);
  }

  /**
   * 提交所有注册的变更
   * 这将保存所有新建、修改和删除的聚合根
   */
  async commitChanges(): Promise<void> {
    if (!this.isActive) {
      throw new Error('工作单元未激活，请先调用begin()');
    }
    
    this.logger.debug(`工作单元 ${this.id} 开始提交变更: ${this.newAggregates.size}个新建, ${this.dirtyAggregates.size}个修改, ${this.removedAggregates.size}个删除`);
    
    // 使用注册的仓库保存聚合根
    for (const repository of this.repositories) {
      // 保存新建的聚合根
      for (const aggregate of this.newAggregates) {
        if (repository.supports(aggregate)) {
          await repository.save(aggregate);
        }
      }
      
      // 保存修改的聚合根
      for (const aggregate of this.dirtyAggregates) {
        if (repository.supports(aggregate)) {
          await repository.save(aggregate);
        }
      }
      
      // 删除标记为删除的聚合根
      for (const aggregate of this.removedAggregates) {
        if (repository.supports(aggregate)) {
          await repository.delete(aggregate);
        }
      }
    }
    
    // 发布所有聚合根的领域事件
    const allAggregates = [...this.newAggregates, ...this.dirtyAggregates, ...this.removedAggregates];
    for (const aggregate of allAggregates) {
      if (aggregate.domainEvents.length > 0) {
        await this.eventPublisher.publishAll(aggregate.domainEvents);
        aggregate.clearEvents();
      }
    }
    
    this.logger.debug(`工作单元 ${this.id} 完成提交变更`);
  }

  /**
   * 获取当前工作单元的ID
   */
  getId(): string {
    return this.id;
  }

  /**
   * 获取当前工作单元的状态
   */
  getStatus(): {
    newCount: number;
    dirtyCount: number;
    removedCount: number;
    repositoryCount: number;
  } {
    return {
      newCount: this.newAggregates.size,
      dirtyCount: this.dirtyAggregates.size,
      removedCount: this.removedAggregates.size,
      repositoryCount: this.repositories.size
    };
  }

  /**
   * 清除当前工作单元的所有注册
   */
  clear(): void {
    this.newAggregates.clear();
    this.dirtyAggregates.clear();
    this.removedAggregates.clear();
    this.repositories.clear();
    this.logger.debug(`工作单元 ${this.id} 已清除所有注册`);
  }
}
