import { UnitOfWork } from './UnitOfWork';
import { Sequelize, Transaction } from 'sequelize';

/**
 * SequelizeUnitOfWork类
 * 使用Sequelize实现的工作单元
 * 它使用Sequelize的事务机制来管理事务
 */
export class SequelizeUnitOfWork implements UnitOfWork {
  /**
   * 当前事务
   */
  private transaction: Transaction | null = null;

  /**
   * 构造函数
   * @param sequelize Sequelize实例
   */
  constructor(private readonly sequelize: Sequelize) {}

  /**
   * 开始事务
   */
  async begin(): Promise<void> {
    if (this.transaction) {
      throw new Error('事务已经开始');
    }
    this.transaction = await this.sequelize.transaction();
  }

  /**
   * 提交事务
   */
  async commit(): Promise<void> {
    if (!this.transaction) {
      throw new Error('没有事务可提交');
    }
    await this.transaction.commit();
    this.transaction = null;
  }

  /**
   * 回滚事务
   */
  async rollback(): Promise<void> {
    if (!this.transaction) {
      throw new Error('没有事务可回滚');
    }
    await this.transaction.rollback();
    this.transaction = null;
  }

  /**
   * 在事务中运行工作
   * @param work 要在事务中运行的工作函数
   * @returns 工作函数的结果
   */
  async runInTransaction<T>(work: () => Promise<T>): Promise<T> {
    const isOuterTransaction = !this.transaction;
    
    if (isOuterTransaction) {
      await this.begin();
    }

    try {
      const result = await work();
      
      if (isOuterTransaction) {
        await this.commit();
      }
      
      return result;
    } catch (error) {
      if (isOuterTransaction && this.transaction) {
        await this.rollback();
      }
      throw error;
    }
  }

  /**
   * 获取当前事务
   * @returns 当前事务，如果没有事务，则返回null
   */
  getTransaction(): Transaction | null {
    return this.transaction;
  }
}
