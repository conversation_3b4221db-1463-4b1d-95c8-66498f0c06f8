/**
 * UnitOfWork接口
 * 工作单元负责管理事务和确保聚合的一致性
 * 它跟踪在事务期间对聚合所做的所有更改，并在提交时将它们作为一个单元保存
 */
export interface UnitOfWork {
  /**
   * 开始事务
   */
  begin(): Promise<void>;

  /**
   * 提交事务
   */
  commit(): Promise<void>;

  /**
   * 回滚事务
   */
  rollback(): Promise<void>;

  /**
   * 在事务中运行工作
   * @param work 要在事务中运行的工作函数
   * @returns 工作函数的结果
   */
  runInTransaction<T>(work: () => Promise<T>): Promise<T>;
}
