/**
 * 缓存仓库装饰器
 * 为仓库添加缓存功能
 */
import { Entity } from '../../../domain/Entity';
import { ReadOnlyRepository } from '../repositories/ReadOnlyRepository';
import { MultiLevelCacheStrategy } from './MultiLevelCacheStrategy';
import { Logger } from '../../logging/Logger';

/**
 * 缓存仓库装饰器类
 * @template T 实体类型
 * @template ID 实体ID类型
 */
export class CachedRepositoryDecorator<T extends Entity, ID> implements ReadOnlyRepository<T, ID> {
  /**
   * 构造函数
   * @param repository 被装饰的仓库
   * @param cacheStrategy 缓存策略
   * @param logger 日志记录器
   * @param entityName 实体名称
   * @param ttl 缓存过期时间（秒）
   */
  constructor(
    private readonly repository: ReadOnlyRepository<T, ID>,
    private readonly cacheStrategy: MultiLevelCacheStrategy,
    private readonly logger: Logger,
    private readonly entityName: string,
    private readonly ttl: number = 3600 // 默认1小时
  ) {}

  /**
   * 生成缓存键
   * @param id 实体ID
   * @returns 缓存键
   * @private
   */
  private generateKey(id: ID): string {
    return `${this.entityName}:${id}`;
  }

  /**
   * 生成列表缓存键
   * @param suffix 后缀
   * @returns 缓存键
   * @private
   */
  private generateListKey(suffix: string = 'all'): string {
    return `${this.entityName}:list:${suffix}`;
  }

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 如果找到实体，则返回实体，否则返回null
   */
  async findById(id: ID): Promise<T | null> {
    const cacheKey = this.generateKey(id);

    // 尝试从缓存获取
    const cachedEntity = await this.cacheStrategy.get(cacheKey);
    if (cachedEntity) {
      this.logger.debug(`从缓存获取实体: ${cacheKey}`);
      return cachedEntity;
    }

    // 从仓库获取
    const entity = await this.repository.findById(id);

    // 如果找到实体，则缓存
    if (entity) {
      await this.cacheStrategy.set(cacheKey, entity, { ttl: this.ttl });
      this.logger.debug(`缓存实体: ${cacheKey}`);
    }

    return entity;
  }

  /**
   * 查找所有实体
   * @returns 实体列表
   */
  async findAll(): Promise<T[]> {
    const cacheKey = this.generateListKey();

    // 尝试从缓存获取
    const cachedEntities = await this.cacheStrategy.get(cacheKey);
    if (cachedEntities) {
      this.logger.debug(`从缓存获取实体列表: ${cacheKey}`);
      return cachedEntities;
    }

    // 从仓库获取
    const entities = await this.repository.findAll();

    // 缓存实体列表
    await this.cacheStrategy.set(cacheKey, entities, { ttl: this.ttl });
    this.logger.debug(`缓存实体列表: ${cacheKey}`);

    return entities;
  }

  /**
   * 根据条件查找实体
   * @param criteria 查询条件
   * @returns 实体列表
   */
  async findByCriteria(criteria: any): Promise<T[]> {
    // 生成缓存键
    const criteriaStr = JSON.stringify(criteria);
    const cacheKey = this.generateListKey(`criteria:${this._hashString(criteriaStr)}`);

    // 尝试从缓存获取
    const cachedEntities = await this.cacheStrategy.get(cacheKey);
    if (cachedEntities) {
      this.logger.debug(`从缓存获取实体列表: ${cacheKey}`);
      return cachedEntities;
    }

    // 从仓库获取
    const entities = await this.repository.findByCriteria(criteria);

    // 缓存实体列表
    await this.cacheStrategy.set(cacheKey, entities, { ttl: this.ttl });
    this.logger.debug(`缓存实体列表: ${cacheKey}`);

    return entities;
  }

  /**
   * 检查实体是否存在
   * @param id 实体ID
   * @returns 如果实体存在，则返回true，否则返回false
   */
  async exists(id: ID): Promise<boolean> {
    const cacheKey = this.generateKey(id);

    // 尝试从缓存获取
    const cachedEntity = await this.cacheStrategy.get(cacheKey);
    if (cachedEntity) {
      return true;
    }

    // 从仓库检查
    return await this.repository.exists(id);
  }

  /**
   * 计算实体数量
   * @param criteria 查询条件
   * @returns 实体数量
   */
  async count(criteria?: any): Promise<number> {
    // 生成缓存键
    const criteriaStr = criteria ? JSON.stringify(criteria) : 'all';
    const cacheKey = `${this.entityName}:count:${this._hashString(criteriaStr)}`;

    // 尝试从缓存获取
    const cachedCount = await this.cacheStrategy.get(cacheKey);
    if (cachedCount !== null) {
      this.logger.debug(`从缓存获取实体数量: ${cacheKey}`);
      return cachedCount;
    }

    // 从仓库获取
    const count = await this.repository.count(criteria);

    // 缓存实体数量
    await this.cacheStrategy.set(cacheKey, count, { ttl: this.ttl });
    this.logger.debug(`缓存实体数量: ${cacheKey}`);

    return count;
  }

  /**
   * 清除实体缓存
   * @param id 实体ID
   * @returns 是否成功
   */
  async clearEntityCache(id: ID): Promise<boolean> {
    const cacheKey = this.generateKey(id);
    return await this.cacheStrategy.del(cacheKey);
  }

  /**
   * 清除实体列表缓存
   * @returns 是否成功
   */
  async clearListCache(): Promise<boolean> {
    return await this.cacheStrategy.delByPattern(`${this.entityName}:list:*`);
  }

  /**
   * 清除实体数量缓存
   * @returns 是否成功
   */
  async clearCountCache(): Promise<boolean> {
    return await this.cacheStrategy.delByPattern(`${this.entityName}:count:*`);
  }

  /**
   * 清除所有缓存
   * @returns 是否成功
   */
  async clearAllCache(): Promise<boolean> {
    return await this.cacheStrategy.delByPattern(`${this.entityName}:*`);
  }

  /**
   * 预热实体缓存
   * @param ids 实体ID列表
   * @returns 预热成功的实体数量
   */
  async warmupEntityCache(ids: ID[]): Promise<number> {
    const keys = ids.map(id => this.generateKey(id));

    return await this.cacheStrategy.warmup(keys, async (key) => {
      const idStr = key.split(':')[1];
      const id = (typeof ids[0] === 'number') ? parseInt(idStr) : idStr;
      return await this.repository.findById(id as ID);
    });
  }

  /**
   * 对字符串进行哈希处理
   * @param str 字符串
   * @returns 哈希值
   * @private
   */
  private _hashString(str: string): string {
    let hash = 0;

    if (str.length === 0) {
      return hash.toString();
    }

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(16);
  }
}