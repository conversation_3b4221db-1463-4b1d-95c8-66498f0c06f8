/**
 * 多级缓存策略
 * 实现多级缓存和缓存一致性
 */
import { Logger } from '../../logging/Logger';
import { EnhancedCacheService } from '../../services/cache/EnhancedCacheService';

/**
 * 缓存级别枚举
 */
export enum CacheLevel {
  MEMORY = 'memory',
  REDIS = 'redis',
  ALL = 'all'
}

/**
 * 缓存策略选项接口
 */
export interface CacheStrategyOptions {
  ttl?: number;
  localTtl?: number;
  level?: CacheLevel;
  prefix?: string;
  useCompression?: boolean;
  compressionThreshold?: number;
  updateStats?: boolean;
}

/**
 * 多级缓存策略类
 */
export class MultiLevelCacheStrategy {
  private prefix: string;
  private defaultTtl: number;
  private defaultLocalTtl: number;
  private useCompression: boolean;
  private compressionThreshold: number;

  /**
   * 构造函数
   * @param cacheService 缓存服务
   * @param logger 日志记录器
   * @param options 缓存策略选项
   */
  constructor(
    private readonly cacheService: EnhancedCacheService,
    private readonly logger: Logger,
    options: CacheStrategyOptions = {}
  ) {
    this.prefix = options.prefix || '';
    this.defaultTtl = options.ttl || 3600; // 默认1小时
    this.defaultLocalTtl = options.localTtl || Math.floor(this.defaultTtl / 2); // 默认本地缓存时间为Redis缓存时间的一半
    this.useCompression = options.useCompression || false;
    this.compressionThreshold = options.compressionThreshold || 1024; // 默认1KB以上启用压缩
  }

  /**
   * 生成缓存键
   * @param key 原始键
   * @returns 带前缀的缓存键
   * @private
   */
  private generateKey(key: string): string {
    return this.prefix ? `${this.prefix}:${key}` : key;
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param options 缓存选项
   * @returns 是否成功
   */
  async set(key: string, data: any, options: CacheStrategyOptions = {}): Promise<boolean> {
    const cacheKey = this.generateKey(key);
    const ttl = options.ttl || this.defaultTtl;
    const localTtl = options.localTtl || this.defaultLocalTtl;
    const level = options.level || CacheLevel.ALL;

    // 处理数据压缩
    let cacheData = data;
    if (this.useCompression && typeof data === 'string' && data.length > this.compressionThreshold) {
      try {
        // 这里可以实现数据压缩逻辑
        // 例如使用zlib进行压缩
        this.logger.debug(`压缩缓存数据: ${cacheKey}`);
      } catch (error) {
        this.logger.error(`压缩缓存数据失败: ${error.message}`);
      }
    }

    // 根据缓存级别设置缓存选项
    const cacheOptions: any = {
      ttl,
      localTtl,
      updateStats: options.updateStats
    };

    switch (level) {
      case CacheLevel.MEMORY:
        cacheOptions.onlyMemory = true;
        break;
      case CacheLevel.REDIS:
        cacheOptions.onlyRedis = true;
        break;
      case CacheLevel.ALL:
      default:
        // 使用默认选项
        break;
    }

    try {
      const result = await this.cacheService.set(cacheKey, cacheData, cacheOptions);

      if (result) {
        this.logger.debug(`缓存设置成功: ${cacheKey}, TTL: ${ttl}秒, 级别: ${level}`);
      } else {
        this.logger.warn(`缓存设置失败: ${cacheKey}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`设置缓存失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @param options 缓存选项
   * @returns 缓存数据或null
   */
  async get(key: string, options: CacheStrategyOptions = {}): Promise<any> {
    const cacheKey = this.generateKey(key);
    const level = options.level || CacheLevel.ALL;

    // 根据缓存级别设置缓存选项
    const cacheOptions: any = {
      updateStats: options.updateStats
    };

    switch (level) {
      case CacheLevel.MEMORY:
        cacheOptions.skipRedis = true;
        break;
      case CacheLevel.REDIS:
        cacheOptions.skipMemory = true;
        break;
      case CacheLevel.ALL:
      default:
        // 使用默认选项
        break;
    }

    try {
      const data = await this.cacheService.get(cacheKey, cacheOptions);

      if (data !== null) {
        this.logger.debug(`缓存命中: ${cacheKey}, 级别: ${level}`);

        // 处理数据解压缩
        if (this.useCompression && typeof data === 'object' && data.__compressed) {
          try {
            // 这里可以实现数据解压缩逻辑
            this.logger.debug(`解压缩缓存数据: ${cacheKey}`);
            return data.data;
          } catch (error) {
            this.logger.error(`解压缩缓存数据失败: ${error.message}`);
            return null;
          }
        }

        return data;
      } else {
        this.logger.debug(`缓存未命中: ${cacheKey}`);
        return null;
      }
    } catch (error) {
      this.logger.error(`获取缓存失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 删除缓存
   * @param key 缓存键
   * @returns 是否成功
   */
  async del(key: string): Promise<boolean> {
    const cacheKey = this.generateKey(key);

    try {
      const result = await this.cacheService.del(cacheKey);

      if (result) {
        this.logger.debug(`缓存删除成功: ${cacheKey}`);
      } else {
        this.logger.warn(`缓存删除失败: ${cacheKey}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`删除缓存失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 删除匹配模式的所有缓存
   * @param pattern 匹配模式
   * @returns 是否成功
   */
  async delByPattern(pattern: string): Promise<boolean> {
    const cachePattern = this.generateKey(pattern);

    try {
      const result = await this.cacheService.delByPattern(cachePattern);

      if (result) {
        this.logger.debug(`匹配模式缓存删除成功: ${cachePattern}`);
      } else {
        this.logger.warn(`匹配模式缓存删除失败: ${cachePattern}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`删除匹配模式缓存失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 缓存预热
   * @param keys 要预热的键列表
   * @param dataLoader 数据加载函数
   * @returns 预热成功的键数量
   */
  async warmup(keys: string[], dataLoader: (key: string) => Promise<any>): Promise<number> {
    let successCount = 0;

    for (const key of keys) {
      try {
        // 检查缓存是否存在
        const cacheKey = this.generateKey(key);
        const existingData = await this.cacheService.get(cacheKey);

        if (existingData === null) {
          // 加载数据
          const data = await dataLoader(key);

          if (data !== null && data !== undefined) {
            // 设置缓存
            const result = await this.set(key, data);

            if (result) {
              successCount++;
              this.logger.debug(`缓存预热成功: ${cacheKey}`);
            }
          }
        } else {
          // 缓存已存在，跳过
          this.logger.debug(`缓存已存在，跳过预热: ${cacheKey}`);
          successCount++;
        }
      } catch (error) {
        this.logger.error(`缓存预热失败: ${key}, 错误: ${error.message}`);
      }
    }

    this.logger.info(`缓存预热完成，成功: ${successCount}/${keys.length}`);
    return successCount;
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计信息
   */
  getStats(): any {
    return this.cacheService.getStats();
  }

  /**
   * 重置缓存统计信息
   */
  resetStats(): void {
    this.cacheService.resetStats();
  }
}