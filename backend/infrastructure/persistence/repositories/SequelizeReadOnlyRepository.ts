/**
 * Sequelize只读仓库基类
 * 提供基于Sequelize的只读仓库实现
 */
import { Model, ModelCtor, Op, WhereOptions } from 'sequelize';
import { Entity } from '../../../domain/Entity';
import { ReadOnlyRepository } from './ReadOnlyRepository';
import { Logger } from '../../logging/Logger';
import { DatabaseConnectionManager } from '../DatabaseConnectionManager';

/**
 * Sequelize只读仓库基类
 * @template T 实体类型
 * @template M Sequelize模型类型
 * @template ID 实体ID类型
 */
export abstract class SequelizeReadOnlyRepository<T extends Entity, M extends Model, ID> implements ReadOnlyRepository<T, ID> {
  /**
   * 构造函数
   * @param model Sequelize模型
   * @param logger 日志记录器
   * @param dbConnectionManager 数据库连接管理器
   */
  constructor(
    protected readonly model: ModelCtor<M>,
    protected readonly logger: Logger,
    protected readonly dbConnectionManager: DatabaseConnectionManager
  ) {}

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 如果找到实体，则返回实体，否则返回null
   */
  async findById(id: ID): Promise<T | null> {
    try {
      // 使用从连接进行查询
      const sequelize = this.dbConnectionManager.getSlaveConnection();

      const data = await this.model.findByPk(id as any, {
        include: this.getDefaultIncludes(),
        sequelize
      });

      if (!data) {
        return null;
      }

      return this.toDomainModel(data);
    } catch (error) {
      this.logger.error(`查找实体失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 查找所有实体
   * @returns 实体列表
   */
  async findAll(): Promise<T[]> {
    try {
      // 使用从连接进行查询
      const sequelize = this.dbConnectionManager.getSlaveConnection();

      const data = await this.model.findAll({
        include: this.getDefaultIncludes(),
        sequelize
      });

      return data.map(item => this.toDomainModel(item));
    } catch (error) {
      this.logger.error(`查找所有实体失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据条件查找实体
   * @param criteria 查询条件
   * @returns 实体列表
   */
  async findByCriteria(criteria: any): Promise<T[]> {
    try {
      // 使用从连接进行查询
      const sequelize = this.dbConnectionManager.getSlaveConnection();

      const whereOptions = this.buildWhereOptions(criteria);

      const data = await this.model.findAll({
        where: whereOptions,
        include: this.getDefaultIncludes(),
        sequelize
      });

      return data.map(item => this.toDomainModel(item));
    } catch (error) {
      this.logger.error(`根据条件查找实体失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查实体是否存在
   * @param id 实体ID
   * @returns 如果实体存在，则返回true，否则返回false
   */
  async exists(id: ID): Promise<boolean> {
    try {
      // 使用从连接进行查询
      const sequelize = this.dbConnectionManager.getSlaveConnection();

      const count = await this.model.count({
        where: { id } as any,
        sequelize
      });

      return count > 0;
    } catch (error) {
      this.logger.error(`检查实体是否存在失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 计算实体数量
   * @param criteria 查询条件
   * @returns 实体数量
   */
  async count(criteria?: any): Promise<number> {
    try {
      // 使用从连接进行查询
      const sequelize = this.dbConnectionManager.getSlaveConnection();

      const whereOptions = criteria ? this.buildWhereOptions(criteria) : undefined;

      return await this.model.count({
        where: whereOptions,
        sequelize
      });
    } catch (error) {
      this.logger.error(`计算实体数量失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 分页查询
   * @param page 页码（从1开始）
   * @param pageSize 每页记录数
   * @param criteria 查询条件
   * @param orderBy 排序条件
   * @returns 分页结果
   */
  async findWithPagination(
    page: number = 1,
    pageSize: number = 10,
    criteria?: any,
    orderBy?: [string, string][]
  ): Promise<{ data: T[]; total: number; page: number; pageSize: number; totalPages: number }> {
    try {
      // 使用从连接进行查询
      const sequelize = this.dbConnectionManager.getSlaveConnection();

      const whereOptions = criteria ? this.buildWhereOptions(criteria) : undefined;

      // 计算总记录数
      const total = await this.model.count({
        where: whereOptions,
        sequelize
      });

      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);

      // 计算偏移量
      const offset = (page - 1) * pageSize;

      // 查询数据
      const data = await this.model.findAll({
        where: whereOptions,
        include: this.getDefaultIncludes(),
        order: orderBy,
        limit: pageSize,
        offset,
        sequelize
      });

      return {
        data: data.map(item => this.toDomainModel(item)),
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      this.logger.error(`分页查询失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 构建WHERE选项
   * @param criteria 查询条件
   * @returns WHERE选项
   * @protected
   */
  protected buildWhereOptions(criteria: any): WhereOptions {
    const whereOptions: WhereOptions = {};

    // 处理软删除
    if (this.model.rawAttributes.deletedAt) {
      whereOptions.deletedAt = null;
    }

    // 处理查询条件
    for (const [key, value] of Object.entries(criteria)) {
      if (value === undefined || value === null) {
        continue;
      }

      // 处理特殊操作符
      if (typeof value === 'object' && !Array.isArray(value)) {
        const operators: any = {};

        for (const [opKey, opValue] of Object.entries(value)) {
          switch (opKey) {
            case 'eq':
              operators[Op.eq] = opValue;
              break;
            case 'ne':
              operators[Op.ne] = opValue;
              break;
            case 'gt':
              operators[Op.gt] = opValue;
              break;
            case 'gte':
              operators[Op.gte] = opValue;
              break;
            case 'lt':
              operators[Op.lt] = opValue;
              break;
            case 'lte':
              operators[Op.lte] = opValue;
              break;
            case 'in':
              operators[Op.in] = opValue;
              break;
            case 'notIn':
              operators[Op.notIn] = opValue;
              break;
            case 'like':
              operators[Op.like] = `%${opValue}%`;
              break;
            case 'notLike':
              operators[Op.notLike] = `%${opValue}%`;
              break;
            case 'startsWith':
              operators[Op.startsWith] = opValue;
              break;
            case 'endsWith':
              operators[Op.endsWith] = opValue;
              break;
            case 'between':
              operators[Op.between] = opValue;
              break;
            case 'notBetween':
              operators[Op.notBetween] = opValue;
              break;
            default:
              operators[opKey] = opValue;
          }
        }

        whereOptions[key] = operators;
      } else if (Array.isArray(value)) {
        whereOptions[key] = {
          [Op.in]: value
        };
      } else {
        whereOptions[key] = value;
      }
    }

    return whereOptions;
  }

  /**
   * 获取默认关联
   * @returns 默认关联
   * @protected
   */
  protected abstract getDefaultIncludes(): any[];

  /**
   * 将Sequelize模型转换为领域模型
   * @param model Sequelize模型
   * @returns 领域模型
   * @protected
   */
  protected abstract toDomainModel(model: M): T;
}