/**
 * 增强版Sequelize练习仓库
 * 使用增强版仓库基类实现
 */
import { Exercise } from '../../../../../domain/models/content/exercise/Exercise';
import { ExerciseRepository } from '../../../../../domain/repositories/content/exercise/ExerciseRepository';
import { EnhancedRepositoryBase } from '../../EnhancedRepositoryBase';
import { UnitOfWork } from '../../../UnitOfWork';
import { EventPublisher } from '../../../../events/EventPublisher';
import { Logger } from '../../../../logging/Logger';
import { Sequelize, Op } from 'sequelize';
import { Difficulty } from '../../../../../domain/models/content/Difficulty';
import { ContentStatus } from '../../../../../domain/models/content/ContentStatus';
import { Visibility } from '../../../../../domain/models/content/Visibility';
import { AggregateRoot } from '../../../../../domain/models/common/AggregateRoot';

/**
 * EnhancedSequelizeExerciseRepository类
 * 使用Sequelize实现的增强版练习仓库
 */
export class EnhancedSequelizeExerciseRepository extends EnhancedRepositoryBase<Exercise, number> implements ExerciseRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param logger 日志记录器
   * @param sequelize Sequelize实例
   * @param exerciseModel 练习模型
   * @param exerciseTagModel 练习标签关联模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    logger: Logger,
    private readonly sequelize: Sequelize,
    private readonly exerciseModel: any,
    private readonly exerciseTagModel: any
  ) {
    super(unitOfWork, eventPublisher, logger);
  }

  /**
   * 根据ID查找练习
   * @param id 练习ID
   * @returns 如果找到练习，则返回练习，否则返回null
   */
  async findById(id: number): Promise<Exercise | null> {
    this.logger.debug(`查找练习: ID=${id}`);
    
    const exerciseData = await this.exerciseModel.findByPk(id, {
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    if (!exerciseData) {
      this.logger.debug(`未找到练习: ID=${id}`);
      return null;
    }

    const exercise = this.toDomainModel(exerciseData);
    this.logger.debug(`找到练习: ID=${id}, 标题=${exercise.title}`);
    return exercise;
  }

  /**
   * 查找所有练习
   * @returns 练习列表
   */
  async findAll(): Promise<Exercise[]> {
    this.logger.debug('查找所有练习');
    
    const exerciseData = await this.exerciseModel.findAll({
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个练习`);
    return exercises;
  }

  /**
   * 根据标签ID查找练习
   * @param tagId 标签ID
   * @returns 练习列表
   */
  async findByTagId(tagId: number): Promise<Exercise[]> {
    this.logger.debug(`根据标签ID查找练习: tagId=${tagId}`);
    
    const exerciseData = await this.exerciseModel.findAll({
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags',
          where: { tag_id: tagId }
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个练习: tagId=${tagId}`);
    return exercises;
  }

  /**
   * 根据标签ID和难度查找练习
   * @param tagId 标签ID
   * @param difficulty 难度级别
   * @returns 练习列表
   */
  async findByTagIdAndDifficulty(tagId: number, difficulty: Difficulty): Promise<Exercise[]> {
    this.logger.debug(`根据标签ID和难度查找练习: tagId=${tagId}, difficulty=${difficulty}`);
    
    const difficultyValue = this.mapDifficultyToDbValue(difficulty);

    const exerciseData = await this.exerciseModel.findAll({
      where: {
        difficulty: difficultyValue
      },
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags',
          where: { tag_id: tagId }
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个练习: tagId=${tagId}, difficulty=${difficulty}`);
    return exercises;
  }

  /**
   * 根据创建者ID查找练习
   * @param creatorId 创建者ID
   * @returns 练习列表
   */
  async findByCreatorId(creatorId: string): Promise<Exercise[]> {
    this.logger.debug(`根据创建者ID查找练习: creatorId=${creatorId}`);
    
    const exerciseData = await this.exerciseModel.findAll({
      where: { creator_id: creatorId },
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个练习: creatorId=${creatorId}`);
    return exercises;
  }

  /**
   * 根据关键字搜索练习
   * @param keyword 关键字
   * @param limit 返回的最大数量
   * @returns 练习列表
   */
  async searchByKeyword(keyword: string, limit?: number): Promise<Exercise[]> {
    this.logger.debug(`根据关键字搜索练习: keyword=${keyword}, limit=${limit}`);
    
    const exerciseData = await this.exerciseModel.findAll({
      where: {
        [Op.or]: [
          { title: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ]
      },
      limit: limit,
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个练习: keyword=${keyword}`);
    return exercises;
  }

  /**
   * 查找已删除的练习
   * @param creatorId 创建者ID
   * @returns 已删除的练习列表
   */
  async findDeleted(creatorId: string): Promise<Exercise[]> {
    this.logger.debug(`查找已删除的练习: creatorId=${creatorId}`);
    
    const exerciseData = await this.exerciseModel.findAll({
      where: {
        creator_id: creatorId,
        status: this.mapContentStatusToDbValue(ContentStatus.DELETED)
      },
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      paranoid: false, // 包括已软删除的记录
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个已删除的练习: creatorId=${creatorId}`);
    return exercises;
  }

  /**
   * 查找最近创建的练习
   * @param limit 返回的最大数量
   * @returns 最近创建的练习列表
   */
  async findRecent(limit: number): Promise<Exercise[]> {
    this.logger.debug(`查找最近创建的练习: limit=${limit}`);
    
    const exerciseData = await this.exerciseModel.findAll({
      order: [['created_at', 'DESC']],
      limit: limit,
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个最近创建的练习`);
    return exercises;
  }

  /**
   * 查找官方练习
   * @param limit 返回的最大数量
   * @returns 官方练习列表
   */
  async findOfficial(limit: number): Promise<Exercise[]> {
    this.logger.debug(`查找官方练习: limit=${limit}`);
    
    const exerciseData = await this.exerciseModel.findAll({
      where: { is_official: true },
      limit: limit,
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个官方练习`);
    return exercises;
  }

  /**
   * 根据条件查询
   * @param criteria 查询条件
   * @returns 符合条件的练习列表
   */
  async findByCriteria(criteria: any): Promise<Exercise[]> {
    this.logger.debug(`根据条件查询练习: ${JSON.stringify(criteria)}`);
    
    const where: any = {};
    
    if (criteria.title) {
      where.title = { [Op.like]: `%${criteria.title}%` };
    }
    
    if (criteria.difficulty) {
      where.difficulty = this.mapDifficultyToDbValue(criteria.difficulty);
    }
    
    if (criteria.status) {
      where.status = this.mapContentStatusToDbValue(criteria.status);
    }
    
    if (criteria.creatorId) {
      where.creator_id = criteria.creatorId;
    }
    
    if (criteria.isOfficial !== undefined) {
      where.is_official = criteria.isOfficial;
    }
    
    const exerciseData = await this.exerciseModel.findAll({
      where,
      include: [
        {
          model: this.exerciseTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const exercises = exerciseData.map(exercise => this.toDomainModel(exercise));
    this.logger.debug(`找到${exercises.length}个符合条件的练习`);
    return exercises;
  }

  /**
   * 检查仓库是否支持指定的实体类型
   * @param entity 要检查的实体
   * @returns 是否支持
   */
  supports(entity: AggregateRoot): boolean {
    return entity instanceof Exercise;
  }

  /**
   * 保存练习
   * @param exercise 要保存的练习
   * @returns 保存后的练习
   */
  protected async doSave(exercise: Exercise): Promise<Exercise> {
    this.logger.debug(`保存练习: ID=${exercise.id}, 标题=${exercise.title}`);
    
    const transaction = this.getTransaction();

    // 准备练习数据
    const exerciseData = {
      title: exercise.title,
      description: exercise.description,
      expected_result: exercise.expectedResult,
      difficulty: this.mapDifficultyToDbValue(exercise.difficulty),
      time_estimate_minutes: exercise.timeEstimateMinutes,
      creator_id: exercise.creatorId,
      status: this.mapContentStatusToDbValue(exercise.status),
      is_official: exercise.isOfficial,
      created_at: exercise.createdAt,
      updated_at: exercise.updatedAt,
      deleted_at: exercise.deletedAt
    };

    if (exercise.id === 0) {
      // 创建新练习
      this.logger.debug('创建新练习');
      const createdExercise = await this.exerciseModel.create(exerciseData, { transaction });

      // 更新ID
      const newExercise = new Exercise(
        createdExercise.id,
        exercise.title,
        exercise.description,
        exercise.expectedResult,
        exercise.difficulty,
        exercise.timeEstimateMinutes,
        exercise.creatorId,
        exercise.status,
        exercise.visibility,
        exercise.isOfficial,
        exercise.createdAt,
        exercise.updatedAt,
        exercise.deletedAt
      );

      // 复制标签
      exercise.tags.forEach(tag => newExercise.addTag(tag));

      // 保存标签关联
      await this.saveExerciseTags(newExercise.id, newExercise.tags, transaction);
      
      this.logger.debug(`创建练习成功: ID=${newExercise.id}`);
      return newExercise;
    } else {
      // 更新现有练习
      this.logger.debug(`更新练习: ID=${exercise.id}`);
      await this.exerciseModel.update(exerciseData, {
        where: { id: exercise.id },
        transaction
      });

      // 更新标签关联
      await this.exerciseTagModel.destroy({
        where: { exercise_id: exercise.id },
        transaction
      });

      await this.saveExerciseTags(exercise.id, exercise.tags, transaction);
      
      this.logger.debug(`更新练习成功: ID=${exercise.id}`);
      return exercise;
    }
  }

  /**
   * 删除练习
   * @param exercise 要删除的练习
   */
  protected async doDelete(exercise: Exercise): Promise<void> {
    this.logger.debug(`删除练习: ID=${exercise.id}, 标题=${exercise.title}`);
    
    const transaction = this.getTransaction();

    // 软删除
    await this.exerciseModel.update({
      status: this.mapContentStatusToDbValue(ContentStatus.DELETED),
      deleted_at: new Date(),
      updated_at: new Date()
    }, {
      where: { id: exercise.id },
      transaction
    });
    
    this.logger.debug(`删除练习成功: ID=${exercise.id}`);
  }

  /**
   * 保存练习标签关联
   * @param exerciseId 练习ID
   * @param tags 标签列表
   * @param transaction 事务
   */
  private async saveExerciseTags(exerciseId: number, tags: string[], transaction: any): Promise<void> {
    if (tags.length === 0) return;

    this.logger.debug(`保存练习标签关联: exerciseId=${exerciseId}, tags=${tags.join(', ')}`);
    
    const tagData = tags.map(tag => ({
      exercise_id: exerciseId,
      tag_name: tag
    }));

    await this.exerciseTagModel.bulkCreate(tagData, { transaction });
  }

  /**
   * 将数据库模型转换为领域模型
   * @param exerciseData 数据库模型
   * @returns 领域模型
   */
  private toDomainModel(exerciseData: any): Exercise {
    const exercise = new Exercise(
      exerciseData.id,
      exerciseData.title,
      exerciseData.description,
      exerciseData.expected_result,
      this.mapDbValueToDifficulty(exerciseData.difficulty),
      exerciseData.time_estimate_minutes,
      exerciseData.creator_id,
      this.mapDbValueToContentStatus(exerciseData.status),
      Visibility.PRIVATE, // 默认值，可能需要从数据库中获取
      exerciseData.is_official,
      exerciseData.created_at,
      exerciseData.updated_at,
      exerciseData.deleted_at
    );

    // 添加标签
    if (exerciseData.tags) {
      exerciseData.tags.forEach((tag: any) => {
        exercise.addTag(tag.tag_name);
      });
    }

    return exercise;
  }

  /**
   * 将难度枚举映射为数据库值
   * @param difficulty 难度枚举
   * @returns 数据库值
   */
  private mapDifficultyToDbValue(difficulty: Difficulty): string {
    switch (difficulty) {
      case Difficulty.EASY:
        return 'beginner';
      case Difficulty.MEDIUM:
        return 'intermediate';
      case Difficulty.HARD:
        return 'advanced';
      default:
        return 'beginner';
    }
  }

  /**
   * 将数据库值映射为难度枚举
   * @param dbValue 数据库值
   * @returns 难度枚举
   */
  private mapDbValueToDifficulty(dbValue: string): Difficulty {
    switch (dbValue) {
      case 'beginner':
        return Difficulty.EASY;
      case 'intermediate':
        return Difficulty.MEDIUM;
      case 'advanced':
        return Difficulty.HARD;
      default:
        return Difficulty.EASY;
    }
  }

  /**
   * 将内容状态枚举映射为数据库值
   * @param status 内容状态枚举
   * @returns 数据库值
   */
  private mapContentStatusToDbValue(status: ContentStatus): string {
    switch (status) {
      case ContentStatus.DRAFT:
        return 'draft';
      case ContentStatus.PUBLISHED:
        return 'published';
      case ContentStatus.DELETED:
        return 'archived';
      default:
        return 'draft';
    }
  }

  /**
   * 将数据库值映射为内容状态枚举
   * @param dbValue 数据库值
   * @returns 内容状态枚举
   */
  private mapDbValueToContentStatus(dbValue: string): ContentStatus {
    switch (dbValue) {
      case 'draft':
        return ContentStatus.DRAFT;
      case 'published':
        return ContentStatus.PUBLISHED;
      case 'archived':
        return ContentStatus.DELETED;
      default:
        return ContentStatus.DRAFT;
    }
  }
}
