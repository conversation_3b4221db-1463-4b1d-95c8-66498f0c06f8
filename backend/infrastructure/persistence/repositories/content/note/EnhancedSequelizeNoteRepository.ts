/**
 * 增强版Sequelize笔记仓库
 * 使用增强版仓库基类实现
 */
import { Note } from '../../../../../domain/models/content/note/Note';
import { NoteRepository } from '../../../../../domain/repositories/content/note/NoteRepository';
import { EnhancedRepositoryBase } from '../../EnhancedRepositoryBase';
import { UnitOfWork } from '../../../UnitOfWork';
import { EventPublisher } from '../../../../events/EventPublisher';
import { Logger } from '../../../../logging/Logger';
import { Sequelize, Op } from 'sequelize';
import { ContentStatus } from '../../../../../domain/models/content/ContentStatus';
import { Visibility } from '../../../../../domain/models/content/Visibility';
import { AggregateRoot } from '../../../../../domain/models/common/AggregateRoot';

/**
 * EnhancedSequelizeNoteRepository类
 * 使用Sequelize实现的增强版笔记仓库
 */
export class EnhancedSequelizeNoteRepository extends EnhancedRepositoryBase<Note, number> implements NoteRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param logger 日志记录器
   * @param sequelize Sequelize实例
   * @param noteModel 笔记模型
   * @param noteTagModel 笔记标签关联模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    logger: Logger,
    private readonly sequelize: Sequelize,
    private readonly noteModel: any,
    private readonly noteTagModel: any
  ) {
    super(unitOfWork, eventPublisher, logger);
  }

  /**
   * 根据ID查找笔记
   * @param id 笔记ID
   * @returns 如果找到笔记，则返回笔记，否则返回null
   */
  async findById(id: number): Promise<Note | null> {
    this.logger.debug(`查找笔记: ID=${id}`);
    
    const noteData = await this.noteModel.findByPk(id, {
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    if (!noteData) {
      this.logger.debug(`未找到笔记: ID=${id}`);
      return null;
    }

    const note = this.toDomainModel(noteData);
    this.logger.debug(`找到笔记: ID=${id}, 标题=${note.title}`);
    return note;
  }

  /**
   * 查找所有笔记
   * @returns 笔记列表
   */
  async findAll(): Promise<Note[]> {
    this.logger.debug('查找所有笔记');
    
    const noteData = await this.noteModel.findAll({
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个笔记`);
    return notes;
  }

  /**
   * 根据用户ID查找笔记
   * @param userId 用户ID
   * @returns 笔记列表
   */
  async findByUserId(userId: string): Promise<Note[]> {
    this.logger.debug(`根据用户ID查找笔记: userId=${userId}`);
    
    const noteData = await this.noteModel.findAll({
      where: { user_id: userId },
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个笔记: userId=${userId}`);
    return notes;
  }

  /**
   * 根据标签ID查找笔记
   * @param tagId 标签ID
   * @returns 笔记列表
   */
  async findByTagId(tagId: number): Promise<Note[]> {
    this.logger.debug(`根据标签ID查找笔记: tagId=${tagId}`);
    
    const noteData = await this.noteModel.findAll({
      include: [
        {
          model: this.noteTagModel,
          as: 'tags',
          where: { tag_id: tagId }
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个笔记: tagId=${tagId}`);
    return notes;
  }

  /**
   * 根据学习计划ID查找笔记
   * @param planId 学习计划ID
   * @returns 笔记列表
   */
  async findByPlanId(planId: number): Promise<Note[]> {
    this.logger.debug(`根据学习计划ID查找笔记: planId=${planId}`);
    
    const noteData = await this.noteModel.findAll({
      where: { plan_id: planId },
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个笔记: planId=${planId}`);
    return notes;
  }

  /**
   * 根据关键字搜索笔记
   * @param keyword 关键字
   * @param limit 返回的最大数量
   * @returns 笔记列表
   */
  async searchByKeyword(keyword: string, limit?: number): Promise<Note[]> {
    this.logger.debug(`根据关键字搜索笔记: keyword=${keyword}, limit=${limit}`);
    
    const noteData = await this.noteModel.findAll({
      where: {
        [Op.or]: [
          { title: { [Op.like]: `%${keyword}%` } },
          { content: { [Op.like]: `%${keyword}%` } }
        ]
      },
      limit: limit,
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个笔记: keyword=${keyword}`);
    return notes;
  }

  /**
   * 查找已删除的笔记
   * @param userId 用户ID
   * @returns 已删除的笔记列表
   */
  async findDeleted(userId: string): Promise<Note[]> {
    this.logger.debug(`查找已删除的笔记: userId=${userId}`);
    
    const noteData = await this.noteModel.findAll({
      where: {
        user_id: userId,
        status: this.mapContentStatusToDbValue(ContentStatus.DELETED)
      },
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      paranoid: false, // 包括已软删除的记录
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个已删除的笔记: userId=${userId}`);
    return notes;
  }

  /**
   * 查找最近创建的笔记
   * @param limit 返回的最大数量
   * @returns 最近创建的笔记列表
   */
  async findRecent(limit: number): Promise<Note[]> {
    this.logger.debug(`查找最近创建的笔记: limit=${limit}`);
    
    const noteData = await this.noteModel.findAll({
      order: [['created_at', 'DESC']],
      limit: limit,
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个最近创建的笔记`);
    return notes;
  }

  /**
   * 查找热门笔记
   * @param limit 返回的最大数量
   * @returns 热门笔记列表
   */
  async findPopular(limit: number): Promise<Note[]> {
    this.logger.debug(`查找热门笔记: limit=${limit}`);
    
    const noteData = await this.noteModel.findAll({
      order: [
        ['like_count', 'DESC'],
        ['view_count', 'DESC']
      ],
      limit: limit,
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个热门笔记`);
    return notes;
  }

  /**
   * 查找AI生成的笔记
   * @param userId 用户ID
   * @returns AI生成的笔记列表
   */
  async findAiGenerated(userId: string): Promise<Note[]> {
    this.logger.debug(`查找AI生成的笔记: userId=${userId}`);
    
    const noteData = await this.noteModel.findAll({
      where: {
        user_id: userId,
        is_ai_generated: true
      },
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个AI生成的笔记: userId=${userId}`);
    return notes;
  }

  /**
   * 更新笔记点赞数
   * @param noteId 笔记ID
   * @param likeCount 点赞数
   */
  async updateLikeCount(noteId: number, likeCount: number): Promise<void> {
    this.logger.debug(`更新笔记点赞数: noteId=${noteId}, likeCount=${likeCount}`);
    
    await this.noteModel.update(
      { like_count: likeCount },
      {
        where: { id: noteId },
        transaction: this.getTransaction()
      }
    );
    
    this.logger.debug(`更新笔记点赞数成功: noteId=${noteId}, likeCount=${likeCount}`);
  }

  /**
   * 更新笔记评论数
   * @param noteId 笔记ID
   * @param commentCount 评论数
   */
  async updateCommentCount(noteId: number, commentCount: number): Promise<void> {
    this.logger.debug(`更新笔记评论数: noteId=${noteId}, commentCount=${commentCount}`);
    
    await this.noteModel.update(
      { comment_count: commentCount },
      {
        where: { id: noteId },
        transaction: this.getTransaction()
      }
    );
    
    this.logger.debug(`更新笔记评论数成功: noteId=${noteId}, commentCount=${commentCount}`);
  }

  /**
   * 更新笔记查看次数
   * @param noteId 笔记ID
   * @param viewCount 查看次数
   */
  async updateViewCount(noteId: number, viewCount: number): Promise<void> {
    this.logger.debug(`更新笔记查看次数: noteId=${noteId}, viewCount=${viewCount}`);
    
    await this.noteModel.update(
      { view_count: viewCount },
      {
        where: { id: noteId },
        transaction: this.getTransaction()
      }
    );
    
    this.logger.debug(`更新笔记查看次数成功: noteId=${noteId}, viewCount=${viewCount}`);
  }

  /**
   * 根据条件查询
   * @param criteria 查询条件
   * @returns 符合条件的笔记列表
   */
  async findByCriteria(criteria: any): Promise<Note[]> {
    this.logger.debug(`根据条件查询笔记: ${JSON.stringify(criteria)}`);
    
    const where: any = {};
    
    if (criteria.title) {
      where.title = { [Op.like]: `%${criteria.title}%` };
    }
    
    if (criteria.userId) {
      where.user_id = criteria.userId;
    }
    
    if (criteria.status) {
      where.status = this.mapContentStatusToDbValue(criteria.status);
    }
    
    if (criteria.isAiGenerated !== undefined) {
      where.is_ai_generated = criteria.isAiGenerated;
    }
    
    if (criteria.planId) {
      where.plan_id = criteria.planId;
    }
    
    const noteData = await this.noteModel.findAll({
      where,
      include: [
        {
          model: this.noteTagModel,
          as: 'tags'
        }
      ],
      transaction: this.getTransaction()
    });

    const notes = noteData.map(note => this.toDomainModel(note));
    this.logger.debug(`找到${notes.length}个符合条件的笔记`);
    return notes;
  }

  /**
   * 检查仓库是否支持指定的实体类型
   * @param entity 要检查的实体
   * @returns 是否支持
   */
  supports(entity: AggregateRoot): boolean {
    return entity instanceof Note;
  }

  /**
   * 保存笔记
   * @param note 要保存的笔记
   * @returns 保存后的笔记
   */
  protected async doSave(note: Note): Promise<Note> {
    this.logger.debug(`保存笔记: ID=${note.id}, 标题=${note.title}`);
    
    const transaction = this.getTransaction();

    // 准备笔记数据
    const noteData = {
      title: note.title,
      content: note.content,
      user_id: note.userId,
      image_url: note.imageUrl,
      status: this.mapContentStatusToDbValue(note.status),
      like_count: note.likeCount,
      comment_count: note.commentCount,
      view_count: note.viewCount,
      is_ai_generated: note.isAiGenerated,
      plan_id: note.planId,
      created_at: note.createdAt,
      updated_at: note.updatedAt,
      deleted_at: note.deletedAt
    };

    if (note.id === 0) {
      // 创建新笔记
      this.logger.debug('创建新笔记');
      const createdNote = await this.noteModel.create(noteData, { transaction });

      // 更新ID
      const newNote = new Note(
        createdNote.id,
        note.title,
        note.content,
        note.userId,
        note.imageUrl,
        note.status,
        note.visibility,
        note.likeCount,
        note.commentCount,
        note.viewCount,
        note.isAiGenerated,
        note.planId,
        note.createdAt,
        note.updatedAt,
        note.deletedAt
      );

      // 复制标签
      note.tags.forEach(tag => newNote.addTag(tag));

      // 保存标签关联
      await this.saveNoteTags(newNote.id, newNote.tags, transaction);
      
      this.logger.debug(`创建笔记成功: ID=${newNote.id}`);
      return newNote;
    } else {
      // 更新现有笔记
      this.logger.debug(`更新笔记: ID=${note.id}`);
      await this.noteModel.update(noteData, {
        where: { id: note.id },
        transaction
      });

      // 更新标签关联
      await this.noteTagModel.destroy({
        where: { note_id: note.id },
        transaction
      });

      await this.saveNoteTags(note.id, note.tags, transaction);
      
      this.logger.debug(`更新笔记成功: ID=${note.id}`);
      return note;
    }
  }

  /**
   * 删除笔记
   * @param note 要删除的笔记
   */
  protected async doDelete(note: Note): Promise<void> {
    this.logger.debug(`删除笔记: ID=${note.id}, 标题=${note.title}`);
    
    const transaction = this.getTransaction();

    // 软删除
    await this.noteModel.update({
      status: this.mapContentStatusToDbValue(ContentStatus.DELETED),
      deleted_at: new Date(),
      updated_at: new Date()
    }, {
      where: { id: note.id },
      transaction
    });
    
    this.logger.debug(`删除笔记成功: ID=${note.id}`);
  }

  /**
   * 保存笔记标签关联
   * @param noteId 笔记ID
   * @param tags 标签列表
   * @param transaction 事务
   */
  private async saveNoteTags(noteId: number, tags: string[], transaction: any): Promise<void> {
    if (tags.length === 0) return;

    this.logger.debug(`保存笔记标签关联: noteId=${noteId}, tags=${tags.join(', ')}`);
    
    const tagData = tags.map(tag => ({
      note_id: noteId,
      tag_name: tag
    }));

    await this.noteTagModel.bulkCreate(tagData, { transaction });
  }

  /**
   * 将数据库模型转换为领域模型
   * @param noteData 数据库模型
   * @returns 领域模型
   */
  private toDomainModel(noteData: any): Note {
    const note = new Note(
      noteData.id,
      noteData.title,
      noteData.content,
      noteData.user_id,
      noteData.image_url,
      this.mapDbValueToContentStatus(noteData.status),
      Visibility.PRIVATE, // 默认值，可能需要从数据库中获取
      noteData.like_count,
      noteData.comment_count,
      noteData.view_count,
      noteData.is_ai_generated,
      noteData.plan_id,
      noteData.created_at,
      noteData.updated_at,
      noteData.deleted_at
    );

    // 添加标签
    if (noteData.tags) {
      noteData.tags.forEach((tag: any) => {
        note.addTag(tag.tag_name);
      });
    }

    return note;
  }

  /**
   * 将内容状态枚举映射为数据库值
   * @param status 内容状态枚举
   * @returns 数据库值
   */
  private mapContentStatusToDbValue(status: ContentStatus): string {
    switch (status) {
      case ContentStatus.DRAFT:
        return 'draft';
      case ContentStatus.PUBLISHED:
        return 'published';
      case ContentStatus.DELETED:
        return 'archived';
      default:
        return 'draft';
    }
  }

  /**
   * 将数据库值映射为内容状态枚举
   * @param dbValue 数据库值
   * @returns 内容状态枚举
   */
  private mapDbValueToContentStatus(dbValue: string): ContentStatus {
    switch (dbValue) {
      case 'draft':
        return ContentStatus.DRAFT;
      case 'published':
        return ContentStatus.PUBLISHED;
      case 'archived':
        return ContentStatus.DELETED;
      default:
        return ContentStatus.DRAFT;
    }
  }
}
