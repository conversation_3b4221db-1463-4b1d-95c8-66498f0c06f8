import { BadgeRepository } from '../../../../domain/repositories/gamification/BadgeRepository';
import { Badge } from '../../../../domain/models/gamification/Badge';
import { BadgeCategory } from '../../../../domain/models/gamification/BadgeCategory';
import { BadgeRarity } from '../../../../domain/models/gamification/BadgeRarity';
import { UnitOfWork } from '../../UnitOfWork';
import { EventPublisher } from '../../../events/EventPublisher';
import { Sequelize, Op, Transaction } from 'sequelize';
import { RepositoryBase } from '../RepositoryBase';

/**
 * Sequelize徽章仓库实现
 * 使用Sequelize ORM实现徽章仓库接口
 */
export class SequelizeBadgeRepository extends RepositoryBase implements BadgeRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param sequelize Sequelize实例
   * @param badgeModel 徽章模型
   * @param userBadgeModel 用户徽章模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    private readonly sequelize: Sequelize,
    private readonly badgeModel: any,
    private readonly userBadgeModel: any
  ) {
    super(unitOfWork, eventPublisher);
  }

  /**
   * 根据ID查找徽章
   * @param id 徽章ID
   * @returns 徽章实体，如果不存在则返回null
   */
  async findById(id: number): Promise<Badge | null> {
    const badgeData = await this.badgeModel.findByPk(id);
    if (!badgeData) {
      return null;
    }
    return this.mapToEntity(badgeData);
  }

  /**
   * 根据名称查找徽章
   * @param name 徽章名称
   * @returns 徽章实体，如果不存在则返回null
   */
  async findByName(name: string): Promise<Badge | null> {
    const badgeData = await this.badgeModel.findOne({
      where: { name }
    });
    if (!badgeData) {
      return null;
    }
    return this.mapToEntity(badgeData);
  }

  /**
   * 查找所有徽章
   * @param includeDeleted 是否包含已删除的徽章
   * @returns 徽章实体数组
   */
  async findAll(includeDeleted: boolean = false): Promise<Badge[]> {
    const where: any = {};
    if (!includeDeleted) {
      where.deleted_at = null;
    }
    const badgesData = await this.badgeModel.findAll({ where });
    return badgesData.map(data => this.mapToEntity(data));
  }

  /**
   * 根据类别查找徽章
   * @param category 徽章类别
   * @param includeDeleted 是否包含已删除的徽章
   * @returns 徽章实体数组
   */
  async findByCategory(category: BadgeCategory, includeDeleted: boolean = false): Promise<Badge[]> {
    const where: any = {
      category: category.value
    };
    if (!includeDeleted) {
      where.deleted_at = null;
    }
    const badgesData = await this.badgeModel.findAll({ where });
    return badgesData.map(data => this.mapToEntity(data));
  }

  /**
   * 根据稀有度查找徽章
   * @param rarity 徽章稀有度
   * @param includeDeleted 是否包含已删除的徽章
   * @returns 徽章实体数组
   */
  async findByRarity(rarity: BadgeRarity, includeDeleted: boolean = false): Promise<Badge[]> {
    const where: any = {
      rarity: rarity.value
    };
    if (!includeDeleted) {
      where.deleted_at = null;
    }
    const badgesData = await this.badgeModel.findAll({ where });
    return badgesData.map(data => this.mapToEntity(data));
  }

  /**
   * 根据用户ID查找已获得的徽章
   * @param userId 用户ID
   * @returns 徽章实体数组
   */
  async findByUserId(userId: string): Promise<Badge[]> {
    const userBadges = await this.userBadgeModel.findAll({
      where: { user_id: userId },
      include: [
        {
          model: this.badgeModel,
          as: 'badge',
          where: { deleted_at: null }
        }
      ]
    });
    
    return userBadges.map(ub => this.mapToEntity(ub.badge));
  }

  /**
   * 根据用户ID查找已装备的徽章
   * @param userId 用户ID
   * @returns 徽章实体数组
   */
  async findEquippedByUserId(userId: string): Promise<Badge[]> {
    const userBadges = await this.userBadgeModel.findAll({
      where: { 
        user_id: userId,
        is_equipped: true
      },
      include: [
        {
          model: this.badgeModel,
          as: 'badge',
          where: { deleted_at: null }
        }
      ]
    });
    
    return userBadges.map(ub => this.mapToEntity(ub.badge));
  }

  /**
   * 保存徽章
   * @param badge 徽章实体
   * @returns 保存后的徽章实体
   */
  async save(badge: Badge): Promise<Badge> {
    const transaction = await this.getTransaction();
    
    try {
      const badgeData = this.mapToModel(badge);
      let savedBadge;
      
      if (badge.id === 0) {
        // 创建新徽章
        savedBadge = await this.badgeModel.create(badgeData, { transaction });
      } else {
        // 更新现有徽章
        await this.badgeModel.update(badgeData, {
          where: { id: badge.id },
          transaction
        });
        savedBadge = await this.badgeModel.findByPk(badge.id, { transaction });
      }
      
      // 发布领域事件
      this.publishEvents(badge);
      
      return this.mapToEntity(savedBadge);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 删除徽章
   * @param id 徽章ID
   * @returns 是否成功删除
   */
  async delete(id: number): Promise<boolean> {
    const transaction = await this.getTransaction();
    
    try {
      const badge = await this.findById(id);
      if (!badge) {
        return false;
      }
      
      badge.softDelete();
      await this.save(badge);
      
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 恢复徽章
   * @param id 徽章ID
   * @returns 是否成功恢复
   */
  async restore(id: number): Promise<boolean> {
    const transaction = await this.getTransaction();
    
    try {
      const badge = await this.findById(id);
      if (!badge) {
        return false;
      }
      
      badge.restore();
      await this.save(badge);
      
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 检查用户是否已获得徽章
   * @param userId 用户ID
   * @param badgeId 徽章ID
   * @returns 是否已获得
   */
  async hasUserAcquired(userId: string, badgeId: number): Promise<boolean> {
    const userBadge = await this.userBadgeModel.findOne({
      where: {
        user_id: userId,
        badge_id: badgeId
      }
    });
    
    return !!userBadge;
  }

  /**
   * 授予用户徽章
   * @param userId 用户ID
   * @param badgeId 徽章ID
   * @returns 是否成功授予
   */
  async awardToUser(userId: string, badgeId: number): Promise<boolean> {
    const transaction = await this.getTransaction();
    
    try {
      // 检查用户是否已获得该徽章
      const hasAcquired = await this.hasUserAcquired(userId, badgeId);
      if (hasAcquired) {
        return false;
      }
      
      // 创建用户徽章记录
      await this.userBadgeModel.create({
        user_id: userId,
        badge_id: badgeId,
        acquired_at: new Date(),
        is_equipped: false
      }, { transaction });
      
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 装备徽章
   * @param userId 用户ID
   * @param badgeId 徽章ID
   * @returns 是否成功装备
   */
  async equipBadge(userId: string, badgeId: number): Promise<boolean> {
    const transaction = await this.getTransaction();
    
    try {
      // 检查用户是否已获得该徽章
      const userBadge = await this.userBadgeModel.findOne({
        where: {
          user_id: userId,
          badge_id: badgeId
        }
      });
      
      if (!userBadge) {
        return false;
      }
      
      // 更新装备状态
      await userBadge.update({ is_equipped: true }, { transaction });
      
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 卸下徽章
   * @param userId 用户ID
   * @param badgeId 徽章ID
   * @returns 是否成功卸下
   */
  async unequipBadge(userId: string, badgeId: number): Promise<boolean> {
    const transaction = await this.getTransaction();
    
    try {
      // 检查用户是否已获得该徽章
      const userBadge = await this.userBadgeModel.findOne({
        where: {
          user_id: userId,
          badge_id: badgeId
        }
      });
      
      if (!userBadge) {
        return false;
      }
      
      // 更新装备状态
      await userBadge.update({ is_equipped: false }, { transaction });
      
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 将数据库模型映射为领域实体
   * @param model 数据库模型
   * @returns 领域实体
   */
  private mapToEntity(model: any): Badge {
    return new Badge(
      model.id,
      model.name,
      model.description,
      model.icon,
      BadgeCategory.fromString(model.category),
      BadgeRarity.fromString(model.rarity),
      model.is_displayable,
      model.unlock_condition,
      new Date(model.created_at),
      new Date(model.updated_at),
      model.deleted_at ? new Date(model.deleted_at) : null
    );
  }

  /**
   * 将领域实体映射为数据库模型
   * @param entity 领域实体
   * @returns 数据库模型
   */
  private mapToModel(entity: Badge): any {
    const model: any = {
      name: entity.name,
      description: entity.description,
      icon: entity.icon,
      category: entity.category.value,
      rarity: entity.rarity.value,
      is_displayable: entity.isDisplayable,
      unlock_condition: entity.unlockCondition,
      updated_at: entity.updatedAt
    };
    
    if (entity.id > 0) {
      model.id = entity.id;
    }
    
    if (entity.isDeleted) {
      model.deleted_at = entity.deletedAt;
    } else {
      model.deleted_at = null;
    }
    
    return model;
  }

  /**
   * 获取事务
   * @returns 事务对象
   */
  private async getTransaction(): Promise<Transaction> {
    return this.unitOfWork.getTransaction() as Transaction;
  }
}
