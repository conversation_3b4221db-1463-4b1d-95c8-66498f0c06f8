import { Sequelize, Op } from 'sequelize';
import { RepositoryBase } from '../RepositoryBase';
import { LearningTemplate } from '../../../../domain/models/learningTemplate/LearningTemplate';
import { LearningTemplateRepository } from '../../../../domain/repositories/learningTemplate/LearningTemplateRepository';
import { UnitOfWork } from '../../UnitOfWork';
import { EventPublisher } from '../../../events/EventPublisher';
import { Difficulty } from '../../../../domain/models/learningTemplate/Difficulty';
import { Rating } from '../../../../domain/models/learningTemplate/Rating';
import { TemplateStatus } from '../../../../domain/models/learningTemplate/TemplateStatus';

/**
 * SequelizeLearningTemplateRepository类
 * 使用Sequelize实现的学习模板仓库
 */
export class SequelizeLearningTemplateRepository extends RepositoryBase<LearningTemplate, number> implements LearningTemplateRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param sequelize Sequelize实例
   * @param learningTemplateModel 学习模板模型
   * @param templateTagModel 模板标签关联模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    private readonly sequelize: Sequelize,
    private readonly learningTemplateModel: any,
    private readonly templateTagModel: any
  ) {
    super(unitOfWork, eventPublisher);
  }

  /**
   * 根据ID查找学习模板
   * @param id 学习模板ID
   * @returns 如果找到学习模板，则返回学习模板，否则返回null
   */
  async findById(id: number): Promise<LearningTemplate | null> {
    const templateData = await this.learningTemplateModel.findByPk(id);
    if (!templateData) return null;

    return this.toDomainModel(templateData);
  }

  /**
   * 查找所有学习模板
   * @returns 学习模板列表
   */
  async findAll(): Promise<LearningTemplate[]> {
    const templatesData = await this.learningTemplateModel.findAll();
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 根据主题ID查找学习模板
   * @param themeId 主题ID
   * @returns 学习模板列表
   */
  async findByThemeId(themeId: number): Promise<LearningTemplate[]> {
    const templatesData = await this.learningTemplateModel.findAll({
      where: { theme_id: themeId }
    });
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 根据创建者ID查找学习模板
   * @param creatorId 创建者ID
   * @returns 学习模板列表
   */
  async findByCreatorId(creatorId: string): Promise<LearningTemplate[]> {
    const templatesData = await this.learningTemplateModel.findAll({
      where: { creator_id: creatorId }
    });
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 查找已发布的学习模板
   * @returns 已发布的学习模板列表
   */
  async findPublished(): Promise<LearningTemplate[]> {
    const templatesData = await this.learningTemplateModel.findAll({
      where: { status: 'published' }
    });
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 查找热门学习模板
   * @param limit 限制数量
   * @returns 热门学习模板列表
   */
  async findPopular(limit: number): Promise<LearningTemplate[]> {
    const templatesData = await this.learningTemplateModel.findAll({
      where: { status: 'published' },
      order: [['popularity', 'DESC']],
      limit
    });
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 根据难度查找学习模板
   * @param difficulty 难度级别
   * @returns 学习模板列表
   */
  async findByDifficulty(difficulty: Difficulty): Promise<LearningTemplate[]> {
    const templatesData = await this.learningTemplateModel.findAll({
      where: { difficulty: difficulty.toString() }
    });
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 根据标签ID查找学习模板
   * @param tagId 标签ID
   * @returns 学习模板列表
   */
  async findByTagId(tagId: number): Promise<LearningTemplate[]> {
    const templateTagsData = await this.templateTagModel.findAll({
      where: { tag_id: tagId }
    });
    
    const templateIds = templateTagsData.map(tt => tt.template_id);
    
    if (templateIds.length === 0) return [];
    
    const templatesData = await this.learningTemplateModel.findAll({
      where: { id: { [Op.in]: templateIds } }
    });
    
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 查找官方学习模板
   * @returns 官方学习模板列表
   */
  async findOfficial(): Promise<LearningTemplate[]> {
    const templatesData = await this.learningTemplateModel.findAll({
      where: { is_official: true }
    });
    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 搜索学习模板
   * @param keyword 关键字
   * @param options 搜索选项
   * @returns 学习模板列表
   */
  async search(keyword: string, options?: {
    themeId?: number;
    difficulty?: Difficulty;
    isOfficial?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<LearningTemplate[]> {
    const where: any = {
      [Op.or]: [
        { title: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ]
    };

    if (options?.themeId) {
      where.theme_id = options.themeId;
    }

    if (options?.difficulty) {
      where.difficulty = options.difficulty.toString();
    }

    if (options?.isOfficial !== undefined) {
      where.is_official = options.isOfficial;
    }

    const templatesData = await this.learningTemplateModel.findAll({
      where,
      limit: options?.limit || 20,
      offset: options?.offset || 0,
      order: [['popularity', 'DESC']]
    });

    return templatesData.map(template => this.toDomainModel(template));
  }

  /**
   * 保存学习模板
   * @param learningTemplate 要保存的学习模板
   * @returns 保存后的学习模板
   */
  protected async doSave(learningTemplate: LearningTemplate): Promise<LearningTemplate> {
    const transaction = (this.unitOfWork as any).getTransaction();

    const templateData = {
      theme_id: learningTemplate.themeId,
      title: learningTemplate.title,
      description: learningTemplate.description,
      cover_image_url: learningTemplate.coverImageUrl,
      difficulty: learningTemplate.difficulty.toString(),
      estimated_days: learningTemplate.estimatedDays,
      daily_goal_minutes: learningTemplate.dailyGoalMinutes,
      is_official: learningTemplate.isOfficial,
      creator_id: learningTemplate.creatorId,
      popularity: learningTemplate.popularity,
      rating: learningTemplate.rating.value,
      rating_count: learningTemplate.ratingCount,
      price: learningTemplate.price,
      status: learningTemplate.status.toString(),
      created_at: learningTemplate.createdAt,
      updated_at: learningTemplate.updatedAt,
      deleted_at: learningTemplate.deletedAt
    };

    if (learningTemplate.id === 0) {
      // 创建新学习模板
      const createdTemplate = await this.learningTemplateModel.create(templateData, { transaction });

      // 更新ID
      return new LearningTemplate(
        createdTemplate.id,
        learningTemplate.themeId,
        learningTemplate.title,
        learningTemplate.description,
        learningTemplate.coverImageUrl,
        learningTemplate.difficulty,
        learningTemplate.estimatedDays,
        learningTemplate.dailyGoalMinutes,
        learningTemplate.isOfficial,
        learningTemplate.creatorId,
        learningTemplate.popularity,
        learningTemplate.rating,
        learningTemplate.ratingCount,
        learningTemplate.price,
        learningTemplate.status,
        learningTemplate.createdAt,
        learningTemplate.updatedAt,
        learningTemplate.deletedAt
      );
    } else {
      // 更新现有学习模板
      await this.learningTemplateModel.update(templateData, {
        where: { id: learningTemplate.id },
        transaction
      });

      return learningTemplate;
    }
  }

  /**
   * 删除学习模板
   * @param learningTemplate 要删除的学习模板
   */
  protected async doDelete(learningTemplate: LearningTemplate): Promise<void> {
    const transaction = (this.unitOfWork as any).getTransaction();

    // 软删除
    await this.learningTemplateModel.update({
      deleted_at: new Date(),
      updated_at: new Date()
    }, {
      where: { id: learningTemplate.id },
      transaction
    });
  }

  /**
   * 将数据库模型转换为领域模型
   * @param templateData 数据库模型
   * @returns 领域模型
   */
  private toDomainModel(templateData: any): LearningTemplate {
    return new LearningTemplate(
      templateData.id,
      templateData.theme_id,
      templateData.title,
      templateData.description,
      templateData.cover_image_url,
      Difficulty.fromString(templateData.difficulty),
      templateData.estimated_days,
      templateData.daily_goal_minutes,
      templateData.is_official,
      templateData.creator_id,
      templateData.popularity,
      Rating.create(templateData.rating),
      templateData.rating_count,
      templateData.price,
      TemplateStatus.fromString(templateData.status),
      new Date(templateData.created_at),
      new Date(templateData.updated_at),
      templateData.deleted_at ? new Date(templateData.deleted_at) : null
    );
  }
}
