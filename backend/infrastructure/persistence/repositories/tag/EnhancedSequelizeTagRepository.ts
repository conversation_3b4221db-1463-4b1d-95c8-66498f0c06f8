/**
 * 增强版Sequelize标签仓库
 * 使用增强版仓库基类实现
 */
import { Tag } from '../../../../domain/models/tag/Tag';
import { TagRepository } from '../../../../domain/repositories/tag/TagRepository';
import { EnhancedRepositoryBase } from '../../EnhancedRepositoryBase';
import { UnitOfWork } from '../../../UnitOfWork';
import { EventPublisher } from '../../../events/EventPublisher';
import { Logger } from '../../../logging/Logger';
import { Sequelize, Op } from 'sequelize';
import { AggregateRoot } from '../../../../domain/models/common/AggregateRoot';

/**
 * EnhancedSequelizeTagRepository类
 * 使用Sequelize实现的增强版标签仓库
 */
export class EnhancedSequelizeTagRepository extends EnhancedRepositoryBase<Tag, number> implements TagRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param logger 日志记录器
   * @param sequelize Sequelize实例
   * @param tagModel 标签模型
   * @param tagSynonymModel 标签同义词模型
   * @param tagFeedbackModel 标签反馈模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    logger: Logger,
    private readonly sequelize: Sequelize,
    private readonly tagModel: any,
    private readonly tagSynonymModel: any,
    private readonly tagFeedbackModel: any
  ) {
    super(unitOfWork, eventPublisher, logger);
  }

  /**
   * 根据ID查找标签
   * @param id 标签ID
   * @returns 如果找到标签，则返回标签，否则返回null
   */
  async findById(id: number): Promise<Tag | null> {
    this.logger.debug(`查找标签: ID=${id}`);
    
    const tagData = await this.tagModel.findByPk(id, {
      transaction: this.getTransaction()
    });
    
    if (!tagData) return null;
    
    return this.toDomainModel(tagData);
  }

  /**
   * 根据名称查找标签
   * @param name 标签名称
   * @returns 如果找到标签，则返回标签，否则返回null
   */
  async findByName(name: string): Promise<Tag | null> {
    this.logger.debug(`根据名称查找标签: name=${name}`);
    
    const tagData = await this.tagModel.findOne({
      where: { name },
      transaction: this.getTransaction()
    });
    
    if (!tagData) return null;
    
    return this.toDomainModel(tagData);
  }

  /**
   * 根据分类ID查找标签
   * @param categoryId 分类ID
   * @returns 标签列表
   */
  async findByCategoryId(categoryId: number): Promise<Tag[]> {
    this.logger.debug(`根据分类ID查找标签: categoryId=${categoryId}`);
    
    const tagData = await this.tagModel.findAll({
      where: { category_id: categoryId },
      transaction: this.getTransaction()
    });
    
    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 根据热度查找标签
   * @param minPopularity 最小热度
   * @param limit 返回的最大数量
   * @returns 标签列表
   */
  async findByPopularity(minPopularity: number, limit?: number): Promise<Tag[]> {
    this.logger.debug(`根据热度查找标签: minPopularity=${minPopularity}, limit=${limit}`);
    
    const tagData = await this.tagModel.findAll({
      where: { 
        // 使用usage_count作为热度指标
        usage_count: { [Op.gte]: minPopularity } 
      },
      order: [['usage_count', 'DESC']],
      limit: limit,
      transaction: this.getTransaction()
    });
    
    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 查找标签及其同义词
   * @param tagId 标签ID
   * @returns 如果找到标签，则返回标签及其同义词，否则返回null
   */
  async findWithSynonyms(tagId: number): Promise<Tag | null> {
    this.logger.debug(`查找标签及其同义词: tagId=${tagId}`);
    
    const tagData = await this.tagModel.findByPk(tagId, {
      include: [
        {
          model: this.tagSynonymModel,
          as: 'synonyms'
        }
      ],
      transaction: this.getTransaction()
    });
    
    if (!tagData) return null;
    
    return this.toDomainModelWithRelations(tagData);
  }

  /**
   * 查找标签及其反馈
   * @param tagId 标签ID
   * @returns 如果找到标签，则返回标签及其反馈，否则返回null
   */
  async findWithFeedbacks(tagId: number): Promise<Tag | null> {
    this.logger.debug(`查找标签及其反馈: tagId=${tagId}`);
    
    const tagData = await this.tagModel.findByPk(tagId, {
      include: [
        {
          model: this.tagFeedbackModel,
          as: 'feedbacks'
        }
      ],
      transaction: this.getTransaction()
    });
    
    if (!tagData) return null;
    
    return this.toDomainModelWithRelations(tagData);
  }

  /**
   * 根据关键词搜索标签
   * @param keyword 关键词
   * @param limit 返回的最大数量
   * @returns 标签列表
   */
  async searchByKeyword(keyword: string, limit?: number): Promise<Tag[]> {
    this.logger.debug(`根据关键词搜索标签: keyword=${keyword}, limit=${limit}`);
    
    const tagData = await this.tagModel.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ]
      },
      limit: limit,
      transaction: this.getTransaction()
    });
    
    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 查找所有标签
   * @returns 标签列表
   */
  async findAll(): Promise<Tag[]> {
    this.logger.debug('查找所有标签');
    
    const tagData = await this.tagModel.findAll({
      transaction: this.getTransaction()
    });
    
    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 根据条件查询标签
   * @param criteria 查询条件
   * @returns 符合条件的标签列表
   */
  async findByCriteria(criteria: any): Promise<Tag[]> {
    this.logger.debug(`根据条件查询标签: ${JSON.stringify(criteria)}`);
    
    const tagData = await this.tagModel.findAll({
      where: criteria,
      transaction: this.getTransaction()
    });
    
    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 检查仓库是否支持指定的实体类型
   * @param entity 要检查的实体
   * @returns 是否支持
   */
  supports(entity: AggregateRoot): boolean {
    return entity instanceof Tag;
  }

  /**
   * 实际保存标签的方法
   * @param tag 要保存的标签
   * @returns 保存后的标签
   */
  protected async doSave(tag: Tag): Promise<Tag> {
    const tagData = this.toDataModel(tag);
    
    if (tag.id === 0) {
      // 创建新标签
      const createdTag = await this.tagModel.create(tagData, {
        transaction: this.getTransaction()
      });
      
      return this.toDomainModel({
        ...createdTag.get({ plain: true }),
        synonyms: []
      });
    } else {
      // 更新现有标签
      await this.tagModel.update(tagData, {
        where: { id: tag.id },
        transaction: this.getTransaction()
      });
      
      // 处理同义词
      if (tag.synonyms && tag.synonyms.length > 0) {
        // 先删除现有同义词
        await this.tagSynonymModel.destroy({
          where: { primary_tag_id: tag.id },
          transaction: this.getTransaction()
        });
        
        // 添加新同义词
        for (const synonym of tag.synonyms) {
          await this.tagSynonymModel.create({
            primary_tag_id: tag.id,
            synonym_name: synonym
          }, {
            transaction: this.getTransaction()
          });
        }
      }
      
      return this.findById(tag.id) as Promise<Tag>;
    }
  }

  /**
   * 实际删除标签的方法
   * @param tag 要删除的标签
   */
  protected async doDelete(tag: Tag): Promise<void> {
    if (tag.isDeleted) {
      // 物理删除
      await this.tagModel.destroy({
        where: { id: tag.id },
        force: true,
        transaction: this.getTransaction()
      });
    } else {
      // 软删除
      await this.tagModel.destroy({
        where: { id: tag.id },
        transaction: this.getTransaction()
      });
    }
  }

  /**
   * 将数据模型转换为领域模型
   * @param data 数据模型
   * @returns 领域模型
   */
  private toDomainModel(data: any): Tag {
    return new Tag(
      data.id,
      data.name,
      data.category_id,
      data.description || '',
      data.creator_id?.toString() || '0',
      data.usage_count || 0,
      new Date(data.created_at),
      new Date(data.updated_at),
      data.deleted_at ? new Date(data.deleted_at) : null
    );
  }

  /**
   * 将数据模型及其关联转换为领域模型
   * @param data 数据模型及其关联
   * @returns 领域模型
   */
  private toDomainModelWithRelations(data: any): Tag {
    const tag = this.toDomainModel(data);
    
    // 处理同义词
    if (data.synonyms) {
      data.synonyms.forEach((synonym: any) => {
        tag.addSynonym(synonym.synonym_name);
      });
    }
    
    return tag;
  }

  /**
   * 将领域模型转换为数据模型
   * @param tag 领域模型
   * @returns 数据模型
   */
  private toDataModel(tag: Tag): any {
    return {
      id: tag.id === 0 ? undefined : tag.id,
      name: tag.name,
      category_id: tag.categoryId,
      description: tag.description,
      creator_id: tag.creatorId,
      usage_count: tag.popularity,
      created_at: tag.createdAt,
      updated_at: tag.updatedAt,
      deleted_at: tag.deletedAt
    };
  }
}
