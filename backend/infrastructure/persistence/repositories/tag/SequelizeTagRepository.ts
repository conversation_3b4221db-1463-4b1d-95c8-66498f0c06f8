import { Tag } from '../../../../domain/models/tag/Tag';
import { TagRepository } from '../../../../domain/repositories/tag/TagRepository';
import { RepositoryBase } from '../RepositoryBase';
import { UnitOfWork } from '../../UnitOfWork';
import { EventPublisher } from '../../../events/EventPublisher';
import { Sequelize, Op } from 'sequelize';

/**
 * SequelizeTagRepository类
 * 使用Sequelize实现的标签仓库
 */
export class SequelizeTagRepository extends RepositoryBase<Tag, number> implements TagRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param sequelize Sequelize实例
   * @param tagModel 标签模型
   * @param tagSynonymModel 标签同义词模型
   * @param tagFeedbackModel 标签反馈模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    private readonly sequelize: Sequelize,
    private readonly tagModel: any,
    private readonly tagSynonymModel: any,
    private readonly tagFeedbackModel: any
  ) {
    super(unitOfWork, eventPublisher);
  }

  /**
   * 根据ID查找标签
   * @param id 标签ID
   * @returns 如果找到标签，则返回标签，否则返回null
   */
  async findById(id: number): Promise<Tag | null> {
    const tagData = await this.tagModel.findByPk(id);
    if (!tagData) return null;

    return this.toDomainModel(tagData);
  }

  /**
   * 根据名称查找标签
   * @param name 标签名称
   * @returns 如果找到标签，则返回标签，否则返回null
   */
  async findByName(name: string): Promise<Tag | null> {
    const tagData = await this.tagModel.findOne({
      where: { name }
    });

    if (!tagData) return null;

    return this.toDomainModel(tagData);
  }

  /**
   * 根据分类ID查找标签
   * @param categoryId 分类ID
   * @returns 标签列表
   */
  async findByCategoryId(categoryId: number): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll({
      where: { category_id: categoryId }
    });

    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 根据热度查找标签
   * @param minPopularity 最小热度
   * @param limit 返回的最大数量
   * @returns 标签列表
   */
  async findByPopularity(minPopularity: number, limit?: number): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll({
      where: { popularity: { [Op.gte]: minPopularity } },
      order: [['popularity', 'DESC']],
      limit: limit
    });

    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 查找标签及其同义词
   * @param tagId 标签ID
   * @returns 如果找到标签，则返回标签及其同义词，否则返回null
   */
  async findWithSynonyms(tagId: number): Promise<Tag | null> {
    const tagData = await this.tagModel.findByPk(tagId, {
      include: [
        {
          model: this.tagSynonymModel,
          as: 'synonyms'
        }
      ]
    });

    if (!tagData) return null;

    return this.toDomainModelWithRelations(tagData);
  }

  /**
   * 查找标签及其反馈
   * @param tagId 标签ID
   * @returns 如果找到标签，则返回标签及其反馈，否则返回null
   */
  async findWithFeedbacks(tagId: number): Promise<Tag | null> {
    const tagData = await this.tagModel.findByPk(tagId, {
      include: [
        {
          model: this.tagFeedbackModel,
          as: 'feedbacks'
        }
      ]
    });

    if (!tagData) return null;

    return this.toDomainModelWithRelations(tagData);
  }

  /**
   * 查找所有标签
   * @returns 标签列表
   */
  async findAll(): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll();
    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 根据关键字搜索标签
   * @param keyword 关键字
   * @param limit 返回的最大数量
   * @returns 标签列表
   */
  async searchByKeyword(keyword: string, limit?: number): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ]
      },
      limit: limit
    });

    return tagData.map(tag => this.toDomainModel(tag));
  }

  /**
   * 保存标签
   * @param entity 要保存的标签
   * @returns 保存后的标签
   */
  protected async doSave(tag: Tag): Promise<Tag> {
    const transaction = (this.unitOfWork as any).getTransaction();

    if (tag.id === 0) {
      // 创建新标签
      const tagData = await this.tagModel.create({
        name: tag.name,
        category_id: tag.categoryId,
        description: tag.description,
        creator_id: tag.creatorId,
        popularity: tag.popularity,
        created_at: tag.createdAt,
        updated_at: tag.updatedAt,
        deleted_at: tag.deletedAt
      }, { transaction });

      // 更新ID
      return new Tag(
        tagData.id,
        tag.name,
        tag.categoryId,
        tag.description,
        tag.creatorId,
        tag.popularity,
        tag.createdAt,
        tag.updatedAt,
        tag.deletedAt
      );
    } else {
      // 更新现有标签
      await this.tagModel.update({
        name: tag.name,
        category_id: tag.categoryId,
        description: tag.description,
        popularity: tag.popularity,
        updated_at: tag.updatedAt,
        deleted_at: tag.deletedAt
      }, {
        where: { id: tag.id },
        transaction
      });

      return tag;
    }
  }

  /**
   * 删除标签
   * @param entity 要删除的标签
   */
  protected async doDelete(tag: Tag): Promise<void> {
    const transaction = (this.unitOfWork as any).getTransaction();

    // 软删除
    await this.tagModel.update({
      deleted_at: new Date(),
      updated_at: new Date()
    }, {
      where: { id: tag.id },
      transaction
    });
  }

  /**
   * 将数据库模型转换为领域模型
   * @param tagData 数据库模型
   * @returns 领域模型
   */
  private toDomainModel(tagData: any): Tag {
    return new Tag(
      tagData.id,
      tagData.name,
      tagData.category_id,
      tagData.description || '',
      tagData.creator_id,
      tagData.popularity || 0,
      tagData.created_at,
      tagData.updated_at,
      tagData.deleted_at
    );
  }

  /**
   * 将数据库模型及其关联转换为领域模型
   * @param tagData 数据库模型及其关联
   * @returns 领域模型
   */
  private toDomainModelWithRelations(tagData: any): Tag {
    const tag = this.toDomainModel(tagData);

    // 添加同义词
    if (tagData.synonyms) {
      for (const synonymData of tagData.synonyms) {
        tag.addSynonym(synonymData.synonym_name);
      }
    }

    return tag;
  }
}
