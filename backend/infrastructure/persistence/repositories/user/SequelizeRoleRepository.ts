import { Role } from '../../../../domain/models/user/Role';
import { RoleRepository } from '../../../../domain/repositories/user/RoleRepository';
import { RepositoryBase } from '../RepositoryBase';
import { UnitOfWork } from '../../UnitOfWork';
import { EventPublisher } from '../../../events/EventPublisher';
import { Sequelize } from 'sequelize';
import { Permission } from '../../../../domain/models/user/Permission';

/**
 * SequelizeRoleRepository类
 * 使用Sequelize实现的角色仓库
 */
export class SequelizeRoleRepository extends RepositoryBase<Role, number> implements RoleRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param sequelize Sequelize实例
   * @param roleModel 角色模型
   * @param permissionModel 权限模型
   * @param rolePermissionModel 角色权限关联模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    private readonly sequelize: Sequelize,
    private readonly roleModel: any,
    private readonly permissionModel: any,
    private readonly rolePermissionModel: any
  ) {
    super(unitOfWork, eventPublisher);
  }

  /**
   * 根据ID查找角色
   * @param id 角色ID
   * @returns 如果找到角色，则返回角色，否则返回null
   */
  async findById(id: number): Promise<Role | null> {
    const roleData = await this.roleModel.findByPk(id);
    if (!roleData) return null;

    return this.toDomainModel(roleData);
  }

  /**
   * 根据名称查找角色
   * @param name 角色名称
   * @returns 如果找到角色，则返回角色，否则返回null
   */
  async findByName(name: string): Promise<Role | null> {
    const roleData = await this.roleModel.findOne({
      where: { name }
    });

    if (!roleData) return null;

    return this.toDomainModel(roleData);
  }

  /**
   * 查找所有角色及其权限
   * @returns 角色列表
   */
  async findWithPermissions(): Promise<Role[]> {
    const rolesData = await this.roleModel.findAll({
      include: [
        {
          model: this.permissionModel,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    });

    return rolesData.map(roleData => this.toDomainModelWithPermissions(roleData));
  }

  /**
   * 根据ID查找角色及其权限
   * @param id 角色ID
   * @returns 如果找到角色，则返回角色，否则返回null
   */
  async findWithPermissionsById(id: number): Promise<Role | null> {
    const roleData = await this.roleModel.findByPk(id, {
      include: [
        {
          model: this.permissionModel,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    });

    if (!roleData) return null;

    return this.toDomainModelWithPermissions(roleData);
  }

  /**
   * 查找所有角色
   * @returns 角色列表
   */
  async findAll(): Promise<Role[]> {
    const rolesData = await this.roleModel.findAll();
    return rolesData.map(roleData => this.toDomainModel(roleData));
  }

  /**
   * 保存角色
   * @param role 要保存的角色
   * @returns 保存后的角色
   */
  protected async doSave(role: Role): Promise<Role> {
    const transaction = (this.unitOfWork as any).getTransaction();

    if (role.id === 0) {
      // 创建新角色
      const roleData = await this.roleModel.create({
        name: role.name,
        description: role.description,
        created_at: new Date(),
        updated_at: new Date()
      }, { transaction });

      // 创建角色权限关联
      for (const permission of role.permissions) {
        await this.rolePermissionModel.create({
          role_id: roleData.id,
          permission_id: permission.id,
          created_at: new Date(),
          updated_at: new Date()
        }, { transaction });
      }

      // 返回带有新ID的角色
      return this.findById(roleData.id) as Promise<Role>;
    } else {
      // 更新现有角色
      await this.roleModel.update({
        name: role.name,
        description: role.description,
        updated_at: new Date()
      }, {
        where: { id: role.id },
        transaction
      });

      // 更新角色权限关联
      await this.rolePermissionModel.destroy({
        where: { role_id: role.id },
        transaction
      });

      for (const permission of role.permissions) {
        await this.rolePermissionModel.create({
          role_id: role.id,
          permission_id: permission.id,
          created_at: new Date(),
          updated_at: new Date()
        }, { transaction });
      }

      return role;
    }
  }

  /**
   * 删除角色
   * @param role 要删除的角色
   */
  protected async doDelete(role: Role): Promise<void> {
    const transaction = (this.unitOfWork as any).getTransaction();

    // 删除角色权限关联
    await this.rolePermissionModel.destroy({
      where: { role_id: role.id },
      transaction
    });

    // 删除角色
    await this.roleModel.destroy({
      where: { id: role.id },
      transaction
    });
  }

  /**
   * 将数据库模型转换为领域模型
   * @param roleData 数据库模型
   * @returns 领域模型
   */
  private toDomainModel(roleData: any): Role {
    return new Role(
      roleData.id,
      roleData.name,
      roleData.description,
      []
    );
  }

  /**
   * 将数据库模型转换为带权限的领域模型
   * @param roleData 数据库模型
   * @returns 领域模型
   */
  private toDomainModelWithPermissions(roleData: any): Role {
    const permissions = roleData.permissions
      ? roleData.permissions.map((permissionData: any) => new Permission(
          permissionData.id,
          permissionData.name,
          permissionData.description,
          permissionData.resource_type,
          permissionData.action
        ))
      : [];

    return new Role(
      roleData.id,
      roleData.name,
      roleData.description,
      permissions
    );
  }
}
