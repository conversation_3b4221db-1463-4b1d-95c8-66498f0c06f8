import { User } from '../../../../domain/models/user/User';
import { UserRepository } from '../../../../domain/repositories/user/UserRepository';
import { RepositoryBase } from '../RepositoryBase';
import { UnitOfWork } from '../../UnitOfWork';
import { EventPublisher } from '../../../events/EventPublisher';
import { Sequelize, Op } from 'sequelize';
import { Email } from '../../../../domain/models/user/Email';
import { PhoneNumber } from '../../../../domain/models/user/PhoneNumber';
import { Password } from '../../../../domain/models/user/Password';
import { Gender } from '../../../../domain/models/user/Gender';
import { UserStatus } from '../../../../domain/models/user/UserStatus';
import { Role } from '../../../../domain/models/user/Role';
import { UserSetting } from '../../../../domain/models/user/UserSetting';
import { UserNotificationSetting } from '../../../../domain/models/user/UserNotificationSetting';
import { NotificationType } from '../../../../domain/models/user/NotificationType';
import { NotificationChannel } from '../../../../domain/models/user/NotificationChannel';
import { PrivacySettings } from '../../../../domain/models/user/PrivacySettings';

/**
 * SequelizeUserRepository类
 * 使用Sequelize实现的用户仓库
 */
export class SequelizeUserRepository extends RepositoryBase<User, number> implements UserRepository {
  /**
   * 构造函数
   * @param unitOfWork 工作单元
   * @param eventPublisher 事件发布者
   * @param sequelize Sequelize实例
   * @param userModel 用户模型
   * @param userSettingModel 用户设置模型
   * @param userNotificationSettingModel 用户通知设置模型
   * @param roleModel 角色模型
   * @param userRoleModel 用户角色关联模型
   */
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    private readonly sequelize: Sequelize,
    private readonly userModel: any,
    private readonly userSettingModel: any,
    private readonly userNotificationSettingModel: any,
    private readonly roleModel: any,
    private readonly userRoleModel: any
  ) {
    super(unitOfWork, eventPublisher);
  }

  /**
   * 根据ID查找用户
   * @param id 用户ID
   * @returns 如果找到用户，则返回用户，否则返回null
   */
  async findById(id: number): Promise<User | null> {
    const userData = await this.userModel.findByPk(id, {
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    if (!userData) return null;

    return this.toDomainModel(userData);
  }

  /**
   * 根据用户名查找用户
   * @param username 用户名
   * @returns 如果找到用户，则返回用户，否则返回null
   */
  async findByUsername(username: string): Promise<User | null> {
    const userData = await this.userModel.findOne({
      where: { username },
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    if (!userData) return null;

    return this.toDomainModel(userData);
  }

  /**
   * 根据邮箱查找用户
   * @param email 邮箱
   * @returns 如果找到用户，则返回用户，否则返回null
   */
  async findByEmail(email: string): Promise<User | null> {
    const userData = await this.userModel.findOne({
      where: { email },
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    if (!userData) return null;

    return this.toDomainModel(userData);
  }

  /**
   * 根据手机号查找用户
   * @param phoneNumber 手机号
   * @returns 如果找到用户，则返回用户，否则返回null
   */
  async findByPhoneNumber(phoneNumber: string): Promise<User | null> {
    const userData = await this.userModel.findOne({
      where: { phone_number: phoneNumber },
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    if (!userData) return null;

    return this.toDomainModel(userData);
  }

  /**
   * 根据微信OpenID查找用户
   * @param wechatOpenId 微信OpenID
   * @returns 如果找到用户，则返回用户，否则返回null
   */
  async findByWechatOpenId(wechatOpenId: string): Promise<User | null> {
    const userData = await this.userModel.findOne({
      where: { wechat_open_id: wechatOpenId },
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    if (!userData) return null;

    return this.toDomainModel(userData);
  }

  /**
   * 分页查找用户
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 用户列表和总数
   */
  async findWithPagination(page: number, pageSize: number): Promise<{ items: User[], total: number }> {
    const offset = (page - 1) * pageSize;
    const { count, rows } = await this.userModel.findAndCountAll({
      limit: pageSize,
      offset,
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    const users = rows.map(userData => this.toDomainModel(userData));
    return { items: users, total: count };
  }

  /**
   * 根据角色查找用户
   * @param roleId 角色ID
   * @returns 用户列表
   */
  async findByRole(roleId: number): Promise<User[]> {
    const usersData = await this.userModel.findAll({
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] },
          where: { id: roleId }
        }
      ]
    });

    return usersData.map(userData => this.toDomainModel(userData));
  }

  /**
   * 查找所有用户
   * @returns 用户列表
   */
  async findAll(): Promise<User[]> {
    const usersData = await this.userModel.findAll({
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    return usersData.map(userData => this.toDomainModel(userData));
  }

  /**
   * 查找已删除的用户
   * @returns 已删除的用户列表
   */
  async findDeleted(): Promise<User[]> {
    const usersData = await this.userModel.findAll({
      where: { deleted_at: { [Op.not]: null } },
      paranoid: false,
      include: [
        {
          model: this.userSettingModel,
          as: 'userSetting',
          include: [
            {
              model: this.userNotificationSettingModel,
              as: 'notificationSettings'
            }
          ]
        },
        {
          model: this.roleModel,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    return usersData.map(userData => this.toDomainModel(userData));
  }

  /**
   * 恢复已删除的用户
   * @param id 用户ID
   * @returns 恢复后的用户或null
   */
  async restore(id: number): Promise<User | null> {
    await this.userModel.restore({
      where: { id }
    });

    return this.findById(id);
  }

  /**
   * 保存用户
   * @param user 要保存的用户
   * @returns 保存后的用户
   */
  protected async doSave(user: User): Promise<User> {
    const transaction = (this.unitOfWork as any).getTransaction();

    if (user.id === 0) {
      // 创建新用户
      const userData = await this.userModel.create({
        username: user.username,
        nickname: user.nickname,
        email: user.email ? user.email.value : null,
        email_verified: user.email ? user.email.isVerified : false,
        phone_number: user.phoneNumber ? user.phoneNumber.value : null,
        phone_verified: user.phoneNumber ? user.phoneNumber.isVerified : false,
        password_hash: user.password ? user.password.hash : null,
        password_salt: user.password ? user.password.salt : null,
        wechat_open_id: user.wechatOpenId,
        avatar: user.avatar,
        gender: user.gender.value,
        birthday: user.birthday,
        status: user.status.value,
        registered_at: user.registeredAt,
        last_login_at: user.lastLoginAt,
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: user.deletedAt
      }, { transaction });

      // 创建用户设置
      const userSettingData = await this.userSettingModel.create({
        user_id: userData.id,
        theme: user.userSetting.theme,
        font_size: user.userSetting.fontSize,
        daily_goal_minutes: user.userSetting.dailyGoalMinutes,
        reminder_time: user.userSetting.reminderTime,
        privacy_settings: JSON.stringify(user.userSetting.privacySettings.toJSON()),
        created_at: new Date(),
        updated_at: new Date()
      }, { transaction });

      // 创建用户通知设置
      for (const notificationSetting of user.userSetting.notificationSettings) {
        await this.userNotificationSettingModel.create({
          user_setting_id: userSettingData.id,
          notification_type: notificationSetting.notificationType.value,
          notification_channel: notificationSetting.notificationChannel.value,
          enabled: notificationSetting.enabled,
          created_at: new Date(),
          updated_at: new Date()
        }, { transaction });
      }

      // 创建用户角色关联
      for (const role of user.roles) {
        await this.userRoleModel.create({
          user_id: userData.id,
          role_id: role.id,
          created_at: new Date(),
          updated_at: new Date()
        }, { transaction });
      }

      // 返回带有新ID的用户
      return this.findById(userData.id) as Promise<User>;
    } else {
      // 更新现有用户
      await this.userModel.update({
        username: user.username,
        nickname: user.nickname,
        email: user.email ? user.email.value : null,
        email_verified: user.email ? user.email.isVerified : false,
        phone_number: user.phoneNumber ? user.phoneNumber.value : null,
        phone_verified: user.phoneNumber ? user.phoneNumber.isVerified : false,
        password_hash: user.password ? user.password.hash : null,
        password_salt: user.password ? user.password.salt : null,
        wechat_open_id: user.wechatOpenId,
        avatar: user.avatar,
        gender: user.gender.value,
        birthday: user.birthday,
        status: user.status.value,
        last_login_at: user.lastLoginAt,
        updated_at: new Date(),
        deleted_at: user.deletedAt
      }, {
        where: { id: user.id },
        transaction
      });

      // 更新用户设置
      await this.userSettingModel.update({
        theme: user.userSetting.theme,
        font_size: user.userSetting.fontSize,
        daily_goal_minutes: user.userSetting.dailyGoalMinutes,
        reminder_time: user.userSetting.reminderTime,
        privacy_settings: JSON.stringify(user.userSetting.privacySettings.toJSON()),
        updated_at: new Date()
      }, {
        where: { user_id: user.id },
        transaction
      });

      // 获取用户设置ID
      const userSettingData = await this.userSettingModel.findOne({
        where: { user_id: user.id },
        transaction
      });

      // 删除现有通知设置
      await this.userNotificationSettingModel.destroy({
        where: { user_setting_id: userSettingData.id },
        transaction
      });

      // 创建新通知设置
      for (const notificationSetting of user.userSetting.notificationSettings) {
        await this.userNotificationSettingModel.create({
          user_setting_id: userSettingData.id,
          notification_type: notificationSetting.notificationType.value,
          notification_channel: notificationSetting.notificationChannel.value,
          enabled: notificationSetting.enabled,
          created_at: new Date(),
          updated_at: new Date()
        }, { transaction });
      }

      // 更新用户角色关联
      await this.userRoleModel.destroy({
        where: { user_id: user.id },
        transaction
      });

      for (const role of user.roles) {
        await this.userRoleModel.create({
          user_id: user.id,
          role_id: role.id,
          created_at: new Date(),
          updated_at: new Date()
        }, { transaction });
      }

      return user;
    }
  }

  /**
   * 删除用户
   * @param user 要删除的用户
   */
  protected async doDelete(user: User): Promise<void> {
    const transaction = (this.unitOfWork as any).getTransaction();

    // 软删除用户
    await this.userModel.update({
      deleted_at: new Date(),
      updated_at: new Date()
    }, {
      where: { id: user.id },
      transaction
    });
  }

  /**
   * 将数据库模型转换为领域模型
   * @param userData 数据库模型
   * @returns 领域模型
   */
  private toDomainModel(userData: any): User {
    // 创建Email值对象
    const email = userData.email
      ? new Email(userData.email, userData.email_verified)
      : null;

    // 创建PhoneNumber值对象
    const phoneNumber = userData.phone_number
      ? new PhoneNumber(userData.phone_number, userData.phone_verified)
      : null;

    // 创建Password值对象
    const password = userData.password_hash
      ? new Password(userData.password_hash, userData.password_salt)
      : null;

    // 创建Gender值对象
    const gender = Gender.fromString(userData.gender);

    // 创建UserStatus值对象
    const status = UserStatus.fromString(userData.status);

    // 创建Role实体
    const roles = userData.roles
      ? userData.roles.map((roleData: any) => new Role(
          roleData.id,
          roleData.name,
          roleData.description,
          []
        ))
      : [];

    // 创建UserNotificationSetting实体
    const notificationSettings = userData.userSetting && userData.userSetting.notificationSettings
      ? userData.userSetting.notificationSettings.map((settingData: any) => new UserNotificationSetting(
          settingData.id,
          NotificationType.fromString(settingData.notification_type),
          NotificationChannel.fromString(settingData.notification_channel),
          settingData.enabled
        ))
      : [];

    // 创建PrivacySettings值对象
    const privacySettings = userData.userSetting && userData.userSetting.privacy_settings
      ? PrivacySettings.fromJSON(JSON.parse(userData.userSetting.privacy_settings))
      : PrivacySettings.createDefault();

    // 创建UserSetting实体
    const userSetting = userData.userSetting
      ? new UserSetting(
          userData.userSetting.id,
          userData.id,
          userData.userSetting.theme,
          userData.userSetting.font_size,
          userData.userSetting.daily_goal_minutes,
          userData.userSetting.reminder_time,
          privacySettings,
          notificationSettings
        )
      : UserSetting.createDefault(0, userData.id);

    // 创建User实体
    return new User(
      userData.id,
      userData.username,
      userData.nickname,
      email,
      phoneNumber,
      password,
      userData.wechat_open_id,
      userData.avatar,
      gender,
      userData.birthday,
      status,
      roles,
      userData.registered_at,
      userData.last_login_at,
      userSetting
    );
  }
}
