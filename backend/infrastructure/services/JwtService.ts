/**
 * JWT服务
 * 用于生成和验证JWT令牌
 */
import jwt from 'jsonwebtoken';

/**
 * JWT负载接口
 */
export interface JwtPayload {
  userId: number;
  username: string;
  roles: string[];
  tokenId: number;
  [key: string]: any;
}

/**
 * JWT服务类
 */
export class JwtService {
  /**
   * 构造函数
   * @param secret JWT密钥
   * @param expiresIn JWT过期时间
   */
  constructor(
    private readonly secret: string,
    private readonly expiresIn: string = '24h'
  ) {}

  /**
   * 生成JWT令牌
   * @param payload JWT负载
   * @returns JWT令牌
   */
  generateToken(payload: JwtPayload): string {
    return jwt.sign(payload, this.secret, { expiresIn: this.expiresIn });
  }

  /**
   * 验证JWT令牌
   * @param token JWT令牌
   * @returns JWT负载或null
   */
  verifyToken(token: string): JwtPayload | null {
    try {
      return jwt.verify(token, this.secret) as JwtPayload;
    } catch (error) {
      return null;
    }
  }

  /**
   * 解码JWT令牌
   * @param token JWT令牌
   * @returns JWT负载或null
   */
  decodeToken(token: string): JwtPayload | null {
    try {
      return jwt.decode(token) as JwtPayload;
    } catch (error) {
      return null;
    }
  }

  /**
   * 刷新JWT令牌
   * @param token JWT令牌
   * @returns 新的JWT令牌或null
   */
  refreshToken(token: string): string | null {
    const payload = this.verifyToken(token);
    if (!payload) {
      return null;
    }

    // 删除过期时间
    delete payload.exp;
    delete payload.iat;

    // 生成新令牌
    return this.generateToken(payload);
  }
}
