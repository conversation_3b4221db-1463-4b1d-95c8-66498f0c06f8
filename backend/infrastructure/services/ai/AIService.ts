/**
 * AI服务
 * 处理与AI API的交互
 */
import axios from 'axios';
import OpenAI from 'openai';
import { Logger } from '../../logging/Logger';
import { AppError } from '../../../utils/errorHandler';

/**
 * AI使用统计信息接口
 */
export interface AIUsageStats {
  requestCount: number;
  errorCount: number;
  tokenUsage: {
    prompt: number;
    completion: number;
    total: number;
  };
  errorRate: number;
}

/**
 * AI响应接口
 */
export interface AIResponse {
  choices: {
    message: {
      content: string;
      role?: string;
      thinking_content?: string;
      function_call?: any;
    };
    finish_reason?: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * 标签对象接口
 */
export interface Tag {
  name: string;
  relevanceScore: number;
  sortOrder: number;
}

/**
 * 学习计划内容接口
 */
export interface LearningPlanContent {
  enhancedTitle: string;
  designPrinciple: string;
  contentPlan: {
    day: number;
    title: string;
    content: string;
  }[];
  tags: Tag[];
}

/**
 * 学习计划数据接口
 */
export interface LearningPlanData {
  title: string;
  trouble?: string;
  learningIntensity?: string;
  learningDuration?: number;
  theme?: {
    name?: string;
  };
}

/**
 * AI服务类
 */
export class AIService {
  private openai: OpenAI;
  private requestCount: number;
  private errorCount: number;
  private tokenUsage: {
    prompt: number;
    completion: number;
    total: number;
  };
  private lastRequestTime: number;

  /**
   * 构造函数
   * @param logger 日志记录器
   * @param config 配置对象
   */
  constructor(
    private readonly logger: Logger,
    private readonly config: any,
    private readonly createError: (message: string, code: string, statusCode: number) => AppError
  ) {
    // 验证AI配置
    try {
      this.config.ai.validateConfig();
    } catch (error) {
      this.logger.error(`AI配置验证失败: ${error.message}`);
    }

    // 打印API配置信息，用于调试
    this.logger.debug(`AI配置信息:
    - Provider: ${this.config.ai.provider}
    - 字节大模型 API Key: ${process.env.ARK_API_KEY ? '已设置' : '未设置'}
    - 阿里云百炼 API Key: ${process.env.DASHSCOPE_API_KEY ? '已设置' : '未设置'}
    - 腾讯混元 API Key: ${process.env.HUNYUAN_API_KEY ? '已设置' : '未设置'}`);

    this.provider = this.config.ai.provider;

    // 根据提供商设置API配置
    if (this.provider === 'bytedance') {
      // 使用环境变量中的ARK_API_KEY
      // 直接初始化字节大模型OpenAI兼容客户端
      this.openai = new OpenAI({
        apiKey: process.env.ARK_API_KEY,
        baseURL: 'https://ark.cn-beijing.volces.com/api/v3'
      });

      // 从环境变量中读取模型ID
      this.model = process.env.ARK_API_MODEL || 'deepseek-r1-250120';

    } else if (this.provider === 'aliyun') {
      // 使用环境变量中的DASHSCOPE_API_KEY
      // 直接初始化阿里云百炼OpenAI兼容客户端
      this.openai = new OpenAI({
        apiKey: process.env.DASHSCOPE_API_KEY,
        baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
      });

      // 从环境变量中读取模型ID和思考模式设置
      this.model = process.env.DASHSCOPE_API_MODEL || 'qwen-plus-latest';
      this.enableThinking = process.env.DASHSCOPE_ENABLE_THINKING === 'true';

    } else if (this.provider === 'hunyuan') {
      // 使用环境变量中的HUNYUAN_API_KEY
      // 直接初始化腾讯混元OpenAI兼容客户端
      this.openai = new OpenAI({
        apiKey: process.env.HUNYUAN_API_KEY,
        baseURL: 'https://api.hunyuan.cloud.tencent.com/v1'
      });

      // 从环境变量中读取模型ID
      this.model = process.env.HUNYUAN_API_MODEL || 'hunyuan-turbos-latest';

    } else {
      this.logger.error(`不支持的AI提供商: ${this.provider}`);
      throw this.createError(`不支持的AI提供商: ${this.provider}`, 'INVALID_AI_PROVIDER', 500);
    }

    // 请求计数器和错误计数器（用于监控）
    this.requestCount = 0;
    this.errorCount = 0;
    this.tokenUsage = {
      prompt: 0,
      completion: 0,
      total: 0
    };

    // 上次请求时间（用于限流）
    this.lastRequestTime = 0;
  }

  /**
   * 获取使用统计信息
   * @returns 使用统计信息
   */
  getUsageStats(): AIUsageStats {
    return {
      requestCount: this.requestCount,
      errorCount: this.errorCount,
      tokenUsage: this.tokenUsage,
      errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount) : 0
    };
  }

  /**
   * 重置使用统计信息
   */
  resetUsageStats(): void {
    this.requestCount = 0;
    this.errorCount = 0;
    this.tokenUsage = {
      prompt: 0,
      completion: 0,
      total: 0
    };
    this.logger.info('AI使用统计已重置');
  }

  /**
   * 生成学习计划的标签
   * @param plan 学习计划对象
   * @param maxTags 最大标签数量
   * @returns 生成的标签数组
   */
  async generateTags(plan: any, maxTags: number = 10): Promise<Tag[]> {
    try {
      // 构建提示词
      const prompt = this._buildTagGenerationPrompt(plan, maxTags);

      // 调用AI接口
      const response = await this._callAIAPI(prompt);

      // 解析响应
      const tags = this._parseTagsFromResponse(response);

      // 验证标签
      const validatedTags = this._validateTags(tags, maxTags);

      this.logger.info(`为学习计划 "${plan.title}" 生成了 ${validatedTags.length} 个标签`);

      return validatedTags;
    } catch (error) {
      this.logger.error(`生成标签失败: ${error.message}`);

      // 返回备用标签（在API失败的情况下）
      return this._generateFallbackTags(plan);
    }
  }

  /**
   * 构建标签生成提示词
   * @private
   * @param plan 学习计划对象
   * @param maxTags 最大标签数量
   * @returns 提示词
   */
  private _buildTagGenerationPrompt(plan: any, maxTags: number): string {
    return `请为以下学习计划生成${maxTags}个标签，每个标签2-4个汉字，不要编号，每行一个标签：

学习计划标题：${plan.title}
${plan.description ? `学习计划描述：${plan.description}` : ''}
${plan.theme && plan.theme.name ? `主题：${plan.theme.name}` : ''}

要求：
1. 每个标签2-4个汉字
2. 标签应该简洁、有吸引力
3. 标签应该与学习计划主题相关
4. 不要使用重复或相似的标签
5. 不要使用过于宽泛的标签
6. 不要使用"学习"、"提升"等过于通用的词语
7. 直接输出标签，每行一个，不要有编号或其他格式`;
  }

  /**
   * 调用AI API
   * @private
   * @param prompt 提示词
   * @returns API响应
   */
  private async _callAIAPI(prompt: string): Promise<AIResponse> {
    try {
      // 记录请求开始时间（用于性能监控）
      const startTime = Date.now();

      // 实现限流：如果距离上次请求不足1秒，则等待
      const timeSinceLastRequest = startTime - this.lastRequestTime;
      if (timeSinceLastRequest < 1000) {
        await new Promise(resolve => setTimeout(resolve, 1000 - timeSinceLastRequest));
      }

      // 增加请求计数
      this.requestCount++;

      // 设置请求超时（30秒）
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), 30000);
      });

      // 根据提供商选择不同的API调用方法
      let responsePromise;
      if (this.provider === 'bytedance') {
        responsePromise = this._callBytedanceAPI(prompt);
      } else if (this.provider === 'aliyun') {
        responsePromise = this._callAliyunAPI(prompt);
      } else if (this.provider === 'hunyuan') {
        responsePromise = this._callHunyuanAPI(prompt);
      } else {
        throw new Error(`不支持的AI提供商: ${this.provider}`);
      }

      // 使用Promise.race实现超时控制
      const response = await Promise.race([responsePromise, timeoutPromise]);

      // 更新上次请求时间
      this.lastRequestTime = Date.now();

      // 记录请求耗时（用于性能监控）
      const requestDuration = this.lastRequestTime - startTime;
      this.logger.debug(`AI请求耗时: ${requestDuration}ms`);

      return response;
    } catch (error) {
      // 增加错误计数
      this.errorCount++;

      // 增强错误信息
      if (error.response) {
        if (error.response.status === 429) {
          this.logger.warn(`AI服务请求频率超限: ${this.provider}`);
          throw this.createError('AI服务请求频率超限，请稍后再试', 'RATE_LIMIT_EXCEEDED', 429);
        } else if (error.response.status === 401) {
          this.logger.error(`AI服务认证失败: ${this.provider}`);
          throw this.createError('AI服务认证失败，请检查API密钥', 'INVALID_API_KEY', 401);
        } else if (error.response.status >= 500) {
          this.logger.error(`AI服务服务器错误: ${this.provider}, 状态码: ${error.response.status}`);
          throw this.createError(`AI服务暂时不可用，请稍后再试 (${error.response.status})`, 'SERVICE_UNAVAILABLE', 503);
        }
      } else if (error.code === 'ECONNABORTED' || error.message === '请求超时') {
        this.logger.warn(`AI服务请求超时: ${this.provider}`);
        throw this.createError('AI服务请求超时，请稍后再试', 'REQUEST_TIMEOUT', 408);
      } else if (error.code === 'ECONNREFUSED') {
        this.logger.error(`AI服务连接被拒绝: ${this.provider}`);
        throw this.createError('无法连接到AI服务，请检查网络连接', 'CONNECTION_REFUSED', 502);
      }

      // 通用错误
      this.logger.error(`AI服务请求失败: ${error.message}`);
      throw this.createError(`AI服务请求失败: ${error.message}`, 'AI_SERVICE_ERROR', 500);
    }
  }

  /**
   * 调用字节大模型API
   * @private
   * @param prompt 提示词
   * @returns API响应
   */
  private async _callBytedanceAPI(prompt: string): Promise<AIResponse> {
    // 打印请求信息
    this.logger.debug(`发送请求到字节大模型API: https://ark.cn-beijing.volces.com/api/v3`);
    this.logger.debug(`使用模型: ${this.model}`);
    this.logger.debug(`提示词长度: ${prompt.length}字符`);

    // 使用OpenAI SDK发送请求
    const completion = await this.openai.chat.completions.create({
      model: this.model,
      messages: [
        { role: "system", content: "你是人工智能助手" },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
      timeout: 30000 // 设置30秒超时
    });

    // 更新Token使用统计
    if (completion.usage) {
      this.tokenUsage.prompt += completion.usage.prompt_tokens || 0;
      this.tokenUsage.completion += completion.usage.completion_tokens || 0;
      this.tokenUsage.total += completion.usage.total_tokens || 0;
    }

    // 打印响应信息
    this.logger.debug(`字节大模型响应成功，生成内容长度: ${completion.choices[0].message.content.length}字符`);
    this.logger.debug(`Token使用情况: 输入=${completion.usage?.prompt_tokens || 0}, 输出=${completion.usage?.completion_tokens || 0}, 总计=${completion.usage?.total_tokens || 0}`);

    // 返回响应数据
    return this._transformOpenAIResponse(completion);
  }

  /**
   * 调用阿里云百炼API
   * @private
   * @param prompt 提示词
   * @returns API响应
   */
  private async _callAliyunAPI(prompt: string): Promise<AIResponse> {
    // 打印请求信息
    this.logger.debug(`发送请求到阿里云百炼API: https://dashscope.aliyuncs.com/compatible-mode/v1`);
    this.logger.debug(`使用模型: ${this.model}`);
    this.logger.debug(`提示词长度: ${prompt.length}字符`);

    // 构建请求参数
    const requestParams = {
      model: this.model,
      messages: [
        { role: "system", content: "你是一个智能助手，擅长生成简洁、清晰的内容" },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
      timeout: 30000 // 设置30秒超时
    };

    // 使用OpenAI SDK发送请求
    const completion = await this.openai.chat.completions.create(requestParams);

    // 更新Token使用统计
    if (completion.usage) {
      this.tokenUsage.prompt += completion.usage.prompt_tokens || 0;
      this.tokenUsage.completion += completion.usage.completion_tokens || 0;
      this.tokenUsage.total += completion.usage.total_tokens || 0;
    }

    // 打印响应信息
    this.logger.debug(`阿里云百炼响应成功，生成内容长度: ${completion.choices[0].message.content.length}字符`);
    this.logger.debug(`Token使用情况: 输入=${completion.usage?.prompt_tokens || 0}, 输出=${completion.usage?.completion_tokens || 0}, 总计=${completion.usage?.total_tokens || 0}`);

    // 检查是否有思考过程输出
    if (completion.choices[0].message.thinking_content) {
      this.logger.debug(`思考过程长度: ${completion.choices[0].message.thinking_content.length}字符`);
    }

    // 返回响应数据
    return this._transformOpenAIResponse(completion);
  }

  /**
   * 调用腾讯混元API
   * @private
   * @param prompt 提示词
   * @returns API响应
   */
  private async _callHunyuanAPI(prompt: string): Promise<AIResponse> {
    // 打印请求信息
    this.logger.debug(`发送请求到腾讯混元API: https://api.hunyuan.cloud.tencent.com/v1`);
    this.logger.debug(`使用模型: ${this.model}`);
    this.logger.debug(`提示词长度: ${prompt.length}字符`);

    // 构建请求参数
    const requestParams = {
      model: this.model,
      messages: [
        { role: "system", content: "你是一个智能助手，请用中文回答问题" },
        { role: "user", content: prompt }
      ],
      temperature: 0.8, // 增加创造性
      max_tokens: 1024, // 设置较大的输出长度
      top_p: 0.9, // 增加多样性
      timeout: 30000, // 设置30秒超时
      enable_enhancement: true // 开启功能增强
    };

    // 使用OpenAI SDK发送请求
    const completion = await this.openai.chat.completions.create(requestParams);

    // 更新Token使用统计
    if (completion.usage) {
      this.tokenUsage.prompt += completion.usage.prompt_tokens || 0;
      this.tokenUsage.completion += completion.usage.completion_tokens || 0;
      this.tokenUsage.total += completion.usage.total_tokens || 0;
    }

    // 打印响应信息
    this.logger.debug(`腾讯混元响应成功，生成内容长度: ${completion.choices[0].message.content.length}字符`);
    this.logger.debug(`Token使用情况: 输入=${completion.usage?.prompt_tokens || 0}, 输出=${completion.usage?.completion_tokens || 0}, 总计=${completion.usage?.total_tokens || 0}`);

    // 返回响应数据
    return this._transformOpenAIResponse(completion);
  }

  /**
   * 转换OpenAI兼容格式的响应为标准格式
   * @private
   * @param openaiResponse OpenAI兼容格式的响应
   * @returns 转换后的响应
   */
  private _transformOpenAIResponse(openaiResponse: any): AIResponse {
    try {
      // 检查响应是否有效
      if (!openaiResponse || !openaiResponse.choices || !openaiResponse.choices[0] || !openaiResponse.choices[0].message) {
        this.logger.warn('收到无效的AI响应格式');
        return {
          choices: [{ message: { content: '' } }],
          usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
        };
      }

      // 构建标准响应格式
      const standardResponse: AIResponse = {
        choices: [
          {
            message: {
              content: openaiResponse.choices[0].message.content || '',
              role: openaiResponse.choices[0].message.role || 'assistant'
            }
          }
        ],
        usage: openaiResponse.usage || {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }
      };

      // 处理阿里云百炼的思考模式输出
      if (openaiResponse.choices[0].message.thinking_content) {
        standardResponse.choices[0].message.thinking_content = openaiResponse.choices[0].message.thinking_content;
        this.logger.debug(`包含思考过程输出，长度: ${openaiResponse.choices[0].message.thinking_content.length}字符`);
      }

      // 处理其他可能的特殊字段
      if (openaiResponse.choices[0].message.function_call) {
        standardResponse.choices[0].message.function_call = openaiResponse.choices[0].message.function_call;
      }

      if (openaiResponse.choices[0].finish_reason) {
        standardResponse.choices[0].finish_reason = openaiResponse.choices[0].finish_reason;
      }

      return standardResponse;
    } catch (error) {
      this.logger.error(`转换OpenAI响应失败: ${error.message}`);
      throw new Error(`转换OpenAI响应失败: ${error.message}`);
    }
  }

  /**
   * 从API响应中解析标签
   * @private
   * @param response API响应
   * @returns 标签数组
   */
  private _parseTagsFromResponse(response: AIResponse): string[] {
    try {
      // 获取AI回复的内容
      const content = response.choices[0].message.content.trim();

      // 按行分割，并过滤空行
      const tags = content.split('\n')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0)
        // 移除可能的编号（如"1. "、"- "等）
        .map(tag => tag.replace(/^(\d+\.|\-|\*)\s+/, ''))
        // 移除可能的引号
        .map(tag => tag.replace(/^["'](.*)["']$/, '$1'));

      return tags;
    } catch (error) {
      this.logger.error(`解析标签失败: ${error.message}`);
      throw new Error(`解析标签失败: ${error.message}`);
    }
  }

  /**
   * 验证标签
   * @private
   * @param tags 标签数组
   * @param maxTags 最大标签数量
   * @returns 验证后的标签对象数组
   */
  private _validateTags(tags: string[], maxTags: number): Tag[] {
    // 过滤无效标签（长度不在2-4之间的）
    const validTags = tags
      .filter(tag => tag.length >= 2 && tag.length <= 4)
      // 去重
      .filter((tag, index, self) => self.indexOf(tag) === index);

    // 限制标签数量
    const limitedTags = validTags.slice(0, maxTags);

    // 转换为标签对象
    return limitedTags.map((tag, index) => ({
      name: tag,
      relevanceScore: 1 - (index * 0.05), // 根据顺序设置相关性得分
      sortOrder: index
    }));
  }

  /**
   * 生成备用标签（在API调用失败时使用）
   * @private
   * @param plan 学习计划对象
   * @returns 备用标签对象数组
   */
  private _generateFallbackTags(plan: any): Tag[] {
    // 根据主题选择不同的备用标签
    let fallbackTags: string[] = [];

    if (plan.theme && plan.theme.name) {
      const themeName = plan.theme.name;

      if (themeName.includes('沟通')) {
        fallbackTags = ['倾听', '表达', '同理心', '反馈', '肢体语言', '提问', '演讲', '辩论', '说服', '谈判'];
      } else if (themeName.includes('学习')) {
        fallbackTags = ['专注', '记忆', '理解', '应用', '分析', '总结', '复习', '笔记', '思维导图', '效率'];
      } else if (themeName.includes('职场')) {
        fallbackTags = ['规划', '执行', '团队', '领导', '创新', '决策', '时间管理', '压力', '谈判', '人脉'];
      } else if (themeName.includes('情绪')) {
        fallbackTags = ['觉察', '接纳', '调节', '表达', '共情', '冥想', '感恩', '乐观', '韧性', '平衡'];
      } else {
        // 通用备用标签
        fallbackTags = ['专注', '高效', '创新', '思考', '行动', '坚持', '突破', '成长', '平衡', '反思'];
      }
    } else {
      // 通用备用标签
      fallbackTags = ['专注', '高效', '创新', '思考', '行动', '坚持', '突破', '成长', '平衡', '反思'];
    }

    // 转换为标签对象
    return fallbackTags.map((tag, index) => ({
      name: tag,
      relevanceScore: 1 - (index * 0.05), // 根据顺序设置相关性得分
      sortOrder: index
    }));
  }

  /**
   * 生成完整的学习计划内容
   * @param planData 用户提交的学习计划基本数据
   * @returns 生成的学习计划内容
   */
  async generateLearningPlanContent(planData: LearningPlanData): Promise<LearningPlanContent> {
    try {
      // 构建提示词
      const prompt = this._buildLearningPlanPrompt(planData);

      // 调用AI接口
      const response = await this._callAIAPI(prompt);

      // 解析响应
      const planContent = this._parseLearningPlanFromResponse(response);

      this.logger.info(`为学习计划 "${planData.title}" 生成了内容`);

      return planContent;
    } catch (error) {
      this.logger.error(`生成学习计划内容失败: ${error.message}`);

      // 返回备用内容
      return this._generateFallbackPlanContent(planData);
    }
  }

  /**
   * 构建学习计划生成提示词
   * @private
   * @param planData 学习计划基本数据
   * @returns 提示词
   */
  private _buildLearningPlanPrompt(planData: LearningPlanData): string {
    // 学习强度映射
    const intensityMap: Record<string, string> = {
      easy: '轻松',
      medium: '适中',
      hard: '挑战'
    };

    // 学习强度描述
    const intensity = intensityMap[planData.learningIntensity] || '适中';

    return `请为以下学习计划生成详细内容，以JSON格式返回：

学习计划标题：${planData.title}
学习者困扰：${planData.trouble || ''}
学习强度：${intensity}
学习天数：${planData.learningDuration || 7}天

请生成以下内容：
1. 增强版标题（enhancedTitle）：更吸引人的标题
2. 设计原则（designPrinciple）：简要说明本学习计划的设计思路和原则
3. 内容计划（contentPlan）：每天的学习内容，包括：
   - 天数（day）：从1开始
   - 标题（title）：当天学习主题
   - 内容（content）：详细的学习内容，至少300字
4. 标签（tags）：5-10个与学习计划相关的标签，每个标签2-4个汉字

请直接返回JSON格式，不要有其他说明文字，格式如下：
{
  "enhancedTitle": "更吸引人的标题",
  "designPrinciple": "设计原则说明...",
  "contentPlan": [
    {
      "day": 1,
      "title": "第一天标题",
      "content": "第一天详细内容..."
    },
    ...
  ],
  "tags": ["标签1", "标签2", ...]
}`;
  }

  /**
   * 从API响应中解析学习计划内容
   * @private
   * @param response API响应
   * @returns 学习计划内容对象
   */
  private _parseLearningPlanFromResponse(response: AIResponse): LearningPlanContent {
    try {
      // 获取AI回复的内容
      const content = response.choices[0].message.content.trim();

      // 提取JSON字符串（可能被包裹在代码块中）
      let jsonStr = content;

      // 如果内容被代码块包裹，提取其中的JSON
      const codeBlockMatch = content.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        jsonStr = codeBlockMatch[1];
      }

      // 解析JSON
      const planContent = JSON.parse(jsonStr);

      // 验证必要的字段
      if (!planContent.enhancedTitle || !planContent.designPrinciple ||
          !planContent.contentPlan || !planContent.tags || !Array.isArray(planContent.tags)) {
        throw new Error("AI响应缺少必要的学习计划字段");
      }

      // 验证标签
      planContent.tags = this._validateTags(planContent.tags, 15);

      return planContent;
    } catch (error) {
      this.logger.error(`解析学习计划内容失败: ${error.message}`);
      throw new Error(`解析学习计划内容失败: ${error.message}`);
    }
  }

  /**
   * 生成备用学习计划内容（在API调用失败时使用）
   * @private
   * @param planData 学习计划基本数据
   * @returns 备用学习计划内容
   */
  private _generateFallbackPlanContent(planData: LearningPlanData): LearningPlanContent {
    // 生成备用标题
    const enhancedTitle = `${planData.title} - 系统学习指南`;

    // 生成备用设计原则
    const designPrinciple = `本学习计划采用循序渐进的方式，帮助学习者在${planData.learningDuration || 7}天内系统掌握相关知识和技能，解决"${planData.trouble || '相关问题'}"。`;

    // 生成备用内容计划
    const contentPlan = [];
    for (let i = 1; i <= (planData.learningDuration || 7); i++) {
      contentPlan.push({
        day: i,
        title: `第${i}天：基础知识与概念`,
        content: `今天我们将学习关于"${planData.title}"的基础知识。首先，我们需要理解核心概念和原理。这些基础知识将为后续的学习打下坚实基础。\n\n在学习过程中，建议采用以下方法：\n1. 仔细阅读相关资料\n2. 做好笔记，记录重点内容\n3. 尝试用自己的话解释所学概念\n4. 思考这些知识如何应用到实际问题中\n\n今天的学习目标是掌握3-5个核心概念，并能够清晰地解释它们。如果遇到困难，可以尝试查找更多的例子或者寻求他人的帮助。\n\n记住，扎实的基础是成功的关键。今天的投入将为未来的学习奠定基础。`
      });
    }

    // 生成备用标签
    const tags = this._generateFallbackTags({ title: planData.title }).map(tag => tag);

    return {
      enhancedTitle,
      designPrinciple,
      contentPlan,
      tags
    };
  }
}