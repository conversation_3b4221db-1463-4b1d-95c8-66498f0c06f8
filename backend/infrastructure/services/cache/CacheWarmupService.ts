/**
 * 缓存预热服务
 * 提供缓存预热功能，支持分级预热和增量预热
 */
import { Logger } from '../../logging/Logger';
import { EnhancedCacheService } from './EnhancedCacheService';
import { Sequelize, Op } from 'sequelize';

/**
 * 缓存预热服务类
 */
export class CacheWarmupService {
  /**
   * 构造函数
   * @param logger 日志记录器
   * @param enhancedCache 增强版缓存服务
   * @param sequelize Sequelize实例
   * @param config 配置对象
   * @param models 数据模型
   */
  constructor(
    private readonly logger: Logger,
    private readonly enhancedCache: EnhancedCacheService,
    private readonly sequelize: Sequelize,
    private readonly config: any,
    private readonly models: any
  ) {}

  /**
   * 执行完整缓存预热
   * @returns 是否成功
   */
  async warmupAll(): Promise<boolean> {
    try {
      this.logger.info('开始执行完整缓存预热...');

      // 预热主题数据
      await this.warmupThemes();

      // 预热标签数据
      await this.warmupTags();

      // 预热标签分类数据
      await this.warmupTagCategories();

      // 预热热门学习计划
      await this.warmupPopularLearningPlans();

      // 预热系统默认学习计划
      await this.warmupSystemDefaultPlans();

      // 预热热门练习
      await this.warmupPopularExercises();

      // 预热热门笔记
      await this.warmupPopularNotes();

      // 预热热门见解
      await this.warmupPopularInsights();

      // 预热学习模板
      await this.warmupLearningTemplates();

      // 预热活跃用户
      await this.warmupActiveUsers();

      this.logger.info('完整缓存预热完成');
      return true;
    } catch (error) {
      this.logger.error(`完整缓存预热失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 执行增量缓存预热
   * @param hours 最近小时数
   * @returns 是否成功
   */
  async warmupIncremental(hours: number = 24): Promise<boolean> {
    try {
      this.logger.info(`开始执行增量缓存预热（最近${hours}小时）...`);

      const since = new Date(Date.now() - hours * 60 * 60 * 1000);

      // 预热最近更新的学习计划
      await this.warmupRecentLearningPlans(since);

      // 预热最近更新的练习
      await this.warmupRecentExercises(since);

      // 预热最近更新的笔记
      await this.warmupRecentNotes(since);

      // 预热最近更新的见解
      await this.warmupRecentInsights(since);

      // 预热最近活跃的用户
      await this.warmupRecentActiveUsers(since);

      this.logger.info('增量缓存预热完成');
      return true;
    } catch (error) {
      this.logger.error(`增量缓存预热失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热主题数据
   * @returns 是否成功
   */
  async warmupThemes(): Promise<boolean> {
    try {
      this.logger.info('开始预热主题数据...');

      const themes = await this.models.Theme.findAll({
        where: {
          isActive: true
        },
        order: [['displayOrder', 'ASC']]
      });

      if (themes && themes.length > 0) {
        await this.enhancedCache.set('themes', themes, { ttl: this.config.redis.ttl.theme });
        this.logger.info(`已预热${themes.length}个主题数据`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热主题数据失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热标签数据
   * @returns 是否成功
   */
  async warmupTags(): Promise<boolean> {
    try {
      this.logger.info('开始预热标签数据...');

      const tags = await this.models.Tag.findAll({
        where: {
          isActive: true
        },
        include: [
          {
            model: this.models.TagCategory,
            as: 'category'
          }
        ],
        order: [['usageCount', 'DESC']],
        limit: 100
      });

      if (tags && tags.length > 0) {
        await this.enhancedCache.set('system:tags', tags, { ttl: this.config.redis.ttl.tag });
        this.logger.info(`已预热${tags.length}个标签数据`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热标签数据失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热标签分类数据
   * @returns 是否成功
   */
  async warmupTagCategories(): Promise<boolean> {
    try {
      this.logger.info('开始预热标签分类数据...');

      const categories = await this.models.TagCategory.findAll({
        where: {
          isActive: true
        },
        order: [['displayOrder', 'ASC']]
      });

      if (categories && categories.length > 0) {
        await this.enhancedCache.set('system:categories', categories, { ttl: this.config.redis.ttl.tagCategory });
        this.logger.info(`已预热${categories.length}个标签分类数据`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热标签分类数据失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热热门学习计划
   * @returns 是否成功
   */
  async warmupPopularLearningPlans(): Promise<boolean> {
    try {
      this.logger.info('开始预热热门学习计划...');

      const plans = await this.models.LearningPlan.findAll({
        where: {
          isPublic: true,
          isActive: true
        },
        include: [
          {
            model: this.models.Theme,
            as: 'theme'
          }
        ],
        order: [['viewCount', 'DESC']],
        limit: 20
      });

      if (plans && plans.length > 0) {
        await this.enhancedCache.set('popular:plans', plans, { ttl: this.config.redis.ttl.popularPlan });
        this.logger.info(`已预热${plans.length}个热门学习计划`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热热门学习计划失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热系统默认学习计划
   * @returns 是否成功
   */
  async warmupSystemDefaultPlans(): Promise<boolean> {
    try {
      this.logger.info('开始预热系统默认学习计划...');

      const plans = await this.models.LearningPlan.findAll({
        where: {
          isSystemDefault: true,
          isActive: true
        },
        include: [
          {
            model: this.models.Theme,
            as: 'theme'
          }
        ]
      });

      if (plans && plans.length > 0) {
        await this.enhancedCache.set('system:default:plans', plans, { ttl: this.config.redis.ttl.systemPlan });
        this.logger.info(`已预热${plans.length}个系统默认学习计划`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热系统默认学习计划失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热热门练习
   * @returns 是否成功
   */
  async warmupPopularExercises(): Promise<boolean> {
    try {
      this.logger.info('开始预热热门练习...');

      const exercises = await this.models.Exercise.findAll({
        where: {
          visibility: 'public',
          isActive: true
        },
        order: [['viewCount', 'DESC']],
        limit: 20
      });

      if (exercises && exercises.length > 0) {
        await this.enhancedCache.set('popular:exercises', exercises, { ttl: this.config.redis.ttl.popularExercise });
        this.logger.info(`已预热${exercises.length}个热门练习`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热热门练习失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热热门笔记
   * @returns 是否成功
   */
  async warmupPopularNotes(): Promise<boolean> {
    try {
      this.logger.info('开始预热热门笔记...');

      const notes = await this.models.Note.findAll({
        where: {
          visibility: 'public',
          isActive: true
        },
        order: [['viewCount', 'DESC']],
        limit: 20
      });

      if (notes && notes.length > 0) {
        await this.enhancedCache.set('popular:notes', notes, { ttl: this.config.redis.ttl.popularNote });
        this.logger.info(`已预热${notes.length}个热门笔记`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热热门笔记失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热热门见解
   * @returns 是否成功
   */
  async warmupPopularInsights(): Promise<boolean> {
    try {
      this.logger.info('开始预热热门见解...');

      const insights = await this.models.Insight.findAll({
        where: {
          visibility: 'public',
          isActive: true
        },
        order: [['viewCount', 'DESC']],
        limit: 20
      });

      if (insights && insights.length > 0) {
        await this.enhancedCache.set('popular:insights', insights, { ttl: this.config.redis.ttl.popularInsight });
        this.logger.info(`已预热${insights.length}个热门见解`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热热门见解失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热学习模板
   * @returns 是否成功
   */
  async warmupLearningTemplates(): Promise<boolean> {
    try {
      this.logger.info('开始预热学习模板...');

      const templates = await this.models.LearningTemplate.findAll({
        where: {
          isActive: true
        },
        order: [['displayOrder', 'ASC']]
      });

      if (templates && templates.length > 0) {
        await this.enhancedCache.set('learning:templates', templates, { ttl: this.config.redis.ttl.learningTemplate });
        this.logger.info(`已预热${templates.length}个学习模板`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热学习模板失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热活跃用户
   * @returns 是否成功
   */
  async warmupActiveUsers(): Promise<boolean> {
    try {
      this.logger.info('开始预热活跃用户...');

      const users = await this.models.User.findAll({
        where: {
          isActive: true
        },
        attributes: ['id', 'username', 'nickname', 'avatar', 'bio', 'createdAt'],
        order: [['lastLoginAt', 'DESC']],
        limit: 20
      });

      if (users && users.length > 0) {
        await this.enhancedCache.set('active:users', users, { ttl: this.config.redis.ttl.activeUser });
        this.logger.info(`已预热${users.length}个活跃用户`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热活跃用户失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热最近更新的学习计划
   * @param since 起始时间
   * @returns 是否成功
   */
  async warmupRecentLearningPlans(since: Date): Promise<boolean> {
    try {
      this.logger.info(`开始预热最近更新的学习计划（自${since.toISOString()}起）...`);

      const plans = await this.models.LearningPlan.findAll({
        where: {
          isActive: true,
          updatedAt: {
            [Op.gte]: since
          }
        },
        include: [
          {
            model: this.models.Theme,
            as: 'theme'
          }
        ],
        order: [['updatedAt', 'DESC']],
        limit: 50
      });

      if (plans && plans.length > 0) {
        await this.enhancedCache.set('recent:plans', plans, { ttl: this.config.redis.ttl.recentPlan });
        this.logger.info(`已预热${plans.length}个最近更新的学习计划`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热最近更新的学习计划失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热最近更新的练习
   * @param since 起始时间
   * @returns 是否成功
   */
  async warmupRecentExercises(since: Date): Promise<boolean> {
    try {
      this.logger.info(`开始预热最近更新的练习（自${since.toISOString()}起）...`);

      const exercises = await this.models.Exercise.findAll({
        where: {
          isActive: true,
          updatedAt: {
            [Op.gte]: since
          }
        },
        order: [['updatedAt', 'DESC']],
        limit: 50
      });

      if (exercises && exercises.length > 0) {
        await this.enhancedCache.set('recent:exercises', exercises, { ttl: this.config.redis.ttl.recentExercise });
        this.logger.info(`已预热${exercises.length}个最近更新的练习`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热最近更新的练习失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热最近更新的笔记
   * @param since 起始时间
   * @returns 是否成功
   */
  async warmupRecentNotes(since: Date): Promise<boolean> {
    try {
      this.logger.info(`开始预热最近更新的笔记（自${since.toISOString()}起）...`);

      const notes = await this.models.Note.findAll({
        where: {
          isActive: true,
          updatedAt: {
            [Op.gte]: since
          }
        },
        order: [['updatedAt', 'DESC']],
        limit: 50
      });

      if (notes && notes.length > 0) {
        await this.enhancedCache.set('recent:notes', notes, { ttl: this.config.redis.ttl.recentNote });
        this.logger.info(`已预热${notes.length}个最近更新的笔记`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热最近更新的笔记失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热最近更新的见解
   * @param since 起始时间
   * @returns 是否成功
   */
  async warmupRecentInsights(since: Date): Promise<boolean> {
    try {
      this.logger.info(`开始预热最近更新的见解（自${since.toISOString()}起）...`);

      const insights = await this.models.Insight.findAll({
        where: {
          isActive: true,
          updatedAt: {
            [Op.gte]: since
          }
        },
        order: [['updatedAt', 'DESC']],
        limit: 50
      });

      if (insights && insights.length > 0) {
        await this.enhancedCache.set('recent:insights', insights, { ttl: this.config.redis.ttl.recentInsight });
        this.logger.info(`已预热${insights.length}个最近更新的见解`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热最近更新的见解失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 预热最近活跃的用户
   * @param since 起始时间
   * @returns 是否成功
   */
  async warmupRecentActiveUsers(since: Date): Promise<boolean> {
    try {
      this.logger.info(`开始预热最近活跃的用户（自${since.toISOString()}起）...`);

      const users = await this.models.User.findAll({
        where: {
          isActive: true,
          lastLoginAt: {
            [Op.gte]: since
          }
        },
        attributes: ['id', 'username', 'nickname', 'avatar', 'bio', 'createdAt', 'lastLoginAt'],
        order: [['lastLoginAt', 'DESC']],
        limit: 50
      });

      if (users && users.length > 0) {
        await this.enhancedCache.set('recent:active:users', users, { ttl: this.config.redis.ttl.recentActiveUser });
        this.logger.info(`已预热${users.length}个最近活跃的用户`);
      }

      return true;
    } catch (error) {
      this.logger.error(`预热最近活跃的用户失败: ${error.message}`);
      return false;
    }
  }
}