/**
 * 短信服务
 * 处理短信发送相关的业务逻辑
 */
import axios from 'axios';
import { Logger } from '../../logging/Logger';

/**
 * 短信服务类
 */
export class SMSService {
  /**
   * 构造函数
   * @param logger 日志记录器
   * @param config 配置对象
   */
  constructor(
    private readonly logger: Logger,
    private readonly config: any
  ) {}

  /**
   * 发送短信
   * @param phoneNumber 手机号
   * @param content 短信内容
   * @returns 是否发送成功
   */
  async sendSMS(phoneNumber: string, content: string): Promise<boolean> {
    try {
      // 如果是测试环境，只记录日志，不实际发送短信
      if (this.config.env === 'test' || this.config.env === 'development') {
        this.logger.info(`[测试环境] 短信发送: ${phoneNumber}, 内容: ${content}`);
        return true;
      }

      // 根据配置的短信服务商发送短信
      switch (this.config.sms.provider) {
        case 'aliyun':
          return await this.sendAliyunSMS(phoneNumber, content);
        case 'tencent':
          return await this.sendTencentSMS(phoneNumber, content);
        default:
          throw new Error(`不支持的短信服务商: ${this.config.sms.provider}`);
      }
    } catch (error) {
      this.logger.error(`短信发送失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送阿里云短信
   * @param phoneNumber 手机号
   * @param content 短信内容
   * @returns 是否发送成功
   */
  private async sendAliyunSMS(phoneNumber: string, content: string): Promise<boolean> {
    try {
      // 阿里云短信API参数
      const params = {
        AccessKeyId: this.config.sms.aliyun.accessKeyId,
        Action: 'SendSms',
        Format: 'JSON',
        PhoneNumbers: phoneNumber,
        SignName: this.config.sms.aliyun.signName,
        TemplateCode: this.config.sms.aliyun.templateCode,
        TemplateParam: JSON.stringify({ code: content.match(/\d{6}/)?.[0] || '' }),
        Version: '2017-05-25'
      };

      // 发送请求
      const response = await axios.get('https://dysmsapi.aliyuncs.com/', { params });

      // 检查响应
      if (response.data.Code === 'OK') {
        this.logger.info(`阿里云短信发送成功: ${response.data.RequestId}`);
        return true;
      } else {
        this.logger.error(`阿里云短信发送失败: ${response.data.Message}`);
        throw new Error(response.data.Message);
      }
    } catch (error) {
      this.logger.error(`阿里云短信发送失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送腾讯云短信
   * @param phoneNumber 手机号
   * @param content 短信内容
   * @returns 是否发送成功
   */
  private async sendTencentSMS(phoneNumber: string, content: string): Promise<boolean> {
    try {
      // 腾讯云短信API参数
      const params = {
        PhoneNumberSet: [phoneNumber],
        SmsSdkAppId: this.config.sms.tencent.appId,
        SignName: this.config.sms.tencent.signName,
        TemplateId: this.config.sms.tencent.templateId,
        TemplateParamSet: [content.match(/\d{6}/)?.[0] || '']
      };

      // 发送请求
      const response = await axios.post('https://sms.tencentcloudapi.com/', params, {
        headers: {
          'Content-Type': 'application/json',
          'X-TC-Action': 'SendSms',
          'X-TC-Version': '2021-01-11',
          'X-TC-Region': 'ap-guangzhou',
          'X-TC-Timestamp': Math.floor(Date.now() / 1000),
          'X-TC-SecretId': this.config.sms.tencent.secretId,
          'X-TC-SecretKey': this.config.sms.tencent.secretKey
        }
      });

      // 检查响应
      if (response.data.Response.SendStatusSet[0].Code === 'Ok') {
        this.logger.info(`腾讯云短信发送成功: ${response.data.Response.RequestId}`);
        return true;
      } else {
        this.logger.error(`腾讯云短信发送失败: ${response.data.Response.SendStatusSet[0].Message}`);
        throw new Error(response.data.Response.SendStatusSet[0].Message);
      }
    } catch (error) {
      this.logger.error(`腾讯云短信发送失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送验证码短信
   * @param phoneNumber 手机号
   * @param verificationCode 验证码
   * @returns 是否发送成功
   */
  async sendVerificationSMS(phoneNumber: string, verificationCode: string): Promise<boolean> {
    try {
      // 构建短信内容
      const content = `【AIBUBB】您的验证码是：${verificationCode}，有效期10分钟，请勿泄露给他人。`;

      // 发送短信
      return await this.sendSMS(phoneNumber, content);
    } catch (error) {
      this.logger.error(`验证码短信发送失败: ${error.message}`);
      throw error;
    }
  }
}
