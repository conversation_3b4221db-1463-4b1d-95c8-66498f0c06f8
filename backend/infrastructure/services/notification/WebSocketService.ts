/**
 * WebSocket服务
 * 提供实时通知和数据同步功能
 */
import WebSocket from 'ws';
import http from 'http';
import url from 'url';
import { RedisClientType } from 'redis';
import { Logger } from '../../logging/Logger';
import { JwtService } from '../security/JwtService';

/**
 * WebSocket客户端接口
 */
interface WebSocketClient extends WebSocket {
  userId: string;
  isAdmin: boolean;
  lastActivity: number;
  heartbeatTimer?: NodeJS.Timeout;
  status?: string;
  subscriptions?: Set<string>;
}

/**
 * WebSocket服务类
 */
export class WebSocketService {
  private wss: WebSocket.Server | null;
  private server: http.Server | null;
  private clients: Map<string, Set<WebSocketClient>>;
  private onlineUsers: Map<string, number>;
  private onlineStatusInterval: number;
  private onlineStatusTimer: NodeJS.Timeout | null;
  private redisSubscriber: RedisClientType | null;
  private channelPrefix: string;
  private heartbeatInterval: number;
  private heartbeatTimeout: number;

  /**
   * 构造函数
   * @param logger 日志记录器
   * @param jwtService JWT服务
   * @param redisClient Redis客户端
   */
  constructor(
    private readonly logger: Logger,
    private readonly jwtService: JwtService,
    private readonly redisClient: RedisClientType
  ) {
    // WebSocket服务器
    this.wss = null;

    // HTTP服务器
    this.server = null;

    // 客户端连接映射表 (userId -> Set<WebSocket>)
    this.clients = new Map();

    // 在线用户映射表 (userId -> lastActivity)
    this.onlineUsers = new Map();

    // 在线状态更新间隔（毫秒）
    this.onlineStatusInterval = 60000;

    // 在线状态更新定时器
    this.onlineStatusTimer = null;

    // Redis订阅客户端
    this.redisSubscriber = null;

    // 频道前缀
    this.channelPrefix = 'ws:';

    // 心跳间隔（毫秒）
    this.heartbeatInterval = 30000;

    // 心跳超时（毫秒）
    this.heartbeatTimeout = 60000;
  }

  /**
   * 初始化WebSocket服务
   * @param httpServer HTTP服务器
   * @returns 服务实例
   */
  init(httpServer: http.Server): WebSocketService {
    // 创建WebSocket服务器
    this.wss = new WebSocket.Server({
      server: httpServer,
      path: '/ws'
    });

    // 保存HTTP服务器引用
    this.server = httpServer;

    // 设置连接处理
    this.wss.on('connection', this.handleConnection.bind(this));

    // 启动在线状态更新
    this.startOnlineStatusUpdate();

    // 初始化Redis订阅
    this.initRedisSubscriber();

    this.logger.info('WebSocket服务已初始化');
    return this;
  }

  /**
   * 初始化Redis订阅
   */
  async initRedisSubscriber(): Promise<void> {
    if (!this.redisClient.isOpen) {
      this.logger.warn('Redis客户端未连接，WebSocket服务将无法使用Redis发布/订阅');
      return;
    }

    try {
      // 创建Redis订阅客户端
      this.redisSubscriber = this.redisClient.duplicate();

      // 连接Redis
      await this.redisSubscriber.connect();

      // 订阅广播频道
      await this.redisSubscriber.subscribe(`${this.channelPrefix}broadcast`, (message) => {
        this.handleRedisMessage('broadcast', message);
      });

      // 订阅用户频道
      await this.redisSubscriber.subscribe(`${this.channelPrefix}user:*`, (message, channel) => {
        const userId = channel.split(':')[2];
        this.handleRedisMessage('user', message, userId);
      });

      this.logger.info('WebSocket服务已连接到Redis发布/订阅系统');
    } catch (error) {
      this.logger.error(`Redis订阅初始化失败: ${error.message}`);
    }
  }

  /**
   * 处理WebSocket连接
   * @param ws WebSocket连接
   * @param req HTTP请求
   */
  async handleConnection(ws: WebSocket, req: http.IncomingMessage): Promise<void> {
    try {
      const client = ws as WebSocketClient;

      // 解析URL查询参数
      const { query } = url.parse(req.url || '', true);

      // 验证令牌
      if (!query.token) {
        client.close(4001, '未提供认证令牌');
        return;
      }

      const decoded = await this.jwtService.verifyAccessToken(query.token as string);
      if (!decoded) {
        client.close(4002, '无效或过期的令牌');
        return;
      }

      // 保存用户ID
      client.userId = decoded.id;
      client.isAdmin = decoded.isAdmin;
      client.lastActivity = Date.now();

      // 添加到客户端映射表
      if (!this.clients.has(client.userId)) {
        this.clients.set(client.userId, new Set());
      }
      this.clients.get(client.userId)?.add(client);

      // 更新在线状态
      this.onlineUsers.set(client.userId, Date.now());
      this.broadcastOnlineStatus(client.userId, true);

      // 发送欢迎消息
      this.sendToClient(client, {
        type: 'welcome',
        message: '已连接到WebSocket服务',
        userId: client.userId
      });

      // 设置消息处理
      client.on('message', (message) => this.handleMessage(client, message));

      // 设置关闭处理
      client.on('close', () => this.handleClose(client));

      // 设置错误处理
      client.on('error', (error) => this.handleError(client, error));

      // 设置心跳检查
      client.heartbeatTimer = setInterval(() => {
        this.checkHeartbeat(client);
      }, this.heartbeatInterval);

      this.logger.info(`WebSocket客户端已连接: ${client.userId}`);
    } catch (error) {
      this.logger.error(`WebSocket连接处理失败: ${error.message}`);
      ws.close(4000, '连接处理失败');
    }
  }

  /**
   * 处理WebSocket消息
   * @param client WebSocket客户端
   * @param message 消息数据
   */
  handleMessage(client: WebSocketClient, message: WebSocket.Data): void {
    try {
      // 解析消息
      const data = JSON.parse(message.toString());

      // 更新最后活动时间
      client.lastActivity = Date.now();

      // 处理心跳消息
      if (data.type === 'ping') {
        this.sendToClient(client, { type: 'pong', timestamp: Date.now() });
        return;
      }

      // 处理其他消息类型
      switch (data.type) {
        case 'status':
          // 处理状态更新
          this.handleStatusUpdate(client, data);
          break;

        case 'subscribe':
          // 处理订阅请求
          this.handleSubscribe(client, data);
          break;

        case 'unsubscribe':
          // 处理取消订阅请求
          this.handleUnsubscribe(client, data);
          break;

        default:
          // 未知消息类型
          this.sendToClient(client, {
            type: 'error',
            message: '未知的消息类型'
          });
      }
    } catch (error) {
      this.logger.error(`WebSocket消息处理失败: ${error.message}`);
      this.sendToClient(client, {
        type: 'error',
        message: '消息处理失败'
      });
    }
  }

  /**
   * 处理WebSocket关闭
   * @param client WebSocket客户端
   */
  handleClose(client: WebSocketClient): void {
    try {
      // 清除心跳定时器
      if (client.heartbeatTimer) {
        clearInterval(client.heartbeatTimer);
      }

      // 从客户端映射表中移除
      if (client.userId && this.clients.has(client.userId)) {
        this.clients.get(client.userId)?.delete(client);

        // 如果用户没有其他连接，从在线用户映射表中移除
        if (this.clients.get(client.userId)?.size === 0) {
          this.clients.delete(client.userId);
          this.onlineUsers.delete(client.userId);

          // 广播用户下线
          this.broadcastOnlineStatus(client.userId, false);
        }
      }

      this.logger.info(`WebSocket客户端已断开连接: ${client.userId || '未知'}`);
    } catch (error) {
      this.logger.error(`WebSocket关闭处理失败: ${error.message}`);
    }
  }

  /**
   * 处理WebSocket错误
   * @param client WebSocket客户端
   * @param error 错误对象
   */
  handleError(client: WebSocketClient, error: Error): void {
    this.logger.error(`WebSocket错误: ${error.message}`);

    // 尝试发送错误消息
    try {
      this.sendToClient(client, {
        type: 'error',
        message: '连接错误'
      });
    } catch (e) {
      // 忽略发送错误
    }
  }

  /**
   * 检查心跳
   * @param client WebSocket客户端
   */
  checkHeartbeat(client: WebSocketClient): void {
    const now = Date.now();

    // 如果超过心跳超时时间没有活动，关闭连接
    if (now - client.lastActivity > this.heartbeatTimeout) {
      this.logger.debug(`WebSocket心跳超时: ${client.userId}`);
      client.close(4003, '心跳超时');
      return;
    }

    // 发送心跳请求
    this.sendToClient(client, { type: 'ping', timestamp: now });
  }

  /**
   * 向客户端发送消息
   * @param client WebSocket客户端
   * @param data 消息数据
   */
  sendToClient(client: WebSocketClient, data: any): void {
    try {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(data));
      }
    } catch (error) {
      this.logger.error(`WebSocket发送消息失败: ${error.message}`);
    }
  }

  /**
   * 向用户的所有连接发送消息
   * @param userId 用户ID
   * @param data 消息数据
   * @returns 发送成功的连接数
   */
  sendToUser(userId: string, data: any): number {
    if (!this.clients.has(userId)) {
      return 0;
    }

    let sentCount = 0;

    for (const client of this.clients.get(userId) || []) {
      try {
        this.sendToClient(client, data);
        sentCount++;
      } catch (error) {
        this.logger.error(`向用户发送消息失败: ${error.message}`);
      }
    }

    return sentCount;
  }

  /**
   * 广播消息给所有连接的客户端
   * @param data 消息数据
   * @param filter 过滤函数，返回true表示发送
   * @returns 发送成功的连接数
   */
  broadcast(data: any, filter: ((client: WebSocketClient) => boolean) | null = null): number {
    let sentCount = 0;

    for (const [userId, connections] of this.clients.entries()) {
      for (const client of connections) {
        if (!filter || filter(client)) {
          try {
            this.sendToClient(client, data);
            sentCount++;
          } catch (error) {
            this.logger.error(`广播消息失败: ${error.message}`);
          }
        }
      }
    }

    return sentCount;
  }

  /**
   * 通过Redis发布消息
   * @param channel 频道名称
   * @param data 消息数据
   * @returns 是否成功
   */
  async publish(channel: string, data: any): Promise<boolean> {
    if (!this.redisClient.isOpen) {
      this.logger.warn('Redis客户端未连接，无法发布消息');
      return false;
    }

    try {
      const message = JSON.stringify(data);
      await this.redisClient.publish(`${this.channelPrefix}${channel}`, message);
      return true;
    } catch (error) {
      this.logger.error(`Redis发布消息失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 处理Redis消息
   * @param type 消息类型
   * @param message 消息内容
   * @param userId 用户ID（可选）
   */
  handleRedisMessage(type: string, message: string, userId: string | null = null): void {
    try {
      const data = JSON.parse(message);

      if (type === 'broadcast') {
        // 广播消息
        this.broadcast(data);
      } else if (type === 'user' && userId) {
        // 用户消息
        this.sendToUser(userId, data);
      }
    } catch (error) {
      this.logger.error(`处理Redis消息失败: ${error.message}`);
    }
  }

  /**
   * 启动在线状态更新
   */
  startOnlineStatusUpdate(): void {
    if (this.onlineStatusTimer) {
      clearInterval(this.onlineStatusTimer);
    }

    this.onlineStatusTimer = setInterval(() => {
      this.updateOnlineStatus();
    }, this.onlineStatusInterval);

    this.logger.info(`在线状态更新已启动，间隔: ${this.onlineStatusInterval}ms`);
  }

  /**
   * 更新在线状态
   */
  updateOnlineStatus(): void {
    try {
      const onlineUserIds = Array.from(this.onlineUsers.keys());

      // 广播在线用户列表
      this.broadcast({
        type: 'onlineUsers',
        users: onlineUserIds,
        count: onlineUserIds.length,
        timestamp: Date.now()
      });

      this.logger.debug(`已更新在线状态，当前在线用户: ${onlineUserIds.length}`);
    } catch (error) {
      this.logger.error(`更新在线状态失败: ${error.message}`);
    }
  }

  /**
   * 广播用户在线状态变化
   * @param userId 用户ID
   * @param isOnline 是否在线
   */
  broadcastOnlineStatus(userId: string, isOnline: boolean): void {
    this.broadcast({
      type: 'userStatus',
      userId,
      status: isOnline ? 'online' : 'offline',
      timestamp: Date.now()
    });
  }

  /**
   * 处理状态更新
   * @param client WebSocket客户端
   * @param data 消息数据
   */
  handleStatusUpdate(client: WebSocketClient, data: any): void {
    // 更新用户状态
    if (data.status) {
      client.status = data.status;

      // 广播状态更新
      this.broadcast({
        type: 'userStatus',
        userId: client.userId,
        status: client.status,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 处理订阅请求
   * @param client WebSocket客户端
   * @param data 消息数据
   */
  handleSubscribe(client: WebSocketClient, data: any): void {
    // 添加订阅
    if (data.channel) {
      if (!client.subscriptions) {
        client.subscriptions = new Set();
      }

      client.subscriptions.add(data.channel);

      this.sendToClient(client, {
        type: 'subscribed',
        channel: data.channel,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 处理取消订阅请求
   * @param client WebSocket客户端
   * @param data 消息数据
   */
  handleUnsubscribe(client: WebSocketClient, data: any): void {
    // 移除订阅
    if (data.channel && client.subscriptions) {
      client.subscriptions.delete(data.channel);

      this.sendToClient(client, {
        type: 'unsubscribed',
        channel: data.channel,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 关闭WebSocket服务
   */
  async close(): Promise<void> {
    // 停止在线状态更新
    if (this.onlineStatusTimer) {
      clearInterval(this.onlineStatusTimer);
      this.onlineStatusTimer = null;
    }

    // 关闭所有连接
    if (this.wss) {
      for (const client of this.wss.clients) {
        client.close(1001, '服务关闭');
      }

      this.wss.close();
      this.wss = null;
    }

    // 关闭Redis订阅
    if (this.redisSubscriber) {
      await this.redisSubscriber.quit();
      this.redisSubscriber = null;
    }

    // 清空客户端映射表
    this.clients.clear();
    this.onlineUsers.clear();

    this.logger.info('WebSocket服务已关闭');
  }
}