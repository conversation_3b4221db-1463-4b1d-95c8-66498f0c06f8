/**
 * OpenTelemetry追踪器初始化
 * 用于配置和初始化OpenTelemetry追踪器
 */

const opentelemetry = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-trace-otlp-http');
const { ConsoleSpanExporter } = require('@opentelemetry/sdk-trace-base');
const { Resource } = require('@opentelemetry/resources');
const { SemanticResourceAttributes } = require('@opentelemetry/semantic-conventions');
const config = require('../../config/unified-config');
const logger = require('../../config/logger');

// 追踪器实例
let sdk = null;

/**
 * 创建资源
 * @returns {Resource} OpenTelemetry资源
 */
function createResource() {
  const resourceAttributes = {
    [SemanticResourceAttributes.SERVICE_NAME]: config.telemetry.serviceName,
    ...config.telemetry.resourceAttributes
  };
  
  return new Resource(resourceAttributes);
}

/**
 * 创建导出器
 * @returns {SpanExporter} 追踪导出器
 */
function createExporter() {
  const exporterType = config.telemetry.exporter.type;
  
  switch (exporterType) {
    case 'console':
      return new ConsoleSpanExporter();
      
    case 'otlp':
      return new OTLPTraceExporter({
        url: config.telemetry.exporter.otlp.url,
        headers: config.telemetry.exporter.otlp.headers
      });
      
    case 'jaeger':
      // 使用OTLP导出器连接到Jaeger
      return new OTLPTraceExporter({
        url: config.telemetry.exporter.jaeger.endpoint
      });
      
    case 'zipkin':
      // 使用OTLP导出器连接到Zipkin
      return new OTLPTraceExporter({
        url: config.telemetry.exporter.zipkin.url
      });
      
    default:
      logger.warn(`未知的导出器类型: ${exporterType}，使用控制台导出器`);
      return new ConsoleSpanExporter();
  }
}

/**
 * 获取自动检测配置
 * @returns {Object} 自动检测配置
 */
function getInstrumentationConfig() {
  return {
    '@opentelemetry/instrumentation-http': {
      enabled: config.telemetry.tracing.captureHttpRequests,
      ignoreIncomingPaths: config.telemetry.ignorePaths,
      applyCustomAttributesOnSpan: (span, request, response) => {
        if (config.telemetry.tracing.captureHttpHeaders) {
          // 添加请求头信息（排除敏感信息）
          const headers = { ...request.headers };
          delete headers.authorization;
          delete headers.cookie;
          span.setAttribute('http.request.headers', JSON.stringify(headers));
        }
        
        if (response && config.telemetry.tracing.captureHttpHeaders) {
          // 添加响应头信息
          span.setAttribute('http.response.headers', JSON.stringify(response.headers));
        }
      }
    },
    '@opentelemetry/instrumentation-express': {
      enabled: true
    },
    '@opentelemetry/instrumentation-mysql2': {
      enabled: config.telemetry.tracing.captureSqlQueries
    },
    '@opentelemetry/instrumentation-redis': {
      enabled: config.telemetry.tracing.captureRedisCommands
    },
    '@opentelemetry/instrumentation-pg': {
      enabled: config.telemetry.tracing.captureSqlQueries
    },
    '@opentelemetry/instrumentation-mongodb': {
      enabled: config.telemetry.tracing.captureSqlQueries
    }
  };
}

/**
 * 初始化OpenTelemetry
 */
function init() {
  if (!config.telemetry.enabled) {
    logger.info('OpenTelemetry追踪已禁用');
    return;
  }
  
  try {
    // 创建SDK
    sdk = new opentelemetry.NodeSDK({
      resource: createResource(),
      traceExporter: createExporter(),
      instrumentations: [
        getNodeAutoInstrumentations(getInstrumentationConfig())
      ],
      sampler: new opentelemetry.TraceIdRatioBasedSampler(config.telemetry.samplingRatio)
    });
    
    // 启动SDK
    sdk.start()
      .then(() => {
        logger.info(`OpenTelemetry追踪已启用，服务名称: ${config.telemetry.serviceName}`);
        logger.info(`导出器类型: ${config.telemetry.exporter.type}`);
        logger.info(`采样率: ${config.telemetry.samplingRatio * 100}%`);
      })
      .catch(error => {
        logger.error(`OpenTelemetry初始化失败: ${error.message}`);
      });
    
    // 注册关闭处理程序
    process.on('SIGTERM', () => {
      shutdown()
        .then(() => logger.info('OpenTelemetry已正常关闭'))
        .catch(error => logger.error(`OpenTelemetry关闭失败: ${error.message}`))
        .finally(() => process.exit(0));
    });
  } catch (error) {
    logger.error(`OpenTelemetry初始化错误: ${error.message}`);
  }
}

/**
 * 关闭OpenTelemetry
 * @returns {Promise<void>}
 */
async function shutdown() {
  if (sdk) {
    return sdk.shutdown();
  }
  return Promise.resolve();
}

/**
 * 创建自定义追踪
 * @param {string} name - 追踪名称
 * @param {Object} options - 追踪选项
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createCustomSpan(name, options, callback) {
  if (!config.telemetry.enabled) {
    return callback();
  }
  
  const api = require('@opentelemetry/api');
  const tracer = api.trace.getTracer('custom');
  
  return tracer.startActiveSpan(name, options, (span) => {
    try {
      const result = callback(span);
      
      if (result instanceof Promise) {
        return result
          .then(value => {
            span.end();
            return value;
          })
          .catch(error => {
            span.recordException(error);
            span.setStatus({ code: api.SpanStatusCode.ERROR });
            span.end();
            throw error;
          });
      } else {
        span.end();
        return result;
      }
    } catch (error) {
      span.recordException(error);
      span.setStatus({ code: api.SpanStatusCode.ERROR });
      span.end();
      throw error;
    }
  });
}

module.exports = {
  init,
  shutdown,
  createCustomSpan
};
