/**
 * 追踪工具类
 * 提供创建自定义追踪的便捷方法
 */

const api = require('@opentelemetry/api');
const config = require('../../config/unified-config');

// 获取追踪器
const tracer = api.trace.getTracer('aibubb-custom');

/**
 * 创建自定义追踪
 * @param {string} name - 追踪名称
 * @param {Object} options - 追踪选项
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createSpan(name, options, callback) {
  // 如果遥测未启用，直接执行回调
  if (!config.telemetry.enabled) {
    return callback();
  }
  
  // 如果options是函数，则它是回调
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  return tracer.startActiveSpan(name, options, (span) => {
    try {
      const result = callback(span);
      
      // 如果结果是Promise，在Promise完成时结束span
      if (result instanceof Promise) {
        return result
          .then(value => {
            span.end();
            return value;
          })
          .catch(error => {
            span.recordException(error);
            span.setStatus({ code: api.SpanStatusCode.ERROR });
            span.end();
            throw error;
          });
      } else {
        // 否则立即结束span
        span.end();
        return result;
      }
    } catch (error) {
      // 记录异常并结束span
      span.recordException(error);
      span.setStatus({ code: api.SpanStatusCode.ERROR });
      span.end();
      throw error;
    }
  });
}

/**
 * 创建数据库操作追踪
 * @param {string} operation - 操作类型（如：find, create, update, delete）
 * @param {string} entity - 实体名称
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createDbSpan(operation, entity, callback) {
  return createSpan(`db.${entity}.${operation}`, (span) => {
    span.setAttribute('db.operation', operation);
    span.setAttribute('db.entity', entity);
    return callback(span);
  });
}

/**
 * 创建服务操作追踪
 * @param {string} service - 服务名称
 * @param {string} operation - 操作名称
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createServiceSpan(service, operation, callback) {
  return createSpan(`service.${service}.${operation}`, (span) => {
    span.setAttribute('service.name', service);
    span.setAttribute('service.operation', operation);
    return callback(span);
  });
}

/**
 * 创建外部API调用追踪
 * @param {string} api - API名称
 * @param {string} operation - 操作名称
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createExternalApiSpan(api, operation, callback) {
  return createSpan(`external.${api}.${operation}`, (span) => {
    span.setAttribute('external.api', api);
    span.setAttribute('external.operation', operation);
    return callback(span);
  });
}

/**
 * 创建缓存操作追踪
 * @param {string} operation - 操作类型（如：get, set, delete）
 * @param {string} key - 缓存键
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createCacheSpan(operation, key, callback) {
  return createSpan(`cache.${operation}`, (span) => {
    span.setAttribute('cache.operation', operation);
    span.setAttribute('cache.key', key);
    return callback(span);
  });
}

/**
 * 创建事件处理追踪
 * @param {string} eventType - 事件类型
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createEventSpan(eventType, callback) {
  return createSpan(`event.${eventType}`, (span) => {
    span.setAttribute('event.type', eventType);
    return callback(span);
  });
}

/**
 * 创建任务处理追踪
 * @param {string} taskName - 任务名称
 * @param {Function} callback - 回调函数
 * @returns {*} 回调函数的返回值
 */
function createTaskSpan(taskName, callback) {
  return createSpan(`task.${taskName}`, (span) => {
    span.setAttribute('task.name', taskName);
    return callback(span);
  });
}

/**
 * 记录异常
 * @param {Error} error - 错误对象
 * @param {Object} attributes - 附加属性
 */
function recordException(error, attributes = {}) {
  const currentSpan = api.trace.getSpan(api.context.active());
  if (currentSpan && error) {
    currentSpan.recordException(error);
    currentSpan.setStatus({ code: api.SpanStatusCode.ERROR });
    
    // 添加附加属性
    Object.entries(attributes).forEach(([key, value]) => {
      currentSpan.setAttribute(key, value);
    });
  }
}

module.exports = {
  createSpan,
  createDbSpan,
  createServiceSpan,
  createExternalApiSpan,
  createCacheSpan,
  createEventSpan,
  createTaskSpan,
  recordException
};
