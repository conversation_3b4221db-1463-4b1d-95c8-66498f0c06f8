import { Request, Response, NextFunction } from 'express';
import { ExerciseApplicationService } from '../../../../application/services/content/exercise/ExerciseApplicationService';
import { CreateExerciseCommand } from '../../../../application/commands/content/exercise/CreateExerciseCommand';
import { UpdateExerciseCommand } from '../../../../application/commands/content/exercise/UpdateExerciseCommand';
import { DeleteExerciseCommand } from '../../../../application/commands/content/exercise/DeleteExerciseCommand';
import { RestoreExerciseCommand } from '../../../../application/commands/content/exercise/RestoreExerciseCommand';
import { PublishExerciseCommand } from '../../../../application/commands/content/exercise/PublishExerciseCommand';
import { AddExerciseTagCommand } from '../../../../application/commands/content/exercise/AddExerciseTagCommand';
import { RemoveExerciseTagCommand } from '../../../../application/commands/content/exercise/RemoveExerciseTagCommand';
import { GetExerciseQuery } from '../../../../application/queries/content/exercise/GetExerciseQuery';
import { SearchExercisesQuery } from '../../../../application/queries/content/exercise/SearchExercisesQuery';

/**
 * ExerciseController
 * 练习控制器，处理练习相关的HTTP请求
 *
 * @swagger
 * tags:
 *   name: Exercises
 *   description: 练习管理API
 */
export class ExerciseController {
  /**
   * 构造函数
   * @param exerciseApplicationService 练习应用服务
   */
  constructor(private readonly exerciseApplicationService: ExerciseApplicationService) {}

  /**
   * 创建练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   *
   * @swagger
   * /api/v2/exercises:
   *   post:
   *     summary: 创建练习
   *     description: 创建一个新的练习
   *     tags: [Exercises]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - title
   *               - description
   *               - expectedResult
   *               - difficulty
   *               - timeEstimateMinutes
   *             properties:
   *               title:
   *                 type: string
   *                 maxLength: 100
   *                 description: 练习标题
   *                 example: "JavaScript基础练习"
   *               description:
   *                 type: string
   *                 description: 练习描述
   *                 example: "这是一个JavaScript基础练习，帮助你掌握基本语法"
   *               expectedResult:
   *                 type: string
   *                 description: 预期结果
   *                 example: "完成后能够理解JavaScript的基本语法和使用方法"
   *               difficulty:
   *                 type: string
   *                 enum: [EASY, MEDIUM, HARD]
   *                 description: 难度级别
   *                 example: "MEDIUM"
   *               timeEstimateMinutes:
   *                 type: integer
   *                 minimum: 1
   *                 description: 预计完成时间（分钟）
   *                 example: 30
   *               visibility:
   *                 type: string
   *                 enum: [PUBLIC, PRIVATE, RESTRICTED]
   *                 description: 可见性
   *                 example: "PUBLIC"
   *               isOfficial:
   *                 type: boolean
   *                 description: 是否为官方练习
   *                 example: false
   *               tags:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: 标签列表
   *                 example: ["JavaScript", "编程基础"]
   *     responses:
   *       201:
   *         description: 练习创建成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 id:
   *                   type: integer
   *                   example: 1
   *                 title:
   *                   type: string
   *                   example: "JavaScript基础练习"
   *                 description:
   *                   type: string
   *                   example: "这是一个JavaScript基础练习，帮助你掌握基本语法"
   *                 expectedResult:
   *                   type: string
   *                   example: "完成后能够理解JavaScript的基本语法和使用方法"
   *                 difficulty:
   *                   type: string
   *                   example: "MEDIUM"
   *                 timeEstimateMinutes:
   *                   type: integer
   *                   example: 30
   *                 creatorId:
   *                   type: string
   *                   example: "user123"
   *                 status:
   *                   type: string
   *                   example: "DRAFT"
   *                 visibility:
   *                   type: string
   *                   example: "PUBLIC"
   *                 isOfficial:
   *                   type: boolean
   *                   example: false
   *                 tags:
   *                   type: array
   *                   items:
   *                     type: string
   *                   example: ["JavaScript", "编程基础"]
   *                 createdAt:
   *                   type: string
   *                   format: date-time
   *                   example: "2025-05-04T10:30:00Z"
   *                 updatedAt:
   *                   type: string
   *                   format: date-time
   *                   example: "2025-05-04T10:30:00Z"
   *                 deletedAt:
   *                   type: string
   *                   format: date-time
   *                   nullable: true
   *                   example: null
   *                 isDeleted:
   *                   type: boolean
   *                   example: false
   *                 isPublished:
   *                   type: boolean
   *                   example: false
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  async createExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: CreateExerciseCommand = {
        title: req.body.title,
        description: req.body.description,
        expectedResult: req.body.expectedResult,
        difficulty: req.body.difficulty,
        timeEstimateMinutes: req.body.timeEstimateMinutes,
        creatorId: req.user!.id,
        visibility: req.body.visibility,
        isOfficial: req.body.isOfficial,
        tags: req.body.tags
      };

      const result = await this.exerciseApplicationService.createExercise(command);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async updateExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: UpdateExerciseCommand = {
        exerciseId: parseInt(req.params.id),
        title: req.body.title,
        description: req.body.description,
        expectedResult: req.body.expectedResult,
        difficulty: req.body.difficulty,
        timeEstimateMinutes: req.body.timeEstimateMinutes,
        visibility: req.body.visibility,
        isOfficial: req.body.isOfficial,
        tags: req.body.tags
      };

      const result = await this.exerciseApplicationService.updateExercise(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   *
   * @swagger
   * /api/v2/exercises/{id}:
   *   delete:
   *     summary: 软删除练习
   *     description: 根据ID软删除练习，可以通过恢复接口恢复
   *     tags: [Exercises]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 练习ID
   *     responses:
   *       204:
   *         description: 练习删除成功，无返回内容
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         description: 练习不存在
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "练习ID 999 不存在"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  async deleteExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: DeleteExerciseCommand = {
        exerciseId: parseInt(req.params.id)
      };

      await this.exerciseApplicationService.deleteExercise(command);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  }

  /**
   * 恢复练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   *
   * @swagger
   * /api/v2/exercises/{id}/restore:
   *   post:
   *     summary: 恢复已删除的练习
   *     description: 恢复已软删除的练习
   *     tags: [Exercises]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 练习ID
   *     responses:
   *       200:
   *         description: 练习恢复成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 id:
   *                   type: integer
   *                   example: 1
   *                 title:
   *                   type: string
   *                   example: "JavaScript基础练习"
   *                 isDeleted:
   *                   type: boolean
   *                   example: false
   *                 deletedAt:
   *                   type: string
   *                   format: date-time
   *                   nullable: true
   *                   example: null
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         description: 练习不存在
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "练习ID 999 不存在"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  async restoreExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RestoreExerciseCommand = {
        exerciseId: parseInt(req.params.id)
      };

      const result = await this.exerciseApplicationService.restoreExercise(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 发布练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   *
   * @swagger
   * /api/v2/exercises/{id}/publish:
   *   post:
   *     summary: 发布练习
   *     description: 将练习状态更改为已发布
   *     tags: [Exercises]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 练习ID
   *     responses:
   *       200:
   *         description: 练习发布成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 id:
   *                   type: integer
   *                   example: 1
   *                 title:
   *                   type: string
   *                   example: "JavaScript基础练习"
   *                 status:
   *                   type: string
   *                   example: "PUBLISHED"
   *                 isPublished:
   *                   type: boolean
   *                   example: true
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         description: 练习不存在
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "练习ID 999 不存在"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  async publishExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: PublishExerciseCommand = {
        exerciseId: parseInt(req.params.id)
      };

      const result = await this.exerciseApplicationService.publishExercise(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 添加练习标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async addExerciseTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: AddExerciseTagCommand = {
        exerciseId: parseInt(req.params.id),
        tag: req.body.tag
      };

      const result = await this.exerciseApplicationService.addExerciseTag(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 移除练习标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async removeExerciseTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RemoveExerciseTagCommand = {
        exerciseId: parseInt(req.params.id),
        tag: req.params.tag
      };

      const result = await this.exerciseApplicationService.removeExerciseTag(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   *
   * @swagger
   * /api/v2/exercises/{id}:
   *   get:
   *     summary: 获取练习详情
   *     description: 根据ID获取练习的详细信息
   *     tags: [Exercises]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 练习ID
   *     responses:
   *       200:
   *         description: 成功获取练习详情
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 id:
   *                   type: integer
   *                   example: 1
   *                 title:
   *                   type: string
   *                   example: "JavaScript基础练习"
   *                 description:
   *                   type: string
   *                   example: "这是一个JavaScript基础练习，帮助你掌握基本语法"
   *                 expectedResult:
   *                   type: string
   *                   example: "完成后能够理解JavaScript的基本语法和使用方法"
   *                 difficulty:
   *                   type: string
   *                   example: "MEDIUM"
   *                 timeEstimateMinutes:
   *                   type: integer
   *                   example: 30
   *                 creatorId:
   *                   type: string
   *                   example: "user123"
   *                 status:
   *                   type: string
   *                   example: "PUBLISHED"
   *                 visibility:
   *                   type: string
   *                   example: "PUBLIC"
   *                 isOfficial:
   *                   type: boolean
   *                   example: false
   *                 tags:
   *                   type: array
   *                   items:
   *                     type: string
   *                   example: ["JavaScript", "编程基础"]
   *                 createdAt:
   *                   type: string
   *                   format: date-time
   *                   example: "2025-05-04T10:30:00Z"
   *                 updatedAt:
   *                   type: string
   *                   format: date-time
   *                   example: "2025-05-04T10:30:00Z"
   *                 deletedAt:
   *                   type: string
   *                   format: date-time
   *                   nullable: true
   *                   example: null
   *                 isDeleted:
   *                   type: boolean
   *                   example: false
   *                 isPublished:
   *                   type: boolean
   *                   example: true
   *       404:
   *         description: 练习不存在
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "练习不存在"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  async getExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: GetExerciseQuery = {
        exerciseId: parseInt(req.params.id)
      };

      const result = await this.exerciseApplicationService.getExercise(query);

      if (!result) {
        res.status(404).json({ message: '练习不存在' });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 搜索练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   *
   * @swagger
   * /api/v2/exercises:
   *   get:
   *     summary: 搜索练习
   *     description: 根据关键词、标签、难度等条件搜索练习
   *     tags: [Exercises]
   *     parameters:
   *       - in: query
   *         name: keyword
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *       - in: query
   *         name: tagIds
   *         schema:
   *           type: string
   *         description: 标签ID列表，以逗号分隔
   *         example: "1,2,3"
   *       - in: query
   *         name: difficulty
   *         schema:
   *           type: string
   *           enum: [EASY, MEDIUM, HARD]
   *         description: 难度级别
   *       - in: query
   *         name: creatorId
   *         schema:
   *           type: string
   *         description: 创建者ID
   *       - in: query
   *         name: isOfficial
   *         schema:
   *           type: boolean
   *         description: 是否为官方练习
   *       - in: query
   *         name: includeDeleted
   *         schema:
   *           type: boolean
   *           default: false
   *         description: 是否包含已删除的练习
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           enum: [relevance, date, difficulty]
   *           default: relevance
   *         description: 排序方式
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 50
   *           default: 20
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 搜索结果
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 type: object
   *                 properties:
   *                   id:
   *                     type: integer
   *                     example: 1
   *                   title:
   *                     type: string
   *                     example: "JavaScript基础练习"
   *                   description:
   *                     type: string
   *                     example: "这是一个JavaScript基础练习，帮助你掌握基本语法"
   *                   difficulty:
   *                     type: string
   *                     example: "MEDIUM"
   *                   timeEstimateMinutes:
   *                     type: integer
   *                     example: 30
   *                   creatorId:
   *                     type: string
   *                     example: "user123"
   *                   status:
   *                     type: string
   *                     example: "PUBLISHED"
   *                   visibility:
   *                     type: string
   *                     example: "PUBLIC"
   *                   isOfficial:
   *                     type: boolean
   *                     example: false
   *                   tags:
   *                     type: array
   *                     items:
   *                       type: string
   *                     example: ["JavaScript", "编程基础"]
   *                   createdAt:
   *                     type: string
   *                     format: date-time
   *                     example: "2025-05-04T10:30:00Z"
   *                   isDeleted:
   *                     type: boolean
   *                     example: false
   *                   isPublished:
   *                     type: boolean
   *                     example: true
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  async searchExercises(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: SearchExercisesQuery = {
        keyword: req.query.keyword as string,
        tagIds: req.query.tagIds ? (req.query.tagIds as string).split(',').map(id => parseInt(id)) : undefined,
        difficulty: req.query.difficulty as any,
        creatorId: req.query.creatorId as string,
        isOfficial: req.query.isOfficial === 'true',
        includeDeleted: req.query.includeDeleted === 'true',
        sortBy: req.query.sortBy as 'relevance' | 'date' | 'difficulty',
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        pageSize: req.query.pageSize ? parseInt(req.query.pageSize as string) : undefined
      };

      const results = await this.exerciseApplicationService.searchExercises(query);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取相似练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getSimilarExercises(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const exerciseId = parseInt(req.params.id);
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 3;

      const results = await this.exerciseApplicationService.getSimilarExercises(exerciseId, limit);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }
}
