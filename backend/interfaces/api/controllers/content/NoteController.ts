import { Request, Response, NextFunction } from 'express';
import { NoteApplicationService } from '../../../../application/services/content/note/NoteApplicationService';
import { CreateNoteCommand } from '../../../../application/commands/content/note/CreateNoteCommand';
import { UpdateNoteCommand } from '../../../../application/commands/content/note/UpdateNoteCommand';
import { DeleteNoteCommand } from '../../../../application/commands/content/note/DeleteNoteCommand';
import { RestoreNoteCommand } from '../../../../application/commands/content/note/RestoreNoteCommand';
import { PublishNoteCommand } from '../../../../application/commands/content/note/PublishNoteCommand';
import { AddNoteTagCommand } from '../../../../application/commands/content/note/AddNoteTagCommand';
import { RemoveNoteTagCommand } from '../../../../application/commands/content/note/RemoveNoteTagCommand';
import { GetNoteQuery } from '../../../../application/queries/content/note/GetNoteQuery';
import { SearchNotesQuery } from '../../../../application/queries/content/note/SearchNotesQuery';

/**
 * NoteController
 * 笔记控制器，处理笔记相关的HTTP请求
 */
export class NoteController {
  /**
   * 构造函数
   * @param noteApplicationService 笔记应用服务
   */
  constructor(private readonly noteApplicationService: NoteApplicationService) {}

  /**
   * 创建笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async createNote(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: CreateNoteCommand = {
        title: req.body.title,
        content: req.body.content,
        userId: req.user!.id,
        imageUrl: req.body.imageUrl,
        visibility: req.body.visibility,
        isAiGenerated: req.body.isAiGenerated,
        planId: req.body.planId,
        tags: req.body.tags
      };

      const result = await this.noteApplicationService.createNote(command);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async updateNote(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: UpdateNoteCommand = {
        noteId: parseInt(req.params.id),
        title: req.body.title,
        content: req.body.content,
        imageUrl: req.body.imageUrl,
        visibility: req.body.visibility,
        planId: req.body.planId,
        tags: req.body.tags
      };

      const result = await this.noteApplicationService.updateNote(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async deleteNote(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: DeleteNoteCommand = {
        noteId: parseInt(req.params.id)
      };

      await this.noteApplicationService.deleteNote(command);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  }

  /**
   * 恢复笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async restoreNote(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RestoreNoteCommand = {
        noteId: parseInt(req.params.id)
      };

      const result = await this.noteApplicationService.restoreNote(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 发布笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async publishNote(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: PublishNoteCommand = {
        noteId: parseInt(req.params.id)
      };

      const result = await this.noteApplicationService.publishNote(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 添加笔记标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async addNoteTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: AddNoteTagCommand = {
        noteId: parseInt(req.params.id),
        tag: req.body.tag
      };

      const result = await this.noteApplicationService.addNoteTag(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 移除笔记标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async removeNoteTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RemoveNoteTagCommand = {
        noteId: parseInt(req.params.id),
        tag: req.params.tag
      };

      const result = await this.noteApplicationService.removeNoteTag(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 增加笔记点赞数
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async incrementLikeCount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const noteId = parseInt(req.params.id);
      const result = await this.noteApplicationService.incrementLikeCount(noteId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 减少笔记点赞数
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async decrementLikeCount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const noteId = parseInt(req.params.id);
      const result = await this.noteApplicationService.decrementLikeCount(noteId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 增加笔记评论数
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async incrementCommentCount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const noteId = parseInt(req.params.id);
      const result = await this.noteApplicationService.incrementCommentCount(noteId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 减少笔记评论数
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async decrementCommentCount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const noteId = parseInt(req.params.id);
      const result = await this.noteApplicationService.decrementCommentCount(noteId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 增加笔记查看次数
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async incrementViewCount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const noteId = parseInt(req.params.id);
      const result = await this.noteApplicationService.incrementViewCount(noteId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getNote(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: GetNoteQuery = {
        noteId: parseInt(req.params.id)
      };

      const result = await this.noteApplicationService.getNote(query);
      
      if (!result) {
        res.status(404).json({ message: '笔记不存在' });
        return;
      }
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 搜索笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async searchNotes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: SearchNotesQuery = {
        keyword: req.query.keyword as string,
        tagIds: req.query.tagIds ? (req.query.tagIds as string).split(',').map(id => parseInt(id)) : undefined,
        userId: req.query.userId as string,
        planId: req.query.planId ? parseInt(req.query.planId as string) : undefined,
        isAiGenerated: req.query.isAiGenerated === 'true',
        includeDeleted: req.query.includeDeleted === 'true',
        sortBy: req.query.sortBy as 'date' | 'popularity',
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        pageSize: req.query.pageSize ? parseInt(req.query.pageSize as string) : undefined
      };

      const results = await this.noteApplicationService.searchNotes(query);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取相似笔记
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getSimilarNotes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const noteId = parseInt(req.params.id);
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 3;
      
      const results = await this.noteApplicationService.getSimilarNotes(noteId, limit);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }
}
