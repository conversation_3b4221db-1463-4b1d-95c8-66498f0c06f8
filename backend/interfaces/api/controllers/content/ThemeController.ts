import { Request, Response, NextFunction } from 'express';
import { ThemeApplicationService } from '../../../../application/services/content/theme/ThemeApplicationService';
import { CreateThemeCommand } from '../../../../application/commands/content/theme/CreateThemeCommand';
import { UpdateThemeCommand } from '../../../../application/commands/content/theme/UpdateThemeCommand';
import { DeleteThemeCommand } from '../../../../application/commands/content/theme/DeleteThemeCommand';
import { RestoreThemeCommand } from '../../../../application/commands/content/theme/RestoreThemeCommand';
import { UpdateThemeStatusCommand } from '../../../../application/commands/content/theme/UpdateThemeStatusCommand';
import { UpdateThemeSortOrderCommand } from '../../../../application/commands/content/theme/UpdateThemeSortOrderCommand';
import { GetThemeQuery } from '../../../../application/queries/content/theme/GetThemeQuery';
import { SearchThemesQuery } from '../../../../application/queries/content/theme/SearchThemesQuery';

/**
 * ThemeController
 * 主题控制器，处理主题相关的HTTP请求
 */
export class ThemeController {
  /**
   * 构造函数
   * @param themeApplicationService 主题应用服务
   */
  constructor(private readonly themeApplicationService: ThemeApplicationService) {}

  /**
   * 创建主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async createTheme(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: CreateThemeCommand = {
        name: req.body.name,
        englishName: req.body.englishName,
        description: req.body.description,
        icon: req.body.icon,
        color: req.body.color,
        coverImageUrl: req.body.coverImageUrl,
        sortOrder: req.body.sortOrder,
        isActive: req.body.isActive !== undefined ? req.body.isActive : true,
        parentId: req.body.parentId
      };

      const result = await this.themeApplicationService.createTheme(command);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async updateTheme(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: UpdateThemeCommand = {
        themeId: parseInt(req.params.id),
        name: req.body.name,
        englishName: req.body.englishName,
        description: req.body.description,
        icon: req.body.icon,
        color: req.body.color,
        coverImageUrl: req.body.coverImageUrl,
        sortOrder: req.body.sortOrder,
        isActive: req.body.isActive
      };

      const result = await this.themeApplicationService.updateTheme(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async deleteTheme(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: DeleteThemeCommand = {
        themeId: parseInt(req.params.id)
      };

      await this.themeApplicationService.deleteTheme(command);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  }

  /**
   * 恢复主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async restoreTheme(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RestoreThemeCommand = {
        themeId: parseInt(req.params.id)
      };

      const result = await this.themeApplicationService.restoreTheme(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新主题状态
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async updateThemeStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: UpdateThemeStatusCommand = {
        themeId: parseInt(req.params.id),
        isActive: req.body.isActive
      };

      const result = await this.themeApplicationService.updateThemeStatus(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新主题排序顺序
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async updateThemeSortOrder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: UpdateThemeSortOrderCommand = {
        themeId: parseInt(req.params.id),
        sortOrder: req.body.sortOrder
      };

      const result = await this.themeApplicationService.updateThemeSortOrder(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getTheme(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: GetThemeQuery = {
        themeId: parseInt(req.params.id)
      };

      const result = await this.themeApplicationService.getTheme(query);
      
      if (!result) {
        res.status(404).json({ message: '主题不存在' });
        return;
      }
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 搜索主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async searchThemes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: SearchThemesQuery = {
        keyword: req.query.keyword as string,
        parentId: req.query.parentId !== undefined ? 
          (req.query.parentId === 'null' ? null : parseInt(req.query.parentId as string)) : 
          undefined,
        isActive: req.query.isActive !== undefined ? req.query.isActive === 'true' : undefined,
        includeDeleted: req.query.includeDeleted === 'true',
        sortBy: req.query.sortBy as 'name' | 'sortOrder',
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined
      };

      const results = await this.themeApplicationService.searchThemes(query);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取顶级主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getTopLevelThemes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const results = await this.themeApplicationService.getTopLevelThemes();
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取激活的主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getActiveThemes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const results = await this.themeApplicationService.getActiveThemes();
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取已删除的主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getDeletedThemes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const results = await this.themeApplicationService.getDeletedThemes();
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取按排序顺序排序的主题
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getThemesBySortOrder(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const results = await this.themeApplicationService.getThemesBySortOrder();
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }
}
