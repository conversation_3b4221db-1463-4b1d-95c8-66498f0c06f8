import { Request, Response, NextFunction } from 'express';
import { TagApplicationService } from '../../../../application/services/tag/TagApplicationService';
import { CreateTagCommand } from '../../../../application/commands/tag/CreateTagCommand';
import { UpdateTagCommand } from '../../../../application/commands/tag/UpdateTagCommand';
import { DeleteTagCommand } from '../../../../application/commands/tag/DeleteTagCommand';
import { RestoreTagCommand } from '../../../../application/commands/tag/RestoreTagCommand';
import { AddTagSynonymCommand } from '../../../../application/commands/tag/AddTagSynonymCommand';
import { RemoveTagSynonymCommand } from '../../../../application/commands/tag/RemoveTagSynonymCommand';
import { MergeTagsCommand } from '../../../../application/commands/tag/MergeTagsCommand';
import { GetTagQuery } from '../../../../application/queries/tag/GetTagQuery';
import { SearchTagsQuery } from '../../../../application/queries/tag/SearchTagsQuery';
import { GetSuggestedTagsQuery } from '../../../../application/queries/tag/GetSuggestedTagsQuery';

/**
 * TagController
 * 标签控制器，处理标签相关的HTTP请求
 */
export class TagController {
  /**
   * 构造函数
   * @param tagApplicationService 标签应用服务
   */
  constructor(private readonly tagApplicationService: TagApplicationService) {}

  /**
   * 创建标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async createTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: CreateTagCommand = {
        name: req.body.name,
        categoryId: req.body.categoryId,
        description: req.body.description,
        creatorId: req.user!.id
      };

      const result = await this.tagApplicationService.createTag(command);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async updateTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: UpdateTagCommand = {
        tagId: parseInt(req.params.id),
        name: req.body.name,
        categoryId: req.body.categoryId,
        description: req.body.description
      };

      const result = await this.tagApplicationService.updateTag(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async deleteTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: DeleteTagCommand = {
        tagId: parseInt(req.params.id)
      };

      await this.tagApplicationService.deleteTag(command);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  }

  /**
   * 恢复标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async restoreTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RestoreTagCommand = {
        tagId: parseInt(req.params.id)
      };

      await this.tagApplicationService.restoreTag(command);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: GetTagQuery = {
        tagId: parseInt(req.params.id)
      };

      const result = await this.tagApplicationService.getTag(query);
      
      if (!result) {
        res.status(404).json({ message: '标签不存在' });
        return;
      }
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 搜索标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async searchTags(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: SearchTagsQuery = {
        keyword: req.query.keyword as string,
        categoryId: req.query.categoryId ? parseInt(req.query.categoryId as string) : undefined,
        includeDeleted: req.query.includeDeleted === 'true',
        sortBy: req.query.sortBy as 'name' | 'popularity',
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined
      };

      const results = await this.tagApplicationService.searchTags(query);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 添加标签同义词
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async addTagSynonym(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: AddTagSynonymCommand = {
        tagId: parseInt(req.params.id),
        name: req.body.name
      };

      const result = await this.tagApplicationService.addTagSynonym(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 移除标签同义词
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async removeTagSynonym(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RemoveTagSynonymCommand = {
        tagId: parseInt(req.params.id),
        synonym: req.params.synonym
      };

      const result = await this.tagApplicationService.removeTagSynonym(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 合并标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async mergeTags(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: MergeTagsCommand = {
        sourceTagId: parseInt(req.body.sourceTagId),
        targetTagId: parseInt(req.body.targetTagId)
      };

      const result = await this.tagApplicationService.mergeTags(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取推荐标签
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async getSuggestedTags(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: GetSuggestedTagsQuery = {
        tagId: parseInt(req.params.id),
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined
      };

      const results = await this.tagApplicationService.getSuggestedTags(query);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }
}
