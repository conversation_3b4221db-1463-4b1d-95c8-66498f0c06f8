import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { Logger } from '../../../infrastructure/logging/Logger';

// 扩展Express的Request接口，添加user属性
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username?: string;
        email?: string;
        roles?: string[];
        permissions?: string[];
        isAdmin?: boolean;
        jti?: string;
        [key: string]: any;
      };
    }
  }
}

// 创建Logger实例
const logger = new Logger('AuthMiddleware');

/**
 * 从请求头中提取JWT令牌
 * @param req 请求对象
 * @returns 令牌或null
 */
export const extractTokenFromHeader = (req: Request): string | null => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.split(' ')[1];
};

/**
 * 认证中间件
 * 验证请求是否包含有效的JWT令牌
 * @param req Express请求对象
 * @param res Express响应对象
 * @param next Express下一个中间件函数
 */
export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // 从请求头中获取令牌
  const token = extractTokenFromHeader(req);

  if (!token) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: '未提供认证令牌',
        details: {}
      }
    });
  }

  try {
    // 验证令牌
    if (!process.env.JWT_SECRET) {
      logger.error('JWT_SECRET 环境变量未设置');
      return res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '服务器配置错误',
          details: {}
        }
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: ['HS256'],
      complete: false
    }) as jwt.JwtPayload;

    // 将用户信息添加到请求对象
    req.user = {
      id: decoded.id || decoded.sub,
      username: decoded.username,
      email: decoded.email,
      roles: decoded.roles || [],
      permissions: decoded.permissions || [],
      isAdmin: decoded.isAdmin === true,
      jti: decoded.jti
    };

    // 检查用户是否有效
    if (!req.user.id) {
      logger.warn('令牌中缺少用户ID');
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: '无效的认证令牌',
          details: {}
        }
      });
    }

    next();
  } catch (error) {
    logger.error(`令牌验证失败: ${error.message}`);
    return res.status(403).json({
      success: false,
      error: {
        code: 'FORBIDDEN',
        message: '无效的认证令牌',
        details: {}
      }
    });
  }
};

/**
 * 可选认证中间件
 * 如果提供了令牌，则验证并添加用户信息，否则继续
 * @param req Express请求对象
 * @param res Express响应对象
 * @param next Express下一个中间件函数
 */
export const optionalAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // 从请求头中获取令牌
  const token = extractTokenFromHeader(req);

  // 如果没有提供令牌，直接继续
  if (!token) {
    return next();
  }

  try {
    // 验证令牌
    if (!process.env.JWT_SECRET) {
      logger.error('JWT_SECRET 环境变量未设置');
      return next();
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: ['HS256'],
      complete: false
    }) as jwt.JwtPayload;

    // 将用户信息添加到请求对象
    req.user = {
      id: decoded.id || decoded.sub,
      username: decoded.username,
      email: decoded.email,
      roles: decoded.roles || [],
      permissions: decoded.permissions || [],
      isAdmin: decoded.isAdmin === true,
      jti: decoded.jti
    };

    next();
  } catch (error) {
    // 如果令牌无效，忽略错误并继续
    logger.debug(`可选认证中的令牌验证失败: ${error.message}`);
    next();
  }
};

/**
 * 管理员权限中间件
 * 验证用户是否为管理员
 * @param req Express请求对象
 * @param res Express响应对象
 * @param next Express下一个中间件函数
 */
export const adminMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // 确保用户已经通过认证
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: '请先登录',
        details: {}
      }
    });
  }

  // 检查用户是否为管理员
  if (!req.user.isAdmin) {
    // 检查用户角色中是否包含管理员角色
    const hasAdminRole = req.user.roles && Array.isArray(req.user.roles) &&
                        (req.user.roles.includes('admin') || req.user.roles.includes('ADMIN'));

    if (!hasAdminRole) {
      logger.warn(`用户 ${req.user.id} 尝试访问管理员资源，但没有管理员权限`);
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: '没有管理员权限',
          details: {}
        }
      });
    }

    // 如果有管理员角色，设置isAdmin标志
    req.user.isAdmin = true;
  }

  next();
};

/**
 * 权限检查中间件
 * 验证用户是否有指定权限
 * @param permission 所需权限
 * @returns 中间件函数
 */
export const hasPermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // 确保用户已经通过认证
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '请先登录',
          details: {}
        }
      });
    }

    // 管理员拥有所有权限
    if (req.user.isAdmin) {
      return next();
    }

    // 检查用户权限
    const hasRequiredPermission = req.user.permissions &&
                                Array.isArray(req.user.permissions) &&
                                req.user.permissions.includes(permission);

    if (!hasRequiredPermission) {
      logger.warn(`用户 ${req.user.id} 尝试访问需要 ${permission} 权限的资源`);
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: '没有足够的权限',
          details: {}
        }
      });
    }

    next();
  };
};
