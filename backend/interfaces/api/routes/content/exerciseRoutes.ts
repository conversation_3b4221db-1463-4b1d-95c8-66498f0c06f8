import { Router } from 'express';
import { ExerciseController } from '../../controllers/content/ExerciseController';
import { authMiddleware } from '../../middlewares/authMiddleware';
import { validateRequest } from '../../middlewares/validateRequestMiddleware';
import { 
  createExerciseSchema, 
  updateExerciseSchema, 
  addTagSchema 
} from '../../validators/content/exerciseValidators';

/**
 * 配置练习路由
 * @param router Express路由器
 * @param exerciseController 练习控制器
 */
export const exerciseRoutes = (router: Router, exerciseController: ExerciseController): void => {
  // 练习CRUD
  router.post(
    '/exercises',
    authMiddleware,
    validateRequest(createExerciseSchema),
    exerciseController.createExercise.bind(exerciseController)
  );
  
  router.get(
    '/exercises/:id',
    exerciseController.getExercise.bind(exerciseController)
  );
  
  router.put(
    '/exercises/:id',
    authMiddleware,
    validateRequest(updateExerciseSchema),
    exerciseController.updateExercise.bind(exerciseController)
  );
  
  router.delete(
    '/exercises/:id',
    authMiddleware,
    exerciseController.deleteExercise.bind(exerciseController)
  );
  
  router.post(
    '/exercises/:id/restore',
    authMiddleware,
    exerciseController.restoreExercise.bind(exerciseController)
  );
  
  router.get(
    '/exercises',
    exerciseController.searchExercises.bind(exerciseController)
  );
  
  // 练习发布
  router.post(
    '/exercises/:id/publish',
    authMiddleware,
    exerciseController.publishExercise.bind(exerciseController)
  );
  
  // 练习标签管理
  router.post(
    '/exercises/:id/tags',
    authMiddleware,
    validateRequest(addTagSchema),
    exerciseController.addExerciseTag.bind(exerciseController)
  );
  
  router.delete(
    '/exercises/:id/tags/:tag',
    authMiddleware,
    exerciseController.removeExerciseTag.bind(exerciseController)
  );
  
  // 相似练习
  router.get(
    '/exercises/:id/similar',
    exerciseController.getSimilarExercises.bind(exerciseController)
  );
};
