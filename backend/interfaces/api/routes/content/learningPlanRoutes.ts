import { Router } from 'express';
import { LearningPlanController } from '../../controllers/content/LearningPlanController';
import { authMiddleware } from '../../middlewares/authMiddleware';
import { validateRequest } from '../../middlewares/validateRequestMiddleware';
import { 
  createLearningPlanSchema, 
  updateLearningPlanSchema, 
  updateProgressSchema, 
  addTagSchema 
} from '../../validators/content/learningPlanValidators';

/**
 * 配置学习计划路由
 * @param router Express路由器
 * @param learningPlanController 学习计划控制器
 */
export const learningPlanRoutes = (router: Router, learningPlanController: LearningPlanController): void => {
  // 学习计划CRUD
  router.post(
    '/learning-plans',
    authMiddleware,
    validateRequest(createLearningPlanSchema),
    learningPlanController.createLearningPlan.bind(learningPlanController)
  );
  
  router.get(
    '/learning-plans/:id',
    learningPlanController.getLearningPlan.bind(learningPlanController)
  );
  
  router.put(
    '/learning-plans/:id',
    authMiddleware,
    validateRequest(updateLearningPlanSchema),
    learningPlanController.updateLearningPlan.bind(learningPlanController)
  );
  
  router.delete(
    '/learning-plans/:id',
    authMiddleware,
    learningPlanController.deleteLearningPlan.bind(learningPlanController)
  );
  
  router.post(
    '/learning-plans/:id/restore',
    authMiddleware,
    learningPlanController.restoreLearningPlan.bind(learningPlanController)
  );
  
  router.get(
    '/learning-plans',
    learningPlanController.searchLearningPlans.bind(learningPlanController)
  );
  
  // 学习计划状态管理
  router.post(
    '/learning-plans/:id/start',
    authMiddleware,
    learningPlanController.startLearningPlan.bind(learningPlanController)
  );
  
  router.post(
    '/learning-plans/:id/complete',
    authMiddleware,
    learningPlanController.completeLearningPlan.bind(learningPlanController)
  );
  
  router.post(
    '/learning-plans/:id/pause',
    authMiddleware,
    learningPlanController.pauseLearningPlan.bind(learningPlanController)
  );
  
  router.post(
    '/learning-plans/:id/abandon',
    authMiddleware,
    learningPlanController.abandonLearningPlan.bind(learningPlanController)
  );
  
  router.post(
    '/learning-plans/:id/set-as-current',
    authMiddleware,
    learningPlanController.setAsCurrent.bind(learningPlanController)
  );
  
  router.put(
    '/learning-plans/:id/progress',
    authMiddleware,
    validateRequest(updateProgressSchema),
    learningPlanController.updateProgress.bind(learningPlanController)
  );
  
  // 学习计划标签管理
  router.post(
    '/learning-plans/:id/tags',
    authMiddleware,
    validateRequest(addTagSchema),
    learningPlanController.addLearningPlanTag.bind(learningPlanController)
  );
  
  router.delete(
    '/learning-plans/:id/tags/:tag',
    authMiddleware,
    learningPlanController.removeLearningPlanTag.bind(learningPlanController)
  );
  
  // 特殊查询
  router.get(
    '/current-learning-plan',
    authMiddleware,
    learningPlanController.getCurrentPlan.bind(learningPlanController)
  );
  
  router.get(
    '/deleted-learning-plans',
    authMiddleware,
    learningPlanController.getDeletedPlans.bind(learningPlanController)
  );
};
