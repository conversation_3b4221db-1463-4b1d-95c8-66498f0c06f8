import { Router } from 'express';
import { TagController } from '../../controllers/tag/TagController';
import { authMiddleware } from '../../middlewares/authMiddleware';
import { validateRequest } from '../../middlewares/validateRequestMiddleware';
import { createTagSchema, updateTagSchema, addSynonymSchema, mergeTagsSchema } from '../../validators/tag/tagValidators';

/**
 * 配置标签路由
 * @param router Express路由器
 * @param tagController 标签控制器
 */
export const tagRoutes = (router: Router, tagController: TagController): void => {
  // 标签CRUD
  router.post(
    '/tags',
    authMiddleware,
    validateRequest(createTagSchema),
    tagController.createTag.bind(tagController)
  );
  
  router.get(
    '/tags/:id',
    tagController.getTag.bind(tagController)
  );
  
  router.put(
    '/tags/:id',
    authMiddleware,
    validateRequest(updateTagSchema),
    tagController.updateTag.bind(tagController)
  );
  
  router.delete(
    '/tags/:id',
    authMiddleware,
    tagController.deleteTag.bind(tagController)
  );
  
  router.post(
    '/tags/:id/restore',
    authMiddleware,
    tagController.restoreTag.bind(tagController)
  );
  
  router.get(
    '/tags',
    tagController.searchTags.bind(tagController)
  );
  
  // 标签同义词
  router.post(
    '/tags/:id/synonyms',
    authMiddleware,
    validateRequest(addSynonymSchema),
    tagController.addTagSynonym.bind(tagController)
  );
  
  router.delete(
    '/tags/:id/synonyms/:synonym',
    authMiddleware,
    tagController.removeTagSynonym.bind(tagController)
  );
  
  // 标签合并
  router.post(
    '/tags/merge',
    authMiddleware,
    validateRequest(mergeTagsSchema),
    tagController.mergeTags.bind(tagController)
  );
  
  // 相关标签推荐
  router.get(
    '/tags/:id/suggested',
    tagController.getSuggestedTags.bind(tagController)
  );
};
