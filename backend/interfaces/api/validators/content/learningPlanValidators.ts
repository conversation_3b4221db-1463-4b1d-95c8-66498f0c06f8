import { z } from 'zod';

/**
 * 创建学习计划验证模式
 */
export const createLearningPlanSchema = z.object({
  body: z.object({
    title: z.string()
      .min(1, '标题不能为空')
      .max(100, '标题最多100个字符'),
    description: z.string()
      .max(1000, '描述最多1000个字符')
      .optional(),
    templateId: z.number()
      .int('模板ID必须是整数')
      .optional(),
    themeId: z.number()
      .int('主题ID必须是整数')
      .optional(),
    targetDays: z.number()
      .int('目标天数必须是整数')
      .min(1, '目标天数必须大于0')
      .max(365, '目标天数不能超过365天'),
    dailyGoalExercises: z.number()
      .int('每日练习目标必须是整数')
      .min(0, '每日练习目标不能为负数')
      .optional(),
    dailyGoalInsights: z.number()
      .int('每日洞察目标必须是整数')
      .min(0, '每日洞察目标不能为负数')
      .optional(),
    dailyGoalMinutes: z.number()
      .int('每日学习时间目标必须是整数')
      .min(0, '每日学习时间目标不能为负数')
      .optional(),
    isPublic: z.boolean()
      .optional(),
    tags: z.array(z.string())
      .optional(),
    setAsCurrent: z.boolean()
      .optional()
  })
});

/**
 * 更新学习计划验证模式
 */
export const updateLearningPlanSchema = z.object({
  body: z.object({
    title: z.string()
      .min(1, '标题不能为空')
      .max(100, '标题最多100个字符')
      .optional(),
    description: z.string()
      .max(1000, '描述最多1000个字符')
      .optional(),
    coverImageUrl: z.string()
      .url('封面图片URL格式不正确')
      .optional()
      .nullable(),
    targetDays: z.number()
      .int('目标天数必须是整数')
      .min(1, '目标天数必须大于0')
      .max(365, '目标天数不能超过365天')
      .optional(),
    dailyGoalExercises: z.number()
      .int('每日练习目标必须是整数')
      .min(0, '每日练习目标不能为负数')
      .optional(),
    dailyGoalInsights: z.number()
      .int('每日洞察目标必须是整数')
      .min(0, '每日洞察目标不能为负数')
      .optional(),
    dailyGoalMinutes: z.number()
      .int('每日学习时间目标必须是整数')
      .min(0, '每日学习时间目标不能为负数')
      .optional(),
    isPublic: z.boolean()
      .optional(),
    tags: z.array(z.string())
      .optional()
  })
});

/**
 * 更新学习计划进度验证模式
 */
export const updateProgressSchema = z.object({
  body: z.object({
    completedDays: z.number()
      .int('完成天数必须是整数')
      .min(0, '完成天数不能为负数')
  })
});

/**
 * 添加标签验证模式
 */
export const addTagSchema = z.object({
  body: z.object({
    tag: z.string()
      .min(1, '标签不能为空')
  })
});
