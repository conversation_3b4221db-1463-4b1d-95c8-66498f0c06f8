/**
 * 错误处理中间件
 *
 * 此文件为兼容层，直接导出统一错误处理中间件的功能
 * 为了保持向后兼容性，保留了原有的API接口
 *
 * @deprecated 请使用 unified-error.middleware.js
 */

// 导入统一错误处理中间件
const {
  errorHandler,
  handleEncodingError,
  createError
} = require('./unified-error.middleware');

// 显示弃用警告
console.warn('\x1b[33m%s\x1b[0m', '警告: errorHandler.js 已弃用，请使用 unified-error.middleware.js');

// 导出兼容接口
module.exports = {
  errorHandler,
  handleEncodingError,
  createError
};