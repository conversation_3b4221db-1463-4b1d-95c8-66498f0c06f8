/**
 * HTTPS中间件
 */

const https = require('https');
const { httpsConfig, getSSLOptions, createHTTPSRedirectMiddleware, createHSTSMiddleware } = require('../config/https');
const logger = require('../config/logger');

/**
 * 应用HTTPS中间件
 */
function applyHTTPSMiddleware(app) {
  // HTTP重定向到HTTPS中间件
  app.use(createHTTPSRedirectMiddleware());
  
  // HSTS中间件
  app.use(createHSTSMiddleware());
  
  // 安全头部中间件
  app.use((req, res, next) => {
    // 强制HTTPS
    if (httpsConfig.enabled) {
      res.setHeader('Content-Security-Policy', "upgrade-insecure-requests");
    }
    
    // 防止混合内容
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    next();
  });
}

/**
 * 创建HTTPS服务器
 */
function createHTTPSServer(app) {
  if (!httpsConfig.enabled) {
    return null;
  }

  const sslOptions = getSSLOptions();
  if (!sslOptions || !sslOptions.key || !sslOptions.cert) {
    logger.error('SSL证书配置无效，无法启动HTTPS服务器');
    return null;
  }

  try {
    const httpsServer = https.createServer(sslOptions, app);
    
    httpsServer.on('error', (error) => {
      logger.error('HTTPS服务器错误:', error);
    });

    httpsServer.on('listening', () => {
      logger.info(`🔒 HTTPS服务器启动成功，端口: ${httpsConfig.port}`);
    });

    return httpsServer;
  } catch (error) {
    logger.error('创建HTTPS服务器失败:', error);
    return null;
  }
}

/**
 * 启动HTTPS服务器
 */
function startHTTPSServer(app) {
  const httpsServer = createHTTPSServer(app);
  
  if (httpsServer) {
    httpsServer.listen(httpsConfig.port, () => {
      logger.info(`🔒 HTTPS服务器正在监听端口: ${httpsConfig.port}`);
    });
    
    return httpsServer;
  }
  
  return null;
}

module.exports = {
  applyHTTPSMiddleware,
  createHTTPSServer,
  startHTTPSServer,
};