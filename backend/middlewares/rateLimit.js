/**
 * 限流中间件配置
 */

const rateLimit = require('express-rate-limit');
const config = require('../config/config');
const logger = require('../config/logger');

// Redis存储（如果可用）
let RedisStore;
try {
  RedisStore = require('rate-limit-redis');
} catch (error) {
  logger.warn('rate-limit-redis not available, using memory store');
}

// 创建Redis存储实例
const createRedisStore = () => {
  if (!RedisStore || !config.redis.url) {
    return undefined;
  }

  try {
    const redis = require('redis');
    const client = redis.createClient({
      url: config.redis.url,
      socket: {
        connectTimeout: 5000,
        lazyConnect: true,
      },
    });

    return new RedisStore({
      sendCommand: (...args) => client.sendCommand(args),
      prefix: 'rl:',
    });
  } catch (error) {
    logger.error('Failed to create Redis store for rate limiting:', error);
    return undefined;
  }
};

// 默认限流配置
const defaultConfig = {
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个窗口期最大请求数
  message: {
    error: 'Too many requests',
    message: '请求过于频繁，请稍后再试',
    retryAfter: '15 minutes',
  },
  standardHeaders: true, // 返回标准的 `RateLimit` 头
  legacyHeaders: false, // 禁用 `X-RateLimit-*` 头
  store: createRedisStore(),
  keyGenerator: req => {
    // 优先使用真实IP，然后是转发IP，最后是连接IP
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  handler: (req, res) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
    res.status(429).json({
      error: 'Too Many Requests',
      message: '请求过于频繁，请稍后再试',
      retryAfter: Math.ceil(req.rateLimit.resetTime / 1000),
    });
  },
  skip: req => {
    // 跳过健康检查和静态资源
    const skipPaths = ['/health', '/favicon.ico', '/robots.txt'];
    return skipPaths.some(path => req.path.startsWith(path));
  },
};

// API限流配置
const apiLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // API请求限制更宽松
  message: {
    error: 'API rate limit exceeded',
    message: 'API请求过于频繁，请稍后再试',
    retryAfter: '15 minutes',
  },
});

// 认证API限流（更严格）
const authLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 认证请求限制更严格
  message: {
    error: 'Authentication rate limit exceeded',
    message: '登录尝试过于频繁，请稍后再试',
    retryAfter: '15 minutes',
  },
  skipSuccessfulRequests: true, // 成功的请求不计入限制
});

// 注册API限流
const registerLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 60 * 60 * 1000, // 1小时
  max: 5, // 每小时最多5次注册尝试
  message: {
    error: 'Registration rate limit exceeded',
    message: '注册尝试过于频繁，请1小时后再试',
    retryAfter: '1 hour',
  },
});

// 密码重置限流
const passwordResetLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每小时最多3次密码重置
  message: {
    error: 'Password reset rate limit exceeded',
    message: '密码重置请求过于频繁，请1小时后再试',
    retryAfter: '1 hour',
  },
});

// 文件上传限流
const uploadLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 60 * 60 * 1000, // 1小时
  max: 50, // 每小时最多50次上传
  message: {
    error: 'Upload rate limit exceeded',
    message: '文件上传过于频繁，请稍后再试',
    retryAfter: '1 hour',
  },
});

// 搜索API限流
const searchLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次搜索
  message: {
    error: 'Search rate limit exceeded',
    message: '搜索请求过于频繁，请稍后再试',
    retryAfter: '1 minute',
  },
});

// AI API限流（更严格，因为成本较高）
const aiLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 60 * 60 * 1000, // 1小时
  max: 100, // 每小时最多100次AI请求
  message: {
    error: 'AI API rate limit exceeded',
    message: 'AI服务请求过于频繁，请稍后再试',
    retryAfter: '1 hour',
  },
});

// 严格限流（用于敏感操作）
const strictLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 每小时最多10次
  message: {
    error: 'Strict rate limit exceeded',
    message: '操作过于频繁，请稍后再试',
    retryAfter: '1 hour',
  },
});

// 宽松限流（用于公开API）
const permissiveLimiter = rateLimit({
  ...defaultConfig,
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5000, // 很高的限制
  message: {
    error: 'Rate limit exceeded',
    message: '请求过于频繁，请稍后再试',
    retryAfter: '15 minutes',
  },
});

// 创建自定义限流器
const createCustomLimiter = (options = {}) => {
  return rateLimit({
    ...defaultConfig,
    ...options,
    store: options.useRedis !== false ? createRedisStore() : undefined,
  });
};

// 限流中间件工厂
const rateLimitFactory = {
  // 基于用户的限流
  createUserLimiter: (windowMs = 15 * 60 * 1000, max = 100) => {
    return rateLimit({
      ...defaultConfig,
      windowMs,
      max,
      keyGenerator: req => {
        // 如果用户已认证，使用用户ID，否则使用IP
        return req.user?.id || req.ip || 'anonymous';
      },
    });
  },

  // 基于API密钥的限流
  createApiKeyLimiter: (windowMs = 60 * 60 * 1000, max = 1000) => {
    return rateLimit({
      ...defaultConfig,
      windowMs,
      max,
      keyGenerator: req => {
        return req.headers['x-api-key'] || req.ip || 'no-key';
      },
    });
  },

  // 基于路径的限流
  createPathLimiter: (path, windowMs = 15 * 60 * 1000, max = 100) => {
    return rateLimit({
      ...defaultConfig,
      windowMs,
      max,
      keyGenerator: req => {
        return `${path}:${req.ip}`;
      },
      skip: req => !req.path.startsWith(path),
    });
  },
};

// 限流统计和监控
const rateLimitStats = {
  // 获取限流统计
  getStats: () => {
    return {
      redisAvailable: !!RedisStore && !!config.redis.url,
      defaultWindowMs: defaultConfig.windowMs,
      defaultMax: defaultConfig.max,
      limiters: {
        api: { windowMs: 15 * 60 * 1000, max: 1000 },
        auth: { windowMs: 15 * 60 * 1000, max: 10 },
        register: { windowMs: 60 * 60 * 1000, max: 5 },
        passwordReset: { windowMs: 60 * 60 * 1000, max: 3 },
        upload: { windowMs: 60 * 60 * 1000, max: 50 },
        search: { windowMs: 1 * 60 * 1000, max: 30 },
        ai: { windowMs: 60 * 60 * 1000, max: 100 },
      },
    };
  },

  // 检查限流配置
  validateConfig: () => {
    const errors = [];
    const warnings = [];

    if (!RedisStore && config.server.env === 'production') {
      warnings.push('Redis store not available for rate limiting in production');
    }

    if (defaultConfig.max > 10000) {
      warnings.push('Default rate limit is very high, consider lowering it');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  },
};

module.exports = {
  // 基础限流器
  defaultLimiter: rateLimit(defaultConfig),
  apiLimiter,
  authLimiter,
  registerLimiter,
  passwordResetLimiter,
  uploadLimiter,
  searchLimiter,
  aiLimiter,
  strictLimiter,
  permissiveLimiter,

  // 工厂函数
  createCustomLimiter,
  rateLimitFactory,

  // 统计和监控
  rateLimitStats,

  // 配置
  defaultConfig,
};
