/**
 * 安全中间件配置
 * 包含CORS、He<PERSON><PERSON>、限流等安全配置
 */

const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');

/**
 * CORS配置
 */
const corsOptions = {
  origin: function (origin, callback) {
    // 允许的域名列表
    const allowedOrigins = [
      'https://your-domain.com',
      'https://www.your-domain.com',
      'https://admin.your-domain.com'
    ];
    
    // 开发环境允许所有来源
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }
    
    // 生产环境检查白名单
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

/**
 * Helmet安全头配置
 */
const helmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  crossOriginEmbedderPolicy: false
};

/**
 * API限流配置
 */
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs: windowMs || parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
    max: max || parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 100次请求
    message: message || {
      error: 'Too many requests from this IP, please try again later.',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      res.status(429).json({
        error: 'Too many requests from this IP, please try again later.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.round(windowMs / 1000)
      });
    }
  });
};

/**
 * 严格的API限流（用于敏感操作）
 */
const strictRateLimit = createRateLimit(
  15 * 60 * 1000, // 15分钟
  10, // 10次请求
  {
    error: 'Too many sensitive operations from this IP, please try again later.',
    code: 'STRICT_RATE_LIMIT_EXCEEDED'
  }
);

/**
 * 宽松的API限流（用于一般操作）
 */
const generalRateLimit = createRateLimit(
  15 * 60 * 1000, // 15分钟
  100, // 100次请求
  {
    error: 'Too many requests from this IP, please try again later.',
    code: 'GENERAL_RATE_LIMIT_EXCEEDED'
  }
);

/**
 * 登录限流（防止暴力破解）
 */
const loginRateLimit = createRateLimit(
  15 * 60 * 1000, // 15分钟
  5, // 5次登录尝试
  {
    error: 'Too many login attempts from this IP, please try again later.',
    code: 'LOGIN_RATE_LIMIT_EXCEEDED'
  }
);

/**
 * 应用所有安全中间件
 */
const applySecurity = (app) => {
  // 应用Helmet安全头
  app.use(helmet(helmetOptions));
  
  // 应用CORS
  app.use(cors(corsOptions));
  
  // 信任代理（如果使用负载均衡器）
  if (process.env.NODE_ENV === 'production') {
    app.set('trust proxy', 1);
  }
  
  // 一般API限流
  app.use('/api/', generalRateLimit);
  
  // 登录限流
  app.use('/api/auth/login', loginRateLimit);
  app.use('/api/auth/register', loginRateLimit);
  
  // 敏感操作限流
  app.use('/api/admin/', strictRateLimit);
  app.use('/api/users/delete', strictRateLimit);
  app.use('/api/users/update-password', strictRateLimit);
};

module.exports = {
  applySecurity,
  corsOptions,
  helmetOptions,
  generalRateLimit,
  strictRateLimit,
  loginRateLimit,
  createRateLimit
};