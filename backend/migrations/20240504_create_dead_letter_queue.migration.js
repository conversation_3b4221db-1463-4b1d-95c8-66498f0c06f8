/**
 * 创建死信队列表迁移脚本
 * 
 * 此脚本创建dead_letter_queue表，用于存储处理失败的事件
 */

const Migration = require('./Migration');

class CreateDeadLetterQueueMigration extends Migration {
  constructor() {
    super('20240504_create_dead_letter_queue');
  }

  /**
   * 向前迁移：创建dead_letter_queue表
   * @param {Transaction} transaction - 事务对象
   */
  async up(transaction) {
    this.logger.info('开始创建dead_letter_queue表...');

    // 检查表是否已存在
    const [tables] = await this.sequelize.query(
      "SHOW TABLES LIKE 'dead_letter_queue'",
      { transaction }
    );

    // 如果表不存在，创建表
    if (tables.length === 0) {
      await this.sequelize.query(`
        CREATE TABLE dead_letter_queue (
          id INT NOT NULL AUTO_INCREMENT,
          event_id VARCHAR(36) NOT NULL COMMENT '事件ID',
          event_type VARCHAR(100) NOT NULL COMMENT '事件类型',
          handler_name VARCHAR(100) NOT NULL COMMENT '处理器名称',
          aggregate_id VARCHAR(36) NOT NULL COMMENT '聚合根ID',
          aggregate_type VARCHAR(100) NOT NULL COMMENT '聚合根类型',
          payload JSON NOT NULL COMMENT '事件数据',
          error_message TEXT NOT NULL COMMENT '错误信息',
          error_stack TEXT COMMENT '错误堆栈',
          retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
          max_retries INT NOT NULL DEFAULT 3 COMMENT '最大重试次数',
          next_retry_at DATETIME NULL COMMENT '下次重试时间',
          status ENUM('pending', 'retrying', 'failed', 'resolved') NOT NULL DEFAULT 'pending' COMMENT '状态：pending待处理，retrying重试中，failed失败，resolved已解决',
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          resolved_at DATETIME NULL COMMENT '解决时间',
          PRIMARY KEY (id),
          UNIQUE INDEX idx_event_handler (event_id, handler_name),
          INDEX idx_event_type (event_type),
          INDEX idx_status (status),
          INDEX idx_next_retry (next_retry_at),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        COMMENT='事件处理死信队列表';
      `, { transaction });

      this.logger.info('成功创建dead_letter_queue表');
    } else {
      this.logger.info('dead_letter_queue表已存在，跳过迁移');
    }
  }

  /**
   * 向后迁移：删除dead_letter_queue表
   * @param {Transaction} transaction - 事务对象
   */
  async down(transaction) {
    this.logger.info('开始删除dead_letter_queue表...');

    // 检查表是否存在
    const [tables] = await this.sequelize.query(
      "SHOW TABLES LIKE 'dead_letter_queue'",
      { transaction }
    );

    // 如果表存在，删除表
    if (tables.length > 0) {
      await this.sequelize.query(
        "DROP TABLE dead_letter_queue",
        { transaction }
      );

      this.logger.info('成功删除dead_letter_queue表');
    } else {
      this.logger.info('dead_letter_queue表不存在，跳过回滚');
    }
  }
}

module.exports = CreateDeadLetterQueueMigration;
