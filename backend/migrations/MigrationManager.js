/**
 * 迁移脚本管理器
 * 用于执行和回滚迁移
 */
const fs = require('fs');
const path = require('path');
const logger = require('../config/logger');
const { sequelize } = require('../config/database');

class MigrationManager {
  constructor() {
    this.migrationsDir = path.join(__dirname);
    this.migrations = [];
  }

  /**
   * 加载所有迁移脚本
   */
  async loadMigrations() {
    try {
      // 读取迁移目录
      const files = fs.readdirSync(this.migrationsDir);
      
      // 过滤出迁移脚本文件
      const migrationFiles = files.filter(file => 
        file.endsWith('.migration.js') && 
        !file.startsWith('_') &&
        fs.statSync(path.join(this.migrationsDir, file)).isFile()
      );
      
      // 加载迁移脚本
      this.migrations = migrationFiles.map(file => {
        const Migration = require(path.join(this.migrationsDir, file));
        return new Migration();
      });
      
      // 按名称排序
      this.migrations.sort((a, b) => {
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();
        return nameA < nameB ? -1 : nameA > nameB ? 1 : 0;
      });
      
      logger.info(`加载了 ${this.migrations.length} 个迁移脚本`);
    } catch (error) {
      logger.error(`加载迁移脚本失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行所有迁移
   */
  async migrateAll() {
    try {
      await this.loadMigrations();
      
      logger.info('开始执行所有迁移...');
      
      for (const migration of this.migrations) {
        await migration.execute();
      }
      
      logger.info('所有迁移执行完成');
    } catch (error) {
      logger.error(`执行迁移失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行指定迁移
   * @param {string} name - 迁移名称
   */
  async migrate(name) {
    try {
      await this.loadMigrations();
      
      const migration = this.migrations.find(m => m.name === name);
      
      if (!migration) {
        throw new Error(`找不到迁移: ${name}`);
      }
      
      await migration.execute();
    } catch (error) {
      logger.error(`执行迁移 ${name} 失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 回滚所有迁移
   */
  async revertAll() {
    try {
      await this.loadMigrations();
      
      logger.info('开始回滚所有迁移...');
      
      // 反向执行
      for (let i = this.migrations.length - 1; i >= 0; i--) {
        await this.migrations[i].revert();
      }
      
      logger.info('所有迁移回滚完成');
    } catch (error) {
      logger.error(`回滚迁移失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 回滚指定迁移
   * @param {string} name - 迁移名称
   */
  async revert(name) {
    try {
      await this.loadMigrations();
      
      const migration = this.migrations.find(m => m.name === name);
      
      if (!migration) {
        throw new Error(`找不到迁移: ${name}`);
      }
      
      await migration.revert();
    } catch (error) {
      logger.error(`回滚迁移 ${name} 失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取已执行的迁移
   * @returns {Promise<Array>} 已执行的迁移
   */
  async getExecutedMigrations() {
    try {
      // 检查迁移表是否存在
      const [tables] = await sequelize.query("SHOW TABLES LIKE 'Migrations'");
      
      if (tables.length === 0) {
        return [];
      }
      
      // 查询已执行的迁移
      const [migrations] = await sequelize.query(
        "SELECT * FROM Migrations ORDER BY executed_at"
      );
      
      return migrations;
    } catch (error) {
      logger.error(`获取已执行的迁移失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取待执行的迁移
   * @returns {Promise<Array>} 待执行的迁移
   */
  async getPendingMigrations() {
    try {
      await this.loadMigrations();
      
      // 获取已执行的迁移
      const executedMigrations = await this.getExecutedMigrations();
      const executedNames = executedMigrations.map(m => m.name);
      
      // 过滤出待执行的迁移
      const pendingMigrations = this.migrations.filter(
        m => !executedNames.includes(m.name)
      );
      
      return pendingMigrations;
    } catch (error) {
      logger.error(`获取待执行的迁移失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new MigrationManager();
