/**
 * 创建daily_contents表，用于存储AI生成的学习计划每日内容
 */
const { sequelize, testConnection } = require('../scripts/db-connection');
const logger = require('../config/logger');

async function up() {
  try {
    // 测试数据库连接
    const connected = await testConnection();
    if (!connected) {
      throw new Error('无法连接到数据库');
    }

    // 检查表是否已存在
    const [tables] = await sequelize.query(`
      SHOW TABLES LIKE 'daily_contents'
    `);

    // 如果表不存在，创建表
    if (tables.length === 0) {
      await sequelize.query(`
        CREATE TABLE daily_contents (
          id INT NOT NULL AUTO_INCREMENT,
          plan_id INT NOT NULL,
          day_number INT NOT NULL,
          title VARCHAR(100) NOT NULL,
          content TEXT NOT NULL,
          is_completed BOOLEAN NOT NULL DEFAULT FALSE,
          completion_date DATETIME NULL,
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          INDEX daily_content_plan_id_idx (plan_id),
          UNIQUE INDEX daily_content_plan_day_idx (plan_id, day_number),
          CONSTRAINT fk_daily_content_plan FOREIGN KEY (plan_id) REFERENCES learning_plans(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        COMMENT='学习计划每日内容表';
      `);

      logger.info('成功创建daily_contents表');
    } else {
      logger.info('daily_contents表已存在，跳过迁移');
    }
  } catch (error) {
    logger.error(`迁移失败: ${error.message}`);
    throw error;
  }
}

async function down() {
  try {
    // 测试数据库连接
    const connected = await testConnection();
    if (!connected) {
      throw new Error('无法连接到数据库');
    }

    // 检查表是否存在
    const [tables] = await sequelize.query(`
      SHOW TABLES LIKE 'daily_contents'
    `);

    // 如果表存在，删除表
    if (tables.length > 0) {
      await sequelize.query(`
        DROP TABLE daily_contents
      `);

      logger.info('成功删除daily_contents表');
    } else {
      logger.info('daily_contents表不存在，跳过回滚');
    }
  } catch (error) {
    logger.error(`回滚失败: ${error.message}`);
    throw error;
  }
}

// 如果直接运行此脚本，执行迁移
if (require.main === module) {
  up()
    .then(() => {
      console.log('迁移成功完成');
      process.exit(0);
    })
    .catch(error => {
      console.error(`迁移失败: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { up, down }; 