/**
 * 模拟AI服务路由
 */

const express = require('express');
const router = express.Router();

// 模拟AI生成内容
router.post('/generate', (req, res) => {
  const { prompt, maxTokens, temperature } = req.body;
  
  if (!prompt) {
    return res.status(400).json({
      success: false,
      message: '缺少提示词',
      error: 'MISSING_PROMPT'
    });
  }
  
  // 模拟AI生成延迟
  setTimeout(() => {
    // 根据提示词生成模拟内容
    let response = '';
    
    if (prompt.includes('学习计划')) {
      response = `
# 学习计划

## 第一周：基础知识
- 了解基本概念
- 学习核心原理
- 完成入门练习

## 第二周：进阶内容
- 深入理解关键技术
- 实践应用场景
- 解决常见问题

## 第三周：项目实践
- 设计项目方案
- 实现核心功能
- 测试和优化

## 总结
按照这个计划学习，可以系统地掌握相关知识和技能。
      `;
    } else if (prompt.includes('笔记')) {
      response = `
# 学习笔记

## 重要概念
- 概念A：核心基础，用于...
- 概念B：进阶内容，主要解决...
- 概念C：高级应用，适用于...

## 关键技术
1. 技术一：实现方法...
2. 技术二：使用场景...
3. 技术三：优缺点...

## 学习心得
这些内容对我的帮助很大，特别是在...方面有了新的理解。
      `;
    } else if (prompt.includes('问题') || prompt.includes('问答')) {
      response = `
这个问题的答案是：

首先，需要理解基本原理，即...
其次，应用这个原理到具体场景中...
最后，注意以下几个关键点：
1. 点一
2. 点二
3. 点三

希望这个回答对你有帮助！
      `;
    } else {
      response = `这是对"${prompt}"的AI生成回复。根据您的要求，我生成了这段文本内容。如果需要更详细的内容，请提供更具体的提示词。`;
    }
    
    res.json({
      success: true,
      message: 'AI生成成功',
      data: {
        text: response,
        usage: {
          promptTokens: prompt.length,
          completionTokens: response.length,
          totalTokens: prompt.length + response.length
        }
      }
    });
  }, 1000); // 模拟1秒延迟
});

// 模拟AI内容分析
router.post('/analyze', (req, res) => {
  const { content, type } = req.body;
  
  if (!content) {
    return res.status(400).json({
      success: false,
      message: '缺少内容',
      error: 'MISSING_CONTENT'
    });
  }
  
  // 模拟AI分析延迟
  setTimeout(() => {
    let analysis = {};
    
    switch (type) {
      case 'sentiment':
        // 情感分析
        analysis = {
          sentiment: Math.random() > 0.5 ? 'positive' : 'negative',
          score: Math.random().toFixed(2),
          keywords: ['关键词1', '关键词2', '关键词3']
        };
        break;
        
      case 'summary':
        // 内容摘要
        analysis = {
          summary: `这是对内容的摘要：${content.substring(0, 50)}...`,
          keyPoints: ['要点1', '要点2', '要点3']
        };
        break;
        
      case 'tags':
        // 标签推荐
        analysis = {
          tags: ['标签1', '标签2', '标签3', '标签4', '标签5'],
          confidence: Math.random().toFixed(2)
        };
        break;
        
      default:
        // 默认分析
        analysis = {
          length: content.length,
          wordCount: content.split(/\s+/).length,
          readability: 'medium'
        };
    }
    
    res.json({
      success: true,
      message: 'AI分析成功',
      data: analysis
    });
  }, 800); // 模拟0.8秒延迟
});

// 模拟AI聊天
router.post('/chat', (req, res) => {
  const { messages, model } = req.body;
  
  if (!messages || !Array.isArray(messages)) {
    return res.status(400).json({
      success: false,
      message: '缺少消息或格式错误',
      error: 'INVALID_MESSAGES'
    });
  }
  
  // 模拟AI聊天延迟
  setTimeout(() => {
    // 获取最后一条用户消息
    const lastUserMessage = messages.filter(m => m.role === 'user').pop();
    let response = '';
    
    if (lastUserMessage) {
      const content = lastUserMessage.content.toLowerCase();
      
      if (content.includes('你好') || content.includes('hello')) {
        response = '你好！我是AI助手，有什么可以帮助你的吗？';
      } else if (content.includes('学习') || content.includes('学习计划')) {
        response = '学习是一个持续的过程。建议你制定明确的学习目标，分解为小任务，并保持规律的学习习惯。需要我帮你制定一个学习计划吗？';
      } else if (content.includes('谢谢') || content.includes('感谢')) {
        response = '不客气！如果还有其他问题，随时可以问我。';
      } else {
        response = `我理解你的问题是关于"${lastUserMessage.content.substring(0, 20)}..."。这是一个很好的问题，让我来回答...`;
      }
    } else {
      response = '你好！我是AI助手，有什么可以帮助你的吗？';
    }
    
    res.json({
      success: true,
      message: 'AI聊天成功',
      data: {
        message: {
          role: 'assistant',
          content: response
        },
        usage: {
          promptTokens: JSON.stringify(messages).length,
          completionTokens: response.length,
          totalTokens: JSON.stringify(messages).length + response.length
        }
      }
    });
  }, 1200); // 模拟1.2秒延迟
});

module.exports = router;
