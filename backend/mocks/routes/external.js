/**
 * 模拟外部API路由
 */

const express = require('express');
const router = express.Router();

// 模拟天气API
router.get('/weather', (req, res) => {
  const { city } = req.query;
  
  if (!city) {
    return res.status(400).json({
      success: false,
      message: '缺少城市参数',
      error: 'MISSING_CITY'
    });
  }
  
  // 模拟天气数据
  const weather = {
    city,
    temperature: Math.floor(Math.random() * 30) + 5, // 5-35度
    condition: ['晴朗', '多云', '阴天', '小雨', '大雨'][Math.floor(Math.random() * 5)],
    humidity: Math.floor(Math.random() * 50) + 30, // 30-80%
    wind: Math.floor(Math.random() * 30) + 5, // 5-35km/h
    forecast: [
      { day: '今天', temperature: [15, 25], condition: '晴朗' },
      { day: '明天', temperature: [14, 24], condition: '多云' },
      { day: '后天', temperature: [13, 23], condition: '小雨' }
    ]
  };
  
  res.json({
    success: true,
    message: '获取天气数据成功',
    data: weather
  });
});

// 模拟新闻API
router.get('/news', (req, res) => {
  const { category, limit } = req.query;
  const pageSize = parseInt(limit) || 10;
  
  // 模拟新闻数据
  const newsCategories = {
    technology: [
      { id: 1, title: '新AI技术突破', summary: '研究人员开发出新的AI模型，性能提升50%。', date: '2025-06-01' },
      { id: 2, title: '量子计算新进展', summary: '科学家实现了100量子比特的稳定操作。', date: '2025-05-28' },
      { id: 3, title: '5G覆盖率达到新高', summary: '全国5G网络覆盖率已达95%，用户体验大幅提升。', date: '2025-05-25' }
    ],
    business: [
      { id: 4, title: '科技巨头发布季度财报', summary: '多家科技公司业绩超预期，股价上涨。', date: '2025-06-02' },
      { id: 5, title: '新创企业融资创新高', summary: '今年第二季度创业公司融资总额突破历史记录。', date: '2025-05-30' },
      { id: 6, title: '全球供应链持续恢复', summary: '全球供应链问题逐渐缓解，物流成本下降。', date: '2025-05-27' }
    ],
    education: [
      { id: 7, title: '在线教育平台用户增长', summary: '主要在线教育平台用户数量同比增长30%。', date: '2025-06-03' },
      { id: 8, title: '新教育政策出台', summary: '政府发布新的教育改革方案，强调素质教育。', date: '2025-05-29' },
      { id: 9, title: '大学录取率创新高', summary: '今年高校录取率达到历史新高，教育机会更加普及。', date: '2025-05-26' }
    ]
  };
  
  // 获取指定类别的新闻，如果没有指定类别，则返回所有新闻
  let news = [];
  if (category && newsCategories[category]) {
    news = newsCategories[category];
  } else {
    // 合并所有类别的新闻
    Object.values(newsCategories).forEach(categoryNews => {
      news = news.concat(categoryNews);
    });
  }
  
  // 限制返回数量
  news = news.slice(0, pageSize);
  
  res.json({
    success: true,
    message: '获取新闻数据成功',
    data: {
      news,
      total: news.length,
      category: category || 'all'
    }
  });
});

// 模拟翻译API
router.post('/translate', (req, res) => {
  const { text, targetLanguage } = req.body;
  
  if (!text) {
    return res.status(400).json({
      success: false,
      message: '缺少文本',
      error: 'MISSING_TEXT'
    });
  }
  
  if (!targetLanguage) {
    return res.status(400).json({
      success: false,
      message: '缺少目标语言',
      error: 'MISSING_TARGET_LANGUAGE'
    });
  }
  
  // 模拟翻译
  let translatedText = '';
  
  switch (targetLanguage) {
    case 'en':
      translatedText = `[English Translation] ${text}`;
      break;
    case 'zh':
      translatedText = `[中文翻译] ${text}`;
      break;
    case 'ja':
      translatedText = `[日本語翻訳] ${text}`;
      break;
    case 'ko':
      translatedText = `[한국어 번역] ${text}`;
      break;
    case 'fr':
      translatedText = `[Traduction française] ${text}`;
      break;
    default:
      translatedText = `[${targetLanguage} Translation] ${text}`;
  }
  
  res.json({
    success: true,
    message: '翻译成功',
    data: {
      originalText: text,
      translatedText,
      targetLanguage
    }
  });
});

module.exports = router;
