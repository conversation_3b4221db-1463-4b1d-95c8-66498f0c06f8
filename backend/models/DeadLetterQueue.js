/**
 * 死信队列模型
 * 用于存储处理失败的事件
 */
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const DeadLetterQueue = sequelize.define('DeadLetterQueue', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true
  },
  eventId: {
    type: DataTypes.STRING(36),
    allowNull: false,
    field: 'event_id',
    comment: '事件ID'
  },
  eventType: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'event_type',
    comment: '事件类型'
  },
  handlerName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'handler_name',
    comment: '处理器名称'
  },
  aggregateId: {
    type: DataTypes.STRING(36),
    allowNull: false,
    field: 'aggregate_id',
    comment: '聚合根ID'
  },
  aggregateType: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'aggregate_type',
    comment: '聚合根类型'
  },
  payload: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '事件数据'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: false,
    field: 'error_message',
    comment: '错误信息'
  },
  errorStack: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_stack',
    comment: '错误堆栈'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'retry_count',
    comment: '重试次数'
  },
  maxRetries: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    field: 'max_retries',
    comment: '最大重试次数'
  },
  nextRetryAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'next_retry_at',
    comment: '下次重试时间'
  },
  status: {
    type: DataTypes.ENUM('pending', 'retrying', 'failed', 'resolved'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态：pending待处理，retrying重试中，failed失败，resolved已解决'
  },
  resolvedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'resolved_at',
    comment: '解决时间'
  }
}, {
  tableName: 'dead_letter_queue',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['event_id', 'handler_name'],
      name: 'idx_event_handler'
    },
    {
      fields: ['event_type'],
      name: 'idx_event_type'
    },
    {
      fields: ['status'],
      name: 'idx_status'
    },
    {
      fields: ['next_retry_at'],
      name: 'idx_next_retry'
    },
    {
      fields: ['created_at'],
      name: 'idx_created_at'
    }
  ]
});

module.exports = DeadLetterQueue;
