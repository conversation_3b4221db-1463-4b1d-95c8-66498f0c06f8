const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Achievement = sequelize.define('Achievement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '成就ID'
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '成就名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '成就描述'
  },
  icon: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '成就图标'
  },
  category: {
    type: DataTypes.ENUM('learning', 'social', 'creation', 'special'),
    allowNull: false,
    comment: '成就类别'
  },
  difficulty: {
    type: DataTypes.ENUM('easy', 'medium', 'hard', 'expert'),
    allowNull: false,
    comment: '难度'
  },
  points: {
    type: DataTypes.INTEGER,
    defaultValue: 10,
    comment: '获得点数'
  },
  condition_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '触发条件类型 (如 complete_exercises, consecutive_days)'
  },
  condition_value: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '触发条件值 (JSON结构需定义，未来可考虑拆分)'
  },
  is_hidden: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否隐藏成就'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  }
}, {
  tableName: 'achievement',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_category',
      fields: ['category']
    },
    {
      name: 'idx_difficulty',
      fields: ['difficulty']
    },
    {
      name: 'idx_is_hidden',
      fields: ['is_hidden']
    },
    {
      name: 'idx_is_active',
      fields: ['is_active']
    }
  ]
});

module.exports = Achievement;
