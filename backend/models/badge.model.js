const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Badge = sequelize.define('Badge', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '徽章ID'
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '徽章名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '徽章描述'
  },
  icon: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '徽章图标'
  },
  category: {
    type: DataTypes.ENUM('theme', 'skill', 'event', 'special'),
    allowNull: false,
    comment: '徽章类别'
  },
  rarity: {
    type: DataTypes.ENUM('common', 'uncommon', 'rare', 'epic', 'legendary'),
    defaultValue: 'common',
    comment: '稀有度'
  },
  is_displayable: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否可在个人资料页展示'
  },
  unlock_condition: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '解锁条件描述 (给用户看)'
  }
}, {
  tableName: 'badge',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_category',
      fields: ['category']
    },
    {
      name: 'idx_rarity',
      fields: ['rarity']
    }
  ]
});

module.exports = Badge;
