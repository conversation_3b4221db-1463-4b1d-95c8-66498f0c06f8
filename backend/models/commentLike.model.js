const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CommentLike = sequelize.define('CommentLike', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '评论点赞ID'
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '用户ID'
  },
  comment_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '评论ID'
  }
}, {
  tableName: 'comment_like',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    {
      name: 'idx_user_id',
      fields: ['user_id']
    },
    {
      name: 'idx_comment_id',
      fields: ['comment_id']
    },
    {
      name: 'uk_user_comment',
      unique: true,
      fields: ['user_id', 'comment_id']
    }
  ]
});

module.exports = CommentLike;
