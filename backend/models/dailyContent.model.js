const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 每日学习内容模型
 * 存储AI生成的学习计划每日内容
 */
const DailyContent = sequelize.define('DailyContent', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: '内容ID'
  },
  plan_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '所属学习计划ID',
    references: {
      model: 'learning_plan',
      key: 'id'
    }
  },
  day_number: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '天数（第几天）'
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '日内容标题'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '日内容详情'
  },
  summary: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '内容摘要'
  },
  difficulty: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
    defaultValue: 'intermediate',
    comment: '难度级别'
  },
  estimated_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '预计完成时间(分钟)'
  },
  is_completed: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已完成'
  },
  completion_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '完成日期'
  },
  status: {
    type: DataTypes.ENUM('pending', 'active', 'completed', 'skipped'),
    defaultValue: 'pending',
    comment: '状态'
  }
}, {
  tableName: 'daily_content',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  deletedAt: 'deleted_at',
  paranoid: true, // 启用软删除
  indexes: [
    {
      name: 'idx_plan_id',
      fields: ['plan_id']
    },
    {
      name: 'uk_plan_day',
      fields: ['plan_id', 'day_number'],
      unique: true
    },
    {
      name: 'idx_is_completed',
      fields: ['is_completed']
    },
    {
      name: 'idx_status',
      fields: ['status']
    },
    {
      name: 'idx_deleted_at',
      fields: ['deleted_at']
    },
    {
      name: 'fulltext_title_content',
      type: 'FULLTEXT',
      fields: ['title', 'content']
    }
  ]
});

module.exports = DailyContent;