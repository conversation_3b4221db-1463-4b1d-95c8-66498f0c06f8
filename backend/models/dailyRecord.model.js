const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 每日学习记录模型
 * 记录用户每天的学习统计和成就
 */
const DailyRecord = sequelize.define('DailyRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '记录ID'
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '用户ID',
    references: {
      model: 'user',
      key: 'id'
    }
  },
  record_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '记录日期',
    defaultValue: DataTypes.NOW
  },
  total_time_minutes: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '总学习时间(分钟)'
  },
  exercises_completed: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '完成练习数量'
  },
  insights_viewed: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '查看观点数量'
  },
  notes_created: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '创建笔记数量'
  },
  exp_gained: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '获得的经验值'
  },
  streak_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '连续学习天数'
  },
  core_completed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否完成核心任务'
  },
  daily_goal_completed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否完成每日目标'
  },
  mood: {
    type: DataTypes.ENUM('great', 'good', 'neutral', 'bad', 'terrible'),
    allowNull: true,
    comment: '心情记录'
  },
  summary: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '每日总结'
  }
}, {
  tableName: 'daily_record',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_user_id',
      fields: ['user_id']
    },
    {
      name: 'idx_record_date',
      fields: ['record_date']
    },
    {
      name: 'uk_user_date',
      unique: true,
      fields: ['user_id', 'record_date']
    },
    {
      name: 'idx_streak_days',
      fields: ['streak_days']
    },
    {
      name: 'idx_core_completed',
      fields: ['core_completed']
    },
    {
      name: 'idx_daily_goal_completed',
      fields: ['daily_goal_completed']
    }
  ]
});

module.exports = DailyRecord;
