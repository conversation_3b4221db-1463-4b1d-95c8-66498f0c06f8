const { sequelize } = require('../config/database');

// 导入模型
const User = require('./user.model');
const Level = require('./level.model');
const UserSetting = require('./userSetting.model');
const UserNotificationSetting = require('./userNotificationSetting.model');
const Theme = require('./theme.model');
const LearningTemplate = require('./learningTemplate.model');
const LearningPlan = require('./learningPlan.model');
const Tag = require('./tag.model');
const PlanTag = require('./planTag.model');
const Exercise = require('./exercise.model');
const Insight = require('./insight.model');
const Note = require('./note.model');
const NoteLike = require('./noteLike.model');
const TagLike = require('./tagLike.model');
const NoteComment = require('./noteComment.model');
const CommentLike = require('./commentLike.model');
const UserFollow = require('./userFollow.model');
const TagCategory = require('./tagCategory.model');
const TagSynonym = require('./tagSynonym.model');
const TagFeedback = require('./tagFeedback.model');
const LearningActivity = require('./learningActivity.model');
const DailyRecord = require('./dailyRecord.model');
const DailyContent = require('./dailyContent.model');
const DailyContentRelation = require('./dailyContentRelation.model');
const UserContentProgress = require('./userContentProgress.model');
const UserLearningStats = require('./userLearningStats.model');

// 定义模型之间的关联关系

// Level - User: 一对多
Level.hasMany(User, {
  foreignKey: 'level_id',
  as: 'users'
});
User.belongsTo(Level, {
  foreignKey: 'level_id',
  as: 'level'
});

// User - UserSetting: 一对一
User.hasOne(UserSetting, {
  foreignKey: 'user_id',
  as: 'settings'
});
UserSetting.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// User - UserNotificationSetting: 一对多
User.hasMany(UserNotificationSetting, {
  foreignKey: 'user_id',
  as: 'notificationSettings'
});
UserNotificationSetting.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// User - LearningPlan: 一对多
User.hasMany(LearningPlan, {
  foreignKey: 'user_id',
  as: 'learningPlans'
});
LearningPlan.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// Theme - LearningTemplate: 一对多
Theme.hasMany(LearningTemplate, {
  foreignKey: 'theme_id',
  as: 'learningTemplates'
});
LearningTemplate.belongsTo(Theme, {
  foreignKey: 'theme_id',
  as: 'theme'
});

// User - LearningTemplate: 一对多 (创建者关系)
User.hasMany(LearningTemplate, {
  foreignKey: 'creator_id',
  as: 'createdTemplates'
});
LearningTemplate.belongsTo(User, {
  foreignKey: 'creator_id',
  as: 'creator'
});

// LearningTemplate - LearningPlan: 一对多
LearningTemplate.hasMany(LearningPlan, {
  foreignKey: 'template_id',
  as: 'learningPlans'
});
LearningPlan.belongsTo(LearningTemplate, {
  foreignKey: 'template_id',
  as: 'template'
});

// Theme - LearningPlan: 一对多
Theme.hasMany(LearningPlan, {
  foreignKey: 'theme_id',
  as: 'learningPlans'
});
LearningPlan.belongsTo(Theme, {
  foreignKey: 'theme_id',
  as: 'theme'
});

// LearningPlan - Tag: 多对多（通过PlanTag）
LearningPlan.belongsToMany(Tag, {
  through: PlanTag,
  foreignKey: 'plan_id',
  otherKey: 'tag_id',
  as: 'associatedTags'
});
Tag.belongsToMany(LearningPlan, {
  through: PlanTag,
  foreignKey: 'tag_id',
  otherKey: 'plan_id',
  as: 'associatedPlans'
});

// PlanTag关联
LearningPlan.hasMany(PlanTag, {
  foreignKey: 'plan_id',
  as: 'planTags'
});
PlanTag.belongsTo(LearningPlan, {
  foreignKey: 'plan_id',
  as: 'plan'
});

Tag.hasMany(PlanTag, {
  foreignKey: 'tag_id',
  as: 'tagPlans'
});
PlanTag.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// LearningPlan - DailyContent: 一对多
LearningPlan.hasMany(DailyContent, {
  foreignKey: 'plan_id',
  as: 'dailyContents'
});
DailyContent.belongsTo(LearningPlan, {
  foreignKey: 'plan_id',
  as: 'learningPlan'
});

// DailyContent - DailyContentRelation: 一对多
DailyContent.hasMany(DailyContentRelation, {
  foreignKey: 'daily_content_id',
  as: 'contentRelations'
});
DailyContentRelation.belongsTo(DailyContent, {
  foreignKey: 'daily_content_id',
  as: 'dailyContent'
});

// User - UserContentProgress: 一对多
User.hasMany(UserContentProgress, {
  foreignKey: 'user_id',
  as: 'contentProgresses'
});
UserContentProgress.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// LearningPlan - UserContentProgress: 一对多
LearningPlan.hasMany(UserContentProgress, {
  foreignKey: 'plan_id',
  as: 'contentProgresses'
});
UserContentProgress.belongsTo(LearningPlan, {
  foreignKey: 'plan_id',
  as: 'learningPlan'
});

// User - UserLearningStats: 一对一
User.hasOne(UserLearningStats, {
  foreignKey: 'user_id',
  as: 'learningStats'
});
UserLearningStats.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 保留原有关联关系...
// Tag - Exercise: 一对多
Tag.hasMany(Exercise, {
  foreignKey: 'tag_id',
  as: 'exercises'
});
Exercise.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// Tag - Insight: 一对多
Tag.hasMany(Insight, {
  foreignKey: 'tag_id',
  as: 'insights'
});
Insight.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// Tag - Note: 一对多
Tag.hasMany(Note, {
  foreignKey: 'tag_id',
  as: 'notes'
});
Note.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// User - Note: 一对多
User.hasMany(Note, {
  foreignKey: 'user_id',
  as: 'notes'
});
Note.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 导出模型
module.exports = {
  User,
  Level,
  UserSetting,
  UserNotificationSetting,
  Theme,
  LearningTemplate,
  LearningPlan,
  Tag,
  PlanTag,
  Exercise,
  Insight,
  Note,
  NoteLike,
  TagLike,
  NoteComment,
  CommentLike,
  UserFollow,
  TagCategory,
  TagSynonym,
  TagFeedback,
  LearningActivity,
  DailyRecord,
  DailyContent,
  DailyContentRelation,
  UserContentProgress,
  UserLearningStats
};
