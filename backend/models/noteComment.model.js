const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 笔记评论模型
 * 记录用户对笔记的评论
 */
const NoteComment = sequelize.define('NoteComment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '评论ID'
  },
  note_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '笔记ID',
    references: {
      model: 'note',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '用户ID',
    references: {
      model: 'user',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '评论内容'
  },
  parent_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '父评论ID，用于回复',
    references: {
      model: 'note_comment',
      key: 'id'
    },
    onDelete: 'SET NULL'
  },
  like_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '点赞数 (聚合数据)'
  },
  status: {
    type: DataTypes.ENUM('active', 'hidden', 'deleted'),
    defaultValue: 'active',
    comment: '状态'
  }
}, {
  tableName: 'note_comment',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  deletedAt: 'deleted_at',
  paranoid: true, // 启用软删除
  indexes: [
    {
      name: 'idx_note_id',
      fields: ['note_id']
    },
    {
      name: 'idx_user_id',
      fields: ['user_id']
    },
    {
      name: 'idx_parent_id',
      fields: ['parent_id']
    },
    {
      name: 'idx_status',
      fields: ['status']
    },
    {
      name: 'idx_created_at',
      fields: ['created_at']
    },
    {
      name: 'idx_deleted_at',
      fields: ['deleted_at']
    }
  ]
});

module.exports = NoteComment;
