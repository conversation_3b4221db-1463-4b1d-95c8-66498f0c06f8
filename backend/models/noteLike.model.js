const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 笔记点赞模型
 * 记录用户对笔记的点赞
 */
const NoteLike = sequelize.define('NoteLike', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '点赞ID'
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '用户ID',
    references: {
      model: 'user',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  note_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '笔记ID',
    references: {
      model: 'note',
      key: 'id'
    },
    onDelete: 'CASCADE'
  }
}, {
  tableName: 'note_like',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    {
      name: 'uk_user_note',
      unique: true,
      fields: ['user_id', 'note_id']
    },
    {
      name: 'idx_user_id',
      fields: ['user_id']
    },
    {
      name: 'idx_note_id',
      fields: ['note_id']
    },
    {
      name: 'idx_created_at',
      fields: ['created_at']
    }
  ]
});

module.exports = NoteLike;
