const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '通知ID'
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '接收用户ID'
  },
  sender_id: {
    type: DataTypes.BIGINT,
    allowNull: true,
    comment: '发送者ID (NULL表示系统)'
  },
  notification_type: {
    type: DataTypes.ENUM('like', 'comment', 'follow', 'achievement', 'system', 'reminder', 'badge', 'level_up'),
    allowNull: false,
    comment: '通知类型'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '通知内容'
  },
  reference_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '引用对象类型 (如 note, comment, user, achievement)'
  },
  reference_id: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '引用对象ID'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已读'
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '阅读时间'
  }
}, {
  tableName: 'notification',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    {
      name: 'idx_user_id',
      fields: ['user_id']
    },
    {
      name: 'idx_sender_id',
      fields: ['sender_id']
    },
    {
      name: 'idx_notification_type',
      fields: ['notification_type']
    },
    {
      name: 'idx_is_read',
      fields: ['is_read']
    },
    {
      name: 'idx_created_at',
      fields: ['created_at']
    }
  ]
});

module.exports = Notification;
