/**
 * 密码重置令牌模型
 * 用于存储密码重置令牌信息
 */
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const PasswordResetToken = sequelize.define('PasswordResetToken', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '主键ID'
    },
    user_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '用户ID'
    },
    token: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '重置令牌'
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '过期时间'
    },
    used: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否已使用'
    },
    ip_address: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '请求IP地址'
    },
    user_agent: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '用户代理'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'password_reset_token',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        name: 'idx_password_reset_token_user_id',
        fields: ['user_id']
      },
      {
        name: 'idx_password_reset_token_token',
        fields: ['token'],
        unique: true
      },
      {
        name: 'idx_password_reset_token_expires_at',
        fields: ['expires_at']
      }
    ]
  });

  PasswordResetToken.associate = (models) => {
    // 与用户模型关联
    PasswordResetToken.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  return PasswordResetToken;
};
