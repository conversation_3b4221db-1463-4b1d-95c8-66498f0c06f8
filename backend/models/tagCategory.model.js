const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 标签分类模型
 * 用于管理标签的层级结构
 */
const TagCategory = sequelize.define('TagCategory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '分类ID'
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '分类名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '分类描述'
  },
  parent_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '父分类ID，顶级分类为null',
    references: {
      model: 'tag_category',
      key: 'id'
    },
    onDelete: 'SET NULL'
  },
  theme_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '所属主题ID',
    references: {
      model: 'theme',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '层级，1为顶级分类，2为二级分类，以此类推'
  },
  icon: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '分类图标'
  },
  color: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '分类颜色'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序顺序'
  }
}, {
  tableName: 'tag_category',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  deletedAt: 'deleted_at',
  paranoid: true, // 启用软删除
  indexes: [
    {
      name: 'idx_name',
      fields: ['name']
    },
    {
      name: 'idx_parent_id',
      fields: ['parent_id']
    },
    {
      name: 'idx_theme_id',
      fields: ['theme_id']
    },
    {
      name: 'idx_level',
      fields: ['level']
    },
    {
      name: 'idx_is_active',
      fields: ['is_active']
    },
    {
      name: 'idx_deleted_at',
      fields: ['deleted_at']
    }
  ]
});

module.exports = TagCategory;
