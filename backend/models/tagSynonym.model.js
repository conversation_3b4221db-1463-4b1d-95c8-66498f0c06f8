const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 标签同义词模型
 * 用于管理标签的同义词关系
 */
const TagSynonym = sequelize.define('TagSynonym', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  primary_tag_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '主标签ID',
    references: {
      model: 'Tag',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  synonym_name: {
    type: DataTypes.STRING(10),
    allowNull: false,
    comment: '同义词名称'
  },
  similarity_score: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0.8,
    comment: '相似度得分(0-1)'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'TagSynonym',
  timestamps: false,
  indexes: [
    {
      fields: ['primary_tag_id'],
      name: 'idx_primary_tag_id'
    },
    {
      fields: ['synonym_name'],
      name: 'idx_synonym_name'
    }
  ]
});

module.exports = TagSynonym;
