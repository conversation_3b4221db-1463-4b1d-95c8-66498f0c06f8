const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '用户唯一标识，使用自增BIGINT'
  },
  openid: {
    type: DataTypes.STRING(64),
    allowNull: true,
    unique: true,
    comment: '微信OpenID'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true,
    comment: '手机号'
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '用户昵称'
  },
  avatar_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '头像URL'
  },
  gender: {
    type: DataTypes.TINYINT,
    allowNull: true,
    comment: '性别：0未知，1男，2女'
  },
  password_hash: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '密码哈希'
  },
  login_type: {
    type: DataTypes.ENUM('wechat', 'phone', 'github', 'apple'),
    allowNull: true,
    comment: '最近一次登录类型'
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后登录时间'
  },
  study_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '学习天数 (聚合数据)'
  },
  level_id: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '用户等级ID'
  },
  exp_points: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '经验值'
  },
  is_admin: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否管理员'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'banned'),
    defaultValue: 'active',
    comment: '用户状态'
  }
}, {
  tableName: 'user',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  deletedAt: 'deleted_at',
  paranoid: true, // 启用软删除
  indexes: [
    {
      name: 'idx_nickname',
      fields: ['nickname']
    },
    {
      name: 'idx_level_id',
      fields: ['level_id']
    },
    {
      name: 'idx_created_at',
      fields: ['created_at']
    },
    {
      name: 'idx_deleted_at',
      fields: ['deleted_at']
    }
  ]
});

module.exports = User;
