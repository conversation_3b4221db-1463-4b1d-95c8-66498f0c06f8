const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 用户功能访问模型
 * 用于管理用户对特定功能的访问权限
 */
const UserFeatureAccess = sequelize.define('UserFeatureAccess', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '访问权限ID'
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '用户ID'
  },
  feature_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '功能ID'
  },
  is_granted: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否授予访问权限'
  },
  granted_by: {
    type: DataTypes.BIGINT,
    allowNull: true,
    comment: '授权人ID'
  },
  granted_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '授权时间'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '过期时间，为空表示永久有效'
  },
  reason: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '授权原因'
  }
}, {
  tableName: 'user_feature_access',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'uk_user_feature',
      unique: true,
      fields: ['user_id', 'feature_id']
    },
    {
      name: 'idx_user_id',
      fields: ['user_id']
    },
    {
      name: 'idx_feature_id',
      fields: ['feature_id']
    },
    {
      name: 'idx_is_granted',
      fields: ['is_granted']
    },
    {
      name: 'idx_expires_at',
      fields: ['expires_at']
    }
  ]
});

module.exports = UserFeatureAccess;
