const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 用户关注模型
 * 记录用户之间的关注关系
 */
const UserFollow = sequelize.define('UserFollow', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '关注ID'
  },
  follower_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '关注者ID',
    references: {
      model: 'user',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  following_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '被关注者ID',
    references: {
      model: 'user',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  status: {
    type: DataTypes.ENUM('active', 'blocked'),
    defaultValue: 'active',
    comment: '关注状态'
  }
}, {
  tableName: 'user_follow',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'uk_follower_following',
      unique: true,
      fields: ['follower_id', 'following_id']
    },
    {
      name: 'idx_follower_id',
      fields: ['follower_id']
    },
    {
      name: 'idx_following_id',
      fields: ['following_id']
    },
    {
      name: 'idx_status',
      fields: ['status']
    },
    {
      name: 'idx_created_at',
      fields: ['created_at']
    }
  ]
});

module.exports = UserFollow;
