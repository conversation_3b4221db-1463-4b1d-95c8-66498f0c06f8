const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserSetting = sequelize.define('UserSetting', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '用户ID'
  },
  theme_preference: {
    type: DataTypes.STRING(20),
    defaultValue: 'system',
    comment: '主题偏好：light/dark/system'
  },
  privacy_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '隐私设置 (JSON 结构需定义，未来可考虑拆分)'
  },
  learning_preferences: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '学习偏好设置 (JSON 结构需定义，未来可考虑拆分)'
  }
}, {
  tableName: 'user_setting',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'uk_user_id',
      unique: true,
      fields: ['user_id']
    }
  ]
});

module.exports = UserSetting;
