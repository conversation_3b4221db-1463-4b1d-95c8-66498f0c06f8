/**
 * 基础仓库类
 * 提供通用的数据访问方法，支持软删除和数据转换
 */
const { transformToCamel, transformToSnake } = require('../utils/dataTransformer');
const logger = require('../config/logger');
class BaseRepository {
  /**
   * 构造函数
   * @param {Object} model - Sequelize模型
   */
  constructor(model) {
    this.model = model;
    // 检查模型是否支持软删除
    this.supportsSoftDelete = model.options && model.options.paranoid === true;
  }

  /**
   * 根据ID查找记录
   * @param {number|string} id - 记录ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的记录
   * @param {boolean} [options.raw=true] - 是否返回原始数据
   * @param {boolean} [options.transform=true] - 是否转换字段命名
   * @returns {Promise<Object>} 查询结果
   */
  async findById(id, options = {}) {
    try {
      const { withDeleted = false, raw = true, transform = true, ...otherOptions } = options;

      let findOptions = {
        ...otherOptions,
        raw
      };

      // 如果模型支持软删除且需要包含已删除记录
      if (this.supportsSoftDelete && withDeleted) {
        findOptions.paranoid = false;
      }

      const record = await this.model.findByPk(id, findOptions);

      if (!record) {
        return null;
      }

      // 转换为camelCase
      return transform ? transformToCamel(record) : record;
    } catch (error) {
      logger.error(`[${this.model.name}Repository] findById error:`, error);
      throw error;
    }
  }

  /**
   * 根据条件查找单个记录
   * @param {Object} where - 查询条件
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的记录
   * @param {boolean} [options.raw=true] - 是否返回原始数据
   * @param {boolean} [options.transform=true] - 是否转换字段命名
   * @returns {Promise<Object>} 查询结果
   */
  async findOne(where, options = {}) {
    try {
      const { withDeleted = false, raw = true, transform = true, ...otherOptions } = options;

      let findOptions = {
        where,
        ...otherOptions,
        raw
      };

      // 如果模型支持软删除且需要包含已删除记录
      if (this.supportsSoftDelete && withDeleted) {
        findOptions.paranoid = false;
      }

      const record = await this.model.findOne(findOptions);

      if (!record) {
        return null;
      }

      // 转换为camelCase
      return transform ? transformToCamel(record) : record;
    } catch (error) {
      logger.error(`[${this.model.name}Repository] findOne error:`, error);
      throw error;
    }
  }

  /**
   * 查找所有符合条件的记录
   * @param {Object} where - 查询条件
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的记录
   * @param {boolean} [options.raw=true] - 是否返回原始数据
   * @param {boolean} [options.transform=true] - 是否转换字段命名
   * @returns {Promise<Array>} 查询结果
   */
  async findAll(where = {}, options = {}) {
    try {
      const { withDeleted = false, raw = true, transform = true, ...otherOptions } = options;

      let findOptions = {
        where,
        ...otherOptions,
        raw
      };

      // 如果模型支持软删除且需要包含已删除记录
      if (this.supportsSoftDelete && withDeleted) {
        findOptions.paranoid = false;
      }

      const records = await this.model.findAll(findOptions);

      // 转换为camelCase
      return transform ? transformToCamel(records) : records;
    } catch (error) {
      logger.error(`[${this.model.name}Repository] findAll error:`, error);
      throw error;
    }
  }

  /**
   * 分页查询
   * @param {Object} where - 查询条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的记录
   * @param {boolean} [options.raw=true] - 是否返回原始数据
   * @param {boolean} [options.transform=true] - 是否转换字段命名
   * @returns {Promise<Object>} 查询结果和分页信息
   */
  async findAndCountAll(where = {}, page = 1, pageSize = 10, options = {}) {
    try {
      const { withDeleted = false, raw = true, transform = true, ...otherOptions } = options;
      const offset = (page - 1) * pageSize;
      const limit = pageSize;

      // 如果模型支持软删除且需要包含已删除记录
      let queryOptions = {
        where,
        offset,
        limit,
        ...otherOptions,
        raw
      };

      if (this.supportsSoftDelete && withDeleted) {
        queryOptions.paranoid = false;
      }

      const result = await this.model.findAndCountAll(queryOptions);

      // 转换为camelCase
      const rows = transform ? transformToCamel(result.rows) : result.rows;

      return {
        rows,
        count: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize)
      };
    } catch (error) {
      logger.error(`[${this.model.name}Repository] findAndCountAll error:`, error);
      throw error;
    }
  }

  /**
   * 创建记录
   * @param {Object} data - 记录数据
   * @param {Object} options - 创建选项
   * @param {boolean} [options.transform=true] - 是否转换字段命名
   * @returns {Promise<Object>} 创建的记录
   */
  async create(data, options = {}) {
    try {
      const { transform = true, ...otherOptions } = options;

      // 转换为snake_case
      const snakeData = transform ? transformToSnake(data) : data;

      const record = await this.model.create(snakeData, otherOptions);

      // 转换为camelCase
      return transform ? transformToCamel(record.get({ plain: true })) : record;
    } catch (error) {
      logger.error(`[${this.model.name}Repository] create error:`, error);
      throw error;
    }
  }

  /**
   * 批量创建记录
   * @param {Array} dataArray - 记录数据数组
   * @param {Object} options - 创建选项
   * @param {boolean} [options.transform=true] - 是否转换字段命名
   * @returns {Promise<Array>} 创建的记录数组
   */
  async bulkCreate(dataArray, options = {}) {
    try {
      const { transform = true, ...otherOptions } = options;

      // 转换为snake_case
      const snakeDataArray = transform ? transformToSnake(dataArray) : dataArray;

      const records = await this.model.bulkCreate(snakeDataArray, {
        ...otherOptions,
        returning: true
      });

      // 转换为camelCase
      return transform ? transformToCamel(records.map(record => record.get({ plain: true }))) : records;
    } catch (error) {
      logger.error(`[${this.model.name}Repository] bulkCreate error:`, error);
      throw error;
    }
  }

  /**
   * 更新记录
   * @param {Object} data - 更新数据
   * @param {Object} where - 更新条件
   * @param {Object} options - 更新选项
   * @param {boolean} [options.withDeleted=false] - 是否更新已软删除的记录
   * @param {boolean} [options.transform=true] - 是否转换字段命名
   * @param {boolean} [options.returning=false] - 是否返回更新后的记录
   * @returns {Promise<Array|Object>} 更新结果
   */
  async update(data, where, options = {}) {
    try {
      const { withDeleted = false, transform = true, returning = false, ...otherOptions } = options;

      // 转换为snake_case
      const snakeData = transform ? transformToSnake(data) : data;

      let updateOptions = {
        where,
        ...otherOptions
      };

      // 如果模型支持软删除且需要更新已删除记录
      if (this.supportsSoftDelete && withDeleted) {
        updateOptions.paranoid = false;
      }

      // 执行更新
      const [affectedCount] = await this.model.update(snakeData, updateOptions);

      // 如果需要返回更新后的记录
      if (returning && affectedCount > 0) {
        const records = await this.findAll(where, {
          withDeleted,
          transform,
          ...otherOptions
        });
        return { affectedCount, records };
      }

      return { affectedCount };
    } catch (error) {
      logger.error(`[${this.model.name}Repository] update error:`, error);
      throw error;
    }
  }

  /**
   * 软删除记录
   * @param {Object} where - 删除条件
   * @param {Object} options - 删除选项
   * @returns {Promise<number>} 删除的记录数
   */
  async softDelete(where, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.model.destroy({
      where,
      ...options
    });
  }

  /**
   * 批量软删除记录
   * @param {Array<number|string>} ids - 要删除的记录ID数组
   * @param {Object} options - 删除选项
   * @returns {Promise<number>} 删除的记录数
   */
  async batchSoftDelete(ids, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('ID数组不能为空');
    }

    return this.model.destroy({
      where: {
        id: {
          [this.model.sequelize.Op.in]: ids
        }
      },
      ...options
    });
  }

  /**
   * 硬删除记录（永久删除）
   * @param {Object} where - 删除条件
   * @param {Object} options - 删除选项
   * @returns {Promise<number>} 删除的记录数
   */
  async hardDelete(where, options = {}) {
    return this.model.destroy({
      where,
      ...options,
      force: true
    });
  }

  /**
   * 批量硬删除记录（永久删除）
   * @param {Array<number|string>} ids - 要删除的记录ID数组
   * @param {Object} options - 删除选项
   * @returns {Promise<number>} 删除的记录数
   */
  async batchHardDelete(ids, options = {}) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('ID数组不能为空');
    }

    return this.model.destroy({
      where: {
        id: {
          [this.model.sequelize.Op.in]: ids
        }
      },
      ...options,
      force: true
    });
  }

  /**
   * 删除记录（根据模型配置决定是软删除还是硬删除）
   * @param {Object} where - 删除条件
   * @param {Object} options - 删除选项
   * @param {boolean} [options.force=false] - 是否强制硬删除
   * @returns {Promise<number>} 删除的记录数
   */
  async delete(where, options = {}) {
    const { force = false, ...otherOptions } = options;

    return this.model.destroy({
      where,
      ...otherOptions,
      force: force
    });
  }

  /**
   * 批量删除记录（根据模型配置决定是软删除还是硬删除）
   * @param {Array<number|string>} ids - 要删除的记录ID数组
   * @param {Object} options - 删除选项
   * @param {boolean} [options.force=false] - 是否强制硬删除
   * @returns {Promise<number>} 删除的记录数
   */
  async batchDelete(ids, options = {}) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('ID数组不能为空');
    }

    const { force = false, ...otherOptions } = options;

    return this.model.destroy({
      where: {
        id: {
          [this.model.sequelize.Op.in]: ids
        }
      },
      ...otherOptions,
      force: force
    });
  }

  /**
   * 恢复已软删除的记录
   * @param {Object} where - 恢复条件
   * @param {Object} options - 恢复选项
   * @returns {Promise<number>} 恢复的记录数
   */
  async restore(where, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.model.restore({
      where,
      ...options
    });
  }

  /**
   * 批量恢复已软删除的记录
   * @param {Array<number|string>} ids - 要恢复的记录ID数组
   * @param {Object} options - 恢复选项
   * @returns {Promise<number>} 恢复的记录数
   */
  async batchRestore(ids, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('ID数组不能为空');
    }

    return this.model.restore({
      where: {
        id: {
          [this.model.sequelize.Op.in]: ids
        }
      },
      ...options
    });
  }

  /**
   * 计数
   * @param {Object} where - 计数条件
   * @param {Object} options - 计数选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的记录
   * @returns {Promise<number>} 记录数
   */
  async count(where = {}, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    let countOptions = {
      where,
      ...otherOptions
    };

    // 如果模型支持软删除且需要计数已删除记录
    if (this.supportsSoftDelete && withDeleted) {
      countOptions.paranoid = false;
    }

    return this.model.count(countOptions);
  }

  /**
   * 事务操作
   * @param {Function} callback - 事务回调函数
   * @returns {Promise<*>} 事务结果
   */
  async transaction(callback) {
    const transaction = await this.model.sequelize.transaction();
    try {
      const result = await callback(transaction);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 级联软删除
   * 注意：此方法需要在子类中重写，以实现特定模型的级联删除逻辑
   * @param {number|string} id - 记录ID
   * @param {Object} options - 删除选项
   * @returns {Promise<boolean>} 操作结果
   */
  async cascadeSoftDelete(id, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    // 默认实现只删除当前记录，不进行级联操作
    // 子类应该重写此方法以实现特定的级联删除逻辑
    await this.softDelete({ id }, options);
    return true;
  }

  /**
   * 批量级联软删除
   * 注意：此方法需要在子类中重写，以实现特定模型的级联删除逻辑
   * @param {Array<number|string>} ids - 记录ID数组
   * @param {Object} options - 删除选项
   * @returns {Promise<boolean>} 操作结果
   */
  async batchCascadeSoftDelete(ids, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('ID数组不能为空');
    }

    // 默认实现只删除当前记录，不进行级联操作
    // 子类应该重写此方法以实现特定的级联删除逻辑
    await this.batchSoftDelete(ids, options);
    return true;
  }
}

module.exports = BaseRepository;
