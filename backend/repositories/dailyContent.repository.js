/**
 * 每日内容仓库
 * 处理每日内容相关的数据访问
 */
const BaseRepository = require('./base.repository');
const { DailyContent, LearningPlan } = require('../models');
const { Op } = require('sequelize');

class DailyContentRepository extends BaseRepository {
  constructor() {
    super(DailyContent);
  }

  /**
   * 获取学习计划的每日内容列表
   * @param {number} planId - 学习计划ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的内容
   * @returns {Promise<Array>} 每日内容列表
   */
  async getPlanContents(planId, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;
    
    const queryOptions = {
      order: [['day_number', 'ASC']],
      ...otherOptions
    };
    
    // 如果需要包含已删除的内容
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }
    
    return this.findAll({ plan_id: planId }, queryOptions);
  }

  /**
   * 获取每日内容详情
   * @param {number} contentId - 内容ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的内容
   * @returns {Promise<Object>} 内容详情
   */
  async getContentDetails(contentId, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;
    
    const queryOptions = {
      include: [
        {
          model: LearningPlan,
          as: 'learningPlan',
          attributes: ['id', 'title', 'user_id']
        }
      ],
      ...otherOptions
    };
    
    // 如果需要包含已删除的内容
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }
    
    return this.findById(contentId, queryOptions);
  }

  /**
   * 创建每日内容
   * @param {Object} contentData - 内容数据
   * @returns {Promise<Object>} 创建的内容
   */
  async createContent(contentData) {
    return this.create(contentData);
  }

  /**
   * 更新每日内容
   * @param {number} contentId - 内容ID
   * @param {Object} contentData - 内容数据
   * @returns {Promise<Array>} 更新结果
   */
  async updateContent(contentId, contentData) {
    return this.update(contentData, { id: contentId });
  }

  /**
   * 软删除每日内容
   * @param {number} contentId - 内容ID
   * @returns {Promise<number>} 删除的记录数
   */
  async softDeleteContent(contentId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.softDelete({ id: contentId });
  }

  /**
   * 恢复已软删除的每日内容
   * @param {number} contentId - 内容ID
   * @returns {Promise<number>} 恢复的记录数
   */
  async restoreContent(contentId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.restore({ id: contentId });
  }

  /**
   * 获取已删除的每日内容列表
   * @param {number} planId - 学习计划ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 内容列表和分页信息
   */
  async getDeletedContents(planId, page = 1, pageSize = 10) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.findAndCountAll(
      { plan_id: planId },
      page,
      pageSize,
      {
        withDeleted: true,
        where: {
          deleted_at: { [Op.ne]: null }
        },
        order: [['deleted_at', 'DESC']]
      }
    );
  }

  /**
   * 验证内容所属权
   * @param {number} contentId - 内容ID
   * @param {string} userId - 用户ID
   * @param {boolean} includeDeleted - 是否包含已删除的内容
   * @returns {Promise<boolean>} 验证结果
   */
  async validateContentOwnership(contentId, userId, includeDeleted = false) {
    const options = {
      include: [
        {
          model: LearningPlan,
          as: 'learningPlan',
          attributes: ['id', 'user_id']
        }
      ]
    };

    if (includeDeleted) {
      options.withDeleted = true;
    }

    const content = await this.findById(contentId, options);

    return content && content.learningPlan && content.learningPlan.user_id === userId;
  }
}

module.exports = DailyContentRepository;
