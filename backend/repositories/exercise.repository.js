/**
 * 练习仓库
 * 处理练习相关的数据访问，支持软删除
 */
const BaseRepository = require('./base.repository');
const { Exercise, Tag, LearningPlan } = require('../models');
const { Op } = require('sequelize');

class ExerciseRepository extends BaseRepository {
  constructor() {
    super(Exercise);
  }

  /**
   * 获取标签下的练习列表
   * @param {number} tagId - 标签ID
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的练习
   * @returns {Promise<Object>} 练习列表和分页信息
   */
  async getExercisesByTagId(tagId, filters = {}, page = 1, pageSize = 10, options = {}) {
    // 构建查询条件
    const where = { tag_id: tagId, ...filters };

    // 查询选项
    const queryOptions = {
      order: [['difficulty', 'ASC'], ['created_at', 'DESC']],
      ...options
    };

    return this.findAndCountAll(where, page, pageSize, queryOptions);
  }

  /**
   * 获取练习详情
   * @param {number} exerciseId - 练习ID
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的练习
   * @returns {Promise<Object>} 练习详情
   */
  async getExerciseDetails(exerciseId, userId, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    const queryOptions = {
      include: [
        {
          model: Tag,
          as: 'tag',
          include: [
            {
              model: LearningPlan,
              as: 'learningPlan',
              where: { user_id: userId },
              attributes: ['id', 'user_id', 'title']
            }
          ]
        }
      ],
      ...otherOptions
    };

    // 如果需要包含已删除的练习
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }

    return this.findOne({ id: exerciseId }, queryOptions);
  }

  /**
   * 验证标签是否属于用户
   * @param {number} tagId - 标签ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 是否属于用户
   */
  async validateTagOwnership(tagId, userId) {
    const tag = await Tag.findOne({
      where: { id: tagId },
      include: [
        {
          model: LearningPlan,
          as: 'learningPlan',
          where: { user_id: userId },
          attributes: ['id', 'user_id']
        }
      ]
    });

    return !!tag;
  }

  /**
   * 创建练习
   * @param {Object} exerciseData - 练习数据
   * @returns {Promise<Object>} 创建的练习
   */
  async createExercise(exerciseData) {
    return this.create(exerciseData);
  }

  /**
   * 更新练习
   * @param {number} exerciseId - 练习ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<[number, Array]>} 更新结果
   */
  async updateExercise(exerciseId, updateData) {
    return this.update(updateData, { id: exerciseId });
  }

  /**
   * 删除练习
   * @param {number} exerciseId - 练习ID
   * @param {boolean} force - 是否强制删除（硬删除）
   * @returns {Promise<number>} 删除的记录数
   */
  async deleteExercise(exerciseId, force = false) {
    return this.delete({ id: exerciseId }, { force });
  }

  /**
   * 软删除练习
   * @param {number} exerciseId - 练习ID
   * @returns {Promise<number>} 删除的记录数
   */
  async softDeleteExercise(exerciseId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.softDelete({ id: exerciseId });
  }

  /**
   * 恢复已软删除的练习
   * @param {number} exerciseId - 练习ID
   * @returns {Promise<number>} 恢复的记录数
   */
  async restoreExercise(exerciseId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.restore({ id: exerciseId });
  }

  /**
   * 获取已删除的练习列表
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 练习列表和分页信息
   */
  async getDeletedExercises(page = 1, pageSize = 10) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.findAndCountAll(
      {},
      page,
      pageSize,
      {
        withDeleted: true,
        where: {
          deleted_at: { [Op.ne]: null }
        },
        order: [['deleted_at', 'DESC']]
      }
    );
  }
}

module.exports = ExerciseRepository;
