/**
 * 笔记仓库
 * 处理笔记相关的数据访问，支持软删除
 */
const BaseRepository = require('./base.repository');
const { Note, User, Tag, NoteLike, NoteComment } = require('../models');
const { Op } = require('sequelize');

class NoteRepository extends BaseRepository {
  constructor() {
    super(Note);
  }

  /**
   * 获取用户的笔记列表
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getUserNotes(userId, page = 1, pageSize = 10, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    const queryOptions = {
      include: [
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      ...otherOptions
    };

    // 如果需要包含已删除的笔记
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }

    return this.findAndCountAll(
      { user_id: userId },
      page,
      pageSize,
      queryOptions
    );
  }

  /**
   * 获取标签下的笔记列表
   * @param {number} tagId - 标签ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getNotesByTagId(tagId, page = 1, pageSize = 10, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    const queryOptions = {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar_url']
        }
      ],
      order: [['created_at', 'DESC']],
      ...otherOptions
    };

    // 如果需要包含已删除的笔记
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }

    return this.findAndCountAll(
      { tag_id: tagId },
      page,
      pageSize,
      queryOptions
    );
  }

  /**
   * 获取笔记详情
   * @param {number} noteId - 笔记ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的笔记
   * @returns {Promise<Object>} 笔记详情
   */
  async getNoteDetails(noteId, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    const queryOptions = {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar_url']
        },
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name']
        }
      ],
      ...otherOptions
    };

    // 如果需要包含已删除的笔记
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }

    return this.findById(noteId, queryOptions);
  }

  /**
   * 获取笔记点赞数
   * @param {number} noteId - 笔记ID
   * @returns {Promise<number>} 点赞数
   */
  async getLikesCount(noteId) {
    return NoteLike.count({
      where: { note_id: noteId }
    });
  }

  /**
   * 获取笔记评论数
   * @param {number} noteId - 笔记ID
   * @returns {Promise<number>} 评论数
   */
  async getCommentsCount(noteId) {
    return NoteComment.count({
      where: { note_id: noteId }
    });
  }

  /**
   * 检查用户是否已点赞笔记
   * @param {number} noteId - 笔记ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 是否已点赞
   */
  async checkUserLiked(noteId, userId) {
    const like = await NoteLike.findOne({
      where: { note_id: noteId, user_id: userId }
    });
    return !!like;
  }

  /**
   * 获取广场笔记列表
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getSquareNotes(page = 1, pageSize = 10, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    const queryOptions = {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar_url']
        },
        {
          model: Tag,
          as: 'tag',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      ...otherOptions
    };

    // 如果需要包含已删除的笔记
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }

    return this.findAndCountAll(
      { is_public: true },
      page,
      pageSize,
      queryOptions
    );
  }

  /**
   * 获取标签下的广场笔记列表
   * @param {number} tagId - 标签ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的笔记
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getSquareNotesByTagId(tagId, page = 1, pageSize = 10, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    const queryOptions = {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar_url']
        }
      ],
      order: [['created_at', 'DESC']],
      ...otherOptions
    };

    // 如果需要包含已删除的笔记
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }

    return this.findAndCountAll(
      { tag_id: tagId, is_public: true },
      page,
      pageSize,
      queryOptions
    );
  }

  /**
   * 软删除笔记
   * @param {number} noteId - 笔记ID
   * @returns {Promise<number>} 删除的记录数
   */
  async softDeleteNote(noteId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.softDelete({ id: noteId });
  }

  /**
   * 恢复已软删除的笔记
   * @param {number} noteId - 笔记ID
   * @returns {Promise<number>} 恢复的记录数
   */
  async restoreNote(noteId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.restore({ id: noteId });
  }

  /**
   * 获取已删除的笔记列表
   * @param {string} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 笔记列表和分页信息
   */
  async getDeletedNotes(userId, page = 1, pageSize = 10) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.findAndCountAll(
      { user_id: userId },
      page,
      pageSize,
      {
        withDeleted: true,
        where: {
          deleted_at: { [Op.ne]: null }
        },
        include: [
          {
            model: Tag,
            as: 'tag',
            attributes: ['id', 'name']
          }
        ],
        order: [['deleted_at', 'DESC']]
      }
    );
  }

  /**
   * 硬删除笔记（永久删除）
   * @param {number} noteId - 笔记ID
   * @returns {Promise<number>} 删除的记录数
   */
  async hardDeleteNote(noteId) {
    return this.hardDelete({ id: noteId });
  }
}

module.exports = NoteRepository;
