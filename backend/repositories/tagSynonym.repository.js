/**
 * 标签同义词仓库
 * 处理标签同义词相关的数据访问
 */
const BaseRepository = require('./base.repository');
const { TagSynonym, Tag } = require('../models');
const { Op } = require('sequelize');

class TagSynonymRepository extends BaseRepository {
  constructor() {
    super(TagSynonym);
  }

  /**
   * 获取标签的所有同义词
   * @param {number} tagId - 标签ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的同义词
   * @returns {Promise<Array>} 同义词列表
   */
  async getSynonymsByTagId(tagId, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;
    
    const queryOptions = {
      order: [['similarity_score', 'DESC']],
      ...otherOptions
    };
    
    // 如果需要包含已删除的同义词
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }
    
    return this.findAll({ primary_tag_id: tagId }, queryOptions);
  }

  /**
   * 获取同义词详情
   * @param {number} synonymId - 同义词ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的同义词
   * @returns {Promise<Object>} 同义词详情
   */
  async getSynonymDetails(synonymId, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;
    
    const queryOptions = {
      include: [
        {
          model: Tag,
          as: 'primaryTag',
          attributes: ['id', 'name']
        }
      ],
      ...otherOptions
    };
    
    // 如果需要包含已删除的同义词
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }
    
    return this.findById(synonymId, queryOptions);
  }

  /**
   * 创建标签同义词
   * @param {Object} synonymData - 同义词数据
   * @returns {Promise<Object>} 创建的同义词
   */
  async createSynonym(synonymData) {
    return this.create(synonymData);
  }

  /**
   * 更新标签同义词
   * @param {number} synonymId - 同义词ID
   * @param {Object} synonymData - 同义词数据
   * @returns {Promise<Array>} 更新结果
   */
  async updateSynonym(synonymId, synonymData) {
    return this.update(synonymData, { id: synonymId });
  }

  /**
   * 软删除标签同义词
   * @param {number} synonymId - 同义词ID
   * @returns {Promise<number>} 删除的记录数
   */
  async softDeleteSynonym(synonymId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.softDelete({ id: synonymId });
  }

  /**
   * 恢复已软删除的标签同义词
   * @param {number} synonymId - 同义词ID
   * @returns {Promise<number>} 恢复的记录数
   */
  async restoreSynonym(synonymId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.restore({ id: synonymId });
  }

  /**
   * 获取已删除的标签同义词列表
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 同义词列表和分页信息
   */
  async getDeletedSynonyms(page = 1, pageSize = 10) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.findAndCountAll(
      {},
      page,
      pageSize,
      {
        withDeleted: true,
        where: {
          deleted_at: { [Op.ne]: null }
        },
        include: [
          {
            model: Tag,
            as: 'primaryTag',
            attributes: ['id', 'name']
          }
        ],
        order: [['deleted_at', 'DESC']]
      }
    );
  }

  /**
   * 根据标签ID软删除所有同义词
   * @param {number} tagId - 标签ID
   * @returns {Promise<number>} 删除的记录数
   */
  async softDeleteByTagId(tagId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.softDelete({ primary_tag_id: tagId });
  }

  /**
   * 根据标签ID恢复所有同义词
   * @param {number} tagId - 标签ID
   * @returns {Promise<number>} 恢复的记录数
   */
  async restoreByTagId(tagId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.restore({ primary_tag_id: tagId });
  }

  /**
   * 批量软删除标签同义词
   * @param {Array<number>} synonymIds - 同义词ID数组
   * @returns {Promise<number>} 删除的记录数
   */
  async batchSoftDeleteSynonyms(synonymIds) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(synonymIds) || synonymIds.length === 0) {
      throw new Error('同义词ID数组不能为空');
    }

    return this.batchSoftDelete(synonymIds);
  }

  /**
   * 批量恢复标签同义词
   * @param {Array<number>} synonymIds - 同义词ID数组
   * @returns {Promise<number>} 恢复的记录数
   */
  async batchRestoreSynonyms(synonymIds) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(synonymIds) || synonymIds.length === 0) {
      throw new Error('同义词ID数组不能为空');
    }

    return this.batchRestore(synonymIds);
  }

  /**
   * 批量根据标签ID软删除同义词
   * @param {Array<number>} tagIds - 标签ID数组
   * @returns {Promise<number>} 删除的记录数
   */
  async batchSoftDeleteByTagIds(tagIds) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(tagIds) || tagIds.length === 0) {
      throw new Error('标签ID数组不能为空');
    }

    return this.model.destroy({
      where: {
        primary_tag_id: {
          [Op.in]: tagIds
        }
      }
    });
  }
}

module.exports = TagSynonymRepository;
