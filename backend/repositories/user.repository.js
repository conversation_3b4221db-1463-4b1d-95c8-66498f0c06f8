/**
 * 用户仓库
 * 处理用户相关的数据访问，支持软删除
 */
const BaseRepository = require('./base.repository');
const { User, sequelize } = require('../models');
const { Op } = require('sequelize');
const serviceContainer = require('../config/serviceContainer');

class UserRepository extends BaseRepository {
  constructor() {
    super(User);
  }

  /**
   * 根据ID查找用户
   * @param {string|number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的用户
   * @returns {Promise<Object>} 用户信息
   */
  async findById(userId, options = {}) {
    // 使用基类的findById方法，支持软删除
    return super.findById(userId, options);
  }

  /**
   * 根据手机号查找用户
   * @param {string} phone - 手机号
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的用户
   * @returns {Promise<Object>} 用户信息
   */
  async findByPhone(phone, options = {}) {
    // 使用基类的findOne方法，支持软删除
    return super.findOne({ phone }, options);
  }

  /**
   * 根据微信OpenID查找用户
   * @param {string} openid - 微信OpenID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的用户
   * @returns {Promise<Object>} 用户信息
   */
  async findByOpenid(openid, options = {}) {
    // 使用基类的findOne方法，支持软删除
    return super.findOne({ openid }, options);
  }

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @param {Object} options - 创建选项
   * @returns {Promise<Object>} 创建的用户
   */
  async createUser(userData, options = {}) {
    // 使用基类的create方法
    return super.create(userData, options);
  }

  /**
   * 更新用户信息
   * @param {string|number} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @param {Object} options - 更新选项
   * @param {boolean} [options.withDeleted=false] - 是否更新已软删除的用户
   * @returns {Promise<Object>} 更新后的用户
   */
  async updateUser(userId, updateData, options = {}) {
    // 使用基类的update方法，支持软删除
    await super.update(updateData, { id: userId }, options);

    // 返回更新后的用户
    return this.findById(userId, options);
  }

  /**
   * 更新用户最后登录时间
   * @param {string|number} userId - 用户ID
   * @param {Object} options - 更新选项
   * @returns {Promise<Object>} 更新结果
   */
  async updateLastLoginTime(userId, options = {}) {
    // 使用基类的update方法，支持软删除
    return super.update(
      { last_login_at: new Date() },
      { id: userId },
      options
    );
  }

  /**
   * 增加用户学习天数
   * @param {string|number} userId - 用户ID
   * @param {number} days - 增加的天数
   * @param {Object} options - 更新选项
   * @returns {Promise<Object>} 更新结果
   */
  async incrementStudyDays(userId, days = 1, options = {}) {
    // 使用基类的update方法，支持软删除
    return super.update(
      { study_days: sequelize.literal(`study_days + ${days}`) },
      { id: userId },
      options
    );
  }

  /**
   * 更新用户等级
   * @param {string|number} userId - 用户ID
   * @param {number} level - 新等级
   * @param {Object} options - 更新选项
   * @returns {Promise<Object>} 更新结果
   */
  async updateUserLevel(userId, level, options = {}) {
    // 使用基类的update方法，支持软删除
    return super.update(
      { level },
      { id: userId },
      options
    );
  }

  /**
   * 绑定手机号
   * @param {string|number} userId - 用户ID
   * @param {string} phone - 手机号
   * @param {string} hashedPassword - 哈希后的密码
   * @param {Object} options - 更新选项
   * @returns {Promise<Object>} 更新结果
   */
  async bindPhone(userId, phone, hashedPassword, options = {}) {
    // 使用基类的update方法，支持软删除
    return super.update(
      { phone, password: hashedPassword },
      { id: userId },
      options
    );
  }

  /**
   * 模糊查询用户
   * @param {string} nickname - 用户昵称
   * @param {number} limit - 限制数量
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的用户
   * @returns {Promise<Array>} 用户列表
   */
  async searchUsersByNickname(nickname, limit = 10, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    // 构建查询选项
    const findOptions = {
      where: {
        nickname: {
          [Op.like]: `%${nickname}%`
        }
      },
      limit,
      ...otherOptions
    };

    // 如果需要包含已软删除的用户
    if (this.supportsSoftDelete && withDeleted) {
      findOptions.paranoid = false;
    }

    return this.model.findAll(findOptions);
  }

  /**
   * 软删除用户
   * @param {string|number} userId - 用户ID
   * @param {Object} options - 删除选项
   * @returns {Promise<number>} 删除的记录数
   */
  async softDeleteUser(userId, options = {}) {
    // 使用基类的softDelete方法
    return super.softDelete({ id: userId }, options);
  }

  /**
   * 恢复已软删除的用户
   * @param {string|number} userId - 用户ID
   * @param {Object} options - 恢复选项
   * @returns {Promise<number>} 恢复的记录数
   */
  async restoreUser(userId, options = {}) {
    // 使用基类的restore方法
    return super.restore({ id: userId }, options);
  }

  /**
   * 获取已删除的用户列表
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 用户列表和分页信息
   */
  async getDeletedUsers(page = 1, pageSize = 10) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.findAndCountAll(
      {},
      page,
      pageSize,
      {
        withDeleted: true,
        where: {
          deleted_at: { [Op.ne]: null }
        },
        order: [['deleted_at', 'DESC']]
      }
    );
  }

  /**
   * 批量软删除用户
   * @param {Array<string|number>} userIds - 用户ID数组
   * @returns {Promise<number>} 删除的记录数
   */
  async batchSoftDeleteUsers(userIds) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new Error('用户ID数组不能为空');
    }

    return this.batchSoftDelete(userIds);
  }

  /**
   * 批量恢复用户
   * @param {Array<string|number>} userIds - 用户ID数组
   * @returns {Promise<number>} 恢复的记录数
   */
  async batchRestoreUsers(userIds) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new Error('用户ID数组不能为空');
    }

    return this.batchRestore(userIds);
  }

  /**
   * 级联软删除用户
   * 当用户被删除时，同时删除其相关数据
   * @param {string|number} userId - 用户ID
   * @param {Object} options - 删除选项
   * @returns {Promise<boolean>} 操作结果
   */
  async cascadeSoftDelete(userId, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.transaction(async (transaction) => {
      try {
        // 获取相关仓库
        const userSettingRepository = serviceContainer.getRepository('userSettingRepository');
        const learningPlanRepository = serviceContainer.getRepository('learningPlanRepository');
        const noteRepository = serviceContainer.getRepository('noteRepository');
        const exerciseRepository = serviceContainer.getRepository('exerciseRepository');
        const insightRepository = serviceContainer.getRepository('insightRepository');
        const tagFeedbackRepository = serviceContainer.getRepository('tagFeedbackRepository');

        // 软删除用户设置
        if (userSettingRepository.supportsSoftDelete) {
          await userSettingRepository.softDeleteSettings(userId, { transaction });
        }

        // 软删除学习计划
        if (learningPlanRepository.supportsSoftDelete) {
          await learningPlanRepository.softDelete({ user_id: userId }, { transaction });
        }

        // 软删除笔记
        if (noteRepository.supportsSoftDelete) {
          await noteRepository.softDelete({ user_id: userId }, { transaction });
        }

        // 软删除练习
        if (exerciseRepository.supportsSoftDelete) {
          await exerciseRepository.softDelete({ user_id: userId }, { transaction });
        }

        // 软删除观点
        if (insightRepository.supportsSoftDelete) {
          await insightRepository.softDelete({ user_id: userId }, { transaction });
        }

        // 软删除标签反馈
        if (tagFeedbackRepository.supportsSoftDelete) {
          await tagFeedbackRepository.softDeleteByUserId(userId, { transaction });
        }

        // 软删除用户
        await this.softDelete({ id: userId }, { transaction, ...options });

        return true;
      } catch (error) {
        // 记录错误并重新抛出
        console.error(`级联软删除用户失败: ${error.message}`);
        throw error;
      }
    });
  }

  /**
   * 批量级联软删除用户
   * @param {Array<string|number>} userIds - 用户ID数组
   * @param {Object} options - 删除选项
   * @returns {Promise<boolean>} 操作结果
   */
  async batchCascadeSoftDelete(userIds, options = {}) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new Error('用户ID数组不能为空');
    }

    return this.transaction(async (transaction) => {
      try {
        // 获取相关仓库
        const userSettingRepository = serviceContainer.getRepository('userSettingRepository');
        const learningPlanRepository = serviceContainer.getRepository('learningPlanRepository');
        const noteRepository = serviceContainer.getRepository('noteRepository');
        const exerciseRepository = serviceContainer.getRepository('exerciseRepository');
        const insightRepository = serviceContainer.getRepository('insightRepository');
        const tagFeedbackRepository = serviceContainer.getRepository('tagFeedbackRepository');

        // 批量软删除用户设置
        if (userSettingRepository.supportsSoftDelete) {
          await userSettingRepository.batchSoftDeleteByUserIds(userIds, { transaction });
        }

        // 批量软删除学习计划
        if (learningPlanRepository.supportsSoftDelete) {
          await learningPlanRepository.softDelete({
            user_id: { [Op.in]: userIds }
          }, { transaction });
        }

        // 批量软删除笔记
        if (noteRepository.supportsSoftDelete) {
          await noteRepository.softDelete({
            user_id: { [Op.in]: userIds }
          }, { transaction });
        }

        // 批量软删除练习
        if (exerciseRepository.supportsSoftDelete) {
          await exerciseRepository.softDelete({
            user_id: { [Op.in]: userIds }
          }, { transaction });
        }

        // 批量软删除观点
        if (insightRepository.supportsSoftDelete) {
          await insightRepository.softDelete({
            user_id: { [Op.in]: userIds }
          }, { transaction });
        }

        // 批量软删除标签反馈
        if (tagFeedbackRepository.supportsSoftDelete) {
          await tagFeedbackRepository.batchSoftDeleteByUserIds(userIds, { transaction });
        }

        // 批量软删除用户
        await this.batchSoftDelete(userIds, { transaction, ...options });

        return true;
      } catch (error) {
        // 记录错误并重新抛出
        console.error(`批量级联软删除用户失败: ${error.message}`);
        throw error;
      }
    });
  }
}

module.exports = UserRepository;
