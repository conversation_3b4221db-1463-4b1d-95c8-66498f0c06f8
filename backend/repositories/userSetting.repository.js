/**
 * 用户设置仓库
 * 处理用户设置相关的数据访问，支持软删除
 */
const BaseRepository = require('./base.repository');
const { UserSetting, User } = require('../models');
const { Op } = require('sequelize');

class UserSettingRepository extends BaseRepository {
  constructor() {
    super(UserSetting);
  }

  /**
   * 根据用户ID获取用户设置
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {boolean} [options.withDeleted=false] - 是否包含已软删除的设置
   * @returns {Promise<Object>} 用户设置
   */
  async getSettingsByUserId(userId, options = {}) {
    const { withDeleted = false, ...otherOptions } = options;

    const queryOptions = {
      ...otherOptions
    };

    // 如果需要包含已删除的设置
    if (withDeleted) {
      queryOptions.withDeleted = true;
    }

    return this.findOne({ user_id: userId }, queryOptions);
  }

  /**
   * 创建或更新用户设置
   * @param {string} userId - 用户ID
   * @param {Object} settingsData - 设置数据
   * @returns {Promise<Object>} 创建或更新的设置
   */
  async createOrUpdateSettings(userId, settingsData) {
    // 查找现有设置
    const existingSettings = await this.getSettingsByUserId(userId);

    if (existingSettings) {
      // 更新设置
      await this.update(settingsData, { user_id: userId });
      return this.getSettingsByUserId(userId);
    } else {
      // 创建设置
      return this.create({
        user_id: userId,
        ...settingsData
      });
    }
  }

  /**
   * 软删除用户设置
   * @param {string} userId - 用户ID
   * @returns {Promise<number>} 删除的记录数
   */
  async softDeleteSettings(userId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.softDelete({ user_id: userId });
  }

  /**
   * 恢复已软删除的用户设置
   * @param {string} userId - 用户ID
   * @returns {Promise<number>} 恢复的记录数
   */
  async restoreSettings(userId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }
    return this.restore({ user_id: userId });
  }

  /**
   * 获取已删除的用户设置列表
   * @param {number} page - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise<Object>} 设置列表和分页信息
   */
  async getDeletedSettings(page = 1, pageSize = 10) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    return this.findAndCountAll(
      {},
      page,
      pageSize,
      {
        withDeleted: true,
        where: {
          deleted_at: { [Op.ne]: null }
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'avatar_url']
          }
        ],
        order: [['deleted_at', 'DESC']]
      }
    );
  }

  /**
   * 级联软删除用户设置
   * 当用户被删除时，同时删除用户设置
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 操作结果
   */
  async cascadeSoftDelete(userId) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    await this.softDelete({ user_id: userId });
    return true;
  }

  /**
   * 批量软删除用户设置
   * @param {Array<string>} userIds - 用户ID数组
   * @returns {Promise<number>} 删除的记录数
   */
  async batchSoftDeleteByUserIds(userIds) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new Error('用户ID数组不能为空');
    }

    return this.model.destroy({
      where: {
        user_id: {
          [Op.in]: userIds
        }
      }
    });
  }

  /**
   * 批量恢复用户设置
   * @param {Array<string>} userIds - 用户ID数组
   * @returns {Promise<number>} 恢复的记录数
   */
  async batchRestoreByUserIds(userIds) {
    if (!this.supportsSoftDelete) {
      throw new Error('此模型不支持软删除');
    }

    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new Error('用户ID数组不能为空');
    }

    return this.model.restore({
      where: {
        user_id: {
          [Op.in]: userIds
        }
      }
    });
  }
}

module.exports = UserSettingRepository;
