const express = require('express');
const { body } = require('express-validator');
const aiController = require('../controllers/ai.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

/**
 * @swagger
 * /ai/stats:
 *   get:
 *     summary: 获取AI使用统计信息
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取AI统计信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     provider:
 *                       type: string
 *                       example: deepseek
 *                     model:
 *                       type: string
 *                       example: deepseek-r1-250120
 *                     stats:
 *                       type: object
 *                       properties:
 *                         requestCount:
 *                           type: integer
 *                           example: 10
 *                         errorCount:
 *                           type: integer
 *                           example: 1
 *                         tokenUsage:
 *                           type: object
 *                           properties:
 *                             prompt:
 *                               type: integer
 *                               example: 1200
 *                             completion:
 *                               type: integer
 *                               example: 800
 *                             total:
 *                               type: integer
 *                               example: 2000
 *                         errorRate:
 *                           type: number
 *                           example: 0.1
 *                         estimatedCost:
 *                           type: object
 *                           properties:
 *                             promptCost:
 *                               type: number
 *                               example: 0.0024
 *                             completionCost:
 *                               type: number
 *                               example: 0.0016
 *                             totalCost:
 *                               type: number
 *                               example: 0.004
 *                             currency:
 *                               type: string
 *                               example: USD
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/stats',
  authMiddleware,
  aiController.getAIStats
);

/**
 * @swagger
 * /ai/stats/reset:
 *   post:
 *     summary: 重置AI使用统计信息
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功重置AI统计信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: AI统计信息已重置
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post(
  '/stats/reset',
  authMiddleware,
  aiController.resetAIStats
);

/**
 * @swagger
 * /ai/test/tags:
 *   post:
 *     summary: 测试AI标签生成功能
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *             properties:
 *               title:
 *                 type: string
 *                 description: 学习计划标题
 *                 example: 提升与伴侣的沟通能力
 *               description:
 *                 type: string
 *                 description: 学习计划描述
 *                 example: 我希望能更好地与伴侣沟通，减少误解和冲突
 *               themeId:
 *                 type: integer
 *                 description: 主题ID
 *                 example: 1
 *     responses:
 *       200:
 *         description: 成功生成标签
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     plan:
 *                       type: object
 *                       properties:
 *                         title:
 *                           type: string
 *                           example: 提升与伴侣的沟通能力
 *                         description:
 *                           type: string
 *                           example: 我希望能更好地与伴侣沟通，减少误解和冲突
 *                         theme:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 1
 *                             name:
 *                               type: string
 *                               example: 人际沟通
 *                     tags:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: 倾听
 *                           relevanceScore:
 *                             type: number
 *                             example: 0.95
 *                           sortOrder:
 *                             type: integer
 *                             example: 0
 *                     stats:
 *                       type: object
 *                       properties:
 *                         requestCount:
 *                           type: integer
 *                           example: 1
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post(
  '/test/tags',
  // 暂时移除认证要求，方便测试
  // authMiddleware,
  [
    body('title').notEmpty().withMessage('学习计划标题不能为空'),
    validate
  ],
  aiController.testTagGeneration
);

/**
 * @swagger
 * /ai/learning-plans/generate:
 *   post:
 *     summary: 生成学习计划内容
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *             properties:
 *               title:
 *                 type: string
 *                 description: 学习计划标题
 *                 example: 如何提高沟通效率
 *               trouble:
 *                 type: string
 *                 description: 学习者的困扰/问题
 *                 example: 在团队讨论中经常无法清晰表达自己的想法
 *               learningIntensity:
 *                 type: string
 *                 enum: [easy, medium, hard]
 *                 description: 学习强度
 *                 example: medium
 *               learningDuration:
 *                 type: integer
 *                 description: 学习天数
 *                 example: 7
 *     responses:
 *       200:
 *         description: 成功生成学习计划内容
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     planContent:
 *                       type: object
 *                       properties:
 *                         enhancedTitle:
 *                           type: string
 *                           example: 高效团队沟通技巧掌握
 *                         designPrinciple:
 *                           type: string
 *                           example: 本学习计划采用循序渐进的方式...
 *                         contentPlan:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               day:
 *                                 type: integer
 *                                 example: 1
 *                               title:
 *                                 type: string
 *                                 example: 沟通基础与自我认知
 *                               content:
 *                                 type: string
 *                                 example: 今天我们将学习沟通的基本模型...
 *                         tags:
 *                           type: array
 *                           items:
 *                             type: string
 *                             example: 表达力
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post(
  '/learning-plans/generate',
  authMiddleware,
  [
    body('title').notEmpty().withMessage('学习计划标题不能为空'),
    validate
  ],
  aiController.generateLearningPlanContent
);

module.exports = router;
