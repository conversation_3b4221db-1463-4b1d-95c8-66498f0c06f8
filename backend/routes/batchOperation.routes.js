/**
 * 批量操作路由
 * 处理批量软删除和批量恢复操作
 */
const express = require('express');
const router = express.Router();
const batchOperationController = require('../controllers/batchOperation.controller');
const { authenticate, authorizeAdmin } = require('../middleware/auth');

/**
 * @swagger
 * /api/v2/batch/tags/soft-delete:
 *   post:
 *     summary: 批量软删除标签
 *     description: 批量软删除指定ID的标签
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 标签ID数组
 *               cascade:
 *                 type: boolean
 *                 description: 是否级联删除相关数据
 *                 default: false
 *     responses:
 *       200:
 *         description: 标签已被批量软删除
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.post('/tags/soft-delete', authenticate, authorizeAdmin, batchOperationController.batchSoftDeleteTags);

/**
 * @swagger
 * /api/v2/batch/tags/restore:
 *   post:
 *     summary: 批量恢复标签
 *     description: 批量恢复已软删除的标签
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 标签ID数组
 *     responses:
 *       200:
 *         description: 标签已被批量恢复
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.post('/tags/restore', authenticate, authorizeAdmin, batchOperationController.batchRestoreTags);

/**
 * @swagger
 * /api/v2/batch/users/soft-delete:
 *   post:
 *     summary: 批量软删除用户
 *     description: 批量软删除指定ID的用户
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 用户ID数组
 *               cascade:
 *                 type: boolean
 *                 description: 是否级联删除相关数据
 *                 default: false
 *     responses:
 *       200:
 *         description: 用户已被批量软删除
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.post('/users/soft-delete', authenticate, authorizeAdmin, batchOperationController.batchSoftDeleteUsers);

/**
 * @swagger
 * /api/v2/batch/users/restore:
 *   post:
 *     summary: 批量恢复用户
 *     description: 批量恢复已软删除的用户
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 用户ID数组
 *     responses:
 *       200:
 *         description: 用户已被批量恢复
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.post('/users/restore', authenticate, authorizeAdmin, batchOperationController.batchRestoreUsers);

/**
 * @swagger
 * /api/v2/batch/notes/soft-delete:
 *   post:
 *     summary: 批量软删除笔记
 *     description: 批量软删除指定ID的笔记
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 笔记ID数组
 *     responses:
 *       200:
 *         description: 笔记已被批量软删除
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/notes/soft-delete', authenticate, batchOperationController.batchSoftDeleteNotes);

/**
 * @swagger
 * /api/v2/batch/notes/restore:
 *   post:
 *     summary: 批量恢复笔记
 *     description: 批量恢复已软删除的笔记
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 笔记ID数组
 *     responses:
 *       200:
 *         description: 笔记已被批量恢复
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/notes/restore', authenticate, batchOperationController.batchRestoreNotes);

/**
 * @swagger
 * /api/v2/batch/insights/soft-delete:
 *   post:
 *     summary: 批量软删除观点
 *     description: 批量软删除指定ID的观点
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 观点ID数组
 *     responses:
 *       200:
 *         description: 观点已被批量软删除
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/insights/soft-delete', authenticate, batchOperationController.batchSoftDeleteInsights);

/**
 * @swagger
 * /api/v2/batch/insights/restore:
 *   post:
 *     summary: 批量恢复观点
 *     description: 批量恢复已软删除的观点
 *     tags: [BatchOperations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 观点ID数组
 *     responses:
 *       200:
 *         description: 观点已被批量恢复
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/insights/restore', authenticate, batchOperationController.batchRestoreInsights);

module.exports = router;
