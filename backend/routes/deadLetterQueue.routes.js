/**
 * 死信队列路由
 */
const express = require('express');
const router = express.Router();
const deadLetterQueueController = require('../controllers/deadLetterQueue.controller');
const { adminMiddleware } = require('../middlewares/auth.middleware');

// 所有死信队列路由都需要管理员权限
router.use(adminMiddleware);

// 获取死信队列列表
router.get('/', deadLetterQueueController.getDeadLetterQueue);

// 获取死信队列项详情
router.get('/:id', deadLetterQueueController.getDeadLetterQueueItem);

// 重试死信队列项
router.post('/:id/retry', deadLetterQueueController.retryDeadLetterQueueItem);

// 解决死信队列项
router.post('/:id/resolve', deadLetterQueueController.resolveDeadLetterQueueItem);

module.exports = router;
