import { Router } from 'express';
import { authMiddleware } from '../../middlewares/auth.middleware';
import { adminMiddleware } from '../../middlewares/admin.middleware';
import { validateSchema } from '../../middlewares/validation.middleware';
import { createBadgeSchema, updateBadgeSchema, awardBadgeSchema, equipBadgeSchema } from '../../validations/gamification/badge.validation';
import { createBadgeV2Controller } from '../../controllers/gamification/badgeV2.controller.factory';

/**
 * 创建徽章路由
 * @returns 路由实例
 */
export function createBadgeV2Routes(): Router {
  const router = Router();
  const controller = createBadgeV2Controller();

  // 获取徽章列表
  router.get(
    '/',
    authMiddleware,
    (req, res) => controller.listBadges(req, res)
  );

  // 获取单个徽章
  router.get(
    '/:id',
    authMiddleware,
    (req, res) => controller.getBadge(req, res)
  );

  // 获取用户徽章
  router.get(
    '/user/:userId',
    authMiddleware,
    (req, res) => controller.getUserBadges(req, res)
  );

  // 创建徽章（仅管理员）
  router.post(
    '/',
    authMiddleware,
    adminMiddleware,
    validateSchema(createBadgeSchema),
    (req, res) => controller.createBadge(req, res)
  );

  // 更新徽章（仅管理员）
  router.put(
    '/:id',
    authMiddleware,
    adminMiddleware,
    validateSchema(updateBadgeSchema),
    (req, res) => controller.updateBadge(req, res)
  );

  // 删除徽章（仅管理员）
  router.delete(
    '/:id',
    authMiddleware,
    adminMiddleware,
    (req, res) => controller.deleteBadge(req, res)
  );

  // 恢复徽章（仅管理员）
  router.post(
    '/:id/restore',
    authMiddleware,
    adminMiddleware,
    (req, res) => controller.restoreBadge(req, res)
  );

  // 授予徽章给用户（仅管理员）
  router.post(
    '/:id/award',
    authMiddleware,
    adminMiddleware,
    validateSchema(awardBadgeSchema),
    (req, res) => controller.awardBadge(req, res)
  );

  // 装备徽章
  router.post(
    '/:id/equip',
    authMiddleware,
    validateSchema(equipBadgeSchema),
    (req, res) => controller.equipBadge(req, res)
  );

  // 卸下徽章
  router.post(
    '/:id/unequip',
    authMiddleware,
    validateSchema(equipBadgeSchema),
    (req, res) => controller.unequipBadge(req, res)
  );

  return router;
}
