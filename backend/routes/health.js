/**
 * 健康检查路由
 * 提供系统健康状态检查端点
 */

const express = require('express');
const router = express.Router();
const { Sequelize } = require('sequelize');
const redis = require('redis');

// 导入数据库配置
const sequelize = require('../config/database');

/**
 * 基础健康检查
 * GET /health
 */
router.get('/', async (req, res) => {
  try {
    const healthCheck = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      services: {}
    };

    // 检查数据库连接
    try {
      await sequelize.authenticate();
      healthCheck.services.database = {
        status: 'OK',
        responseTime: Date.now()
      };
    } catch (error) {
      healthCheck.services.database = {
        status: 'ERROR',
        error: error.message
      };
      healthCheck.status = 'DEGRADED';
    }

    // 检查Redis连接
    if (process.env.REDIS_URL) {
      try {
        const redisClient = redis.createClient({
          url: process.env.REDIS_URL
        });
        
        const startTime = Date.now();
        await redisClient.connect();
        await redisClient.ping();
        await redisClient.disconnect();
        
        healthCheck.services.redis = {
          status: 'OK',
          responseTime: Date.now() - startTime
        };
      } catch (error) {
        healthCheck.services.redis = {
          status: 'ERROR',
          error: error.message
        };
        healthCheck.status = 'DEGRADED';
      }
    }

    // 检查内存使用情况
    const memoryUsage = process.memoryUsage();
    healthCheck.memory = {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`
    };

    // 根据状态设置HTTP状态码
    const statusCode = healthCheck.status === 'OK' ? 200 : 503;
    
    res.status(statusCode).json(healthCheck);
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * 详细健康检查
 * GET /health/detailed
 */
router.get('/detailed', async (req, res) => {
  try {
    const detailedCheck = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid
      },
      services: {},
      performance: {}
    };

    // 数据库详细检查
    try {
      const startTime = Date.now();
      await sequelize.authenticate();
      
      // 检查数据库连接池状态
      const pool = sequelize.connectionManager.pool;
      
      detailedCheck.services.database = {
        status: 'OK',
        responseTime: Date.now() - startTime,
        pool: {
          size: pool.size,
          available: pool.available,
          using: pool.using,
          waiting: pool.waiting
        }
      };
    } catch (error) {
      detailedCheck.services.database = {
        status: 'ERROR',
        error: error.message
      };
      detailedCheck.status = 'DEGRADED';
    }

    // Redis详细检查
    if (process.env.REDIS_URL) {
      try {
        const redisClient = redis.createClient({
          url: process.env.REDIS_URL
        });
        
        const startTime = Date.now();
        await redisClient.connect();
        
        const info = await redisClient.info();
        await redisClient.disconnect();
        
        detailedCheck.services.redis = {
          status: 'OK',
          responseTime: Date.now() - startTime,
          info: info.split('\r\n').slice(0, 10) // 只显示前10行信息
        };
      } catch (error) {
        detailedCheck.services.redis = {
          status: 'ERROR',
          error: error.message
        };
        detailedCheck.status = 'DEGRADED';
      }
    }

    // 性能指标
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    detailedCheck.performance = {
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      eventLoop: {
        delay: process.hrtime.bigint ? Number(process.hrtime.bigint()) : 0
      }
    };

    const statusCode = detailedCheck.status === 'OK' ? 200 : 503;
    res.status(statusCode).json(detailedCheck);
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * 就绪检查（用于Kubernetes等容器编排）
 * GET /health/ready
 */
router.get('/ready', async (req, res) => {
  try {
    // 检查关键服务是否就绪
    const checks = [];
    
    // 数据库就绪检查
    checks.push(
      sequelize.authenticate()
        .then(() => ({ service: 'database', status: 'ready' }))
        .catch(error => ({ service: 'database', status: 'not_ready', error: error.message }))
    );
    
    // Redis就绪检查（如果配置了）
    if (process.env.REDIS_URL) {
      const redisClient = redis.createClient({
        url: process.env.REDIS_URL
      });
      
      checks.push(
        redisClient.connect()
          .then(() => redisClient.ping())
          .then(() => redisClient.disconnect())
          .then(() => ({ service: 'redis', status: 'ready' }))
          .catch(error => ({ service: 'redis', status: 'not_ready', error: error.message }))
      );
    }
    
    const results = await Promise.all(checks);
    const allReady = results.every(result => result.status === 'ready');
    
    const response = {
      status: allReady ? 'ready' : 'not_ready',
      timestamp: new Date().toISOString(),
      services: results
    };
    
    res.status(allReady ? 200 : 503).json(response);
  } catch (error) {
    res.status(503).json({
      status: 'not_ready',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * 存活检查（用于Kubernetes等容器编排）
 * GET /health/live
 */
router.get('/live', (req, res) => {
  // 简单的存活检查，只要进程在运行就返回OK
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    pid: process.pid
  });
});

module.exports = router;