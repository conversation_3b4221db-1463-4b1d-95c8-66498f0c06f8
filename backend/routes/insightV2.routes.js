/**
 * 观点路由 V2
 * 使用统一的认证中间件和新的观点控制器
 */
const express = require('express');
const { body, query } = require('express-validator');
const insightController = require('../controllers/insightV2.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

router.get(
  '/tags/:tagId/insights',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  insightController.getInsightsByTagId
);

router.get(
  '/insights/:id',
  authMiddleware,
  insightController.getInsightById
);

router.post(
  '/insights',
  authMiddleware,
  [
    body('tagId').isInt().withMessage('标签ID必须是整数'),
    body('content').notEmpty().withMessage('观点内容不能为空'),
    body('source').optional(),
    body('background').optional(),
    validate
  ],
  insightController.createInsight
);

router.put(
  '/insights/:id',
  authMiddleware,
  [
    body('content').optional().notEmpty().withMessage('观点内容不能为空'),
    body('source').optional(),
    body('background').optional(),
    validate
  ],
  insightController.updateInsight
);

router.delete(
  '/insights/:id',
  authMiddleware,
  insightController.deleteInsight
);

/**
 * @route DELETE /api/v2/insights/:id/soft-delete
 * @desc 软删除观点
 * @access Private
 */
router.delete(
  '/insights/:id/soft-delete',
  authMiddleware,
  insightController.softDeleteInsight
);

/**
 * @route POST /api/v2/insights/:id/restore
 * @desc 恢复已软删除的观点
 * @access Private
 */
router.post(
  '/insights/:id/restore',
  authMiddleware,
  insightController.restoreInsight
);

/**
 * @route GET /api/v2/tags/:tagId/insights/deleted
 * @desc 获取已删除的观点列表
 * @access Private
 */
router.get(
  '/tags/:tagId/insights/deleted',
  authMiddleware,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  insightController.getDeletedInsights
);

module.exports = router;
