/**
 * 学习计划路由 V2
 * 支持软删除功能
 */
const express = require('express');
const router = express.Router();
const learningPlanController = require('../controllers/learningPlanV2.controller');
const { authenticate } = require('../middleware/auth');
const sanitizeInput = require('../middlewares/sanitize-input.middleware');

/**
 * @swagger
 * /api/v2/learning-plans:
 *   get:
 *     summary: 获取用户的学习计划列表
 *     description: 获取当前用户的所有学习计划
 *     tags: [LearningPlans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页记录数
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed, archived]
 *         description: 学习计划状态
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, updatedAt, title]
 *           default: updatedAt
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取学习计划列表
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/', authenticate, learningPlanController.getUserPlans);

/**
 * @swagger
 * /api/v2/learning-plans/{id}:
 *   get:
 *     summary: 获取单个学习计划
 *     description: 根据ID获取学习计划详情
 *     tags: [LearningPlans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 学习计划ID
 *     responses:
 *       200:
 *         description: 成功获取学习计划详情
 *       401:
 *         description: 未授权
 *       404:
 *         description: 学习计划不存在或不属于当前用户
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', authenticate, learningPlanController.getPlanById);

/**
 * @swagger
 * /api/v2/learning-plans:
 *   post:
 *     summary: 创建学习计划
 *     description: 创建新的学习计划
 *     tags: [LearningPlans]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - themeId
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               themeId:
 *                 type: integer
 *               duration:
 *                 type: integer
 *               status:
 *                 type: string
 *                 enum: [active, completed, archived]
 *                 default: active
 *     responses:
 *       201:
 *         description: 成功创建学习计划
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/', authenticate, sanitizeInput(['description']), learningPlanController.createPlan);

/**
 * @swagger
 * /api/v2/learning-plans/{id}:
 *   put:
 *     summary: 更新学习计划
 *     description: 更新指定ID的学习计划
 *     tags: [LearningPlans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 学习计划ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               themeId:
 *                 type: integer
 *               duration:
 *                 type: integer
 *               status:
 *                 type: string
 *                 enum: [active, completed, archived]
 *     responses:
 *       200:
 *         description: 成功更新学习计划
 *       401:
 *         description: 未授权
 *       404:
 *         description: 学习计划不存在或不属于当前用户
 *       500:
 *         description: 服务器错误
 */
router.put('/:id', authenticate, sanitizeInput(['description']), learningPlanController.updatePlan);

/**
 * @swagger
 * /api/v2/learning-plans/{id}/soft-delete:
 *   delete:
 *     summary: 软删除学习计划
 *     description: 软删除指定ID的学习计划
 *     tags: [LearningPlans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 学习计划ID
 *     responses:
 *       200:
 *         description: 学习计划已被软删除
 *       401:
 *         description: 未授权
 *       404:
 *         description: 学习计划不存在或不属于当前用户
 *       500:
 *         description: 服务器错误
 */
router.delete('/:id/soft-delete', authenticate, learningPlanController.softDeletePlan);

/**
 * @swagger
 * /api/v2/learning-plans/{id}/restore:
 *   post:
 *     summary: 恢复已删除的学习计划
 *     description: 恢复已软删除的学习计划
 *     tags: [LearningPlans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 学习计划ID
 *     responses:
 *       200:
 *         description: 学习计划已恢复
 *       400:
 *         description: 学习计划未被删除，无需恢复
 *       401:
 *         description: 未授权
 *       404:
 *         description: 学习计划不存在或不属于当前用户
 *       500:
 *         description: 服务器错误
 */
router.post('/:id/restore', authenticate, learningPlanController.restorePlan);

/**
 * @swagger
 * /api/v2/learning-plans/deleted:
 *   get:
 *     summary: 获取已删除的学习计划列表
 *     description: 获取当前用户的所有已软删除的学习计划
 *     tags: [LearningPlans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取已删除的学习计划列表
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/deleted', authenticate, learningPlanController.getDeletedPlans);

module.exports = router;
