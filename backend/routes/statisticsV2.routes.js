/**
 * 统计路由V2
 * 处理统计相关的路由
 */
const express = require('express');
const { body, query } = require('express-validator');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');
const statisticsMonitorMiddleware = require('../middlewares/statisticsMonitor.middleware');
const serviceContainer = require('../config/serviceContainer');

// 获取统计服务实例 (使用 getService 方法)
// const statisticsService = serviceContainer.services.statisticsService; // 旧方式
const statisticsService = serviceContainer.getService('statisticsService'); // 新方式

// 创建统计控制器实例
const statisticsV2Controller = require('../controllers/statisticsV2.controller')(statisticsService);

const router = express.Router();

// 添加统计监控中间件
router.use(statisticsMonitorMiddleware);

/**
 * @swagger
 * /api/v1/statistics/learning:
 *   get:
 *     summary: 获取学习统计数据
 *     description: 获取用户的学习统计数据，包括学习天数、连续天数、完成练习数等
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取学习统计数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalStudyDays:
 *                       type: integer
 *                       example: 15
 *                     currentStreak:
 *                       type: integer
 *                       example: 3
 *                     longestStreak:
 *                       type: integer
 *                       example: 7
 *                     completedExercises:
 *                       type: integer
 *                       example: 25
 *                     viewedInsights:
 *                       type: integer
 *                       example: 42
 *                     createdNotes:
 *                       type: integer
 *                       example: 10
 *                     totalTimeSpent:
 *                       type: integer
 *                       example: 320
 *                     activePlans:
 *                       type: integer
 *                       example: 2
 *                     completedPlans:
 *                       type: integer
 *                       example: 1
 *                     activityStats:
 *                       type: object
 *                       example:
 *                         login: 30
 *                         view_exercise: 50
 *                         complete_exercise: 25
 *                         view_insight: 42
 *                         create_note: 10
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/statistics/learning',
  authMiddleware,
  statisticsV2Controller.getLearningStatistics
);

/**
 * @swagger
 * /api/v1/statistics/daily:
 *   get:
 *     summary: 获取每日学习记录
 *     description: 获取用户的每日学习记录，包括学习时间、完成练习数等
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期（YYYY-MM-DD）
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期（YYYY-MM-DD）
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 30
 *         description: 每页记录数
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: 偏移量
 *     responses:
 *       200:
 *         description: 成功获取每日学习记录
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     records:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           date:
 *                             type: string
 *                             format: date
 *                             example: 2023-06-15
 *                           timeSpent:
 *                             type: integer
 *                             example: 30
 *                           exercisesCompleted:
 *                             type: integer
 *                             example: 2
 *                           insightsViewed:
 *                             type: integer
 *                             example: 5
 *                           notesCreated:
 *                             type: integer
 *                             example: 1
 *                           bubbleInteractions:
 *                             type: integer
 *                             example: 3
 *                           hasActivity:
 *                             type: boolean
 *                             example: true
 *                     count:
 *                       type: integer
 *                       example: 30
 *                     limit:
 *                       type: integer
 *                       example: 30
 *                     offset:
 *                       type: integer
 *                       example: 0
 *                     totalPages:
 *                       type: integer
 *                       example: 1
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/statistics/daily',
  authMiddleware,
  [
    query('startDate').optional().isDate().withMessage('开始日期格式无效'),
    query('endDate').optional().isDate().withMessage('结束日期格式无效'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('限制数量必须是1-100之间的整数'),
    query('offset').optional().isInt({ min: 0 }).withMessage('偏移量必须是非负整数'),
    validate
  ],
  statisticsV2Controller.getDailyRecords
);

/**
 * @swagger
 * /api/v1/statistics/activities:
 *   post:
 *     summary: 记录学习活动
 *     description: 记录用户的学习活动，如查看练习、完成练习等
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - activityType
 *             properties:
 *               activityType:
 *                 type: string
 *                 enum: [login, view_exercise, complete_exercise, view_insight, create_note, like_note, comment_note, bubble_interaction, share_content]
 *                 description: 活动类型
 *               planId:
 *                 type: integer
 *                 description: 学习计划ID
 *               contentType:
 *                 type: string
 *                 enum: [exercise, insight, note, tag, plan, bubble]
 *                 description: 内容类型
 *               contentId:
 *                 type: integer
 *                 description: 内容ID
 *               duration:
 *                 type: integer
 *                 description: 持续时间（秒）
 *               details:
 *                 type: object
 *                 description: 活动详情
 *     responses:
 *       200:
 *         description: 学习活动已记录
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     activityId:
 *                       type: integer
 *                       example: 123
 *                     activityType:
 *                       type: string
 *                       example: complete_exercise
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-06-15T08:00:00Z
 *                 message:
 *                   type: string
 *                   example: 学习活动已记录
 *       400:
 *         $ref: '#/components/responses/BadRequestError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post(
  '/statistics/activities',
  authMiddleware,
  [
    body('activityType').isIn([
      'login',
      'view_exercise',
      'complete_exercise',
      'view_insight',
      'create_note',
      'like_note',
      'comment_note',
      'bubble_interaction',
      'share_content'
    ]).withMessage('无效的活动类型'),
    body('planId').optional().isInt().withMessage('学习计划ID必须是整数'),
    body('contentType').optional().isIn([
      'exercise',
      'insight',
      'note',
      'tag',
      'plan',
      'bubble'
    ]).withMessage('无效的内容类型'),
    body('contentId').optional().isInt().withMessage('内容ID必须是整数'),
    body('duration').optional().isInt({ min: 0 }).withMessage('持续时间必须是非负整数'),
    body('details').optional().isObject().withMessage('活动详情必须是对象'),
    validate
  ],
  statisticsV2Controller.recordLearningActivity
);

/**
 * @swagger
 * /api/v1/statistics/activities:
 *   get:
 *     summary: 获取学习活动列表
 *     description: 获取用户的学习活动列表，支持分页和过滤
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: activityType
 *         schema:
 *           type: string
 *           enum: [login, view_exercise, complete_exercise, view_insight, create_note, like_note, comment_note, bubble_interaction, share_content]
 *         description: 活动类型
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期（YYYY-MM-DD）
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期（YYYY-MM-DD）
 *       - in: query
 *         name: contentType
 *         schema:
 *           type: string
 *           enum: [exercise, insight, note, tag, plan, bubble]
 *         description: 内容类型
 *       - in: query
 *         name: planId
 *         schema:
 *           type: integer
 *         description: 学习计划ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 成功获取学习活动列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     activities:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 123
 *                           activityType:
 *                             type: string
 *                             example: complete_exercise
 *                           contentType:
 *                             type: string
 *                             example: exercise
 *                           contentId:
 *                             type: integer
 *                             example: 456
 *                           planId:
 *                             type: integer
 *                             example: 789
 *                           duration:
 *                             type: integer
 *                             example: 300
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: 2023-06-15T08:00:00Z
 *                     count:
 *                       type: integer
 *                       example: 50
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 20
 *                     totalPages:
 *                       type: integer
 *                       example: 3
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/statistics/activities',
  authMiddleware,
  [
    query('activityType').optional().isIn([
      'login',
      'view_exercise',
      'complete_exercise',
      'view_insight',
      'create_note',
      'like_note',
      'comment_note',
      'bubble_interaction',
      'share_content'
    ]).withMessage('无效的活动类型'),
    query('startDate').optional().isDate().withMessage('开始日期格式无效'),
    query('endDate').optional().isDate().withMessage('结束日期格式无效'),
    query('contentType').optional().isIn([
      'exercise',
      'insight',
      'note',
      'tag',
      'plan',
      'bubble'
    ]).withMessage('无效的内容类型'),
    query('planId').optional().isInt().withMessage('学习计划ID必须是整数'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  statisticsV2Controller.getLearningActivities
);

/**
 * @swagger
 * /api/v1/statistics/overview:
 *   get:
 *     summary: 获取学习概览
 *     description: 获取用户的学习概览，包括统计数据和最近趋势
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取学习概览
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     statistics:
 *                       $ref: '#/components/schemas/LearningStatistics'
 *                     recentTrend:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DailyRecord'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/statistics/overview',
  authMiddleware,
  statisticsV2Controller.getLearningOverview
);

/**
 * @swagger
 * /api/v1/statistics/trend:
 *   get:
 *     summary: 获取学习趋势
 *     description: 获取用户的学习趋势数据
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 90
 *           default: 30
 *         description: 天数
 *     responses:
 *       200:
 *         description: 成功获取学习趋势
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     trend:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DailyRecord'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.get(
  '/statistics/trend',
  authMiddleware,
  [
    query('days').optional().isInt({ min: 1, max: 90 }).withMessage('天数必须是1-90之间的整数'),
    validate
  ],
  statisticsV2Controller.getLearningTrend
);

module.exports = router;
