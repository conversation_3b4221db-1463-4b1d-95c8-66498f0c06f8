/**
 * API契约测试脚本
 * 基于OpenAPI规范验证API实现的一致性
 * 
 * 使用方法:
 * node backend/scripts/api-contract-test.js [--path=/api/v2/users] [--method=GET] [--all]
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { program } = require('commander');
const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const swaggerParser = require('@apidevtools/swagger-parser');
const chalk = require('chalk');
const config = require('../config/config');

// 定义命令行选项
program
  .option('--path <path>', '要测试的API路径，例如 /api/v2/users')
  .option('--method <method>', '要测试的HTTP方法，例如 GET, POST, PUT, DELETE')
  .option('--all', '测试所有API端点')
  .option('--verbose', '显示详细输出')
  .option('--base-url <url>', '基础URL', `http://localhost:${config.server.port}`)
  .parse(process.argv);

const options = program.opts();

// 创建JSON Schema验证器
const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

// 加载OpenAPI规范
async function loadOpenApiSpec() {
  try {
    const specPath = path.resolve(__dirname, '../config/swagger.json');
    
    // 检查文件是否存在
    if (!fs.existsSync(specPath)) {
      console.error(chalk.red('错误: swagger.json文件不存在'));
      console.log(chalk.yellow('提示: 请确保已生成swagger.json文件'));
      process.exit(1);
    }
    
    // 解析并验证OpenAPI规范
    const api = await swaggerParser.validate(specPath);
    console.log(chalk.green(`✓ OpenAPI规范验证通过: ${api.info.title} v${api.info.version}`));
    return api;
  } catch (error) {
    console.error(chalk.red('OpenAPI规范验证失败:'), error);
    process.exit(1);
  }
}

// 从OpenAPI规范中提取端点信息
function extractEndpoints(api) {
  const endpoints = [];
  
  // 遍历所有路径
  Object.keys(api.paths).forEach(path => {
    const pathItem = api.paths[path];
    
    // 遍历路径下的所有HTTP方法
    Object.keys(pathItem).forEach(method => {
      if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
        const operation = pathItem[method];
        
        // 提取请求体schema
        let requestSchema = null;
        if (operation.requestBody && 
            operation.requestBody.content && 
            operation.requestBody.content['application/json'] &&
            operation.requestBody.content['application/json'].schema) {
          requestSchema = operation.requestBody.content['application/json'].schema;
        }
        
        // 提取响应schema
        let responseSchema = null;
        if (operation.responses && 
            operation.responses['200'] && 
            operation.responses['200'].content && 
            operation.responses['200'].content['application/json'] &&
            operation.responses['200'].content['application/json'].schema) {
          responseSchema = operation.responses['200'].content['application/json'].schema;
        }
        
        endpoints.push({
          path,
          method: method.toUpperCase(),
          operationId: operation.operationId,
          summary: operation.summary,
          requestSchema,
          responseSchema,
          parameters: operation.parameters || []
        });
      }
    });
  });
  
  return endpoints;
}

// 生成测试数据
function generateTestData(schema) {
  if (!schema) return null;
  
  // 简单实现，实际项目中可以使用更复杂的测试数据生成库
  const result = {};
  
  if (schema.properties) {
    Object.keys(schema.properties).forEach(prop => {
      const propSchema = schema.properties[prop];
      
      switch (propSchema.type) {
        case 'string':
          if (propSchema.format === 'email') {
            result[prop] = '<EMAIL>';
          } else if (propSchema.format === 'date-time') {
            result[prop] = new Date().toISOString();
          } else if (propSchema.enum) {
            result[prop] = propSchema.enum[0];
          } else {
            result[prop] = `test-${prop}`;
          }
          break;
        case 'number':
        case 'integer':
          result[prop] = 1;
          break;
        case 'boolean':
          result[prop] = true;
          break;
        case 'array':
          result[prop] = propSchema.items ? [generateTestData(propSchema.items)] : [];
          break;
        case 'object':
          result[prop] = generateTestData(propSchema);
          break;
      }
    });
  }
  
  return result;
}

// 验证响应
function validateResponse(schema, response) {
  if (!schema) return { valid: true };
  
  const validate = ajv.compile(schema);
  const valid = validate(response);
  
  return {
    valid,
    errors: validate.errors
  };
}

// 测试单个端点
async function testEndpoint(endpoint, baseUrl) {
  console.log(chalk.cyan(`\n测试端点: ${endpoint.method} ${endpoint.path}`));
  console.log(chalk.gray(`操作ID: ${endpoint.operationId}`));
  console.log(chalk.gray(`描述: ${endpoint.summary || '无描述'}`));
  
  try {
    // 准备请求参数
    const url = `${baseUrl}${endpoint.path}`;
    const requestData = endpoint.requestSchema ? generateTestData(endpoint.requestSchema) : null;
    
    // 发送请求
    console.log(chalk.gray(`发送请求到: ${url}`));
    if (requestData && options.verbose) {
      console.log(chalk.gray('请求数据:'), requestData);
    }
    
    // 实际发送请求（这里只是模拟，实际项目中需要实现真实请求）
    console.log(chalk.yellow('注意: 这是一个模拟请求，未实际发送到服务器'));
    
    // 模拟响应
    const mockResponse = {
      success: true,
      message: '操作成功',
      data: endpoint.responseSchema ? generateTestData(endpoint.responseSchema) : {}
    };
    
    // 验证响应
    if (endpoint.responseSchema) {
      const validation = validateResponse(endpoint.responseSchema, mockResponse);
      
      if (validation.valid) {
        console.log(chalk.green('✓ 响应验证通过'));
      } else {
        console.log(chalk.red('✗ 响应验证失败'));
        console.log(chalk.red('验证错误:'), validation.errors);
      }
    } else {
      console.log(chalk.yellow('⚠ 无响应schema可验证'));
    }
    
    return {
      endpoint,
      success: true
    };
  } catch (error) {
    console.error(chalk.red(`✗ 测试失败: ${error.message}`));
    
    return {
      endpoint,
      success: false,
      error: error.message
    };
  }
}

// 主函数
async function main() {
  try {
    console.log(chalk.blue('开始API契约测试...'));
    
    // 加载OpenAPI规范
    const api = await loadOpenApiSpec();
    
    // 提取端点
    const allEndpoints = extractEndpoints(api);
    console.log(chalk.green(`发现 ${allEndpoints.length} 个API端点`));
    
    // 过滤端点
    let endpoints = allEndpoints;
    if (options.path) {
      endpoints = endpoints.filter(e => e.path === options.path);
    }
    if (options.method) {
      endpoints = endpoints.filter(e => e.method === options.method.toUpperCase());
    }
    
    if (!options.all && !options.path && !options.method) {
      console.log(chalk.yellow('提示: 未指定测试端点，将测试所有端点'));
      console.log(chalk.yellow('使用 --path 和 --method 选项指定特定端点，或使用 --all 测试所有端点'));
    }
    
    // 测试端点
    const results = [];
    for (const endpoint of endpoints) {
      const result = await testEndpoint(endpoint, options.baseUrl);
      results.push(result);
    }
    
    // 输出结果摘要
    console.log(chalk.blue('\n测试结果摘要:'));
    console.log(chalk.green(`✓ 成功: ${results.filter(r => r.success).length}`));
    console.log(chalk.red(`✗ 失败: ${results.filter(r => !r.success).length}`));
    
    // 如果有失败的测试，列出它们
    const failures = results.filter(r => !r.success);
    if (failures.length > 0) {
      console.log(chalk.red('\n失败的测试:'));
      failures.forEach(failure => {
        console.log(chalk.red(`- ${failure.endpoint.method} ${failure.endpoint.path}: ${failure.error}`));
      });
      process.exit(1);
    }
    
    console.log(chalk.green('\nAPI契约测试完成!'));
  } catch (error) {
    console.error(chalk.red('测试过程中出错:'), error);
    process.exit(1);
  }
}

// 执行主函数
main();
