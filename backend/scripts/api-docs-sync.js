/**
 * API文档同步脚本
 * 自动检查API实现与文档的一致性，并生成同步报告
 */

const fs = require('fs');
const path = require('path');
const swaggerJsdoc = require('swagger-jsdoc');
const { glob } = require('glob');

// Swagger配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'AIBUBB API',
      version: '2.1.1',
      description: 'AIBUBB AI辅助学习平台API文档',
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: '开发环境',
      },
      {
        url: 'https://api.aibubb.com',
        description: '生产环境',
      },
    ],
  },
  apis: ['./routes/*.js', './controllers/*.js', './models/*.js'],
};

class ApiDocsSyncService {
  constructor() {
    this.issues = [];
    this.stats = {
      totalRoutes: 0,
      documentedRoutes: 0,
      undocumentedRoutes: 0,
      inconsistentRoutes: 0,
      deprecatedRoutes: 0,
    };
  }

  /**
   * 执行API文档同步检查
   */
  async syncApiDocs() {
    console.log('🔄 开始API文档同步检查...\n');

    try {
      // 1. 扫描路由文件
      const routes = await this.scanRoutes();
      console.log(`📁 发现 ${routes.length} 个路由文件`);

      // 2. 提取API端点
      const endpoints = await this.extractEndpoints(routes);
      console.log(`🔗 提取 ${endpoints.length} 个API端点`);

      // 3. 生成Swagger文档
      const swaggerSpec = await this.generateSwaggerSpec();
      console.log(`📚 生成Swagger文档规范`);

      // 4. 检查文档一致性
      await this.checkDocumentationConsistency(endpoints, swaggerSpec);

      // 5. 验证示例代码
      await this.validateExamples(swaggerSpec);

      // 6. 检查废弃API
      await this.checkDeprecatedApis(endpoints);

      // 7. 生成同步报告
      const report = await this.generateSyncReport();

      // 8. 更新文档文件
      await this.updateDocumentationFiles(swaggerSpec);

      console.log('\n✅ API文档同步检查完成');
      return report;
    } catch (error) {
      console.error('❌ API文档同步失败:', error);
      throw error;
    }
  }

  /**
   * 扫描路由文件
   */
  async scanRoutes() {
    const routeFiles = await glob('./routes/*.js', { cwd: process.cwd() });
    return routeFiles.map(file => ({
      file,
      fullPath: path.resolve(file),
    }));
  }

  /**
   * 提取API端点
   */
  async extractEndpoints(routes) {
    const endpoints = [];

    for (const route of routes) {
      try {
        const content = fs.readFileSync(route.fullPath, 'utf8');
        const routeEndpoints = this.parseRouteFile(content, route.file);
        endpoints.push(...routeEndpoints);
      } catch (error) {
        this.issues.push({
          type: 'file_read_error',
          file: route.file,
          message: `无法读取路由文件: ${error.message}`,
        });
      }
    }

    this.stats.totalRoutes = endpoints.length;
    return endpoints;
  }

  /**
   * 解析路由文件
   */
  parseRouteFile(content, filename) {
    const endpoints = [];

    // 匹配路由定义的正则表达式
    const routePatterns = [
      /router\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)['"`]/g,
      /app\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)['"`]/g,
    ];

    routePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const [, method, path] = match;

        endpoints.push({
          method: method.toUpperCase(),
          path: this.normalizePath(path),
          file: filename,
          line: this.getLineNumber(content, match.index),
          hasSwaggerDoc: this.hasSwaggerDocumentation(content, match.index),
          swaggerDoc: this.extractSwaggerDoc(content, match.index),
        });
      }
    });

    return endpoints;
  }

  /**
   * 标准化路径
   */
  normalizePath(path) {
    // 处理路径参数
    return path.replace(/:(\w+)/g, '{$1}');
  }

  /**
   * 获取行号
   */
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  /**
   * 检查是否有Swagger文档
   */
  hasSwaggerDocumentation(content, routeIndex) {
    // 在路由定义前查找Swagger注释
    const beforeRoute = content.substring(0, routeIndex);
    const lines = beforeRoute.split('\n');

    // 向上查找最近的注释块
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim();

      if (line.includes('@swagger') || line.includes('* @')) {
        return true;
      }

      // 如果遇到非注释行且不是空行，停止查找
      if (line && !line.startsWith('*') && !line.startsWith('//') && !line.startsWith('/**')) {
        break;
      }
    }

    return false;
  }

  /**
   * 提取Swagger文档
   */
  extractSwaggerDoc(content, routeIndex) {
    const beforeRoute = content.substring(0, routeIndex);
    const lines = beforeRoute.split('\n');
    const docLines = [];

    // 向上查找注释块
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i];

      if (line.includes('/**')) {
        docLines.reverse();
        return docLines.join('\n');
      }

      if (line.includes('*/')) {
        continue;
      }

      if (line.trim().startsWith('*')) {
        docLines.push(line);
      }
    }

    return null;
  }

  /**
   * 生成Swagger规范
   */
  async generateSwaggerSpec() {
    try {
      const spec = swaggerJsdoc(swaggerOptions);
      return spec;
    } catch (error) {
      this.issues.push({
        type: 'swagger_generation_error',
        message: `Swagger规范生成失败: ${error.message}`,
      });
      return null;
    }
  }

  /**
   * 检查文档一致性
   */
  async checkDocumentationConsistency(endpoints, swaggerSpec) {
    console.log('🔍 检查文档一致性...');

    const swaggerPaths = swaggerSpec?.paths || {};

    for (const endpoint of endpoints) {
      const pathKey = endpoint.path;
      const methodKey = endpoint.method.toLowerCase();

      // 检查是否有文档
      if (!endpoint.hasSwaggerDoc) {
        this.issues.push({
          type: 'missing_documentation',
          endpoint: `${endpoint.method} ${endpoint.path}`,
          file: endpoint.file,
          line: endpoint.line,
          message: '缺少API文档',
        });
        this.stats.undocumentedRoutes++;
        continue;
      }

      this.stats.documentedRoutes++;

      // 检查Swagger规范中是否存在
      if (!swaggerPaths[pathKey] || !swaggerPaths[pathKey][methodKey]) {
        this.issues.push({
          type: 'swagger_missing',
          endpoint: `${endpoint.method} ${endpoint.path}`,
          file: endpoint.file,
          line: endpoint.line,
          message: 'Swagger规范中缺少此端点',
        });
        this.stats.inconsistentRoutes++;
        continue;
      }

      // 检查文档完整性
      const swaggerEndpoint = swaggerPaths[pathKey][methodKey];
      this.validateEndpointDocumentation(endpoint, swaggerEndpoint);
    }

    // 检查Swagger中是否有多余的端点
    this.checkOrphanedSwaggerEndpoints(endpoints, swaggerPaths);
  }

  /**
   * 验证端点文档
   */
  validateEndpointDocumentation(endpoint, swaggerEndpoint) {
    const required = ['summary', 'description', 'responses'];
    const missing = [];

    required.forEach(field => {
      if (!swaggerEndpoint[field]) {
        missing.push(field);
      }
    });

    if (missing.length > 0) {
      this.issues.push({
        type: 'incomplete_documentation',
        endpoint: `${endpoint.method} ${endpoint.path}`,
        file: endpoint.file,
        line: endpoint.line,
        message: `文档不完整，缺少: ${missing.join(', ')}`,
      });
    }

    // 检查响应状态码
    if (swaggerEndpoint.responses) {
      const hasSuccessResponse = Object.keys(swaggerEndpoint.responses).some(code =>
        code.startsWith('2'),
      );

      if (!hasSuccessResponse) {
        this.issues.push({
          type: 'missing_success_response',
          endpoint: `${endpoint.method} ${endpoint.path}`,
          file: endpoint.file,
          line: endpoint.line,
          message: '缺少成功响应状态码定义',
        });
      }
    }

    // 检查参数文档
    if (endpoint.path.includes('{') && !swaggerEndpoint.parameters) {
      this.issues.push({
        type: 'missing_parameters',
        endpoint: `${endpoint.method} ${endpoint.path}`,
        file: endpoint.file,
        line: endpoint.line,
        message: '路径包含参数但缺少参数文档',
      });
    }
  }

  /**
   * 检查孤立的Swagger端点
   */
  checkOrphanedSwaggerEndpoints(endpoints, swaggerPaths) {
    const implementedEndpoints = new Set(
      endpoints.map(ep => `${ep.method.toLowerCase()}:${ep.path}`),
    );

    Object.keys(swaggerPaths).forEach(path => {
      Object.keys(swaggerPaths[path]).forEach(method => {
        const endpointKey = `${method}:${path}`;

        if (!implementedEndpoints.has(endpointKey)) {
          this.issues.push({
            type: 'orphaned_documentation',
            endpoint: `${method.toUpperCase()} ${path}`,
            message: 'Swagger文档中存在但未实现的端点',
          });
        }
      });
    });
  }

  /**
   * 验证示例代码
   */
  async validateExamples(swaggerSpec) {
    console.log('🧪 验证示例代码...');

    if (!swaggerSpec?.paths) return;

    Object.keys(swaggerSpec.paths).forEach(path => {
      Object.keys(swaggerSpec.paths[path]).forEach(method => {
        const endpoint = swaggerSpec.paths[path][method];

        // 检查请求示例
        if (endpoint.requestBody?.content) {
          Object.keys(endpoint.requestBody.content).forEach(contentType => {
            const content = endpoint.requestBody.content[contentType];
            if (content.example) {
              this.validateJsonExample(content.example, `${method.toUpperCase()} ${path} 请求示例`);
            }
          });
        }

        // 检查响应示例
        if (endpoint.responses) {
          Object.keys(endpoint.responses).forEach(statusCode => {
            const response = endpoint.responses[statusCode];
            if (response.content) {
              Object.keys(response.content).forEach(contentType => {
                const content = response.content[contentType];
                if (content.example) {
                  this.validateJsonExample(
                    content.example,
                    `${method.toUpperCase()} ${path} 响应示例 (${statusCode})`,
                  );
                }
              });
            }
          });
        }
      });
    });
  }

  /**
   * 验证JSON示例
   */
  validateJsonExample(example, context) {
    try {
      if (typeof example === 'string') {
        JSON.parse(example);
      }
    } catch (error) {
      this.issues.push({
        type: 'invalid_example',
        context,
        message: `JSON示例格式错误: ${error.message}`,
      });
    }
  }

  /**
   * 检查废弃API
   */
  async checkDeprecatedApis(endpoints) {
    console.log('🗑️ 检查废弃API...');

    endpoints.forEach(endpoint => {
      // 检查路径中是否包含版本信息
      if (endpoint.path.includes('/v1/')) {
        this.issues.push({
          type: 'deprecated_version',
          endpoint: `${endpoint.method} ${endpoint.path}`,
          file: endpoint.file,
          line: endpoint.line,
          message: '使用了废弃的API版本 v1',
        });
        this.stats.deprecatedRoutes++;
      }

      // 检查Swagger文档中的deprecated标记
      if (endpoint.swaggerDoc && endpoint.swaggerDoc.includes('deprecated')) {
        this.stats.deprecatedRoutes++;
      }
    });
  }

  /**
   * 生成同步报告
   */
  async generateSyncReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        ...this.stats,
        issueCount: this.issues.length,
        documentationCoverage:
          this.stats.totalRoutes > 0
            ? Math.round((this.stats.documentedRoutes / this.stats.totalRoutes) * 100)
            : 0,
      },
      issues: this.issues,
      recommendations: this.generateRecommendations(),
    };

    // 保存报告到文件
    const reportPath = path.join(process.cwd(), 'api-docs-sync-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // 生成Markdown报告
    const markdownReport = this.generateMarkdownReport(report);
    const markdownPath = path.join(process.cwd(), 'api-docs-sync-report.md');
    fs.writeFileSync(markdownPath, markdownReport);

    console.log(`\n📄 同步报告已生成:`);
    console.log(`   JSON: ${reportPath}`);
    console.log(`   Markdown: ${markdownPath}`);

    return report;
  }

  /**
   * 生成改进建议
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.stats.undocumentedRoutes > 0) {
      recommendations.push({
        type: 'documentation',
        priority: 'high',
        message: `有 ${this.stats.undocumentedRoutes} 个API端点缺少文档，建议添加Swagger注释`,
      });
    }

    if (this.stats.inconsistentRoutes > 0) {
      recommendations.push({
        type: 'consistency',
        priority: 'medium',
        message: `有 ${this.stats.inconsistentRoutes} 个API端点文档不一致，需要同步更新`,
      });
    }

    if (this.stats.deprecatedRoutes > 0) {
      recommendations.push({
        type: 'deprecation',
        priority: 'low',
        message: `有 ${this.stats.deprecatedRoutes} 个废弃API，建议制定迁移计划`,
      });
    }

    const coverage =
      this.stats.totalRoutes > 0
        ? Math.round((this.stats.documentedRoutes / this.stats.totalRoutes) * 100)
        : 0;

    if (coverage < 80) {
      recommendations.push({
        type: 'coverage',
        priority: 'high',
        message: `API文档覆盖率仅为 ${coverage}%，建议提升至80%以上`,
      });
    }

    return recommendations;
  }

  /**
   * 生成Markdown报告
   */
  generateMarkdownReport(report) {
    let markdown = `# API文档同步报告\n\n`;
    markdown += `**生成时间**: ${new Date(report.timestamp).toLocaleString()}\n\n`;

    // 概览
    markdown += `## 📊 概览\n\n`;
    markdown += `| 指标 | 数量 |\n`;
    markdown += `|------|------|\n`;
    markdown += `| 总API端点 | ${report.summary.totalRoutes} |\n`;
    markdown += `| 已文档化 | ${report.summary.documentedRoutes} |\n`;
    markdown += `| 未文档化 | ${report.summary.undocumentedRoutes} |\n`;
    markdown += `| 不一致 | ${report.summary.inconsistentRoutes} |\n`;
    markdown += `| 已废弃 | ${report.summary.deprecatedRoutes} |\n`;
    markdown += `| 文档覆盖率 | ${report.summary.documentationCoverage}% |\n`;
    markdown += `| 问题总数 | ${report.summary.issueCount} |\n\n`;

    // 问题列表
    if (report.issues.length > 0) {
      markdown += `## ❌ 发现的问题\n\n`;

      const issuesByType = {};
      report.issues.forEach(issue => {
        if (!issuesByType[issue.type]) {
          issuesByType[issue.type] = [];
        }
        issuesByType[issue.type].push(issue);
      });

      Object.keys(issuesByType).forEach(type => {
        const typeIssues = issuesByType[type];
        markdown += `### ${this.getIssueTypeTitle(type)} (${typeIssues.length})\n\n`;

        typeIssues.forEach((issue, index) => {
          markdown += `${index + 1}. **${issue.endpoint || issue.context || '未知'}**\n`;
          markdown += `   - ${issue.message}\n`;
          if (issue.file) {
            markdown += `   - 文件: ${issue.file}`;
            if (issue.line) {
              markdown += `:${issue.line}`;
            }
            markdown += `\n`;
          }
          markdown += `\n`;
        });
      });
    } else {
      markdown += `## ✅ 未发现问题\n\n`;
    }

    // 改进建议
    if (report.recommendations.length > 0) {
      markdown += `## 💡 改进建议\n\n`;
      report.recommendations.forEach((rec, index) => {
        const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢';
        markdown += `${index + 1}. ${priority} **${rec.type}**: ${rec.message}\n`;
      });
    }

    return markdown;
  }

  /**
   * 获取问题类型标题
   */
  getIssueTypeTitle(type) {
    const titles = {
      missing_documentation: '缺少文档',
      swagger_missing: 'Swagger规范缺失',
      incomplete_documentation: '文档不完整',
      missing_success_response: '缺少成功响应',
      missing_parameters: '缺少参数文档',
      orphaned_documentation: '孤立文档',
      invalid_example: '无效示例',
      deprecated_version: '废弃版本',
      file_read_error: '文件读取错误',
      swagger_generation_error: 'Swagger生成错误',
    };

    return titles[type] || type;
  }

  /**
   * 更新文档文件
   */
  async updateDocumentationFiles(swaggerSpec) {
    console.log('📝 更新文档文件...');

    try {
      // 更新swagger.json
      const swaggerPath = path.join(process.cwd(), 'docs', 'swagger.json');
      fs.mkdirSync(path.dirname(swaggerPath), { recursive: true });
      fs.writeFileSync(swaggerPath, JSON.stringify(swaggerSpec, null, 2));

      // 更新API文档索引
      await this.updateApiIndex();

      console.log('✅ 文档文件更新完成');
    } catch (error) {
      this.issues.push({
        type: 'file_update_error',
        message: `文档文件更新失败: ${error.message}`,
      });
    }
  }

  /**
   * 更新API文档索引
   */
  async updateApiIndex() {
    const indexContent = `# API文档索引

## 文档文件

- [Swagger JSON](./swagger.json) - OpenAPI 3.0规范
- [API同步报告](../api-docs-sync-report.md) - 文档同步状态

## 在线文档

- [Swagger UI](http://localhost:3000/api-docs) - 交互式API文档
- [ReDoc](http://localhost:3000/redoc) - 美观的API文档

## 最后更新

${new Date().toLocaleString()}
`;

    const indexPath = path.join(process.cwd(), 'docs', 'README.md');
    fs.writeFileSync(indexPath, indexContent);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const syncService = new ApiDocsSyncService();
    const report = await syncService.syncApiDocs();

    // 输出结果
    console.log('\n📈 同步结果:');
    console.log(`   文档覆盖率: ${report.summary.documentationCoverage}%`);
    console.log(`   问题数量: ${report.summary.issueCount}`);

    if (report.summary.issueCount === 0) {
      console.log('\n🎉 API文档完全同步！');
      process.exit(0);
    } else {
      console.log('\n⚠️  发现文档同步问题，请查看报告进行修复');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ API文档同步失败:', error);
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = ApiDocsSyncService;
