/**
 * API版本比较工具
 * 比较两个OpenAPI规范文件，检测不兼容变更
 * 
 * 使用方法:
 * node backend/scripts/api-version-compare.js --old=path/to/old-swagger.json --new=path/to/new-swagger.json
 */

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const chalk = require('chalk');
const swaggerParser = require('@apidevtools/swagger-parser');

// 定义命令行选项
program
  .option('--old <path>', '旧版API规范文件路径')
  .option('--new <path>', '新版API规范文件路径')
  .option('--output <path>', '输出结果到文件')
  .option('--verbose', '显示详细输出')
  .parse(process.argv);

const options = program.opts();

// 验证必要的选项
if (!options.old || !options.new) {
  console.error(chalk.red('错误: 必须提供旧版和新版API规范文件路径'));
  console.log(chalk.yellow('使用方法: node api-version-compare.js --old=path/to/old-swagger.json --new=path/to/new-swagger.json'));
  process.exit(1);
}

// 加载OpenAPI规范
async function loadOpenApiSpec(filePath) {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.error(chalk.red(`错误: 文件不存在: ${filePath}`));
      process.exit(1);
    }
    
    // 解析并验证OpenAPI规范
    const api = await swaggerParser.validate(filePath);
    return api;
  } catch (error) {
    console.error(chalk.red(`OpenAPI规范验证失败: ${filePath}`), error);
    process.exit(1);
  }
}

// 提取端点信息
function extractEndpoints(api) {
  const endpoints = {};
  
  // 遍历所有路径
  Object.keys(api.paths).forEach(path => {
    const pathItem = api.paths[path];
    
    // 遍历路径下的所有HTTP方法
    Object.keys(pathItem).forEach(method => {
      if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
        const operation = pathItem[method];
        const key = `${method.toUpperCase()} ${path}`;
        
        // 提取请求体schema
        let requestSchema = null;
        if (operation.requestBody && 
            operation.requestBody.content && 
            operation.requestBody.content['application/json'] &&
            operation.requestBody.content['application/json'].schema) {
          requestSchema = operation.requestBody.content['application/json'].schema;
        }
        
        // 提取响应schema
        let responseSchema = null;
        if (operation.responses && 
            operation.responses['200'] && 
            operation.responses['200'].content && 
            operation.responses['200'].content['application/json'] &&
            operation.responses['200'].content['application/json'].schema) {
          responseSchema = operation.responses['200'].content['application/json'].schema;
        }
        
        endpoints[key] = {
          path,
          method: method.toUpperCase(),
          operationId: operation.operationId,
          summary: operation.summary,
          requestSchema,
          responseSchema,
          parameters: operation.parameters || [],
          deprecated: operation.deprecated || false
        };
      }
    });
  });
  
  return endpoints;
}

// 比较两个schema
function compareSchemas(oldSchema, newSchema, path = '') {
  if (!oldSchema || !newSchema) {
    return oldSchema === newSchema ? [] : [{
      path,
      type: 'schema_change',
      description: `Schema ${oldSchema ? '被移除' : '被添加'}`
    }];
  }
  
  const changes = [];
  
  // 比较类型
  if (oldSchema.type !== newSchema.type) {
    changes.push({
      path,
      type: 'breaking_change',
      description: `类型变更: ${oldSchema.type || 'undefined'} -> ${newSchema.type || 'undefined'}`
    });
  }
  
  // 比较属性
  if (oldSchema.properties && newSchema.properties) {
    // 检查移除的属性
    Object.keys(oldSchema.properties).forEach(prop => {
      const propPath = path ? `${path}.${prop}` : prop;
      
      if (!newSchema.properties[prop]) {
        changes.push({
          path: propPath,
          type: 'breaking_change',
          description: `属性被移除: ${prop}`
        });
      } else {
        // 递归比较属性schema
        const propChanges = compareSchemas(
          oldSchema.properties[prop],
          newSchema.properties[prop],
          propPath
        );
        changes.push(...propChanges);
      }
    });
    
    // 检查新增的属性
    Object.keys(newSchema.properties).forEach(prop => {
      const propPath = path ? `${path}.${prop}` : prop;
      
      if (!oldSchema.properties[prop]) {
        // 新增属性不一定是破坏性变更，除非它是必需的
        const isRequired = newSchema.required && newSchema.required.includes(prop);
        
        changes.push({
          path: propPath,
          type: isRequired ? 'breaking_change' : 'non_breaking_change',
          description: `属性被添加: ${prop}${isRequired ? ' (必需)' : ''}`
        });
      }
    });
  }
  
  // 比较必需属性
  if (oldSchema.required && newSchema.required) {
    // 检查新增的必需属性
    newSchema.required.forEach(prop => {
      if (!oldSchema.required.includes(prop)) {
        changes.push({
          path: path ? `${path}.${prop}` : prop,
          type: 'breaking_change',
          description: `属性变为必需: ${prop}`
        });
      }
    });
  } else if (!oldSchema.required && newSchema.required) {
    changes.push({
      path,
      type: 'breaking_change',
      description: `添加了必需属性: ${newSchema.required.join(', ')}`
    });
  }
  
  return changes;
}

// 比较参数
function compareParameters(oldParams, newParams) {
  if (!oldParams || !newParams) {
    return oldParams === newParams ? [] : [{
      type: 'parameter_change',
      description: `参数 ${oldParams ? '被移除' : '被添加'}`
    }];
  }
  
  const changes = [];
  
  // 创建参数映射
  const oldParamsMap = {};
  const newParamsMap = {};
  
  oldParams.forEach(param => {
    oldParamsMap[`${param.in}:${param.name}`] = param;
  });
  
  newParams.forEach(param => {
    newParamsMap[`${param.in}:${param.name}`] = param;
  });
  
  // 检查移除的参数
  Object.keys(oldParamsMap).forEach(key => {
    if (!newParamsMap[key]) {
      const param = oldParamsMap[key];
      changes.push({
        type: 'breaking_change',
        description: `参数被移除: ${param.name} (${param.in})`
      });
    }
  });
  
  // 检查新增的参数
  Object.keys(newParamsMap).forEach(key => {
    if (!oldParamsMap[key]) {
      const param = newParamsMap[key];
      // 新增参数不一定是破坏性变更，除非它是必需的
      changes.push({
        type: param.required ? 'breaking_change' : 'non_breaking_change',
        description: `参数被添加: ${param.name} (${param.in})${param.required ? ' (必需)' : ''}`
      });
    }
  });
  
  // 检查参数属性变更
  Object.keys(newParamsMap).forEach(key => {
    if (oldParamsMap[key]) {
      const oldParam = oldParamsMap[key];
      const newParam = newParamsMap[key];
      
      // 检查必需性变更
      if (!oldParam.required && newParam.required) {
        changes.push({
          type: 'breaking_change',
          description: `参数变为必需: ${newParam.name} (${newParam.in})`
        });
      }
      
      // 检查类型变更
      if (oldParam.schema && newParam.schema && oldParam.schema.type !== newParam.schema.type) {
        changes.push({
          type: 'breaking_change',
          description: `参数类型变更: ${newParam.name} (${newParam.in}) ${oldParam.schema.type} -> ${newParam.schema.type}`
        });
      }
    }
  });
  
  return changes;
}

// 比较端点
function compareEndpoints(oldEndpoints, newEndpoints) {
  const changes = [];
  
  // 检查移除的端点
  Object.keys(oldEndpoints).forEach(key => {
    if (!newEndpoints[key]) {
      changes.push({
        endpoint: key,
        type: 'breaking_change',
        description: `端点被移除: ${key}`
      });
    }
  });
  
  // 检查新增的端点
  Object.keys(newEndpoints).forEach(key => {
    if (!oldEndpoints[key]) {
      changes.push({
        endpoint: key,
        type: 'non_breaking_change',
        description: `端点被添加: ${key}`
      });
    }
  });
  
  // 检查端点变更
  Object.keys(newEndpoints).forEach(key => {
    if (oldEndpoints[key]) {
      const oldEndpoint = oldEndpoints[key];
      const newEndpoint = newEndpoints[key];
      
      // 检查废弃状态
      if (!oldEndpoint.deprecated && newEndpoint.deprecated) {
        changes.push({
          endpoint: key,
          type: 'non_breaking_change',
          description: `端点被标记为废弃: ${key}`
        });
      }
      
      // 比较请求schema
      const requestChanges = compareSchemas(
        oldEndpoint.requestSchema,
        newEndpoint.requestSchema,
        'request'
      );
      
      requestChanges.forEach(change => {
        changes.push({
          endpoint: key,
          ...change
        });
      });
      
      // 比较响应schema
      const responseChanges = compareSchemas(
        oldEndpoint.responseSchema,
        newEndpoint.responseSchema,
        'response'
      );
      
      responseChanges.forEach(change => {
        changes.push({
          endpoint: key,
          ...change
        });
      });
      
      // 比较参数
      const paramChanges = compareParameters(
        oldEndpoint.parameters,
        newEndpoint.parameters
      );
      
      paramChanges.forEach(change => {
        changes.push({
          endpoint: key,
          ...change
        });
      });
    }
  });
  
  return changes;
}

// 主函数
async function main() {
  try {
    console.log(chalk.blue('开始比较API版本...'));
    
    // 加载OpenAPI规范
    console.log(chalk.gray(`加载旧版API规范: ${options.old}`));
    const oldApi = await loadOpenApiSpec(options.old);
    console.log(chalk.green(`✓ 旧版API规范加载成功: ${oldApi.info.title} v${oldApi.info.version}`));
    
    console.log(chalk.gray(`加载新版API规范: ${options.new}`));
    const newApi = await loadOpenApiSpec(options.new);
    console.log(chalk.green(`✓ 新版API规范加载成功: ${newApi.info.title} v${newApi.info.version}`));
    
    // 提取端点
    const oldEndpoints = extractEndpoints(oldApi);
    const newEndpoints = extractEndpoints(newApi);
    
    console.log(chalk.gray(`旧版API端点数量: ${Object.keys(oldEndpoints).length}`));
    console.log(chalk.gray(`新版API端点数量: ${Object.keys(newEndpoints).length}`));
    
    // 比较端点
    const changes = compareEndpoints(oldEndpoints, newEndpoints);
    
    // 分类变更
    const breakingChanges = changes.filter(c => c.type === 'breaking_change');
    const nonBreakingChanges = changes.filter(c => c.type === 'non_breaking_change');
    
    // 输出结果
    console.log(chalk.blue('\n比较结果:'));
    console.log(chalk.red(`破坏性变更: ${breakingChanges.length}`));
    console.log(chalk.yellow(`非破坏性变更: ${nonBreakingChanges.length}`));
    
    // 输出破坏性变更
    if (breakingChanges.length > 0) {
      console.log(chalk.red('\n破坏性变更:'));
      breakingChanges.forEach(change => {
        console.log(chalk.red(`- ${change.endpoint}: ${change.description}`));
        if (options.verbose && change.path) {
          console.log(chalk.gray(`  路径: ${change.path}`));
        }
      });
    }
    
    // 输出非破坏性变更
    if (nonBreakingChanges.length > 0 && options.verbose) {
      console.log(chalk.yellow('\n非破坏性变更:'));
      nonBreakingChanges.forEach(change => {
        console.log(chalk.yellow(`- ${change.endpoint}: ${change.description}`));
        if (change.path) {
          console.log(chalk.gray(`  路径: ${change.path}`));
        }
      });
    }
    
    // 输出结果到文件
    if (options.output) {
      const output = {
        timestamp: new Date().toISOString(),
        oldVersion: oldApi.info.version,
        newVersion: newApi.info.version,
        breakingChangesCount: breakingChanges.length,
        nonBreakingChangesCount: nonBreakingChanges.length,
        breakingChanges,
        nonBreakingChanges
      };
      
      fs.writeFileSync(options.output, JSON.stringify(output, null, 2));
      console.log(chalk.green(`\n结果已保存到: ${options.output}`));
    }
    
    console.log(chalk.green('\nAPI版本比较完成!'));
    
    // 如果有破坏性变更，以非零状态码退出
    if (breakingChanges.length > 0) {
      process.exit(1);
    }
  } catch (error) {
    console.error(chalk.red('比较过程中出错:'), error);
    process.exit(1);
  }
}

// 执行主函数
main();
