/**
 * 数据库和文件备份脚本
 */

require('dotenv').config();

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const archiver = require('archiver');
const config = require('../config/config');
const logger = require('../config/logger');

class BackupService {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || path.join(process.cwd(), 'backups');
    this.retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 30;
    this.compressionLevel = parseInt(process.env.BACKUP_COMPRESSION_LEVEL) || 6;

    // 确保备份目录存在
    this.ensureBackupDirectory();
  }

  /**
   * 确保备份目录存在
   */
  ensureBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      logger.info(`Created backup directory: ${this.backupDir}`);
    }
  }

  /**
   * 执行完整备份
   */
  async performFullBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `full-backup-${timestamp}`;
    const backupPath = path.join(this.backupDir, backupName);

    logger.info(`Starting full backup: ${backupName}`);

    try {
      // 创建备份目录
      fs.mkdirSync(backupPath, { recursive: true });

      // 备份数据库
      await this.backupDatabase(backupPath);

      // 备份文件
      await this.backupFiles(backupPath);

      // 备份配置
      await this.backupConfiguration(backupPath);

      // 创建备份元数据
      await this.createBackupMetadata(backupPath);

      // 压缩备份
      const archivePath = await this.compressBackup(backupPath, `${backupName}.tar.gz`);

      // 清理临时目录
      await this.cleanupDirectory(backupPath);

      // 清理旧备份
      await this.cleanupOldBackups();

      logger.info(`Full backup completed: ${archivePath}`);
      return archivePath;
    } catch (error) {
      logger.error('Full backup failed:', error);
      throw error;
    }
  }

  /**
   * 备份数据库
   */
  async backupDatabase(backupPath) {
    logger.info('Backing up database...');

    const dbConfig = config.database;
    const dumpFile = path.join(backupPath, 'database.sql');

    const mysqldumpArgs = [
      '--host=' + dbConfig.host,
      '--port=' + (dbConfig.port || 3306),
      '--user=' + dbConfig.username,
      '--password=' + dbConfig.password,
      '--single-transaction',
      '--routines',
      '--triggers',
      '--events',
      '--add-drop-database',
      '--add-drop-table',
      '--create-options',
      '--disable-keys',
      '--extended-insert',
      '--quick',
      '--lock-tables=false',
      dbConfig.database,
    ];

    return new Promise((resolve, reject) => {
      const mysqldump = spawn('mysqldump', mysqldumpArgs);
      const writeStream = fs.createWriteStream(dumpFile);

      mysqldump.stdout.pipe(writeStream);

      let stderr = '';
      mysqldump.stderr.on('data', data => {
        stderr += data.toString();
      });

      mysqldump.on('close', code => {
        writeStream.end();
        if (code === 0) {
          logger.info(`Database backup completed: ${dumpFile}`);
          resolve(dumpFile);
        } else {
          logger.error(`mysqldump failed with code ${code}: ${stderr}`);
          reject(new Error(`mysqldump failed: ${stderr}`));
        }
      });

      mysqldump.on('error', error => {
        writeStream.end();
        logger.error('mysqldump process error:', error);
        reject(error);
      });
    });
  }

  /**
   * 备份文件
   */
  async backupFiles(backupPath) {
    logger.info('Backing up files...');

    const filesToBackup = [
      'uploads',
      'logs',
      'public',
      '.env.production',
      'package.json',
      'package-lock.json',
    ];

    const filesBackupPath = path.join(backupPath, 'files');
    fs.mkdirSync(filesBackupPath, { recursive: true });

    for (const item of filesToBackup) {
      const sourcePath = path.join(process.cwd(), item);
      const targetPath = path.join(filesBackupPath, item);

      if (fs.existsSync(sourcePath)) {
        await this.copyRecursive(sourcePath, targetPath);
        logger.info(`Backed up: ${item}`);
      } else {
        logger.warn(`File/directory not found, skipping: ${item}`);
      }
    }

    logger.info('Files backup completed');
  }

  /**
   * 备份配置
   */
  async backupConfiguration(backupPath) {
    logger.info('Backing up configuration...');

    const configBackupPath = path.join(backupPath, 'config');
    fs.mkdirSync(configBackupPath, { recursive: true });

    // 备份配置文件
    const configFiles = ['config', 'middlewares', 'infrastructure'];

    for (const configDir of configFiles) {
      const sourcePath = path.join(process.cwd(), configDir);
      const targetPath = path.join(configBackupPath, configDir);

      if (fs.existsSync(sourcePath)) {
        await this.copyRecursive(sourcePath, targetPath);
        logger.info(`Backed up config: ${configDir}`);
      }
    }

    // 备份Docker配置
    const dockerFiles = ['Dockerfile', 'docker-compose.yml', '.dockerignore'];
    for (const dockerFile of dockerFiles) {
      const sourcePath = path.join(process.cwd(), dockerFile);
      const targetPath = path.join(configBackupPath, dockerFile);

      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath);
        logger.info(`Backed up Docker config: ${dockerFile}`);
      }
    }

    logger.info('Configuration backup completed');
  }

  /**
   * 创建备份元数据
   */
  async createBackupMetadata(backupPath) {
    const metadata = {
      timestamp: new Date().toISOString(),
      version: require('../package.json').version,
      environment: process.env.NODE_ENV,
      database: {
        host: config.database.host,
        name: config.database.database,
      },
      backup: {
        type: 'full',
        retention_days: this.retentionDays,
        compression_level: this.compressionLevel,
      },
      system: {
        node_version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };

    const metadataFile = path.join(backupPath, 'metadata.json');
    fs.writeFileSync(metadataFile, JSON.stringify(metadata, null, 2));

    logger.info('Backup metadata created');
  }

  /**
   * 压缩备份
   */
  async compressBackup(backupPath, archiveName) {
    logger.info(`Compressing backup: ${archiveName}`);

    const archivePath = path.join(this.backupDir, archiveName);
    const output = fs.createWriteStream(archivePath);
    const archive = archiver('tar', {
      gzip: true,
      gzipOptions: {
        level: this.compressionLevel,
      },
    });

    return new Promise((resolve, reject) => {
      output.on('close', () => {
        const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
        logger.info(`Backup compressed: ${archivePath} (${sizeInMB} MB)`);
        resolve(archivePath);
      });

      archive.on('error', error => {
        logger.error('Archive error:', error);
        reject(error);
      });

      archive.pipe(output);
      archive.directory(backupPath, false);
      archive.finalize();
    });
  }

  /**
   * 递归复制文件/目录
   */
  async copyRecursive(source, target) {
    const stat = fs.statSync(source);

    if (stat.isDirectory()) {
      if (!fs.existsSync(target)) {
        fs.mkdirSync(target, { recursive: true });
      }

      const files = fs.readdirSync(source);
      for (const file of files) {
        const sourcePath = path.join(source, file);
        const targetPath = path.join(target, file);
        await this.copyRecursive(sourcePath, targetPath);
      }
    } else {
      const targetDir = path.dirname(target);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }
      fs.copyFileSync(source, target);
    }
  }

  /**
   * 清理目录
   */
  async cleanupDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath);
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          await this.cleanupDirectory(filePath);
        } else {
          fs.unlinkSync(filePath);
        }
      }
      fs.rmdirSync(dirPath);
    }
  }

  /**
   * 清理旧备份
   */
  async cleanupOldBackups() {
    logger.info('Cleaning up old backups...');

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

    const files = fs.readdirSync(this.backupDir);
    let deletedCount = 0;

    for (const file of files) {
      const filePath = path.join(this.backupDir, file);
      const stat = fs.statSync(filePath);

      if (stat.mtime < cutoffDate) {
        if (stat.isDirectory()) {
          await this.cleanupDirectory(filePath);
        } else {
          fs.unlinkSync(filePath);
        }
        deletedCount++;
        logger.info(`Deleted old backup: ${file}`);
      }
    }

    logger.info(`Cleanup completed. Deleted ${deletedCount} old backups.`);
  }

  /**
   * 列出备份
   */
  listBackups() {
    const files = fs.readdirSync(this.backupDir);
    const backups = [];

    for (const file of files) {
      const filePath = path.join(this.backupDir, file);
      const stat = fs.statSync(filePath);

      if (file.endsWith('.tar.gz')) {
        backups.push({
          name: file,
          path: filePath,
          size: stat.size,
          created: stat.mtime,
        });
      }
    }

    return backups.sort((a, b) => b.created - a.created);
  }

  /**
   * 验证备份
   */
  async validateBackup(backupPath) {
    logger.info(`Validating backup: ${backupPath}`);

    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }

    // 检查文件大小
    const stat = fs.statSync(backupPath);
    if (stat.size === 0) {
      throw new Error('Backup file is empty');
    }

    // 可以添加更多验证逻辑，如解压测试等

    logger.info('Backup validation passed');
    return true;
  }

  /**
   * 获取备份统计
   */
  getBackupStats() {
    const backups = this.listBackups();
    const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);

    return {
      count: backups.length,
      totalSize: totalSize,
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      oldestBackup: backups.length > 0 ? backups[backups.length - 1].created : null,
      newestBackup: backups.length > 0 ? backups[0].created : null,
      retentionDays: this.retentionDays,
      backupDir: this.backupDir,
    };
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'full';

  const backupService = new BackupService();

  try {
    switch (command) {
      case 'full':
        await backupService.performFullBackup();
        break;

      case 'list':
        const backups = backupService.listBackups();
        console.log('Available backups:');
        backups.forEach(backup => {
          console.log(
            `  ${backup.name} (${(backup.size / 1024 / 1024).toFixed(2)} MB) - ${backup.created}`,
          );
        });
        break;

      case 'stats':
        const stats = backupService.getBackupStats();
        console.log('Backup statistics:');
        console.log(`  Count: ${stats.count}`);
        console.log(`  Total size: ${stats.totalSizeMB} MB`);
        console.log(`  Retention: ${stats.retentionDays} days`);
        console.log(`  Directory: ${stats.backupDir}`);
        break;

      case 'cleanup':
        await backupService.cleanupOldBackups();
        break;

      case 'validate':
        if (args[1]) {
          await backupService.validateBackup(args[1]);
          console.log('Backup validation passed');
        } else {
          console.error('Please provide backup file path');
          process.exit(1);
        }
        break;

      default:
        console.log('Usage: node backup.js [command]');
        console.log('Commands:');
        console.log('  full     - Perform full backup (default)');
        console.log('  list     - List available backups');
        console.log('  stats    - Show backup statistics');
        console.log('  cleanup  - Clean up old backups');
        console.log('  validate <path> - Validate backup file');
        break;
    }
  } catch (error) {
    logger.error('Backup operation failed:', error);
    console.error('Backup operation failed:', error.message);
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = BackupService;
