/**
 * 数据库备份脚本
 *
 * 使用方法：
 * node backend/scripts/backup_database.js
 */

const { execFile } = require('child_process');
const fs = require('fs');
const path = require('path');
const config = require('../config/config');
const logger = require('../config/logger');
const { promisify } = require('util');

// 创建备份目录
const backupDir = path.join(__dirname, '../../backups');
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

// 生成备份文件名
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const backupFileName = `aibubb_db_backup_${timestamp}.sql`;
const backupFilePath = path.join(backupDir, backupFileName);

// 获取数据库配置
const dbConfig = config.database;
const { host, port, user, password, name } = dbConfig;

// 准备mysqldump参数
const mysqldumpArgs = [
  '-h', host,
  '-P', port.toString(),
  '-u', user
];

// 安全地添加密码参数
if (password) {
  mysqldumpArgs.push(`-p${password}`);
}

// 添加数据库名称
mysqldumpArgs.push(name);

logger.info(`开始备份数据库 ${name} 到 ${backupFilePath}`);

// 使用流重定向到文件，而不是在命令中使用重定向
const backupFileStream = fs.createWriteStream(backupFilePath);

// 执行备份
const mysqldumpProcess = execFile('mysqldump', mysqldumpArgs, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
  if (error) {
    logger.error(`数据库备份失败: ${error.message}`);
    logger.error(stderr);
    process.exit(1);
  }

  // 将输出写入文件
  backupFileStream.write(stdout);
  backupFileStream.end();
});

// 处理流完成事件
backupFileStream.on('finish', () => {
  // 检查备份文件是否创建成功
  try {
    const stats = fs.statSync(backupFilePath);
    const fileSizeInMB = stats.size / (1024 * 1024);
    logger.info(`数据库备份成功，文件大小: ${fileSizeInMB.toFixed(2)} MB`);
    logger.info(`备份文件路径: ${backupFilePath}`);
    process.exit(0);
  } catch (err) {
    logger.error(`备份文件检查失败: ${err.message}`);
    process.exit(1);
  }
});

// 处理流错误事件
backupFileStream.on('error', (err) => {
  logger.error(`备份文件写入失败: ${err.message}`);
  process.exit(1);
});
