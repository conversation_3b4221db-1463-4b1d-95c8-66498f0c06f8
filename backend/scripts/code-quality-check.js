#!/usr/bin/env node

/**
 * 代码质量检查脚本
 * 用于在CI/CD流程中运行，检查代码质量并生成报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');

// 定义颜色
const RED = '\x1b[31m';
const GREEN = '\x1b[32m';
const YELLOW = '\x1b[33m';
const BLUE = '\x1b[34m';
const RESET = '\x1b[0m';

// 定义报告目录
const REPORTS_DIR = path.resolve(__dirname, '../../code-quality-reports');

// 创建报告目录
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

// 定义质量指标阈值
const THRESHOLDS = {
  eslint: {
    errors: 0,
    warnings: 10
  },
  complexity: {
    max: 10
  },
  coverage: {
    lines: 70,
    statements: 70,
    functions: 70,
    branches: 60
  },
  duplications: {
    percentage: 5
  }
};

/**
 * 运行ESLint检查
 */
function runEslintCheck() {
  console.log(`${BLUE}=== 运行ESLint检查 ===${RESET}`);
  
  try {
    // 运行ESLint并生成JSON报告
    execSync('npx eslint . --ext .js,.ts --format json > ' + path.join(REPORTS_DIR, 'eslint-report.json'), {
      stdio: 'inherit'
    });
    
    // 读取ESLint报告
    const eslintReport = JSON.parse(fs.readFileSync(path.join(REPORTS_DIR, 'eslint-report.json'), 'utf8'));
    
    // 统计错误和警告数量
    let errorCount = 0;
    let warningCount = 0;
    
    eslintReport.forEach(file => {
      errorCount += file.errorCount;
      warningCount += file.warningCount;
    });
    
    console.log(`${BLUE}ESLint错误数: ${errorCount > 0 ? RED : GREEN}${errorCount}${RESET}`);
    console.log(`${BLUE}ESLint警告数: ${warningCount > THRESHOLDS.eslint.warnings ? YELLOW : GREEN}${warningCount}${RESET}`);
    
    // 检查是否超过阈值
    if (errorCount > THRESHOLDS.eslint.errors) {
      console.log(`${RED}ESLint错误数超过阈值(${THRESHOLDS.eslint.errors})!${RESET}`);
      return false;
    }
    
    if (warningCount > THRESHOLDS.eslint.warnings) {
      console.log(`${YELLOW}ESLint警告数超过阈值(${THRESHOLDS.eslint.warnings})!${RESET}`);
    }
    
    return true;
  } catch (error) {
    console.error(`${RED}运行ESLint检查时出错: ${error.message}${RESET}`);
    return false;
  }
}

/**
 * 运行代码复杂度检查
 */
function runComplexityCheck() {
  console.log(`${BLUE}=== 运行代码复杂度检查 ===${RESET}`);
  
  try {
    // 使用ESLint的complexity规则检查代码复杂度
    execSync('npx eslint . --ext .js,.ts --no-eslintrc --config ' + path.join(__dirname, 'complexity-check.json') + ' --format json > ' + path.join(REPORTS_DIR, 'complexity-report.json'), {
      stdio: 'inherit'
    });
    
    // 读取复杂度报告
    const complexityReport = JSON.parse(fs.readFileSync(path.join(REPORTS_DIR, 'complexity-report.json'), 'utf8'));
    
    // 统计超过阈值的函数数量
    let complexFunctions = 0;
    
    complexityReport.forEach(file => {
      file.messages.forEach(message => {
        if (message.ruleId === 'complexity') {
          complexFunctions++;
        }
      });
    });
    
    console.log(`${BLUE}复杂度超过阈值(${THRESHOLDS.complexity.max})的函数数量: ${complexFunctions > 0 ? YELLOW : GREEN}${complexFunctions}${RESET}`);
    
    return true;
  } catch (error) {
    console.error(`${RED}运行代码复杂度检查时出错: ${error.message}${RESET}`);
    return false;
  }
}

/**
 * 运行测试覆盖率检查
 */
function runCoverageCheck() {
  console.log(`${BLUE}=== 运行测试覆盖率检查 ===${RESET}`);
  
  try {
    // 运行测试并生成覆盖率报告
    execSync('npx jest --coverage --coverageReporters=json-summary', {
      stdio: 'inherit'
    });
    
    // 读取覆盖率报告
    const coverageSummary = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
    
    // 获取总体覆盖率
    const { lines, statements, functions, branches } = coverageSummary.total.pct;
    
    console.log(`${BLUE}行覆盖率: ${lines < THRESHOLDS.coverage.lines ? RED : GREEN}${lines}%${RESET}`);
    console.log(`${BLUE}语句覆盖率: ${statements < THRESHOLDS.coverage.statements ? RED : GREEN}${statements}%${RESET}`);
    console.log(`${BLUE}函数覆盖率: ${functions < THRESHOLDS.coverage.functions ? RED : GREEN}${functions}%${RESET}`);
    console.log(`${BLUE}分支覆盖率: ${branches < THRESHOLDS.coverage.branches ? RED : GREEN}${branches}%${RESET}`);
    
    // 检查是否超过阈值
    if (lines < THRESHOLDS.coverage.lines) {
      console.log(`${RED}行覆盖率低于阈值(${THRESHOLDS.coverage.lines}%)!${RESET}`);
      return false;
    }
    
    if (statements < THRESHOLDS.coverage.statements) {
      console.log(`${RED}语句覆盖率低于阈值(${THRESHOLDS.coverage.statements}%)!${RESET}`);
      return false;
    }
    
    if (functions < THRESHOLDS.coverage.functions) {
      console.log(`${RED}函数覆盖率低于阈值(${THRESHOLDS.coverage.functions}%)!${RESET}`);
      return false;
    }
    
    if (branches < THRESHOLDS.coverage.branches) {
      console.log(`${RED}分支覆盖率低于阈值(${THRESHOLDS.coverage.branches}%)!${RESET}`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`${RED}运行测试覆盖率检查时出错: ${error.message}${RESET}`);
    return false;
  }
}

/**
 * 运行代码重复检查
 */
function runDuplicationCheck() {
  console.log(`${BLUE}=== 运行代码重复检查 ===${RESET}`);
  
  try {
    // 使用jscpd检查代码重复
    execSync('npx jscpd . --ignore "node_modules/**,coverage/**,dist/**,test-reports/**,__tests__/**" --reporters json --output ' + REPORTS_DIR, {
      stdio: 'inherit'
    });
    
    // 读取重复报告
    const duplicationReport = JSON.parse(fs.readFileSync(path.join(REPORTS_DIR, 'jscpd-report.json'), 'utf8'));
    
    // 获取重复百分比
    const duplicationPercentage = duplicationReport.statistics.total.percentage;
    
    console.log(`${BLUE}代码重复率: ${duplicationPercentage > THRESHOLDS.duplications.percentage ? YELLOW : GREEN}${duplicationPercentage}%${RESET}`);
    
    // 检查是否超过阈值
    if (duplicationPercentage > THRESHOLDS.duplications.percentage) {
      console.log(`${YELLOW}代码重复率超过阈值(${THRESHOLDS.duplications.percentage}%)!${RESET}`);
    }
    
    return true;
  } catch (error) {
    console.error(`${RED}运行代码重复检查时出错: ${error.message}${RESET}`);
    return false;
  }
}

/**
 * 生成质量报告
 */
function generateQualityReport() {
  console.log(`${BLUE}=== 生成质量报告 ===${RESET}`);
  
  try {
    // 创建质量报告
    const qualityReport = {
      timestamp: new Date().toISOString(),
      eslint: {
        report: path.join(REPORTS_DIR, 'eslint-report.json')
      },
      complexity: {
        report: path.join(REPORTS_DIR, 'complexity-report.json')
      },
      coverage: {
        report: 'coverage/coverage-summary.json'
      },
      duplication: {
        report: path.join(REPORTS_DIR, 'jscpd-report.json')
      }
    };
    
    // 写入质量报告
    fs.writeFileSync(path.join(REPORTS_DIR, 'quality-report.json'), JSON.stringify(qualityReport, null, 2));
    
    console.log(`${GREEN}质量报告已生成: ${path.join(REPORTS_DIR, 'quality-report.json')}${RESET}`);
    
    return true;
  } catch (error) {
    console.error(`${RED}生成质量报告时出错: ${error.message}${RESET}`);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log(`${BLUE}开始代码质量检查...${RESET}`);
  
  let success = true;
  
  // 运行ESLint检查
  if (!runEslintCheck()) {
    success = false;
  }
  
  // 运行代码复杂度检查
  if (!runComplexityCheck()) {
    success = false;
  }
  
  // 运行测试覆盖率检查
  if (!runCoverageCheck()) {
    success = false;
  }
  
  // 运行代码重复检查
  if (!runDuplicationCheck()) {
    success = false;
  }
  
  // 生成质量报告
  if (!generateQualityReport()) {
    success = false;
  }
  
  if (success) {
    console.log(`${GREEN}代码质量检查通过!${RESET}`);
    process.exit(0);
  } else {
    console.log(`${RED}代码质量检查失败!${RESET}`);
    process.exit(1);
  }
}

// 运行主函数
main().catch(error => {
  console.error(`${RED}运行代码质量检查时出错: ${error.message}${RESET}`);
  process.exit(1);
});
