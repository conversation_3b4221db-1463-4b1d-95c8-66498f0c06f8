/**
 * 完全清理V1 API脚本
 * 用于彻底清理系统中残留的V1 API相关文件和注释
 * 
 * 使用方法：
 * 1. 确保已经完成V1 API的移除工作
 * 2. 运行此脚本，完全清理V1版本的文件和注释
 * 
 * 示例：
 * node backend/scripts/complete-v1-cleanup.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
  // 需要检查的目录
  directories: {
    routes: path.resolve(__dirname, '../routes'),
    controllers: path.resolve(__dirname, '../controllers'),
    services: path.resolve(__dirname, '../services'),
    middlewares: path.resolve(__dirname, '../middlewares'),
  },
  // 需要保留的文件（这些文件虽然没有V2后缀，但仍然需要保留）
  keepFiles: [
    'health.routes.js',
    'health.controller.js',
    'errorMonitor.routes.js',
    'errorMonitor.controller.js',
    'mockData.routes.js',
    'mockData.controller.js',
    'deadLetterQueue.routes.js',
    'deadLetterQueue.controller.js',
    'cleanup.routes.js',
    'cleanup.controller.js',
    'batchOperation.routes.js',
    'batchOperation.controller.js',
    'statisticsMonitor.routes.js',
    'statisticsMonitor.controller.js',
    'ai.routes.js',
    'ai.controller.js',
    'testData.routes.js',
  ],
  // 需要删除的特定文件
  specificFilesToRemove: [
    path.resolve(__dirname, '../routes/exercise.routes.js'),
    path.resolve(__dirname, '../routes/tag.routes.js'),
    path.resolve(__dirname, '../controllers/exercise.controller.js'),
    path.resolve(__dirname, '../controllers/tag.controller.js'),
  ],
  // 需要修改的文件
  filesToModify: {
    versionRoutes: path.resolve(__dirname, '../config/version-routes.js'),
  },
  // Git相关配置
  git: {
    commitMessage: 'chore: 完全清理V1 API文件和注释',
    branchName: 'complete-v1-cleanup',
  },
  // 备份目录
  backupDir: path.resolve(__dirname, '../backup/v1-files'),
};

/**
 * 检查文件是否需要保留
 * @param {string} filePath - 文件路径
 * @returns {boolean} - 是否需要保留
 */
function shouldKeepFile(filePath) {
  const fileName = path.basename(filePath);
  
  // 如果文件名包含V2或v2，则保留
  if (fileName.includes('V2') || fileName.includes('v2')) {
    return true;
  }
  
  // 如果文件在保留列表中，则保留
  if (config.keepFiles.includes(fileName)) {
    return true;
  }
  
  return false;
}

/**
 * 备份文件
 * @param {string} filePath - 文件路径
 */
function backupFile(filePath) {
  const relativePath = path.relative(path.resolve(__dirname, '..'), filePath);
  const backupPath = path.join(config.backupDir, relativePath);
  
  // 创建备份目录
  const backupDir = path.dirname(backupPath);
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  // 复制文件到备份目录
  fs.copyFileSync(filePath, backupPath);
  
  console.log(`已备份文件: ${filePath} -> ${backupPath}`);
}

/**
 * 清理目录中的V1文件
 * @param {string} directory - 目录路径
 * @param {string} filePattern - 文件模式
 */
function cleanupDirectory(directory, filePattern) {
  console.log(`正在清理目录: ${directory}`);
  
  // 获取目录中的所有文件
  const files = fs.readdirSync(directory, { withFileTypes: true });
  
  // 遍历文件
  for (const file of files) {
    const filePath = path.join(directory, file.name);
    
    if (file.isDirectory()) {
      // 如果是目录，则递归清理
      cleanupDirectory(filePath, filePattern);
    } else if (file.isFile() && file.name.endsWith(filePattern)) {
      // 如果是文件，且匹配文件模式，则检查是否需要保留
      if (!shouldKeepFile(filePath)) {
        // 备份文件
        backupFile(filePath);
        
        // 删除文件
        fs.unlinkSync(filePath);
        console.log(`已删除文件: ${filePath}`);
      }
    }
  }
}

/**
 * 清理特定文件
 */
function cleanupSpecificFiles() {
  console.log('正在清理特定文件...');
  
  config.specificFilesToRemove.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      // 备份文件
      backupFile(filePath);
      
      // 删除文件
      fs.unlinkSync(filePath);
      console.log(`已删除特定文件: ${filePath}`);
    } else {
      console.log(`特定文件不存在: ${filePath}`);
    }
  });
}

/**
 * 清理文件中的V1 API注释
 * @param {string} directory - 目录路径
 * @param {string} filePattern - 文件模式
 */
function cleanupV1ApiComments(directory, filePattern) {
  console.log(`正在清理目录中的V1 API注释: ${directory}`);
  
  // 获取目录中的所有文件
  const files = fs.readdirSync(directory, { withFileTypes: true });
  
  // 遍历文件
  for (const file of files) {
    const filePath = path.join(directory, file.name);
    
    if (file.isDirectory()) {
      // 如果是目录，则递归清理
      cleanupV1ApiComments(filePath, filePattern);
    } else if (file.isFile() && file.name.endsWith(filePattern)) {
      // 如果是文件，且匹配文件模式，则清理V1 API注释
      cleanupV1ApiCommentsInFile(filePath);
    }
  }
}

/**
 * 清理文件中的V1 API注释
 * @param {string} filePath - 文件路径
 */
function cleanupV1ApiCommentsInFile(filePath) {
  console.log(`正在清理文件中的V1 API注释: ${filePath}`);
  
  // 读取文件内容
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 使用正则表达式匹配并移除V1 API注释
  const originalContent = content;
  content = content.replace(/\/\*\*\s*\n\s*\*\s*@route\s+[A-Z]+\s+\/api\/v1\/[^\n]*\s*\n\s*\*\s*@desc[^\n]*\s*\n\s*\*\s*@access[^\n]*\s*\n\s*\*\/\s*\n/g, '');
  
  // 如果内容有变化，则写入文件
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`已清理V1 API注释: ${filePath}`);
  }
}

/**
 * 提交更改到Git
 */
function commitChanges() {
  console.log('正在提交更改到Git...');
  
  try {
    // 创建新分支
    execSync(`git checkout -b ${config.git.branchName}`);
    console.log(`已创建分支: ${config.git.branchName}`);
    
    // 添加更改
    execSync('git add .');
    console.log('已添加更改');
    
    // 提交更改
    execSync(`git commit -m "${config.git.commitMessage}"`);
    console.log(`已提交更改: ${config.git.commitMessage}`);
    
    console.log('Git操作完成');
  } catch (error) {
    console.error('Git操作失败:', error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('开始执行V1 API完全清理...');
  
  try {
    // 创建备份目录
    if (!fs.existsSync(config.backupDir)) {
      fs.mkdirSync(config.backupDir, { recursive: true });
    }
    
    // 清理特定文件
    cleanupSpecificFiles();
    
    // 清理路由文件
    cleanupDirectory(config.directories.routes, '.routes.js');
    
    // 清理控制器文件
    cleanupDirectory(config.directories.controllers, '.controller.js');
    
    // 清理服务文件
    cleanupDirectory(config.directories.services, '.service.js');
    
    // 清理中间件文件
    cleanupDirectory(config.directories.middlewares, '.middleware.js');
    
    // 清理V1 API注释
    cleanupV1ApiComments(config.directories.routes, '.routes.js');
    cleanupV1ApiComments(config.directories.controllers, '.controller.js');
    
    // 提交更改到Git
    commitChanges();
    
    console.log('V1 API完全清理完成');
    console.log(`备份文件已保存到: ${config.backupDir}`);
  } catch (error) {
    console.error('执行失败:', error.message);
    process.exit(1);
  }
}

// 执行主函数
main();
