/**
 * 为所有已注册但没有学习计划的用户复制系统默认学习计划
 */

const { User, LearningPlan, Tag, PlanTag, DailyContent, sequelize } = require('../models');
const { copySystemDefaultPlanToUser } = require('../services/learningPlan.service');
const logger = require('../config/logger');

async function copyDefaultPlanToExistingUsers() {
  console.log('开始为现有用户复制默认学习计划...');
  logger.info('开始为现有用户复制默认学习计划');

  // 用于记录处理结果
  const results = {
    totalUsers: 0,
    usersWithoutPlans: 0,
    successCount: 0,
    failCount: 0,
    failedUsers: []
  };

  try {
    // 获取所有用户
    const users = await User.findAll();
    results.totalUsers = users.length;
    console.log(`找到 ${users.length} 个用户`);
    logger.info(`找到 ${users.length} 个用户`);

    // 遍历所有用户，检查是否有学习计划
    for (const user of users) {
      const userId = user.id;
      
      try {
        // 检查用户是否已有学习计划
        const existingPlans = await LearningPlan.findAll({
          where: { user_id: userId }
        });

        if (existingPlans.length === 0) {
          // 用户没有学习计划，复制默认计划
          results.usersWithoutPlans++;
          console.log(`用户 ${userId} 没有学习计划，正在复制默认计划...`);
          logger.info(`用户 ${userId} 没有学习计划，正在复制默认计划`);

          try {
            // 复制系统默认计划给用户
            await copySystemDefaultPlanToUser(userId);
            console.log(`成功为用户 ${userId} 复制默认学习计划`);
            logger.info(`成功为用户 ${userId} 复制默认学习计划`);
            results.successCount++;
          } catch (error) {
            console.error(`为用户 ${userId} 复制默认计划失败: ${error.message}`);
            logger.error(`为用户 ${userId} 复制默认计划失败: ${error.message}`);
            results.failCount++;
            results.failedUsers.push(userId);
          }
        } else {
          console.log(`用户 ${userId} 已有 ${existingPlans.length} 个学习计划，跳过`);
          logger.info(`用户 ${userId} 已有 ${existingPlans.length} 个学习计划，跳过`);
        }
      } catch (error) {
        console.error(`处理用户 ${userId} 时出错: ${error.message}`);
        logger.error(`处理用户 ${userId} 时出错: ${error.message}`);
        results.failCount++;
        results.failedUsers.push(userId);
      }
    }

    // 打印处理结果
    console.log('\n处理结果汇总:');
    console.log(`总用户数: ${results.totalUsers}`);
    console.log(`没有学习计划的用户数: ${results.usersWithoutPlans}`);
    console.log(`成功复制默认计划数: ${results.successCount}`);
    console.log(`失败数: ${results.failCount}`);
    
    if (results.failCount > 0) {
      console.log('失败的用户:', results.failedUsers);
    }

    logger.info(`处理完成。总用户: ${results.totalUsers}, 无计划用户: ${results.usersWithoutPlans}, 成功: ${results.successCount}, 失败: ${results.failCount}`);
    
    console.log('\n处理完成!');
  } catch (error) {
    console.error(`批量处理失败: ${error.message}`);
    logger.error(`批量处理失败: ${error.message}`);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行主函数
copyDefaultPlanToExistingUsers().catch(error => {
  console.error('脚本执行失败:', error);
  process.exit(1);
}); 