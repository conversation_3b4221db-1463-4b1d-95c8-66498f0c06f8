/**
 * 为指定用户复制系统默认学习计划
 * 用法: node copy-default-plan-to-user.js <用户ID>
 */

const { User, LearningPlan, sequelize } = require('../models');
const { copySystemDefaultPlanToUser } = require('../services/learningPlan.service');
const logger = require('../config/logger');

async function copyDefaultPlanToUser(userId) {
  console.log(`开始为用户 ${userId} 复制默认学习计划...`);
  logger.info(`开始为用户 ${userId} 复制默认学习计划`);

  try {
    // 验证用户是否存在
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error(`用户 ${userId} 不存在`);
    }

    // 检查用户是否已有学习计划
    const existingPlans = await LearningPlan.findAll({
      where: { user_id: userId }
    });

    if (existingPlans.length > 0) {
      console.log(`用户 ${userId} 已有 ${existingPlans.length} 个学习计划`);
      
      // 询问是否继续
      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
      });

      const answer = await new Promise(resolve => {
        readline.question('是否仍要创建新计划? (y/n): ', resolve);
      });
      readline.close();

      if (answer.toLowerCase() !== 'y') {
        console.log('操作已取消');
        return;
      }
    }

    // 复制默认计划
    const plan = await copySystemDefaultPlanToUser(userId);
    console.log(`成功为用户 ${userId} 复制默认学习计划，计划ID: ${plan.id}`);
    logger.info(`成功为用户 ${userId} 复制默认学习计划，计划ID: ${plan.id}`);
  } catch (error) {
    console.error(`为用户 ${userId} 复制默认计划失败: ${error.message}`);
    logger.error(`为用户 ${userId} 复制默认计划失败: ${error.message}`);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 从命令行参数获取用户ID
const userId = process.argv[2];

if (!userId) {
  console.error('错误: 请提供用户ID作为参数');
  console.log('用法: node copy-default-plan-to-user.js <用户ID>');
  process.exit(1);
}

// 执行主函数
copyDefaultPlanToUser(userId).catch(error => {
  console.error('脚本执行失败:', error);
  process.exit(1);
}); 