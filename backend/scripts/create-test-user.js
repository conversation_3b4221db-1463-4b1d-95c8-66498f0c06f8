// scripts/create-test-user.js
// 创建测试用户脚本

// 加载环境变量
require('dotenv').config();

// 导入模型和工具
const { User } = require('../models');
const { hashPassword } = require('../utils/password');
const logger = require('../config/logger');

// 测试用户信息
const TEST_USER = {
  phone: '13800138000',
  password: '123456',
  nickname: '测试用户'
};

/**
 * 创建测试用户
 */
async function createTestUser() {
  try {
    // 检查用户是否已存在
    const existingUser = await User.findOne({ where: { phone: TEST_USER.phone } });

    if (existingUser) {
      logger.info(`测试用户已存在: ${existingUser.id}`);
      console.log(`测试用户已存在: ${existingUser.id}`);
      console.log(`手机号: ${TEST_USER.phone}`);
      console.log(`密码: ${TEST_USER.password}`);
      return;
    }

    // 对密码进行哈希处理
    const hashedPassword = await hashPassword(TEST_USER.password);

    // 创建用户ID（使用phone_前缀加手机号）
    const userId = `phone_${TEST_USER.phone}`;

    // 创建新用户
    const user = await User.create({
      id: userId,
      phone: TEST_USER.phone,
      password: hashedPassword,
      nickname: TEST_USER.nickname,
      login_type: 'phone',
      last_login_at: new Date()
    });

    logger.info(`测试用户创建成功: ${user.id}`);
    console.log(`测试用户创建成功: ${user.id}`);
    console.log(`手机号: ${TEST_USER.phone}`);
    console.log(`密码: ${TEST_USER.password}`);
  } catch (error) {
    logger.error(`创建测试用户失败: ${error.message}`);
    console.error(`创建测试用户失败: ${error.message}`);
    console.error(error);
  } finally {
    // 关闭数据库连接
    process.exit(0);
  }
}

// 执行创建测试用户
createTestUser();
