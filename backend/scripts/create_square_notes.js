/**
 * 为广场默认标签创建笔记
 * 为系统默认学习计划的5个标签各创建5条笔记
 */
const { Sequelize } = require('sequelize');
const config = require('../config/config');
const logger = require('../config/logger');

// Docker环境数据库连接信息
const sequelize = new Sequelize(
  'aibubb_db',
  'aibubb_user',
  'aibubb_password',
  {
    host: 'mysql',
    dialect: 'mysql',
    logging: console.log
  }
);

// 直接定义模型，避免循环依赖问题
const Note = sequelize.define('Note', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  tag_id: {
    type: Sequelize.INTEGER,
    allowNull: false
  },
  user_id: {
    type: Sequelize.STRING(32),
    allowNull: true
  },
  title: {
    type: Sequelize.STRING(100),
    allowNull: false
  },
  content: {
    type: Sequelize.TEXT,
    allowNull: false
  },
  image_url: {
    type: Sequelize.STRING(255),
    allowNull: true
  },
  likes: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  },
  comments: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  },
  is_ai_generated: {
    type: Sequelize.BOOLEAN,
    defaultValue: false
  },
  status: {
    type: Sequelize.ENUM('draft', 'published', 'hidden'),
    defaultValue: 'published'
  }
}, {
  tableName: 'Note',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 笔记内容
// 1. 平台介绍标签的笔记
const platformIntroNotes = [
  {
    title: 'AIBUBB平台的核心理念',
    content: 'AIBUBB平台基于"碎片化学习与系统化提升相结合"的理念，通过泡泡组件提供短时间学习内容，同时通过学习计划实现长期系统化学习。我们相信，真正的能力提升来自于实践和反思的循环，因此平台设计了练习和笔记功能，帮助用户将知识内化为能力。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: 'AIBUBB如何提升沟通能力',
    content: 'AIBUBB专注于沟通能力的全方位提升，包括倾听、表达、同理心等多个维度。平台通过个性化内容推荐、互动练习和社区分享，创造了一个完整的学习生态系统。无论是职场沟通还是日常交流，AIBUBB都能帮助你找到适合的学习内容和方法。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: 'AIBUBB的用户界面设计',
    content: 'AIBUBB采用了直观友好的界面设计，主要分为首页、广场、学习和个人中心四大板块。首页的泡泡设计灵感来源于思维泡泡，代表着知识的流动和连接；广场则采用瀑布流设计，方便用户浏览和发现内容；整体色调温暖明亮，创造轻松愉悦的学习氛围。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: 'AIBUBB的技术创新',
    content: 'AIBUBB平台融合了多项技术创新，包括基于用户行为的智能推荐算法、自适应学习路径和实时互动反馈系统。我们的AI助手能够分析用户的学习模式和偏好，提供个性化的学习建议和内容推荐，让每位用户都能获得量身定制的学习体验。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: 'AIBUBB的未来发展规划',
    content: 'AIBUBB计划在未来引入更多创新功能，包括虚拟角色扮演练习、实时语音分析反馈、多人协作学习空间等。我们将不断完善内容生态，扩展更多沟通场景的专业指导，并建立更加活跃的用户社区，让学习沟通变得更加有趣和高效。',
    status: 'published',
    is_ai_generated: true
  }
];

// 2. 泡泡功能标签的笔记
const bubbleFunctionNotes = [
  {
    title: '泡泡组件的交互设计',
    content: 'AIBUBB首页的泡泡组件采用了流体动画效果，每个泡泡代表一个学习内容。轻触泡泡可以查看详情，长按可以拖动，松手后泡泡会以自然的物理效果弹回。这种设计不仅美观，还增强了用户与内容的情感连接，使学习过程更加生动有趣。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '泡泡内容的分类与推荐',
    content: '泡泡内容根据类型分为观点、笔记和练习三大类，分别用不同的视觉样式区分。系统会根据用户的学习计划、历史行为和兴趣偏好，智能推荐最适合的内容。你也可以通过筛选功能，查看特定类型或主题的泡泡内容。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '如何创建自己的泡泡内容',
    content: '点击首页右下角的"+"按钮，可以创建自己的泡泡内容。你可以选择创建观点、笔记或练习，添加文字、图片，选择关联的标签，然后发布到自己的空间或分享到广场。创建的内容会保存在你的个人中心，方便日后查看和编辑。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '泡泡内容的保存与收藏',
    content: '当你浏览到喜欢的泡泡内容时，可以点击右上角的收藏按钮将其保存。所有收藏的内容会集中显示在个人中心的"收藏"页面，方便日后查看。你还可以对收藏的内容进行分类整理，创建自己的知识库。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '泡泡的高级交互技巧',
    content: '除了基本的点击和拖动，泡泡还支持多种高级交互：双指缩放可以调整泡泡大小；快速左右滑动可以切换到下一个或上一个泡泡；向上滑动可以查看相关推荐内容。这些交互设计让内容浏览更加流畅自然，提升了用户体验。',
    status: 'published',
    is_ai_generated: true
  }
];

// 3. 广场探索标签的笔记
const squareExploreNotes = [
  {
    title: '广场的内容发现机制',
    content: '广场采用智能推荐和标签分类相结合的内容发现机制。"推荐"标签下展示的是根据你的兴趣和行为智能推荐的内容；其他标签则按主题分类展示内容。内容排序综合考虑了时间、热度和相关性，确保你能看到最新最热的优质内容。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '如何在广场分享内容',
    content: '想要在广场分享内容，首先需要创建一篇笔记或观点，然后在发布时选择"分享到广场"选项。优质的分享内容有机会被推荐到更多用户的广场页面，获得更多曝光和互动。记得为你的内容选择合适的标签，这样能让对应主题感兴趣的用户更容易发现。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '广场互动礼仪指南',
    content: '在广场互动时，请遵循基本的社区礼仪：尊重原创，不发表攻击性言论，提供有建设性的评论和反馈。点赞是对创作者的肯定，评论则是深度交流的开始。积极参与讨论，但避免无意义的刷屏，共同维护一个友善、高质量的学习社区。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '广场内容的筛选与个性化',
    content: '广场页面右上角的筛选按钮可以帮助你更精确地找到感兴趣的内容。你可以按时间、热度排序，或者只查看特定类型的内容。系统还会根据你的浏览和互动历史，逐渐调整推荐内容，使广场页面越来越符合你的个人偏好。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '从广场发现学习伙伴',
    content: '广场不仅是内容分享的平台，也是结交志同道合学习伙伴的地方。当你发现某位用户的分享特别有价值时，可以点击他的头像查看主页，关注他的更新，或者通过私信开始交流。与学习伙伴一起讨论和实践，能大大提升学习效果和动力。',
    status: 'published',
    is_ai_generated: true
  }
];

// 4. 学习计划标签的笔记
const learningPlanNotes = [
  {
    title: '如何创建有效的学习计划',
    content: '创建学习计划时，建议遵循SMART原则：具体(Specific)、可衡量(Measurable)、可实现(Achievable)、相关性(Relevant)和时限性(Time-bound)。例如，不要笼统地计划"提高表达能力"，而是设定"一周内完成3次结构化表达练习，并在小组讨论中应用"这样具体可执行的目标。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '学习计划的进度追踪',
    content: 'AIBUBB提供了直观的进度追踪功能，包括完成率统计、每日打卡记录和学习时长分析。你可以在"学习"页面查看这些数据，了解自己的学习状态和趋势。系统还会根据你的进度情况，适时发送提醒和鼓励，帮助你保持学习动力。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '学习计划的调整与优化',
    content: '学习过程中，定期评估和调整计划是很重要的。如果发现某个目标过于困难或简单，可以在"学习计划"页面进行修改。AIBUBB还提供了"计划分析"功能，会根据你的学习数据，提出优化建议，如调整学习频率、增加特定类型的练习等，帮助你制定更适合自己的计划。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '多个学习计划的管理策略',
    content: '如果你同时有多个学习目标，可以创建多个学习计划。建议将相关的目标合并到一个计划中，避免计划过于分散。你可以为不同计划设置优先级，集中精力在最重要的计划上。系统支持计划切换，但建议不要频繁更换当前计划，以保持学习的连贯性。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '学习计划的社交分享功能',
    content: 'AIBUBB允许你将学习计划分享给朋友或发布到广场。分享计划可以增加社交压力，提高完成率；也可以寻找有相似目标的伙伴，一起学习。在"学习计划"页面，点击"分享"按钮，选择分享范围和内容，就可以将你的计划和进度展示给他人，获取支持和反馈。',
    status: 'published',
    is_ai_generated: true
  }
];

// 5. 笔记技巧标签的笔记
const noteTipsNotes = [
  {
    title: '结构化笔记法提升理解',
    content: '结构化笔记法可以显著提升对内容的理解和记忆。尝试使用大纲法、康奈尔笔记法或思维导图来组织信息。在AIBUBB中创建笔记时，可以使用标题、列表和分段来构建清晰的结构，帮助自己梳理思路，也方便日后查阅和复习。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '笔记中融入个人思考',
    content: '高质量的笔记不仅是对原始内容的记录，更包含个人的思考和联想。在记录关键信息的同时，尝试添加自己的例子、疑问或见解。这种"对话式"的笔记方法能促进深度思考，将新知识与已有经验连接起来，形成更牢固的记忆和理解。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '利用标签系统整理笔记',
    content: 'AIBUBB的标签系统是整理笔记的强大工具。为每篇笔记添加相关标签，可以从不同维度对知识进行分类。例如，一篇关于演讲技巧的笔记，可以同时添加"表达"、"公开演讲"、"肢体语言"等标签。这样在需要时，可以快速找到所有相关笔记，建立知识间的联系。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '定期复习与笔记更新',
    content: '根据艾宾浩斯遗忘曲线，定期复习是巩固记忆的关键。AIBUBB提供了笔记复习提醒功能，会在最佳时间点提示你复习特定笔记。复习时，不要只是重读，而是尝试回忆、总结或应用笔记内容。同时，随着理解的深入，及时更新和完善笔记，让它成为动态成长的知识库。',
    status: 'published',
    is_ai_generated: true
  },
  {
    title: '多媒体笔记的创作技巧',
    content: 'AIBUBB支持创建包含文字、图片的多媒体笔记。对于复杂概念，尝试使用图表或示意图进行可视化；对于实践技巧，可以添加操作步骤的截图。多媒体元素不仅能增强笔记的表现力，还能激活大脑的多个区域，促进多通道学习，提高记忆效果。',
    status: 'published',
    is_ai_generated: true
  }
];

// 标签名称到笔记数组的映射
const tagNotesMap = {
  '平台介绍': platformIntroNotes,
  '泡泡功能': bubbleFunctionNotes,
  '广场探索': squareExploreNotes,
  '学习计划': learningPlanNotes,
  '笔记技巧': noteTipsNotes
};

/**
 * 主函数：创建广场笔记
 */
async function createSquareNotes() {
  // 开启事务
  const transaction = await sequelize.transaction();

  try {
    // 1. 查找系统默认学习计划
    logger.info('查找系统默认学习计划...');

    const [defaultPlans] = await sequelize.query(
      "SELECT id FROM LearningPlan WHERE is_system_default = true LIMIT 1",
      { transaction }
    );

    if (defaultPlans.length === 0) {
      logger.error('系统默认学习计划不存在，请先运行 create_system_default_plan.js');
      await transaction.rollback();
      return;
    }

    const defaultPlanId = defaultPlans[0].id;
    logger.info(`找到系统默认学习计划，ID: ${defaultPlanId}`);

    // 2. 查找所有标签
    logger.info('查找系统默认标签...');

    const [tags] = await sequelize.query(
      "SELECT id, name FROM Tag WHERE plan_id = ? ORDER BY sort_order ASC",
      {
        replacements: [defaultPlanId],
        transaction
      }
    );

    if (tags.length === 0) {
      logger.error('系统默认标签不存在，请先运行 create_system_default_plan.js');
      await transaction.rollback();
      return;
    }

    logger.info(`找到 ${tags.length} 个系统默认标签`);

    // 3. 为每个标签创建笔记
    let totalCreated = 0;

    for (const tag of tags) {
      const tagName = tag.name;
      const tagId = tag.id;

      // 检查是否有该标签的笔记内容
      if (tagNotesMap[tagName]) {
        const notes = tagNotesMap[tagName];
        logger.info(`开始为标签 "${tagName}" (ID: ${tagId}) 创建 ${notes.length} 条笔记...`);

        // 检查该标签是否已有笔记
        const [existingNotes] = await sequelize.query(
          "SELECT COUNT(*) as count FROM Note WHERE tag_id = ?",
          {
            replacements: [tagId],
            transaction
          }
        );

        if (existingNotes[0].count > 0) {
          logger.info(`标签 "${tagName}" 已有 ${existingNotes[0].count} 条笔记，跳过创建`);
          continue;
        }

        // 使用原始SQL直接创建笔记，明确指定created_at和updated_at字段的值
        const now = new Date().toISOString().slice(0, 19).replace('T', ' ');

        for (const noteData of notes) {
          try {
            await sequelize.query(
              `INSERT INTO Note (tag_id, user_id, title, content, image_url, likes, comments, is_ai_generated, status, created_at, updated_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              {
                replacements: [
                  tagId,
                  null, // user_id (AI生成)
                  noteData.title,
                  noteData.content,
                  null, // image_url
                  0, // likes
                  0, // comments
                  noteData.is_ai_generated ? 1 : 0, // MySQL中的布尔值
                  noteData.status,
                  now, // created_at
                  now  // updated_at
                ],
                transaction
              }
            );
            totalCreated++;
            logger.info(`成功创建笔记: ${noteData.title}`);
          } catch (error) {
            logger.error(`创建笔记失败: ${noteData.title}`, error);
          }
        }
      } else {
        logger.warn(`标签 "${tagName}" 没有预定义的笔记内容，跳过`);
      }
    }

    // 提交事务
    await transaction.commit();
    logger.info(`广场笔记创建完成，共创建 ${totalCreated} 条笔记`);

  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    logger.error(`创建广场笔记失败: ${error.message}`);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 如果直接运行此脚本，执行创建函数
if (require.main === module) {
  createSquareNotes()
    .then(() => {
      console.log('广场笔记创建成功完成');
      process.exit(0);
    })
    .catch(error => {
      console.error(`广场笔记创建失败: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { createSquareNotes };
