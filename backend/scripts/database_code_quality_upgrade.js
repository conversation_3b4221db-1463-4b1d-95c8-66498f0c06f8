/**
 * 数据库和代码质量升级脚本
 * 执行所有数据库修复和代码质量提升任务
 */
const { createDailyContentTable } = require('./create_daily_content_table');
const logger = require('../config/logger');
const fs = require('fs');
const path = require('path');

/**
 * 检查并创建errorHandler.js文件
 */
async function checkErrorHandler() {
  try {
    logger.info('检查errorHandler.js文件...');
    
    const errorHandlerPath = path.join(__dirname, '../utils/errorHandler.js');
    
    if (fs.existsSync(errorHandlerPath)) {
      logger.info('errorHandler.js文件已存在');
      return true;
    } else {
      logger.error('errorHandler.js文件不存在，请手动创建');
      return false;
    }
  } catch (error) {
    logger.error(`检查errorHandler.js文件失败: ${error.message}`);
    return false;
  }
}

/**
 * 检查DailyContent模型是否已更新
 */
async function checkDailyContentModel() {
  try {
    logger.info('检查DailyContent模型...');
    
    const modelPath = path.join(__dirname, '../models/dailyContent.model.js');
    
    if (fs.existsSync(modelPath)) {
      const content = fs.readFileSync(modelPath, 'utf8');
      
      if (content.includes("tableName: 'DailyContent'")) {
        logger.info('DailyContent模型已更新');
        return true;
      } else {
        logger.error('DailyContent模型未更新，请手动修改tableName为DailyContent');
        return false;
      }
    } else {
      logger.error('DailyContent模型文件不存在');
      return false;
    }
  } catch (error) {
    logger.error(`检查DailyContent模型失败: ${error.message}`);
    return false;
  }
}

/**
 * 主函数
 */
async function upgradeDatabase() {
  try {
    logger.info('开始数据库和代码质量升级...');
    
    // 1. 创建DailyContent表
    logger.info('步骤1: 创建DailyContent表');
    const dailyContentCreated = await createDailyContentTable();
    
    if (!dailyContentCreated) {
      logger.error('创建DailyContent表失败');
      return false;
    }
    
    // 2. 检查DailyContent模型是否已更新
    logger.info('步骤2: 检查DailyContent模型');
    const dailyContentModelUpdated = await checkDailyContentModel();
    
    if (!dailyContentModelUpdated) {
      logger.error('DailyContent模型未更新');
      return false;
    }
    
    // 3. 检查errorHandler.js文件
    logger.info('步骤3: 检查errorHandler.js文件');
    const errorHandlerExists = await checkErrorHandler();
    
    if (!errorHandlerExists) {
      logger.error('errorHandler.js文件不存在');
      return false;
    }
    
    logger.info('数据库和代码质量升级完成');
    return true;
  } catch (error) {
    logger.error(`数据库和代码质量升级失败: ${error.message}`);
    logger.error(error.stack);
    return false;
  }
}

// 如果直接运行此脚本，执行升级
if (require.main === module) {
  upgradeDatabase()
    .then(success => {
      if (success) {
        console.log('数据库和代码质量升级成功');
        process.exit(0);
      } else {
        console.error('数据库和代码质量升级失败');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(`执行脚本时出错: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { upgradeDatabase };
