/**
 * 性能基准建立脚本
 * 用于建立系统性能基准，并保存基准数据
 * 
 * 运行方法:
 * node backend/scripts/establish-performance-baseline.js [--output=backend/config/performance-baseline.json]
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { program } = require('commander');

// 定义命令行选项
program
  .option('--output <file>', '输出文件', 'backend/config/performance-baseline.json')
  .option('--quick', '快速模式（缩短测试时间）', false)
  .option('--force', '强制重新建立基准', false)
  .parse(process.argv);

const options = program.opts();

// 测试类型
const TEST_TYPES = ['load', 'stress', 'concurrency'];

// 获取当前时间戳
const timestamp = new Date().toISOString();

/**
 * 运行性能测试
 * @param {string} testType - 测试类型
 * @returns {Promise<Object>} 测试结果
 */
async function runPerformanceTest(testType) {
  console.log(`\n运行${testType}测试以建立基准...`);
  
  // 构建命令行参数
  const args = [
    'backend/scripts/run-performance-tests.js',
    `--test=${testType}`,
    `--output-dir=backend/test-results/baseline`
  ];
  
  if (options.quick) {
    args.push('--quick');
  }
  
  // 启动测试进程
  const testProcess = spawn('node', args);
  
  // 处理输出
  testProcess.stdout.on('data', (data) => {
    process.stdout.write(data);
  });
  
  testProcess.stderr.on('data', (data) => {
    process.stderr.write(data);
  });
  
  // 等待进程完成
  return new Promise((resolve, reject) => {
    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log(`${testType}测试完成`);
        
        // 读取测试结果
        const resultDir = path.resolve('backend/test-results/baseline');
        const files = fs.readdirSync(resultDir);
        
        // 查找最新的summary文件
        const summaryFile = files
          .filter(file => file.startsWith(`${testType}-test`) && file.endsWith('-summary.json'))
          .sort()
          .pop();
        
        if (summaryFile) {
          const summaryPath = path.join(resultDir, summaryFile);
          const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
          resolve(summary);
        } else {
          reject(new Error(`未找到${testType}测试的summary文件`));
        }
      } else {
        reject(new Error(`${testType}测试失败，退出码: ${code}`));
      }
    });
  });
}

/**
 * 提取关键指标
 * @param {Object} summary - 测试摘要
 * @returns {Object} 关键指标
 */
function extractMetrics(summary) {
  return {
    vus: summary.metrics?.vus?.max || 0,
    iterations: summary.metrics?.iterations?.count || 0,
    requestsPerSecond: summary.metrics?.http_reqs?.rate || 0,
    failRate: summary.metrics?.http_req_failed?.rate || 0,
    avgResponseTime: summary.metrics?.http_req_duration?.avg || 0,
    p95ResponseTime: summary.metrics?.http_req_duration?.p(95) || 0,
    p99ResponseTime: summary.metrics?.http_req_duration?.p(99) || 0,
    errorRate: summary.metrics?.error_rate?.rate || 0,
    successfulRequests: summary.metrics?.successful_requests?.count || 0,
    failedRequests: summary.metrics?.failed_requests?.count || 0
  };
}

/**
 * 建立性能基准
 * @returns {Promise<Object>} 基准数据
 */
async function establishBaseline() {
  console.log('开始建立性能基准...');
  
  const baseline = {
    timestamp,
    tests: {}
  };
  
  // 运行每种测试类型
  for (const testType of TEST_TYPES) {
    try {
      const summary = await runPerformanceTest(testType);
      baseline.tests[testType] = extractMetrics(summary);
      console.log(`已建立${testType}测试基准`);
    } catch (error) {
      console.error(`建立${testType}测试基准失败:`, error);
      baseline.tests[testType] = { error: error.message };
    }
  }
  
  return baseline;
}

/**
 * 保存基准数据
 * @param {Object} baseline - 基准数据
 */
function saveBaseline(baseline) {
  const outputPath = path.resolve(options.output);
  
  // 确保目录存在
  const outputDir = path.dirname(outputPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // 写入文件
  fs.writeFileSync(outputPath, JSON.stringify(baseline, null, 2));
  console.log(`基准数据已保存到: ${outputPath}`);
}

/**
 * 主函数
 */
async function main() {
  const outputPath = path.resolve(options.output);
  
  // 检查是否已存在基准数据
  if (fs.existsSync(outputPath) && !options.force) {
    console.log(`基准数据已存在: ${outputPath}`);
    console.log('使用--force选项强制重新建立基准');
    process.exit(0);
  }
  
  try {
    // 建立基准
    const baseline = await establishBaseline();
    
    // 保存基准
    saveBaseline(baseline);
    
    console.log('性能基准建立完成');
  } catch (error) {
    console.error('建立性能基准失败:', error);
    process.exit(1);
  }
}

// 执行主函数
main();
