/**
 * AI模型评估脚本
 * 
 * 此脚本用于评估和比较不同模型的表现
 * 
 * 使用方法:
 * node backend/scripts/evaluate-ai-models.js [--dir=<results-directory>]
 * 
 * 参数:
 * --dir: 指定测试结果目录，默认为../../ai-test-results
 */

const path = require('path');
const fs = require('fs');

// 解析命令行参数
const args = process.argv.slice(2);
const dirArg = args.find(arg => arg.startsWith('--dir='));

const resultsDir = dirArg 
  ? dirArg.split('=')[1] 
  : path.resolve(__dirname, '../../ai-test-results');

// 检查目录是否存在
if (!fs.existsSync(resultsDir)) {
  console.error(`错误: 测试结果目录不存在: ${resultsDir}`);
  process.exit(1);
}

// 读取测试结果文件
const resultFiles = fs.readdirSync(resultsDir)
  .filter(file => file.startsWith('ai-test-results-') && file.endsWith('.json'));

if (resultFiles.length === 0) {
  console.error(`错误: 未找到测试结果文件`);
  process.exit(1);
}

// 按时间排序，获取最新的测试结果
resultFiles.sort((a, b) => {
  const timeA = new Date(a.replace('ai-test-results-', '').replace('.json', '').replace(/-/g, ':'));
  const timeB = new Date(b.replace('ai-test-results-', '').replace('.json', '').replace(/-/g, ':'));
  return timeB - timeA;
});

const latestResultFile = resultFiles[0];
console.log(`使用最新的测试结果文件: ${latestResultFile}`);

// 读取测试结果
const testResults = JSON.parse(fs.readFileSync(path.join(resultsDir, latestResultFile), 'utf8'));

// 评估指标
const metrics = {
  // 响应时间评分 (越低越好)
  responseTimeScore: (provider) => {
    const summary = testResults.summary[provider];
    if (!summary || summary.successTests === 0) return 0;
    
    // 找出所有提供商中的最低和最高响应时间
    const times = Object.values(testResults.summary)
      .filter(s => s.successTests > 0)
      .map(s => s.averageResponseTime);
    
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    // 如果所有响应时间相同，返回满分
    if (minTime === maxTime) return 10;
    
    // 计算得分 (10分满分，线性映射)
    return 10 - ((summary.averageResponseTime - minTime) / (maxTime - minTime)) * 9;
  },
  
  // 成功率评分
  successRateScore: (provider) => {
    const summary = testResults.summary[provider];
    if (!summary || summary.totalTests === 0) return 0;
    
    // 计算成功率 (0-1)
    const successRate = summary.successTests / summary.totalTests;
    
    // 转换为10分制
    return successRate * 10;
  },
  
  // Token效率评分 (越低越好)
  tokenEfficiencyScore: (provider) => {
    const summary = testResults.summary[provider];
    if (!summary || summary.successTests === 0) return 0;
    
    // 计算每个成功测试的平均token使用量
    const avgTokensPerTest = summary.totalTokens / summary.successTests;
    
    // 找出所有提供商中的最低和最高token使用量
    const tokenUsages = Object.values(testResults.summary)
      .filter(s => s.successTests > 0)
      .map(s => s.totalTokens / s.successTests);
    
    const minTokens = Math.min(...tokenUsages);
    const maxTokens = Math.max(...tokenUsages);
    
    // 如果所有token使用量相同，返回满分
    if (minTokens === maxTokens) return 10;
    
    // 计算得分 (10分满分，线性映射)
    return 10 - ((avgTokensPerTest - minTokens) / (maxTokens - minTokens)) * 9;
  },
  
  // 内容质量评分 (基于任务成功率)
  contentQualityScore: (provider) => {
    // 这个指标需要人工评估，这里简单地使用成功率作为代理指标
    return metrics.successRateScore(provider);
  }
};

// 生成评估报告
function generateEvaluationReport() {
  console.log(`\n===== AI模型评估报告 =====`);
  
  // 计算每个提供商的评分
  const scores = {};
  
  for (const provider in testResults.summary) {
    scores[provider] = {
      responseTime: metrics.responseTimeScore(provider),
      successRate: metrics.successRateScore(provider),
      tokenEfficiency: metrics.tokenEfficiencyScore(provider),
      contentQuality: metrics.contentQualityScore(provider),
      // 总分 (加权平均)
      total: 0
    };
    
    // 计算加权总分
    scores[provider].total = (
      scores[provider].responseTime * 0.2 +
      scores[provider].successRate * 0.3 +
      scores[provider].tokenEfficiency * 0.2 +
      scores[provider].contentQuality * 0.3
    ).toFixed(2);
  }
  
  // 打印评分表
  console.log(`\n评分表 (满分10分):`);
  console.log(`\n提供商\t\t响应时间\t成功率\t\tToken效率\t内容质量\t总分`);
  console.log(`----------------------------------------------------------------------------------`);
  
  // 按总分排序
  const sortedProviders = Object.keys(scores).sort((a, b) => scores[b].total - scores[a].total);
  
  for (const provider of sortedProviders) {
    const s = scores[provider];
    const providerName = provider === 'bytedance' ? '字节大模型' : 
                         provider === 'aliyun' ? '阿里云百炼' : 
                         provider === 'hunyuan' ? '腾讯混元' : provider;
    
    console.log(`${providerName}\t${s.responseTime.toFixed(1)}\t\t${s.successRate.toFixed(1)}\t\t${s.tokenEfficiency.toFixed(1)}\t\t${s.contentQuality.toFixed(1)}\t\t${s.total}`);
  }
  
  // 模型性能分析
  console.log(`\n\n===== 模型性能分析 =====`);
  
  // 分析每个提供商的模型表现
  for (const provider in testResults.details) {
    const providerName = provider === 'bytedance' ? '字节大模型' : 
                         provider === 'aliyun' ? '阿里云百炼' : 
                         provider === 'hunyuan' ? '腾讯混元' : provider;
    
    console.log(`\n## ${providerName}`);
    
    // 分析每个模型
    for (const model in testResults.details[provider]) {
      console.log(`\n### 模型: ${model}`);
      
      // 计算模型成功率
      let totalTests = 0;
      let successTests = 0;
      let totalResponseTime = 0;
      let totalTokens = 0;
      
      for (const task in testResults.details[provider][model]) {
        const result = testResults.details[provider][model][task];
        totalTests++;
        
        if (result.success) {
          successTests++;
          totalResponseTime += result.responseTime;
          totalTokens += result.tokenUsage.total_tokens;
        }
      }
      
      const successRate = successTests / totalTests;
      const avgResponseTime = successTests > 0 ? totalResponseTime / successTests : 0;
      const avgTokens = successTests > 0 ? totalTokens / successTests : 0;
      
      console.log(`- 成功率: ${(successRate * 100).toFixed(2)}%`);
      console.log(`- 平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`- 平均Token使用量: ${avgTokens.toFixed(2)}`);
      
      // 分析任务表现
      console.log(`\n任务表现:`);
      
      for (const task in testResults.details[provider][model]) {
        const result = testResults.details[provider][model][task];
        const taskName = task === 'connection' ? '连接测试' : 
                         task === 'tags' ? '标签生成' : 
                         task === 'plan' ? '学习计划生成' : 
                         task === 'creative' ? '创意写作' : task;
        
        console.log(`- ${taskName}: ${result.success ? '成功' : '失败'}`);
        
        if (result.success) {
          console.log(`  响应时间: ${result.responseTime.toFixed(2)}ms`);
          console.log(`  Token使用量: ${result.tokenUsage.total_tokens}`);
        } else {
          console.log(`  错误: ${result.error}`);
        }
      }
    }
  }
  
  // 任务表现分析
  console.log(`\n\n===== 任务表现分析 =====`);
  
  const tasks = {
    connection: '连接测试',
    tags: '标签生成',
    plan: '学习计划生成',
    creative: '创意写作'
  };
  
  for (const taskKey in tasks) {
    console.log(`\n## ${tasks[taskKey]}`);
    
    // 计算每个提供商在此任务上的表现
    const taskResults = {};
    
    for (const provider in testResults.details) {
      taskResults[provider] = {
        models: {},
        successCount: 0,
        totalCount: 0,
        totalResponseTime: 0,
        totalTokens: 0
      };
      
      for (const model in testResults.details[provider]) {
        if (testResults.details[provider][model][taskKey]) {
          const result = testResults.details[provider][model][taskKey];
          taskResults[provider].totalCount++;
          
          if (result.success) {
            taskResults[provider].successCount++;
            taskResults[provider].totalResponseTime += result.responseTime;
            taskResults[provider].totalTokens += result.tokenUsage.total_tokens;
          }
          
          taskResults[provider].models[model] = result;
        }
      }
    }
    
    // 打印任务表现
    console.log(`提供商\t\t成功率\t\t平均响应时间\t平均Token使用量`);
    console.log(`-----------------------------------------------------------------`);
    
    for (const provider in taskResults) {
      const r = taskResults[provider];
      const providerName = provider === 'bytedance' ? '字节大模型' : 
                           provider === 'aliyun' ? '阿里云百炼' : 
                           provider === 'hunyuan' ? '腾讯混元' : provider;
      
      const successRate = r.totalCount > 0 ? (r.successCount / r.totalCount * 100).toFixed(2) : '0.00';
      const avgResponseTime = r.successCount > 0 ? (r.totalResponseTime / r.successCount).toFixed(2) : '0.00';
      const avgTokens = r.successCount > 0 ? (r.totalTokens / r.successCount).toFixed(2) : '0.00';
      
      console.log(`${providerName}\t${successRate}%\t\t${avgResponseTime}ms\t${avgTokens}`);
    }
    
    // 找出此任务的最佳模型
    let bestModel = null;
    let bestResponseTime = Infinity;
    
    for (const provider in taskResults) {
      for (const model in taskResults[provider].models) {
        const result = taskResults[provider].models[model];
        if (result.success && result.responseTime < bestResponseTime) {
          bestResponseTime = result.responseTime;
          bestModel = {
            provider,
            model,
            responseTime: result.responseTime,
            tokenUsage: result.tokenUsage.total_tokens
          };
        }
      }
    }
    
    if (bestModel) {
      const providerName = bestModel.provider === 'bytedance' ? '字节大模型' : 
                           bestModel.provider === 'aliyun' ? '阿里云百炼' : 
                           bestModel.provider === 'hunyuan' ? '腾讯混元' : bestModel.provider;
      
      console.log(`\n最佳模型: ${providerName} - ${bestModel.model}`);
      console.log(`- 响应时间: ${bestModel.responseTime.toFixed(2)}ms`);
      console.log(`- Token使用量: ${bestModel.tokenUsage}`);
    }
  }
  
  // 生成建议
  console.log(`\n\n===== 建议 =====`);
  
  // 找出总分最高的提供商
  const bestProvider = sortedProviders[0];
  const bestProviderName = bestProvider === 'bytedance' ? '字节大模型' : 
                           bestProvider === 'aliyun' ? '阿里云百炼' : 
                           bestProvider === 'hunyuan' ? '腾讯混元' : bestProvider;
  
  console.log(`1. 综合表现最佳的提供商是 ${bestProviderName}，总分为 ${scores[bestProvider].total}。`);
  
  // 找出每个任务的最佳模型
  const bestModels = {};
  
  for (const taskKey in tasks) {
    let bestModel = null;
    let bestResponseTime = Infinity;
    
    for (const provider in testResults.details) {
      for (const model in testResults.details[provider]) {
        if (testResults.details[provider][model][taskKey]) {
          const result = testResults.details[provider][model][taskKey];
          if (result.success && result.responseTime < bestResponseTime) {
            bestResponseTime = result.responseTime;
            bestModel = {
              provider,
              model,
              responseTime: result.responseTime
            };
          }
        }
      }
    }
    
    if (bestModel) {
      bestModels[taskKey] = bestModel;
    }
  }
  
  console.log(`\n2. 针对不同任务的最佳模型建议:`);
  
  for (const taskKey in bestModels) {
    const model = bestModels[taskKey];
    const providerName = model.provider === 'bytedance' ? '字节大模型' : 
                         model.provider === 'aliyun' ? '阿里云百炼' : 
                         model.provider === 'hunyuan' ? '腾讯混元' : model.provider;
    
    console.log(`- ${tasks[taskKey]}: ${providerName} - ${model.model}`);
  }
  
  // 成本效益分析
  console.log(`\n3. 成本效益分析:`);
  
  // 按Token效率排序
  const tokenEfficiencyRanking = Object.keys(scores).sort((a, b) => 
    scores[b].tokenEfficiency - scores[a].tokenEfficiency
  );
  
  const mostEfficientProvider = tokenEfficiencyRanking[0];
  const mostEfficientProviderName = mostEfficientProvider === 'bytedance' ? '字节大模型' : 
                                    mostEfficientProvider === 'aliyun' ? '阿里云百炼' : 
                                    mostEfficientProvider === 'hunyuan' ? '腾讯混元' : mostEfficientProvider;
  
  console.log(`- 最具成本效益的提供商是 ${mostEfficientProviderName}，Token效率得分为 ${scores[mostEfficientProvider].tokenEfficiency.toFixed(1)}。`);
  
  // 响应时间分析
  console.log(`\n4. 响应时间分析:`);
  
  // 按响应时间排序
  const responseTimeRanking = Object.keys(scores).sort((a, b) => 
    scores[b].responseTime - scores[a].responseTime
  );
  
  const fastestProvider = responseTimeRanking[0];
  const fastestProviderName = fastestProvider === 'bytedance' ? '字节大模型' : 
                              fastestProvider === 'aliyun' ? '阿里云百炼' : 
                              fastestProvider === 'hunyuan' ? '腾讯混元' : fastestProvider;
  
  console.log(`- 响应最快的提供商是 ${fastestProviderName}，响应时间得分为 ${scores[fastestProvider].responseTime.toFixed(1)}。`);
  
  // 保存评估报告
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(resultsDir, `ai-evaluation-report-${timestamp}.md`);
  
  let reportContent = `# AI模型评估报告

## 评分表 (满分10分)

| 提供商 | 响应时间 | 成功率 | Token效率 | 内容质量 | 总分 |
|--------|----------|--------|-----------|----------|------|
`;

  for (const provider of sortedProviders) {
    const s = scores[provider];
    const providerName = provider === 'bytedance' ? '字节大模型' : 
                         provider === 'aliyun' ? '阿里云百炼' : 
                         provider === 'hunyuan' ? '腾讯混元' : provider;
    
    reportContent += `| ${providerName} | ${s.responseTime.toFixed(1)} | ${s.successRate.toFixed(1)} | ${s.tokenEfficiency.toFixed(1)} | ${s.contentQuality.toFixed(1)} | ${s.total} |\n`;
  }

  reportContent += `
## 建议

1. 综合表现最佳的提供商是 ${bestProviderName}，总分为 ${scores[bestProvider].total}。

2. 针对不同任务的最佳模型建议:
`;

  for (const taskKey in bestModels) {
    const model = bestModels[taskKey];
    const providerName = model.provider === 'bytedance' ? '字节大模型' : 
                         model.provider === 'aliyun' ? '阿里云百炼' : 
                         model.provider === 'hunyuan' ? '腾讯混元' : model.provider;
    
    reportContent += `   - ${tasks[taskKey]}: ${providerName} - ${model.model}\n`;
  }

  reportContent += `
3. 成本效益分析:
   - 最具成本效益的提供商是 ${mostEfficientProviderName}，Token效率得分为 ${scores[mostEfficientProvider].tokenEfficiency.toFixed(1)}。

4. 响应时间分析:
   - 响应最快的提供商是 ${fastestProviderName}，响应时间得分为 ${scores[fastestProvider].responseTime.toFixed(1)}。
`;

  fs.writeFileSync(reportFile, reportContent);
  console.log(`\n评估报告已保存到: ${reportFile}`);
}

// 生成评估报告
generateEvaluationReport();
