/**
 * 数据库V3升级执行脚本
 * 
 * 此脚本执行以下步骤：
 * 1. 备份当前数据库
 * 2. 执行V3升级迁移
 * 
 * 使用方法：
 * node backend/scripts/execute_v3_upgrade.js
 */

const { spawn } = require('child_process');
const path = require('path');
const logger = require('../config/logger');

/**
 * 执行子进程并返回Promise
 * @param {string} scriptPath - 脚本路径
 * @param {string} description - 描述
 * @returns {Promise<void>}
 */
function executeScript(scriptPath, description) {
  return new Promise((resolve, reject) => {
    logger.info(`开始${description}...`);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit'
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        logger.info(`${description}完成`);
        resolve();
      } else {
        const error = new Error(`${description}失败，退出代码: ${code}`);
        logger.error(error.message);
        reject(error);
      }
    });
    
    child.on('error', (error) => {
      logger.error(`${description}执行错误: ${error.message}`);
      reject(error);
    });
  });
}

/**
 * 主函数
 */
async function main() {
  try {
    // 显示警告并等待确认
    console.log('\x1b[33m%s\x1b[0m', '警告: 此脚本将升级数据库到V3版本，此操作不可逆！');
    console.log('\x1b[33m%s\x1b[0m', '请确保您已经理解了升级内容并已做好准备。');
    console.log('\x1b[33m%s\x1b[0m', '按Ctrl+C可以取消操作。');
    
    // 等待5秒，给用户时间取消
    console.log('5秒后将开始执行升级...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 1. 备份数据库
    const backupScriptPath = path.join(__dirname, 'backup_database.js');
    await executeScript(backupScriptPath, '数据库备份');
    
    // 2. 执行V3升级迁移
    const migrationScriptPath = path.join(__dirname, 'run_v3_migration.js');
    await executeScript(migrationScriptPath, 'V3升级迁移');
    
    logger.info('数据库V3升级全部完成！');
    process.exit(0);
  } catch (error) {
    logger.error(`数据库V3升级失败: ${error.message}`);
    logger.error('请从备份恢复数据库');
    process.exit(1);
  }
}

// 执行主函数
main();
