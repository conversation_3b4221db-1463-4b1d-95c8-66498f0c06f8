/**
 * 修复数据库连接问题并运行JSON字段性能优化迁移脚本
 */
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const logger = console;

// 数据库配置
const dbConfigs = [
  // 配置1: 使用.env中的Docker配置
  {
    name: 'Docker配置',
    config: {
      host: 'localhost', // 在本地环境中使用localhost替代mysql容器名
      port: 3306,
      user: 'aibubb_user',
      password: 'aibubb_password',
      database: 'aibubb_db'
    }
  },
  // 配置2: 使用run-json-migration.js中的配置
  {
    name: 'JSON迁移脚本配置',
    config: {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'secret',
      database: 'aibubb_db'
    }
  },
  // 配置3: 使用默认的root用户
  {
    name: '默认root配置',
    config: {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'aibubb_db'
    }
  }
];

/**
 * 测试数据库连接
 * @param {Object} config - 数据库配置
 * @returns {Promise<boolean>} 连接是否成功
 */
async function testConnection(config) {
  let connection;
  try {
    logger.log(`尝试连接数据库 (${config.name})...`);
    connection = await mysql.createConnection(config.config);
    logger.log(`数据库连接成功 (${config.name})`);
    return true;
  } catch (error) {
    logger.error(`数据库连接失败 (${config.name}): ${error.message}`);
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * 创建本地环境配置文件
 * @param {Object} config - 数据库配置
 */
function createLocalEnvFile(config) {
  logger.log('创建本地环境配置文件...');
  
  const envLocalPath = path.resolve(process.cwd(), '.env.local');
  const envContent = `# 本地环境配置
# 由fix-db-connection.js脚本自动生成

# 数据库配置
DB_HOST=${config.config.host}
DB_PORT=${config.config.port}
DB_NAME=${config.config.database}
DB_USER=${config.config.user}
DB_PASSWORD=${config.config.password}
`;

  fs.writeFileSync(envLocalPath, envContent);
  logger.log(`本地环境配置文件已创建: ${envLocalPath}`);
}

/**
 * 运行JSON字段性能优化迁移脚本
 * @param {Object} config - 数据库配置
 */
async function runJsonMigration(config) {
  try {
    logger.log('运行JSON字段性能优化迁移脚本...');
    
    // 修改run-json-migration.js中的数据库配置
    const migrationScriptPath = path.resolve(__dirname, '../run-json-migration.js');
    let migrationScript = fs.readFileSync(migrationScriptPath, 'utf8');
    
    // 替换数据库配置
    migrationScript = migrationScript.replace(
      /const dbConfig = {[\s\S]*?};/,
      `const dbConfig = {
  host: '${config.config.host}',
  port: ${config.config.port},
  user: '${config.config.user}',
  password: '${config.config.password}',
  database: '${config.config.database}'
};`
    );
    
    // 写回文件
    fs.writeFileSync(migrationScriptPath, migrationScript);
    logger.log('已更新迁移脚本中的数据库配置');
    
    // 运行迁移脚本
    logger.log('执行迁移脚本...');
    execSync('node backend/run-json-migration.js', { stdio: 'inherit' });
    logger.log('迁移脚本执行成功');
    
    return true;
  } catch (error) {
    logger.error(`运行迁移脚本失败: ${error.message}`);
    return false;
  }
}

/**
 * 验证迁移结果
 * @param {Object} config - 数据库配置
 */
async function verifyMigration(config) {
  try {
    logger.log('验证迁移结果...');
    
    // 修改verify-migration.js中的数据库配置
    const verifyScriptPath = path.resolve(__dirname, '../verify-migration.js');
    let verifyScript = fs.readFileSync(verifyScriptPath, 'utf8');
    
    // 替换数据库配置
    verifyScript = verifyScript.replace(
      /const dbConfig = {[\s\S]*?};/,
      `const dbConfig = {
  host: '${config.config.host}',
  port: ${config.config.port},
  user: '${config.config.user}',
  password: '${config.config.password}',
  database: '${config.config.database}'
};`
    );
    
    // 写回文件
    fs.writeFileSync(verifyScriptPath, verifyScript);
    logger.log('已更新验证脚本中的数据库配置');
    
    // 运行验证脚本
    logger.log('执行验证脚本...');
    execSync('node backend/verify-migration.js', { stdio: 'inherit' });
    logger.log('验证脚本执行成功');
    
    return true;
  } catch (error) {
    logger.error(`验证迁移结果失败: ${error.message}`);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  logger.log('开始修复数据库连接问题...');
  
  // 测试所有配置
  let successConfig = null;
  for (const config of dbConfigs) {
    const success = await testConnection(config);
    if (success) {
      successConfig = config;
      break;
    }
  }
  
  if (!successConfig) {
    logger.error('所有数据库配置都连接失败，请检查数据库服务是否正常运行');
    process.exit(1);
  }
  
  // 创建本地环境配置文件
  createLocalEnvFile(successConfig);
  
  // 运行JSON字段性能优化迁移脚本
  const migrationSuccess = await runJsonMigration(successConfig);
  if (!migrationSuccess) {
    logger.error('JSON字段性能优化迁移失败');
    process.exit(1);
  }
  
  // 验证迁移结果
  const verifySuccess = await verifyMigration(successConfig);
  if (!verifySuccess) {
    logger.error('验证迁移结果失败');
    process.exit(1);
  }
  
  logger.log('数据库连接问题修复完成，JSON字段性能优化迁移成功');
  process.exit(0);
}

// 执行主函数
main().catch(error => {
  logger.error(`执行过程中发生错误: ${error.message}`);
  logger.error(error.stack);
  process.exit(1);
});
