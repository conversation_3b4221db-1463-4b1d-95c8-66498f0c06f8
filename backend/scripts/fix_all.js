/**
 * 全面修复脚本
 * 1. 修复数据库结构和关系
 * 2. 修复服务层代码
 */
const { fixDatabaseComplete } = require('./fix_database_complete');
const { fixServiceLayer } = require('./fix_service_layer');
const logger = require('../config/logger');

async function fixAll() {
  try {
    logger.info('开始全面修复...');
    
    // 1. 修复数据库
    logger.info('开始修复数据库...');
    await fixDatabaseComplete();
    logger.info('数据库修复完成');
    
    // 2. 修复服务层
    logger.info('开始修复服务层...');
    await fixServiceLayer();
    logger.info('服务层修复完成');
    
    logger.info('全面修复成功完成');
    return true;
  } catch (error) {
    logger.error(`全面修复失败: ${error.message}`);
    logger.error(error.stack);
    throw error;
  }
}

// 如果直接运行此脚本，执行修复
if (require.main === module) {
  fixAll()
    .then(() => {
      console.log('全面修复成功完成');
      process.exit(0);
    })
    .catch(error => {
      console.error(`全面修复失败: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { fixAll };
