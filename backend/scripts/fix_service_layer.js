/**
 * 修复服务层脚本
 * 1. 添加缺失的getSystemDefaultPlan方法
 */
const fs = require('fs');
const path = require('path');
const logger = require('../config/logger');

async function fixServiceLayer() {
  try {
    logger.info('开始修复服务层...');
    
    // 1. 修复learningPlan.service.js，添加getSystemDefaultPlan方法
    const learningPlanServicePath = path.join(__dirname, '../services/learningPlan.service.js');
    
    // 读取文件内容
    let content = fs.readFileSync(learningPlanServicePath, 'utf8');
    
    // 检查是否已经有getSystemDefaultPlan方法
    if (!content.includes('getSystemDefaultPlan')) {
      logger.info('learningPlan.service.js中缺少getSystemDefaultPlan方法，添加...');
      
      // 找到module.exports
      const exportLine = content.indexOf('module.exports');
      
      if (exportLine === -1) {
        throw new Error('无法找到module.exports行');
      }
      
      // 添加getSystemDefaultPlan方法
      const methodToAdd = `
/**
 * 获取系统默认学习计划
 * @returns {Promise<object>} - 系统默认学习计划
 */
const getSystemDefaultPlan = async () => {
  try {
    logger.info('服务层: 开始获取系统默认学习计划');
    
    // 查询系统默认学习计划
    const defaultPlan = await LearningPlan.findOne({
      where: { is_system_default: true },
      include: [
        {
          model: Theme,
          as: 'theme',
          attributes: ['id', 'name', 'english_name', 'description', 'icon', 'color']
        }
      ]
    });
    
    if (!defaultPlan) {
      logger.warn('服务层: 系统默认学习计划不存在');
      return null;
    }
    
    logger.info(\`服务层: 成功获取系统默认学习计划 ID: \${defaultPlan.id}\`);
    return defaultPlan;
  } catch (error) {
    logger.error(\`服务层: 获取系统默认学习计划失败: \${error.message}\`);
    throw error;
  }
};
`;
      
      // 在module.exports前插入方法
      content = content.slice(0, exportLine) + methodToAdd + content.slice(exportLine);
      
      // 修改module.exports，添加getSystemDefaultPlan
      const exportContent = content.substring(exportLine);
      const exportEndLine = exportContent.indexOf('};') + exportLine + 2;
      
      // 检查是否已经导出了getSystemDefaultPlan
      if (!exportContent.includes('getSystemDefaultPlan')) {
        // 找到最后一个导出项
        const lastExportItem = exportContent.lastIndexOf(',', exportEndLine - exportLine);
        
        if (lastExportItem === -1) {
          throw new Error('无法找到最后一个导出项');
        }
        
        // 在最后一个导出项后添加getSystemDefaultPlan
        content = content.slice(0, exportLine + lastExportItem + 1) + 
                 '\n  getSystemDefaultPlan' + 
                 content.slice(exportLine + lastExportItem + 1);
      }
      
      // 写入文件
      fs.writeFileSync(learningPlanServicePath, content, 'utf8');
      
      logger.info('learningPlan.service.js修复完成');
    } else {
      logger.info('learningPlan.service.js已包含getSystemDefaultPlan方法，无需修复');
    }
    
    // 2. 修复tag.controller.js中的getSystemDefaultPlanTags方法
    const tagControllerPath = path.join(__dirname, '../controllers/tag.controller.js');
    
    // 读取文件内容
    let tagControllerContent = fs.readFileSync(tagControllerPath, 'utf8');
    
    // 检查是否需要修复getSystemDefaultPlanTags方法
    if (tagControllerContent.includes('getSystemDefaultPlanTags') && 
        !tagControllerContent.includes('tagService.getSystemDefaultPlanTags')) {
      logger.info('修复tag.controller.js中的getSystemDefaultPlanTags方法...');
      
      // 找到getSystemDefaultPlanTags方法的开始位置
      const methodStartIndex = tagControllerContent.indexOf('const getSystemDefaultPlanTags');
      
      if (methodStartIndex === -1) {
        throw new Error('无法找到getSystemDefaultPlanTags方法');
      }
      
      // 找到方法的结束位置
      const methodEndIndex = tagControllerContent.indexOf('};', methodStartIndex) + 2;
      
      // 替换方法内容
      const newMethodContent = `const getSystemDefaultPlanTags = async (req, res) => {
  try {
    // 记录请求信息
    const userInfo = req.user ? \`用户ID: \${req.user.userId}\` : '未登录用户';
    logger.info(\`控制器层: 开始获取系统默认标签, \${userInfo}\`);

    // 使用标签服务获取系统默认计划标签
    const tags = await tagService.getSystemDefaultPlanTags();
    
    if (!tags || tags.length === 0) {
      logger.warn('控制器层: 未找到系统默认计划标签，返回备用数据');
      
      // 返回备用数据
      const defaultTags = [
        { id: 21, name: '平台介绍', relevanceScore: 0.95, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 0, category: null },
        { id: 22, name: '泡泡功能', relevanceScore: 0.90, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 1, category: null },
        { id: 23, name: '广场探索', relevanceScore: 0.85, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 2, category: null },
        { id: 24, name: '学习计划', relevanceScore: 0.80, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 3, category: null },
        { id: 25, name: '笔记技巧', relevanceScore: 0.75, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 4, category: null }
      ];
      
      return apiResponse.success(res, { tags: defaultTags });
    }
    
    // 格式化标签数据
    const formattedTags = tags.map(tag => ({
      id: tag.id,
      name: tag.name,
      relevanceScore: tag.relevance_score,
      weight: tag.weight,
      usageCount: tag.usage_count || 0,
      isVerified: tag.is_verified || false,
      sortOrder: tag.sort_order,
      category: tag.category ? {
        id: tag.category.id,
        name: tag.category.name
      } : null
    }));

    logger.info(\`控制器层: 返回\${formattedTags.length}个标签\`);
    
    return apiResponse.success(res, { tags: formattedTags });
  } catch (error) {
    logger.error(\`控制器层: 获取系统默认计划标签失败: \${error.message}\`);
    logger.error('控制器层: 错误堆栈:', error.stack);

    // 即使出错也尝试返回默认数据
    try {
      const defaultTags = [
        { id: 21, name: '平台介绍', relevanceScore: 0.95, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 0, category: null },
        { id: 22, name: '泡泡功能', relevanceScore: 0.90, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 1, category: null },
        { id: 23, name: '广场探索', relevanceScore: 0.85, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 2, category: null },
        { id: 24, name: '学习计划', relevanceScore: 0.80, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 3, category: null },
        { id: 25, name: '笔记技巧', relevanceScore: 0.75, weight: 1.0, usageCount: 0, isVerified: true, sortOrder: 4, category: null }
      ];
      return apiResponse.success(res, { tags: defaultTags });
    } catch (fallbackError) {
      logger.error('控制器层: 返回默认标签数据失败:', fallbackError);
      return apiResponse.error(res, '获取系统默认计划标签失败', 'SERVER_ERROR', 500);
    }
  }
};`;
      
      // 替换方法
      tagControllerContent = tagControllerContent.slice(0, methodStartIndex) + 
                            newMethodContent + 
                            tagControllerContent.slice(methodEndIndex);
      
      // 写入文件
      fs.writeFileSync(tagControllerPath, tagControllerContent, 'utf8');
      
      logger.info('tag.controller.js修复完成');
    } else {
      logger.info('tag.controller.js不需要修复或已修复');
    }
    
    // 3. 修复tag.service.js，添加getSystemDefaultPlanTags方法
    const tagServicePath = path.join(__dirname, '../services/tag.service.js');
    
    // 读取文件内容
    let tagServiceContent = fs.readFileSync(tagServicePath, 'utf8');
    
    // 检查是否已经有getSystemDefaultPlanTags方法
    if (!tagServiceContent.includes('getSystemDefaultPlanTags')) {
      logger.info('tag.service.js中缺少getSystemDefaultPlanTags方法，添加...');
      
      // 找到module.exports
      const tagExportLine = tagServiceContent.indexOf('module.exports');
      
      if (tagExportLine === -1) {
        throw new Error('无法找到module.exports行');
      }
      
      // 添加getSystemDefaultPlanTags方法
      const tagMethodToAdd = `
/**
 * 获取系统默认学习计划的标签
 * @returns {Promise<Array>} - 标签数组
 */
const getSystemDefaultPlanTags = async () => {
  try {
    logger.info('服务层: 开始获取系统默认计划标签');
    
    // 查询系统默认学习计划
    const defaultPlan = await LearningPlan.findOne({
      where: { is_system_default: true }
    });
    
    if (!defaultPlan) {
      logger.warn('服务层: 系统默认学习计划不存在');
      return [];
    }
    
    // 查询标签
    const tags = await Tag.findAll({
      where: { plan_id: defaultPlan.id },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'level']
        }
      ],
      order: [['sort_order', 'ASC']]
    });
    
    logger.info(\`服务层: 成功获取系统默认计划标签，共\${tags.length}个\`);
    return tags;
  } catch (error) {
    logger.error(\`服务层: 获取系统默认计划标签失败: \${error.message}\`);
    throw error;
  }
};
`;
      
      // 在module.exports前插入方法
      tagServiceContent = tagServiceContent.slice(0, tagExportLine) + tagMethodToAdd + tagServiceContent.slice(tagExportLine);
      
      // 修改module.exports，添加getSystemDefaultPlanTags
      const tagExportContent = tagServiceContent.substring(tagExportLine);
      const tagExportEndLine = tagExportContent.indexOf('};') + tagExportLine + 2;
      
      // 检查是否已经导出了getSystemDefaultPlanTags
      if (!tagExportContent.includes('getSystemDefaultPlanTags')) {
        // 找到最后一个导出项
        const lastTagExportItem = tagExportContent.lastIndexOf(',', tagExportEndLine - tagExportLine);
        
        if (lastTagExportItem === -1) {
          throw new Error('无法找到最后一个导出项');
        }
        
        // 在最后一个导出项后添加getSystemDefaultPlanTags
        tagServiceContent = tagServiceContent.slice(0, tagExportLine + lastTagExportItem + 1) + 
                           '\n  getSystemDefaultPlanTags' + 
                           tagServiceContent.slice(tagExportLine + lastTagExportItem + 1);
      }
      
      // 写入文件
      fs.writeFileSync(tagServicePath, tagServiceContent, 'utf8');
      
      logger.info('tag.service.js修复完成');
    } else {
      logger.info('tag.service.js已包含getSystemDefaultPlanTags方法，无需修复');
    }
    
    logger.info('服务层修复完成');
    return true;
  } catch (error) {
    logger.error(`服务层修复失败: ${error.message}`);
    logger.error(error.stack);
    throw error;
  }
}

// 如果直接运行此脚本，执行修复
if (require.main === module) {
  fixServiceLayer()
    .then(() => {
      console.log('服务层修复成功完成');
      process.exit(0);
    })
    .catch(error => {
      console.error(`服务层修复失败: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { fixServiceLayer };
