/**
 * 测试数据生成脚本
 * 用于生成集成测试和契约测试所需的测试数据
 * 
 * 使用方法:
 * node backend/scripts/generate-test-data.js [--env=test] [--output-dir=backend/test/contract-test-data]
 */

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const chalk = require('chalk');

// 定义命令行选项
program
  .option('--env <env>', '环境 (test, development)', 'test')
  .option('--output-dir <dir>', '输出目录', 'backend/test/contract-test-data')
  .option('--verbose', '显示详细日志', false)
  .parse(process.argv);

const options = program.opts();

// 确保输出目录存在
const outputDir = path.resolve(process.cwd(), options.outputDir);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(chalk.green(`✓ 已创建输出目录: ${outputDir}`));
}

// 生成用户数据
function generateUsers(count = 10) {
  const users = [];
  
  for (let i = 1; i <= count; i++) {
    const isAdmin = i === 1; // 第一个用户是管理员
    
    users.push({
      id: i,
      username: isAdmin ? 'admin_user' : `test_user${i}`,
      email: isAdmin ? '<EMAIL>' : `user${i}@example.com`,
      role: isAdmin ? 'admin' : 'user',
      profile: {
        avatar: `https://example.com/avatar${i}.jpg`,
        bio: `测试用户${i}的个人简介`,
        location: ['北京', '上海', '广州', '深圳'][i % 4]
      },
      stats: {
        learningPlans: Math.floor(Math.random() * 5) + 1,
        exercises: Math.floor(Math.random() * 20) + 1,
        notes: Math.floor(Math.random() * 10) + 1
      },
      createdAt: new Date(2025, 0, i).toISOString(),
      updatedAt: new Date(2025, 0, i).toISOString()
    });
  }
  
  return users;
}

// 生成标签分类数据
function generateTagCategories() {
  return [
    {
      id: 1,
      name: '人际关系',
      description: '与人际交往相关的标签',
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 2,
      name: '个人成长',
      description: '与个人成长相关的标签',
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 3,
      name: '职场技能',
      description: '与职场技能相关的标签',
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    }
  ];
}

// 生成标签数据
function generateTags() {
  return [
    {
      id: 1,
      name: '沟通',
      categoryId: 1,
      usageCount: 120,
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 2,
      name: '人际关系',
      categoryId: 1,
      usageCount: 85,
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 3,
      name: '自我提升',
      categoryId: 2,
      usageCount: 150,
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 4,
      name: '情绪管理',
      categoryId: 2,
      usageCount: 95,
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 5,
      name: '职场技能',
      categoryId: 3,
      usageCount: 110,
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    }
  ];
}

// 生成主题数据
function generateThemes() {
  return [
    {
      id: 1,
      name: '人际沟通',
      description: '提升人际沟通能力的主题',
      imageUrl: 'https://example.com/themes/communication.jpg',
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 2,
      name: '情绪管理',
      description: '提升情绪管理能力的主题',
      imageUrl: 'https://example.com/themes/emotion.jpg',
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    },
    {
      id: 3,
      name: '职场技能',
      description: '提升职场技能的主题',
      imageUrl: 'https://example.com/themes/career.jpg',
      createdAt: new Date(2025, 0, 1).toISOString(),
      updatedAt: new Date(2025, 0, 1).toISOString()
    }
  ];
}

// 生成学习计划数据
function generateLearningPlans(users, themes) {
  const plans = [];
  let id = 1;
  
  for (const user of users) {
    for (const theme of themes) {
      if (Math.random() > 0.3) { // 70%的概率创建学习计划
        const targetDays = [7, 14, 21, 30][Math.floor(Math.random() * 4)];
        const statusOptions = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED'];
        const status = statusOptions[Math.floor(Math.random() * 3)];
        const progress = status === 'NOT_STARTED' ? 0 : 
                        status === 'COMPLETED' ? 100 : 
                        Math.floor(Math.random() * 80) + 10;
        
        plans.push({
          id: id++,
          userId: user.id,
          themeId: theme.id,
          title: `${theme.name}学习计划`,
          description: `提升${theme.name}能力的学习计划`,
          targetDays,
          status,
          progress,
          content: {
            designPrinciple: `通过系统学习和实践，提升${theme.name}能力`,
            contentPlan: Array(3).fill(0).map((_, i) => ({
              day: i + 1,
              title: ['基础知识', '核心技能', '实践应用'][i],
              description: `学习${theme.name}的${['基础知识', '核心技能', '实践应用'][i]}`
            }))
          },
          tags: generateTags().slice(0, 3).map(tag => tag.name),
          createdAt: new Date(2025, 1, id).toISOString(),
          updatedAt: new Date(2025, 1, id + 5).toISOString()
        });
      }
    }
  }
  
  return plans;
}

// 生成并保存数据
function generateAndSaveData() {
  try {
    console.log(chalk.yellow('生成测试数据...'));
    
    // 生成数据
    const users = generateUsers(5);
    const tagCategories = generateTagCategories();
    const tags = generateTags();
    const themes = generateThemes();
    const learningPlans = generateLearningPlans(users, themes);
    
    // 创建数据对象
    const data = {
      users,
      tagCategories,
      tags,
      themes,
      learningPlans
    };
    
    // 保存完整数据
    const fullDataPath = path.join(outputDir, 'full-data.json');
    fs.writeFileSync(fullDataPath, JSON.stringify(data, null, 2));
    console.log(chalk.green(`✓ 已保存完整数据: ${fullDataPath}`));
    
    // 保存各个实体的数据
    for (const [entity, entityData] of Object.entries(data)) {
      const entityPath = path.join(outputDir, `${entity}.json`);
      fs.writeFileSync(entityPath, JSON.stringify(entityData, null, 2));
      console.log(chalk.green(`✓ 已保存${entity}数据: ${entityPath}`));
    }
    
    console.log(chalk.green('✓ 测试数据生成完成'));
    return true;
  } catch (error) {
    console.error(chalk.red('✗ 生成测试数据失败:'), error);
    return false;
  }
}

// 主函数
function main() {
  console.log(chalk.blue('开始生成测试数据...'));
  console.log(chalk.gray(`环境: ${options.env}`));
  console.log(chalk.gray(`输出目录: ${outputDir}`));
  
  const success = generateAndSaveData();
  
  if (success) {
    console.log(chalk.green('✓ 测试数据生成成功'));
    process.exit(0);
  } else {
    console.error(chalk.red('✗ 测试数据生成失败'));
    process.exit(1);
  }
}

// 执行主函数
main();
