/**
 * 系统健康检查脚本
 */

require('dotenv').config();

const http = require('http');
const https = require('https');
const mysql = require('mysql2/promise');
const redis = require('redis');
const fs = require('fs');
const path = require('path');
const config = require('../config/config');
const logger = require('../config/logger');

class HealthChecker {
  constructor() {
    this.checks = [];
    this.timeout = parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000;
    this.retries = parseInt(process.env.HEALTH_CHECK_RETRIES) || 3;
  }

  /**
   * 执行所有健康检查
   */
  async performHealthCheck() {
    console.log('🏥 开始系统健康检查...\n');

    const results = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {},
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        warnings: 0,
      },
    };

    try {
      // 应用程序健康检查
      results.checks.application = await this.checkApplication();

      // 数据库健康检查
      results.checks.database = await this.checkDatabase();

      // Redis健康检查
      results.checks.redis = await this.checkRedis();

      // 文件系统健康检查
      results.checks.filesystem = await this.checkFileSystem();

      // 内存使用检查
      results.checks.memory = await this.checkMemoryUsage();

      // CPU使用检查
      results.checks.cpu = await this.checkCpuUsage();

      // 磁盘空间检查
      results.checks.disk = await this.checkDiskSpace();

      // 网络连接检查
      results.checks.network = await this.checkNetworkConnectivity();

      // 外部服务检查
      results.checks.external = await this.checkExternalServices();

      // 计算总体状态
      this.calculateOverallStatus(results);

      console.log('\n✅ 健康检查完成');
      return results;
    } catch (error) {
      console.error('❌ 健康检查失败:', error);
      results.status = 'unhealthy';
      results.error = error.message;
      return results;
    }
  }

  /**
   * 检查应用程序状态
   */
  async checkApplication() {
    console.log('🔍 检查应用程序状态...');

    const check = {
      name: 'Application',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      // 检查服务器端口
      const port = config.server.port;
      const isListening = await this.checkPort('localhost', port);

      check.details.port = {
        status: isListening ? 'healthy' : 'unhealthy',
        port: port,
        listening: isListening,
      };

      // 检查环境变量
      const requiredEnvVars = ['NODE_ENV', 'DB_HOST', 'DB_NAME', 'JWT_SECRET'];
      const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

      check.details.environment = {
        status: missingVars.length === 0 ? 'healthy' : 'unhealthy',
        missing_variables: missingVars,
        node_env: process.env.NODE_ENV,
      };

      // 检查进程状态
      check.details.process = {
        status: 'healthy',
        pid: process.pid,
        uptime: process.uptime(),
        node_version: process.version,
        memory_usage: process.memoryUsage(),
      };

      // 检查日志目录
      const logDir = path.join(process.cwd(), 'logs');
      check.details.logs = {
        status: fs.existsSync(logDir) ? 'healthy' : 'warning',
        directory_exists: fs.existsSync(logDir),
        writable: fs.existsSync(logDir) ? this.checkWritePermission(logDir) : false,
      };

      // 计算应用程序整体状态
      const hasUnhealthy = Object.values(check.details).some(
        detail => detail.status === 'unhealthy',
      );
      check.status = hasUnhealthy ? 'unhealthy' : 'healthy';
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
    }

    return check;
  }

  /**
   * 检查数据库连接
   */
  async checkDatabase() {
    console.log('🗄️ 检查数据库连接...');

    const check = {
      name: 'Database',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      const dbConfig = config.database;
      const connection = await mysql.createConnection({
        host: dbConfig.host,
        port: dbConfig.port || 3306,
        user: dbConfig.username,
        password: dbConfig.password,
        database: dbConfig.database,
        connectTimeout: this.timeout,
      });

      // 测试连接
      const startTime = Date.now();
      await connection.execute('SELECT 1 as test');
      const responseTime = Date.now() - startTime;

      // 检查数据库版本
      const [versionResult] = await connection.execute('SELECT VERSION() as version');
      const version = versionResult[0].version;

      // 检查连接数
      const [processResult] = await connection.execute('SHOW PROCESSLIST');
      const connectionCount = processResult.length;

      // 检查数据库大小
      const [sizeResult] = await connection.execute(
        `
        SELECT
          ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables
        WHERE table_schema = ?
      `,
        [dbConfig.database],
      );

      const databaseSize = sizeResult[0].size_mb;

      await connection.end();

      check.details = {
        status: 'healthy',
        host: dbConfig.host,
        port: dbConfig.port || 3306,
        database: dbConfig.database,
        version: version,
        response_time_ms: responseTime,
        connection_count: connectionCount,
        size_mb: databaseSize,
      };

      // 检查响应时间
      if (responseTime > 1000) {
        check.status = 'warning';
        check.details.warning = 'Database response time is slow';
      }
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
      check.details = {
        status: 'unhealthy',
        error: error.message,
      };
    }

    return check;
  }

  /**
   * 检查Redis连接
   */
  async checkRedis() {
    console.log('⚡ 检查Redis连接...');

    const check = {
      name: 'Redis',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      const client = redis.createClient({
        url: config.redis.url,
        socket: {
          connectTimeout: this.timeout,
        },
      });

      await client.connect();

      // 测试连接
      const startTime = Date.now();
      await client.ping();
      const responseTime = Date.now() - startTime;

      // 获取Redis信息
      const info = await client.info();
      const memory = await client.info('memory');

      // 解析信息
      const redisVersion = this.parseRedisInfo(info, 'redis_version');
      const usedMemory = this.parseRedisInfo(memory, 'used_memory_human');
      const connectedClients = this.parseRedisInfo(info, 'connected_clients');

      await client.disconnect();

      check.details = {
        status: 'healthy',
        url: config.redis.url,
        version: redisVersion,
        response_time_ms: responseTime,
        used_memory: usedMemory,
        connected_clients: parseInt(connectedClients),
      };

      // 检查响应时间
      if (responseTime > 500) {
        check.status = 'warning';
        check.details.warning = 'Redis response time is slow';
      }
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
      check.details = {
        status: 'unhealthy',
        error: error.message,
      };
    }

    return check;
  }

  /**
   * 检查文件系统
   */
  async checkFileSystem() {
    console.log('📁 检查文件系统...');

    const check = {
      name: 'FileSystem',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      const directories = [
        { path: 'logs', required: false },
        { path: 'uploads', required: false },
        { path: 'public', required: true },
        { path: 'config', required: true },
        { path: 'node_modules', required: true },
      ];

      check.details.directories = {};

      for (const dir of directories) {
        const fullPath = path.join(process.cwd(), dir.path);
        const exists = fs.existsSync(fullPath);
        const writable = exists ? this.checkWritePermission(fullPath) : false;

        check.details.directories[dir.path] = {
          exists,
          writable,
          required: dir.required,
          status: !exists && dir.required ? 'unhealthy' : 'healthy',
        };
      }

      // 检查关键文件
      const files = [
        { path: 'package.json', required: true },
        { path: '.env', required: false },
        { path: '.env.production', required: false },
      ];

      check.details.files = {};

      for (const file of files) {
        const fullPath = path.join(process.cwd(), file.path);
        const exists = fs.existsSync(fullPath);

        check.details.files[file.path] = {
          exists,
          required: file.required,
          status: !exists && file.required ? 'unhealthy' : 'healthy',
        };
      }

      // 计算整体状态
      const hasUnhealthy = [
        ...Object.values(check.details.directories),
        ...Object.values(check.details.files),
      ].some(item => item.status === 'unhealthy');

      check.status = hasUnhealthy ? 'unhealthy' : 'healthy';
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
    }

    return check;
  }

  /**
   * 检查内存使用
   */
  async checkMemoryUsage() {
    console.log('🧠 检查内存使用...');

    const check = {
      name: 'Memory',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      const memUsage = process.memoryUsage();
      const totalMemory = require('os').totalmem();
      const freeMemory = require('os').freemem();
      const usedMemory = totalMemory - freeMemory;

      check.details = {
        process: {
          rss_mb: Math.round(memUsage.rss / 1024 / 1024),
          heap_used_mb: Math.round(memUsage.heapUsed / 1024 / 1024),
          heap_total_mb: Math.round(memUsage.heapTotal / 1024 / 1024),
          external_mb: Math.round(memUsage.external / 1024 / 1024),
        },
        system: {
          total_mb: Math.round(totalMemory / 1024 / 1024),
          used_mb: Math.round(usedMemory / 1024 / 1024),
          free_mb: Math.round(freeMemory / 1024 / 1024),
          usage_percent: Math.round((usedMemory / totalMemory) * 100),
        },
      };

      // 检查内存使用率
      if (check.details.system.usage_percent > 90) {
        check.status = 'unhealthy';
        check.details.warning = 'High memory usage';
      } else if (check.details.system.usage_percent > 80) {
        check.status = 'warning';
        check.details.warning = 'Memory usage is high';
      }
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
    }

    return check;
  }

  /**
   * 检查CPU使用
   */
  async checkCpuUsage() {
    console.log('⚙️ 检查CPU使用...');

    const check = {
      name: 'CPU',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      const cpus = require('os').cpus();
      const loadAvg = require('os').loadavg();

      check.details = {
        cores: cpus.length,
        model: cpus[0].model,
        load_average: {
          '1min': loadAvg[0].toFixed(2),
          '5min': loadAvg[1].toFixed(2),
          '15min': loadAvg[2].toFixed(2),
        },
        usage_percent: Math.round((loadAvg[0] / cpus.length) * 100),
      };

      // 检查CPU负载
      const cpuUsage = (loadAvg[0] / cpus.length) * 100;
      if (cpuUsage > 90) {
        check.status = 'unhealthy';
        check.details.warning = 'High CPU usage';
      } else if (cpuUsage > 80) {
        check.status = 'warning';
        check.details.warning = 'CPU usage is high';
      }
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
    }

    return check;
  }

  /**
   * 检查磁盘空间
   */
  async checkDiskSpace() {
    console.log('💾 检查磁盘空间...');

    const check = {
      name: 'Disk',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      const { execSync } = require('child_process');

      // 获取磁盘使用情况（Unix/Linux/macOS）
      let diskUsage;
      try {
        const output = execSync('df -h /', { encoding: 'utf8' });
        const lines = output.trim().split('\n');
        const data = lines[1].split(/\s+/);

        diskUsage = {
          total: data[1],
          used: data[2],
          available: data[3],
          usage_percent: parseInt(data[4].replace('%', '')),
        };
      } catch (error) {
        // Windows fallback
        diskUsage = {
          total: 'Unknown',
          used: 'Unknown',
          available: 'Unknown',
          usage_percent: 0,
        };
      }

      check.details = diskUsage;

      // 检查磁盘使用率
      if (diskUsage.usage_percent > 95) {
        check.status = 'unhealthy';
        check.details.warning = 'Disk space critically low';
      } else if (diskUsage.usage_percent > 85) {
        check.status = 'warning';
        check.details.warning = 'Disk space is running low';
      }
    } catch (error) {
      check.status = 'warning';
      check.error = error.message;
    }

    return check;
  }

  /**
   * 检查网络连接
   */
  async checkNetworkConnectivity() {
    console.log('🌐 检查网络连接...');

    const check = {
      name: 'Network',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      const testUrls = ['https://www.google.com', 'https://www.github.com'];

      check.details.connectivity = {};

      for (const url of testUrls) {
        try {
          const startTime = Date.now();
          await this.httpRequest(url);
          const responseTime = Date.now() - startTime;

          check.details.connectivity[url] = {
            status: 'healthy',
            response_time_ms: responseTime,
          };
        } catch (error) {
          check.details.connectivity[url] = {
            status: 'unhealthy',
            error: error.message,
          };
        }
      }

      // 检查是否有连接失败
      const hasFailures = Object.values(check.details.connectivity).some(
        conn => conn.status === 'unhealthy',
      );

      if (hasFailures) {
        check.status = 'warning';
        check.details.warning = 'Some network connections failed';
      }
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
    }

    return check;
  }

  /**
   * 检查外部服务
   */
  async checkExternalServices() {
    console.log('🔗 检查外部服务...');

    const check = {
      name: 'ExternalServices',
      status: 'healthy',
      details: {},
      timestamp: new Date().toISOString(),
    };

    try {
      // 检查OpenAI API（如果配置了）
      if (process.env.OPENAI_API_KEY) {
        try {
          const startTime = Date.now();
          // 这里可以添加实际的OpenAI API测试
          const responseTime = Date.now() - startTime;

          check.details.openai = {
            status: 'healthy',
            configured: true,
            response_time_ms: responseTime,
          };
        } catch (error) {
          check.details.openai = {
            status: 'unhealthy',
            configured: true,
            error: error.message,
          };
        }
      } else {
        check.details.openai = {
          status: 'warning',
          configured: false,
          message: 'OpenAI API key not configured',
        };
      }

      // 可以添加其他外部服务检查
    } catch (error) {
      check.status = 'unhealthy';
      check.error = error.message;
    }

    return check;
  }

  /**
   * 计算总体状态
   */
  calculateOverallStatus(results) {
    const checks = Object.values(results.checks);

    results.summary.total = checks.length;
    results.summary.passed = checks.filter(check => check.status === 'healthy').length;
    results.summary.failed = checks.filter(check => check.status === 'unhealthy').length;
    results.summary.warnings = checks.filter(check => check.status === 'warning').length;

    // 确定总体状态
    if (results.summary.failed > 0) {
      results.status = 'unhealthy';
    } else if (results.summary.warnings > 0) {
      results.status = 'degraded';
    } else {
      results.status = 'healthy';
    }
  }

  /**
   * 辅助方法：检查端口是否监听
   */
  async checkPort(host, port) {
    return new Promise(resolve => {
      const socket = require('net').createConnection(port, host);

      socket.on('connect', () => {
        socket.end();
        resolve(true);
      });

      socket.on('error', () => {
        resolve(false);
      });

      setTimeout(() => {
        socket.destroy();
        resolve(false);
      }, this.timeout);
    });
  }

  /**
   * 辅助方法：检查写权限
   */
  checkWritePermission(dirPath) {
    try {
      const testFile = path.join(dirPath, '.write-test');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 辅助方法：解析Redis信息
   */
  parseRedisInfo(info, key) {
    const lines = info.split('\r\n');
    const line = lines.find(line => line.startsWith(key + ':'));
    return line ? line.split(':')[1] : 'Unknown';
  }

  /**
   * 辅助方法：HTTP请求
   */
  async httpRequest(url) {
    return new Promise((resolve, reject) => {
      const client = url.startsWith('https') ? https : http;

      const req = client.get(url, res => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res);
        } else {
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      });

      req.on('error', reject);
      req.setTimeout(this.timeout, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const format = args.includes('--json') ? 'json' : 'console';
  const verbose = args.includes('--verbose');

  try {
    const checker = new HealthChecker();
    const results = await checker.performHealthCheck();

    if (format === 'json') {
      console.log(JSON.stringify(results, null, 2));
    } else {
      // 控制台输出
      console.log('\n📊 健康检查结果:');
      console.log(`   总体状态: ${getStatusEmoji(results.status)} ${results.status.toUpperCase()}`);
      console.log(`   检查项: ${results.summary.total}`);
      console.log(`   通过: ${results.summary.passed}`);
      console.log(`   失败: ${results.summary.failed}`);
      console.log(`   警告: ${results.summary.warnings}`);

      if (verbose) {
        console.log('\n📋 详细结果:');
        Object.entries(results.checks).forEach(([name, check]) => {
          console.log(`   ${getStatusEmoji(check.status)} ${name}: ${check.status}`);
          if (check.error) {
            console.log(`     错误: ${check.error}`);
          }
        });
      }
    }

    // 根据状态设置退出码
    process.exit(results.status === 'healthy' ? 0 : 1);
  } catch (error) {
    console.error('❌ 健康检查失败:', error);
    process.exit(1);
  }
}

/**
 * 获取状态表情符号
 */
function getStatusEmoji(status) {
  const emojis = {
    healthy: '✅',
    degraded: '⚠️',
    warning: '⚠️',
    unhealthy: '❌',
  };
  return emojis[status] || '❓';
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = HealthChecker;
