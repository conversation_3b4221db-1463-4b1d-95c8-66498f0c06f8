/**
 * 代码质量提升脚本
 * 用于减少重复代码，移除未使用的代码和文件，统一错误处理策略
 */
const fs = require('fs');
const path = require('path');
const logger = require('../config/logger');

/**
 * 重构统计模块中的重复代码
 */
async function refactorStatisticsModule() {
  try {
    logger.info('开始重构统计模块中的重复代码...');
    
    // 检查是否存在多个统计相关的文件
    const statisticsFiles = [
      path.join(__dirname, '../../fix-statistics.js'),
      path.join(__dirname, '../../statistics-fixed.js'),
      path.join(__dirname, '../../statistics-fixed-2.js')
    ];
    
    const existingFiles = statisticsFiles.filter(file => fs.existsSync(file));
    
    if (existingFiles.length > 1) {
      logger.info(`发现 ${existingFiles.length} 个统计相关文件，进行合并...`);
      
      // 创建备份目录
      const backupDir = path.join(__dirname, '../../backup');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir);
      }
      
      // 备份文件
      for (const file of existingFiles) {
        const fileName = path.basename(file);
        const backupPath = path.join(backupDir, fileName);
        fs.copyFileSync(file, backupPath);
        logger.info(`已备份文件 ${fileName} 到 backup 目录`);
      }
      
      // 检查最新的统计控制器文件
      const controllerPath = path.join(__dirname, '../controllers/statistics.controller.js');
      if (fs.existsSync(controllerPath)) {
        // 使用最新的控制器文件作为标准
        logger.info('使用现有的 statistics.controller.js 作为标准');
      } else {
        // 如果不存在，则创建新的控制器文件
        logger.info('创建新的 statistics.controller.js 文件');
        
        // 从最新的修复文件中提取代码
        const latestFile = existingFiles[existingFiles.length - 1];
        const content = fs.readFileSync(latestFile, 'utf8');
        
        // 创建控制器文件
        const controllerContent = `/**
 * 统计控制器
 * 处理与学习统计相关的请求
 */
const { User, LearningActivity, DailyRecord, LearningPlan, sequelize } = require('../models');
const apiResponse = require('../utils/apiResponse');
const logger = require('../config/logger');
const statisticsService = require('../services/statistics.service');

${content}`;
        
        fs.writeFileSync(controllerPath, controllerContent, 'utf8');
        logger.info('已创建新的 statistics.controller.js 文件');
      }
    } else {
      logger.info('未发现多个统计相关文件，无需合并');
    }
    
    logger.info('统计模块重构完成');
  } catch (error) {
    logger.error(`重构统计模块时出错: ${error.message}`);
    logger.error(error.stack);
  }
}

/**
 * 创建通用的错误处理和日志记录函数
 */
async function createCommonErrorHandler() {
  try {
    logger.info('开始创建通用的错误处理和日志记录函数...');
    
    const errorHandlerPath = path.join(__dirname, '../utils/errorHandler.js');
    
    // 检查是否已存在
    if (fs.existsSync(errorHandlerPath)) {
      logger.info('errorHandler.js 文件已存在，检查是否需要更新...');
      
      // 读取现有文件
      const content = fs.readFileSync(errorHandlerPath, 'utf8');
      
      // 检查是否包含所有必要的函数
      const hasHandleError = content.includes('handleError');
      const hasHandleApiError = content.includes('handleApiError');
      const hasHandleValidationError = content.includes('handleValidationError');
      
      if (hasHandleError && hasHandleApiError && hasHandleValidationError) {
        logger.info('errorHandler.js 文件已包含所有必要的函数，无需更新');
      } else {
        // 更新文件
        logger.info('更新 errorHandler.js 文件...');
        
        const newContent = `/**
 * 错误处理工具
 * 提供统一的错误处理和日志记录函数
 */
const logger = require('../config/logger');
const apiResponse = require('./apiResponse');

/**
 * 处理通用错误
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 * @returns {Object} 格式化的错误对象
 */
const handleError = (error, context = '') => {
  const errorContext = context ? \`[\${context}] \` : '';
  
  // 记录错误
  logger.error(\`\${errorContext}\${error.message}\`);
  
  if (error.stack) {
    logger.debug(error.stack);
  }
  
  // 返回格式化的错误对象
  return {
    message: error.message,
    code: error.code || 'INTERNAL_ERROR',
    status: error.status || 500
  };
};

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {Object} res - Express响应对象
 * @param {string} context - 错误上下文
 * @returns {Object} Express响应
 */
const handleApiError = (error, res, context = '') => {
  const formattedError = handleError(error, context);
  
  return apiResponse.error(
    res,
    formattedError.message,
    formattedError.code,
    formattedError.status
  );
};

/**
 * 处理验证错误
 * @param {Array} errors - 验证错误数组
 * @param {Object} res - Express响应对象
 * @returns {Object} Express响应
 */
const handleValidationError = (errors, res) => {
  const errorMessages = errors.map(err => err.msg).join(', ');
  logger.warn(\`验证错误: \${errorMessages}\`);
  
  return apiResponse.badRequest(res, errorMessages);
};

/**
 * 处理数据库错误
 * @param {Error} error - 数据库错误对象
 * @param {Object} res - Express响应对象
 * @param {string} context - 错误上下文
 * @returns {Object} Express响应
 */
const handleDatabaseError = (error, res, context = '') => {
  let message = '数据库操作失败';
  let code = 'DATABASE_ERROR';
  
  // 处理特定类型的数据库错误
  if (error.name === 'SequelizeUniqueConstraintError') {
    message = '数据已存在，无法创建重复记录';
    code = 'DUPLICATE_RECORD';
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    message = '关联数据不存在或已被删除';
    code = 'FOREIGN_KEY_ERROR';
  } else if (error.name === 'SequelizeValidationError') {
    message = '数据验证失败: ' + error.message;
    code = 'VALIDATION_ERROR';
  }
  
  // 记录详细错误
  logger.error(\`[\${context}] 数据库错误: \${error.message}\`);
  logger.debug(error.stack);
  
  return apiResponse.error(res, message, code, 500);
};

module.exports = {
  handleError,
  handleApiError,
  handleValidationError,
  handleDatabaseError
};
`;
        
        fs.writeFileSync(errorHandlerPath, newContent, 'utf8');
        logger.info('已更新 errorHandler.js 文件');
      }
    } else {
      // 创建新文件
      logger.info('创建新的 errorHandler.js 文件...');
      
      const content = `/**
 * 错误处理工具
 * 提供统一的错误处理和日志记录函数
 */
const logger = require('../config/logger');
const apiResponse = require('./apiResponse');

/**
 * 处理通用错误
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 * @returns {Object} 格式化的错误对象
 */
const handleError = (error, context = '') => {
  const errorContext = context ? \`[\${context}] \` : '';
  
  // 记录错误
  logger.error(\`\${errorContext}\${error.message}\`);
  
  if (error.stack) {
    logger.debug(error.stack);
  }
  
  // 返回格式化的错误对象
  return {
    message: error.message,
    code: error.code || 'INTERNAL_ERROR',
    status: error.status || 500
  };
};

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {Object} res - Express响应对象
 * @param {string} context - 错误上下文
 * @returns {Object} Express响应
 */
const handleApiError = (error, res, context = '') => {
  const formattedError = handleError(error, context);
  
  return apiResponse.error(
    res,
    formattedError.message,
    formattedError.code,
    formattedError.status
  );
};

/**
 * 处理验证错误
 * @param {Array} errors - 验证错误数组
 * @param {Object} res - Express响应对象
 * @returns {Object} Express响应
 */
const handleValidationError = (errors, res) => {
  const errorMessages = errors.map(err => err.msg).join(', ');
  logger.warn(\`验证错误: \${errorMessages}\`);
  
  return apiResponse.badRequest(res, errorMessages);
};

/**
 * 处理数据库错误
 * @param {Error} error - 数据库错误对象
 * @param {Object} res - Express响应对象
 * @param {string} context - 错误上下文
 * @returns {Object} Express响应
 */
const handleDatabaseError = (error, res, context = '') => {
  let message = '数据库操作失败';
  let code = 'DATABASE_ERROR';
  
  // 处理特定类型的数据库错误
  if (error.name === 'SequelizeUniqueConstraintError') {
    message = '数据已存在，无法创建重复记录';
    code = 'DUPLICATE_RECORD';
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    message = '关联数据不存在或已被删除';
    code = 'FOREIGN_KEY_ERROR';
  } else if (error.name === 'SequelizeValidationError') {
    message = '数据验证失败: ' + error.message;
    code = 'VALIDATION_ERROR';
  }
  
  // 记录详细错误
  logger.error(\`[\${context}] 数据库错误: \${error.message}\`);
  logger.debug(error.stack);
  
  return apiResponse.error(res, message, code, 500);
};

module.exports = {
  handleError,
  handleApiError,
  handleValidationError,
  handleDatabaseError
};
`;
      
      fs.writeFileSync(errorHandlerPath, content, 'utf8');
      logger.info('已创建 errorHandler.js 文件');
    }
    
    logger.info('通用错误处理和日志记录函数创建完成');
  } catch (error) {
    logger.error(`创建通用错误处理函数时出错: ${error.message}`);
    logger.error(error.stack);
  }
}

/**
 * 清理临时文件和测试文件
 */
async function cleanupTemporaryFiles() {
  try {
    logger.info('开始清理临时文件和测试文件...');
    
    // 需要清理的文件模式
    const patterns = [
      '*.tmp',
      '*.bak',
      'test-*.js',
      'temp-*.js'
    ];
    
    // 需要排除的重要测试文件
    const excludes = [
      'test-ai-service.js',
      'test-ai-models.js',
      'test-system-default-tags.js'
    ];
    
    // 查找根目录下的临时文件
    const rootDir = path.join(__dirname, '../..');
    const rootFiles = fs.readdirSync(rootDir);
    
    // 创建备份目录
    const backupDir = path.join(rootDir, 'backup');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }
    
    // 匹配并备份文件
    let matchedFiles = [];
    for (const pattern of patterns) {
      const regex = new RegExp(pattern.replace('*', '.*'));
      const matches = rootFiles.filter(file => 
        regex.test(file) && 
        !excludes.includes(file) && 
        fs.statSync(path.join(rootDir, file)).isFile()
      );
      matchedFiles = [...matchedFiles, ...matches];
    }
    
    // 去重
    matchedFiles = [...new Set(matchedFiles)];
    
    if (matchedFiles.length > 0) {
      logger.info(`找到 ${matchedFiles.length} 个临时文件或测试文件`);
      
      // 备份并移动文件
      for (const file of matchedFiles) {
        const filePath = path.join(rootDir, file);
        const backupPath = path.join(backupDir, file);
        
        // 复制到备份目录
        fs.copyFileSync(filePath, backupPath);
        logger.info(`已备份文件 ${file} 到 backup 目录`);
        
        // 移动到备份目录（相当于从原位置删除）
        fs.renameSync(filePath, backupPath);
        logger.info(`已移动文件 ${file} 到 backup 目录`);
      }
    } else {
      logger.info('未找到需要清理的临时文件或测试文件');
    }
    
    logger.info('临时文件和测试文件清理完成');
  } catch (error) {
    logger.error(`清理临时文件时出错: ${error.message}`);
    logger.error(error.stack);
  }
}

/**
 * 主函数
 */
async function improveCodeQuality() {
  try {
    logger.info('开始提升代码质量...');
    
    // 重构统计模块中的重复代码
    await refactorStatisticsModule();
    
    // 创建通用的错误处理和日志记录函数
    await createCommonErrorHandler();
    
    // 清理临时文件和测试文件
    await cleanupTemporaryFiles();
    
    logger.info('代码质量提升完成');
    return true;
  } catch (error) {
    logger.error(`代码质量提升失败: ${error.message}`);
    logger.error(error.stack);
    return false;
  }
}

// 如果直接运行此脚本，执行提升
if (require.main === module) {
  improveCodeQuality()
    .then(success => {
      if (success) {
        console.log('代码质量提升成功');
        process.exit(0);
      } else {
        console.error('代码质量提升失败');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(`执行脚本时出错: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { improveCodeQuality };
