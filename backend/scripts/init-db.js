/**
 * 数据库初始化脚本
 * 用于创建数据库表结构
 */
const { sequelize, testConnection } = require('./db-connection');
const logger = require('../config/logger');

// 导入所有模型
const models = require('../models');

async function initDatabase() {
  try {
    // 测试数据库连接
    const connected = await testConnection();
    if (!connected) {
      throw new Error('无法连接到数据库');
    }

    logger.info('开始同步数据库表结构...');

    // 同步所有模型到数据库
    await sequelize.sync({ alter: true });

    logger.info('数据库表结构同步完成');
    return true;
  } catch (error) {
    logger.error(`数据库初始化失败: ${error.message}`);
    return false;
  }
}

// 如果直接运行此脚本，执行初始化
if (require.main === module) {
  initDatabase()
    .then(success => {
      if (success) {
        console.log('数据库初始化成功');
        process.exit(0);
      } else {
        console.error('数据库初始化失败');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(`数据库初始化出错: ${error.message}`);
      process.exit(1);
    });
}

module.exports = initDatabase;
