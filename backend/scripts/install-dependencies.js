#!/usr/bin/env node

/**
 * 依赖安装和配置脚本
 * 自动安装缺失的依赖包并进行基础配置
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始安装AIBUBB项目依赖...\n');

// 需要安装的依赖包
const requiredDependencies = [
  'sequelize',
  'mysql2', 
  'redis',
  'jsonwebtoken',
  'bcryptjs',
  'express-rate-limit',
  'helmet',
  'cors',
  'compression',
  'express-validator',
  'winston',
  'winston-daily-rotate-file',
  'dotenv',
  'joi',
  'moment',
  'uuid',
  'multer',
  'express-fileupload'
];

const devDependencies = [
  'jest',
  'supertest',
  'nodemon',
  'eslint',
  'prettier',
  'husky',
  'lint-staged',
  '@types/node',
  'cross-env'
];

/**
 * 执行命令并显示输出
 */
function executeCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✅ ${description} 完成\n`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} 失败:`, error.message);
    return false;
  }
}

/**
 * 检查package.json是否存在
 */
function checkPackageJson() {
  const packagePath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.log('📄 创建package.json文件...');
    executeCommand('npm init -y', '初始化package.json');
  }
}

/**
 * 安装生产依赖
 */
function installProductionDependencies() {
  console.log('📦 安装生产环境依赖包...');
  const command = `npm install ${requiredDependencies.join(' ')}`;
  return executeCommand(command, '安装生产依赖');
}

/**
 * 安装开发依赖
 */
function installDevDependencies() {
  console.log('📦 安装开发环境依赖包...');
  const command = `npm install --save-dev ${devDependencies.join(' ')}`;
  return executeCommand(command, '安装开发依赖');
}

/**
 * 更新package.json脚本
 */
function updatePackageScripts() {
  console.log('📝 更新package.json脚本...');
  
  const packagePath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // 更新scripts
  packageJson.scripts = {
    ...packageJson.scripts,
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "cross-env NODE_ENV=test jest",
    "test:unit": "cross-env NODE_ENV=test jest __tests__/unit",
    "test:integration": "cross-env NODE_ENV=test jest __tests__/integration",
    "test:coverage": "cross-env NODE_ENV=test jest --coverage",
    "test:watch": "cross-env NODE_ENV=test jest --watch",
    "lint": "eslint . --ext .js,.ts",
    "lint:fix": "eslint . --ext .js,.ts --fix",
    "format": "prettier --write .",
    "prepare": "husky install",
    "migration:run": "node migrations/run-migrations.js",
    "migration:rollback": "node migrations/rollback-migrations.js",
    "seed:run": "node seeders/run-seeders.js",
    "db:reset": "npm run migration:rollback && npm run migration:run && npm run seed:run",
    "security:audit": "npm audit",
    "security:fix": "npm audit fix",
    "performance:baseline": "node scripts/performance-baseline.js",
    "docs:sync": "node scripts/api-docs-sync.js",
    "deployment:check": "node scripts/production-deployment-check.js",
    "monitoring:start": "node infrastructure/monitoring/ProductionMonitoringService.js",
    "coverage:check": "node scripts/test-coverage-check.js"
  };
  
  // 添加Jest配置
  packageJson.jest = {
    testEnvironment: "node",
    collectCoverageFrom: [
      "**/*.{js,ts}",
      "!**/node_modules/**",
      "!**/coverage/**",
      "!**/dist/**",
      "!**/*.config.js",
      "!**/migrations/**",
      "!**/seeders/**"
    ],
    coverageThreshold: {
      global: {
        branches: 80,
        functions: 80,
        lines: 80,
        statements: 80
      }
    },
    testMatch: [
      "**/__tests__/**/*.test.js",
      "**/?(*.)+(spec|test).js"
    ]
  };
  
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json脚本更新完成\n');
}

/**
 * 创建必要的目录结构
 */
function createDirectories() {
  console.log('📁 创建必要的目录结构...');
  
  const directories = [
    'logs',
    'uploads',
    'temp',
    'backup',
    'coverage',
    'docs/api',
    'monitoring/config',
    'nginx/conf.d',
    'nginx/ssl',
    'mysql/init',
    'mysql/conf',
    'redis'
  ];
  
  directories.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`  ✅ 创建目录: ${dir}`);
    }
  });
  
  console.log('✅ 目录结构创建完成\n');
}

/**
 * 创建基础配置文件
 */
function createConfigFiles() {
  console.log('⚙️ 创建基础配置文件...');
  
  // 创建.gitignore
  const gitignoreContent = `
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Uploads and temp files
uploads/
temp/
backup/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build outputs
dist/
build/

# Database
*.sqlite
*.db
`;
  
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  if (!fs.existsSync(gitignorePath)) {
    fs.writeFileSync(gitignorePath, gitignoreContent.trim());
    console.log('  ✅ 创建.gitignore文件');
  }
  
  // 创建.env.example
  const envExampleContent = `
# AIBUBB 环境配置示例
NODE_ENV=development
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=aibubb
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# Redis配置
REDIS_URL=redis://localhost:6379

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# 监控配置
MONITORING_ENABLED=true
HEALTH_CHECK_ENABLED=true
`;
  
  const envExamplePath = path.join(process.cwd(), '.env.example');
  if (!fs.existsSync(envExamplePath)) {
    fs.writeFileSync(envExamplePath, envExampleContent.trim());
    console.log('  ✅ 创建.env.example文件');
  }
  
  console.log('✅ 配置文件创建完成\n');
}

/**
 * 运行安全审计
 */
function runSecurityAudit() {
  console.log('🔒 运行安全审计...');
  executeCommand('npm audit', '安全审计检查');
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🎯 AIBUBB项目依赖安装脚本');
    console.log('================================\n');
    
    // 1. 检查package.json
    checkPackageJson();
    
    // 2. 安装生产依赖
    if (!installProductionDependencies()) {
      console.error('❌ 生产依赖安装失败，请检查网络连接和npm配置');
      process.exit(1);
    }
    
    // 3. 安装开发依赖
    if (!installDevDependencies()) {
      console.error('❌ 开发依赖安装失败，请检查网络连接和npm配置');
      process.exit(1);
    }
    
    // 4. 更新package.json脚本
    updatePackageScripts();
    
    // 5. 创建目录结构
    createDirectories();
    
    // 6. 创建配置文件
    createConfigFiles();
    
    // 7. 运行安全审计
    runSecurityAudit();
    
    console.log('🎉 依赖安装完成！');
    console.log('================================');
    console.log('');
    console.log('📋 下一步操作：');
    console.log('1. 复制 .env.example 到 .env 并配置环境变量');
    console.log('2. 配置数据库连接信息');
    console.log('3. 运行 npm run migration:run 初始化数据库');
    console.log('4. 运行 npm run dev 启动开发服务器');
    console.log('5. 运行 npm run test 执行测试');
    console.log('6. 运行 npm run deployment:check 检查生产部署就绪状态');
    console.log('');
    console.log('🔧 可用的npm脚本：');
    console.log('- npm run dev          # 启动开发服务器');
    console.log('- npm run test         # 运行所有测试');
    console.log('- npm run test:coverage # 运行测试并生成覆盖率报告');
    console.log('- npm run lint         # 代码检查');
    console.log('- npm run docs:sync    # 同步API文档');
    console.log('- npm run deployment:check # 生产部署检查');
    
  } catch (error) {
    console.error('❌ 安装过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  main,
  requiredDependencies,
  devDependencies
};