#!/usr/bin/env node

/**
 * 环境变量迁移工具
 * 用于将现有的环境变量迁移到新的格式
 * 
 * 使用方法：
 * node backend/scripts/migrate-env-vars.js [--env=production] [--output=.env.new] [--force]
 */

const path = require('path');
const fs = require('fs');
const chalk = require('chalk');
const dotenv = require('dotenv');
const { program } = require('commander');

// 定义命令行选项
program
  .option('--env <environment>', '指定环境', process.env.NODE_ENV || 'development')
  .option('--output <file>', '输出文件', '.env.new')
  .option('--force', '强制覆盖输出文件', false)
  .option('--encrypt-secrets', '加密敏感信息', false)
  .parse(process.argv);

const options = program.opts();

// 定义环境变量模板文件
const ENV_TEMPLATES = {
  development: '.env.development.example',
  production: '.env.production.example',
  test: '.env.test.example',
  docker: '.env.docker.example'
};

// 定义敏感信息变量
const SENSITIVE_VARS = [
  'DB_PASSWORD',
  'REDIS_PASSWORD',
  'JWT_SECRET',
  'WECHAT_APP_ID',
  'WECHAT_APP_SECRET',
  'ARK_API_KEY',
  'DASHSCOPE_API_KEY',
  'HUNYUAN_API_KEY'
];

// 定义环境变量映射（旧名称 -> 新名称）
const ENV_VAR_MAPPING = {
  // 服务器配置
  'SERVER_PORT': 'PORT',
  'API_VERSION': 'API_PREFIX',
  
  // 数据库配置
  'DATABASE_HOST': 'DB_HOST',
  'DATABASE_PORT': 'DB_PORT',
  'DATABASE_NAME': 'DB_NAME',
  'DATABASE_USER': 'DB_USER',
  'DATABASE_PASSWORD': 'DB_PASSWORD',
  
  // Redis配置
  'REDIS_HOST': 'REDIS_URL',
  
  // JWT配置
  'JWT_TOKEN_SECRET': 'JWT_SECRET',
  'JWT_TOKEN_EXPIRES': 'JWT_EXPIRES_IN',
  'JWT_REFRESH_TOKEN_EXPIRES': 'JWT_REFRESH_EXPIRES_IN',
  
  // 微信小程序配置
  'WECHAT_MINI_APP_ID': 'WECHAT_APP_ID',
  'WECHAT_MINI_APP_SECRET': 'WECHAT_APP_SECRET',
  
  // AI配置
  'AI_MODEL_PROVIDER': 'AI_PROVIDER',
  'BYTEDANCE_API_KEY': 'ARK_API_KEY',
  'BYTEDANCE_MODEL': 'ARK_API_MODEL',
  'ALIYUN_API_KEY': 'DASHSCOPE_API_KEY',
  'ALIYUN_MODEL': 'DASHSCOPE_API_MODEL',
  'TENCENT_API_KEY': 'HUNYUAN_API_KEY',
  'TENCENT_MODEL': 'HUNYUAN_API_MODEL'
};

/**
 * 主函数
 */
async function main() {
  console.log(chalk.green('===== 环境变量迁移工具 ====='));
  console.log(chalk.green(`环境: ${options.env}`));
  console.log(chalk.green(`输出文件: ${options.output}`));

  try {
    // 检查输出文件是否存在
    const outputPath = path.resolve(process.cwd(), options.output);
    if (fs.existsSync(outputPath) && !options.force) {
      console.log(chalk.red(`✗ 输出文件已存在: ${options.output}`));
      console.log(chalk.yellow('提示: 使用 --force 选项覆盖输出文件'));
      process.exit(1);
    }

    // 获取环境变量模板文件
    const templateFile = ENV_TEMPLATES[options.env] || '.env.example';
    const templatePath = path.resolve(process.cwd(), templateFile);

    if (!fs.existsSync(templatePath)) {
      console.log(chalk.red(`✗ 未找到环境变量模板文件: ${templateFile}`));
      process.exit(1);
    }

    // 读取模板文件
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    const templateVars = {};
    const templateComments = {};
    let currentComment = '';

    // 解析模板文件
    templateContent.split('\n').forEach(line => {
      if (line.trim().startsWith('#')) {
        currentComment = line;
      } else if (line.trim()) {
        const match = line.match(/^([^=]+)=(.*)$/);
        if (match) {
          templateVars[match[1]] = match[2];
          templateComments[match[1]] = currentComment;
          currentComment = '';
        }
      } else {
        currentComment = '';
      }
    });

    // 获取当前环境变量
    const envFiles = [
      '.env',
      `.env.${options.env}`,
      `.env.${options.env}.local`,
      '.env.local'
    ];

    const currentVars = {};

    // 读取环境变量文件
    for (const envFile of envFiles) {
      const envPath = path.resolve(process.cwd(), envFile);
      if (fs.existsSync(envPath)) {
        console.log(chalk.green(`读取环境变量文件: ${envFile}`));
        const envConfig = dotenv.parse(fs.readFileSync(envPath));
        
        // 合并环境变量
        Object.assign(currentVars, envConfig);
      }
    }

    // 迁移环境变量
    const migratedVars = {};
    const migratedCount = {
      unchanged: 0,
      renamed: 0,
      added: 0,
      sensitive: 0
    };

    // 处理当前环境变量
    for (const [key, value] of Object.entries(currentVars)) {
      // 检查是否需要重命名
      const newKey = ENV_VAR_MAPPING[key] || key;
      
      // 如果是敏感信息且需要加密
      if (options.encryptSecrets && SENSITIVE_VARS.includes(newKey)) {
        migratedVars[newKey] = '******'; // 使用占位符，后续会通过环境配置管理器加密
        migratedCount.sensitive++;
      } else {
        migratedVars[newKey] = value;
        
        if (newKey !== key) {
          migratedCount.renamed++;
        } else {
          migratedCount.unchanged++;
        }
      }
    }

    // 添加模板中有但当前环境变量中没有的变量
    for (const [key, value] of Object.entries(templateVars)) {
      if (!migratedVars[key]) {
        migratedVars[key] = value;
        migratedCount.added++;
      }
    }

    // 生成新的环境变量文件内容
    let outputContent = `# 环境变量配置文件\n# 环境: ${options.env}\n# 生成时间: ${new Date().toISOString()}\n\n`;

    // 按照模板的顺序添加环境变量
    let currentSection = '';
    templateContent.split('\n').forEach(line => {
      if (line.trim().startsWith('#')) {
        // 如果是注释行，检查是否是新的部分
        if (line.includes('配置')) {
          currentSection = line;
          outputContent += `\n${line}\n`;
        }
      } else if (line.trim()) {
        const match = line.match(/^([^=]+)=(.*)$/);
        if (match) {
          const key = match[1];
          if (migratedVars[key] !== undefined) {
            outputContent += `${key}=${migratedVars[key]}\n`;
            delete migratedVars[key]; // 从已处理的变量中删除
          }
        }
      }
    });

    // 添加剩余的环境变量
    if (Object.keys(migratedVars).length > 0) {
      outputContent += '\n# 其他配置\n';
      for (const [key, value] of Object.entries(migratedVars)) {
        outputContent += `${key}=${value}\n`;
      }
    }

    // 保存新的环境变量文件
    fs.writeFileSync(outputPath, outputContent, 'utf8');

    console.log(chalk.green(`\n✓ 环境变量迁移完成，已保存到 ${options.output}`));
    console.log(chalk.green(`  - 保持不变: ${migratedCount.unchanged}`));
    console.log(chalk.green(`  - 重命名: ${migratedCount.renamed}`));
    console.log(chalk.green(`  - 新增: ${migratedCount.added}`));
    
    if (options.encryptSecrets) {
      console.log(chalk.green(`  - 敏感信息(需加密): ${migratedCount.sensitive}`));
      console.log(chalk.yellow('\n提示: 敏感信息已使用占位符替换，请使用环境配置管理器的set方法设置敏感信息'));
    }

    // 提示下一步操作
    console.log(chalk.yellow('\n下一步操作:'));
    console.log(chalk.yellow('1. 检查新的环境变量文件，确保所有值都正确'));
    console.log(chalk.yellow('2. 使用以下命令验证新的环境变量文件:'));
    console.log(chalk.yellow(`   node backend/scripts/validate-config.js --env=${options.env} --verbose`));
    console.log(chalk.yellow('3. 如果验证通过，将新的环境变量文件重命名为.env:'));
    console.log(chalk.yellow(`   mv ${options.output} .env`));
  } catch (error) {
    console.error(chalk.red(`\n✗ 迁移过程中发生错误: ${error.message}`));
    process.exit(1);
  }
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red(`\n✗ 执行过程中发生错误: ${error.message}`));
  process.exit(1);
});
