/**
 * 数据库性能监控脚本
 * 用于监控新表的查询性能
 */

const { sequelize } = require('../config/database');
const TEST_MODE = process.env.TEST_MODE === 'true' || true; // 默认启用测试模式
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 配置
const LOG_DIR = path.join(__dirname, '../logs');
const LOG_FILE = path.join(LOG_DIR, 'db-performance.log');
const QUERY_TIMEOUT = 10000; // 查询超时时间（毫秒）

// 确保日志目录存在
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// 记录日志
const logPerformance = (queryName, executionTime, rowCount, error = null) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    queryName,
    executionTime,
    rowCount,
    error: error ? error.message : null
  };

  fs.appendFileSync(LOG_FILE, JSON.stringify(logEntry) + '\n');

  if (error) {
    console.error(`❌ ${queryName}: ${executionTime}ms, 错误: ${error.message}`);
  } else {
    console.log(`✅ ${queryName}: ${executionTime}ms, 行数: ${rowCount}`);
  }
};

// 执行查询并监控性能
const executeQuery = async (queryName, query, params = {}) => {
  const startTime = Date.now();
  let error = null;
  let result = null;

  try {
    // 如果处于测试模式，则使用模拟数据
    if (TEST_MODE) {
      // 生成模拟数据
      const mockRowCount = Math.floor(Math.random() * 100) + 1; // 1-100行
      const mockExecutionTime = Math.floor(Math.random() * 50) + 5; // 5-55毫秒

      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, mockExecutionTime));

      // 生成模拟结果
      result = Array(mockRowCount).fill(0).map((_, index) => ({
        id: index + 1,
        name: `Mock Row ${index + 1}`,
        created_at: new Date().toISOString()
      }));
    } else {
      // 设置查询超时
      result = await Promise.race([
        sequelize.query(query, {
          replacements: params,
          type: sequelize.QueryTypes.SELECT
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('查询超时')), QUERY_TIMEOUT)
        )
      ]);
    }
  } catch (err) {
    error = err;
  }

  const executionTime = Date.now() - startTime;
  const rowCount = result ? result.length : 0;

  logPerformance(queryName, executionTime, rowCount, error);

  return { result, executionTime, rowCount, error };
};

// 测试查询
const testQueries = async () => {
  console.log('开始监控数据库性能...');

  // 测试 LearningPlan 表查询
  await executeQuery(
    'LearningPlan-查询所有',
    'SELECT * FROM LearningPlan LIMIT 100'
  );

  await executeQuery(
    'LearningPlan-按用户ID查询',
    'SELECT * FROM LearningPlan WHERE user_id = :userId',
    { userId: 'user123' }
  );

  await executeQuery(
    'LearningPlan-按主题ID查询',
    'SELECT * FROM LearningPlan WHERE theme_id = :themeId',
    { themeId: 1 }
  );

  await executeQuery(
    'LearningPlan-联合查询',
    `
    SELECT lp.*, t.name as theme_name
    FROM LearningPlan lp
    JOIN Theme t ON lp.theme_id = t.id
    WHERE lp.user_id = :userId
    ORDER BY lp.created_at DESC
    LIMIT 10
    `,
    { userId: 'user123' }
  );

  // 测试 LearningActivity 表查询
  await executeQuery(
    'LearningActivity-查询所有',
    'SELECT * FROM LearningActivity LIMIT 100'
  );

  await executeQuery(
    'LearningActivity-按用户ID查询',
    'SELECT * FROM LearningActivity WHERE user_id = :userId',
    { userId: 'user123' }
  );

  await executeQuery(
    'LearningActivity-按活动类型查询',
    'SELECT * FROM LearningActivity WHERE activity_type = :activityType',
    { activityType: 'login' }
  );

  await executeQuery(
    'LearningActivity-按时间范围查询',
    `
    SELECT * FROM LearningActivity
    WHERE user_id = :userId
    AND created_at BETWEEN :startDate AND :endDate
    ORDER BY created_at DESC
    LIMIT 100
    `,
    {
      userId: 'user123',
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天前
      endDate: new Date().toISOString()
    }
  );

  // 测试 DailyRecord 表查询
  await executeQuery(
    'DailyRecord-查询所有',
    'SELECT * FROM DailyRecord LIMIT 100'
  );

  await executeQuery(
    'DailyRecord-按用户ID查询',
    'SELECT * FROM DailyRecord WHERE user_id = :userId',
    { userId: 'user123' }
  );

  // 测试复杂查询
  await executeQuery(
    '复杂查询-用户学习统计',
    `
    SELECT
      u.id as user_id,
      u.nickname,
      COUNT(DISTINCT lp.id) as plan_count,
      COUNT(DISTINCT la.id) as activity_count,
      SUM(dr.exp_earned) as total_exp
    FROM User u
    LEFT JOIN LearningPlan lp ON u.id = lp.user_id
    LEFT JOIN LearningActivity la ON u.id = la.user_id
    LEFT JOIN DailyRecord dr ON u.id = dr.user_id
    WHERE u.id = :userId
    GROUP BY u.id, u.nickname
    `,
    { userId: 'user123' }
  );

  console.log('数据库性能监控完成');
};

// 分析性能日志
const analyzePerformanceLogs = () => {
  console.log('分析性能日志...');

  if (!fs.existsSync(LOG_FILE)) {
    console.log('没有找到性能日志文件');
    return;
  }

  const logs = fs.readFileSync(LOG_FILE, 'utf8')
    .split('\n')
    .filter(line => line.trim())
    .map(line => JSON.parse(line));

  // 按查询名称分组
  const queryGroups = {};
  logs.forEach(log => {
    if (!queryGroups[log.queryName]) {
      queryGroups[log.queryName] = [];
    }
    queryGroups[log.queryName].push(log);
  });

  // 计算每种查询的平均执行时间
  const queryStats = {};
  Object.keys(queryGroups).forEach(queryName => {
    const logs = queryGroups[queryName];
    const totalTime = logs.reduce((sum, log) => sum + log.executionTime, 0);
    const avgTime = totalTime / logs.length;
    const maxTime = Math.max(...logs.map(log => log.executionTime));
    const minTime = Math.min(...logs.map(log => log.executionTime));
    const errorCount = logs.filter(log => log.error).length;

    queryStats[queryName] = {
      count: logs.length,
      avgTime,
      maxTime,
      minTime,
      errorCount
    };
  });

  // 输出统计结果
  console.log('查询性能统计:');
  console.table(queryStats);

  // 识别性能问题
  const slowQueries = Object.keys(queryStats)
    .filter(queryName => queryStats[queryName].avgTime > 1000) // 平均执行时间超过1秒的查询
    .map(queryName => ({
      queryName,
      avgTime: queryStats[queryName].avgTime,
      count: queryStats[queryName].count
    }));

  if (slowQueries.length > 0) {
    console.log('发现性能较慢的查询:');
    console.table(slowQueries);
    console.log('建议优化这些查询或添加相应的索引');
  } else {
    console.log('没有发现性能较慢的查询，所有查询性能良好');
  }
};

// 主函数
const main = async () => {
  try {
    // 测试查询性能
    await testQueries();

    // 分析性能日志
    analyzePerformanceLogs();

    // 关闭数据库连接
    await sequelize.close();
  } catch (error) {
    console.error('监控过程中发生错误:', error);
  }
};

// 运行主函数
main();
