/**
 * 性能基线建立脚本
 * 用于建立API性能基线并生成性能报告
 */

const k6 = require('k6');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 性能基线配置
const PERFORMANCE_BASELINES = {
  // API响应时间基线 (毫秒)
  responseTime: {
    p50: 100, // 50%请求在100ms内完成
    p90: 200, // 90%请求在200ms内完成
    p95: 300, // 95%请求在300ms内完成
    p99: 500, // 99%请求在500ms内完成
  },

  // 吞吐量基线 (请求/秒)
  throughput: {
    minimum: 100, // 最低100 RPS
    target: 500, // 目标500 RPS
    maximum: 1000, // 最大1000 RPS
  },

  // 错误率基线
  errorRate: {
    maximum: 0.01, // 最大1%错误率
  },

  // 资源使用基线
  resources: {
    cpu: 80, // CPU使用率不超过80%
    memory: 85, // 内存使用率不超过85%
    connections: 1000, // 最大并发连接数
  },
};

// 测试场景配置
const TEST_SCENARIOS = [
  {
    name: 'smoke_test',
    description: '冒烟测试 - 基本功能验证',
    vus: 1,
    duration: '30s',
    thresholds: {
      http_req_duration: ['p(95)<500'],
      http_req_failed: ['rate<0.01'],
    },
  },
  {
    name: 'load_test',
    description: '负载测试 - 正常负载下的性能',
    stages: [
      { duration: '2m', target: 10 },
      { duration: '5m', target: 10 },
      { duration: '2m', target: 0 },
    ],
    thresholds: {
      http_req_duration: ['p(95)<300'],
      http_req_failed: ['rate<0.01'],
    },
  },
  {
    name: 'stress_test',
    description: '压力测试 - 高负载下的性能',
    stages: [
      { duration: '2m', target: 50 },
      { duration: '5m', target: 50 },
      { duration: '2m', target: 100 },
      { duration: '5m', target: 100 },
      { duration: '2m', target: 0 },
    ],
    thresholds: {
      http_req_duration: ['p(95)<500'],
      http_req_failed: ['rate<0.05'],
    },
  },
  {
    name: 'spike_test',
    description: '峰值测试 - 突发流量处理',
    stages: [
      { duration: '1m', target: 10 },
      { duration: '30s', target: 200 },
      { duration: '1m', target: 10 },
    ],
    thresholds: {
      http_req_duration: ['p(95)<1000'],
      http_req_failed: ['rate<0.1'],
    },
  },
];

/**
 * 生成K6测试脚本
 */
function generateK6Script(scenario) {
  const script = `
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// 自定义指标
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');

// 测试配置
export let options = ${JSON.stringify(
    {
      scenarios: {
        [scenario.name]: {
          executor: scenario.stages ? 'ramping-vus' : 'constant-vus',
          ...(scenario.stages
            ? { stages: scenario.stages }
            : { vus: scenario.vus, duration: scenario.duration }),
        },
      },
      thresholds: scenario.thresholds,
    },
    null,
    2,
  )};

// 测试数据
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';

// API端点配置
const endpoints = [
  {
    name: 'health_check',
    method: 'GET',
    url: '/api/health',
    weight: 10
  },
  {
    name: 'get_learning_plans',
    method: 'GET',
    url: '/api/v2/learning-plans',
    headers: { 'Authorization': \`Bearer \${AUTH_TOKEN}\` },
    weight: 30
  },
  {
    name: 'get_plan_detail',
    method: 'GET',
    url: '/api/v2/learning-plans/1',
    headers: { 'Authorization': \`Bearer \${AUTH_TOKEN}\` },
    weight: 20
  },
  {
    name: 'create_learning_plan',
    method: 'POST',
    url: '/api/v2/learning-plans',
    headers: {
      'Authorization': \`Bearer \${AUTH_TOKEN}\`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      themeId: 1,
      title: 'Performance Test Plan',
      description: 'Created during performance testing',
      targetDays: 7
    }),
    weight: 15
  },
  {
    name: 'get_statistics',
    method: 'GET',
    url: '/api/v2/statistics/overview',
    headers: { 'Authorization': \`Bearer \${AUTH_TOKEN}\` },
    weight: 15
  },
  {
    name: 'get_exercises',
    method: 'GET',
    url: '/api/v2/exercises',
    headers: { 'Authorization': \`Bearer \${AUTH_TOKEN}\` },
    weight: 10
  }
];

// 权重选择函数
function selectEndpoint() {
  const totalWeight = endpoints.reduce((sum, ep) => sum + ep.weight, 0);
  let random = Math.random() * totalWeight;

  for (const endpoint of endpoints) {
    random -= endpoint.weight;
    if (random <= 0) {
      return endpoint;
    }
  }

  return endpoints[0];
}

export default function() {
  const endpoint = selectEndpoint();
  const startTime = Date.now();

  const params = {
    headers: endpoint.headers || {},
    timeout: '30s'
  };

  let response;

  switch (endpoint.method) {
    case 'GET':
      response = http.get(\`\${BASE_URL}\${endpoint.url}\`, params);
      break;
    case 'POST':
      response = http.post(\`\${BASE_URL}\${endpoint.url}\`, endpoint.body, params);
      break;
    case 'PUT':
      response = http.put(\`\${BASE_URL}\${endpoint.url}\`, endpoint.body, params);
      break;
    case 'DELETE':
      response = http.del(\`\${BASE_URL}\${endpoint.url}\`, null, params);
      break;
    default:
      response = http.get(\`\${BASE_URL}\${endpoint.url}\`, params);
  }

  const duration = Date.now() - startTime;

  // 记录指标
  responseTime.add(duration);

  // 检查响应
  const success = check(response, {
    [\`\${endpoint.name}: status is 200-299\`]: (r) => r.status >= 200 && r.status < 300,
    [\`\${endpoint.name}: response time < 1000ms\`]: (r) => r.timings.duration < 1000,
    [\`\${endpoint.name}: response has body\`]: (r) => r.body && r.body.length > 0
  });

  errorRate.add(!success);

  // 添加标签用于分析
  response.tags = {
    endpoint: endpoint.name,
    method: endpoint.method
  };

  sleep(Math.random() * 2 + 1); // 1-3秒随机等待
}

export function handleSummary(data) {
  return {
    'performance-results.json': JSON.stringify(data, null, 2),
    'performance-summary.txt': textSummary(data, { indent: ' ', enableColors: false })
  };
}

function textSummary(data, options = {}) {
  const indent = options.indent || '';
  const enableColors = options.enableColors !== false;

  let summary = \`
\${indent}Performance Test Summary
\${indent}========================
\${indent}
\${indent}Test Duration: \${data.state.testRunDurationMs}ms
\${indent}Virtual Users: \${data.metrics.vus?.values?.value || 'N/A'}
\${indent}
\${indent}HTTP Metrics:
\${indent}  Requests: \${data.metrics.http_reqs?.values?.count || 0}
\${indent}  Failed: \${data.metrics.http_req_failed?.values?.rate * 100 || 0}%
\${indent}  Duration (avg): \${data.metrics.http_req_duration?.values?.avg || 0}ms
\${indent}  Duration (p95): \${data.metrics.http_req_duration?.values?.['p(95)'] || 0}ms
\${indent}  Duration (p99): \${data.metrics.http_req_duration?.values?.['p(99)'] || 0}ms
\${indent}  RPS: \${data.metrics.http_reqs?.values?.rate || 0}
\${indent}
\${indent}Custom Metrics:
\${indent}  Error Rate: \${data.metrics.errors?.values?.rate * 100 || 0}%
\${indent}  Avg Response Time: \${data.metrics.response_time?.values?.avg || 0}ms
\`;

  return summary;
}
`;

  return script;
}

/**
 * 运行性能测试
 */
async function runPerformanceTest(scenario) {
  console.log(`🚀 开始运行性能测试: ${scenario.name}`);
  console.log(`📝 描述: ${scenario.description}`);

  // 生成K6脚本
  const scriptContent = generateK6Script(scenario);
  const scriptPath = path.join(__dirname, `k6-${scenario.name}.js`);
  fs.writeFileSync(scriptPath, scriptContent);

  // 运行K6测试
  return new Promise((resolve, reject) => {
    const k6Process = spawn('k6', ['run', scriptPath], {
      stdio: 'inherit',
      env: {
        ...process.env,
        BASE_URL: process.env.BASE_URL || 'http://localhost:3000',
        AUTH_TOKEN: process.env.AUTH_TOKEN || 'test-token',
      },
    });

    k6Process.on('close', code => {
      // 清理脚本文件
      fs.unlinkSync(scriptPath);

      if (code === 0) {
        console.log(`✅ 性能测试 ${scenario.name} 完成`);
        resolve();
      } else {
        console.error(`❌ 性能测试 ${scenario.name} 失败`);
        reject(new Error(`K6 exited with code ${code}`));
      }
    });
  });
}

/**
 * 分析性能结果
 */
function analyzePerformanceResults() {
  const resultsFile = path.join(process.cwd(), 'performance-results.json');

  if (!fs.existsSync(resultsFile)) {
    throw new Error('性能测试结果文件不存在');
  }

  const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
  const analysis = {
    summary: {},
    issues: [],
    recommendations: [],
  };

  // 分析响应时间
  const httpDuration = results.metrics.http_req_duration?.values || {};
  analysis.summary.responseTime = {
    avg: httpDuration.avg || 0,
    p50: httpDuration['p(50)'] || 0,
    p90: httpDuration['p(90)'] || 0,
    p95: httpDuration['p(95)'] || 0,
    p99: httpDuration['p(99)'] || 0,
  };

  // 检查响应时间基线
  Object.keys(PERFORMANCE_BASELINES.responseTime).forEach(percentile => {
    const baseline = PERFORMANCE_BASELINES.responseTime[percentile];
    const actual = analysis.summary.responseTime[percentile];

    if (actual > baseline) {
      analysis.issues.push({
        type: 'response_time',
        metric: percentile,
        baseline,
        actual,
        message: `${percentile}响应时间 ${actual.toFixed(2)}ms 超过基线 ${baseline}ms`,
      });
    }
  });

  // 分析吞吐量
  const httpReqs = results.metrics.http_reqs?.values || {};
  analysis.summary.throughput = {
    total: httpReqs.count || 0,
    rate: httpReqs.rate || 0,
  };

  // 检查吞吐量基线
  if (analysis.summary.throughput.rate < PERFORMANCE_BASELINES.throughput.minimum) {
    analysis.issues.push({
      type: 'throughput',
      metric: 'rate',
      baseline: PERFORMANCE_BASELINES.throughput.minimum,
      actual: analysis.summary.throughput.rate,
      message: `吞吐量 ${analysis.summary.throughput.rate.toFixed(2)} RPS 低于最低基线 ${PERFORMANCE_BASELINES.throughput.minimum} RPS`,
    });
  }

  // 分析错误率
  const httpFailed = results.metrics.http_req_failed?.values || {};
  analysis.summary.errorRate = httpFailed.rate || 0;

  // 检查错误率基线
  if (analysis.summary.errorRate > PERFORMANCE_BASELINES.errorRate.maximum) {
    analysis.issues.push({
      type: 'error_rate',
      metric: 'rate',
      baseline: PERFORMANCE_BASELINES.errorRate.maximum,
      actual: analysis.summary.errorRate,
      message: `错误率 ${(analysis.summary.errorRate * 100).toFixed(2)}% 超过基线 ${(PERFORMANCE_BASELINES.errorRate.maximum * 100).toFixed(2)}%`,
    });
  }

  // 生成改进建议
  if (analysis.issues.length > 0) {
    analysis.recommendations.push('考虑以下性能优化措施:');

    if (analysis.issues.some(issue => issue.type === 'response_time')) {
      analysis.recommendations.push('- 优化数据库查询，添加适当的索引');
      analysis.recommendations.push('- 实施缓存策略，减少重复计算');
      analysis.recommendations.push('- 优化API响应数据结构，减少传输量');
    }

    if (analysis.issues.some(issue => issue.type === 'throughput')) {
      analysis.recommendations.push('- 增加服务器资源或实施水平扩展');
      analysis.recommendations.push('- 优化应用程序代码，减少CPU密集型操作');
      analysis.recommendations.push('- 考虑使用负载均衡器分散请求');
    }

    if (analysis.issues.some(issue => issue.type === 'error_rate')) {
      analysis.recommendations.push('- 检查错误日志，修复导致错误的问题');
      analysis.recommendations.push('- 增强错误处理和重试机制');
      analysis.recommendations.push('- 实施断路器模式防止级联故障');
    }
  } else {
    analysis.recommendations.push('当前性能表现良好，建议:');
    analysis.recommendations.push('- 定期监控性能指标');
    analysis.recommendations.push('- 建立性能回归测试');
    analysis.recommendations.push('- 持续优化关键路径');
  }

  return analysis;
}

/**
 * 生成性能基线报告
 */
function generateBaselineReport(analysis) {
  const reportPath = path.join(process.cwd(), 'performance-baseline-report.md');

  let report = `# 性能基线报告\n\n`;
  report += `**生成时间**: ${new Date().toLocaleString()}\n\n`;

  // 性能摘要
  report += `## 📊 性能摘要\n\n`;
  report += `### 响应时间\n`;
  report += `| 指标 | 实际值 | 基线 | 状态 |\n`;
  report += `|------|--------|------|------|\n`;

  Object.keys(PERFORMANCE_BASELINES.responseTime).forEach(percentile => {
    const baseline = PERFORMANCE_BASELINES.responseTime[percentile];
    const actual = analysis.summary.responseTime[percentile];
    const status = actual <= baseline ? '✅' : '❌';

    report += `| ${percentile} | ${actual.toFixed(2)}ms | ${baseline}ms | ${status} |\n`;
  });

  report += `\n### 吞吐量\n`;
  report += `- **总请求数**: ${analysis.summary.throughput.total}\n`;
  report += `- **请求速率**: ${analysis.summary.throughput.rate.toFixed(2)} RPS\n`;
  report += `- **基线要求**: ${PERFORMANCE_BASELINES.throughput.minimum} RPS (最低)\n`;

  report += `\n### 错误率\n`;
  report += `- **实际错误率**: ${(analysis.summary.errorRate * 100).toFixed(2)}%\n`;
  report += `- **基线要求**: ${(PERFORMANCE_BASELINES.errorRate.maximum * 100).toFixed(2)}% (最大)\n`;

  // 问题列表
  if (analysis.issues.length > 0) {
    report += `\n## ❌ 性能问题\n\n`;
    analysis.issues.forEach((issue, index) => {
      report += `${index + 1}. ${issue.message}\n`;
    });
  } else {
    report += `\n## ✅ 所有性能指标达标！\n`;
  }

  // 改进建议
  report += `\n## 💡 改进建议\n\n`;
  analysis.recommendations.forEach(recommendation => {
    report += `${recommendation}\n`;
  });

  // 基线配置
  report += `\n## ⚙️ 性能基线配置\n\n`;
  report += `\`\`\`json\n${JSON.stringify(PERFORMANCE_BASELINES, null, 2)}\n\`\`\`\n`;

  fs.writeFileSync(reportPath, report);
  console.log(`\n📄 性能基线报告已生成: ${reportPath}`);

  return report;
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🎯 开始建立性能基线...\n');

    // 运行冒烟测试
    await runPerformanceTest(TEST_SCENARIOS[0]);

    // 分析结果
    console.log('\n📊 分析性能结果...');
    const analysis = analyzePerformanceResults();

    // 生成报告
    console.log('📝 生成性能基线报告...');
    generateBaselineReport(analysis);

    // 输出结果
    if (analysis.issues.length === 0) {
      console.log('\n🎉 性能基线建立成功！所有指标达标');
      process.exit(0);
    } else {
      console.log(`\n⚠️  发现 ${analysis.issues.length} 个性能问题:`);
      analysis.issues.forEach(issue => {
        console.log(`   - ${issue.message}`);
      });
      console.log('\n💡 请查看详细报告并进行性能优化');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 性能基线建立失败:', error.message);
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  runPerformanceTest,
  analyzePerformanceResults,
  generateBaselineReport,
  PERFORMANCE_BASELINES,
  TEST_SCENARIOS,
};
