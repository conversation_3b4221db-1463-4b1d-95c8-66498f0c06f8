/**
 * 并发测试脚本
 * 使用k6测试API在高并发场景下的表现
 * 
 * 运行方法:
 * k6 run backend/scripts/performance-tests/concurrency-test.js
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { randomItem, randomString } from './utils.js';

// 自定义指标
const errorRate = new Rate('error_rate');
const apiLatency = new Trend('api_latency');
const concurrencyImpact = new Trend('concurrency_impact');
const successfulRequests = new Counter('successful_requests');
const failedRequests = new Counter('failed_requests');

// 测试配置
export const options = {
  // 基本配置
  vus: 0,            // 初始虚拟用户数
  duration: '5m',    // 测试持续时间
  
  // 阶段配置 - 并发测试通常会快速增加用户数，然后保持一段时间
  stages: [
    { duration: '30s', target: 100 },   // 逐渐增加到100个用户
    { duration: '1m', target: 100 },    // 保持100个用户1分钟
    { duration: '30s', target: 300 },   // 逐渐增加到300个用户
    { duration: '1m', target: 300 },    // 保持300个用户1分钟
    { duration: '30s', target: 500 },   // 逐渐增加到500个用户
    { duration: '1m', target: 500 },    // 保持500个用户1分钟
    { duration: '30s', target: 0 },     // 逐渐减少到0个用户
  ],
  
  // 阈值配置
  thresholds: {
    http_req_duration: ['p(95)<1000'], // 95%的请求响应时间小于1秒
    http_req_failed: ['rate<0.05'],    // 请求失败率小于5%
    error_rate: ['rate<0.1'],          // 自定义错误率小于10%
    'concurrency_impact{endpoint:get_users}': ['p(95)<500'], // 用户列表接口95%的响应时间小于500ms
    'concurrency_impact{endpoint:create_note}': ['p(95)<800'], // 创建笔记接口95%的响应时间小于800ms
  },
};

// 测试环境配置
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_VERSION = __ENV.API_VERSION || 'v2';
const API_BASE = `${BASE_URL}/api/${API_VERSION}`;

// 请求头配置
const headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// 如果有认证令牌，可以在这里添加
if (__ENV.AUTH_TOKEN) {
  headers['Authorization'] = `Bearer ${__ENV.AUTH_TOKEN}`;
}

// 测试初始化
export function setup() {
  console.log(`开始并发测试: ${BASE_URL}`);
  
  // 创建测试用户
  const createUserRes = http.post(`${API_BASE}/users`, JSON.stringify({
    username: `concurrencytest_${Date.now()}`,
    email: `concurrency_${Date.now()}@example.com`,
    password: 'ConcurrencyTest123!'
  }), { headers });
  
  let userId = null;
  if (createUserRes.status === 201) {
    const body = JSON.parse(createUserRes.body);
    userId = body.data.id;
    console.log(`创建测试用户成功: ${userId}`);
  } else {
    console.log(`创建测试用户失败: ${createUserRes.status}`);
    // 使用默认测试用户
    userId = 1;
  }
  
  // 创建一些测试数据
  const testData = {
    userId,
    noteIds: [],
    insightIds: [],
    exerciseIds: []
  };
  
  // 创建一些笔记
  for (let i = 0; i < 5; i++) {
    const createNoteRes = http.post(`${API_BASE}/notes`, JSON.stringify({
      content: `Concurrency test note ${i} - ${Date.now()}`,
      userId,
      tags: ['concurrency-test']
    }), { headers });
    
    if (createNoteRes.status === 201) {
      const body = JSON.parse(createNoteRes.body);
      testData.noteIds.push(body.data.id);
    }
  }
  
  return testData;
}

// 测试场景
export default function(data) {
  const userId = data.userId || 1;
  const noteIds = data.noteIds || [];
  const currentVUs = __VU; // 当前虚拟用户数
  
  // 随机选择一个API端点进行测试
  const endpoint = randomItem([
    'users',
    'notes',
    'insights',
    'exercises',
    'learning-plans',
    'themes',
    'tags'
  ]);
  
  // 执行GET请求
  {
    const startTime = new Date();
    const response = http.get(`${API_BASE}/${endpoint}?page=1&pageSize=20`, { headers });
    const endTime = new Date();
    
    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: `get_${endpoint}` });
    
    // 记录并发影响
    concurrencyImpact.add(endTime - startTime, { 
      endpoint: `get_${endpoint}`,
      vus: currentVUs
    });
    
    // 检查响应
    const success = check(response, {
      'status is 200': (r) => r.status === 200,
      'has data': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.success && body.data;
        } catch (e) {
          return false;
        }
      },
    });
    
    // 记录成功/失败请求
    if (success) {
      successfulRequests.add(1, { endpoint: `get_${endpoint}` });
    } else {
      failedRequests.add(1, { endpoint: `get_${endpoint}` });
    }
    
    // 记录错误率
    errorRate.add(!success);
  }
  
  // 执行POST请求 - 创建笔记
  {
    const payload = JSON.stringify({
      content: `Concurrency test note - VU: ${currentVUs} - ${randomString(10)}`,
      userId,
      tags: ['concurrency-test', `vu-${currentVUs}`]
    });
    
    const startTime = new Date();
    const response = http.post(`${API_BASE}/notes`, payload, { headers });
    const endTime = new Date();
    
    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: 'create_note' });
    
    // 记录并发影响
    concurrencyImpact.add(endTime - startTime, { 
      endpoint: 'create_note',
      vus: currentVUs
    });
    
    // 检查响应
    const success = check(response, {
      'status is 201': (r) => r.status === 201,
      'has note data': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.success && body.data && body.data.content;
        } catch (e) {
          return false;
        }
      },
    });
    
    // 记录成功/失败请求
    if (success) {
      successfulRequests.add(1, { endpoint: 'create_note' });
    } else {
      failedRequests.add(1, { endpoint: 'create_note' });
    }
    
    // 记录错误率
    errorRate.add(!success);
  }
  
  // 如果有笔记ID，执行PUT请求 - 更新笔记
  if (noteIds.length > 0 && Math.random() < 0.3) { // 30%的概率执行更新操作
    const noteId = randomItem(noteIds);
    const payload = JSON.stringify({
      content: `Updated concurrency test note - VU: ${currentVUs} - ${randomString(10)}`,
      tags: ['concurrency-test', `vu-${currentVUs}`, 'updated']
    });
    
    const startTime = new Date();
    const response = http.put(`${API_BASE}/notes/${noteId}`, payload, { headers });
    const endTime = new Date();
    
    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: 'update_note' });
    
    // 记录并发影响
    concurrencyImpact.add(endTime - startTime, { 
      endpoint: 'update_note',
      vus: currentVUs
    });
    
    // 检查响应
    const success = check(response, {
      'status is 200': (r) => r.status === 200,
      'has updated note data': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.success && body.data && body.data.content.includes('Updated');
        } catch (e) {
          return false;
        }
      },
    });
    
    // 记录成功/失败请求
    if (success) {
      successfulRequests.add(1, { endpoint: 'update_note' });
    } else {
      failedRequests.add(1, { endpoint: 'update_note' });
    }
    
    // 记录错误率
    errorRate.add(!success);
  }
  
  // 在请求之间添加短暂延迟，避免完全淹没服务器
  sleep(Math.random() * 0.3); // 0-0.3秒的随机延迟
}

// 测试清理
export function teardown(data) {
  console.log('并发测试完成');
  
  // 清理测试数据
  if (data.userId && data.userId !== 1) {
    const response = http.del(`${API_BASE}/users/${data.userId}`, null, { headers });
    console.log(`清理测试用户: ${response.status}`);
  }
  
  // 清理创建的笔记
  if (data.noteIds && data.noteIds.length > 0) {
    console.log(`清理测试笔记: ${data.noteIds.length}个`);
    data.noteIds.forEach(noteId => {
      http.del(`${API_BASE}/notes/${noteId}`, null, { headers });
    });
  }
}
