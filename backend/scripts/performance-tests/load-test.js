/**
 * 负载测试脚本
 * 使用k6测试API在不同负载下的性能
 *
 * 运行方法:
 * k6 run backend/scripts/performance-tests/load-test.js
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomItem } from './utils.js';

// 自定义指标
const errorRate = new Rate('error_rate');
const apiLatency = new Trend('api_latency');

// 测试配置
export const options = {
  // 快速模式下的配置
  ...((__ENV.QUICK_MODE === 'true') && {
    stages: [
      { duration: '30s', target: 10 }, // 30秒内增加到10个虚拟用户
      { duration: '1m', target: 10 },  // 保持10个虚拟用户1分钟
      { duration: '30s', target: 0 },  // 30秒内减少到0个虚拟用户
    ],
  }),

  // 正常模式下的配置
  ...(!(__ENV.QUICK_MODE === 'true') && {
    stages: [
      { duration: '1m', target: 10 },   // 逐渐增加到10个用户
      { duration: '3m', target: 50 },   // 逐渐增加到50个用户
      { duration: '1m', target: 100 },  // 逐渐增加到100个用户
      { duration: '2m', target: 100 },  // 保持100个用户2分钟
      { duration: '1m', target: 0 },    // 逐渐减少到0个用户
    ],
  }),

  // 阈值配置
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%的请求响应时间小于500ms
    http_req_failed: ['rate<0.01'],   // 请求失败率小于1%
    error_rate: ['rate<0.05'],        // 自定义错误率小于5%
    'api_latency{endpoint:get_users}': ['p(95)<300'], // 获取用户列表接口95%的响应时间小于300ms
    'api_latency{endpoint:get_insights}': ['p(95)<400'], // 获取观点列表接口95%的响应时间小于400ms
  },
};

// 测试数据
const testData = new SharedArray('test_data', function() {
  // 这里可以从JSON文件加载测试数据
  return [
    { userId: 1, username: 'test1' },
    { userId: 2, username: 'test2' },
    { userId: 3, username: 'test3' },
    { userId: 4, username: 'test4' },
    { userId: 5, username: 'test5' },
  ];
});

// 测试环境配置
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_VERSION = __ENV.API_VERSION || 'v2';
const API_BASE = `${BASE_URL}/api/${API_VERSION}`;

// 请求头配置
const headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// 如果有认证令牌，可以在这里添加
if (__ENV.AUTH_TOKEN) {
  headers['Authorization'] = `Bearer ${__ENV.AUTH_TOKEN}`;
}

// 测试初始化
export function setup() {
  console.log(`开始负载测试: ${BASE_URL}`);

  // 可以在这里进行测试前的准备工作，如获取认证令牌
  const loginRes = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify({
    username: 'testuser',
    password: 'testpassword',
  }), { headers: { 'Content-Type': 'application/json' } });

  if (loginRes.status === 200) {
    const token = JSON.parse(loginRes.body).data.token;
    return { token };
  }

  return {};
}

// 测试场景
export default function(data) {
  // 如果setup返回了token，使用它
  if (data.token) {
    headers['Authorization'] = `Bearer ${data.token}`;
  }

  // 随机选择一个测试用户
  const testUser = randomItem(testData);

  // 测试获取用户列表
  {
    const startTime = new Date();
    const response = http.get(`${API_BASE}/users?page=1&pageSize=20`, { headers });
    const endTime = new Date();

    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: 'get_users' });

    // 检查响应
    const success = check(response, {
      'status is 200': (r) => r.status === 200,
      'has users data': (r) => {
        const body = JSON.parse(r.body);
        return body.success && Array.isArray(body.data.rows);
      },
    });

    // 记录错误率
    errorRate.add(!success);
  }

  // 测试获取特定用户
  {
    const startTime = new Date();
    const response = http.get(`${API_BASE}/users/${testUser.userId}`, { headers });
    const endTime = new Date();

    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: 'get_user' });

    // 检查响应
    const success = check(response, {
      'status is 200': (r) => r.status === 200,
      'has user data': (r) => {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.id === testUser.userId;
      },
    });

    // 记录错误率
    errorRate.add(!success);
  }

  // 测试获取观点列表
  {
    const startTime = new Date();
    const response = http.get(`${API_BASE}/insights?page=1&pageSize=10`, { headers });
    const endTime = new Date();

    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: 'get_insights' });

    // 检查响应
    const success = check(response, {
      'status is 200': (r) => r.status === 200,
      'has insights data': (r) => {
        const body = JSON.parse(r.body);
        return body.success && Array.isArray(body.data.rows);
      },
    });

    // 记录错误率
    errorRate.add(!success);
  }

  // 测试创建笔记
  {
    const payload = JSON.stringify({
      content: `Test note from performance test - ${new Date().toISOString()}`,
      userId: testUser.userId,
      tags: ['test', 'performance']
    });

    const startTime = new Date();
    const response = http.post(`${API_BASE}/notes`, payload, { headers });
    const endTime = new Date();

    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: 'create_note' });

    // 检查响应
    const success = check(response, {
      'status is 201': (r) => r.status === 201,
      'has note data': (r) => {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.content;
      },
    });

    // 记录错误率
    errorRate.add(!success);
  }

  // 在请求之间添加随机延迟，模拟真实用户行为
  sleep(Math.random() * 3 + 1); // 1-4秒的随机延迟
}

// 测试清理
export function teardown(data) {
  console.log('负载测试完成');

  // 可以在这里进行测试后的清理工作
  if (data.token) {
    http.post(`${BASE_URL}/api/auth/logout`, null, {
      headers: {
        'Authorization': `Bearer ${data.token}`,
        'Content-Type': 'application/json'
      }
    });
  }
}
