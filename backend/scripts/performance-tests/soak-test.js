/**
 * 长稳测试脚本
 * 使用k6测试API在长时间运行下的稳定性
 * 
 * 运行方法:
 * k6 run backend/scripts/performance-tests/soak-test.js
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { randomItem, randomString } from './utils.js';

// 自定义指标
const errorRate = new Rate('error_rate');
const apiLatency = new Trend('api_latency');
const memoryLeak = new Trend('memory_leak_indicator');
const successfulRequests = new Counter('successful_requests');
const failedRequests = new Counter('failed_requests');

// 测试配置
export const options = {
  // 基本配置
  vus: 50,           // 虚拟用户数
  duration: '12h',   // 测试持续时间 - 长稳测试通常运行数小时甚至数天
  
  // 阶段配置
  stages: [
    { duration: '30m', target: 50 },   // 逐渐增加到50个用户
    { duration: '10h', target: 50 },   // 保持50个用户10小时
    { duration: '30m', target: 0 },    // 逐渐减少到0个用户
  ],
  
  // 阈值配置
  thresholds: {
    http_req_duration: ['p(95)<500', 'p(99)<1000'], // 95%的请求响应时间小于500ms，99%小于1秒
    http_req_failed: ['rate<0.01'],    // 请求失败率小于1%
    error_rate: ['rate<0.05'],         // 自定义错误率小于5%
    'memory_leak_indicator': ['avg<100'], // 内存泄漏指标平均值小于100
  },
};

// 测试环境配置
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_VERSION = __ENV.API_VERSION || 'v2';
const API_BASE = `${BASE_URL}/api/${API_VERSION}`;

// 请求头配置
const headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// 如果有认证令牌，可以在这里添加
if (__ENV.AUTH_TOKEN) {
  headers['Authorization'] = `Bearer ${__ENV.AUTH_TOKEN}`;
}

// 测试初始化
export function setup() {
  console.log(`开始长稳测试: ${BASE_URL}`);
  console.log(`测试将持续运行: ${options.duration}`);
  
  // 创建测试用户
  const users = [];
  for (let i = 0; i < 5; i++) {
    const createUserRes = http.post(`${API_BASE}/users`, JSON.stringify({
      username: `soaktest_${Date.now()}_${i}`,
      email: `soak_${Date.now()}_${i}@example.com`,
      password: 'SoakTest123!'
    }), { headers });
    
    if (createUserRes.status === 201) {
      const body = JSON.parse(createUserRes.body);
      users.push(body.data.id);
      console.log(`创建测试用户成功: ${body.data.id}`);
    }
  }
  
  // 如果没有成功创建用户，使用默认用户
  if (users.length === 0) {
    users.push(1);
  }
  
  return { users, startTime: Date.now() };
}

// 测试场景
export default function(data) {
  const users = data.users || [1];
  const userId = randomItem(users);
  const testStartTime = data.startTime || Date.now();
  const testRunningTime = (Date.now() - testStartTime) / 1000 / 60; // 分钟
  
  // 随机选择一个API端点进行测试
  const endpoint = randomItem([
    'users',
    'insights',
    'exercises',
    'notes',
    'learning-plans',
    'themes',
    'tags',
    'statistics'
  ]);
  
  // 执行GET请求
  {
    const startTime = new Date();
    const response = http.get(`${API_BASE}/${endpoint}?page=1&pageSize=20`, { headers });
    const endTime = new Date();
    
    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: `get_${endpoint}` });
    
    // 检查响应
    const success = check(response, {
      'status is 200': (r) => r.status === 200,
      'has data': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.success && body.data;
        } catch (e) {
          return false;
        }
      },
    });
    
    // 记录成功/失败请求
    if (success) {
      successfulRequests.add(1, { endpoint });
    } else {
      failedRequests.add(1, { endpoint });
    }
    
    // 记录错误率
    errorRate.add(!success);
    
    // 记录响应时间随测试运行时间的变化，用于检测内存泄漏
    // 如果系统存在内存泄漏，随着时间推移，响应时间通常会增加
    memoryLeak.add(endTime - startTime, { runningTime: Math.floor(testRunningTime) });
  }
  
  // 执行一些写操作
  if (Math.random() < 0.2) { // 20%的概率执行写操作
    const writeEndpoint = randomItem(['notes', 'insights']);
    const payload = JSON.stringify({
      content: `Soak test content - ${randomString(20)} - Running time: ${testRunningTime.toFixed(2)} minutes`,
      userId: userId,
      tags: ['soak-test', randomString(5)]
    });
    
    const startTime = new Date();
    const response = http.post(`${API_BASE}/${writeEndpoint}`, payload, { headers });
    const endTime = new Date();
    
    // 记录延迟
    apiLatency.add(endTime - startTime, { endpoint: `create_${writeEndpoint}` });
    
    // 检查响应
    const success = check(response, {
      'status is 201': (r) => r.status === 201,
      'has data': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.success && body.data;
        } catch (e) {
          return false;
        }
      },
    });
    
    // 记录成功/失败请求
    if (success) {
      successfulRequests.add(1, { endpoint: `create_${writeEndpoint}` });
    } else {
      failedRequests.add(1, { endpoint: `create_${writeEndpoint}` });
    }
    
    // 记录错误率
    errorRate.add(!success);
  }
  
  // 每小时检查一次系统健康状态
  if (Math.random() < 0.001) { // 大约每1000次迭代检查一次
    const healthRes = http.get(`${BASE_URL}/health`, { headers });
    console.log(`系统健康检查: ${healthRes.status} - 运行时间: ${testRunningTime.toFixed(2)} 分钟`);
    
    // 如果有监控API，可以获取内存使用情况
    try {
      const metricsRes = http.get(`${BASE_URL}/metrics`, { headers });
      if (metricsRes.status === 200) {
        const metrics = JSON.parse(metricsRes.body);
        console.log(`系统指标 - 内存使用: ${metrics.memory.heapUsed}MB - 运行时间: ${testRunningTime.toFixed(2)} 分钟`);
      }
    } catch (e) {
      // 忽略错误
    }
  }
  
  // 在请求之间添加随机延迟，模拟真实用户行为
  sleep(Math.random() * 5 + 1); // 1-6秒的随机延迟
}

// 测试清理
export function teardown(data) {
  console.log('长稳测试完成');
  
  // 清理测试数据
  if (data.users) {
    data.users.forEach(userId => {
      if (userId !== 1) {
        const response = http.del(`${API_BASE}/users/${userId}`, null, { headers });
        console.log(`清理测试用户 ${userId}: ${response.status}`);
      }
    });
  }
}
