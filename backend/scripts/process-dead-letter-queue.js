/**
 * 死信队列处理脚本
 * 
 * 此脚本用于处理死信队列中的事件，尝试重新处理失败的事件
 * 可以通过cron作业定期运行，例如每5分钟运行一次
 */

const { sequelize } = require('../config/database');
const logger = require('../config/logger');
const container = require('../infrastructure/di/container').default;

/**
 * 处理死信队列
 */
async function processDeadLetterQueue() {
  try {
    logger.info('开始处理死信队列...');

    // 获取死信队列服务
    const deadLetterQueueService = container.get('deadLetterQueueService');
    
    // 处理死信队列中的事件
    const limit = process.env.DLQ_PROCESS_LIMIT ? parseInt(process.env.DLQ_PROCESS_LIMIT) : 10;
    await deadLetterQueueService.processDeadLetterQueue(limit);
    
    logger.info('死信队列处理完成');
    process.exit(0);
  } catch (error) {
    logger.error(`处理死信队列失败: ${error.message}`, {
      error: error.stack
    });
    process.exit(1);
  }
}

// 执行处理
processDeadLetterQueue();
