/**
 * 生产部署检查脚本
 * 全面检查生产环境部署准备情况
 */

// 加载环境变量
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class ProductionDeploymentChecker {
  constructor() {
    this.checks = [];
    this.issues = [];
    this.warnings = [];
    this.passed = 0;
    this.failed = 0;
  }

  /**
   * 执行所有部署检查
   */
  async runAllChecks() {
    console.log('🚀 开始生产部署检查...\n');

    try {
      // 环境配置检查
      await this.checkEnvironmentConfiguration();

      // 依赖检查
      await this.checkDependencies();

      // 安全配置检查
      await this.checkSecurityConfiguration();

      // 数据库配置检查
      await this.checkDatabaseConfiguration();

      // 缓存配置检查
      await this.checkCacheConfiguration();

      // 日志配置检查
      await this.checkLoggingConfiguration();

      // 监控配置检查
      await this.checkMonitoringConfiguration();

      // 性能配置检查
      await this.checkPerformanceConfiguration();

      // Docker配置检查
      await this.checkDockerConfiguration();

      // 备份策略检查
      await this.checkBackupStrategy();

      // 健康检查配置
      await this.checkHealthCheckConfiguration();

      // 生成检查报告
      const report = this.generateReport();

      console.log('\n✅ 生产部署检查完成');
      return report;
    } catch (error) {
      console.error('❌ 生产部署检查失败:', error);
      throw error;
    }
  }

  /**
   * 检查环境配置
   */
  async checkEnvironmentConfiguration() {
    console.log('🔧 检查环境配置...');

    // 检查必需的环境变量
    const requiredEnvVars = [
      'NODE_ENV',
      'PORT',
      'DB_HOST',
      'DB_NAME',
      'DB_USER',
      'DB_PASSWORD',
      'JWT_SECRET',
      'REDIS_URL',
    ];

    const missingVars = [];
    requiredEnvVars.forEach(varName => {
      if (!process.env[varName]) {
        missingVars.push(varName);
      }
    });

    if (missingVars.length > 0) {
      this.addIssue('environment', 'critical', `缺少必需的环境变量: ${missingVars.join(', ')}`);
    } else {
      this.addCheck('environment_vars', '所有必需的环境变量已配置', 'passed');
    }

    // 检查NODE_ENV设置
    if (process.env.NODE_ENV !== 'production') {
      this.addWarning(
        'environment',
        `NODE_ENV设置为 '${process.env.NODE_ENV}'，生产环境应设置为 'production'`,
      );
    } else {
      this.addCheck('node_env', 'NODE_ENV正确设置为production', 'passed');
    }

    // 检查端口配置
    const port = parseInt(process.env.PORT);
    if (isNaN(port) || port < 1 || port > 65535) {
      this.addIssue('environment', 'high', `端口配置无效: ${process.env.PORT}`);
    } else {
      this.addCheck('port_config', `端口配置正确: ${port}`, 'passed');
    }

    // 检查.env文件
    const envFiles = ['.env.production', '.env'];
    let envFileExists = false;

    for (const envFile of envFiles) {
      if (fs.existsSync(envFile)) {
        envFileExists = true;
        this.addCheck('env_file', `环境配置文件存在: ${envFile}`, 'passed');
        break;
      }
    }

    if (!envFileExists) {
      this.addWarning('environment', '未找到环境配置文件 (.env.production 或 .env)');
    }
  }

  /**
   * 检查依赖
   */
  async checkDependencies() {
    console.log('📦 检查依赖...');

    // 检查package.json
    if (!fs.existsSync('package.json')) {
      this.addIssue('dependencies', 'critical', 'package.json文件不存在');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

    // 检查必需的依赖
    const requiredDeps = [
      'express',
      'sequelize',
      'mysql2',
      'redis',
      'jsonwebtoken',
      'bcryptjs',
      'winston',
      'helmet',
      'cors',
      'express-rate-limit',
    ];

    const missingDeps = [];
    requiredDeps.forEach(dep => {
      if (!packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]) {
        missingDeps.push(dep);
      }
    });

    if (missingDeps.length > 0) {
      this.addIssue('dependencies', 'high', `缺少必需的依赖: ${missingDeps.join(', ')}`);
    } else {
      this.addCheck('required_deps', '所有必需的依赖已安装', 'passed');
    }

    // 检查node_modules
    if (!fs.existsSync('node_modules')) {
      this.addIssue('dependencies', 'critical', 'node_modules目录不存在，请运行 npm install');
    } else {
      this.addCheck('node_modules', 'node_modules目录存在', 'passed');
    }

    // 检查package-lock.json
    if (!fs.existsSync('package-lock.json')) {
      this.addWarning('dependencies', '缺少package-lock.json，建议使用npm ci进行生产部署');
    } else {
      this.addCheck('package_lock', 'package-lock.json存在', 'passed');
    }

    // 检查安全漏洞
    try {
      await this.runCommand('npm', ['audit', '--audit-level', 'high']);
      this.addCheck('security_audit', 'npm安全审计通过', 'passed');
    } catch (error) {
      this.addWarning('dependencies', 'npm安全审计发现问题，请运行 npm audit fix');
    }
  }

  /**
   * 检查安全配置
   */
  async checkSecurityConfiguration() {
    console.log('🔒 检查安全配置...');

    // 检查JWT密钥强度
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      this.addIssue('security', 'critical', 'JWT_SECRET未设置');
    } else if (jwtSecret.length < 32) {
      this.addIssue('security', 'high', 'JWT_SECRET长度不足，建议至少32个字符');
    } else {
      this.addCheck('jwt_secret', 'JWT_SECRET配置安全', 'passed');
    }

    // 检查数据库密码强度
    const dbPassword = process.env.DB_PASSWORD;
    if (!dbPassword) {
      this.addIssue('security', 'critical', 'DB_PASSWORD未设置');
    } else if (dbPassword.length < 12) {
      this.addWarning('security', '数据库密码较弱，建议使用更强的密码');
    } else {
      this.addCheck('db_password', '数据库密码配置安全', 'passed');
    }

    // 检查HTTPS配置
    if (!process.env.HTTPS_ENABLED && !process.env.SSL_CERT) {
      this.addWarning('security', '未配置HTTPS，生产环境建议启用HTTPS');
    }

    // 检查CORS配置
    const corsConfig = this.checkFileContains('./config/cors.js', 'origin:');
    if (!corsConfig) {
      this.addWarning('security', '未找到CORS配置，请确保正确配置跨域访问');
    } else {
      this.addCheck('cors_config', 'CORS配置存在', 'passed');
    }

    // 检查Helmet配置
    const helmetConfig = this.checkFileContains('./middlewares/security.js', 'helmet');
    if (!helmetConfig) {
      this.addWarning('security', '未找到Helmet安全中间件配置');
    } else {
      this.addCheck('helmet_config', 'Helmet安全中间件已配置', 'passed');
    }

    // 检查限流配置
    const rateLimitConfig = this.checkFileContains('./middlewares/rateLimit.js', 'rateLimit');
    if (!rateLimitConfig) {
      this.addWarning('security', '未找到限流配置');
    } else {
      this.addCheck('rate_limit', '限流配置已设置', 'passed');
    }
  }

  /**
   * 检查数据库配置
   */
  async checkDatabaseConfiguration() {
    console.log('🗄️ 检查数据库配置...');

    // 检查数据库连接配置
    const dbConfig = {
      host: process.env.DB_HOST,
      name: process.env.DB_NAME,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      port: process.env.DB_PORT || 3306,
    };

    const missingDbConfig = [];
    Object.keys(dbConfig).forEach(key => {
      if (!dbConfig[key]) {
        missingDbConfig.push(`DB_${key.toUpperCase()}`);
      }
    });

    if (missingDbConfig.length > 0) {
      this.addIssue('database', 'critical', `缺少数据库配置: ${missingDbConfig.join(', ')}`);
    } else {
      this.addCheck('db_config', '数据库配置完整', 'passed');
    }

    // 检查数据库连接池配置
    const poolConfig = this.checkFileContains('./config/database.js', 'pool');
    if (!poolConfig) {
      this.addWarning('database', '未找到数据库连接池配置');
    } else {
      this.addCheck('db_pool', '数据库连接池已配置', 'passed');
    }

    // 检查迁移文件
    if (!fs.existsSync('./migrations') || fs.readdirSync('./migrations').length === 0) {
      this.addWarning('database', '未找到数据库迁移文件');
    } else {
      this.addCheck('db_migrations', '数据库迁移文件存在', 'passed');
    }

    // 检查种子文件
    if (fs.existsSync('./seeders') && fs.readdirSync('./seeders').length > 0) {
      this.addCheck('db_seeders', '数据库种子文件存在', 'passed');
    }
  }

  /**
   * 检查缓存配置
   */
  async checkCacheConfiguration() {
    console.log('⚡ 检查缓存配置...');

    // 检查Redis配置
    if (!process.env.REDIS_URL && !process.env.REDIS_HOST) {
      this.addWarning('cache', '未配置Redis缓存');
    } else {
      this.addCheck('redis_config', 'Redis配置存在', 'passed');
    }

    // 检查缓存策略
    const cacheConfig = this.checkFileContains('./config/cache.js', 'ttl');
    if (!cacheConfig) {
      this.addWarning('cache', '未找到缓存策略配置');
    } else {
      this.addCheck('cache_strategy', '缓存策略已配置', 'passed');
    }
  }

  /**
   * 检查日志配置
   */
  async checkLoggingConfiguration() {
    console.log('📝 检查日志配置...');

    // 检查Winston配置
    if (!fs.existsSync('./config/logger.js')) {
      this.addIssue('logging', 'high', '缺少日志配置文件');
    } else {
      this.addCheck('logger_config', '日志配置文件存在', 'passed');
    }

    // 检查日志目录
    if (!fs.existsSync('./logs')) {
      this.addWarning('logging', '日志目录不存在，将在运行时创建');
    } else {
      this.addCheck('log_directory', '日志目录存在', 'passed');
    }

    // 检查日志级别配置
    const logLevel = process.env.LOG_LEVEL || 'info';
    if (['error', 'warn', 'info'].includes(logLevel)) {
      this.addCheck('log_level', `日志级别配置正确: ${logLevel}`, 'passed');
    } else {
      this.addWarning('logging', `日志级别配置可能不当: ${logLevel}`);
    }
  }

  /**
   * 检查监控配置
   */
  async checkMonitoringConfiguration() {
    console.log('📊 检查监控配置...');

    // 检查健康检查端点
    const healthCheck = this.checkFileContains('./routes/health.js', '/health');
    if (!healthCheck) {
      this.addWarning('monitoring', '未找到健康检查端点');
    } else {
      this.addCheck('health_endpoint', '健康检查端点存在', 'passed');
    }

    // 检查监控服务
    if (fs.existsSync('./infrastructure/monitoring')) {
      this.addCheck('monitoring_service', '监控服务配置存在', 'passed');
    } else {
      this.addWarning('monitoring', '未找到监控服务配置');
    }

    // 检查告警配置
    if (process.env.ALERT_WEBHOOK_URL || process.env.ALERT_EMAIL) {
      this.addCheck('alert_config', '告警配置存在', 'passed');
    } else {
      this.addWarning('monitoring', '未配置告警通知');
    }
  }

  /**
   * 检查性能配置
   */
  async checkPerformanceConfiguration() {
    console.log('⚡ 检查性能配置...');

    // 检查压缩中间件
    const compressionConfig = this.checkFileContains('./server.js', 'compression');
    if (!compressionConfig) {
      this.addWarning('performance', '未启用响应压缩');
    } else {
      this.addCheck('compression', '响应压缩已启用', 'passed');
    }

    // 检查静态文件缓存
    const staticCache = this.checkFileContains('./server.js', 'maxAge');
    if (!staticCache) {
      this.addWarning('performance', '未配置静态文件缓存');
    } else {
      this.addCheck('static_cache', '静态文件缓存已配置', 'passed');
    }

    // 检查集群配置
    const clusterConfig = this.checkFileContains('./server.js', 'cluster');
    if (!clusterConfig && !process.env.PM2_USAGE) {
      this.addWarning('performance', '未配置集群模式，考虑使用PM2或cluster模块');
    }
  }

  /**
   * 检查Docker配置
   */
  async checkDockerConfiguration() {
    console.log('🐳 检查Docker配置...');

    // 检查Dockerfile
    if (!fs.existsSync('Dockerfile')) {
      this.addWarning('docker', '缺少Dockerfile');
    } else {
      this.addCheck('dockerfile', 'Dockerfile存在', 'passed');

      // 检查Dockerfile内容
      const dockerfileContent = fs.readFileSync('Dockerfile', 'utf8');

      if (!dockerfileContent.includes('NODE_ENV=production')) {
        this.addWarning('docker', 'Dockerfile中未设置NODE_ENV=production');
      }

      if (!dockerfileContent.includes('USER ')) {
        this.addWarning('docker', 'Dockerfile中未设置非root用户');
      }

      if (dockerfileContent.includes('npm install') && !dockerfileContent.includes('npm ci')) {
        this.addWarning('docker', '建议在Dockerfile中使用npm ci而不是npm install');
      }
    }

    // 检查docker-compose.yml
    if (!fs.existsSync('docker-compose.yml') && !fs.existsSync('docker-compose.prod.yml')) {
      this.addWarning('docker', '缺少Docker Compose配置文件');
    } else {
      this.addCheck('docker_compose', 'Docker Compose配置存在', 'passed');
    }

    // 检查.dockerignore
    if (!fs.existsSync('.dockerignore')) {
      this.addWarning('docker', '缺少.dockerignore文件');
    } else {
      this.addCheck('dockerignore', '.dockerignore文件存在', 'passed');
    }
  }

  /**
   * 检查备份策略
   */
  async checkBackupStrategy() {
    console.log('💾 检查备份策略...');

    // 检查备份脚本
    if (fs.existsSync('./scripts/backup.js') || fs.existsSync('./scripts/backup.sh')) {
      this.addCheck('backup_script', '备份脚本存在', 'passed');
    } else {
      this.addWarning('backup', '未找到备份脚本');
    }

    // 检查备份配置
    if (process.env.BACKUP_SCHEDULE || process.env.BACKUP_STORAGE) {
      this.addCheck('backup_config', '备份配置存在', 'passed');
    } else {
      this.addWarning('backup', '未配置备份策略');
    }
  }

  /**
   * 检查健康检查配置
   */
  async checkHealthCheckConfiguration() {
    console.log('🏥 检查健康检查配置...');

    // 检查健康检查脚本
    if (fs.existsSync('./scripts/health-check.js')) {
      this.addCheck('health_script', '健康检查脚本存在', 'passed');
    } else {
      this.addWarning('health', '未找到健康检查脚本');
    }

    // 检查Docker健康检查
    if (fs.existsSync('Dockerfile')) {
      const dockerfileContent = fs.readFileSync('Dockerfile', 'utf8');
      if (dockerfileContent.includes('HEALTHCHECK')) {
        this.addCheck('docker_health', 'Docker健康检查已配置', 'passed');
      } else {
        this.addWarning('health', 'Dockerfile中未配置健康检查');
      }
    }
  }

  /**
   * 检查文件是否包含特定内容
   */
  checkFileContains(filePath, content) {
    try {
      if (!fs.existsSync(filePath)) {
        return false;
      }
      const fileContent = fs.readFileSync(filePath, 'utf8');
      return fileContent.includes(content);
    } catch (error) {
      return false;
    }
  }

  /**
   * 运行命令
   */
  async runCommand(command, args) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { stdio: 'pipe' });

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', data => {
        stdout += data.toString();
      });

      process.stderr.on('data', data => {
        stderr += data.toString();
      });

      process.on('close', code => {
        if (code === 0) {
          resolve(stdout);
        } else {
          reject(new Error(stderr || `Command failed with code ${code}`));
        }
      });
    });
  }

  /**
   * 添加检查项
   */
  addCheck(id, message, status) {
    this.checks.push({ id, message, status, timestamp: new Date() });
    if (status === 'passed') {
      this.passed++;
    } else {
      this.failed++;
    }
  }

  /**
   * 添加问题
   */
  addIssue(category, severity, message) {
    this.issues.push({ category, severity, message, timestamp: new Date() });
    this.failed++;
  }

  /**
   * 添加警告
   */
  addWarning(category, message) {
    this.warnings.push({ category, message, timestamp: new Date() });
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.checks.length + this.issues.length,
        passed: this.passed,
        failed: this.failed,
        warnings: this.warnings.length,
        readiness: this.calculateReadiness(),
      },
      checks: this.checks,
      issues: this.issues,
      warnings: this.warnings,
      recommendations: this.generateRecommendations(),
    };

    // 保存报告
    const reportPath = path.join(process.cwd(), 'production-deployment-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // 生成Markdown报告
    const markdownReport = this.generateMarkdownReport(report);
    const markdownPath = path.join(process.cwd(), 'production-deployment-report.md');
    fs.writeFileSync(markdownPath, markdownReport);

    console.log(`\n📄 部署检查报告已生成:`);
    console.log(`   JSON: ${reportPath}`);
    console.log(`   Markdown: ${markdownPath}`);

    return report;
  }

  /**
   * 计算部署就绪度
   */
  calculateReadiness() {
    const criticalIssues = this.issues.filter(issue => issue.severity === 'critical').length;
    const highIssues = this.issues.filter(issue => issue.severity === 'high').length;

    if (criticalIssues > 0) {
      return 'not_ready';
    } else if (highIssues > 0) {
      return 'needs_attention';
    } else if (this.warnings.length > 5) {
      return 'ready_with_warnings';
    } else {
      return 'ready';
    }
  }

  /**
   * 生成改进建议
   */
  generateRecommendations() {
    const recommendations = [];

    const criticalIssues = this.issues.filter(issue => issue.severity === 'critical');
    if (criticalIssues.length > 0) {
      recommendations.push({
        priority: 'critical',
        message: `必须解决 ${criticalIssues.length} 个关键问题才能部署到生产环境`,
      });
    }

    const highIssues = this.issues.filter(issue => issue.severity === 'high');
    if (highIssues.length > 0) {
      recommendations.push({
        priority: 'high',
        message: `建议解决 ${highIssues.length} 个高优先级问题以提高系统稳定性`,
      });
    }

    if (this.warnings.length > 0) {
      recommendations.push({
        priority: 'medium',
        message: `有 ${this.warnings.length} 个警告项，建议在部署前处理`,
      });
    }

    // 基于检查结果生成具体建议
    const securityIssues = this.issues.filter(issue => issue.category === 'security');
    if (securityIssues.length > 0) {
      recommendations.push({
        priority: 'high',
        message: '安全配置存在问题，请优先处理安全相关配置',
      });
    }

    const envIssues = this.issues.filter(issue => issue.category === 'environment');
    if (envIssues.length > 0) {
      recommendations.push({
        priority: 'critical',
        message: '环境配置不完整，请确保所有必需的环境变量已正确设置',
      });
    }

    return recommendations;
  }

  /**
   * 生成Markdown报告
   */
  generateMarkdownReport(report) {
    let markdown = `# 生产部署检查报告\n\n`;
    markdown += `**生成时间**: ${new Date(report.timestamp).toLocaleString()}\n`;
    markdown += `**部署就绪度**: ${this.getReadinessStatus(report.summary.readiness)}\n\n`;

    // 概览
    markdown += `## 📊 检查概览\n\n`;
    markdown += `| 指标 | 数量 |\n`;
    markdown += `|------|------|\n`;
    markdown += `| 总检查项 | ${report.summary.total} |\n`;
    markdown += `| 通过 | ${report.summary.passed} |\n`;
    markdown += `| 失败 | ${report.summary.failed} |\n`;
    markdown += `| 警告 | ${report.summary.warnings} |\n\n`;

    // 关键问题
    if (report.issues.length > 0) {
      markdown += `## ❌ 需要解决的问题\n\n`;

      const issuesByCategory = {};
      report.issues.forEach(issue => {
        if (!issuesByCategory[issue.category]) {
          issuesByCategory[issue.category] = [];
        }
        issuesByCategory[issue.category].push(issue);
      });

      Object.keys(issuesByCategory).forEach(category => {
        const categoryIssues = issuesByCategory[category];
        markdown += `### ${category.charAt(0).toUpperCase() + category.slice(1)}\n\n`;

        categoryIssues.forEach((issue, index) => {
          const severity =
            issue.severity === 'critical' ? '🔴' : issue.severity === 'high' ? '🟠' : '🟡';
          markdown += `${index + 1}. ${severity} ${issue.message}\n`;
        });
        markdown += `\n`;
      });
    }

    // 警告
    if (report.warnings.length > 0) {
      markdown += `## ⚠️ 警告项\n\n`;
      report.warnings.forEach((warning, index) => {
        markdown += `${index + 1}. **${warning.category}**: ${warning.message}\n`;
      });
      markdown += `\n`;
    }

    // 通过的检查
    const passedChecks = report.checks.filter(check => check.status === 'passed');
    if (passedChecks.length > 0) {
      markdown += `## ✅ 通过的检查 (${passedChecks.length})\n\n`;
      passedChecks.forEach((check, index) => {
        markdown += `${index + 1}. ${check.message}\n`;
      });
      markdown += `\n`;
    }

    // 改进建议
    if (report.recommendations.length > 0) {
      markdown += `## 💡 改进建议\n\n`;
      report.recommendations.forEach((rec, index) => {
        const priority = rec.priority === 'critical' ? '🔴' : rec.priority === 'high' ? '🟠' : '🟡';
        markdown += `${index + 1}. ${priority} ${rec.message}\n`;
      });
    }

    return markdown;
  }

  /**
   * 获取就绪度状态描述
   */
  getReadinessStatus(readiness) {
    const statuses = {
      ready: '🟢 准备就绪',
      ready_with_warnings: '🟡 基本就绪（有警告）',
      needs_attention: '🟠 需要关注',
      not_ready: '🔴 未准备就绪',
    };

    return statuses[readiness] || readiness;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const checker = new ProductionDeploymentChecker();
    const report = await checker.runAllChecks();

    // 输出结果
    console.log('\n📈 检查结果:');
    console.log(`   部署就绪度: ${checker.getReadinessStatus(report.summary.readiness)}`);
    console.log(`   通过: ${report.summary.passed}`);
    console.log(`   失败: ${report.summary.failed}`);
    console.log(`   警告: ${report.summary.warnings}`);

    if (report.summary.readiness === 'ready') {
      console.log('\n🎉 系统已准备好部署到生产环境！');
      process.exit(0);
    } else if (report.summary.readiness === 'not_ready') {
      console.log('\n🚫 系统未准备好部署，请解决关键问题');
      process.exit(1);
    } else {
      console.log('\n⚠️  系统基本准备就绪，但建议处理警告项');
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ 生产部署检查失败:', error);
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = ProductionDeploymentChecker;
