/**
 * 回滚迁移脚本
 * 用于回滚指定的迁移脚本
 *
 * 使用方法：
 * node scripts/revert-migration.js <迁移脚本名称>
 *
 * 示例：
 * node scripts/revert-migration.js 20250504_add_json_generated_columns
 */
const path = require('path');
const fs = require('fs');
const logger = require('../config/logger');

// 获取迁移脚本名称
const migrationName = process.argv[2];

if (!migrationName) {
  logger.error('请指定迁移脚本名称');
  logger.info('使用方法: node scripts/revert-migration.js <迁移脚本名称>');
  logger.info('示例: node scripts/revert-migration.js 20250504_add_json_generated_columns');
  process.exit(1);
}

// 迁移脚本路径
const migrationPath = path.join(__dirname, '../migrations', `${migrationName}.migration.js`);

// 检查迁移脚本是否存在
if (!fs.existsSync(migrationPath)) {
  logger.error(`迁移脚本 ${migrationName} 不存在`);
  logger.info(`路径: ${migrationPath}`);
  process.exit(1);
}

// 加载迁移脚本
logger.info(`加载迁移脚本: ${migrationName}`);

// 使用安全的迁移脚本加载方式
let migration;
try {
  // 验证迁移脚本名称格式
  if (!/^[0-9]{8}_[a-zA-Z0-9_]+$/.test(migrationName)) {
    throw new Error('迁移脚本名称格式不正确，应为: YYYYMMDD_name');
  }

  // 验证路径是否在预期目录内
  const expectedDir = path.resolve(__dirname, '../migrations');
  const resolvedPath = path.resolve(migrationPath);

  if (!resolvedPath.startsWith(expectedDir)) {
    throw new Error('迁移脚本路径不在预期目录内');
  }

  // 动态导入迁移脚本
  migration = require(resolvedPath);

  // 验证迁移脚本接口
  if (typeof migration.revert !== 'function') {
    throw new Error('迁移脚本必须导出revert函数');
  }
} catch (error) {
  logger.error(`加载迁移脚本失败: ${error.message}`);
  process.exit(1);
}

// 执行回滚
logger.info(`开始回滚迁移: ${migrationName}`);
migration.revert()
  .then(success => {
    if (success) {
      logger.info(`迁移 ${migrationName} 回滚成功`);
      process.exit(0);
    } else {
      logger.error(`迁移 ${migrationName} 回滚失败`);
      process.exit(1);
    }
  })
  .catch(error => {
    logger.error(`迁移 ${migrationName} 回滚出错: ${error.message}`);
    logger.error(error.stack);
    process.exit(1);
  });
