/**
 * 端到端测试运行脚本
 * 用于运行端到端测试
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const chalk = require('chalk');
const { program } = require('commander');
const config = require('../config/e2e-test-env');

// 定义命令行选项
program
  .option('--server-url <url>', '服务器URL', `${config.server.protocol}://${config.server.host}:${config.server.port}${config.server.apiPrefix}`)
  .option('--report-format <format>', '报告格式 (json, html, junit)', 'html')
  .option('--report-file <file>', '报告文件路径')
  .option('--test-pattern <pattern>', '测试文件匹配模式', '**/*.e2e.test.{js,ts}')
  .option('--timeout <ms>', '测试超时时间(毫秒)', config.test.timeout)
  .option('--skip-server', '跳过启动测试服务器', false)
  .option('--verbose', '显示详细日志', false)
  .parse(process.argv);

const options = program.opts();

// 设置报告文件路径
const reportFormat = options.reportFormat.toLowerCase();
const reportExtension = reportFormat === 'html' ? 'html' :
                       reportFormat === 'junit' ? 'xml' : 'json';

const reportFileName = options.reportFile ||
                      path.join(config.test.reportDir,
                               `e2e-test-report-${new Date().toISOString().replace(/[:.]/g, '-')}.${reportExtension}`);

/**
 * 启动端到端测试环境
 * @returns {Promise<ChildProcess|null>} 子进程或null
 */
async function startE2eTestEnvironment() {
  if (options.skipServer) {
    console.log(chalk.yellow('跳过启动测试服务器'));
    return null;
  }

  console.log(chalk.yellow('启动端到端测试环境...'));

  try {
    const serverProcess = spawn('node', ['scripts/start-e2e-test-env.js', '--reset-db', '--verbose'], {
      cwd: path.resolve(__dirname, '..'),
      env: process.env,
      stdio: options.verbose ? 'inherit' : 'pipe'
    });

    serverProcess.on('error', (error) => {
      console.error(chalk.red('✗ 启动端到端测试环境时出错:'), error);
    });

    if (options.verbose) {
      serverProcess.stdout.on('data', (data) => {
        console.log(chalk.gray(`[测试环境] ${data.toString().trim()}`));
      });

      serverProcess.stderr.on('data', (data) => {
        console.error(chalk.red(`[测试环境] ${data.toString().trim()}`));
      });
    }

    // 等待测试环境启动
    console.log(chalk.yellow('等待测试环境启动...'));
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log(chalk.green('✓ 端到端测试环境已启动'));
    return serverProcess;
  } catch (error) {
    console.error(chalk.red('✗ 启动端到端测试环境时出错:'), error);
    return null;
  }
}

/**
 * 运行端到端测试
 * @returns {Promise<boolean>} 是否成功
 */
async function runE2eTests() {
  console.log(chalk.yellow('运行端到端测试...'));
  console.log(chalk.gray(`测试文件匹配模式: ${options.testPattern}`));
  console.log(chalk.gray(`服务器URL: ${options.serverUrl}`));

  try {
    // 构建Jest命令行参数
    const jestArgs = [
      '--config=backend/tests/e2e/jest.e2e.config.js',
      `--testMatch=${options.testPattern}`,
      `--testTimeout=${options.timeout}`,
      '--runInBand',
      '--forceExit'
    ];

    // 添加报告格式
    if (reportFormat === 'json') {
      jestArgs.push('--json', `--outputFile=${reportFileName}`);
    } else if (reportFormat === 'junit') {
      jestArgs.push('--reporters=jest-junit');
      process.env.JEST_JUNIT_OUTPUT_FILE = reportFileName;
    } else if (reportFormat === 'html') {
      jestArgs.push('--reporters=jest-html-reporter');
      process.env.JEST_HTML_REPORTER_OUTPUT_PATH = reportFileName;
    }

    // 设置环境变量
    process.env.E2E_TEST_API_URL = options.serverUrl;

    // 运行Jest
    const jestProcess = spawn('npx', ['jest', ...jestArgs, '--detectOpenHandles', '--no-cache'], {
      cwd: path.resolve(__dirname, '../..'),
      env: process.env,
      stdio: 'inherit'
    });

    // 等待Jest完成
    const exitCode = await new Promise(resolve => {
      jestProcess.on('close', resolve);
    });

    if (exitCode === 0) {
      console.log(chalk.green('✓ 端到端测试通过'));
      return true;
    } else {
      console.error(chalk.red(`✗ 端到端测试失败，退出码: ${exitCode}`));
      return false;
    }
  } catch (error) {
    console.error(chalk.red('✗ 运行端到端测试时出错:'), error);
    return false;
  }
}

// 主函数
async function main() {
  console.log(chalk.blue('端到端测试运行脚本'));

  // 启动端到端测试环境
  const serverProcess = await startE2eTestEnvironment();

  // 运行端到端测试
  const testResult = await runE2eTests();

  // 关闭端到端测试环境
  if (serverProcess) {
    console.log(chalk.yellow('关闭端到端测试环境...'));
    serverProcess.kill();
  }

  // 输出测试报告路径
  if (testResult) {
    console.log(chalk.green(`✓ 测试报告已生成: ${reportFileName}`));
  }

  // 退出进程
  process.exit(testResult ? 0 : 1);
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red('✗ 运行端到端测试时出错:'), error);
  process.exit(1);
});
