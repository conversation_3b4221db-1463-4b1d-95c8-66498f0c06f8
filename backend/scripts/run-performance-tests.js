/**
 * 性能测试运行脚本
 * 用于执行所有性能测试
 * 
 * 运行方法:
 * node backend/scripts/run-performance-tests.js [--test=load|stress|soak|concurrency] [--base-url=http://localhost:3000] [--api-version=v2]
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { program } = require('commander');

// 定义命令行选项
program
  .option('--test <type>', '要运行的测试类型: load, stress, soak, concurrency, all', 'load')
  .option('--base-url <url>', 'API基础URL', 'http://localhost:3000')
  .option('--api-version <version>', 'API版本', 'v2')
  .option('--output-dir <dir>', '输出目录', 'backend/test-results/performance')
  .option('--auth-token <token>', '认证令牌')
  .option('--quick', '快速模式（缩短测试时间）', false)
  .parse(process.argv);

const options = program.opts();

// 测试配置
const testConfigs = {
  load: {
    script: 'load-test.js',
    description: '负载测试',
    quickOptions: '--stage-duration 10s --duration 1m'
  },
  stress: {
    script: 'stress-test.js',
    description: '压力测试',
    quickOptions: '--stage-duration 10s --duration 2m'
  },
  soak: {
    script: 'soak-test.js',
    description: '长稳测试',
    quickOptions: '--stage-duration 30s --duration 5m'
  },
  concurrency: {
    script: 'concurrency-test.js',
    description: '并发测试',
    quickOptions: '--stage-duration 10s --duration 2m'
  }
};

// 创建输出目录
const outputDir = path.resolve(options.outputDir);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 获取当前时间戳
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

/**
 * 运行单个测试
 * @param {string} testType - 测试类型
 * @returns {Promise<void>}
 */
async function runTest(testType) {
  const config = testConfigs[testType];
  if (!config) {
    console.error(`未知的测试类型: ${testType}`);
    return;
  }
  
  console.log(`\n开始${config.description}...`);
  
  const scriptPath = path.resolve(__dirname, 'performance-tests', config.script);
  const outputPath = path.resolve(outputDir, `${testType}-test-${timestamp}`);
  
  // 构建环境变量
  const env = {
    ...process.env,
    BASE_URL: options.baseUrl,
    API_VERSION: options.apiVersion
  };
  
  if (options.authToken) {
    env.AUTH_TOKEN = options.authToken;
  }
  
  // 构建命令行参数
  let args = [
    'run',
    '--out', `json=${outputPath}.json`,
    '--summary-export', `${outputPath}-summary.json`
  ];
  
  // 如果是快速模式，添加快速选项
  if (options.quick && config.quickOptions) {
    args = args.concat(config.quickOptions.split(' '));
  }
  
  // 添加脚本路径
  args.push(scriptPath);
  
  // 启动k6进程
  const k6Process = spawn('k6', args, { env });
  
  // 处理输出
  k6Process.stdout.on('data', (data) => {
    process.stdout.write(data);
  });
  
  k6Process.stderr.on('data', (data) => {
    process.stderr.write(data);
  });
  
  // 等待进程完成
  return new Promise((resolve, reject) => {
    k6Process.on('close', (code) => {
      if (code === 0) {
        console.log(`${config.description}完成，结果保存在: ${outputPath}.json`);
        resolve();
      } else {
        console.error(`${config.description}失败，退出码: ${code}`);
        reject(new Error(`测试失败，退出码: ${code}`));
      }
    });
  });
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  const testTypes = options.test === 'all' 
    ? Object.keys(testConfigs) 
    : [options.test];
  
  console.log(`开始性能测试，基础URL: ${options.baseUrl}, API版本: ${options.apiVersion}`);
  console.log(`测试类型: ${testTypes.join(', ')}`);
  console.log(`输出目录: ${outputDir}`);
  
  if (options.quick) {
    console.log('快速模式: 启用');
  }
  
  for (const testType of testTypes) {
    try {
      await runTest(testType);
    } catch (error) {
      console.error(`运行${testType}测试时出错:`, error);
    }
  }
  
  console.log('\n所有测试完成!');
  console.log(`结果保存在: ${outputDir}`);
}

// 检查k6是否已安装
function checkK6Installed() {
  try {
    const result = spawn.sync('k6', ['version']);
    if (result.status === 0) {
      return true;
    }
  } catch (error) {
    return false;
  }
  return false;
}

// 主函数
async function main() {
  // 检查k6是否已安装
  if (!checkK6Installed()) {
    console.error('错误: k6未安装或不在PATH中');
    console.log('请安装k6: https://k6.io/docs/getting-started/installation/');
    process.exit(1);
  }
  
  try {
    await runAllTests();
  } catch (error) {
    console.error('运行测试时出错:', error);
    process.exit(1);
  }
}

// 执行主函数
main();
