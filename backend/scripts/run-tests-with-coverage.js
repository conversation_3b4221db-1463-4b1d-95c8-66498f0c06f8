/**
 * 运行测试并生成覆盖率报告
 * 
 * 此脚本用于运行所有测试并生成详细的覆盖率报告
 * 支持单元测试、集成测试和端到端测试
 * 
 * 使用方法:
 * node scripts/run-tests-with-coverage.js [--unit] [--integration] [--e2e] [--all]
 * 
 * 参数:
 * --unit: 只运行单元测试
 * --integration: 只运行集成测试
 * --e2e: 只运行端到端测试
 * --all: 运行所有测试 (默认)
 * --threshold=<number>: 设置覆盖率阈值，默认为80
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 解析命令行参数
const args = process.argv.slice(2);
const runUnit = args.includes('--unit') || args.includes('--all') || args.length === 0;
const runIntegration = args.includes('--integration') || args.includes('--all') || args.length === 0;
const runE2E = args.includes('--e2e') || args.includes('--all') || args.length === 0;

// 获取覆盖率阈值
let coverageThreshold = 80;
const thresholdArg = args.find(arg => arg.startsWith('--threshold='));
if (thresholdArg) {
  const threshold = parseInt(thresholdArg.split('=')[1]);
  if (!isNaN(threshold) && threshold > 0 && threshold <= 100) {
    coverageThreshold = threshold;
  }
}

// 创建覆盖率报告目录
const coverageDir = path.resolve(__dirname, '../test-reports/coverage');
if (!fs.existsSync(coverageDir)) {
  fs.mkdirSync(coverageDir, { recursive: true });
}

// 运行测试并生成覆盖率报告的函数
function runTestsWithCoverage(testType, command) {
  console.log(`\n========== 运行${testType}并生成覆盖率报告 ==========\n`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`\n✅ ${testType}运行成功\n`);
    return true;
  } catch (error) {
    console.error(`\n❌ ${testType}运行失败: ${error.message}\n`);
    return false;
  }
}

// 合并覆盖率报告的函数
function mergeCoverageReports() {
  console.log('\n========== 合并覆盖率报告 ==========\n');
  try {
    execSync('npx nyc merge .nyc_output ./test-reports/coverage/coverage-final.json', { stdio: 'inherit' });
    execSync('npx nyc report --reporter=html --reporter=text --reporter=lcov --report-dir=./test-reports/coverage', { stdio: 'inherit' });
    console.log('\n✅ 覆盖率报告合并成功\n');
    return true;
  } catch (error) {
    console.error(`\n❌ 覆盖率报告合并失败: ${error.message}\n`);
    return false;
  }
}

// 检查覆盖率是否达到阈值
function checkCoverageThreshold() {
  console.log(`\n========== 检查覆盖率是否达到阈值 (${coverageThreshold}%) ==========\n`);
  try {
    const coverageSummary = JSON.parse(fs.readFileSync(path.resolve(__dirname, '../test-reports/coverage/coverage-summary.json'), 'utf8'));
    const totalCoverage = coverageSummary.total.lines.pct;
    
    console.log(`总体行覆盖率: ${totalCoverage}%`);
    
    if (totalCoverage < coverageThreshold) {
      console.error(`\n❌ 覆盖率未达到阈值: ${totalCoverage}% < ${coverageThreshold}%\n`);
      return false;
    }
    
    console.log(`\n✅ 覆盖率达到阈值: ${totalCoverage}% >= ${coverageThreshold}%\n`);
    return true;
  } catch (error) {
    console.error(`\n❌ 检查覆盖率失败: ${error.message}\n`);
    return false;
  }
}

// 生成覆盖率报告
function generateCoverageReport() {
  console.log('\n========== 生成覆盖率报告 ==========\n');
  try {
    // 确保目录存在
    if (!fs.existsSync(path.resolve(__dirname, '../test-reports'))) {
      fs.mkdirSync(path.resolve(__dirname, '../test-reports'), { recursive: true });
    }
    
    // 生成HTML报告
    execSync('npx nyc report --reporter=html --report-dir=./test-reports/coverage/html', { stdio: 'inherit' });
    
    // 生成文本报告
    const textReport = execSync('npx nyc report --reporter=text').toString();
    fs.writeFileSync(path.resolve(__dirname, '../test-reports/coverage/coverage-report.txt'), textReport);
    
    // 生成JSON摘要
    execSync('npx nyc report --reporter=json-summary --report-dir=./test-reports/coverage', { stdio: 'inherit' });
    
    console.log('\n✅ 覆盖率报告生成成功\n');
    console.log(`报告位置: ${path.resolve(__dirname, '../test-reports/coverage')}\n`);
    return true;
  } catch (error) {
    console.error(`\n❌ 覆盖率报告生成失败: ${error.message}\n`);
    return false;
  }
}

// 主函数
async function main() {
  console.log(`\n========== 开始运行测试并生成覆盖率报告 ==========\n`);
  console.log(`覆盖率阈值: ${coverageThreshold}%`);
  
  let success = true;
  
  // 清理之前的覆盖率数据
  if (fs.existsSync(path.resolve(__dirname, '../.nyc_output'))) {
    fs.rmSync(path.resolve(__dirname, '../.nyc_output'), { recursive: true, force: true });
  }
  
  // 运行单元测试
  if (runUnit) {
    success = runTestsWithCoverage('单元测试', 'npx jest --config=jest.config.js --testPathPattern="tests/unit" --coverage') && success;
  }
  
  // 运行集成测试
  if (runIntegration) {
    success = runTestsWithCoverage('集成测试', 'npx jest --config=jest.config.js --testPathPattern="tests/integration" --coverage') && success;
  }
  
  // 运行端到端测试
  if (runE2E) {
    success = runTestsWithCoverage('端到端测试', 'npx jest --config=tests/e2e/jest.e2e.config.js --coverage') && success;
  }
  
  // 合并覆盖率报告
  if (success) {
    success = mergeCoverageReports() && success;
  }
  
  // 生成覆盖率报告
  if (success) {
    success = generateCoverageReport() && success;
  }
  
  // 检查覆盖率是否达到阈值
  if (success) {
    success = checkCoverageThreshold() && success;
  }
  
  if (success) {
    console.log('\n✅ 所有测试运行成功，覆盖率报告生成成功\n');
    process.exit(0);
  } else {
    console.error('\n❌ 测试运行或覆盖率报告生成过程中出现错误\n');
    process.exit(1);
  }
}

// 执行主函数
main().catch(error => {
  console.error(`运行测试时发生错误: ${error.message}`);
  process.exit(1);
});
