/**
 * 数据库迁移脚本：净化已存储的数据
 * 用于防止XSS攻击，净化数据库中已存储的用户输入数据
 */
const { sanitizeHtml } = require('../utils/html-sanitizer');
const { Note, User, LearningPlan, Tag, TagCategory, Exercise, Insight } = require('../models');
const logger = require('../config/logger');

/**
 * 净化已存储的数据
 */
async function sanitizeExistingData() {
  console.log('开始净化已存储的数据...');
  
  try {
    // 净化笔记数据
    const notes = await Note.findAll();
    console.log(`找到 ${notes.length} 条笔记记录`);
    
    let sanitizedCount = 0;
    for (const note of notes) {
      const originalTitle = note.title;
      const originalContent = note.content;
      
      note.title = sanitizeHtml(note.title);
      note.content = sanitizeHtml(note.content, true); // 允许部分HTML
      
      if (originalTitle !== note.title || originalContent !== note.content) {
        await note.save();
        sanitizedCount++;
      }
    }
    console.log(`笔记数据净化完成，修改了 ${sanitizedCount} 条记录`);
    
    // 净化用户数据
    const users = await User.findAll();
    console.log(`找到 ${users.length} 条用户记录`);
    
    sanitizedCount = 0;
    for (const user of users) {
      const originalNickname = user.nickname;
      
      user.nickname = sanitizeHtml(user.nickname);
      
      if (originalNickname !== user.nickname) {
        await user.save();
        sanitizedCount++;
      }
    }
    console.log(`用户数据净化完成，修改了 ${sanitizedCount} 条记录`);
    
    // 净化学习计划数据
    const plans = await LearningPlan.findAll();
    console.log(`找到 ${plans.length} 条学习计划记录`);
    
    sanitizedCount = 0;
    for (const plan of plans) {
      const originalTitle = plan.title;
      const originalDescription = plan.description;
      
      plan.title = sanitizeHtml(plan.title);
      plan.description = sanitizeHtml(plan.description, true); // 允许部分HTML
      
      if (originalTitle !== plan.title || originalDescription !== plan.description) {
        await plan.save();
        sanitizedCount++;
      }
    }
    console.log(`学习计划数据净化完成，修改了 ${sanitizedCount} 条记录`);
    
    // 净化标签数据
    const tags = await Tag.findAll();
    console.log(`找到 ${tags.length} 条标签记录`);
    
    sanitizedCount = 0;
    for (const tag of tags) {
      const originalName = tag.name;
      const originalDescription = tag.description;
      
      tag.name = sanitizeHtml(tag.name);
      tag.description = sanitizeHtml(tag.description);
      
      if (originalName !== tag.name || originalDescription !== tag.description) {
        await tag.save();
        sanitizedCount++;
      }
    }
    console.log(`标签数据净化完成，修改了 ${sanitizedCount} 条记录`);
    
    // 净化标签分类数据
    const categories = await TagCategory.findAll();
    console.log(`找到 ${categories.length} 条标签分类记录`);
    
    sanitizedCount = 0;
    for (const category of categories) {
      const originalName = category.name;
      const originalDescription = category.description;
      
      category.name = sanitizeHtml(category.name);
      category.description = sanitizeHtml(category.description);
      
      if (originalName !== category.name || originalDescription !== category.description) {
        await category.save();
        sanitizedCount++;
      }
    }
    console.log(`标签分类数据净化完成，修改了 ${sanitizedCount} 条记录`);
    
    // 净化练习数据
    const exercises = await Exercise.findAll();
    console.log(`找到 ${exercises.length} 条练习记录`);
    
    sanitizedCount = 0;
    for (const exercise of exercises) {
      const originalTitle = exercise.title;
      const originalDescription = exercise.description;
      const originalExpectedResult = exercise.expected_result;
      
      exercise.title = sanitizeHtml(exercise.title);
      exercise.description = sanitizeHtml(exercise.description, true); // 允许部分HTML
      exercise.expected_result = sanitizeHtml(exercise.expected_result, true); // 允许部分HTML
      
      if (originalTitle !== exercise.title || 
          originalDescription !== exercise.description || 
          originalExpectedResult !== exercise.expected_result) {
        await exercise.save();
        sanitizedCount++;
      }
    }
    console.log(`练习数据净化完成，修改了 ${sanitizedCount} 条记录`);
    
    // 净化观点数据
    const insights = await Insight.findAll();
    console.log(`找到 ${insights.length} 条观点记录`);
    
    sanitizedCount = 0;
    for (const insight of insights) {
      const originalTitle = insight.title;
      const originalContent = insight.content;
      
      insight.title = sanitizeHtml(insight.title);
      insight.content = sanitizeHtml(insight.content, true); // 允许部分HTML
      
      if (originalTitle !== insight.title || originalContent !== insight.content) {
        await insight.save();
        sanitizedCount++;
      }
    }
    console.log(`观点数据净化完成，修改了 ${sanitizedCount} 条记录`);
    
    console.log('所有数据净化完成');
  } catch (error) {
    console.error('净化数据时出错:', error);
    logger.error(`净化数据时出错: ${error.message}`);
  }
}

// 执行净化
sanitizeExistingData()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('净化数据时出错:', error);
    process.exit(1);
  });
