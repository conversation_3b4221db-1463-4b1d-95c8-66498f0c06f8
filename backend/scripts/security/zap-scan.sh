#!/bin/bash

# OWASP ZAP自动扫描脚本
# 此脚本用于自动化运行OWASP ZAP扫描，并生成扫描报告
# 
# 使用方法:
# ./zap-scan.sh [选项]
#
# 选项:
#   --target <URL>       要扫描的目标URL (默认: http://localhost:9090)
#   --api-target <URL>   要扫描的API目标URL (默认: http://localhost:9090/api/v2)
#   --format <FORMAT>    报告格式 (html, xml, json, md) (默认: html)
#   --level <LEVEL>      扫描级别 (1-3) (默认: 2)
#   --docker             使用Docker运行ZAP (默认)
#   --local              使用本地安装的ZAP
#   --ci                 CI模式，失败时返回非零退出码
#   --help               显示帮助信息

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认值
TARGET="http://localhost:9090"
API_TARGET="http://localhost:9090/api/v2"
FORMAT="html"
LEVEL=2
USE_DOCKER=true
CI_MODE=false
ZAP_DOCKER_IMAGE="owasp/zap2docker-stable"
REPORT_DIR="$(pwd)/security-reports/zap"
DATE_SUFFIX=$(date +"%Y-%m-%d")
CONTEXT_FILE="zap-context.context"
API_SCAN=false

# 解析参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --target)
      TARGET="$2"
      shift 2
      ;;
    --api-target)
      API_TARGET="$2"
      API_SCAN=true
      shift 2
      ;;
    --format)
      FORMAT="$2"
      shift 2
      ;;
    --level)
      LEVEL="$2"
      shift 2
      ;;
    --docker)
      USE_DOCKER=true
      shift
      ;;
    --local)
      USE_DOCKER=false
      shift
      ;;
    --ci)
      CI_MODE=true
      shift
      ;;
    --help)
      echo "OWASP ZAP自动扫描脚本"
      echo "使用方法: ./zap-scan.sh [选项]"
      echo ""
      echo "选项:"
      echo "  --target <URL>       要扫描的目标URL (默认: http://localhost:9090)"
      echo "  --api-target <URL>   要扫描的API目标URL (默认: http://localhost:9090/api/v2)"
      echo "  --format <FORMAT>    报告格式 (html, xml, json, md) (默认: html)"
      echo "  --level <LEVEL>      扫描级别 (1-3) (默认: 2)"
      echo "  --docker             使用Docker运行ZAP (默认)"
      echo "  --local              使用本地安装的ZAP"
      echo "  --ci                 CI模式，失败时返回非零退出码"
      echo "  --help               显示帮助信息"
      exit 0
      ;;
    *)
      echo -e "${RED}错误: 未知选项 $1${NC}"
      exit 1
      ;;
  esac
done

# 创建报告目录
mkdir -p "$REPORT_DIR"

# 设置报告文件名
if [ "$API_SCAN" = true ]; then
  REPORT_NAME="zap-api-scan-report-$DATE_SUFFIX"
else
  REPORT_NAME="zap-scan-report-$DATE_SUFFIX"
fi

REPORT_FILE="$REPORT_DIR/$REPORT_NAME.$FORMAT"

echo -e "${BLUE}=== OWASP ZAP 安全扫描 ===${NC}"
echo -e "${YELLOW}目标URL: $TARGET${NC}"
if [ "$API_SCAN" = true ]; then
  echo -e "${YELLOW}API目标URL: $API_TARGET${NC}"
fi
echo -e "${YELLOW}扫描级别: $LEVEL${NC}"
echo -e "${YELLOW}报告格式: $FORMAT${NC}"
echo -e "${YELLOW}报告文件: $REPORT_FILE${NC}"

# 检查Docker是否可用（如果使用Docker）
if [ "$USE_DOCKER" = true ]; then
  if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装或不可用${NC}"
    exit 1
  fi
  
  # 拉取ZAP Docker镜像
  echo -e "${BLUE}拉取ZAP Docker镜像...${NC}"
  docker pull "$ZAP_DOCKER_IMAGE"
fi

# 运行ZAP扫描
echo -e "${BLUE}开始ZAP扫描...${NC}"

if [ "$USE_DOCKER" = true ]; then
  if [ "$API_SCAN" = true ]; then
    # API扫描
    docker run --rm -v "$REPORT_DIR:/zap/wrk/:rw" -t "$ZAP_DOCKER_IMAGE" zap-api-scan.py \
      -t "$API_TARGET" \
      -f "$FORMAT" \
      -r "/zap/wrk/$REPORT_NAME.$FORMAT" \
      -l "$LEVEL" \
      -I
  else
    # 常规扫描
    docker run --rm -v "$REPORT_DIR:/zap/wrk/:rw" -t "$ZAP_DOCKER_IMAGE" zap-baseline.py \
      -t "$TARGET" \
      -g "/zap/wrk/$CONTEXT_FILE" \
      -r "/zap/wrk/$REPORT_NAME.$FORMAT" \
      -l "$LEVEL" \
      -I
  fi
  
  SCAN_EXIT_CODE=$?
else
  # 使用本地ZAP
  if ! command -v zap.sh &> /dev/null; then
    echo -e "${RED}错误: 本地ZAP未安装或不在PATH中${NC}"
    exit 1
  fi
  
  if [ "$API_SCAN" = true ]; then
    # API扫描
    zap-api-scan.py -t "$API_TARGET" -f "$FORMAT" -r "$REPORT_FILE" -l "$LEVEL" -I
  else
    # 常规扫描
    zap-baseline.py -t "$TARGET" -g "$CONTEXT_FILE" -r "$REPORT_FILE" -l "$LEVEL" -I
  fi
  
  SCAN_EXIT_CODE=$?
fi

# 处理扫描结果
if [ $SCAN_EXIT_CODE -eq 0 ]; then
  echo -e "${GREEN}ZAP扫描完成，未发现问题${NC}"
elif [ $SCAN_EXIT_CODE -eq 1 ]; then
  echo -e "${YELLOW}ZAP扫描完成，发现低风险问题${NC}"
elif [ $SCAN_EXIT_CODE -eq 2 ]; then
  echo -e "${YELLOW}ZAP扫描完成，发现中等风险问题${NC}"
elif [ $SCAN_EXIT_CODE -eq 3 ]; then
  echo -e "${RED}ZAP扫描完成，发现高风险问题${NC}"
else
  echo -e "${RED}ZAP扫描失败，退出码: $SCAN_EXIT_CODE${NC}"
fi

echo -e "${BLUE}扫描报告已保存到: $REPORT_FILE${NC}"

# 在CI模式下，如果发现高风险问题，则返回非零退出码
if [ "$CI_MODE" = true ] && [ $SCAN_EXIT_CODE -eq 3 ]; then
  echo -e "${RED}CI模式: 发现高风险问题，构建失败${NC}"
  exit 1
fi

# 返回扫描退出码
exit $SCAN_EXIT_CODE
