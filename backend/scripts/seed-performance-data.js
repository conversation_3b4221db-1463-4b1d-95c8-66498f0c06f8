/**
 * 性能测试数据填充脚本
 * 用于生成性能测试所需的大量测试数据
 */

const { sequelize } = require('../config/database');
const { User } = require('../models/user.model');
const { LearningPlan } = require('../models/learning-plan.model');
const { Note } = require('../models/note.model');
const { Tag } = require('../models/tag.model');
const { BubbleInteraction } = require('../models/bubble-interaction.model');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const chalk = require('chalk');
const { program } = require('commander');

// 定义命令行选项
program
  .option('--users <count>', '用户数量', '100')
  .option('--plans <count>', '学习计划数量', '500')
  .option('--notes <count>', '笔记数量', '1000')
  .option('--tags <count>', '标签数量', '200')
  .option('--interactions <count>', '互动数量', '5000')
  .option('--clean', '清空现有数据', false)
  .option('--verbose', '显示详细日志', false)
  .parse(process.argv);

const options = program.opts();

// 转换选项为数字
const userCount = parseInt(options.users, 10);
const planCount = parseInt(options.plans, 10);
const noteCount = parseInt(options.notes, 10);
const tagCount = parseInt(options.tags, 10);
const interactionCount = parseInt(options.interactions, 10);

// 随机数据生成工具
const randomTools = {
  // 生成随机字符串
  randomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // 生成随机邮箱
  randomEmail: () => {
    const domains = ['example.com', 'test.com', 'perf.test', 'aibubb.com', 'demo.org'];
    return `perf-test-${randomTools.randomString(8)}@${domains[Math.floor(Math.random() * domains.length)]}`;
  },
  
  // 生成随机名称
  randomName: () => {
    const firstNames = ['张', '李', '王', '赵', '刘', '陈', '杨', '黄', '周', '吴', '郑', '冯', '朱', '秦', '许'];
    const lastNames = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '涛', '明', '超'];
    return `${firstNames[Math.floor(Math.random() * firstNames.length)]}${lastNames[Math.floor(Math.random() * lastNames.length)]}`;
  },
  
  // 生成随机日期
  randomDate: (start = new Date(2023, 0, 1), end = new Date()) => {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  },
  
  // 从数组中随机选择一个元素
  randomPick: (array) => {
    return array[Math.floor(Math.random() * array.length)];
  },
  
  // 从数组中随机选择多个元素
  randomMultiPick: (array, count = 3) => {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, array.length));
  },
  
  // 生成随机整数
  randomInt: (min, max) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },
  
  // 生成随机布尔值
  randomBool: (trueProbability = 0.5) => {
    return Math.random() < trueProbability;
  }
};

// 数据生成函数
const dataGenerators = {
  // 生成用户数据
  generateUsers: async (count) => {
    console.log(chalk.yellow(`生成${count}个用户...`));
    
    const users = [];
    const password = await bcrypt.hash('password123', 10);
    
    // 创建性能测试专用用户
    users.push({
      name: '性能测试用户',
      email: '<EMAIL>',
      password,
      role: 'user',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // 创建其他随机用户
    for (let i = 1; i < count; i++) {
      users.push({
        name: randomTools.randomName(),
        email: randomTools.randomEmail(),
        password,
        role: randomTools.randomPick(['user', 'user', 'user', 'premium', 'admin']),
        status: randomTools.randomPick(['active', 'active', 'active', 'inactive', 'suspended']),
        createdAt: randomTools.randomDate(),
        updatedAt: new Date()
      });
      
      if (options.verbose && i % 100 === 0) {
        console.log(chalk.gray(`已生成${i}个用户...`));
      }
    }
    
    return users;
  },
  
  // 生成标签数据
  generateTags: (count) => {
    console.log(chalk.yellow(`生成${count}个标签...`));
    
    const tags = [];
    const categories = ['学习', '工作', '生活', '技术', '兴趣', '项目', '其他'];
    
    for (let i = 0; i < count; i++) {
      tags.push({
        name: `标签-${randomTools.randomString(8)}`,
        category: randomTools.randomPick(categories),
        createdAt: randomTools.randomDate(),
        updatedAt: new Date()
      });
      
      if (options.verbose && i % 100 === 0) {
        console.log(chalk.gray(`已生成${i}个标签...`));
      }
    }
    
    return tags;
  },
  
  // 生成学习计划数据
  generateLearningPlans: (count, userIds) => {
    console.log(chalk.yellow(`生成${count}个学习计划...`));
    
    const plans = [];
    
    for (let i = 0; i < count; i++) {
      plans.push({
        title: `学习计划-${randomTools.randomString(10)}`,
        description: `这是一个性能测试学习计划描述-${randomTools.randomString(50)}`,
        userId: randomTools.randomPick(userIds),
        targetDays: randomTools.randomInt(7, 90),
        dailyGoalMinutes: randomTools.randomInt(15, 120),
        isPublic: randomTools.randomBool(0.7),
        status: randomTools.randomPick(['active', 'active', 'active', 'completed', 'abandoned']),
        createdAt: randomTools.randomDate(),
        updatedAt: new Date()
      });
      
      if (options.verbose && i % 100 === 0) {
        console.log(chalk.gray(`已生成${i}个学习计划...`));
      }
    }
    
    return plans;
  },
  
  // 生成笔记数据
  generateNotes: (count, userIds, tagIds) => {
    console.log(chalk.yellow(`生成${count}个笔记...`));
    
    const notes = [];
    
    for (let i = 0; i < count; i++) {
      const noteTags = randomTools.randomMultiPick(tagIds, randomTools.randomInt(0, 5));
      
      notes.push({
        content: `这是一个性能测试笔记内容-${randomTools.randomString(100)}`,
        userId: randomTools.randomPick(userIds),
        tags: noteTags.join(','),
        isPublic: randomTools.randomBool(0.6),
        createdAt: randomTools.randomDate(),
        updatedAt: new Date()
      });
      
      if (options.verbose && i % 100 === 0) {
        console.log(chalk.gray(`已生成${i}个笔记...`));
      }
    }
    
    return notes;
  },
  
  // 生成泡泡互动数据
  generateBubbleInteractions: (count, userIds) => {
    console.log(chalk.yellow(`生成${count}个泡泡互动...`));
    
    const interactions = [];
    const interactionTypes = ['view', 'click', 'share', 'like', 'comment', 'complete'];
    const devicePlatforms = ['mobile', 'desktop', 'tablet'];
    const deviceOSs = ['iOS', 'Android', 'Windows', 'macOS', 'Linux'];
    
    for (let i = 0; i < count; i++) {
      const devicePlatform = randomTools.randomPick(devicePlatforms);
      const deviceOS = randomTools.randomPick(deviceOSs);
      
      interactions.push({
        userId: randomTools.randomPick(userIds),
        bubbleId: uuidv4(),
        interactionType: randomTools.randomPick(interactionTypes),
        deviceInfo: JSON.stringify({
          platform: devicePlatform,
          os: deviceOS,
          model: `${deviceOS}-${randomTools.randomString(5)}`,
          appVersion: `${randomTools.randomInt(1, 5)}.${randomTools.randomInt(0, 9)}.${randomTools.randomInt(0, 9)}`
        }),
        contextData: JSON.stringify({
          view: `${randomTools.randomPick(['home', 'detail', 'profile', 'search', 'category'])}-view`,
          sessionDuration: randomTools.randomInt(10, 600),
          referrer: randomTools.randomPick(['direct', 'search', 'social', 'notification', 'email'])
        }),
        createdAt: randomTools.randomDate(),
        updatedAt: new Date()
      });
      
      if (options.verbose && i % 1000 === 0) {
        console.log(chalk.gray(`已生成${i}个泡泡互动...`));
      }
    }
    
    return interactions;
  }
};

// 主函数
async function main() {
  try {
    console.log(chalk.blue('开始生成性能测试数据...'));
    console.log(chalk.gray(`用户数量: ${userCount}`));
    console.log(chalk.gray(`学习计划数量: ${planCount}`));
    console.log(chalk.gray(`笔记数量: ${noteCount}`));
    console.log(chalk.gray(`标签数量: ${tagCount}`));
    console.log(chalk.gray(`互动数量: ${interactionCount}`));
    
    // 开始事务
    const transaction = await sequelize.transaction();
    
    try {
      // 如果需要清空现有数据
      if (options.clean) {
        console.log(chalk.yellow('清空现有数据...'));
        await BubbleInteraction.destroy({ truncate: true, cascade: true, transaction });
        await Note.destroy({ truncate: true, cascade: true, transaction });
        await LearningPlan.destroy({ truncate: true, cascade: true, transaction });
        await Tag.destroy({ truncate: true, cascade: true, transaction });
        await User.destroy({ truncate: true, cascade: true, transaction });
        console.log(chalk.green('✓ 数据清空完成'));
      }
      
      // 生成用户
      const users = await dataGenerators.generateUsers(userCount);
      await User.bulkCreate(users, { transaction });
      console.log(chalk.green(`✓ 已创建${users.length}个用户`));
      
      // 获取用户ID
      const userRecords = await User.findAll({ attributes: ['id'], transaction });
      const userIds = userRecords.map(user => user.id);
      
      // 生成标签
      const tags = dataGenerators.generateTags(tagCount);
      await Tag.bulkCreate(tags, { transaction });
      console.log(chalk.green(`✓ 已创建${tags.length}个标签`));
      
      // 获取标签ID
      const tagRecords = await Tag.findAll({ attributes: ['id'], transaction });
      const tagIds = tagRecords.map(tag => tag.id);
      
      // 生成学习计划
      const plans = dataGenerators.generateLearningPlans(planCount, userIds);
      await LearningPlan.bulkCreate(plans, { transaction });
      console.log(chalk.green(`✓ 已创建${plans.length}个学习计划`));
      
      // 生成笔记
      const notes = dataGenerators.generateNotes(noteCount, userIds, tagIds);
      await Note.bulkCreate(notes, { transaction });
      console.log(chalk.green(`✓ 已创建${notes.length}个笔记`));
      
      // 生成泡泡互动
      const interactions = dataGenerators.generateBubbleInteractions(interactionCount, userIds);
      await BubbleInteraction.bulkCreate(interactions, { transaction });
      console.log(chalk.green(`✓ 已创建${interactions.length}个泡泡互动`));
      
      // 提交事务
      await transaction.commit();
      console.log(chalk.green('\n✓ 所有数据生成完成'));
      
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
    
  } catch (error) {
    console.error(chalk.red('✗ 生成测试数据时出错:'), error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行主函数
main();
