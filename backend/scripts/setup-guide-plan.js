/**
 * 完整的初始化流程脚本
 * 按顺序执行所有必要的步骤，创建系统默认学习计划
 */
const initDatabase = require('./init-db');
const { up: addSystemDefaultField } = require('../migrations/add_system_default_to_learning_plan');
const createSystemDefaultPlan = require('./create_system_default_plan');
const logger = require('../config/logger');

async function setupGuidePlan() {
  try {
    // 步骤1: 初始化数据库表结构
    logger.info('步骤1: 初始化数据库表结构');
    const dbInitSuccess = await initDatabase();
    if (!dbInitSuccess) {
      throw new Error('数据库初始化失败');
    }

    // 步骤2: 添加is_system_default字段
    logger.info('步骤2: 添加is_system_default字段');
    await addSystemDefaultField();

    // 步骤3: 创建系统默认学习计划
    logger.info('步骤3: 创建系统默认学习计划');
    await createSystemDefaultPlan();

    logger.info('系统默认学习计划设置完成');
    return true;
  } catch (error) {
    logger.error(`设置系统默认学习计划失败: ${error.message}`);
    return false;
  }
}

// 如果直接运行此脚本，执行设置流程
if (require.main === module) {
  setupGuidePlan()
    .then(success => {
      if (success) {
        console.log('系统默认学习计划设置成功');
        process.exit(0);
      } else {
        console.error('系统默认学习计划设置失败');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(`设置过程出错: ${error.message}`);
      process.exit(1);
    });
}

module.exports = setupGuidePlan;
