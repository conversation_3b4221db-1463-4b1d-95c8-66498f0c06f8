/**
 * 简单事件处理器测试脚本
 * 用于测试事件处理器和WebSocket通知功能
 */

// 导入依赖
const logger = require('../config/logger');
const webSocketService = require('../services/websocket.service');

// 导入事件处理器
const { ExerciseCreatedEventHandler } = require('../application/events/content/exercise/ExerciseCreatedEventHandler');
const { NoteCreatedEventHandler } = require('../application/events/content/note/NoteCreatedEventHandler');
const { LearningPlanCreatedEventHandler } = require('../application/events/content/learningPlan/LearningPlanCreatedEventHandler');

// 导入事件
const { ExerciseCreatedEvent } = require('../domain/events/content/exercise/ExerciseCreatedEvent');
const { NoteCreatedEvent } = require('../domain/events/content/note/NoteCreatedEvent');
const { LearningPlanCreatedEvent } = require('../domain/events/content/learningPlan/LearningPlanCreatedEvent');

// 创建事件处理器实例
const exerciseCreatedEventHandler = new ExerciseCreatedEventHandler(webSocketService, logger);
const noteCreatedEventHandler = new NoteCreatedEventHandler(webSocketService, logger);
const learningPlanCreatedEventHandler = new LearningPlanCreatedEventHandler(webSocketService, logger);

// 测试函数
async function runTests() {
  try {
    console.log('开始测试事件处理器...');

    // 创建测试事件
    const exerciseCreatedEvent = new ExerciseCreatedEvent(
      1001, // aggregateId
      '测试练习', // title
      1, // creatorId
      true // isPublic
    );

    const noteCreatedEvent = new NoteCreatedEvent(
      2001, // aggregateId
      '测试笔记', // title
      '1' // userId
    );

    const learningPlanCreatedEvent = new LearningPlanCreatedEvent(
      3001, // aggregateId
      '测试学习计划', // title
      1, // userId
      true // isPublic
    );

    // 处理事件
    console.log('处理练习创建事件...');
    await exerciseCreatedEventHandler.handle(exerciseCreatedEvent);

    // 等待一段时间，确保事件处理完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('处理笔记创建事件...');
    await noteCreatedEventHandler.handle(noteCreatedEvent);

    // 等待一段时间，确保事件处理完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('处理学习计划创建事件...');
    await learningPlanCreatedEventHandler.handle(learningPlanCreatedEvent);

    console.log('事件处理器测试完成');
  } catch (error) {
    console.error(`测试事件处理器失败: ${error.message}`, {
      error: error.stack
    });
  }
}

// 执行测试
runTests()
  .then(() => {
    console.log('测试脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error(`测试脚本执行失败: ${error.message}`, {
      error: error.stack
    });
    process.exit(1);
  });
