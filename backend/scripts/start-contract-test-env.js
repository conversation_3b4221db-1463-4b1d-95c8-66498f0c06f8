/**
 * API契约测试环境启动脚本
 * 用于启动API契约测试环境，包括契约测试服务器和数据准备
 * 
 * 使用方法:
 * node backend/scripts/start-contract-test-env.js [--port=9092] [--mock-mode] [--reset-db]
 */

const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const { program } = require('commander');
const chalk = require('chalk');
const dotenv = require('dotenv');

// 定义命令行选项
program
  .option('--port <port>', '服务器端口', '9092')
  .option('--mock-mode', '使用模拟数据模式', false)
  .option('--reset-db', '重置测试数据库', false)
  .option('--verbose', '显示详细日志', false)
  .parse(process.argv);

const options = program.opts();

// 加载环境变量
const envPath = path.resolve(__dirname, '../.env.test');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(chalk.green('✓ 已加载测试环境配置'));
} else {
  console.warn(chalk.yellow('⚠ 测试环境配置文件不存在，将使用默认配置'));
  dotenv.config();
}

// 设置环境变量
process.env.NODE_ENV = 'test';
process.env.CONTRACT_TEST_PORT = options.port;
process.env.CONTRACT_TEST_USE_MOCKS = options.mockMode ? 'true' : 'false';

// 准备测试数据
function prepareTestData() {
  console.log(chalk.yellow('准备测试数据...'));
  
  try {
    // 生成测试数据
    const generateDataResult = spawn.sync('node', ['backend/scripts/generate-test-data.js'], {
      stdio: options.verbose ? 'inherit' : 'pipe'
    });
    
    if (generateDataResult.status !== 0) {
      console.error(chalk.red('✗ 生成测试数据失败'));
      return false;
    }
    
    console.log(chalk.green('✓ 测试数据准备完成'));
    return true;
  } catch (error) {
    console.error(chalk.red('✗ 准备测试数据时出错:'), error);
    return false;
  }
}

// 启动契约测试服务器
function startContractTestServer() {
  console.log(chalk.yellow('启动API契约测试服务器...'));
  
  const serverArgs = [
    'backend/scripts/contract-test-server.js',
    `--port=${options.port}`
  ];
  
  if (options.mockMode) {
    serverArgs.push('--mock-mode');
  }
  
  if (options.resetDb) {
    serverArgs.push('--reset-db');
  }
  
  if (options.verbose) {
    serverArgs.push('--verbose');
  }
  
  const serverProcess = spawn('node', serverArgs, {
    stdio: 'inherit'
  });
  
  serverProcess.on('error', (error) => {
    console.error(chalk.red('✗ 启动API契约测试服务器时出错:'), error);
    process.exit(1);
  });
  
  return serverProcess;
}

// 主函数
async function main() {
  console.log(chalk.blue('启动API契约测试环境...'));
  console.log(chalk.gray(`端口: ${options.port}`));
  console.log(chalk.gray(`模式: ${options.mockMode ? '模拟数据' : '实际API'}`));
  
  // 准备测试数据
  const dataReady = prepareTestData();
  if (!dataReady) {
    console.error(chalk.red('✗ 准备测试数据失败，无法启动API契约测试环境'));
    process.exit(1);
  }
  
  // 启动契约测试服务器
  const serverProcess = startContractTestServer();
  
  // 处理进程退出
  process.on('SIGINT', () => {
    console.log(chalk.yellow('\n接收到中断信号，正在关闭服务...'));
    serverProcess.kill();
    process.exit(0);
  });
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red('✗ 启动API契约测试环境时出错:'), error);
  process.exit(1);
});
