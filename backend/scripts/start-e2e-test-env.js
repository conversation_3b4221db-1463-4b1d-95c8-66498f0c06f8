/**
 * 端到端测试环境启动脚本
 * 用于启动端到端测试环境，包括API服务器和测试数据库
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const chalk = require('chalk');
const { program } = require('commander');
const config = require('../config/e2e-test-env');

// 定义命令行选项
program
  .option('--port <port>', '服务器端口', config.server.port)
  .option('--env <environment>', '环境 (development, test, production)', 'test')
  .option('--reset-db', '重置测试数据库', false)
  .option('--no-mock', '不使用模拟服务', false)
  .option('--verbose', '显示详细日志', false)
  .parse(process.argv);

const options = program.opts();

// 设置环境变量
process.env.NODE_ENV = options.env;
process.env.E2E_TEST_PORT = options.port;
process.env.E2E_TEST_MOCK_ENABLED = options.mock ? 'true' : 'false';

/**
 * 准备测试数据库
 * @returns {Promise<boolean>} 是否成功
 */
async function prepareTestDatabase() {
  console.log(chalk.yellow('准备测试数据库...'));
  
  try {
    // 如果需要重置数据库
    if (options.resetDb) {
      console.log(chalk.yellow('重置测试数据库...'));
      
      // 运行迁移脚本
      console.log(chalk.yellow('运行数据库迁移...'));
      const migrateResult = spawn.sync('node', ['scripts/migrate.js', '--force'], {
        cwd: path.resolve(__dirname, '..'),
        env: process.env,
        stdio: options.verbose ? 'inherit' : 'pipe'
      });

      if (migrateResult.status !== 0) {
        console.error(chalk.red(`✗ 数据库迁移失败，退出码: ${migrateResult.status}`));
        return false;
      }

      console.log(chalk.green('✓ 数据库迁移成功'));

      // 运行种子脚本
      console.log(chalk.yellow('运行数据库种子...'));
      const seedResult = spawn.sync('node', ['scripts/seed.js'], {
        cwd: path.resolve(__dirname, '..'),
        env: process.env,
        stdio: options.verbose ? 'inherit' : 'pipe'
      });

      if (seedResult.status !== 0) {
        console.error(chalk.red(`✗ 数据库种子失败，退出码: ${seedResult.status}`));
        return false;
      }

      console.log(chalk.green('✓ 数据库种子成功'));
    }
    
    return true;
  } catch (error) {
    console.error(chalk.red('✗ 准备测试数据库时出错:'), error);
    return false;
  }
}

/**
 * 生成API文档
 * @returns {boolean} 是否成功
 */
function generateApiDocs() {
  console.log(chalk.yellow('生成API文档...'));
  
  try {
    const generateDocsResult = spawn.sync('node', ['scripts/generate-swagger.js'], {
      cwd: path.resolve(__dirname, '..'),
      env: process.env,
      stdio: options.verbose ? 'inherit' : 'pipe'
    });
    
    if (generateDocsResult.status !== 0) {
      console.warn(chalk.yellow('⚠ 生成API文档失败，但将继续启动端到端测试环境'));
      return false;
    }
    
    console.log(chalk.green('✓ API文档生成成功'));
    return true;
  } catch (error) {
    console.warn(chalk.yellow('⚠ 生成API文档时出错，但将继续启动端到端测试环境:'), error);
    return false;
  }
}

/**
 * 启动模拟服务
 * @returns {Promise<ChildProcess|null>} 子进程或null
 */
async function startMockServices() {
  if (!options.mock) {
    return null;
  }
  
  console.log(chalk.yellow('启动模拟服务...'));
  
  try {
    const mockProcess = spawn('node', ['scripts/start-mock-server.js'], {
      cwd: path.resolve(__dirname, '..'),
      env: {
        ...process.env,
        MOCK_SERVER_PORT: parseInt(options.port) + 1
      },
      stdio: options.verbose ? 'inherit' : 'pipe'
    });
    
    mockProcess.on('error', (error) => {
      console.error(chalk.red('✗ 启动模拟服务时出错:'), error);
    });
    
    if (options.verbose) {
      mockProcess.stdout.on('data', (data) => {
        console.log(chalk.gray(`[模拟服务] ${data.toString().trim()}`));
      });
      
      mockProcess.stderr.on('data', (data) => {
        console.error(chalk.red(`[模拟服务] ${data.toString().trim()}`));
      });
    }
    
    // 等待模拟服务启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log(chalk.green(`✓ 模拟服务已启动，端口: ${parseInt(options.port) + 1}`));
    return mockProcess;
  } catch (error) {
    console.error(chalk.red('✗ 启动模拟服务时出错:'), error);
    return null;
  }
}

/**
 * 启动端到端测试服务器
 * @returns {ChildProcess} 子进程
 */
function startE2eTestServer() {
  console.log(chalk.yellow('启动端到端测试服务器...'));
  
  const serverProcess = spawn('node', ['server.js'], {
    cwd: path.resolve(__dirname, '..'),
    env: {
      ...process.env,
      PORT: options.port,
      NODE_ENV: options.env,
      E2E_TEST_MODE: 'true'
    },
    stdio: options.verbose ? 'inherit' : 'pipe'
  });
  
  serverProcess.on('error', (error) => {
    console.error(chalk.red('✗ 启动端到端测试服务器时出错:'), error);
  });
  
  if (options.verbose) {
    serverProcess.stdout.on('data', (data) => {
      console.log(chalk.gray(`[服务器] ${data.toString().trim()}`));
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(chalk.red(`[服务器] ${data.toString().trim()}`));
    });
  }
  
  console.log(chalk.green(`✓ 端到端测试服务器已启动，端口: ${options.port}`));
  return serverProcess;
}

// 主函数
async function main() {
  console.log(chalk.blue('启动端到端测试环境...'));
  console.log(chalk.gray(`环境: ${options.env}`));
  console.log(chalk.gray(`端口: ${options.port}`));
  console.log(chalk.gray(`模拟服务: ${options.mock ? '启用' : '禁用'}`));
  console.log(chalk.gray(`重置数据库: ${options.resetDb ? '是' : '否'}`));

  // 准备测试数据库
  const dbReady = await prepareTestDatabase();
  if (!dbReady) {
    console.error(chalk.red('✗ 准备测试数据库失败，无法启动端到端测试环境'));
    process.exit(1);
  }

  // 生成API文档
  const docsReady = generateApiDocs();
  if (!docsReady) {
    console.warn(chalk.yellow('⚠ 生成API文档失败，但将继续启动端到端测试环境'));
  }

  // 启动模拟服务
  let mockProcess = null;
  if (options.mock) {
    mockProcess = await startMockServices();
  }

  // 启动端到端测试服务器
  const serverProcess = startE2eTestServer();

  // 处理进程退出
  process.on('SIGINT', () => {
    console.log(chalk.yellow('\n接收到中断信号，正在关闭服务...'));
    serverProcess.kill();
    if (mockProcess) {
      mockProcess.kill();
    }
    process.exit(0);
  });

  console.log(chalk.green('\n✓ 端到端测试环境已启动'));
  console.log(chalk.blue(`API服务器: ${config.server.protocol}://${config.server.host}:${options.port}${config.server.apiPrefix}`));
  console.log(chalk.blue(`API文档: ${config.server.protocol}://${config.server.host}:${options.port}/api-docs`));
  console.log(chalk.blue(`健康检查: ${config.server.protocol}://${config.server.host}:${options.port}/health`));
  
  if (options.mock) {
    console.log(chalk.blue(`模拟服务: ${config.server.protocol}://${config.server.host}:${parseInt(options.port) + 1}`));
  }
  
  console.log(chalk.yellow('\n按 Ctrl+C 停止服务'));
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red('✗ 启动端到端测试环境时出错:'), error);
  process.exit(1);
});
