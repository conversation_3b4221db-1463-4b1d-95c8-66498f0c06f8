/**
 * 集成测试环境启动脚本
 * 用于启动集成测试环境，包括API服务器和模拟服务
 *
 * 运行方法:
 * node backend/scripts/start-integration-env.js [--port=3001] [--reset-db] [--no-mock] [--verbose]
 */

const path = require('path');
const fs = require('fs');
const { spawn, execSync } = require('child_process');
const dotenv = require('dotenv');
const { program } = require('commander');
const chalk = require('chalk');

// 定义命令行选项
program
  .option('--port <port>', '服务器端口', '3001')
  .option('--reset-db', '重置测试数据库', false)
  .option('--no-mock', '不启动模拟服务', false)
  .option('--verbose', '显示详细日志', false)
  .option('--env <env>', '环境 (test, development)', 'test')
  .parse(process.argv);

const options = program.opts();

// 加载集成测试环境配置
const envPath = path.resolve(__dirname, '../config/integration-test.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(chalk.green('✓ 已加载集成测试环境配置'));
} else {
  console.warn(chalk.yellow('⚠ 集成测试环境配置文件不存在，将使用默认配置'));

  // 尝试加载测试环境配置
  const testEnvPath = path.resolve(__dirname, '../.env.test');
  if (fs.existsSync(testEnvPath)) {
    dotenv.config({ path: testEnvPath });
    console.log(chalk.green('✓ 已加载测试环境配置'));
  } else {
    console.warn(chalk.yellow('⚠ 测试环境配置文件不存在，将使用默认环境变量'));
    dotenv.config();
  }
}

// 设置环境变量
process.env.NODE_ENV = options.env;
process.env.PORT = options.port;
process.env.INTEGRATION_TEST_MODE = 'true';
process.env.MOCK_EXTERNAL_SERVICES = options.mock ? 'true' : 'false';

// 检查数据库连接
function checkDatabaseConnection() {
  console.log(chalk.blue('检查数据库连接...'));

  try {
    // 尝试连接数据库
    const checkResult = spawn.sync('node', ['-e', `
      const mysql = require('mysql2/promise');
      async function checkConnection() {
        const connection = await mysql.createConnection({
          host: process.env.DB_HOST || 'localhost',
          port: process.env.DB_PORT || 3306,
          user: process.env.DB_USER || 'aibubb_test',
          password: process.env.DB_PASSWORD || 'test_password',
          database: process.env.DB_NAME || 'aibubb_test'
        });
        await connection.execute('SELECT 1');
        await connection.end();
        console.log('数据库连接成功');
      }
      checkConnection().catch(err => {
        console.error('数据库连接失败:', err.message);
        process.exit(1);
      });
    `], {
      env: process.env,
      stdio: options.verbose ? 'inherit' : 'pipe'
    });

    if (checkResult.status !== 0) {
      console.error(chalk.red('✗ 数据库连接失败'));
      return false;
    }

    console.log(chalk.green('✓ 数据库连接成功'));
    return true;
  } catch (error) {
    console.error(chalk.red('✗ 检查数据库连接时出错:'), error);
    return false;
  }
}

// 启动API服务器
function startApiServer() {
  console.log(chalk.blue('启动API服务器...'));

  const serverProcess = spawn('node', ['server.js'], {
    cwd: path.resolve(__dirname, '..'),
    env: process.env,
    stdio: 'inherit'
  });

  serverProcess.on('error', (error) => {
    console.error(chalk.red('✗ 启动API服务器时出错:'), error);
    process.exit(1);
  });

  serverProcess.on('close', (code) => {
    if (code !== 0 && code !== null) {
      console.error(chalk.red(`✗ API服务器异常退出，退出码: ${code}`));
      process.exit(code);
    }
  });

  // 等待服务器启动
  console.log(chalk.yellow('等待API服务器启动...'));

  return new Promise((resolve) => {
    // 等待2秒，确保服务器有足够时间启动
    setTimeout(() => {
      console.log(chalk.green('✓ API服务器已启动'));
      resolve(serverProcess);
    }, 2000);
  });
}

// 启动模拟服务
function startMockServices() {
  console.log(chalk.blue('启动模拟服务...'));

  // 创建模拟服务目录
  const mockDir = path.resolve(__dirname, '../mocks');
  if (!fs.existsSync(mockDir)) {
    console.log(chalk.yellow('⚠ 模拟服务目录不存在，创建目录'));
    fs.mkdirSync(mockDir, { recursive: true });
  }

  // 检查模拟服务器文件是否存在
  const mockServerPath = path.resolve(mockDir, 'server.js');
  if (!fs.existsSync(mockServerPath)) {
    console.warn(chalk.yellow('⚠ 模拟服务器文件不存在，创建基本模拟服务器'));

    // 创建基本的模拟服务器文件
    const mockServerContent = `
/**
 * 模拟服务器
 * 用于模拟外部服务和依赖
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.MOCK_PORT || 3002;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 日志中间件
app.use((req, res, next) => {
  console.log(\`[\${new Date().toISOString()}] \${req.method} \${req.url}\`);
  next();
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'mock-server' });
});

// 加载模拟路由
const routesDir = path.join(__dirname, 'routes');
if (fs.existsSync(routesDir)) {
  fs.readdirSync(routesDir).forEach(file => {
    if (file.endsWith('.js')) {
      const route = require(path.join(routesDir, file));
      app.use(route);
    }
  });
}

// 默认路由
app.get('/api/mock', (req, res) => {
  res.json({
    success: true,
    message: '模拟服务器正在运行',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(\`模拟服务器已启动: http://localhost:\${PORT}\`);
});
`;

    fs.writeFileSync(mockServerPath, mockServerContent);
    console.log(chalk.green('✓ 已创建基本模拟服务器文件'));

    // 创建模拟路由目录
    const mockRoutesDir = path.resolve(mockDir, 'routes');
    if (!fs.existsSync(mockRoutesDir)) {
      fs.mkdirSync(mockRoutesDir, { recursive: true });
      console.log(chalk.green('✓ 已创建模拟路由目录'));
    }
  }

  // 启动模拟服务
  const mockProcess = spawn('node', ['mocks/server.js'], {
    cwd: path.resolve(__dirname, '..'),
    env: process.env,
    stdio: options.verbose ? 'inherit' : 'pipe'
  });

  mockProcess.on('error', (error) => {
    console.error(chalk.red('✗ 启动模拟服务时出错:'), error);
    // 不退出主进程，模拟服务不是必需的
  });

  // 等待模拟服务启动
  console.log(chalk.yellow('等待模拟服务启动...'));

  return new Promise((resolve) => {
    // 等待1秒，确保服务器有足够时间启动
    setTimeout(() => {
      console.log(chalk.green('✓ 模拟服务已启动'));
      resolve(mockProcess);
    }, 1000);
  });
}

// 准备测试数据库
async function prepareTestDatabase() {
  console.log(chalk.blue('准备测试数据库...'));

  // 检查数据库连接
  const dbConnected = checkDatabaseConnection();
  if (!dbConnected) {
    console.error(chalk.red('✗ 数据库连接失败，无法准备测试数据库'));
    return false;
  }

  // 如果需要重置数据库
  if (options.resetDb) {
    console.log(chalk.yellow('重置测试数据库...'));

    try {
      // 运行迁移脚本
      console.log(chalk.yellow('运行数据库迁移...'));
      const migrateResult = spawn.sync('node', ['scripts/migrate.js', '--force'], {
        cwd: path.resolve(__dirname, '..'),
        env: process.env,
        stdio: options.verbose ? 'inherit' : 'pipe'
      });

      if (migrateResult.status !== 0) {
        console.error(chalk.red(`✗ 数据库迁移失败，退出码: ${migrateResult.status}`));
        return false;
      }

      console.log(chalk.green('✓ 数据库迁移成功'));

      // 运行种子脚本
      console.log(chalk.yellow('运行数据库种子...'));
      const seedResult = spawn.sync('node', ['scripts/seed.js'], {
        cwd: path.resolve(__dirname, '..'),
        env: process.env,
        stdio: options.verbose ? 'inherit' : 'pipe'
      });

      if (seedResult.status !== 0) {
        console.error(chalk.red(`✗ 数据库种子失败，退出码: ${seedResult.status}`));
        return false;
      }

      console.log(chalk.green('✓ 数据库种子成功'));
    } catch (error) {
      console.error(chalk.red('✗ 重置数据库时出错:'), error);
      return false;
    }
  } else if (process.env.AUTO_SEED_DATA === 'true') {
    // 如果启用了自动种子数据，但不重置数据库
    console.log(chalk.yellow('运行数据库种子...'));

    try {
      const seedResult = spawn.sync('node', ['scripts/seed.js'], {
        cwd: path.resolve(__dirname, '..'),
        env: process.env,
        stdio: options.verbose ? 'inherit' : 'pipe'
      });

      if (seedResult.status !== 0) {
        console.error(chalk.red(`✗ 数据库种子失败，退出码: ${seedResult.status}`));
        return false;
      }

      console.log(chalk.green('✓ 数据库种子成功'));
    } catch (error) {
      console.error(chalk.red('✗ 运行数据库种子时出错:'), error);
      return false;
    }
  }

  console.log(chalk.green('✓ 测试数据库准备完成'));
  return true;
}

// 生成API文档
function generateApiDocs() {
  console.log(chalk.blue('生成API文档...'));

  try {
    // 确保swagger.json文件存在
    const swaggerJsonPath = path.resolve(__dirname, '../config/swagger.json');
    if (!fs.existsSync(swaggerJsonPath)) {
      console.warn(chalk.yellow('⚠ swagger.json文件不存在，将在服务器启动时生成'));
    } else {
      console.log(chalk.green('✓ swagger.json文件已存在'));
    }

    return true;
  } catch (error) {
    console.error(chalk.red('✗ 生成API文档时出错:'), error);
    return false;
  }
}

// 主函数
async function main() {
  console.log(chalk.blue('启动集成测试环境...'));
  console.log(chalk.gray(`环境: ${options.env}`));
  console.log(chalk.gray(`端口: ${options.port}`));
  console.log(chalk.gray(`模拟服务: ${options.mock ? '启用' : '禁用'}`));
  console.log(chalk.gray(`重置数据库: ${options.resetDb ? '是' : '否'}`));

  // 准备测试数据库
  const dbReady = await prepareTestDatabase();
  if (!dbReady) {
    console.error(chalk.red('✗ 准备测试数据库失败，无法启动集成测试环境'));
    process.exit(1);
  }

  // 生成API文档
  const docsReady = generateApiDocs();
  if (!docsReady) {
    console.warn(chalk.yellow('⚠ 生成API文档失败，但将继续启动集成测试环境'));
  }

  // 启动模拟服务
  let mockProcess = null;
  if (options.mock) {
    mockProcess = await startMockServices();
  }

  // 启动API服务器
  const apiServer = await startApiServer();

  // 处理进程退出
  process.on('SIGINT', () => {
    console.log(chalk.yellow('\n接收到中断信号，正在关闭服务...'));

    if (apiServer) {
      apiServer.kill();
    }

    if (mockProcess) {
      mockProcess.kill();
    }

    console.log(chalk.green('✓ 服务已关闭'));
    process.exit(0);
  });

  console.log(chalk.green(`
集成测试环境已启动!
API服务器: http://localhost:${options.port}
API文档: http://localhost:${options.port}/api-docs
健康检查: http://localhost:${options.port}/health
${options.mock ? `模拟服务: http://localhost:${process.env.MOCK_PORT || 3002}` : ''}

按Ctrl+C停止服务
  `));
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red('✗ 启动集成测试环境时出错:'), error);
  process.exit(1);
});
