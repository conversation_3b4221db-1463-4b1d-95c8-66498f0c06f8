/**
 * AI服务测试脚本
 *
 * 此脚本用于测试AI服务的基本功能，包括连接测试和标签生成
 *
 * 使用方法:
 * node backend/scripts/test-ai-service.js
 */

const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
// 导入AI服务单例
const aiService = require('../services/ai.service');

// 测试函数
async function testAIService() {
  console.log(`\n===== AI服务测试 =====`);
  console.log(`提供商: ${aiService.provider}`);
  console.log(`模型: ${aiService.model}`);

  try {
    // 测试标签生成
    console.log(`\n----- 测试标签生成 -----`);
    const plan = {
      title: '提升演讲能力',
      description: '通过系统学习和实践，提升公开演讲的能力和自信心'
    };

    console.log(`为学习计划 "${plan.title}" 生成标签...`);
    const startTime = Date.now();
    const tags = await aiService.generateTags(plan, 5);
    const endTime = Date.now();

    console.log(`生成成功! 耗时: ${endTime - startTime}ms`);
    console.log(`生成的标签:`);
    tags.forEach((tag, index) => {
      console.log(`${index + 1}. ${tag.name} (相关性: ${tag.relevanceScore.toFixed(2)})`);
    });

    // 测试学习计划生成
    console.log(`\n----- 测试学习计划生成 -----`);
    console.log(`为 "${plan.title}" 生成学习计划内容...`);
    const planStartTime = Date.now();
    const planContent = await aiService.generateLearningPlanContent(plan);
    const planEndTime = Date.now();

    console.log(`生成成功! 耗时: ${planEndTime - planStartTime}ms`);
    console.log(`生成的学习计划内容:`);
    console.log(`- 标题: ${planContent.enhancedTitle}`);
    console.log(`- 设计原则: ${planContent.designPrinciple.substring(0, 100)}...`);
    console.log(`- 天数: ${planContent.contentPlan.length}天`);
    console.log(`- 标签数: ${planContent.tags.length}个`);

    // 打印使用统计
    const stats = aiService.getUsageStats();
    console.log(`\n----- 使用统计 -----`);
    console.log(`请求次数: ${stats.requestCount}`);
    console.log(`错误次数: ${stats.errorCount}`);
    console.log(`错误率: ${(stats.errorRate * 100).toFixed(2)}%`);
    console.log(`Token使用:`);
    console.log(`- 输入: ${stats.tokenUsage.prompt}`);
    console.log(`- 输出: ${stats.tokenUsage.completion}`);
    console.log(`- 总计: ${stats.tokenUsage.total}`);

  } catch (error) {
    console.error(`测试失败: ${error.message}`);
  }
}

// 执行测试
testAIService();
