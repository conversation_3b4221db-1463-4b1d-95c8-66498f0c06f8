/**
 * 阿里云百炼API连接测试脚本
 * 使用OpenAI SDK调用阿里云百炼API
 *
 * 使用方法：
 * 1. 确保已设置环境变量DASHSCOPE_API_KEY
 * 2. 运行：node backend/scripts/test-aliyun-connection.js
 */

const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const OpenAI = require('openai');

// 检查环境变量
if (!process.env.DASHSCOPE_API_KEY) {
  console.error('错误: 未设置DASHSCOPE_API_KEY环境变量');
  console.error('请在.env文件中设置DASHSCOPE_API_KEY=sk-your-api-key');
  process.exit(1);
}

// 配置
const apiKey = process.env.DASHSCOPE_API_KEY;
const apiUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1";
const model = process.env.DASHSCOPE_API_MODEL || "qwen-plus-latest"; // 从环境变量中读取模型ID
const enableThinking = process.env.DASHSCOPE_ENABLE_THINKING === 'true'; // 是否启用思考模式

console.log('阿里云百炼API配置信息:');
console.log('API Key:', apiKey ? '已设置' : '未设置');
console.log('API URL:', apiUrl);
console.log('Model:', model);
console.log('思考模式:', enableThinking ? '已启用' : '未启用');

// 测试函数
async function testAliyunConnection() {
  try {
    console.log('\n正在测试阿里云百炼API连接...');

    // 创建OpenAI客户端
    const openai = new OpenAI({
      apiKey: apiKey,
      baseURL: apiUrl
    });

    console.log('发送请求到:', apiUrl);
    console.log('使用模型:', model);

    // 构建请求参数
    const requestParams = {
      model: model,
      messages: [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "你好，请生成3个关于'人际沟通'的标签，每个标签2-4个汉字，用逗号分隔" }
      ],
      temperature: 0.7,
      max_tokens: 100
    };

    // 如果是qwen-plus-latest或qwen-plus-2025-04-28模型，可以启用思考模式
    if ((model === 'qwen-plus-latest' || model === 'qwen-plus-2025-04-28') && enableThinking) {
      requestParams.enable_thinking = true;
      console.log('已启用阿里云百炼思考模式');
    }

    // 发送请求
    const completion = await openai.chat.completions.create(requestParams);

    // 输出响应
    console.log('阿里云百炼API响应成功!');
    console.log('响应数据:', JSON.stringify(completion, null, 2));

    // 解析标签
    if (completion.choices && completion.choices[0] && completion.choices[0].message) {
      const content = completion.choices[0].message.content;
      console.log('生成的内容:', content);

      // 先尝试按中文顿号、逗号分割，如果有多个标签
      let tags = content.includes('、') || content.includes(',') || content.includes('，') ?
        content.split(/[,，、]/).map(tag => tag.trim()).filter(tag => tag.length > 0) :
        // 如果没有分隔符，尝试按行分割
        content.split('\n').map(tag => tag.trim()).filter(tag => tag.length > 0);

      // 移除可能的编号和引号
      tags = tags.map(tag => tag.replace(/^(\d+\.|-|\*)\s+/, ''))
                 .map(tag => tag.replace(/^["'](.*)[\"']$/, '$1'));

      console.log('解析的标签:', tags);
    }

    // 输出token使用情况
    if (completion.usage) {
      console.log('Token使用情况:');
      console.log('- 输入tokens:', completion.usage.prompt_tokens);
      console.log('- 输出tokens:', completion.usage.completion_tokens);
      console.log('- 总tokens:', completion.usage.total_tokens);
    }

  } catch (error) {
    console.error('测试失败:', error.message);

    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', JSON.stringify(error.response.data, null, 2));
    }

    // 提供错误解决建议
    console.log('\n可能的解决方案:');
    console.log('1. 检查API密钥是否正确');
    console.log('2. 检查API URL是否正确');
    console.log('3. 检查网络连接');
    console.log('4. 检查模型名称是否正确');
    console.log('5. 确认阿里云账户是否有足够的余额');
  }
}

// 执行测试
testAliyunConnection();
