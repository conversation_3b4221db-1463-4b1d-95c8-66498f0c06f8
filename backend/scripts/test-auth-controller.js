/**
 * 认证控制器测试脚本
 * 用于测试认证控制器的功能
 * 
 * 使用方法:
 * node backend/scripts/test-auth-controller.js [选项]
 * 
 * 选项:
 *   --legacy           使用旧版认证控制器 [默认: false]
 *   --enhanced         使用增强版认证控制器 [默认: true]
 *   --verbose          显示详细日志 [默认: true]
 *   -h, --help         显示帮助信息
 */

const { program } = require('commander');
const chalk = require('chalk');
const express = require('express');
const request = require('supertest');
const bodyParser = require('body-parser');
const logger = require('../config/logger');

// 定义命令行选项
program
  .option('--legacy', '使用旧版认证控制器', false)
  .option('--enhanced', '使用增强版认证控制器', true)
  .option('--verbose', '显示详细日志', true)
  .helpOption('-h, --help', '显示帮助信息')
  .parse(process.argv);

const options = program.opts();

// 创建测试用户数据
const testUser = {
  phone: '13800138000',
  password: 'password123',
  nickname: '测试用户'
};

// 创建测试应用
const createTestApp = (controller) => {
  const app = express();
  app.use(bodyParser.json());
  
  // 注册路由
  app.post('/auth/register/phone', controller.registerWithPhone);
  app.post('/auth/login/phone', controller.loginWithPhone);
  app.post('/auth/logout', controller.logout);
  app.post('/auth/refresh-token', controller.refreshToken);
  
  return app;
};

// 测试认证控制器
const testAuthController = async (controller, name) => {
  console.log(chalk.blue(`\n测试${name}认证控制器...`));
  
  const app = createTestApp(controller);
  
  // 测试手机号注册
  console.log(chalk.blue('\n测试手机号注册...'));
  const registerResponse = await request(app)
    .post('/auth/register/phone')
    .send(testUser)
    .expect(200);
  
  console.log(chalk.green('✓ 手机号注册成功'));
  if (options.verbose) {
    console.log(chalk.blue('响应:'));
    console.log(registerResponse.body);
  }
  
  // 测试手机号登录
  console.log(chalk.blue('\n测试手机号登录...'));
  const loginResponse = await request(app)
    .post('/auth/login/phone')
    .send({
      phone: testUser.phone,
      password: testUser.password
    })
    .expect(200);
  
  console.log(chalk.green('✓ 手机号登录成功'));
  if (options.verbose) {
    console.log(chalk.blue('响应:'));
    console.log(loginResponse.body);
  }
  
  // 测试登出
  console.log(chalk.blue('\n测试登出...'));
  const logoutResponse = await request(app)
    .post('/auth/logout')
    .set('Authorization', `Bearer ${loginResponse.body.data.token}`)
    .expect(200);
  
  console.log(chalk.green('✓ 登出成功'));
  if (options.verbose) {
    console.log(chalk.blue('响应:'));
    console.log(logoutResponse.body);
  }
};

// 主函数
async function main() {
  try {
    console.log(chalk.blue('开始执行认证控制器测试...'));
    
    // 测试旧版认证控制器
    if (options.legacy) {
      const authController = require('../controllers/auth.controller');
      await testAuthController(authController, '旧版');
    }
    
    // 测试增强版认证控制器
    if (options.enhanced) {
      const authController = require('../controllers/auth.controller.compatibility');
      await testAuthController(authController, '增强版');
    }
    
    console.log(chalk.blue('\n认证控制器测试完成'));
    process.exit(0);
  } catch (error) {
    console.error(chalk.red(`认证控制器测试失败: ${error.message}`));
    process.exit(1);
  }
}

// 执行主函数
main();
