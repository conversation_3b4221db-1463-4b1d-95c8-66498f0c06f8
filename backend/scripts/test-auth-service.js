/**
 * 认证服务测试脚本
 * 用于测试认证服务的功能
 * 
 * 使用方法:
 * node backend/scripts/test-auth-service.js [选项]
 * 
 * 选项:
 *   --legacy           使用旧版认证服务 [默认: false]
 *   --enhanced         使用增强版认证服务 [默认: true]
 *   --verbose          显示详细日志 [默认: true]
 *   -h, --help         显示帮助信息
 */

const { program } = require('commander');
const chalk = require('chalk');
const logger = require('../config/logger');

// 定义命令行选项
program
  .option('--legacy', '使用旧版认证服务', false)
  .option('--enhanced', '使用增强版认证服务', true)
  .option('--verbose', '显示详细日志', true)
  .helpOption('-h, --help', '显示帮助信息')
  .parse(process.argv);

const options = program.opts();

// 创建测试用户数据
const testUser = {
  phone: '13800138000',
  password: 'password123',
  nickname: '测试用户'
};

// 测试认证服务
const testAuthService = async (service, name) => {
  console.log(chalk.blue(`\n测试${name}认证服务...`));
  
  // 测试手机号注册
  console.log(chalk.blue('\n测试手机号注册...'));
  try {
    const registerResult = await service.registerWithPhone(
      testUser.phone,
      testUser.password,
      testUser.nickname
    );
    
    console.log(chalk.green('✓ 手机号注册成功'));
    if (options.verbose) {
      console.log(chalk.blue('结果:'));
      console.log(registerResult);
    }
    
    // 测试手机号登录
    console.log(chalk.blue('\n测试手机号登录...'));
    const loginResult = await service.loginWithPhone(
      testUser.phone,
      testUser.password
    );
    
    console.log(chalk.green('✓ 手机号登录成功'));
    if (options.verbose) {
      console.log(chalk.blue('结果:'));
      console.log(loginResult);
    }
    
    // 测试刷新令牌
    if (loginResult.refreshToken) {
      console.log(chalk.blue('\n测试刷新令牌...'));
      const refreshResult = await service.refreshToken(loginResult.refreshToken);
      
      console.log(chalk.green('✓ 刷新令牌成功'));
      if (options.verbose) {
        console.log(chalk.blue('结果:'));
        console.log(refreshResult);
      }
    }
    
    // 测试登出
    console.log(chalk.blue('\n测试登出...'));
    const logoutResult = await service.logout(loginResult.token);
    
    console.log(chalk.green('✓ 登出成功'));
    if (options.verbose) {
      console.log(chalk.blue('结果:'));
      console.log(logoutResult);
    }
  } catch (error) {
    console.error(chalk.red(`✗ 测试失败: ${error.message}`));
    if (options.verbose && error.stack) {
      console.error(chalk.red(error.stack));
    }
  }
};

// 主函数
async function main() {
  try {
    console.log(chalk.blue('开始执行认证服务测试...'));
    
    // 测试旧版认证服务
    if (options.legacy) {
      const userService = require('../services/user.service');
      await testAuthService(userService, '旧版');
    }
    
    // 测试增强版认证服务
    if (options.enhanced) {
      const authService = require('../services/auth.service');
      await testAuthService(authService, '增强版');
    }
    
    console.log(chalk.blue('\n认证服务测试完成'));
    process.exit(0);
  } catch (error) {
    console.error(chalk.red(`认证服务测试失败: ${error.message}`));
    process.exit(1);
  }
}

// 执行主函数
main();
