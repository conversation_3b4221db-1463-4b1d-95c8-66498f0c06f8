/**
 * 缓存预热测试脚本
 * 用于测试缓存预热服务的功能
 * 
 * 使用方法:
 * node backend/scripts/test-cache-warmup.js [选项]
 * 
 * 选项:
 *   --legacy           使用旧版缓存预热服务 [默认: false]
 *   --enhanced         使用增强版缓存预热服务 [默认: true]
 *   --level <level>    预热级别 (minimal, standard, full) [默认: "minimal"]
 *   --verbose          显示详细日志 [默认: true]
 *   -h, --help         显示帮助信息
 */

const { program } = require('commander');
const chalk = require('chalk');
const { connectRedis, closeRedis } = require('../config/redis');
const logger = require('../config/logger');

// 定义命令行选项
program
  .option('--legacy', '使用旧版缓存预热服务', false)
  .option('--enhanced', '使用增强版缓存预热服务', true)
  .option('--level <level>', '预热级别 (minimal, standard, full)', 'minimal')
  .option('--verbose', '显示详细日志', true)
  .helpOption('-h, --help', '显示帮助信息')
  .parse(process.argv);

const options = program.opts();

// 主函数
async function main() {
  try {
    console.log(chalk.blue('开始执行缓存预热测试...'));
    
    // 连接Redis
    console.log(chalk.blue('连接Redis...'));
    const redisConnected = await connectRedis();
    
    if (!redisConnected) {
      console.error(chalk.red('Redis连接失败，无法执行缓存预热测试'));
      process.exit(1);
    }
    
    console.log(chalk.green('Redis连接成功'));
    
    // 测试旧版缓存预热服务
    if (options.legacy) {
      console.log(chalk.blue('\n测试旧版缓存预热服务...'));
      const legacyCacheWarmupService = require('../services/cache-warmup.service');
      
      console.time('旧版缓存预热');
      const legacySuccess = await legacyCacheWarmupService.warmup();
      console.timeEnd('旧版缓存预热');
      
      if (legacySuccess) {
        console.log(chalk.green('✓ 旧版缓存预热成功'));
      } else {
        console.error(chalk.red('✗ 旧版缓存预热失败'));
      }
    }
    
    // 测试增强版缓存预热服务
    if (options.enhanced) {
      console.log(chalk.blue('\n测试增强版缓存预热服务...'));
      const enhancedCacheWarmupService = require('../services/enhanced-cache-warmup.service');
      
      console.time('增强版缓存预热');
      const enhancedResults = await enhancedCacheWarmupService.warmup({
        level: options.level,
        incremental: false,
        background: false
      });
      console.timeEnd('增强版缓存预热');
      
      if (enhancedResults.success) {
        console.log(chalk.green(`✓ 增强版缓存预热成功，级别: ${enhancedResults.level}`));
        
        if (options.verbose) {
          console.log(chalk.blue('\n预热结果:'));
          
          for (const [item, result] of Object.entries(enhancedResults.items)) {
            if (result.success) {
              console.log(chalk.green(`✓ ${item}: ${result.count}条记录，耗时: ${result.duration}ms`));
            } else {
              console.log(chalk.red(`✗ ${item}: 失败 - ${result.error}`));
            }
          }
          
          console.log(chalk.blue(`\n总耗时: ${enhancedResults.duration}ms`));
        }
      } else {
        console.error(chalk.red(`✗ 增强版缓存预热失败: ${enhancedResults.error}`));
      }
    }
    
    // 关闭Redis连接
    await closeRedis();
    
    console.log(chalk.blue('\n缓存预热测试完成'));
    process.exit(0);
  } catch (error) {
    console.error(chalk.red(`缓存预热测试失败: ${error.message}`));
    
    // 尝试关闭Redis连接
    try {
      await closeRedis();
    } catch (e) {
      // 忽略关闭错误
    }
    
    process.exit(1);
  }
}

// 执行主函数
main();
