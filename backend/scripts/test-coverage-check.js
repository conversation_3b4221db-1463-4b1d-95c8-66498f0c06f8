/**
 * 测试覆盖率检查脚本
 * 用于检查测试覆盖率并生成报告
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 覆盖率阈值配置
const COVERAGE_THRESHOLDS = {
  global: {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80,
  },
  critical: {
    // 关键模块需要更高的覆盖率
    'services/learningPlanV2.service.js': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    'controllers/learningPlanV2.controller.js': {
      branches: 85,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    'middlewares/': {
      branches: 80,
      functions: 85,
      lines: 80,
      statements: 80,
    },
  },
};

/**
 * 运行测试并生成覆盖率报告
 */
async function runCoverageTest() {
  console.log('🧪 开始运行测试覆盖率检查...\n');

  return new Promise((resolve, reject) => {
    const jestProcess = spawn(
      'npx',
      [
        'jest',
        '--coverage',
        '--coverageReporters=json',
        '--coverageReporters=text',
        '--coverageReporters=html',
      ],
      {
        stdio: 'inherit',
        cwd: process.cwd(),
      },
    );

    jestProcess.on('close', code => {
      if (code === 0) {
        console.log('\n✅ 测试执行完成');
        resolve();
      } else {
        console.error('\n❌ 测试执行失败');
        reject(new Error(`Jest exited with code ${code}`));
      }
    });
  });
}

/**
 * 分析覆盖率报告
 */
function analyzeCoverage() {
  const coverageFile = path.join(process.cwd(), 'coverage', 'coverage-final.json');

  if (!fs.existsSync(coverageFile)) {
    throw new Error('覆盖率报告文件不存在');
  }

  const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
  const results = {
    total: {
      statements: { covered: 0, total: 0 },
      branches: { covered: 0, total: 0 },
      functions: { covered: 0, total: 0 },
      lines: { covered: 0, total: 0 },
    },
    files: {},
    passed: true,
    issues: [],
  };

  // 计算总体覆盖率
  Object.keys(coverage).forEach(filePath => {
    const file = coverage[filePath];
    const relativePath = path.relative(process.cwd(), filePath);

    // 跳过测试文件和node_modules
    if (relativePath.includes('__tests__') || relativePath.includes('node_modules')) {
      return;
    }

    // 累计统计
    results.total.statements.covered += file.s
      ? Object.values(file.s).filter(v => v > 0).length
      : 0;
    results.total.statements.total += file.s ? Object.keys(file.s).length : 0;

    results.total.branches.covered += file.b
      ? Object.values(file.b)
          .flat()
          .filter(v => v > 0).length
      : 0;
    results.total.branches.total += file.b ? Object.values(file.b).flat().length : 0;

    results.total.functions.covered += file.f ? Object.values(file.f).filter(v => v > 0).length : 0;
    results.total.functions.total += file.f ? Object.keys(file.f).length : 0;

    // 文件级别统计
    const fileStats = {
      statements: file.s
        ? {
            covered: Object.values(file.s).filter(v => v > 0).length,
            total: Object.keys(file.s).length,
          }
        : { covered: 0, total: 0 },
      branches: file.b
        ? {
            covered: Object.values(file.b)
              .flat()
              .filter(v => v > 0).length,
            total: Object.values(file.b).flat().length,
          }
        : { covered: 0, total: 0 },
      functions: file.f
        ? {
            covered: Object.values(file.f).filter(v => v > 0).length,
            total: Object.keys(file.f).length,
          }
        : { covered: 0, total: 0 },
    };

    results.files[relativePath] = fileStats;
  });

  return results;
}

/**
 * 检查覆盖率是否达标
 */
function checkCoverageThresholds(results) {
  const issues = [];

  // 检查全局阈值
  const globalCoverage = {
    statements:
      results.total.statements.total > 0
        ? (results.total.statements.covered / results.total.statements.total) * 100
        : 0,
    branches:
      results.total.branches.total > 0
        ? (results.total.branches.covered / results.total.branches.total) * 100
        : 0,
    functions:
      results.total.functions.total > 0
        ? (results.total.functions.covered / results.total.functions.total) * 100
        : 0,
    lines:
      results.total.lines.total > 0
        ? (results.total.lines.covered / results.total.lines.total) * 100
        : 0,
  };

  Object.keys(COVERAGE_THRESHOLDS.global).forEach(metric => {
    const threshold = COVERAGE_THRESHOLDS.global[metric];
    const actual = globalCoverage[metric];

    if (actual < threshold) {
      issues.push({
        type: 'global',
        metric,
        threshold,
        actual: actual.toFixed(2),
        message: `全局${metric}覆盖率 ${actual.toFixed(2)}% 低于阈值 ${threshold}%`,
      });
    }
  });

  // 检查关键文件阈值
  Object.keys(COVERAGE_THRESHOLDS.critical).forEach(pattern => {
    const thresholds = COVERAGE_THRESHOLDS.critical[pattern];

    Object.keys(results.files).forEach(filePath => {
      if (filePath.includes(pattern)) {
        const fileStats = results.files[filePath];

        Object.keys(thresholds).forEach(metric => {
          const threshold = thresholds[metric];
          const stats = fileStats[metric];
          const actual = stats.total > 0 ? (stats.covered / stats.total) * 100 : 0;

          if (actual < threshold) {
            issues.push({
              type: 'file',
              file: filePath,
              metric,
              threshold,
              actual: actual.toFixed(2),
              message: `文件 ${filePath} 的${metric}覆盖率 ${actual.toFixed(2)}% 低于阈值 ${threshold}%`,
            });
          }
        });
      }
    });
  });

  return issues;
}

/**
 * 生成覆盖率报告
 */
function generateReport(results, issues) {
  const reportPath = path.join(process.cwd(), 'coverage', 'coverage-report.md');

  const globalCoverage = {
    statements:
      results.total.statements.total > 0
        ? (results.total.statements.covered / results.total.statements.total) * 100
        : 0,
    branches:
      results.total.branches.total > 0
        ? (results.total.branches.covered / results.total.branches.total) * 100
        : 0,
    functions:
      results.total.functions.total > 0
        ? (results.total.functions.covered / results.total.functions.total) * 100
        : 0,
  };

  let report = `# 测试覆盖率报告\n\n`;
  report += `**生成时间**: ${new Date().toLocaleString()}\n\n`;

  // 总体覆盖率
  report += `## 📊 总体覆盖率\n\n`;
  report += `| 指标 | 覆盖率 | 阈值 | 状态 |\n`;
  report += `|------|--------|------|------|\n`;

  Object.keys(COVERAGE_THRESHOLDS.global).forEach(metric => {
    const threshold = COVERAGE_THRESHOLDS.global[metric];
    const actual = globalCoverage[metric] || 0;
    const status = actual >= threshold ? '✅' : '❌';

    report += `| ${metric} | ${actual.toFixed(2)}% | ${threshold}% | ${status} |\n`;
  });

  // 问题列表
  if (issues.length > 0) {
    report += `\n## ❌ 需要改进的项目\n\n`;
    issues.forEach((issue, index) => {
      report += `${index + 1}. ${issue.message}\n`;
    });
  } else {
    report += `\n## ✅ 所有覆盖率检查通过！\n`;
  }

  // 文件级别详情
  report += `\n## 📁 文件级别覆盖率\n\n`;
  report += `| 文件 | 语句 | 分支 | 函数 |\n`;
  report += `|------|------|------|------|\n`;

  Object.keys(results.files).forEach(filePath => {
    const fileStats = results.files[filePath];
    const statements =
      fileStats.statements.total > 0
        ? (fileStats.statements.covered / fileStats.statements.total) * 100
        : 0;
    const branches =
      fileStats.branches.total > 0
        ? (fileStats.branches.covered / fileStats.branches.total) * 100
        : 0;
    const functions =
      fileStats.functions.total > 0
        ? (fileStats.functions.covered / fileStats.functions.total) * 100
        : 0;

    report += `| ${filePath} | ${statements.toFixed(1)}% | ${branches.toFixed(1)}% | ${functions.toFixed(1)}% |\n`;
  });

  // 改进建议
  report += `\n## 💡 改进建议\n\n`;
  if (issues.length > 0) {
    report += `1. **优先处理关键模块**: 重点提升核心业务逻辑的测试覆盖率\n`;
    report += `2. **补充边界条件测试**: 增加异常情况和边界值的测试用例\n`;
    report += `3. **增加集成测试**: 补充模块间交互的测试场景\n`;
    report += `4. **定期检查**: 建议每次提交前运行覆盖率检查\n`;
  } else {
    report += `当前测试覆盖率已达标，建议：\n`;
    report += `1. **保持现有水平**: 确保新增代码也有相应测试\n`;
    report += `2. **持续优化**: 关注测试质量，不仅仅是覆盖率数字\n`;
    report += `3. **性能测试**: 考虑增加性能和压力测试\n`;
  }

  fs.writeFileSync(reportPath, report);
  console.log(`\n📄 覆盖率报告已生成: ${reportPath}`);

  return report;
}

/**
 * 主函数
 */
async function main() {
  try {
    // 运行测试
    await runCoverageTest();

    // 分析覆盖率
    console.log('\n📊 分析覆盖率数据...');
    const results = analyzeCoverage();

    // 检查阈值
    console.log('🔍 检查覆盖率阈值...');
    const issues = checkCoverageThresholds(results);

    // 生成报告
    console.log('📝 生成覆盖率报告...');
    generateReport(results, issues);

    // 输出结果
    if (issues.length === 0) {
      console.log('\n🎉 所有覆盖率检查通过！');
      process.exit(0);
    } else {
      console.log(`\n⚠️  发现 ${issues.length} 个覆盖率问题:`);
      issues.forEach(issue => {
        console.log(`   - ${issue.message}`);
      });
      console.log('\n💡 请查看详细报告并改进测试覆盖率');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 覆盖率检查失败:', error.message);
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  runCoverageTest,
  analyzeCoverage,
  checkCoverageThresholds,
  generateReport,
};
