/**
 * 测试数据生成器测试脚本
 * 用于测试测试数据生成和API访问
 * 
 * 使用方法:
 * node backend/scripts/test-data-generator-test.js
 */

const axios = require('axios');
const chalk = require('chalk');
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 配置
const config = {
  apiBaseUrl: 'http://localhost:3000/api/v2',
  testDataDir: path.resolve(process.cwd(), 'backend/test/contract-test-data'),
  scenarioDir: path.resolve(process.cwd(), 'backend/test/contract-test-data/scenarios')
};

// 确保目录存在
if (!fs.existsSync(config.testDataDir)) {
  fs.mkdirSync(config.testDataDir, { recursive: true });
}
if (!fs.existsSync(config.scenarioDir)) {
  fs.mkdirSync(config.scenarioDir, { recursive: true });
}

// 创建API客户端
const apiClient = axios.create({
  baseURL: config.apiBaseUrl,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 生成测试数据
async function generateTestData() {
  try {
    console.log(chalk.blue('生成测试数据...'));
    
    // 执行测试数据生成脚本
    const command = 'node backend/scripts/enhanced-test-data-generator.js --scenario basic --scale small --format json';
    console.log(chalk.gray(`执行命令: ${command}`));
    
    const output = execSync(command, { encoding: 'utf8' });
    console.log(output);
    
    console.log(chalk.green('✓ 测试数据生成成功'));
    return true;
  } catch (error) {
    console.error(chalk.red('✗ 测试数据生成失败:'), error.message);
    return false;
  }
}

// 测试API访问
async function testApiAccess() {
  try {
    console.log(chalk.blue('测试API访问...'));
    
    // 获取场景列表
    console.log(chalk.gray('获取场景列表...'));
    const scenariosResponse = await apiClient.get('/test-data/scenarios');
    console.log(chalk.green('✓ 获取场景列表成功'));
    console.log(chalk.gray(`场景列表: ${JSON.stringify(scenariosResponse.data.data)}`));
    
    // 如果有场景，获取第一个场景的数据
    if (scenariosResponse.data.data && scenariosResponse.data.data.length > 0) {
      const scenarioName = scenariosResponse.data.data[0];
      console.log(chalk.gray(`获取场景 ${scenarioName} 的数据...`));
      
      const scenarioResponse = await apiClient.get(`/test-data/scenarios/${scenarioName}`);
      console.log(chalk.green(`✓ 获取场景 ${scenarioName} 的数据成功`));
      
      // 获取用户数据
      console.log(chalk.gray(`获取场景 ${scenarioName} 的用户数据...`));
      const usersResponse = await apiClient.get(`/test-data/scenarios/${scenarioName}/users`);
      console.log(chalk.green(`✓ 获取场景 ${scenarioName} 的用户数据成功`));
      console.log(chalk.gray(`用户数量: ${usersResponse.data.data.length}`));
    } else {
      console.log(chalk.yellow('没有可用的场景，跳过场景数据测试'));
      
      // 导入测试数据
      console.log(chalk.gray('导入测试数据...'));
      
      // 创建测试数据
      const testData = {
        users: [
          {
            id: 1,
            username: 'test_user',
            email: '<EMAIL>',
            role: 'user',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ],
        tags: [
          {
            id: 1,
            name: '测试标签',
            category_id: 1,
            relevance_score: 1.0,
            weight: 1.0,
            usage_count: 0,
            is_verified: true,
            is_official: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      };
      
      const importResponse = await apiClient.post('/test-data/import', {
        name: 'test-scenario',
        data: testData
      });
      
      console.log(chalk.green('✓ 导入测试数据成功'));
      console.log(chalk.gray(`响应: ${JSON.stringify(importResponse.data)}`));
      
      // 获取导入的场景数据
      console.log(chalk.gray('获取导入的场景数据...'));
      const importedScenarioResponse = await apiClient.get('/test-data/scenarios/test-scenario');
      console.log(chalk.green('✓ 获取导入的场景数据成功'));
      console.log(chalk.gray(`用户数量: ${importedScenarioResponse.data.data.users.length}`));
    }
    
    console.log(chalk.green('✓ API访问测试成功'));
    return true;
  } catch (error) {
    console.error(chalk.red('✗ API访问测试失败:'), error.message);
    if (error.response) {
      console.error(chalk.red('响应状态:'), error.response.status);
      console.error(chalk.red('响应数据:'), error.response.data);
    }
    return false;
  }
}

// 主函数
async function main() {
  console.log(chalk.blue('开始测试数据生成器测试...'));
  
  // 生成测试数据
  const dataGenerated = await generateTestData();
  
  if (!dataGenerated) {
    console.error(chalk.red('测试数据生成失败，无法继续测试'));
    process.exit(1);
  }
  
  // 测试API访问
  const apiAccessSuccessful = await testApiAccess();
  
  if (!apiAccessSuccessful) {
    console.error(chalk.red('API访问测试失败'));
    process.exit(1);
  }
  
  console.log(chalk.green('✓ 测试数据生成器测试成功'));
  process.exit(0);
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red('测试过程中发生错误:'), error);
  process.exit(1);
});
