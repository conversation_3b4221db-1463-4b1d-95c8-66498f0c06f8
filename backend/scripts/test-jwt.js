/**
 * JWT工具测试脚本
 * 用于测试JWT工具的功能
 * 
 * 使用方法:
 * node backend/scripts/test-jwt.js [选项]
 * 
 * 选项:
 *   --legacy           使用旧版JWT工具 [默认: false]
 *   --enhanced         使用增强版JWT工具 [默认: true]
 *   --verbose          显示详细日志 [默认: true]
 *   -h, --help         显示帮助信息
 */

const { program } = require('commander');
const chalk = require('chalk');
const logger = require('../config/logger');

// 定义命令行选项
program
  .option('--legacy', '使用旧版JWT工具', false)
  .option('--enhanced', '使用增强版JWT工具', true)
  .option('--verbose', '显示详细日志', true)
  .helpOption('-h, --help', '显示帮助信息')
  .parse(process.argv);

const options = program.opts();

// 主函数
async function main() {
  try {
    console.log(chalk.blue('开始执行JWT工具测试...'));
    
    // 测试数据
    const payload = {
      id: 'test-user-id',
      nickname: '测试用户',
      role: 'user'
    };
    
    // 测试旧版JWT工具
    if (options.legacy) {
      console.log(chalk.blue('\n测试旧版JWT工具...'));
      const jwtUtils = require('../utils/jwt');
      
      console.time('旧版JWT生成令牌');
      const token = jwtUtils.generateToken(payload);
      console.timeEnd('旧版JWT生成令牌');
      
      if (token) {
        console.log(chalk.green('✓ 旧版JWT生成令牌成功'));
        
        if (options.verbose) {
          console.log(chalk.blue('\n令牌:'));
          console.log(token);
        }
        
        console.time('旧版JWT验证令牌');
        const decoded = jwtUtils.verifyToken(token);
        console.timeEnd('旧版JWT验证令牌');
        
        if (decoded) {
          console.log(chalk.green('✓ 旧版JWT验证令牌成功'));
          
          if (options.verbose) {
            console.log(chalk.blue('\n解码后的数据:'));
            console.log(decoded);
          }
        } else {
          console.error(chalk.red('✗ 旧版JWT验证令牌失败'));
        }
      } else {
        console.error(chalk.red('✗ 旧版JWT生成令牌失败'));
      }
    }
    
    // 测试增强版JWT工具
    if (options.enhanced) {
      console.log(chalk.blue('\n测试增强版JWT工具...'));
      const enhancedJWT = require('../utils/enhanced-jwt');
      
      console.time('增强版JWT生成令牌');
      const tokens = await enhancedJWT.generateTokens(payload);
      console.timeEnd('增强版JWT生成令牌');
      
      if (tokens && tokens.accessToken) {
        console.log(chalk.green('✓ 增强版JWT生成令牌成功'));
        
        if (options.verbose) {
          console.log(chalk.blue('\n访问令牌:'));
          console.log(tokens.accessToken);
          console.log(chalk.blue('\n刷新令牌:'));
          console.log(tokens.refreshToken);
        }
        
        console.time('增强版JWT验证令牌');
        const decoded = await enhancedJWT.verifyAccessToken(tokens.accessToken);
        console.timeEnd('增强版JWT验证令牌');
        
        if (decoded) {
          console.log(chalk.green('✓ 增强版JWT验证令牌成功'));
          
          if (options.verbose) {
            console.log(chalk.blue('\n解码后的数据:'));
            console.log(decoded);
          }
          
          // 测试刷新令牌
          console.time('增强版JWT刷新令牌');
          const newTokens = await enhancedJWT.refreshTokens(tokens.refreshToken, payload);
          console.timeEnd('增强版JWT刷新令牌');
          
          if (newTokens && newTokens.accessToken) {
            console.log(chalk.green('✓ 增强版JWT刷新令牌成功'));
            
            if (options.verbose) {
              console.log(chalk.blue('\n新的访问令牌:'));
              console.log(newTokens.accessToken);
            }
          } else {
            console.error(chalk.red('✗ 增强版JWT刷新令牌失败'));
          }
        } else {
          console.error(chalk.red('✗ 增强版JWT验证令牌失败'));
        }
      } else {
        console.error(chalk.red('✗ 增强版JWT生成令牌失败'));
      }
    }
    
    console.log(chalk.blue('\nJWT工具测试完成'));
    process.exit(0);
  } catch (error) {
    console.error(chalk.red(`JWT工具测试失败: ${error.message}`));
    process.exit(1);
  }
}

// 执行主函数
main();
