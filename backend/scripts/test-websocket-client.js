/**
 * WebSocket客户端测试脚本
 * 用于测试WebSocket通知功能
 */

// 导入依赖
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');

// 配置
const config = {
  // WebSocket服务器地址
  wsUrl: 'ws://localhost:9090/ws',
  
  // 测试用户ID
  userId: 101,
  
  // 测试用户名
  username: 'testuser',
  
  // JWT密钥（应与后端一致）
  jwtSecret: process.env.JWT_SECRET || 'your-secret-key'
};

// 创建测试令牌
function createTestToken() {
  const payload = {
    id: config.userId,
    username: config.username,
    isAdmin: false
  };
  
  return jwt.sign(payload, config.jwtSecret, { expiresIn: '1h' });
}

// 连接WebSocket服务器
function connectWebSocket() {
  // 创建令牌
  const token = createTestToken();
  
  // 构建WebSocket URL
  const wsUrl = `${config.wsUrl}?token=${token}`;
  
  console.log(`连接到WebSocket服务器: ${wsUrl}`);
  
  // 创建WebSocket连接
  const ws = new WebSocket(wsUrl);
  
  // 连接打开事件
  ws.on('open', () => {
    console.log('WebSocket连接已打开');
    
    // 发送心跳
    ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
    
    // 订阅频道
    ws.send(JSON.stringify({
      type: 'subscribe',
      channel: 'notifications'
    }));
    
    console.log('已订阅通知频道');
  });
  
  // 接收消息事件
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log('收到消息:', JSON.stringify(message, null, 2));
      
      // 响应心跳
      if (message.type === 'ping') {
        ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
      }
    } catch (error) {
      console.error('解析消息失败:', error);
    }
  });
  
  // 错误事件
  ws.on('error', (error) => {
    console.error('WebSocket错误:', error);
  });
  
  // 关闭事件
  ws.on('close', (code, reason) => {
    console.log(`WebSocket连接已关闭: ${code} ${reason}`);
  });
  
  return ws;
}

// 主函数
function main() {
  console.log('启动WebSocket客户端测试...');
  
  // 连接WebSocket服务器
  const ws = connectWebSocket();
  
  // 设置定时器，每30秒发送一次心跳
  const heartbeatInterval = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
    }
  }, 30000);
  
  // 设置关闭处理
  process.on('SIGINT', () => {
    console.log('关闭WebSocket连接...');
    clearInterval(heartbeatInterval);
    ws.close();
    process.exit(0);
  });
  
  console.log('WebSocket客户端测试已启动，按Ctrl+C退出');
}

// 运行主函数
main();
