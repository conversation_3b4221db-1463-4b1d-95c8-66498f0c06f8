/**
 * API示例更新脚本
 *
 * 该脚本用于根据API文档自动生成或更新API使用示例。
 * 它会读取API-DOCUMENTATION-REAL.md文件中的API端点定义，
 * 然后生成或更新API-USAGE-EXAMPLES-REAL.md文件中的示例。
 *
 * 使用方法：
 * node scripts/update-api-examples.js [options]
 *
 * 选项：
 * --verbose: 显示详细信息
 * --dry-run: 不实际写入文件，只显示将要进行的更改
 * --section=<section>: 只更新指定章节的示例，例如 --section=users
 */

const fs = require('fs');
const path = require('path');
const { program } = require('commander');

// 简单的彩色日志函数
const log = {
  green: (text) => console.log(`\x1b[32m${text}\x1b[0m`),
  red: (text) => console.error(`\x1b[31m${text}\x1b[0m`),
  yellow: (text) => console.warn(`\x1b[33m${text}\x1b[0m`),
  blue: (text) => console.log(`\x1b[34m${text}\x1b[0m`)
};

// 定义命令行选项
program
  .option('--verbose', '显示详细信息')
  .option('--dry-run', '不实际写入文件，只显示将要进行的更改')
  .option('--section <section>', '只更新指定章节的示例')
  .parse(process.argv);

const options = program.opts();

// 文档路径
const docsDir = path.resolve(__dirname, '../docs');
const apiDocPath = path.join(docsDir, 'API-DOCUMENTATION-REAL.md');
const apiExamplesPath = path.join(docsDir, 'API-USAGE-EXAMPLES-REAL.md');

// 检查文件是否存在
if (!fs.existsSync(apiDocPath)) {
  log.red(`错误: API文档不存在: ${apiDocPath}`);
  process.exit(1);
}

// 如果示例文件不存在，创建一个空的示例文件
if (!fs.existsSync(apiExamplesPath)) {
  log.yellow(`警告: API使用示例文档不存在，将创建新文件: ${apiExamplesPath}`);
  fs.writeFileSync(apiExamplesPath, '# API使用示例\n\n本文档提供了API的使用示例。\n\n', 'utf8');
}

/**
 * 从API文档中提取端点信息
 * @param {string} content API文档内容
 * @returns {Array} 端点信息列表
 */
function extractEndpointsFromDoc(content) {
  const endpoints = [];
  const sections = content.split(/^## /m).slice(1); // 跳过第一部分（介绍）

  for (const section of sections) {
    const sectionLines = section.split('\n');
    const sectionTitle = sectionLines[0].trim();
    const sectionNumber = sectionTitle.split('.')[0];
    const sectionName = sectionTitle.substring(sectionTitle.indexOf(' ') + 1);

    const subsections = section.split(/^### /m).slice(1); // 跳过第一部分（章节介绍）

    for (const subsection of subsections) {
      const subsectionLines = subsection.split('\n');
      const subsectionTitle = subsectionLines[0].trim();
      const subsectionNumber = subsectionTitle.split('.')[0];
      const subsectionName = subsectionTitle.substring(subsectionTitle.indexOf(' ') + 1);

      // 提取端点信息
      const endpointMatch = subsection.match(/\*\*端点\*\*：`([A-Z]+) ([^`]+)`/);
      if (!endpointMatch) continue;

      const method = endpointMatch[1];
      const path = endpointMatch[2];

      // 提取请求头
      const headersMatch = subsection.match(/\*\*请求头\*\*：`([^`]+)`/);
      const headers = headersMatch ? headersMatch[1].split(',').map(h => h.trim()) : [];

      // 提取请求体
      let requestBody = null;
      const requestBodyMatch = subsection.match(/\*\*请求体\*\*：\s+```json\s+([\s\S]+?)\s+```/);
      if (requestBodyMatch) {
        try {
          requestBody = JSON.parse(requestBodyMatch[1]);
        } catch (e) {
          log.yellow(`警告: 无法解析请求体JSON: ${subsectionTitle}`);
          requestBody = requestBodyMatch[1];
        }
      }

      // 提取响应
      let response = null;
      const responseMatch = subsection.match(/\*\*响应\*\*：\s+```json\s+([\s\S]+?)\s+```/);
      if (responseMatch) {
        try {
          response = JSON.parse(responseMatch[1]);
        } catch (e) {
          log.yellow(`警告: 无法解析响应JSON: ${subsectionTitle}`);
          response = responseMatch[1];
        }
      }

      // 提取查询参数
      const queryParams = [];
      const queryParamsMatch = subsection.match(/\*\*查询参数\*\*：\s+([\s\S]+?)(?:\n\n|\*\*)/);
      if (queryParamsMatch) {
        const paramLines = queryParamsMatch[1].split('\n');
        for (const line of paramLines) {
          const paramMatch = line.match(/- `([^`]+)`：(.+)/);
          if (paramMatch) {
            queryParams.push({
              name: paramMatch[1],
              description: paramMatch[2].trim()
            });
          }
        }
      }

      endpoints.push({
        section: {
          number: sectionNumber,
          name: sectionName
        },
        subsection: {
          number: subsectionNumber,
          name: subsectionName
        },
        method,
        path,
        headers,
        queryParams,
        requestBody,
        response
      });
    }
  }

  return endpoints;
}

/**
 * 生成API使用示例
 * @param {Array} endpoints 端点信息列表
 * @returns {string} API使用示例内容
 */
function generateApiExamples(endpoints) {
  let content = '# API使用示例\n\n';
  content += '本文档提供了API的使用示例。每个示例包括请求和响应的详细信息。\n\n';
  content += '## 目录\n\n';

  // 生成目录
  const sections = {};
  for (const endpoint of endpoints) {
    const sectionNumber = endpoint.section.number;
    const sectionName = endpoint.section.name;
    
    if (!sections[sectionNumber]) {
      sections[sectionNumber] = sectionName;
    }
  }

  for (const [number, name] of Object.entries(sections)) {
    content += `${number}. [${name}](#${number}-${name.toLowerCase().replace(/\s+/g, '-')})\n`;
  }

  content += '\n';

  // 生成示例
  let currentSection = null;
  let exampleCount = 0;

  for (const endpoint of endpoints) {
    const sectionNumber = endpoint.section.number;
    const sectionName = endpoint.section.name;
    
    // 如果指定了章节，只更新该章节的示例
    if (options.section && sectionName.toLowerCase() !== options.section.toLowerCase()) {
      continue;
    }

    // 添加章节标题
    if (currentSection !== sectionNumber) {
      content += `## ${sectionNumber}. ${sectionName}\n\n`;
      currentSection = sectionNumber;
      exampleCount = 0;
    }

    exampleCount++;
    const subsectionName = endpoint.subsection.name;
    
    content += `### ${sectionNumber}.${exampleCount} ${subsectionName}\n\n`;
    content += `**请求**:\n\n`;
    content += '```http\n';
    content += `${endpoint.method} /api/v2${endpoint.path}`;
    
    // 添加查询参数
    if (endpoint.queryParams.length > 0) {
      const queryString = endpoint.queryParams
        .map(param => `${param.name}=${param.name === 'page' ? '1' : param.name === 'pageSize' ? '10' : 'value'}`)
        .join('&');
      content += `?${queryString}`;
    }
    
    content += '\n';
    
    // 添加请求头
    if (endpoint.headers.length > 0) {
      for (const header of endpoint.headers) {
        if (header.includes('Authorization')) {
          content += 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\n';
        } else {
          content += `${header}\n`;
        }
      }
    }
    
    // 添加请求体
    if (endpoint.requestBody) {
      content += 'Content-Type: application/json\n\n';
      if (typeof endpoint.requestBody === 'string') {
        content += endpoint.requestBody;
      } else {
        content += JSON.stringify(endpoint.requestBody, null, 2);
      }
    }
    
    content += '\n```\n\n';
    
    // 添加响应
    content += `**响应**:\n\n`;
    content += '```json\n';
    if (typeof endpoint.response === 'string') {
      content += endpoint.response;
    } else {
      content += JSON.stringify(endpoint.response, null, 2);
    }
    content += '\n```\n\n';
  }

  return content;
}

/**
 * 主函数
 */
function main() {
  log.blue('开始更新API使用示例...');

  // 读取API文档
  const apiDocContent = fs.readFileSync(apiDocPath, 'utf8');
  
  // 提取端点信息
  const endpoints = extractEndpointsFromDoc(apiDocContent);
  
  if (endpoints.length === 0) {
    log.red('错误: 未从API文档中提取到任何端点信息');
    process.exit(1);
  }
  
  log.green(`✓ 从API文档中提取到${endpoints.length}个端点信息`);
  
  // 生成API使用示例
  const apiExamplesContent = generateApiExamples(endpoints);
  
  // 写入文件
  if (!options.dryRun) {
    fs.writeFileSync(apiExamplesPath, apiExamplesContent, 'utf8');
    log.green(`✓ API使用示例已更新: ${apiExamplesPath}`);
  } else {
    log.yellow('⚠ 干运行模式，未写入文件');
    if (options.verbose) {
      console.log(apiExamplesContent);
    }
  }
  
  log.blue('API使用示例更新完成');
}

// 执行主函数
main();
