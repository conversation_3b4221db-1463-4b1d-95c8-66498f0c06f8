/**
 * API实现验证脚本
 *
 * 该脚本用于验证API实现与文档的一致性，包括：
 * 1. 验证API路由配置与Swagger注释的一致性
 * 2. 验证API控制器实现与Swagger注释的一致性
 * 3. 验证API响应格式与文档描述的一致性
 *
 * 使用方法：
 * node scripts/validate-api-implementation.js [options]
 *
 * 选项：
 * --verbose: 显示详细信息
 * --fix: 尝试修复不一致的问题
 * --report: 生成报告文件
 */

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const glob = require('glob');

// 简单的彩色日志函数
const log = {
  green: (text) => console.log(`\x1b[32m${text}\x1b[0m`),
  red: (text) => console.error(`\x1b[31m${text}\x1b[0m`),
  yellow: (text) => console.warn(`\x1b[33m${text}\x1b[0m`),
  blue: (text) => console.log(`\x1b[34m${text}\x1b[0m`)
};

// 定义命令行选项
program
  .option('--verbose', '显示详细信息')
  .option('--fix', '尝试修复不一致的问题')
  .option('--report', '生成报告文件')
  .parse(process.argv);

const options = program.opts();

// 获取Swagger配置
let swaggerSpec;
try {
  const swaggerConfig = require('../config/swagger');
  swaggerSpec = swaggerConfig.swaggerSpec;
} catch (error) {
  log.red(`无法加载Swagger配置：${error.message}`);

  // 创建一个基本的Swagger规范
  const config = require('../config/unified-config');

  swaggerSpec = {
    openapi: '3.0.0',
    info: {
      title: 'AIBUBB API',
      version: '2.0.0',
      description: '以学习计划为核心的小程序后端API - 领域驱动设计实现',
    },
    servers: [
      {
        url: `http://localhost:${config.server.port}/api/v2`,
        description: '开发服务器 V2',
      },
    ],
    paths: {},
    components: {
      schemas: {},
    },
  };
}

// 获取API路由配置
const apiRoutes = [];

// 尝试从routes目录获取路由
try {
  const routesDir = path.resolve(__dirname, '../routes');
  if (fs.existsSync(routesDir)) {
    const routeFiles = glob.sync(`${routesDir}/**/*.js`);

    routeFiles.forEach(file => {
      try {
        const route = require(file);
        if (route && route.stack) {
          route.stack.forEach(layer => {
            if (layer.route) {
              const path = layer.route.path;
              const methods = Object.keys(layer.route.methods).map(method => method.toUpperCase());
              apiRoutes.push({ path, methods });
            }
          });
        }
      } catch (error) {
        log.red(`无法加载路由文件：${file}`);
      }
    });
  }
} catch (error) {
  log.red(`无法获取路由配置：${error.message}`);
}

// 尝试从interfaces/api/routes目录获取路由
try {
  const interfacesRoutesDir = path.resolve(__dirname, '../interfaces/api/routes');
  if (fs.existsSync(interfacesRoutesDir)) {
    const routeFiles = glob.sync(`${interfacesRoutesDir}/**/*.js`);

    routeFiles.forEach(file => {
      try {
        const route = require(file);
        if (route && route.stack) {
          route.stack.forEach(layer => {
            if (layer.route) {
              const path = layer.route.path;
              const methods = Object.keys(layer.route.methods).map(method => method.toUpperCase());
              apiRoutes.push({ path, methods });
            }
          });
        }
      } catch (error) {
        log.red(`无法加载接口路由文件：${file}`);
      }
    });
  }
} catch (error) {
  log.red(`无法获取接口路由配置：${error.message}`);
}

// 获取Swagger定义的路径
const swaggerPaths = [];
Object.keys(swaggerSpec.paths).forEach(path => {
  const methods = Object.keys(swaggerSpec.paths[path]).map(method => method.toUpperCase());
  swaggerPaths.push({ path, methods });
});

/**
 * 验证API路由配置与Swagger注释的一致性
 * @returns {object} 验证结果
 */
function validateRoutes() {
  const missingInSwagger = [];
  const missingInRoutes = [];

  // 检查路由中存在但Swagger中不存在的路径
  apiRoutes.forEach(route => {
    const swaggerPath = swaggerPaths.find(p => p.path === route.path);
    if (!swaggerPath) {
      missingInSwagger.push(route);
    } else {
      // 检查方法是否一致
      route.methods.forEach(method => {
        if (!swaggerPath.methods.includes(method)) {
          missingInSwagger.push({ path: route.path, methods: [method] });
        }
      });
    }
  });

  // 检查Swagger中存在但路由中不存在的路径
  swaggerPaths.forEach(swaggerPath => {
    const route = apiRoutes.find(r => r.path === swaggerPath.path);
    if (!route) {
      missingInRoutes.push(swaggerPath);
    } else {
      // 检查方法是否一致
      swaggerPath.methods.forEach(method => {
        if (!route.methods.includes(method)) {
          missingInRoutes.push({ path: swaggerPath.path, methods: [method] });
        }
      });
    }
  });

  return { missingInSwagger, missingInRoutes };
}

/**
 * 验证API控制器实现与Swagger注释的一致性
 * @returns {object} 验证结果
 */
function validateControllers() {
  const inconsistentControllers = [];
  const missingJsDocControllers = [];
  const missingResponseSchemaControllers = [];

  // 获取控制器文件
  const controllersDir = path.resolve(__dirname, '../controllers');
  const controllerFiles = glob.sync(`${controllersDir}/**/*.js`);

  // 尝试从interfaces/api/controllers目录获取控制器
  const interfacesControllersDir = path.resolve(__dirname, '../interfaces/api/controllers');
  if (fs.existsSync(interfacesControllersDir)) {
    const interfaceControllerFiles = glob.sync(`${interfacesControllersDir}/**/*.js`);
    controllerFiles.push(...interfaceControllerFiles);
  }

  controllerFiles.forEach(file => {
    try {
      const controller = require(file);
      const controllerName = path.basename(file, '.js');

      // 检查控制器中的方法是否在Swagger中有对应的路径
      Object.keys(controller).forEach(methodName => {
        if (typeof controller[methodName] === 'function') {
          // 尝试从方法名推断路径
          const pathPattern = methodName.replace(/([A-Z])/g, '-$1').toLowerCase();
          const possiblePaths = [
            `/api/v2/${controllerName.replace('Controller', '')}/${pathPattern}`,
            `/api/v2/${controllerName.replace('Controller', '')}`,
            `/api/v2/${controllerName.replace('V2Controller', '')}`,
            `/api/v2/${controllerName.replace(/Controller$/, '').toLowerCase()}/${pathPattern}`,
            `/api/v2/${controllerName.replace(/Controller$/, '').toLowerCase()}`
          ];

          let found = false;
          let matchedPath = null;
          let matchedMethod = null;

          possiblePaths.forEach(possiblePath => {
            Object.keys(swaggerSpec.paths || {}).forEach(path => {
              if (path.includes(possiblePath) || possiblePath.includes(path)) {
                found = true;
                matchedPath = path;

                // 尝试推断HTTP方法
                const methodMap = {
                  'get': ['get', 'find', 'list', 'retrieve', 'show'],
                  'post': ['create', 'add', 'insert', 'post'],
                  'put': ['update', 'edit', 'modify', 'put'],
                  'delete': ['delete', 'remove', 'destroy'],
                  'patch': ['patch', 'partialUpdate']
                };

                for (const [httpMethod, methodPatterns] of Object.entries(methodMap)) {
                  if (methodPatterns.some(pattern => methodName.toLowerCase().includes(pattern))) {
                    matchedMethod = httpMethod;
                    break;
                  }
                }
              }
            });
          });

          if (!found) {
            inconsistentControllers.push({
              controller: controllerName,
              method: methodName,
              possiblePaths,
              file
            });
          } else if (matchedPath && matchedMethod) {
            // 检查方法是否有JSDoc注释
            const fileContent = fs.readFileSync(file, 'utf8');
            const methodRegex = new RegExp(`\\s*${methodName}\\s*\\(`);
            const methodIndex = fileContent.search(methodRegex);

            if (methodIndex !== -1) {
              // 查找方法前的JSDoc注释
              const jsDocRegex = /\/\*\*[\s\S]*?\*\//g;
              const jsDocMatches = [...fileContent.substring(0, methodIndex).matchAll(jsDocRegex)];
              const lastJsDoc = jsDocMatches.length > 0 ? jsDocMatches[jsDocMatches.length - 1][0] : null;

              if (!lastJsDoc || !lastJsDoc.includes('@swagger') && !lastJsDoc.includes('@openapi')) {
                missingJsDocControllers.push({
                  controller: controllerName,
                  method: methodName,
                  path: matchedPath,
                  httpMethod: matchedMethod,
                  file
                });
              }

              // 检查响应模式
              const pathObject = swaggerSpec.paths[matchedPath];
              if (pathObject && pathObject[matchedMethod]) {
                const operation = pathObject[matchedMethod];
                if (!operation.responses || !operation.responses['200'] || !operation.responses['200'].content) {
                  missingResponseSchemaControllers.push({
                    controller: controllerName,
                    method: methodName,
                    path: matchedPath,
                    httpMethod: matchedMethod,
                    file
                  });
                }
              }
            }
          }
        }
      });
    } catch (error) {
      log.red(`无法加载控制器文件：${file}`);
      if (options.verbose) {
        log.red(`  错误：${error.message}`);
      }
    }
  });

  return {
    inconsistentControllers,
    missingJsDocControllers,
    missingResponseSchemaControllers
  };
}

/**
 * 生成验证报告
 * @param {object} results 验证结果
 */
function generateReport(results) {
  const reportDir = path.resolve(__dirname, '../reports');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  const reportPath = path.join(reportDir, 'api-validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2), 'utf8');

  log.green(`✓ 验证报告已生成：${reportPath}`);
}

/**
 * 验证API响应格式与文档描述的一致性
 * @returns {object} 验证结果
 */
function validateResponseFormats() {
  const inconsistentResponses = [];
  const missingSuccessField = [];
  const missingDataField = [];

  // 获取控制器文件
  const controllersDir = path.resolve(__dirname, '../controllers');
  const controllerFiles = glob.sync(`${controllersDir}/**/*.js`);

  // 尝试从interfaces/api/controllers目录获取控制器
  const interfacesControllersDir = path.resolve(__dirname, '../interfaces/api/controllers');
  if (fs.existsSync(interfacesControllersDir)) {
    const interfaceControllerFiles = glob.sync(`${interfacesControllersDir}/**/*.js`);
    controllerFiles.push(...interfaceControllerFiles);
  }

  controllerFiles.forEach(file => {
    try {
      const fileContent = fs.readFileSync(file, 'utf8');

      // 查找所有的res.json或res.send调用
      const responseRegex = /res\.(json|send)\s*\(\s*([^)]+)\s*\)/g;
      const responseMatches = [...fileContent.matchAll(responseRegex)];

      responseMatches.forEach(match => {
        const responseCode = match[2];

        // 检查是否包含success字段
        if (!responseCode.includes('success')) {
          missingSuccessField.push({
            file,
            line: fileContent.substring(0, match.index).split('\n').length,
            response: responseCode.trim()
          });
        }

        // 检查是否包含data字段
        if (responseCode.includes('success: true') && !responseCode.includes('data')) {
          missingDataField.push({
            file,
            line: fileContent.substring(0, match.index).split('\n').length,
            response: responseCode.trim()
          });
        }

        // 检查响应格式是否一致
        if (!responseCode.includes('success') ||
            (responseCode.includes('success: true') && !responseCode.includes('data')) ||
            (responseCode.includes('success: false') && !responseCode.includes('error'))) {
          inconsistentResponses.push({
            file,
            line: fileContent.substring(0, match.index).split('\n').length,
            response: responseCode.trim()
          });
        }
      });
    } catch (error) {
      log.red(`无法分析控制器文件：${file}`);
      if (options.verbose) {
        log.red(`  错误：${error.message}`);
      }
    }
  });

  return {
    inconsistentResponses,
    missingSuccessField,
    missingDataField
  };
}

/**
 * 主函数
 */
function main() {
  log.blue('开始验证API实现与文档的一致性...');

  // 验证API路由配置与Swagger注释的一致性
  const routeResults = validateRoutes();

  if (routeResults.missingInSwagger.length > 0) {
    log.yellow(`⚠ 发现${routeResults.missingInSwagger.length}个路由在Swagger中不存在`);
    if (options.verbose) {
      routeResults.missingInSwagger.forEach(route => {
        log.yellow(`  路径：${route.path}，方法：${route.methods.join(', ')}`);
      });
    }
  } else {
    log.green('✓ 所有路由在Swagger中都有对应的定义');
  }

  if (routeResults.missingInRoutes.length > 0) {
    log.yellow(`⚠ 发现${routeResults.missingInRoutes.length}个Swagger定义在路由中不存在`);
    if (options.verbose) {
      routeResults.missingInRoutes.forEach(route => {
        log.yellow(`  路径：${route.path}，方法：${route.methods.join(', ')}`);
      });
    }
  } else {
    log.green('✓ 所有Swagger定义在路由中都有对应的实现');
  }

  // 验证API控制器实现与Swagger注释的一致性
  const controllerResults = validateControllers();

  if (controllerResults.inconsistentControllers.length > 0) {
    log.yellow(`⚠ 发现${controllerResults.inconsistentControllers.length}个控制器方法可能与Swagger定义不一致`);
    if (options.verbose) {
      controllerResults.inconsistentControllers.forEach(controller => {
        log.yellow(`  控制器：${controller.controller}，方法：${controller.method}`);
        log.yellow(`  可能的路径：${controller.possiblePaths.join(', ')}`);
      });
    }
  } else {
    log.green('✓ 所有控制器方法与Swagger定义一致');
  }

  if (controllerResults.missingJsDocControllers.length > 0) {
    log.yellow(`⚠ 发现${controllerResults.missingJsDocControllers.length}个控制器方法缺少JSDoc注释`);
    if (options.verbose) {
      controllerResults.missingJsDocControllers.forEach(controller => {
        log.yellow(`  控制器：${controller.controller}，方法：${controller.method}`);
        log.yellow(`  路径：${controller.path}，HTTP方法：${controller.httpMethod}`);
      });
    }
  } else {
    log.green('✓ 所有控制器方法都有JSDoc注释');
  }

  if (controllerResults.missingResponseSchemaControllers.length > 0) {
    log.yellow(`⚠ 发现${controllerResults.missingResponseSchemaControllers.length}个控制器方法缺少响应模式`);
    if (options.verbose) {
      controllerResults.missingResponseSchemaControllers.forEach(controller => {
        log.yellow(`  控制器：${controller.controller}，方法：${controller.method}`);
        log.yellow(`  路径：${controller.path}，HTTP方法：${controller.httpMethod}`);
      });
    }
  } else {
    log.green('✓ 所有控制器方法都有响应模式');
  }

  // 验证API响应格式与文档描述的一致性
  const responseResults = validateResponseFormats();

  if (responseResults.inconsistentResponses.length > 0) {
    log.yellow(`⚠ 发现${responseResults.inconsistentResponses.length}个响应格式不一致`);
    if (options.verbose) {
      responseResults.inconsistentResponses.forEach(response => {
        log.yellow(`  文件：${response.file}，行：${response.line}`);
        log.yellow(`  响应：${response.response}`);
      });
    }
  } else {
    log.green('✓ 所有响应格式一致');
  }

  if (responseResults.missingSuccessField.length > 0) {
    log.yellow(`⚠ 发现${responseResults.missingSuccessField.length}个响应缺少success字段`);
    if (options.verbose) {
      responseResults.missingSuccessField.forEach(response => {
        log.yellow(`  文件：${response.file}，行：${response.line}`);
        log.yellow(`  响应：${response.response}`);
      });
    }
  } else {
    log.green('✓ 所有响应都包含success字段');
  }

  if (responseResults.missingDataField.length > 0) {
    log.yellow(`⚠ 发现${responseResults.missingDataField.length}个成功响应缺少data字段`);
    if (options.verbose) {
      responseResults.missingDataField.forEach(response => {
        log.yellow(`  文件：${response.file}，行：${response.line}`);
        log.yellow(`  响应：${response.response}`);
      });
    }
  } else {
    log.green('✓ 所有成功响应都包含data字段');
  }

  // 生成报告
  if (options.report) {
    generateReport({
      routes: routeResults,
      controllers: controllerResults,
      responses: responseResults
    });
  }

  // 尝试修复问题
  if (options.fix) {
    log.blue('尝试修复不一致的问题...');

    // 尝试修复响应格式
    if (responseResults.inconsistentResponses.length > 0) {
      log.yellow('尝试修复响应格式...');
      // TODO: 实现修复逻辑
    }

    // 尝试生成缺失的JSDoc注释
    if (controllerResults.missingJsDocControllers.length > 0) {
      log.yellow('尝试生成缺失的JSDoc注释...');
      // TODO: 实现修复逻辑
    }

    log.yellow('⚠ 自动修复功能尚未完全实现');
  }

  log.blue('API实现验证完成');
}

// 执行主函数
main();
