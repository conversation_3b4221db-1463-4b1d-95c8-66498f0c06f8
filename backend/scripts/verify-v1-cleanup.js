/**
 * V1 API清理验证脚本
 * 用于验证系统中是否还有V1 API相关的文件和注释
 * 
 * 使用方法：
 * 1. 在完成V1 API清理工作后运行此脚本
 * 2. 脚本将检查系统中是否还有V1 API相关的文件和注释
 * 
 * 示例：
 * node backend/scripts/verify-v1-cleanup.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
  // 需要检查的目录
  directories: {
    routes: path.resolve(__dirname, '../routes'),
    controllers: path.resolve(__dirname, '../controllers'),
    services: path.resolve(__dirname, '../services'),
    middlewares: path.resolve(__dirname, '../middlewares'),
    interfaces: path.resolve(__dirname, '../interfaces'),
    config: path.resolve(__dirname, '../config'),
    utils: path.resolve(__dirname, '../utils'),
  },
  // 需要检查的文件类型
  filePatterns: [
    '.js',
    '.ts',
    '.jsx',
    '.tsx',
  ],
  // 需要检查的V1 API相关内容
  v1Patterns: [
    // 路由注释
    /@route\s+[A-Z]+\s+\/api\/v1\//,
    // 路由注册
    /router\.(get|post|put|delete|patch)\s*\(\s*['"]\/api\/v1\//,
    // V1版本引用
    /v1:\s*require\(/,
    // 兼容层引用
    /compatibility-layer/,
    // V1废弃警告
    /v1-deprecation/,
    // 暂时使用V1版本注释
    /暂时使用V1版本/,
    // V1相关注释
    /V1版本/,
    // V1 API文档
    /V1-API/,
  ],
  // 需要忽略的文件和目录
  ignorePatterns: [
    /backup/,
    /node_modules/,
    /\.git/,
    /scripts\/remove-v1-api\.js/,
    /scripts\/cleanup-v1-files\.js/,
    /scripts\/complete-v1-cleanup\.js/,
    /scripts\/cleanup-v1-api-comments\.js/,
    /scripts\/verify-v1-cleanup\.js/,
  ],
  // 报告文件路径
  reportPath: path.resolve(__dirname, '../reports/v1-cleanup-verification.json'),
};

/**
 * 检查文件是否应该被忽略
 * @param {string} filePath - 文件路径
 * @returns {boolean} - 是否应该被忽略
 */
function shouldIgnoreFile(filePath) {
  return config.ignorePatterns.some(pattern => pattern.test(filePath));
}

/**
 * 检查文件中是否包含V1 API相关内容
 * @param {string} filePath - 文件路径
 * @returns {Array<{pattern: string, line: string, lineNumber: number}>} - 匹配结果
 */
function checkFileForV1Content(filePath) {
  // 如果文件应该被忽略，则返回空数组
  if (shouldIgnoreFile(filePath)) {
    return [];
  }
  
  // 读取文件内容
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  // 检查每一行是否包含V1 API相关内容
  const matches = [];
  
  lines.forEach((line, index) => {
    config.v1Patterns.forEach(pattern => {
      if (pattern.test(line)) {
        matches.push({
          pattern: pattern.toString(),
          line: line.trim(),
          lineNumber: index + 1,
        });
      }
    });
  });
  
  return matches;
}

/**
 * 递归检查目录中的文件
 * @param {string} directory - 目录路径
 * @param {Array<string>} filePatterns - 文件模式数组
 * @returns {Array<{file: string, matches: Array<{pattern: string, line: string, lineNumber: number}>}>} - 检查结果
 */
function checkDirectoryForV1Content(directory, filePatterns) {
  // 如果目录应该被忽略，则返回空数组
  if (shouldIgnoreFile(directory)) {
    return [];
  }
  
  // 获取目录中的所有文件
  const files = fs.readdirSync(directory, { withFileTypes: true });
  
  // 检查结果
  const results = [];
  
  // 遍历文件
  for (const file of files) {
    const filePath = path.join(directory, file.name);
    
    if (file.isDirectory()) {
      // 如果是目录，则递归检查
      const subResults = checkDirectoryForV1Content(filePath, filePatterns);
      results.push(...subResults);
    } else if (file.isFile() && filePatterns.some(pattern => file.name.endsWith(pattern))) {
      // 如果是文件，且匹配文件模式，则检查文件内容
      const matches = checkFileForV1Content(filePath);
      
      if (matches.length > 0) {
        results.push({
          file: filePath,
          matches,
        });
      }
    }
  }
  
  return results;
}

/**
 * 生成报告
 * @param {Array<{file: string, matches: Array<{pattern: string, line: string, lineNumber: number}>}>} results - 检查结果
 */
function generateReport(results) {
  // 创建报告目录
  const reportDir = path.dirname(config.reportPath);
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  // 生成报告
  const report = {
    timestamp: new Date().toISOString(),
    totalFiles: results.length,
    results,
  };
  
  // 写入报告文件
  fs.writeFileSync(config.reportPath, JSON.stringify(report, null, 2), 'utf8');
  
  console.log(`报告已生成: ${config.reportPath}`);
  
  // 打印摘要
  console.log(`检查结果摘要:`);
  console.log(`- 包含V1 API相关内容的文件数量: ${results.length}`);
  
  if (results.length > 0) {
    console.log(`- 详细信息:`);
    results.forEach(result => {
      console.log(`  - ${result.file}: ${result.matches.length}个匹配项`);
      result.matches.slice(0, 3).forEach(match => {
        console.log(`    - 第${match.lineNumber}行: ${match.line.substring(0, 50)}${match.line.length > 50 ? '...' : ''}`);
      });
      if (result.matches.length > 3) {
        console.log(`    - ... 还有${result.matches.length - 3}个匹配项`);
      }
    });
  }
}

/**
 * 主函数
 */
function main() {
  console.log('开始验证V1 API清理...');
  
  try {
    // 检查结果
    const results = [];
    
    // 检查每个目录
    Object.values(config.directories).forEach(directory => {
      if (fs.existsSync(directory)) {
        const directoryResults = checkDirectoryForV1Content(directory, config.filePatterns);
        results.push(...directoryResults);
      }
    });
    
    // 生成报告
    generateReport(results);
    
    // 输出结果
    if (results.length === 0) {
      console.log('验证成功: 系统中没有发现V1 API相关内容');
      process.exit(0);
    } else {
      console.log(`验证失败: 系统中仍然存在${results.length}个文件包含V1 API相关内容`);
      console.log(`请查看报告文件: ${config.reportPath}`);
      process.exit(1);
    }
  } catch (error) {
    console.error('执行失败:', error.message);
    process.exit(1);
  }
}

// 执行主函数
main();
