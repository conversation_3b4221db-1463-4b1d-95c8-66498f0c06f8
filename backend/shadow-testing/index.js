/**
 * 影子测试入口文件
 * 导出所有影子测试相关的功能
 */
const createRequestDuplicator = require('./request-duplicator.middleware');
const { compareResponses, generateDiffReport, calculateSeverity } = require('./response-comparator');
const { recordComparison, getComparisons, getSummary, clearComparisons } = require('./comparison-recorder');
const shadowTestingRoutes = require('./shadow-testing.routes');
const shadowTestingConfig = require('./shadow-testing.config');

/**
 * 创建影子测试中间件
 * @param {Object} options - 中间件选项
 * @returns {Function} Express中间件
 */
const createShadowTestingMiddleware = (options = {}) => {
  return createRequestDuplicator({
    ...shadowTestingConfig,
    ...options
  });
};

/**
 * 注册影子测试路由
 * @param {Object} app - Express应用实例
 * @param {string} prefix - 路由前缀
 */
const registerShadowTestingRoutes = (app, prefix = '/api/v2/shadow-testing') => {
  app.use(prefix, shadowTestingRoutes);
};

module.exports = {
  // 中间件
  createShadowTestingMiddleware,
  
  // 路由
  registerShadowTestingRoutes,
  shadowTestingRoutes,
  
  // 工具
  compareResponses,
  generateDiffReport,
  calculateSeverity,
  recordComparison,
  getComparisons,
  getSummary,
  clearComparisons,
  
  // 配置
  config: shadowTestingConfig
};
