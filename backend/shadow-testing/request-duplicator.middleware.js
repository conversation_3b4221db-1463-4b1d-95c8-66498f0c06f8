/**
 * 请求复制中间件
 * 用于将请求同时发送到模拟API和真实API
 */
const axios = require('axios');
const logger = require('../config/logger');
const { recordComparison } = require('./comparison-recorder');

/**
 * 创建请求复制中间件
 * @param {Object} options - 中间件选项
 * @returns {Function} Express中间件
 */
const createRequestDuplicator = (options = {}) => {
  const defaultOptions = {
    enabled: process.env.SHADOW_TESTING_ENABLED === 'true',
    stubApiBaseUrl: process.env.STUB_API_BASE_URL || 'http://localhost:3001',
    realApiBaseUrl: process.env.REAL_API_BASE_URL || 'http://localhost:3000',
    sampleRate: parseFloat(process.env.SHADOW_TESTING_SAMPLE_RATE) || 0.1, // 默认10%的请求
    excludePaths: (process.env.SHADOW_TESTING_EXCLUDE_PATHS || '').split(',').filter(Boolean),
    includePaths: (process.env.SHADOW_TESTING_INCLUDE_PATHS || '').split(',').filter(Boolean),
    timeout: parseInt(process.env.SHADOW_TESTING_TIMEOUT) || 5000, // 默认5秒超时
    headers: {
      'X-Shadow-Testing': 'true'
    }
  };

  const config = { ...defaultOptions, ...options };

  // 创建用于发送请求的axios实例
  const stubApiClient = axios.create({
    baseURL: config.stubApiBaseUrl,
    timeout: config.timeout,
    headers: {
      ...config.headers,
      'X-Shadow-Testing-Target': 'stub'
    }
  });

  const realApiClient = axios.create({
    baseURL: config.realApiBaseUrl,
    timeout: config.timeout,
    headers: {
      ...config.headers,
      'X-Shadow-Testing-Target': 'real'
    }
  });

  /**
   * 判断是否应该复制请求
   * @param {Object} req - Express请求对象
   * @returns {boolean} 是否应该复制请求
   */
  const shouldDuplicateRequest = (req) => {
    // 如果未启用，则不复制
    if (!config.enabled) {
      return false;
    }

    // 如果是影子测试请求，则不复制（避免无限循环）
    if (req.headers['x-shadow-testing']) {
      return false;
    }

    // 如果是非GET请求，则根据采样率决定是否复制
    if (req.method !== 'GET') {
      return Math.random() < config.sampleRate;
    }

    // 检查是否在排除路径列表中
    if (config.excludePaths.length > 0) {
      for (const path of config.excludePaths) {
        if (req.path.startsWith(path)) {
          return false;
        }
      }
    }

    // 检查是否在包含路径列表中
    if (config.includePaths.length > 0) {
      for (const path of config.includePaths) {
        if (req.path.startsWith(path)) {
          return true;
        }
      }
      return false;
    }

    // 默认根据采样率决定是否复制
    return Math.random() < config.sampleRate;
  };

  /**
   * 复制请求到模拟API和真实API
   * @param {Object} req - Express请求对象
   * @returns {Promise<Object>} 响应对象
   */
  const duplicateRequest = async (req) => {
    try {
      // 构建请求配置
      const requestConfig = {
        method: req.method,
        url: req.path,
        params: req.query,
        data: req.body,
        headers: {
          ...req.headers,
          host: undefined, // 移除host头，避免冲突
          'content-length': undefined // 移除content-length头，让axios自动计算
        }
      };

      // 如果有认证头，则复制
      if (req.headers.authorization) {
        requestConfig.headers.authorization = req.headers.authorization;
      }

      // 发送请求到模拟API和真实API
      const [stubResponse, realResponse] = await Promise.allSettled([
        stubApiClient(requestConfig),
        realApiClient(requestConfig)
      ]);

      // 记录比较结果
      recordComparison({
        path: req.path,
        method: req.method,
        query: req.query,
        body: req.body,
        stubResponse: stubResponse.status === 'fulfilled' ? stubResponse.value.data : null,
        realResponse: realResponse.status === 'fulfilled' ? realResponse.value.data : null,
        stubError: stubResponse.status === 'rejected' ? stubResponse.reason.message : null,
        realError: realResponse.status === 'rejected' ? realResponse.reason.message : null,
        timestamp: new Date().toISOString()
      });

      return {
        stubResponse: stubResponse.status === 'fulfilled' ? stubResponse.value.data : null,
        realResponse: realResponse.status === 'fulfilled' ? realResponse.value.data : null,
        stubError: stubResponse.status === 'rejected' ? stubResponse.reason : null,
        realError: realResponse.status === 'rejected' ? realResponse.reason : null
      };
    } catch (error) {
      logger.error(`影子测试请求复制失败: ${error.message}`);
      return {
        stubResponse: null,
        realResponse: null,
        stubError: error,
        realError: error
      };
    }
  };

  // 返回中间件函数
  return (req, res, next) => {
    // 判断是否应该复制请求
    if (!shouldDuplicateRequest(req)) {
      return next();
    }

    // 复制请求（不等待结果，避免影响原始请求的响应时间）
    duplicateRequest(req).catch(error => {
      logger.error(`影子测试请求复制失败: ${error.message}`);
    });

    // 继续处理原始请求
    next();
  };
};

module.exports = createRequestDuplicator;
