/**
 * 影子测试配置
 * 配置影子测试的参数
 */

module.exports = {
  // 是否启用影子测试
  enabled: process.env.SHADOW_TESTING_ENABLED === 'true',

  // 模拟API基础URL
  stubApiBaseUrl: process.env.STUB_API_BASE_URL || 'http://localhost:3001',

  // 真实API基础URL
  realApiBaseUrl: process.env.REAL_API_BASE_URL || 'http://localhost:3000',

  // 采样率（0-1之间的小数，表示要复制的请求比例）
  sampleRate: parseFloat(process.env.SHADOW_TESTING_SAMPLE_RATE) || 0.1,

  // 排除路径（不进行影子测试的路径）
  excludePaths: [
    '/api/v2/shadow-testing',
    '/health',
    '/api-docs',
    '/swagger.json',
    '/redoc',
    '/public',
    '/api/v1/files',
    '/api/v1/upload'
  ],

  // 包含路径（只对这些路径进行影子测试，如果为空则对所有路径进行测试）
  includePaths: [],

  // 请求超时时间（毫秒）
  timeout: parseInt(process.env.SHADOW_TESTING_TIMEOUT) || 5000,

  // 最大比较结果数量
  maxComparisons: parseInt(process.env.SHADOW_TESTING_MAX_COMPARISONS) || 1000,

  // 比较选项
  comparisonOptions: {
    // 忽略的字段
    ignoreFields: ['timestamp', 'id', 'createdAt', 'updatedAt'],

    // 是否忽略数组顺序
    ignoreArrayOrder: true,

    // 是否忽略额外字段
    ignoreExtraFields: false,

    // 是否生成详细差异
    detailedDiff: true
  },

  // 优先级排序
  priorityPaths: [
    '/api/v2/auth',
    '/api/v2/users',
    '/api/v2/learning-plans',
    '/api/v2/tags'
  ]
};
