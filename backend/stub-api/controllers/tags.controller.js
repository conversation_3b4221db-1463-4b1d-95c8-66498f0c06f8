/**
 * 标签控制器
 * 处理标签相关的请求
 */
const {
  getMockData,
  getMockItem,
  addMockItem,
  updateMockItem,
  softDeleteMockItem,
  restoreMockItem,
  queryMockData
} = require('../data/loader');

/**
 * 获取所有标签
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getAllTags = (req, res) => {
  try {
    // 解析查询参数
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const includeDeleted = req.query.includeDeleted === 'true';
    const category = req.query.category;
    const isVerified = req.query.isVerified === 'true';
    const isOfficial = req.query.isOfficial === 'true';
    const sortBy = req.query.sortBy || 'id';
    const sortOrder = req.query.sortOrder === 'desc' ? -1 : 1;
    
    // 构建查询条件
    const query = {};
    if (category) query.category = category;
    if (isVerified !== undefined) query.isVerified = isVerified;
    if (isOfficial !== undefined) query.isOfficial = isOfficial;
    
    // 构建查询选项
    const options = {
      page,
      limit,
      includeDeleted,
      sort: { [sortBy]: sortOrder }
    };
    
    // 查询数据
    const result = queryMockData('tags', query, options);
    
    // 返回响应
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取标签失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '获取标签失败',
        details: { error: error.message }
      }
    });
  }
};

/**
 * 获取单个标签
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getTagById = (req, res) => {
  try {
    const id = req.params.id;
    const tag = getMockItem('tags', id);
    
    if (!tag) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `找不到ID为${id}的标签`,
          details: {}
        }
      });
    }
    
    // 检查是否已软删除
    if (tag.deletedAt && req.query.includeDeleted !== 'true') {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `找不到ID为${id}的标签`,
          details: {}
        }
      });
    }
    
    res.json({
      success: true,
      data: tag
    });
  } catch (error) {
    console.error('获取标签失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '获取标签失败',
        details: { error: error.message }
      }
    });
  }
};

/**
 * 创建标签
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const createTag = (req, res) => {
  try {
    const { name, description, categoryId, category, isVerified, isOfficial } = req.body;
    
    // 验证必填字段
    if (!name) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '标签名称不能为空',
          details: { name: '标签名称不能为空' }
        }
      });
    }
    
    // 创建标签
    const newTag = addMockItem('tags', {
      name,
      description,
      categoryId: categoryId || 1,
      category: category || '技术',
      relevanceScore: 0.5,
      usageCount: 0,
      isVerified: isVerified || false,
      isOfficial: isOfficial || false
    });
    
    res.status(201).json({
      success: true,
      data: newTag
    });
  } catch (error) {
    console.error('创建标签失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '创建标签失败',
        details: { error: error.message }
      }
    });
  }
};

/**
 * 更新标签
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const updateTag = (req, res) => {
  try {
    const id = req.params.id;
    const { name, description, categoryId, category, isVerified, isOfficial } = req.body;
    
    // 查找标签
    const tag = getMockItem('tags', id);
    
    if (!tag) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `找不到ID为${id}的标签`,
          details: {}
        }
      });
    }
    
    // 检查是否已软删除
    if (tag.deletedAt) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_OPERATION',
          message: '无法更新已删除的标签',
          details: {}
        }
      });
    }
    
    // 更新标签
    const updatedTag = updateMockItem('tags', id, {
      name: name || tag.name,
      description: description !== undefined ? description : tag.description,
      categoryId: categoryId || tag.categoryId,
      category: category || tag.category,
      isVerified: isVerified !== undefined ? isVerified : tag.isVerified,
      isOfficial: isOfficial !== undefined ? isOfficial : tag.isOfficial
    });
    
    res.json({
      success: true,
      data: updatedTag
    });
  } catch (error) {
    console.error('更新标签失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '更新标签失败',
        details: { error: error.message }
      }
    });
  }
};

/**
 * 软删除标签
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const softDeleteTag = (req, res) => {
  try {
    const id = req.params.id;
    
    // 查找标签
    const tag = getMockItem('tags', id);
    
    if (!tag) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `找不到ID为${id}的标签`,
          details: {}
        }
      });
    }
    
    // 检查是否已软删除
    if (tag.deletedAt) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_OPERATION',
          message: '标签已经被删除',
          details: {}
        }
      });
    }
    
    // 软删除标签
    const deletedTag = softDeleteMockItem('tags', id);
    
    res.json({
      success: true,
      data: deletedTag
    });
  } catch (error) {
    console.error('删除标签失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '删除标签失败',
        details: { error: error.message }
      }
    });
  }
};

/**
 * 恢复已删除的标签
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const restoreTag = (req, res) => {
  try {
    const id = req.params.id;
    
    // 查找标签
    const tag = getMockItem('tags', id);
    
    if (!tag) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `找不到ID为${id}的标签`,
          details: {}
        }
      });
    }
    
    // 检查是否已软删除
    if (!tag.deletedAt) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_OPERATION',
          message: '标签未被删除',
          details: {}
        }
      });
    }
    
    // 恢复标签
    const restoredTag = restoreMockItem('tags', id);
    
    res.json({
      success: true,
      data: restoredTag
    });
  } catch (error) {
    console.error('恢复标签失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '恢复标签失败',
        details: { error: error.message }
      }
    });
  }
};

module.exports = {
  getAllTags,
  getTagById,
  createTag,
  updateTag,
  softDeleteTag,
  restoreTag
};
