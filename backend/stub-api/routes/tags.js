/**
 * 标签路由
 */
const express = require('express');
const router = express.Router();
const {
  getAllTags,
  getTagById,
  createTag,
  updateTag,
  softDeleteTag,
  restoreTag
} = require('../controllers/tags.controller');

// 获取所有标签
router.get('/', getAllTags);

// 获取单个标签
router.get('/:id', getTagById);

// 创建标签
router.post('/', createTag);

// 更新标签
router.put('/:id', updateTag);

// 软删除标签
router.delete('/:id/soft-delete', softDeleteTag);

// 恢复已删除的标签
router.post('/:id/restore', restoreTag);

// 兼容V1版本的删除接口
router.delete('/:id', softDeleteTag);

module.exports = router;
