import { ExerciseApplicationService } from '../../../../../application/services/content/exercise/ExerciseApplicationService';
import { ExerciseRepository } from '../../../../../domain/repositories/content/exercise/ExerciseRepository';
import { ContentRecommendationService } from '../../../../../domain/services/content/ContentRecommendationService';
import { UnitOfWork } from '../../../../../infrastructure/persistence/UnitOfWork';
import { CreateExerciseCommand } from '../../../../../application/commands/content/exercise/CreateExerciseCommand';
import { UpdateExerciseCommand } from '../../../../../application/commands/content/exercise/UpdateExerciseCommand';
import { DeleteExerciseCommand } from '../../../../../application/commands/content/exercise/DeleteExerciseCommand';
import { RestoreExerciseCommand } from '../../../../../application/commands/content/exercise/RestoreExerciseCommand';
import { PublishExerciseCommand } from '../../../../../application/commands/content/exercise/PublishExerciseCommand';
import { AddExerciseTagCommand } from '../../../../../application/commands/content/exercise/AddExerciseTagCommand';
import { RemoveExerciseTagCommand } from '../../../../../application/commands/content/exercise/RemoveExerciseTagCommand';
import { GetExerciseQuery } from '../../../../../application/queries/content/exercise/GetExerciseQuery';
import { SearchExercisesQuery } from '../../../../../application/queries/content/exercise/SearchExercisesQuery';
import { Exercise } from '../../../../../domain/models/content/exercise/Exercise';
import { Difficulty } from '../../../../../domain/models/content/Difficulty';
import { Visibility } from '../../../../../domain/models/content/Visibility';
import { ContentStatus } from '../../../../../domain/models/content/ContentStatus';

// 创建模拟对象
const mockExerciseRepository: jest.Mocked<ExerciseRepository> = {
  save: jest.fn(),
  findById: jest.fn(),
  findAll: jest.fn(),
  findByTagId: jest.fn(),
  findByTagIdAndDifficulty: jest.fn(),
  findByCreatorId: jest.fn(),
  searchByKeyword: jest.fn(),
  delete: jest.fn()
};

const mockContentRecommendationService: jest.Mocked<ContentRecommendationService> = {
  recommendSimilarExercises: jest.fn(),
  recommendSimilarNotes: jest.fn(),
  recommendContentByTags: jest.fn(),
  recommendContentByUserPreferences: jest.fn(),
  recommendTrendingContent: jest.fn()
};

const mockUnitOfWork: jest.Mocked<UnitOfWork> = {
  runInTransaction: jest.fn().mockImplementation(callback => callback())
};

describe('ExerciseApplicationService', () => {
  let exerciseApplicationService: ExerciseApplicationService;

  beforeEach(() => {
    jest.clearAllMocks();
    exerciseApplicationService = new ExerciseApplicationService(
      mockExerciseRepository,
      mockContentRecommendationService,
      mockUnitOfWork
    );
  });

  describe('createExercise', () => {
    it('should create an exercise and return the DTO', async () => {
      // 准备
      const command: CreateExerciseCommand = {
        title: '测试练习',
        description: '这是一个测试练习的描述',
        expectedResult: '预期结果是完成测试',
        difficulty: Difficulty.MEDIUM,
        timeEstimateMinutes: 15,
        creatorId: 'user1',
        visibility: Visibility.PUBLIC,
        isOfficial: false,
        tags: ['标签1', '标签2']
      };

      // 模拟保存后的练习
      const savedExercise = Exercise.create(
        command.title,
        command.description,
        command.expectedResult,
        command.difficulty,
        command.timeEstimateMinutes,
        command.creatorId,
        command.visibility,
        command.isOfficial
      );

      // 添加标签
      command.tags.forEach(tag => savedExercise.addTag(tag));

      // 设置ID
      Object.defineProperty(savedExercise, 'id', { value: 1 });

      mockExerciseRepository.save.mockResolvedValue(savedExercise);

      // 执行
      const result = await exerciseApplicationService.createExercise(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockExerciseRepository.save).toHaveBeenCalled();

      expect(result).toEqual({
        id: 1,
        title: command.title,
        description: command.description,
        expectedResult: command.expectedResult,
        difficulty: command.difficulty,
        timeEstimateMinutes: command.timeEstimateMinutes,
        creatorId: command.creatorId,
        status: ContentStatus.DRAFT,
        visibility: command.visibility,
        isOfficial: command.isOfficial,
        tags: command.tags,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        deletedAt: null,
        isDeleted: false,
        isPublished: false
      });
    });
  });

  describe('getExercise', () => {
    it('should return the exercise DTO if the exercise exists', async () => {
      // 准备
      const query: GetExerciseQuery = {
        exerciseId: 1
      };

      const exercise = Exercise.create(
        '测试练习',
        '这是一个测试练习的描述',
        '预期结果是完成测试',
        Difficulty.MEDIUM,
        15,
        'user1',
        Visibility.PUBLIC,
        false
      );

      // 设置ID
      Object.defineProperty(exercise, 'id', { value: 1 });

      mockExerciseRepository.findById.mockResolvedValue(exercise);

      // 执行
      const result = await exerciseApplicationService.getExercise(query);

      // 验证
      expect(mockExerciseRepository.findById).toHaveBeenCalledWith(1);
      expect(result).not.toBeNull();
      expect(result!.id).toBe(1);
      expect(result!.title).toBe('测试练习');
    });

    it('should return null if the exercise does not exist', async () => {
      // 准备
      const query: GetExerciseQuery = {
        exerciseId: 999
      };

      mockExerciseRepository.findById.mockResolvedValue(null);

      // 执行
      const result = await exerciseApplicationService.getExercise(query);

      // 验证
      expect(mockExerciseRepository.findById).toHaveBeenCalledWith(999);
      expect(result).toBeNull();
    });
  });

  describe('deleteExercise', () => {
    it('should soft delete the exercise', async () => {
      // 准备
      const command: DeleteExerciseCommand = {
        exerciseId: 1
      };

      const exercise = Exercise.create(
        '测试练习',
        '这是一个测试练习的描述',
        '预期结果是完成测试',
        Difficulty.MEDIUM,
        15,
        'user1',
        Visibility.PUBLIC,
        false
      );

      // 设置ID
      Object.defineProperty(exercise, 'id', { value: 1 });

      mockExerciseRepository.findById.mockResolvedValue(exercise);
      mockExerciseRepository.save.mockResolvedValue(exercise);

      // 执行
      await exerciseApplicationService.deleteExercise(command);

      // 验证
      expect(mockExerciseRepository.findById).toHaveBeenCalledWith(1);
      expect(exercise.isDeleted).toBe(true);
      expect(mockExerciseRepository.save).toHaveBeenCalledWith(exercise);
    });

    it('should throw an error if the exercise does not exist', async () => {
      // 准备
      const command: DeleteExerciseCommand = {
        exerciseId: 999
      };

      mockExerciseRepository.findById.mockResolvedValue(null);

      // 执行和验证
      await expect(exerciseApplicationService.deleteExercise(command))
        .rejects.toThrow('练习ID 999 不存在');
    });
  });

  describe('restoreExercise', () => {
    it('should restore a deleted exercise', async () => {
      // 准备
      const command: RestoreExerciseCommand = {
        exerciseId: 1
      };

      const exercise = Exercise.create(
        '测试练习',
        '这是一个测试练习的描述',
        '预期结果是完成测试',
        Difficulty.MEDIUM,
        15,
        'user1',
        Visibility.PUBLIC,
        false
      );

      // 设置ID并软删除
      Object.defineProperty(exercise, 'id', { value: 1 });
      exercise.softDelete();

      mockExerciseRepository.findById.mockResolvedValue(exercise);
      mockExerciseRepository.save.mockResolvedValue(exercise);

      // 执行
      const result = await exerciseApplicationService.restoreExercise(command);

      // 验证
      expect(mockExerciseRepository.findById).toHaveBeenCalledWith(1);
      expect(exercise.isDeleted).toBe(false);
      expect(mockExerciseRepository.save).toHaveBeenCalledWith(exercise);
      expect(result.isDeleted).toBe(false);
    });
  });

  describe('publishExercise', () => {
    it('should publish an exercise', async () => {
      // 准备
      const command: PublishExerciseCommand = {
        exerciseId: 1
      };

      const exercise = Exercise.create(
        '测试练习',
        '这是一个测试练习的描述',
        '预期结果是完成测试',
        Difficulty.MEDIUM,
        15,
        'user1',
        Visibility.PUBLIC,
        false
      );

      // 设置ID
      Object.defineProperty(exercise, 'id', { value: 1 });

      mockExerciseRepository.findById.mockResolvedValue(exercise);
      mockExerciseRepository.save.mockResolvedValue(exercise);

      // 执行
      const result = await exerciseApplicationService.publishExercise(command);

      // 验证
      expect(mockExerciseRepository.findById).toHaveBeenCalledWith(1);
      expect(exercise.isPublished).toBe(true);
      expect(mockExerciseRepository.save).toHaveBeenCalledWith(exercise);
      expect(result.isPublished).toBe(true);
    });
  });

  describe('searchExercises', () => {
    it('should search exercises by keyword', async () => {
      // 准备
      const query: SearchExercisesQuery = {
        keyword: '测试',
        includeDeleted: false,
        sortBy: 'date',
        page: 1,
        pageSize: 10
      };

      const exercises = [
        createTestExercise(1, '测试练习1'),
        createTestExercise(2, '测试练习2')
      ];

      mockExerciseRepository.searchByKeyword.mockResolvedValue(exercises);

      // 执行
      const result = await exerciseApplicationService.searchExercises(query);

      // 验证
      expect(mockExerciseRepository.searchByKeyword).toHaveBeenCalledWith('测试');
      expect(result.length).toBe(2);
      expect(result[0].title).toBe('测试练习1');
      expect(result[1].title).toBe('测试练习2');
    });

    it('should search exercises by tag ID', async () => {
      // 准备
      const query: SearchExercisesQuery = {
        tagIds: [1],
        includeDeleted: false,
        sortBy: 'date',
        page: 1,
        pageSize: 10
      };

      const exercises = [
        createTestExercise(1, '标签练习1'),
        createTestExercise(2, '标签练习2')
      ];

      mockExerciseRepository.findByTagId.mockResolvedValue(exercises);

      // 执行
      const result = await exerciseApplicationService.searchExercises(query);

      // 验证
      expect(mockExerciseRepository.findByTagId).toHaveBeenCalledWith(1);
      expect(result.length).toBe(2);
    });
  });

  // 辅助函数：创建测试练习
  function createTestExercise(id: number, title: string): Exercise {
    const exercise = Exercise.create(
      title,
      '这是一个测试练习的描述',
      '预期结果是完成测试',
      Difficulty.MEDIUM,
      15,
      'user1',
      Visibility.PUBLIC,
      false
    );

    // 设置ID
    Object.defineProperty(exercise, 'id', { value: id });

    return exercise;
  }});
