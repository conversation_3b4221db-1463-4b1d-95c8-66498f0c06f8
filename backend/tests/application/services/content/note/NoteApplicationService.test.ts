import { NoteApplicationService } from '../../../../../application/services/content/note/NoteApplicationService';
import { Note } from '../../../../../domain/models/content/note/Note';
import { ContentStatus } from '../../../../../domain/models/content/ContentStatus';
import { Visibility } from '../../../../../domain/models/content/Visibility';
import { CreateNoteCommand } from '../../../../../application/commands/content/note/CreateNoteCommand';
import { UpdateNoteCommand } from '../../../../../application/commands/content/note/UpdateNoteCommand';
import { DeleteNoteCommand } from '../../../../../application/commands/content/note/DeleteNoteCommand';
import { RestoreNoteCommand } from '../../../../../application/commands/content/note/RestoreNoteCommand';
import { PublishNoteCommand } from '../../../../../application/commands/content/note/PublishNoteCommand';
import { AddNoteTagCommand } from '../../../../../application/commands/content/note/AddNoteTagCommand';
import { RemoveNoteTagCommand } from '../../../../../application/commands/content/note/RemoveNoteTagCommand';
import { GetNoteQuery } from '../../../../../application/queries/content/note/GetNoteQuery';
import { SearchNotesQuery } from '../../../../../application/queries/content/note/SearchNotesQuery';
import { ContentRecommendationService } from '../../../../../domain/services/content/ContentRecommendationService';
import { UnitOfWork } from '../../../../../infrastructure/persistence/UnitOfWork';
import { NoteRepository } from '../../../../../domain/repositories/content/note/NoteRepository';

// 模拟依赖
const mockNoteRepository: jest.Mocked<NoteRepository> = {
  save: jest.fn(),
  findById: jest.fn(),
  findByUserId: jest.fn(),
  findByTagId: jest.fn(),
  findByPlanId: jest.fn(),
  searchByKeyword: jest.fn(),
  findDeleted: jest.fn(),
  findRecent: jest.fn(),
  findPopular: jest.fn(),
  findAiGenerated: jest.fn(),
  updateLikeCount: jest.fn(),
  updateCommentCount: jest.fn(),
  updateViewCount: jest.fn(),
  findAll: jest.fn(),
  delete: jest.fn()
};

const mockContentRecommendationService: jest.Mocked<ContentRecommendationService> = {
  recommendSimilarExercises: jest.fn(),
  recommendSimilarNotes: jest.fn(),
  recommendContentByTags: jest.fn(),
  recommendContentByUserPreferences: jest.fn(),
  recommendTrendingContent: jest.fn()
};

const mockUnitOfWork: jest.Mocked<UnitOfWork> = {
  runInTransaction: jest.fn().mockImplementation(callback => callback())
};

describe('NoteApplicationService', () => {
  let noteApplicationService: NoteApplicationService;

  beforeEach(() => {
    jest.clearAllMocks();
    noteApplicationService = new NoteApplicationService(
      mockNoteRepository,
      mockContentRecommendationService,
      mockUnitOfWork
    );
  });

  describe('createNote', () => {
    it('should create a note and return a DTO', async () => {
      // 准备
      const command: CreateNoteCommand = {
        title: '测试笔记',
        content: '这是一个测试笔记的内容',
        userId: 'user1',
        imageUrl: 'https://example.com/image.jpg',
        visibility: Visibility.PUBLIC,
        isAiGenerated: false,
        planId: 1,
        tags: ['标签1', '标签2']
      };

      const note = Note.create(
        command.title,
        command.content,
        command.userId,
        command.imageUrl,
        command.visibility,
        command.isAiGenerated,
        command.planId
      );

      // 设置ID
      Object.defineProperty(note, 'id', { value: 1 });

      // 添加标签
      command.tags?.forEach(tag => note.addTag(tag));

      mockNoteRepository.save.mockResolvedValue(note);

      // 执行
      const result = await noteApplicationService.createNote(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        id: 1,
        title: '测试笔记',
        content: '这是一个测试笔记的内容',
        userId: 'user1',
        imageUrl: 'https://example.com/image.jpg',
        visibility: Visibility.PUBLIC,
        status: ContentStatus.DRAFT,
        isAiGenerated: false,
        planId: 1,
        tags: ['标签1', '标签2'],
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        deletedAt: null,
        isDeleted: false,
        isPublished: false
      });
    });
  });

  describe('updateNote', () => {
    it('should update a note and return a DTO', async () => {
      // 准备
      const command: UpdateNoteCommand = {
        noteId: 1,
        title: '更新后的标题',
        content: '更新后的内容',
        imageUrl: 'https://example.com/new-image.jpg',
        visibility: Visibility.PRIVATE,
        planId: 2,
        tags: ['标签1', '标签3']
      };

      const note = Note.create(
        '原标题',
        '原内容',
        'user1',
        'https://example.com/old-image.jpg',
        Visibility.PUBLIC,
        false,
        1
      );

      // 设置ID
      Object.defineProperty(note, 'id', { value: 1 });

      // 添加原始标签
      note.addTag('标签1');
      note.addTag('标签2');

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.updateNote(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(result.title).toBe('更新后的标题');
      expect(result.content).toBe('更新后的内容');
      expect(result.imageUrl).toBe('https://example.com/new-image.jpg');
      expect(result.visibility).toBe(Visibility.PRIVATE);
      expect(result.planId).toBe(2);
      expect(result.tags).toContain('标签1');
      expect(result.tags).toContain('标签3');
      expect(result.tags).not.toContain('标签2');
    });

    it('should throw an error if the note does not exist', async () => {
      // 准备
      const command: UpdateNoteCommand = {
        noteId: 1,
        title: '更新后的标题',
        content: '更新后的内容'
      };

      mockNoteRepository.findById.mockResolvedValue(null);

      // 执行和验证
      await expect(noteApplicationService.updateNote(command)).rejects.toThrow('笔记ID 1 不存在');
    });
  });

  describe('deleteNote', () => {
    it('should soft delete a note', async () => {
      // 准备
      const command: DeleteNoteCommand = {
        noteId: 1
      };

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      await noteApplicationService.deleteNote(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(note.isDeleted).toBe(true);
    });

    it('should throw an error if the note does not exist', async () => {
      // 准备
      const command: DeleteNoteCommand = {
        noteId: 1
      };

      mockNoteRepository.findById.mockResolvedValue(null);

      // 执行和验证
      await expect(noteApplicationService.deleteNote(command)).rejects.toThrow('笔记ID 1 不存在');
    });
  });

  describe('restoreNote', () => {
    it('should restore a deleted note', async () => {
      // 准备
      const command: RestoreNoteCommand = {
        noteId: 1
      };

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });
      note.softDelete();

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.restoreNote(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(result.isDeleted).toBe(false);
    });
  });

  describe('publishNote', () => {
    it('should publish a note', async () => {
      // 准备
      const command: PublishNoteCommand = {
        noteId: 1
      };

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.publishNote(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(result.isPublished).toBe(true);
    });
  });

  describe('addNoteTag', () => {
    it('should add a tag to a note', async () => {
      // 准备
      const command: AddNoteTagCommand = {
        noteId: 1,
        tag: '新标签'
      };

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.addNoteTag(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(result.tags).toContain('新标签');
    });
  });

  describe('removeNoteTag', () => {
    it('should remove a tag from a note', async () => {
      // 准备
      const command: RemoveNoteTagCommand = {
        noteId: 1,
        tag: '标签1'
      };

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });
      note.addTag('标签1');

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.removeNoteTag(command);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(result.tags).not.toContain('标签1');
    });
  });

  describe('incrementLikeCount', () => {
    it('should increment the like count of a note', async () => {
      // 准备
      const noteId = 1;

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.incrementLikeCount(noteId);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(mockNoteRepository.updateLikeCount).toHaveBeenCalledWith(1, 1);
      expect(result.likeCount).toBe(1);
    });
  });

  describe('decrementLikeCount', () => {
    it('should decrement the like count of a note', async () => {
      // 准备
      const noteId = 1;

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });
      note.incrementLikeCount(); // 先增加一次

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.decrementLikeCount(noteId);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(mockNoteRepository.updateLikeCount).toHaveBeenCalledWith(1, 0);
      expect(result.likeCount).toBe(0);
    });
  });

  describe('incrementViewCount', () => {
    it('should increment the view count of a note', async () => {
      // 准备
      const noteId = 1;

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });

      mockNoteRepository.findById.mockResolvedValue(note);
      mockNoteRepository.save.mockImplementation(async (updatedNote) => updatedNote);

      // 执行
      const result = await noteApplicationService.incrementViewCount(noteId);

      // 验证
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockNoteRepository.save).toHaveBeenCalled();
      expect(mockNoteRepository.updateViewCount).toHaveBeenCalledWith(1, 1);
      expect(result.viewCount).toBe(1);
    });
  });

  describe('getNote', () => {
    it('should return a note DTO if the note exists', async () => {
      // 准备
      const query: GetNoteQuery = {
        noteId: 1
      };

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });

      mockNoteRepository.findById.mockResolvedValue(note);

      // 执行
      const result = await noteApplicationService.getNote(query);

      // 验证
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(result).not.toBeNull();
      expect(result!.id).toBe(1);
      expect(result!.title).toBe('测试笔记');
    });

    it('should return null if the note does not exist', async () => {
      // 准备
      const query: GetNoteQuery = {
        noteId: 1
      };

      mockNoteRepository.findById.mockResolvedValue(null);

      // 执行
      const result = await noteApplicationService.getNote(query);

      // 验证
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(result).toBeNull();
    });
  });

  describe('searchNotes', () => {
    it('should search notes by keyword', async () => {
      // 准备
      const query: SearchNotesQuery = {
        keyword: '测试'
      };

      const notes = [
        Note.create('测试笔记1', '内容1', 'user1'),
        Note.create('测试笔记2', '内容2', 'user1')
      ];
      Object.defineProperty(notes[0], 'id', { value: 1 });
      Object.defineProperty(notes[1], 'id', { value: 2 });

      mockNoteRepository.searchByKeyword.mockResolvedValue(notes);

      // 执行
      const results = await noteApplicationService.searchNotes(query);

      // 验证
      expect(mockNoteRepository.searchByKeyword).toHaveBeenCalledWith('测试');
      expect(results.length).toBe(2);
      expect(results[0].title).toBe('测试笔记1');
      expect(results[1].title).toBe('测试笔记2');
    });

    it('should search notes by user ID', async () => {
      // 准备
      const query: SearchNotesQuery = {
        userId: 'user1'
      };

      const notes = [
        Note.create('笔记1', '内容1', 'user1'),
        Note.create('笔记2', '内容2', 'user1')
      ];
      Object.defineProperty(notes[0], 'id', { value: 1 });
      Object.defineProperty(notes[1], 'id', { value: 2 });

      mockNoteRepository.findByUserId.mockResolvedValue(notes);

      // 执行
      const results = await noteApplicationService.searchNotes(query);

      // 验证
      expect(mockNoteRepository.findByUserId).toHaveBeenCalledWith('user1');
      expect(results.length).toBe(2);
    });
  });

  describe('getSimilarNotes', () => {
    it('should return similar notes', async () => {
      // 准备
      const noteId = 1;
      const limit = 3;

      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      Object.defineProperty(note, 'id', { value: 1 });

      const similarNotes = [
        Note.create('相似笔记1', '内容1', 'user1'),
        Note.create('相似笔记2', '内容2', 'user1')
      ];
      Object.defineProperty(similarNotes[0], 'id', { value: 2 });
      Object.defineProperty(similarNotes[1], 'id', { value: 3 });

      mockNoteRepository.findById.mockResolvedValue(note);
      mockContentRecommendationService.recommendSimilarNotes.mockResolvedValue(similarNotes);

      // 执行
      const results = await noteApplicationService.getSimilarNotes(noteId, limit);

      // 验证
      expect(mockNoteRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContentRecommendationService.recommendSimilarNotes).toHaveBeenCalledWith(note, limit);
      expect(results.length).toBe(2);
      expect(results[0].title).toBe('相似笔记1');
      expect(results[1].title).toBe('相似笔记2');
    });

    it('should throw an error if the note does not exist', async () => {
      // 准备
      const noteId = 1;
      const limit = 3;

      mockNoteRepository.findById.mockResolvedValue(null);

      // 执行和验证
      await expect(noteApplicationService.getSimilarNotes(noteId, limit)).rejects.toThrow('笔记ID 1 不存在');
    });
  });
});
