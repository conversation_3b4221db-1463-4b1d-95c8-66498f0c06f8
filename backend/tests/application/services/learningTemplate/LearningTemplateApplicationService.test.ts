import { LearningTemplateApplicationService } from '../../../../application/services/learningTemplate/LearningTemplateApplicationService';
import { LearningTemplateRepository } from '../../../../domain/repositories/learningTemplate/LearningTemplateRepository';
import { ThemeRepository } from '../../../../domain/repositories/content/theme/ThemeRepository';
import { UnitOfWork } from '../../../../infrastructure/persistence/UnitOfWork';
import { LearningTemplate } from '../../../../domain/models/learningTemplate/LearningTemplate';
import { Difficulty } from '../../../../domain/models/learningTemplate/Difficulty';
import { Rating } from '../../../../domain/models/learningTemplate/Rating';
import { TemplateStatus } from '../../../../domain/models/learningTemplate/TemplateStatus';
import { CreateLearningTemplateCommand } from '../../../../application/commands/learningTemplate/CreateLearningTemplateCommand';
import { UpdateLearningTemplateCommand } from '../../../../application/commands/learningTemplate/UpdateLearningTemplateCommand';
import { DeleteLearningTemplateCommand } from '../../../../application/commands/learningTemplate/DeleteLearningTemplateCommand';
import { RestoreLearningTemplateCommand } from '../../../../application/commands/learningTemplate/RestoreLearningTemplateCommand';
import { PublishLearningTemplateCommand } from '../../../../application/commands/learningTemplate/PublishLearningTemplateCommand';
import { GetLearningTemplateQuery } from '../../../../application/queries/learningTemplate/GetLearningTemplateQuery';

// 创建模拟对象
const mockLearningTemplateRepository = {
  findById: jest.fn(),
  findAll: jest.fn(),
  findByThemeId: jest.fn(),
  findByCreatorId: jest.fn(),
  findPublished: jest.fn(),
  findPopular: jest.fn(),
  findByDifficulty: jest.fn(),
  findByTagId: jest.fn(),
  findOfficial: jest.fn(),
  search: jest.fn(),
  save: jest.fn(),
  delete: jest.fn()
} as unknown as jest.Mocked<LearningTemplateRepository>;

const mockThemeRepository = {
  findById: jest.fn()
} as unknown as jest.Mocked<ThemeRepository>;

const mockUnitOfWork = {
  runInTransaction: jest.fn((callback) => callback())
} as unknown as jest.Mocked<UnitOfWork>;

describe('LearningTemplateApplicationService', () => {
  let service: LearningTemplateApplicationService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new LearningTemplateApplicationService(
      mockUnitOfWork,
      mockLearningTemplateRepository,
      mockThemeRepository
    );
  });

  describe('createTemplate', () => {
    it('should create a learning template successfully', async () => {
      // Arrange
      const command: CreateLearningTemplateCommand = {
        themeId: 1,
        title: 'Test Template',
        description: 'Test Description',
        coverImageUrl: 'http://example.com/image.jpg',
        difficulty: 'beginner',
        estimatedDays: 7,
        dailyGoalMinutes: 15,
        isOfficial: true,
        creatorId: 'user123',
        price: 0
      };

      const mockTheme = { id: 1, name: 'Test Theme' };
      mockThemeRepository.findById.mockResolvedValue(mockTheme);

      const mockTemplate = new LearningTemplate(
        1,
        command.themeId,
        command.title,
        command.description,
        command.coverImageUrl,
        Difficulty.BEGINNER,
        command.estimatedDays,
        command.dailyGoalMinutes,
        command.isOfficial,
        command.creatorId,
        0,
        Rating.create(5.0),
        0,
        command.price,
        TemplateStatus.DRAFT,
        new Date(),
        new Date(),
        null
      );
      mockLearningTemplateRepository.save.mockResolvedValue(mockTemplate);

      // Act
      const result = await service.createTemplate(command);

      // Assert
      expect(mockThemeRepository.findById).toHaveBeenCalledWith(command.themeId);
      expect(mockLearningTemplateRepository.save).toHaveBeenCalled();
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
      expect(result.title).toBe(command.title);
      expect(result.difficulty).toBe('beginner');
    });

    it('should throw an error if theme does not exist', async () => {
      // Arrange
      const command: CreateLearningTemplateCommand = {
        themeId: 999,
        title: 'Test Template',
        description: 'Test Description',
        coverImageUrl: 'http://example.com/image.jpg',
        difficulty: 'beginner',
        estimatedDays: 7,
        dailyGoalMinutes: 15,
        isOfficial: true,
        creatorId: 'user123',
        price: 0
      };

      mockThemeRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.createTemplate(command)).rejects.toThrow(`主题ID ${command.themeId} 不存在`);
      expect(mockThemeRepository.findById).toHaveBeenCalledWith(command.themeId);
      expect(mockLearningTemplateRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('updateTemplate', () => {
    it('should update a learning template successfully', async () => {
      // Arrange
      const command: UpdateLearningTemplateCommand = {
        id: 1,
        title: 'Updated Template',
        description: 'Updated Description',
        coverImageUrl: 'http://example.com/updated.jpg',
        difficulty: 'intermediate',
        estimatedDays: 14,
        dailyGoalMinutes: 30,
        price: 9.99
      };

      const mockTemplate = new LearningTemplate(
        1,
        1,
        'Original Title',
        'Original Description',
        'http://example.com/original.jpg',
        Difficulty.BEGINNER,
        7,
        15,
        true,
        'user123',
        0,
        Rating.create(5.0),
        0,
        0,
        TemplateStatus.DRAFT,
        new Date(),
        new Date(),
        null
      );
      mockLearningTemplateRepository.findById.mockResolvedValue(mockTemplate);
      mockLearningTemplateRepository.save.mockImplementation((template) => Promise.resolve(template));

      // Act
      const result = await service.updateTemplate(command);

      // Assert
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(command.id);
      expect(mockLearningTemplateRepository.save).toHaveBeenCalled();
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
      expect(result.title).toBe(command.title);
      expect(result.description).toBe(command.description);
      expect(result.difficulty).toBe('intermediate');
    });

    it('should throw an error if template does not exist', async () => {
      // Arrange
      const command: UpdateLearningTemplateCommand = {
        id: 999,
        title: 'Updated Template',
        description: 'Updated Description',
        coverImageUrl: 'http://example.com/updated.jpg',
        difficulty: 'intermediate',
        estimatedDays: 14,
        dailyGoalMinutes: 30,
        price: 9.99
      };

      mockLearningTemplateRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.updateTemplate(command)).rejects.toThrow(`学习模板ID ${command.id} 不存在`);
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(command.id);
      expect(mockLearningTemplateRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('deleteTemplate', () => {
    it('should delete a learning template successfully', async () => {
      // Arrange
      const command: DeleteLearningTemplateCommand = {
        id: 1
      };

      const mockTemplate = new LearningTemplate(
        1,
        1,
        'Test Template',
        'Test Description',
        'http://example.com/image.jpg',
        Difficulty.BEGINNER,
        7,
        15,
        true,
        'user123',
        0,
        Rating.create(5.0),
        0,
        0,
        TemplateStatus.DRAFT,
        new Date(),
        new Date(),
        null
      );
      mockLearningTemplateRepository.findById.mockResolvedValue(mockTemplate);
      mockLearningTemplateRepository.save.mockImplementation((template) => Promise.resolve(template));

      // Act
      await service.deleteTemplate(command);

      // Assert
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(command.id);
      expect(mockLearningTemplateRepository.save).toHaveBeenCalled();
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      
      // Verify that softDelete was called on the template
      const savedTemplate = mockLearningTemplateRepository.save.mock.calls[0][0] as LearningTemplate;
      expect(savedTemplate.deletedAt).not.toBeNull();
    });

    it('should throw an error if template does not exist', async () => {
      // Arrange
      const command: DeleteLearningTemplateCommand = {
        id: 999
      };

      mockLearningTemplateRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.deleteTemplate(command)).rejects.toThrow(`学习模板ID ${command.id} 不存在`);
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(command.id);
      expect(mockLearningTemplateRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('restoreTemplate', () => {
    it('should restore a deleted learning template successfully', async () => {
      // Arrange
      const command: RestoreLearningTemplateCommand = {
        id: 1
      };

      const deletedDate = new Date();
      const mockTemplate = new LearningTemplate(
        1,
        1,
        'Test Template',
        'Test Description',
        'http://example.com/image.jpg',
        Difficulty.BEGINNER,
        7,
        15,
        true,
        'user123',
        0,
        Rating.create(5.0),
        0,
        0,
        TemplateStatus.DRAFT,
        new Date(),
        new Date(),
        deletedDate
      );
      mockLearningTemplateRepository.findById.mockResolvedValue(mockTemplate);
      mockLearningTemplateRepository.save.mockImplementation((template) => Promise.resolve(template));

      // Act
      const result = await service.restoreTemplate(command);

      // Assert
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(command.id);
      expect(mockLearningTemplateRepository.save).toHaveBeenCalled();
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
      expect(result.deletedAt).toBeNull();
    });
  });

  describe('publishTemplate', () => {
    it('should publish a learning template successfully', async () => {
      // Arrange
      const command: PublishLearningTemplateCommand = {
        id: 1
      };

      const mockTemplate = new LearningTemplate(
        1,
        1,
        'Test Template',
        'Test Description',
        'http://example.com/image.jpg',
        Difficulty.BEGINNER,
        7,
        15,
        true,
        'user123',
        0,
        Rating.create(5.0),
        0,
        0,
        TemplateStatus.DRAFT,
        new Date(),
        new Date(),
        null
      );
      mockLearningTemplateRepository.findById.mockResolvedValue(mockTemplate);
      mockLearningTemplateRepository.save.mockImplementation((template) => Promise.resolve(template));

      // Act
      const result = await service.publishTemplate(command);

      // Assert
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(command.id);
      expect(mockLearningTemplateRepository.save).toHaveBeenCalled();
      expect(mockUnitOfWork.runInTransaction).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
      expect(result.status).toBe('published');
    });
  });

  describe('getTemplate', () => {
    it('should return a learning template by id', async () => {
      // Arrange
      const query: GetLearningTemplateQuery = {
        id: 1
      };

      const mockTemplate = new LearningTemplate(
        1,
        1,
        'Test Template',
        'Test Description',
        'http://example.com/image.jpg',
        Difficulty.BEGINNER,
        7,
        15,
        true,
        'user123',
        0,
        Rating.create(5.0),
        0,
        0,
        TemplateStatus.PUBLISHED,
        new Date(),
        new Date(),
        null
      );
      mockLearningTemplateRepository.findById.mockResolvedValue(mockTemplate);

      // Act
      const result = await service.getTemplate(query);

      // Assert
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(query.id);
      expect(result).toBeDefined();
      expect(result?.id).toBe(1);
      expect(result?.title).toBe('Test Template');
      expect(result?.status).toBe('published');
    });

    it('should return null if template does not exist', async () => {
      // Arrange
      const query: GetLearningTemplateQuery = {
        id: 999
      };

      mockLearningTemplateRepository.findById.mockResolvedValue(null);

      // Act
      const result = await service.getTemplate(query);

      // Assert
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(query.id);
      expect(result).toBeNull();
    });
  });
});
