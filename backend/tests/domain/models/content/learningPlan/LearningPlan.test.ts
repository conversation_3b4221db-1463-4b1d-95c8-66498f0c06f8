import { LearningPlan } from '../../../../../domain/models/content/learningPlan/LearningPlan';
import { LearningPlanCreatedEvent } from '../../../../../domain/events/content/learningPlan/LearningPlanCreatedEvent';
import { LearningPlanUpdatedEvent } from '../../../../../domain/events/content/learningPlan/LearningPlanUpdatedEvent';
import { LearningPlanStartedEvent } from '../../../../../domain/events/content/learningPlan/LearningPlanStartedEvent';
import { LearningPlanCompletedEvent } from '../../../../../domain/events/content/learningPlan/LearningPlanCompletedEvent';
import { LearningPlanDeletedEvent } from '../../../../../domain/events/content/learningPlan/LearningPlanDeletedEvent';
import { LearningPlanRestoredEvent } from '../../../../../domain/events/content/learningPlan/LearningPlanRestoredEvent';

describe('LearningPlan', () => {
  describe('create', () => {
    it('should create a learning plan with the given properties', () => {
      const learningPlan = LearningPlan.create(
        'user1',
        '测试学习计划',
        '这是一个测试学习计划的描述',
        1, // templateId
        2, // themeId
        7, // targetDays
        3, // dailyGoalExercises
        5, // dailyGoalInsights
        15, // dailyGoalMinutes
        true // isPublic
      );

      expect(learningPlan.userId).toBe('user1');
      expect(learningPlan.title).toBe('测试学习计划');
      expect(learningPlan.description).toBe('这是一个测试学习计划的描述');
      expect(learningPlan.templateId).toBe(1);
      expect(learningPlan.themeId).toBe(2);
      expect(learningPlan.targetDays).toBe(7);
      expect(learningPlan.completedDays).toBe(0);
      expect(learningPlan.progress).toBe(0);
      expect(learningPlan.dailyGoalExercises).toBe(3);
      expect(learningPlan.dailyGoalInsights).toBe(5);
      expect(learningPlan.dailyGoalMinutes).toBe(15);
      expect(learningPlan.status).toBe('not_started');
      expect(learningPlan.startDate).toBeNull();
      expect(learningPlan.endDate).toBeNull();
      expect(learningPlan.isCurrent).toBe(false);
      expect(learningPlan.isSystemDefault).toBe(false);
      expect(learningPlan.isPublic).toBe(true);
      expect(learningPlan.isDeleted).toBe(false);

      // 验证领域事件
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanCreatedEvent);
      expect((learningPlan.domainEvents[0] as LearningPlanCreatedEvent).title).toBe('测试学习计划');
    });

    it('should throw an error if title is empty', () => {
      expect(() => LearningPlan.create('user1', '', '描述')).toThrow('学习计划标题不能为空');
    });

    it('should throw an error if title is too long', () => {
      const longTitle = 'a'.repeat(101);
      expect(() => LearningPlan.create('user1', longTitle, '描述')).toThrow('学习计划标题不能超过100个字符');
    });
  });

  describe('updateTitle', () => {
    it('should update the title and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateTitle('新标题');

      expect(learningPlan.title).toBe('新标题');
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.title?.from).toBe('测试学习计划');
      expect(event.changes.title?.to).toBe('新标题');
    });

    it('should not update if the title is the same', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateTitle('测试学习计划');

      expect(learningPlan.domainEvents).toHaveLength(0);
    });

    it('should throw an error if title is empty', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      expect(() => learningPlan.updateTitle('')).toThrow('学习计划标题不能为空');
    });

    it('should throw an error if title is too long', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      const longTitle = 'a'.repeat(101);
      expect(() => learningPlan.updateTitle(longTitle)).toThrow('学习计划标题不能超过100个字符');
    });
  });

  describe('updateDescription', () => {
    it('should update the description and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateDescription('新描述');

      expect(learningPlan.description).toBe('新描述');
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.description?.from).toBe('描述');
      expect(event.changes.description?.to).toBe('新描述');
    });

    it('should not update if the description is the same', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateDescription('描述');

      expect(learningPlan.domainEvents).toHaveLength(0);
    });
  });

  describe('start', () => {
    it('should start the learning plan and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.start();

      expect(learningPlan.status).toBe('in_progress');
      expect(learningPlan.startDate).not.toBeNull();
      expect(learningPlan.isStarted).toBe(true);
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanStartedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanStartedEvent;
      expect(event.title).toBe('测试学习计划');
    });

    it('should not start if the learning plan is already started', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.start();
      learningPlan.clearEvents(); // 清除事件

      learningPlan.start();

      expect(learningPlan.domainEvents).toHaveLength(0);
    });

    it('should throw an error if the learning plan is deleted', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.softDelete();

      expect(() => learningPlan.start()).toThrow('已删除的学习计划不能开始');
    });
  });

  describe('complete', () => {
    it('should complete the learning plan and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.complete();

      expect(learningPlan.status).toBe('completed');
      expect(learningPlan.endDate).not.toBeNull();
      expect(learningPlan.isCompleted).toBe(true);
      expect(learningPlan.completedDays).toBe(learningPlan.targetDays);
      expect(learningPlan.progress).toBe(100);
      expect(learningPlan.domainEvents).toHaveLength(2); // start + complete
      expect(learningPlan.domainEvents[1]).toBeInstanceOf(LearningPlanCompletedEvent);

      const event = learningPlan.domainEvents[1] as LearningPlanCompletedEvent;
      expect(event.title).toBe('测试学习计划');
    });

    it('should not complete if the learning plan is already completed', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.complete();
      learningPlan.clearEvents(); // 清除事件

      learningPlan.complete();

      expect(learningPlan.domainEvents).toHaveLength(0);
    });

    it('should throw an error if the learning plan is deleted', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.softDelete();

      expect(() => learningPlan.complete()).toThrow('已删除的学习计划不能完成');
    });
  });

  describe('softDelete', () => {
    it('should mark the learning plan as deleted and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.softDelete();

      expect(learningPlan.isDeleted).toBe(true);
      expect(learningPlan.deletedAt).not.toBeNull();
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanDeletedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanDeletedEvent;
      expect(event.title).toBe('测试学习计划');
    });

    it('should throw an error if the learning plan is already deleted', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.softDelete();

      expect(() => learningPlan.softDelete()).toThrow('学习计划已被删除');
    });
  });

  describe('restore', () => {
    it('should restore the learning plan and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.softDelete();
      learningPlan.clearEvents(); // 清除事件

      learningPlan.restore();

      expect(learningPlan.isDeleted).toBe(false);
      expect(learningPlan.deletedAt).toBeNull();
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanRestoredEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanRestoredEvent;
      expect(event.title).toBe('测试学习计划');
    });

    it('should throw an error if the learning plan is not deleted', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');

      expect(() => learningPlan.restore()).toThrow('学习计划未被删除');
    });
  });

  describe('setAsCurrent', () => {
    it('should set the learning plan as current and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.setAsCurrent();

      expect(learningPlan.isCurrent).toBe(true);
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.isCurrent?.from).toBe(false);
      expect(event.changes.isCurrent?.to).toBe(true);
    });

    it('should not update if the learning plan is already current', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.setAsCurrent();
      learningPlan.clearEvents(); // 清除事件

      learningPlan.setAsCurrent();

      expect(learningPlan.domainEvents).toHaveLength(0);
    });
  });

  describe('updateTargetDays', () => {
    it('should update the target days and recalculate progress', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.updateCompletedDays(2); // 设置已完成天数为2
      learningPlan.clearEvents(); // 清除事件

      learningPlan.updateTargetDays(10);

      expect(learningPlan.targetDays).toBe(10);
      // 进度应该是 2/10 = 20%
      expect(learningPlan.progress).toBe(20);
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.targetDays?.from).toBe(7);
      expect(event.changes.targetDays?.to).toBe(10);
    });

    it('should throw an error if target days is not positive', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');

      expect(() => learningPlan.updateTargetDays(0)).toThrow('目标完成天数必须大于0');
      expect(() => learningPlan.updateTargetDays(-1)).toThrow('目标完成天数必须大于0');
    });
  });

  describe('updateCompletedDays', () => {
    it('should update the completed days and recalculate progress', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateCompletedDays(3);

      expect(learningPlan.completedDays).toBe(3);
      // 进度应该是 3/7 ≈ 43%
      expect(learningPlan.progress).toBe(43);
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.completedDays?.from).toBe(0);
      expect(event.changes.completedDays?.to).toBe(3);
    });

    it('should cap completed days at target days', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');

      learningPlan.updateCompletedDays(10); // 超过目标天数7

      expect(learningPlan.completedDays).toBe(7);
      expect(learningPlan.progress).toBe(100);
    });

    it('should throw an error if completed days is negative', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');

      expect(() => learningPlan.updateCompletedDays(-1)).toThrow('已完成天数不能为负数');
    });
  });

  describe('updateDailyGoals', () => {
    it('should update the daily goals and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateDailyGoals(5, 8, 20);

      expect(learningPlan.dailyGoalExercises).toBe(5);
      expect(learningPlan.dailyGoalInsights).toBe(8);
      expect(learningPlan.dailyGoalMinutes).toBe(20);
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.dailyGoalExercises?.from).toBe(3);
      expect(event.changes.dailyGoalExercises?.to).toBe(5);
      expect(event.changes.dailyGoalInsights?.from).toBe(5);
      expect(event.changes.dailyGoalInsights?.to).toBe(8);
      expect(event.changes.dailyGoalMinutes?.from).toBe(15);
      expect(event.changes.dailyGoalMinutes?.to).toBe(20);
    });
  });

  describe('updateIsPublic', () => {
    it('should update the public status and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateIsPublic(true);

      expect(learningPlan.isPublic).toBe(true);
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.isPublic?.from).toBe(false);
      expect(event.changes.isPublic?.to).toBe(true);
    });

    it('should not update if the public status is the same', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述', null, null, 7, 3, 5, 15, true);
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.updateIsPublic(true);

      expect(learningPlan.domainEvents).toHaveLength(0);
    });
  });

  describe('tag management', () => {
    it('should add a tag and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.addTag('标签1');

      expect(learningPlan.tags).toContain('标签1');
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.tags).toBeDefined();
    });

    it('should not add a duplicate tag', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.addTag('标签1');
      learningPlan.clearEvents(); // 清除事件

      learningPlan.addTag('标签1');

      expect(learningPlan.domainEvents).toHaveLength(0);
    });

    it('should remove a tag and add an event', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.addTag('标签1');
      learningPlan.clearEvents(); // 清除事件

      learningPlan.removeTag('标签1');

      expect(learningPlan.tags).not.toContain('标签1');
      expect(learningPlan.domainEvents).toHaveLength(1);
      expect(learningPlan.domainEvents[0]).toBeInstanceOf(LearningPlanUpdatedEvent);

      const event = learningPlan.domainEvents[0] as LearningPlanUpdatedEvent;
      expect(event.changes.tags).toBeDefined();
    });

    it('should not remove a non-existent tag', () => {
      const learningPlan = LearningPlan.create('user1', '测试学习计划', '描述');
      learningPlan.clearEvents(); // 清除创建事件

      learningPlan.removeTag('不存在的标签');

      expect(learningPlan.domainEvents).toHaveLength(0);
    });
  });
});
