import { Note } from '../../../../../domain/models/content/note/Note';
import { ContentStatus } from '../../../../../domain/models/content/ContentStatus';
import { Visibility } from '../../../../../domain/models/content/Visibility';
import { NoteCreatedEvent } from '../../../../../domain/events/content/note/NoteCreatedEvent';
import { NoteUpdatedEvent } from '../../../../../domain/events/content/note/NoteUpdatedEvent';
import { NoteDeletedEvent } from '../../../../../domain/events/content/note/NoteDeletedEvent';
import { NoteRestoredEvent } from '../../../../../domain/events/content/note/NoteRestoredEvent';
import { NotePublishedEvent } from '../../../../../domain/events/content/note/NotePublishedEvent';

describe('Note', () => {
  describe('create', () => {
    it('should create a note with the given properties', () => {
      const note = Note.create(
        '测试笔记',
        '这是一个测试笔记的内容',
        'user1',
        'https://example.com/image.jpg',
        Visibility.PUBLIC,
        false,
        1
      );
      
      expect(note.title).toBe('测试笔记');
      expect(note.content).toBe('这是一个测试笔记的内容');
      expect(note.userId).toBe('user1');
      expect(note.imageUrl).toBe('https://example.com/image.jpg');
      expect(note.visibility).toBe(Visibility.PUBLIC);
      expect(note.isAiGenerated).toBe(false);
      expect(note.planId).toBe(1);
      expect(note.status).toBe(ContentStatus.DRAFT);
      expect(note.likeCount).toBe(0);
      expect(note.commentCount).toBe(0);
      expect(note.viewCount).toBe(0);
      expect(note.isDeleted).toBe(false);
      expect(note.isPublished).toBe(false);
      
      // 验证领域事件
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NoteCreatedEvent);
      expect((note.domainEvents[0] as NoteCreatedEvent).title).toBe('测试笔记');
    });
    
    it('should throw an error if title is empty', () => {
      expect(() => Note.create('', '内容', 'user1')).toThrow('笔记标题不能为空');
    });
    
    it('should throw an error if title is too long', () => {
      const longTitle = 'a'.repeat(101);
      expect(() => Note.create(longTitle, '内容', 'user1')).toThrow('笔记标题不能超过100个字符');
    });
    
    it('should throw an error if content is empty', () => {
      expect(() => Note.create('标题', '', 'user1')).toThrow('笔记内容不能为空');
    });
  });
  
  describe('updateTitle', () => {
    it('should update the title and add an event', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.updateTitle('新标题');
      
      expect(note.title).toBe('新标题');
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NoteUpdatedEvent);
      
      const event = note.domainEvents[0] as NoteUpdatedEvent;
      expect(event.changes.title?.from).toBe('测试笔记');
      expect(event.changes.title?.to).toBe('新标题');
    });
    
    it('should not update if the title is the same', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.updateTitle('测试笔记');
      
      expect(note.domainEvents).toHaveLength(0);
    });
    
    it('should throw an error if title is empty', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      expect(() => note.updateTitle('')).toThrow('笔记标题不能为空');
    });
    
    it('should throw an error if title is too long', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      const longTitle = 'a'.repeat(101);
      expect(() => note.updateTitle(longTitle)).toThrow('笔记标题不能超过100个字符');
    });
  });
  
  describe('updateContent', () => {
    it('should update the content and add an event', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.updateContent('新内容');
      
      expect(note.content).toBe('新内容');
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NoteUpdatedEvent);
      
      const event = note.domainEvents[0] as NoteUpdatedEvent;
      expect(event.changes.content).toBeDefined();
    });
    
    it('should not update if the content is the same', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.updateContent('这是一个测试笔记的内容');
      
      expect(note.domainEvents).toHaveLength(0);
    });
    
    it('should throw an error if content is empty', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      expect(() => note.updateContent('')).toThrow('笔记内容不能为空');
    });
  });
  
  describe('publish', () => {
    it('should publish the note and add an event', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.publish();
      
      expect(note.status).toBe(ContentStatus.PUBLISHED);
      expect(note.isPublished).toBe(true);
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NotePublishedEvent);
      
      const event = note.domainEvents[0] as NotePublishedEvent;
      expect(event.title).toBe('测试笔记');
    });
    
    it('should not publish if the note is already published', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.publish();
      note.clearEvents(); // 清除事件
      
      note.publish();
      
      expect(note.domainEvents).toHaveLength(0);
    });
    
    it('should throw an error if the note is deleted', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.softDelete();
      
      expect(() => note.publish()).toThrow('已删除的笔记不能发布');
    });
  });
  
  describe('softDelete', () => {
    it('should mark the note as deleted and add an event', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.softDelete();
      
      expect(note.status).toBe(ContentStatus.DELETED);
      expect(note.isDeleted).toBe(true);
      expect(note.deletedAt).not.toBeNull();
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NoteDeletedEvent);
      
      const event = note.domainEvents[0] as NoteDeletedEvent;
      expect(event.title).toBe('测试笔记');
    });
    
    it('should throw an error if the note is already deleted', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.softDelete();
      
      expect(() => note.softDelete()).toThrow('笔记已被删除');
    });
  });
  
  describe('restore', () => {
    it('should restore the note and add an event', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.softDelete();
      note.clearEvents(); // 清除事件
      
      note.restore();
      
      expect(note.status).toBe(ContentStatus.DRAFT);
      expect(note.isDeleted).toBe(false);
      expect(note.deletedAt).toBeNull();
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NoteRestoredEvent);
      
      const event = note.domainEvents[0] as NoteRestoredEvent;
      expect(event.title).toBe('测试笔记');
    });
    
    it('should throw an error if the note is not deleted', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      
      expect(() => note.restore()).toThrow('笔记未被删除');
    });
  });
  
  describe('tag management', () => {
    it('should add a tag and add an event', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.addTag('标签1');
      
      expect(note.tags).toContain('标签1');
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NoteUpdatedEvent);
      
      const event = note.domainEvents[0] as NoteUpdatedEvent;
      expect(event.changes.tags).toBeDefined();
    });
    
    it('should not add a duplicate tag', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.addTag('标签1');
      note.clearEvents(); // 清除事件
      
      note.addTag('标签1');
      
      expect(note.domainEvents).toHaveLength(0);
    });
    
    it('should remove a tag and add an event', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.addTag('标签1');
      note.clearEvents(); // 清除事件
      
      note.removeTag('标签1');
      
      expect(note.tags).not.toContain('标签1');
      expect(note.domainEvents).toHaveLength(1);
      expect(note.domainEvents[0]).toBeInstanceOf(NoteUpdatedEvent);
      
      const event = note.domainEvents[0] as NoteUpdatedEvent;
      expect(event.changes.tags).toBeDefined();
    });
    
    it('should not remove a non-existent tag', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.clearEvents(); // 清除创建事件
      
      note.removeTag('不存在的标签');
      
      expect(note.domainEvents).toHaveLength(0);
    });
  });
  
  describe('interaction counters', () => {
    it('should increment like count', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      
      note.incrementLikeCount();
      
      expect(note.likeCount).toBe(1);
    });
    
    it('should decrement like count', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.incrementLikeCount();
      
      note.decrementLikeCount();
      
      expect(note.likeCount).toBe(0);
    });
    
    it('should not decrement like count below zero', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      
      note.decrementLikeCount();
      
      expect(note.likeCount).toBe(0);
    });
    
    it('should increment comment count', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      
      note.incrementCommentCount();
      
      expect(note.commentCount).toBe(1);
    });
    
    it('should decrement comment count', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      note.incrementCommentCount();
      
      note.decrementCommentCount();
      
      expect(note.commentCount).toBe(0);
    });
    
    it('should not decrement comment count below zero', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      
      note.decrementCommentCount();
      
      expect(note.commentCount).toBe(0);
    });
    
    it('should increment view count', () => {
      const note = Note.create('测试笔记', '这是一个测试笔记的内容', 'user1');
      
      note.incrementViewCount();
      
      expect(note.viewCount).toBe(1);
    });
  });
});
