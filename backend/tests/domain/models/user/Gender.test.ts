import { Gender } from '../../../../domain/models/user/Gender';

describe('Gender', () => {
  describe('constants', () => {
    it('should have the correct values', () => {
      expect(Gender.MALE.value).toBe('MALE');
      expect(Gender.FEMALE.value).toBe('FEMALE');
      expect(Gender.UNKNOWN.value).toBe('UNKNOWN');
    });
  });

  describe('constructor', () => {
    it('should create a gender with the given value', () => {
      const gender = new Gender('MALE');
      
      expect(gender.value).toBe('MALE');
    });

    it('should throw an error if value is invalid', () => {
      expect(() => new Gender('INVALID')).toThrow('无效的性别值: INVALID');
    });
  });

  describe('equals', () => {
    it('should return true if genders have the same value', () => {
      const gender1 = new Gender('MALE');
      const gender2 = Gender.MALE;
      
      expect(gender1.equals(gender2)).toBe(true);
    });

    it('should return false if genders have different values', () => {
      const gender1 = Gender.MALE;
      const gender2 = Gender.FEMALE;
      
      expect(gender1.equals(gender2)).toBe(false);
    });
  });

  describe('fromString', () => {
    it('should create a gender from a valid string value', () => {
      const gender1 = Gender.fromString('MALE');
      const gender2 = Gender.fromString('FEMALE');
      const gender3 = Gender.fromString('UNKNOWN');
      
      expect(gender1.value).toBe('MALE');
      expect(gender2.value).toBe('FEMALE');
      expect(gender3.value).toBe('UNKNOWN');
    });

    it('should create a gender from a valid lowercase string value', () => {
      const gender1 = Gender.fromString('male');
      const gender2 = Gender.fromString('female');
      const gender3 = Gender.fromString('unknown');
      
      expect(gender1.value).toBe('MALE');
      expect(gender2.value).toBe('FEMALE');
      expect(gender3.value).toBe('UNKNOWN');
    });

    it('should return UNKNOWN for null or undefined', () => {
      const gender1 = Gender.fromString(null as any);
      const gender2 = Gender.fromString(undefined as any);
      
      expect(gender1.value).toBe('UNKNOWN');
      expect(gender2.value).toBe('UNKNOWN');
    });

    it('should throw an error for invalid string values', () => {
      expect(() => Gender.fromString('INVALID')).toThrow('无效的性别值: INVALID');
    });
  });

  describe('fromNumber', () => {
    it('should create a gender from a valid number value', () => {
      const gender1 = Gender.fromNumber(1);
      const gender2 = Gender.fromNumber(2);
      const gender3 = Gender.fromNumber(0);
      
      expect(gender1.value).toBe('MALE');
      expect(gender2.value).toBe('FEMALE');
      expect(gender3.value).toBe('UNKNOWN');
    });

    it('should return UNKNOWN for null or undefined', () => {
      const gender1 = Gender.fromNumber(null as any);
      const gender2 = Gender.fromNumber(undefined as any);
      
      expect(gender1.value).toBe('UNKNOWN');
      expect(gender2.value).toBe('UNKNOWN');
    });

    it('should return UNKNOWN for invalid number values', () => {
      const gender = Gender.fromNumber(999);
      
      expect(gender.value).toBe('UNKNOWN');
    });
  });

  describe('toNumber', () => {
    it('should convert gender to number', () => {
      expect(Gender.MALE.toNumber()).toBe(1);
      expect(Gender.FEMALE.toNumber()).toBe(2);
      expect(Gender.UNKNOWN.toNumber()).toBe(0);
    });
  });

  describe('toString', () => {
    it('should convert gender to string', () => {
      expect(Gender.MALE.toString()).toBe('MALE');
      expect(Gender.FEMALE.toString()).toBe('FEMALE');
      expect(Gender.UNKNOWN.toString()).toBe('UNKNOWN');
    });
  });

  describe('toLocalizedString', () => {
    it('should convert gender to localized string', () => {
      expect(Gender.MALE.toLocalizedString()).toBe('男');
      expect(Gender.FEMALE.toLocalizedString()).toBe('女');
      expect(Gender.UNKNOWN.toLocalizedString()).toBe('未知');
    });
  });
});
