import { NotificationChannel } from '../../../../domain/models/user/NotificationChannel';

describe('NotificationChannel', () => {
  describe('constants', () => {
    it('should have the correct values', () => {
      expect(NotificationChannel.APP.value).toBe('APP');
      expect(NotificationChannel.EMAIL.value).toBe('EMAIL');
      expect(NotificationChannel.SMS.value).toBe('SMS');
      expect(NotificationChannel.WECHAT.value).toBe('WECHAT');
    });
  });

  describe('constructor', () => {
    it('should create a notification channel with the given value', () => {
      const notificationChannel = new NotificationChannel('APP');
      
      expect(notificationChannel.value).toBe('APP');
    });

    it('should throw an error if value is invalid', () => {
      expect(() => new NotificationChannel('INVALID')).toThrow('无效的通知渠道值: INVALID');
    });
  });

  describe('equals', () => {
    it('should return true if notification channels have the same value', () => {
      const notificationChannel1 = new NotificationChannel('APP');
      const notificationChannel2 = NotificationChannel.APP;
      
      expect(notificationChannel1.equals(notificationChannel2)).toBe(true);
    });

    it('should return false if notification channels have different values', () => {
      const notificationChannel1 = NotificationChannel.APP;
      const notificationChannel2 = NotificationChannel.EMAIL;
      
      expect(notificationChannel1.equals(notificationChannel2)).toBe(false);
    });
  });

  describe('fromString', () => {
    it('should create a notification channel from a valid string value', () => {
      const notificationChannel1 = NotificationChannel.fromString('APP');
      const notificationChannel2 = NotificationChannel.fromString('EMAIL');
      const notificationChannel3 = NotificationChannel.fromString('SMS');
      const notificationChannel4 = NotificationChannel.fromString('WECHAT');
      
      expect(notificationChannel1.value).toBe('APP');
      expect(notificationChannel2.value).toBe('EMAIL');
      expect(notificationChannel3.value).toBe('SMS');
      expect(notificationChannel4.value).toBe('WECHAT');
    });

    it('should create a notification channel from a valid lowercase string value', () => {
      const notificationChannel1 = NotificationChannel.fromString('app');
      const notificationChannel2 = NotificationChannel.fromString('email');
      const notificationChannel3 = NotificationChannel.fromString('sms');
      const notificationChannel4 = NotificationChannel.fromString('wechat');
      
      expect(notificationChannel1.value).toBe('APP');
      expect(notificationChannel2.value).toBe('EMAIL');
      expect(notificationChannel3.value).toBe('SMS');
      expect(notificationChannel4.value).toBe('WECHAT');
    });

    it('should return APP for null or undefined', () => {
      const notificationChannel1 = NotificationChannel.fromString(null as any);
      const notificationChannel2 = NotificationChannel.fromString(undefined as any);
      
      expect(notificationChannel1.value).toBe('APP');
      expect(notificationChannel2.value).toBe('APP');
    });

    it('should throw an error for invalid string values', () => {
      expect(() => NotificationChannel.fromString('INVALID')).toThrow('无效的通知渠道值: INVALID');
    });
  });

  describe('toString', () => {
    it('should convert notification channel to string', () => {
      expect(NotificationChannel.APP.toString()).toBe('APP');
      expect(NotificationChannel.EMAIL.toString()).toBe('EMAIL');
      expect(NotificationChannel.SMS.toString()).toBe('SMS');
      expect(NotificationChannel.WECHAT.toString()).toBe('WECHAT');
    });
  });

  describe('toLocalizedString', () => {
    it('should convert notification channel to localized string', () => {
      expect(NotificationChannel.APP.toLocalizedString()).toBe('应用内');
      expect(NotificationChannel.EMAIL.toLocalizedString()).toBe('邮件');
      expect(NotificationChannel.SMS.toLocalizedString()).toBe('短信');
      expect(NotificationChannel.WECHAT.toLocalizedString()).toBe('微信');
    });
  });

  describe('getAll', () => {
    it('should return all notification channels', () => {
      const allChannels = NotificationChannel.getAll();
      
      expect(allChannels).toHaveLength(4);
      expect(allChannels).toContainEqual(NotificationChannel.APP);
      expect(allChannels).toContainEqual(NotificationChannel.EMAIL);
      expect(allChannels).toContainEqual(NotificationChannel.SMS);
      expect(allChannels).toContainEqual(NotificationChannel.WECHAT);
    });
  });
});
