import { Password } from '../../../../domain/models/user/Password';

describe('Password', () => {
  describe('create', () => {
    it('should create a password with hash and salt', () => {
      const password = Password.create('password123');
      
      expect(password.hash).toBeDefined();
      expect(password.hash.length).toBeGreaterThan(0);
      expect(password.salt).toBeDefined();
      expect(password.salt.length).toBeGreaterThan(0);
    });

    it('should throw an error if password is empty', () => {
      expect(() => Password.create('')).toThrow('密码不能为空');
    });

    it('should throw an error if password is too short', () => {
      expect(() => Password.create('pass')).toThrow('密码长度不能小于6个字符');
    });
  });

  describe('constructor', () => {
    it('should create a password with the given hash and salt', () => {
      const hash = 'hashed_password';
      const salt = 'salt_value';
      
      const password = new Password(hash, salt);
      
      expect(password.hash).toBe(hash);
      expect(password.salt).toBe(salt);
    });
  });

  describe('verify', () => {
    it('should return true for correct password', () => {
      const plainPassword = 'password123';
      const password = Password.create(plainPassword);
      
      expect(password.verify(plainPassword)).toBe(true);
    });

    it('should return false for incorrect password', () => {
      const password = Password.create('password123');
      
      expect(password.verify('wrong_password')).toBe(false);
    });

    it('should return false for empty password', () => {
      const password = Password.create('password123');
      
      expect(password.verify('')).toBe(false);
    });
  });

  describe('isStrong', () => {
    it('should return true for strong passwords', () => {
      expect(Password.isStrong('Password123!')).toBe(true);
      expect(Password.isStrong('Str0ng_P@ssw0rd')).toBe(true);
      expect(Password.isStrong('C0mpl3x!P@ss')).toBe(true);
    });

    it('should return false for weak passwords', () => {
      expect(Password.isStrong('')).toBe(false);
      expect(Password.isStrong('pass')).toBe(false);
      expect(Password.isStrong('password')).toBe(false);
      expect(Password.isStrong('12345678')).toBe(false);
      expect(Password.isStrong('PASSWORD')).toBe(false);
    });
  });

  describe('hashPassword', () => {
    it('should hash the password with the given salt', () => {
      const plainPassword = 'password123';
      const salt = 'test_salt';
      
      const hash = Password.hashPassword(plainPassword, salt);
      
      expect(hash).toBeDefined();
      expect(hash.length).toBeGreaterThan(0);
      
      // 相同的密码和盐应该产生相同的哈希
      const hash2 = Password.hashPassword(plainPassword, salt);
      expect(hash).toBe(hash2);
    });

    it('should produce different hashes for different salts', () => {
      const plainPassword = 'password123';
      
      const hash1 = Password.hashPassword(plainPassword, 'salt1');
      const hash2 = Password.hashPassword(plainPassword, 'salt2');
      
      expect(hash1).not.toBe(hash2);
    });

    it('should produce different hashes for different passwords', () => {
      const salt = 'test_salt';
      
      const hash1 = Password.hashPassword('password1', salt);
      const hash2 = Password.hashPassword('password2', salt);
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('generateSalt', () => {
    it('should generate a random salt', () => {
      const salt1 = Password.generateSalt();
      const salt2 = Password.generateSalt();
      
      expect(salt1).toBeDefined();
      expect(salt1.length).toBeGreaterThan(0);
      expect(salt2).toBeDefined();
      expect(salt2.length).toBeGreaterThan(0);
      expect(salt1).not.toBe(salt2);
    });
  });
});
