import { Role } from '../../../../domain/models/user/Role';
import { Permission } from '../../../../domain/models/user/Permission';
import { RoleCreatedEvent } from '../../../../domain/events/user/RoleCreatedEvent';
import { RoleUpdatedEvent } from '../../../../domain/events/user/RoleUpdatedEvent';
import { RoleDeletedEvent } from '../../../../domain/events/user/RoleDeletedEvent';
import { PermissionAddedToRoleEvent } from '../../../../domain/events/user/PermissionAddedToRoleEvent';
import { PermissionRemovedFromRoleEvent } from '../../../../domain/events/user/PermissionRemovedFromRoleEvent';

describe('Role', () => {
  // 测试数据
  const roleId = 1;
  const roleName = 'admin';
  const roleDescription = 'Administrator';
  
  // 创建测试对象
  const createTestRole = () => {
    const permission1 = new Permission(1, 'create_user', 'Create User', 'user', 'create');
    const permission2 = new Permission(2, 'update_user', 'Update User', 'user', 'update');
    
    return new Role(
      roleId,
      roleName,
      roleDescription,
      [permission1, permission2]
    );
  };

  describe('constructor', () => {
    it('should create a role with the given properties', () => {
      // 创建角色
      const role = createTestRole();

      // 验证属性
      expect(role.id).toBe(roleId);
      expect(role.name).toBe(roleName);
      expect(role.description).toBe(roleDescription);
      expect(role.permissions).toHaveLength(2);
      expect(role.permissions[0].id).toBe(1);
      expect(role.permissions[0].name).toBe('create_user');
      expect(role.permissions[1].id).toBe(2);
      expect(role.permissions[1].name).toBe('update_user');

      // 验证领域事件
      expect(role.domainEvents).toHaveLength(0);
    });
  });

  describe('create', () => {
    it('should create a role and add a created event', () => {
      // 创建角色
      const role = Role.create(roleName, roleDescription);

      // 验证属性
      expect(role.id).toBe(0);
      expect(role.name).toBe(roleName);
      expect(role.description).toBe(roleDescription);
      expect(role.permissions).toHaveLength(0);

      // 验证领域事件
      expect(role.domainEvents).toHaveLength(1);
      expect(role.domainEvents[0]).toBeInstanceOf(RoleCreatedEvent);
      
      const event = role.domainEvents[0] as RoleCreatedEvent;
      expect(event.name).toBe(roleName);
    });

    it('should throw an error if name is empty', () => {
      expect(() => Role.create('', roleDescription)).toThrow('角色名称不能为空');
    });
  });

  describe('updateName', () => {
    it('should update the name and add an event', () => {
      // 创建角色
      const role = createTestRole();
      const newName = 'super_admin';

      // 更新名称
      role.updateName(newName);

      // 验证属性
      expect(role.name).toBe(newName);

      // 验证领域事件
      expect(role.domainEvents).toHaveLength(1);
      expect(role.domainEvents[0]).toBeInstanceOf(RoleUpdatedEvent);
      
      const event = role.domainEvents[0] as RoleUpdatedEvent;
      expect(event.roleId).toBe(roleId);
      expect(event.changes.name?.from).toBe(roleName);
      expect(event.changes.name?.to).toBe(newName);
    });

    it('should not update if the name is the same', () => {
      // 创建角色
      const role = createTestRole();

      // 更新名称为相同值
      role.updateName(roleName);

      // 验证没有领域事件
      expect(role.domainEvents).toHaveLength(0);
    });

    it('should throw an error if name is empty', () => {
      // 创建角色
      const role = createTestRole();

      // 验证抛出异常
      expect(() => role.updateName('')).toThrow('角色名称不能为空');
    });
  });

  describe('updateDescription', () => {
    it('should update the description and add an event', () => {
      // 创建角色
      const role = createTestRole();
      const newDescription = 'Super Administrator';

      // 更新描述
      role.updateDescription(newDescription);

      // 验证属性
      expect(role.description).toBe(newDescription);

      // 验证领域事件
      expect(role.domainEvents).toHaveLength(1);
      expect(role.domainEvents[0]).toBeInstanceOf(RoleUpdatedEvent);
      
      const event = role.domainEvents[0] as RoleUpdatedEvent;
      expect(event.roleId).toBe(roleId);
      expect(event.changes.description?.from).toBe(roleDescription);
      expect(event.changes.description?.to).toBe(newDescription);
    });

    it('should not update if the description is the same', () => {
      // 创建角色
      const role = createTestRole();

      // 更新描述为相同值
      role.updateDescription(roleDescription);

      // 验证没有领域事件
      expect(role.domainEvents).toHaveLength(0);
    });
  });

  describe('addPermission', () => {
    it('should add a permission and add an event', () => {
      // 创建角色
      const role = createTestRole();
      const newPermission = new Permission(3, 'delete_user', 'Delete User', 'user', 'delete');

      // 添加权限
      role.addPermission(newPermission);

      // 验证属性
      expect(role.permissions).toHaveLength(3);
      expect(role.permissions[2].id).toBe(3);
      expect(role.permissions[2].name).toBe('delete_user');

      // 验证领域事件
      expect(role.domainEvents).toHaveLength(1);
      expect(role.domainEvents[0]).toBeInstanceOf(PermissionAddedToRoleEvent);
      
      const event = role.domainEvents[0] as PermissionAddedToRoleEvent;
      expect(event.roleId).toBe(roleId);
      expect(event.permissionId).toBe(3);
      expect(event.permissionName).toBe('delete_user');
    });

    it('should not add if the permission already exists', () => {
      // 创建角色
      const role = createTestRole();
      const existingPermission = new Permission(1, 'create_user', 'Create User', 'user', 'create');

      // 添加已存在的权限
      role.addPermission(existingPermission);

      // 验证权限数量不变
      expect(role.permissions).toHaveLength(2);

      // 验证没有领域事件
      expect(role.domainEvents).toHaveLength(0);
    });
  });

  describe('removePermission', () => {
    it('should remove a permission and add an event', () => {
      // 创建角色
      const role = createTestRole();

      // 移除权限
      role.removePermission(1);

      // 验证属性
      expect(role.permissions).toHaveLength(1);
      expect(role.permissions[0].id).toBe(2);
      expect(role.permissions[0].name).toBe('update_user');

      // 验证领域事件
      expect(role.domainEvents).toHaveLength(1);
      expect(role.domainEvents[0]).toBeInstanceOf(PermissionRemovedFromRoleEvent);
      
      const event = role.domainEvents[0] as PermissionRemovedFromRoleEvent;
      expect(event.roleId).toBe(roleId);
      expect(event.permissionId).toBe(1);
    });

    it('should not remove if the permission does not exist', () => {
      // 创建角色
      const role = createTestRole();

      // 移除不存在的权限
      role.removePermission(999);

      // 验证权限数量不变
      expect(role.permissions).toHaveLength(2);

      // 验证没有领域事件
      expect(role.domainEvents).toHaveLength(0);
    });
  });

  describe('hasPermission', () => {
    it('should return true if the role has the permission', () => {
      // 创建角色
      const role = createTestRole();

      // 验证权限
      expect(role.hasPermission(1)).toBe(true);
      expect(role.hasPermission(2)).toBe(true);
    });

    it('should return false if the role does not have the permission', () => {
      // 创建角色
      const role = createTestRole();

      // 验证权限
      expect(role.hasPermission(3)).toBe(false);
      expect(role.hasPermission(999)).toBe(false);
    });
  });

  describe('hasPermissionByName', () => {
    it('should return true if the role has the permission', () => {
      // 创建角色
      const role = createTestRole();

      // 验证权限
      expect(role.hasPermissionByName('create_user')).toBe(true);
      expect(role.hasPermissionByName('update_user')).toBe(true);
    });

    it('should return false if the role does not have the permission', () => {
      // 创建角色
      const role = createTestRole();

      // 验证权限
      expect(role.hasPermissionByName('delete_user')).toBe(false);
      expect(role.hasPermissionByName('unknown_permission')).toBe(false);
    });
  });
});
