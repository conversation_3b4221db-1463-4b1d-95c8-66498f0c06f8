import { User } from '../../../../domain/models/user/User';
import { Email } from '../../../../domain/models/user/Email';
import { PhoneNumber } from '../../../../domain/models/user/PhoneNumber';
import { Password } from '../../../../domain/models/user/Password';
import { Gender } from '../../../../domain/models/user/Gender';
import { UserStatus } from '../../../../domain/models/user/UserStatus';
import { Role } from '../../../../domain/models/user/Role';
import { UserSetting } from '../../../../domain/models/user/UserSetting';
import { UserCreatedEvent } from '../../../../domain/events/user/UserCreatedEvent';
import { UserUpdatedEvent } from '../../../../domain/events/user/UserUpdatedEvent';
import { UserDeletedEvent } from '../../../../domain/events/user/UserDeletedEvent';
import { UserProfileUpdatedEvent } from '../../../../domain/events/user/UserProfileUpdatedEvent';
import { UserPasswordUpdatedEvent } from '../../../../domain/events/user/UserPasswordUpdatedEvent';
import { UserRoleAddedEvent } from '../../../../domain/events/user/UserRoleAddedEvent';
import { UserRoleRemovedEvent } from '../../../../domain/events/user/UserRoleRemovedEvent';

describe('User', () => {
  // 测试数据
  const userId = 1;
  const username = 'testuser';
  const nickname = 'Test User';
  const emailValue = '<EMAIL>';
  const phoneNumberValue = '13800138000';
  const passwordValue = 'password123';
  const wechatOpenId = 'wx_123456';
  const avatar = 'https://example.com/avatar.jpg';
  const birthday = new Date('1990-01-01');
  const registeredAt = new Date('2023-01-01');
  const lastLoginAt = new Date('2023-01-02');

  // 创建测试对象
  const createTestUser = () => {
    const email = new Email(emailValue, true);
    const phoneNumber = new PhoneNumber(phoneNumberValue, true);
    const password = Password.create(passwordValue);
    const gender = Gender.MALE;
    const status = UserStatus.ACTIVE;
    const roles = [new Role(1, 'admin', 'Administrator', [])];
    const userSetting = UserSetting.createDefault(0, userId);

    return new User(
      userId,
      username,
      nickname,
      email,
      phoneNumber,
      password,
      wechatOpenId,
      avatar,
      gender,
      birthday,
      status,
      roles,
      registeredAt,
      lastLoginAt,
      userSetting
    );
  };

  describe('constructor', () => {
    it('should create a user with the given properties', () => {
      // 创建用户
      const user = createTestUser();

      // 验证属性
      expect(user.id).toBe(userId);
      expect(user.username).toBe(username);
      expect(user.nickname).toBe(nickname);
      expect(user.email?.value).toBe(emailValue);
      expect(user.email?.isVerified).toBe(true);
      expect(user.phoneNumber?.value).toBe(phoneNumberValue);
      expect(user.phoneNumber?.isVerified).toBe(true);
      expect(user.password?.verify(passwordValue)).toBe(true);
      expect(user.wechatOpenId).toBe(wechatOpenId);
      expect(user.avatar).toBe(avatar);
      expect(user.gender.value).toBe(Gender.MALE.value);
      expect(user.birthday).toEqual(birthday);
      expect(user.status.value).toBe(UserStatus.ACTIVE.value);
      expect(user.roles).toHaveLength(1);
      expect(user.roles[0].id).toBe(1);
      expect(user.roles[0].name).toBe('admin');
      expect(user.registeredAt).toEqual(registeredAt);
      expect(user.lastLoginAt).toEqual(lastLoginAt);
      expect(user.userSetting).toBeDefined();
      expect(user.isDeleted).toBe(false);
      expect(user.deletedAt).toBeNull();

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updateUsername', () => {
    it('should update the username and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newUsername = 'newusername';

      // 更新用户名
      user.updateUsername(newUsername);

      // 验证属性
      expect(user.username).toBe(newUsername);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserUpdatedEvent);

      const event = user.domainEvents[0] as UserUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.username?.from).toBe(username);
      expect(event.changes.username?.to).toBe(newUsername);
    });

    it('should not update if the username is the same', () => {
      // 创建用户
      const user = createTestUser();

      // 更新用户名为相同值
      user.updateUsername(username);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });

    it('should throw an error if the username is empty', () => {
      // 创建用户
      const user = createTestUser();

      // 验证抛出异常
      expect(() => user.updateUsername('')).toThrow('用户名不能为空');
    });
  });

  describe('updateNickname', () => {
    it('should update the nickname and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newNickname = 'New Test User';

      // 更新昵称
      user.updateNickname(newNickname);

      // 验证属性
      expect(user.nickname).toBe(newNickname);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserProfileUpdatedEvent);

      const event = user.domainEvents[0] as UserProfileUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.nickname?.from).toBe(nickname);
      expect(event.changes.nickname?.to).toBe(newNickname);
    });

    it('should not update if the nickname is the same', () => {
      // 创建用户
      const user = createTestUser();

      // 更新昵称为相同值
      user.updateNickname(nickname);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updateEmail', () => {
    it('should update the email and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newEmailValue = '<EMAIL>';
      const newEmail = new Email(newEmailValue, false);

      // 更新邮箱
      user.updateEmail(newEmail);

      // 验证属性
      expect(user.email?.value).toBe(newEmailValue);
      expect(user.email?.isVerified).toBe(false);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserUpdatedEvent);

      const event = user.domainEvents[0] as UserUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.email?.from).toBe(emailValue);
      expect(event.changes.email?.to).toBe(newEmailValue);
    });

    it('should not update if the email is the same', () => {
      // 创建用户
      const user = createTestUser();
      const sameEmail = new Email(emailValue, true);

      // 更新邮箱为相同值
      user.updateEmail(sameEmail);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updatePhoneNumber', () => {
    it('should update the phone number and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newPhoneNumberValue = '13900139000';
      const newPhoneNumber = new PhoneNumber(newPhoneNumberValue, false);

      // 更新手机号
      user.updatePhoneNumber(newPhoneNumber);

      // 验证属性
      expect(user.phoneNumber?.value).toBe(newPhoneNumberValue);
      expect(user.phoneNumber?.isVerified).toBe(false);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserUpdatedEvent);

      const event = user.domainEvents[0] as UserUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.phoneNumber?.from).toBe(phoneNumberValue);
      expect(event.changes.phoneNumber?.to).toBe(newPhoneNumberValue);
    });

    it('should not update if the phone number is the same', () => {
      // 创建用户
      const user = createTestUser();
      const samePhoneNumber = new PhoneNumber(phoneNumberValue, true);

      // 更新手机号为相同值
      user.updatePhoneNumber(samePhoneNumber);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updatePassword', () => {
    it('should update the password and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newPasswordValue = 'newpassword123';
      const newPassword = Password.create(newPasswordValue);

      // 更新密码
      user.updatePassword(newPassword);

      // 验证属性
      expect(user.password?.verify(newPasswordValue)).toBe(true);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserPasswordUpdatedEvent);

      const event = user.domainEvents[0] as UserPasswordUpdatedEvent;
      expect(event.userId).toBe(userId);
    });
  });

  describe('updateWechatOpenId', () => {
    it('should update the wechat open id and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newWechatOpenId = 'wx_654321';

      // 更新微信OpenID
      user.updateWechatOpenId(newWechatOpenId);

      // 验证属性
      expect(user.wechatOpenId).toBe(newWechatOpenId);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserUpdatedEvent);

      const event = user.domainEvents[0] as UserUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.wechatOpenId?.from).toBe(wechatOpenId);
      expect(event.changes.wechatOpenId?.to).toBe(newWechatOpenId);
    });

    it('should not update if the wechat open id is the same', () => {
      // 创建用户
      const user = createTestUser();

      // 更新微信OpenID为相同值
      user.updateWechatOpenId(wechatOpenId);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updateAvatar', () => {
    it('should update the avatar and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newAvatar = 'https://example.com/new-avatar.jpg';

      // 更新头像
      user.updateAvatar(newAvatar);

      // 验证属性
      expect(user.avatar).toBe(newAvatar);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserProfileUpdatedEvent);

      const event = user.domainEvents[0] as UserProfileUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.avatar?.from).toBe(avatar);
      expect(event.changes.avatar?.to).toBe(newAvatar);
    });

    it('should not update if the avatar is the same', () => {
      // 创建用户
      const user = createTestUser();

      // 更新头像为相同值
      user.updateAvatar(avatar);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updateGender', () => {
    it('should update the gender and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newGender = Gender.FEMALE;

      // 更新性别
      user.updateGender(newGender);

      // 验证属性
      expect(user.gender.value).toBe(Gender.FEMALE.value);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserProfileUpdatedEvent);

      const event = user.domainEvents[0] as UserProfileUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.gender?.from).toBe(Gender.MALE.value);
      expect(event.changes.gender?.to).toBe(Gender.FEMALE.value);
    });

    it('should not update if the gender is the same', () => {
      // 创建用户
      const user = createTestUser();

      // 更新性别为相同值
      user.updateGender(Gender.MALE);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updateBirthday', () => {
    it('should update the birthday and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newBirthday = new Date('1995-05-05');

      // 更新生日
      user.updateBirthday(newBirthday);

      // 验证属性
      expect(user.birthday).toEqual(newBirthday);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserProfileUpdatedEvent);

      const event = user.domainEvents[0] as UserProfileUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.birthday?.from).toEqual(birthday);
      expect(event.changes.birthday?.to).toEqual(newBirthday);
    });

    it('should not update if the birthday is the same', () => {
      // 创建用户
      const user = createTestUser();

      // 更新生日为相同值
      user.updateBirthday(new Date(birthday.getTime()));

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updateStatus', () => {
    it('should update the status and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newStatus = UserStatus.INACTIVE;

      // 更新状态
      user.updateStatus(newStatus);

      // 验证属性
      expect(user.status.value).toBe(UserStatus.INACTIVE.value);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserUpdatedEvent);

      const event = user.domainEvents[0] as UserUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.status?.from).toBe(UserStatus.ACTIVE.value);
      expect(event.changes.status?.to).toBe(UserStatus.INACTIVE.value);
    });

    it('should not update if the status is the same', () => {
      // 创建用户
      const user = createTestUser();

      // 更新状态为相同值
      user.updateStatus(UserStatus.ACTIVE);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('updateLastLoginTime', () => {
    it('should update the last login time and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newLastLoginAt = new Date('2023-02-01');

      // 更新最后登录时间
      user.updateLastLoginTime(newLastLoginAt);

      // 验证属性
      expect(user.lastLoginAt).toEqual(newLastLoginAt);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserUpdatedEvent);

      const event = user.domainEvents[0] as UserUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.lastLoginAt?.from).toEqual(lastLoginAt);
      expect(event.changes.lastLoginAt?.to).toEqual(newLastLoginAt);
    });
  });

  describe('addRole', () => {
    it('should add a role and add an event', () => {
      // 创建用户
      const user = createTestUser();
      const newRole = new Role(2, 'editor', 'Editor', []);

      // 添加角色
      user.addRole(newRole);

      // 验证属性
      expect(user.roles).toHaveLength(2);
      expect(user.roles[1].id).toBe(2);
      expect(user.roles[1].name).toBe('editor');

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserRoleAddedEvent);

      const event = user.domainEvents[0] as UserRoleAddedEvent;
      expect(event.userId).toBe(userId);
      expect(event.roleId).toBe(2);
      expect(event.roleName).toBe('editor');
    });

    it('should not add if the role already exists', () => {
      // 创建用户
      const user = createTestUser();
      const existingRole = new Role(1, 'admin', 'Administrator', []);

      // 添加已存在的角色
      user.addRole(existingRole);

      // 验证角色数量不变
      expect(user.roles).toHaveLength(1);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('removeRole', () => {
    it('should remove a role and add an event', () => {
      // 创建用户
      const user = createTestUser();

      // 移除角色
      user.removeRole(1);

      // 验证属性
      expect(user.roles).toHaveLength(0);

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserRoleRemovedEvent);

      const event = user.domainEvents[0] as UserRoleRemovedEvent;
      expect(event.userId).toBe(userId);
      expect(event.roleId).toBe(1);
    });

    it('should not remove if the role does not exist', () => {
      // 创建用户
      const user = createTestUser();

      // 移除不存在的角色
      user.removeRole(999);

      // 验证角色数量不变
      expect(user.roles).toHaveLength(1);

      // 验证没有领域事件
      expect(user.domainEvents).toHaveLength(0);
    });
  });

  describe('softDelete', () => {
    it('should mark the user as deleted and add an event', () => {
      // 创建用户
      const user = createTestUser();

      // 软删除用户
      user.softDelete();

      // 验证属性
      expect(user.isDeleted).toBe(true);
      expect(user.deletedAt).not.toBeNull();

      // 验证领域事件
      expect(user.domainEvents).toHaveLength(1);
      expect(user.domainEvents[0]).toBeInstanceOf(UserDeletedEvent);

      const event = user.domainEvents[0] as UserDeletedEvent;
      expect(event.userId).toBe(userId);
    });

    it('should throw an error if the user is already deleted', () => {
      // 创建用户
      const user = createTestUser();

      // 软删除用户
      user.softDelete();

      // 验证再次软删除会抛出异常
      expect(() => user.softDelete()).toThrow('用户已被删除');
    });
  });
});
