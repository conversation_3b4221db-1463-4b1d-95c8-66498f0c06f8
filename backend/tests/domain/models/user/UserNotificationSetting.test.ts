import { UserNotificationSetting } from '../../../../domain/models/user/UserNotificationSetting';
import { NotificationType } from '../../../../domain/models/user/NotificationType';
import { NotificationChannel } from '../../../../domain/models/user/NotificationChannel';

describe('UserNotificationSetting', () => {
  // 测试数据
  const settingId = 1;
  const notificationType = NotificationType.DAILY_REMINDER;
  const notificationChannel = NotificationChannel.APP;
  const enabled = true;
  
  // 创建测试对象
  const createTestNotificationSetting = () => {
    return new UserNotificationSetting(
      settingId,
      notificationType,
      notificationChannel,
      enabled
    );
  };

  describe('constructor', () => {
    it('should create a user notification setting with the given properties', () => {
      // 创建通知设置
      const notificationSetting = createTestNotificationSetting();

      // 验证属性
      expect(notificationSetting.id).toBe(settingId);
      expect(notificationSetting.notificationType).toBe(notificationType);
      expect(notificationSetting.notificationChannel).toBe(notificationChannel);
      expect(notificationSetting.enabled).toBe(enabled);
    });
  });

  describe('create', () => {
    it('should create a user notification setting with default id', () => {
      // 创建通知设置
      const notificationSetting = UserNotificationSetting.create(
        notificationType,
        notificationChannel,
        enabled
      );

      // 验证属性
      expect(notificationSetting.id).toBe(0);
      expect(notificationSetting.notificationType).toBe(notificationType);
      expect(notificationSetting.notificationChannel).toBe(notificationChannel);
      expect(notificationSetting.enabled).toBe(enabled);
    });
  });

  describe('enable', () => {
    it('should enable the notification setting', () => {
      // 创建禁用的通知设置
      const notificationSetting = new UserNotificationSetting(
        settingId,
        notificationType,
        notificationChannel,
        false
      );

      // 启用通知设置
      const enabledSetting = notificationSetting.enable();

      // 验证属性
      expect(enabledSetting.id).toBe(settingId);
      expect(enabledSetting.notificationType).toBe(notificationType);
      expect(enabledSetting.notificationChannel).toBe(notificationChannel);
      expect(enabledSetting.enabled).toBe(true);
    });

    it('should return a new instance', () => {
      // 创建禁用的通知设置
      const notificationSetting = new UserNotificationSetting(
        settingId,
        notificationType,
        notificationChannel,
        false
      );

      // 启用通知设置
      const enabledSetting = notificationSetting.enable();

      // 验证是新实例
      expect(enabledSetting).not.toBe(notificationSetting);
    });

    it('should not change the original instance', () => {
      // 创建禁用的通知设置
      const notificationSetting = new UserNotificationSetting(
        settingId,
        notificationType,
        notificationChannel,
        false
      );

      // 启用通知设置
      notificationSetting.enable();

      // 验证原实例未变
      expect(notificationSetting.enabled).toBe(false);
    });
  });

  describe('disable', () => {
    it('should disable the notification setting', () => {
      // 创建启用的通知设置
      const notificationSetting = createTestNotificationSetting();

      // 禁用通知设置
      const disabledSetting = notificationSetting.disable();

      // 验证属性
      expect(disabledSetting.id).toBe(settingId);
      expect(disabledSetting.notificationType).toBe(notificationType);
      expect(disabledSetting.notificationChannel).toBe(notificationChannel);
      expect(disabledSetting.enabled).toBe(false);
    });

    it('should return a new instance', () => {
      // 创建启用的通知设置
      const notificationSetting = createTestNotificationSetting();

      // 禁用通知设置
      const disabledSetting = notificationSetting.disable();

      // 验证是新实例
      expect(disabledSetting).not.toBe(notificationSetting);
    });

    it('should not change the original instance', () => {
      // 创建启用的通知设置
      const notificationSetting = createTestNotificationSetting();

      // 禁用通知设置
      notificationSetting.disable();

      // 验证原实例未变
      expect(notificationSetting.enabled).toBe(true);
    });
  });

  describe('toggle', () => {
    it('should toggle the notification setting from enabled to disabled', () => {
      // 创建启用的通知设置
      const notificationSetting = createTestNotificationSetting();

      // 切换通知设置
      const toggledSetting = notificationSetting.toggle();

      // 验证属性
      expect(toggledSetting.id).toBe(settingId);
      expect(toggledSetting.notificationType).toBe(notificationType);
      expect(toggledSetting.notificationChannel).toBe(notificationChannel);
      expect(toggledSetting.enabled).toBe(false);
    });

    it('should toggle the notification setting from disabled to enabled', () => {
      // 创建禁用的通知设置
      const notificationSetting = new UserNotificationSetting(
        settingId,
        notificationType,
        notificationChannel,
        false
      );

      // 切换通知设置
      const toggledSetting = notificationSetting.toggle();

      // 验证属性
      expect(toggledSetting.id).toBe(settingId);
      expect(toggledSetting.notificationType).toBe(notificationType);
      expect(toggledSetting.notificationChannel).toBe(notificationChannel);
      expect(toggledSetting.enabled).toBe(true);
    });

    it('should return a new instance', () => {
      // 创建通知设置
      const notificationSetting = createTestNotificationSetting();

      // 切换通知设置
      const toggledSetting = notificationSetting.toggle();

      // 验证是新实例
      expect(toggledSetting).not.toBe(notificationSetting);
    });

    it('should not change the original instance', () => {
      // 创建通知设置
      const notificationSetting = createTestNotificationSetting();

      // 切换通知设置
      notificationSetting.toggle();

      // 验证原实例未变
      expect(notificationSetting.enabled).toBe(true);
    });
  });
});
