import { UserSetting } from '../../../../domain/models/user/UserSetting';
import { UserNotificationSetting } from '../../../../domain/models/user/UserNotificationSetting';
import { NotificationType } from '../../../../domain/models/user/NotificationType';
import { NotificationChannel } from '../../../../domain/models/user/NotificationChannel';
import { PrivacySettings } from '../../../../domain/models/user/PrivacySettings';
import { UserSettingUpdatedEvent } from '../../../../domain/events/user/UserSettingUpdatedEvent';

describe('UserSetting', () => {
  // 测试数据
  const settingId = 1;
  const userId = 1;
  const theme = 'light';
  const fontSize = 'medium';
  const dailyGoalMinutes = 30;
  const reminderTime = '08:00';
  
  // 创建测试对象
  const createTestUserSetting = () => {
    const privacySettings = new PrivacySettings(true, true, false);
    const notificationSettings = [
      new UserNotificationSetting(1, NotificationType.DAILY_REMINDER, NotificationChannel.APP, true),
      new UserNotificationSetting(2, NotificationType.WEEKLY_SUMMARY, NotificationChannel.EMAIL, true)
    ];
    
    return new UserSetting(
      settingId,
      userId,
      theme,
      fontSize,
      dailyGoalMinutes,
      reminderTime,
      privacySettings,
      notificationSettings
    );
  };

  describe('constructor', () => {
    it('should create a user setting with the given properties', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();

      // 验证属性
      expect(userSetting.id).toBe(settingId);
      expect(userSetting.userId).toBe(userId);
      expect(userSetting.theme).toBe(theme);
      expect(userSetting.fontSize).toBe(fontSize);
      expect(userSetting.dailyGoalMinutes).toBe(dailyGoalMinutes);
      expect(userSetting.reminderTime).toBe(reminderTime);
      expect(userSetting.privacySettings).toBeDefined();
      expect(userSetting.privacySettings.showLearningProgress).toBe(true);
      expect(userSetting.privacySettings.showLearningHistory).toBe(true);
      expect(userSetting.privacySettings.showFollowers).toBe(false);
      expect(userSetting.notificationSettings).toHaveLength(2);
      expect(userSetting.notificationSettings[0].id).toBe(1);
      expect(userSetting.notificationSettings[0].notificationType.value).toBe(NotificationType.DAILY_REMINDER.value);
      expect(userSetting.notificationSettings[1].id).toBe(2);
      expect(userSetting.notificationSettings[1].notificationChannel.value).toBe(NotificationChannel.EMAIL.value);

      // 验证领域事件
      expect(userSetting.domainEvents).toHaveLength(0);
    });
  });

  describe('createDefault', () => {
    it('should create a default user setting', () => {
      // 创建默认用户设置
      const userSetting = UserSetting.createDefault(0, userId);

      // 验证属性
      expect(userSetting.id).toBe(0);
      expect(userSetting.userId).toBe(userId);
      expect(userSetting.theme).toBe('light');
      expect(userSetting.fontSize).toBe('medium');
      expect(userSetting.dailyGoalMinutes).toBe(30);
      expect(userSetting.reminderTime).toBe('08:00');
      expect(userSetting.privacySettings).toBeDefined();
      expect(userSetting.privacySettings.showLearningProgress).toBe(true);
      expect(userSetting.privacySettings.showLearningHistory).toBe(true);
      expect(userSetting.privacySettings.showFollowers).toBe(true);
      expect(userSetting.notificationSettings).toHaveLength(3);
      
      // 验证默认通知设置
      const dailyReminder = userSetting.notificationSettings.find(
        ns => ns.notificationType.equals(NotificationType.DAILY_REMINDER)
      );
      expect(dailyReminder).toBeDefined();
      expect(dailyReminder?.enabled).toBe(true);
      
      const weeklySummary = userSetting.notificationSettings.find(
        ns => ns.notificationType.equals(NotificationType.WEEKLY_SUMMARY)
      );
      expect(weeklySummary).toBeDefined();
      expect(weeklySummary?.enabled).toBe(true);
      
      const achievementEarned = userSetting.notificationSettings.find(
        ns => ns.notificationType.equals(NotificationType.ACHIEVEMENT_EARNED)
      );
      expect(achievementEarned).toBeDefined();
      expect(achievementEarned?.enabled).toBe(true);
    });
  });

  describe('updateTheme', () => {
    it('should update the theme and add an event', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();
      const newTheme = 'dark';

      // 更新主题
      userSetting.updateTheme(newTheme);

      // 验证属性
      expect(userSetting.theme).toBe(newTheme);

      // 验证领域事件
      expect(userSetting.domainEvents).toHaveLength(1);
      expect(userSetting.domainEvents[0]).toBeInstanceOf(UserSettingUpdatedEvent);
      
      const event = userSetting.domainEvents[0] as UserSettingUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.theme?.from).toBe(theme);
      expect(event.changes.theme?.to).toBe(newTheme);
    });

    it('should not update if the theme is the same', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();

      // 更新主题为相同值
      userSetting.updateTheme(theme);

      // 验证没有领域事件
      expect(userSetting.domainEvents).toHaveLength(0);
    });
  });

  describe('updateFontSize', () => {
    it('should update the font size and add an event', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();
      const newFontSize = 'large';

      // 更新字体大小
      userSetting.updateFontSize(newFontSize);

      // 验证属性
      expect(userSetting.fontSize).toBe(newFontSize);

      // 验证领域事件
      expect(userSetting.domainEvents).toHaveLength(1);
      expect(userSetting.domainEvents[0]).toBeInstanceOf(UserSettingUpdatedEvent);
      
      const event = userSetting.domainEvents[0] as UserSettingUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.fontSize?.from).toBe(fontSize);
      expect(event.changes.fontSize?.to).toBe(newFontSize);
    });

    it('should not update if the font size is the same', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();

      // 更新字体大小为相同值
      userSetting.updateFontSize(fontSize);

      // 验证没有领域事件
      expect(userSetting.domainEvents).toHaveLength(0);
    });
  });

  describe('updateDailyGoalMinutes', () => {
    it('should update the daily goal minutes and add an event', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();
      const newDailyGoalMinutes = 60;

      // 更新每日目标分钟数
      userSetting.updateDailyGoalMinutes(newDailyGoalMinutes);

      // 验证属性
      expect(userSetting.dailyGoalMinutes).toBe(newDailyGoalMinutes);

      // 验证领域事件
      expect(userSetting.domainEvents).toHaveLength(1);
      expect(userSetting.domainEvents[0]).toBeInstanceOf(UserSettingUpdatedEvent);
      
      const event = userSetting.domainEvents[0] as UserSettingUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.dailyGoalMinutes?.from).toBe(dailyGoalMinutes);
      expect(event.changes.dailyGoalMinutes?.to).toBe(newDailyGoalMinutes);
    });

    it('should not update if the daily goal minutes is the same', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();

      // 更新每日目标分钟数为相同值
      userSetting.updateDailyGoalMinutes(dailyGoalMinutes);

      // 验证没有领域事件
      expect(userSetting.domainEvents).toHaveLength(0);
    });

    it('should throw an error if daily goal minutes is negative', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();

      // 验证抛出异常
      expect(() => userSetting.updateDailyGoalMinutes(-1)).toThrow('每日目标分钟数不能为负数');
    });
  });

  describe('updateReminderTime', () => {
    it('should update the reminder time and add an event', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();
      const newReminderTime = '18:00';

      // 更新提醒时间
      userSetting.updateReminderTime(newReminderTime);

      // 验证属性
      expect(userSetting.reminderTime).toBe(newReminderTime);

      // 验证领域事件
      expect(userSetting.domainEvents).toHaveLength(1);
      expect(userSetting.domainEvents[0]).toBeInstanceOf(UserSettingUpdatedEvent);
      
      const event = userSetting.domainEvents[0] as UserSettingUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.reminderTime?.from).toBe(reminderTime);
      expect(event.changes.reminderTime?.to).toBe(newReminderTime);
    });

    it('should not update if the reminder time is the same', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();

      // 更新提醒时间为相同值
      userSetting.updateReminderTime(reminderTime);

      // 验证没有领域事件
      expect(userSetting.domainEvents).toHaveLength(0);
    });
  });

  describe('updatePrivacySettings', () => {
    it('should update the privacy settings and add an event', () => {
      // 创建用户设置
      const userSetting = createTestUserSetting();
      const newPrivacySettings = new PrivacySettings(false, false, true);

      // 更新隐私设置
      userSetting.updatePrivacySettings(newPrivacySettings);

      // 验证属性
      expect(userSetting.privacySettings.showLearningProgress).toBe(false);
      expect(userSetting.privacySettings.showLearningHistory).toBe(false);
      expect(userSetting.privacySettings.showFollowers).toBe(true);

      // 验证领域事件
      expect(userSetting.domainEvents).toHaveLength(1);
      expect(userSetting.domainEvents[0]).toBeInstanceOf(UserSettingUpdatedEvent);
      
      const event = userSetting.domainEvents[0] as UserSettingUpdatedEvent;
      expect(event.userId).toBe(userId);
      expect(event.changes.privacySettings).toBeDefined();
    });
  });
});
