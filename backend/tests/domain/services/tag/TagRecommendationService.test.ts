import { TagRecommendationService } from '../../../../domain/services/tag/TagRecommendationService';
import { Tag } from '../../../../domain/models/tag/Tag';
import { LearningTemplate } from '../../../../domain/models/learningTemplate/LearningTemplate';
import { TemplateTag } from '../../../../domain/models/learningTemplate/TemplateTag';

// 模拟仓库
const mockTagRepository = {
  findById: jest.fn(),
  findByPopularity: jest.fn(),
  searchByKeyword: jest.fn()
};

const mockLearningTemplateRepository = {
  findById: jest.fn(),
  findByThemeId: jest.fn()
};

const mockThemeRepository = {
  findById: jest.fn()
};

const mockTemplateTagRepository = {
  findByTemplateId: jest.fn()
};

describe('TagRecommendationService', () => {
  let service: TagRecommendationService;

  beforeEach(() => {
    // 重置所有模拟函数
    jest.clearAllMocks();

    // 创建服务实例
    service = new TagRecommendationService(
      mockTagRepository as any,
      mockLearningTemplateRepository as any,
      mockThemeRepository as any,
      mockTemplateTagRepository as any
    );
  });

  describe('recommendTagsForTemplate', () => {
    it('应该基于模板推荐标签', async () => {
      // 模拟数据
      const templateId = 1;
      const themeId = 10;
      
      // 模拟模板
      const template: LearningTemplate = {
        id: templateId,
        themeId: themeId,
        title: '高效沟通技巧',
        description: '学习如何在各种场景下进行有效沟通',
        coverImageUrl: 'https://example.com/images/communication.jpg',
        difficulty: 'intermediate',
        estimatedDays: 14,
        dailyGoalMinutes: 30,
        isOfficial: false,
        creatorId: 'user123',
        popularity: 0,
        rating: 5.0,
        ratingCount: 0,
        price: 0,
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
        isDeleted: false
      };
      
      // 模拟当前模板的标签
      const currentTemplateTags: TemplateTag[] = [
        { templateId, tagId: 1, createdAt: new Date() },
        { templateId, tagId: 2, createdAt: new Date() }
      ];
      
      // 模拟同主题下的其他模板
      const themeTemplates: LearningTemplate[] = [
        { ...template, id: 2 },
        { ...template, id: 3 }
      ];
      
      // 模拟其他模板的标签
      const template2Tags: TemplateTag[] = [
        { templateId: 2, tagId: 1, createdAt: new Date() },
        { templateId: 2, tagId: 3, createdAt: new Date() },
        { templateId: 2, tagId: 4, createdAt: new Date() }
      ];
      
      const template3Tags: TemplateTag[] = [
        { templateId: 3, tagId: 2, createdAt: new Date() },
        { templateId: 3, tagId: 3, createdAt: new Date() },
        { templateId: 3, tagId: 5, createdAt: new Date() }
      ];
      
      // 模拟标签
      const tag3: Tag = { id: 3, name: '演讲', categoryId: 2, popularity: 72, createdAt: new Date(), updatedAt: new Date() };
      const tag4: Tag = { id: 4, name: '倾听', categoryId: 2, popularity: 68, createdAt: new Date(), updatedAt: new Date() };
      const tag5: Tag = { id: 5, name: '非语言沟通', categoryId: 2, popularity: 65, createdAt: new Date(), updatedAt: new Date() };
      
      // 设置模拟函数返回值
      mockLearningTemplateRepository.findById.mockResolvedValue(template);
      mockLearningTemplateRepository.findByThemeId.mockResolvedValue([template, ...themeTemplates]);
      mockTemplateTagRepository.findByTemplateId.mockImplementation((id) => {
        if (id === templateId) return Promise.resolve(currentTemplateTags);
        if (id === 2) return Promise.resolve(template2Tags);
        if (id === 3) return Promise.resolve(template3Tags);
        return Promise.resolve([]);
      });
      mockTagRepository.findById.mockImplementation((id) => {
        if (id === 3) return Promise.resolve(tag3);
        if (id === 4) return Promise.resolve(tag4);
        if (id === 5) return Promise.resolve(tag5);
        return Promise.resolve(null);
      });
      
      // 调用方法
      const result = await service.recommendTagsForTemplate(templateId);
      
      // 验证结果
      expect(result).toHaveLength(3);
      expect(result).toContainEqual(tag3);
      expect(result).toContainEqual(tag4);
      expect(result).toContainEqual(tag5);
      
      // 验证调用
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(templateId);
      expect(mockLearningTemplateRepository.findByThemeId).toHaveBeenCalledWith(themeId);
      expect(mockTemplateTagRepository.findByTemplateId).toHaveBeenCalledWith(templateId);
      expect(mockTemplateTagRepository.findByTemplateId).toHaveBeenCalledWith(2);
      expect(mockTemplateTagRepository.findByTemplateId).toHaveBeenCalledWith(3);
    });
    
    it('应该在没有足够标签时添加热门标签', async () => {
      // 模拟数据
      const templateId = 1;
      const themeId = 10;
      
      // 模拟模板
      const template: LearningTemplate = {
        id: templateId,
        themeId: themeId,
        title: '高效沟通技巧',
        description: '学习如何在各种场景下进行有效沟通',
        coverImageUrl: 'https://example.com/images/communication.jpg',
        difficulty: 'intermediate',
        estimatedDays: 14,
        dailyGoalMinutes: 30,
        isOfficial: false,
        creatorId: 'user123',
        popularity: 0,
        rating: 5.0,
        ratingCount: 0,
        price: 0,
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
        isDeleted: false
      };
      
      // 模拟当前模板的标签
      const currentTemplateTags: TemplateTag[] = [
        { templateId, tagId: 1, createdAt: new Date() },
        { templateId, tagId: 2, createdAt: new Date() }
      ];
      
      // 模拟同主题下的其他模板（没有其他模板）
      const themeTemplates: LearningTemplate[] = [];
      
      // 模拟热门标签
      const popularTags: Tag[] = [
        { id: 3, name: '演讲', categoryId: 2, popularity: 72, createdAt: new Date(), updatedAt: new Date() },
        { id: 4, name: '倾听', categoryId: 2, popularity: 68, createdAt: new Date(), updatedAt: new Date() },
        { id: 5, name: '非语言沟通', categoryId: 2, popularity: 65, createdAt: new Date(), updatedAt: new Date() }
      ];
      
      // 设置模拟函数返回值
      mockLearningTemplateRepository.findById.mockResolvedValue(template);
      mockLearningTemplateRepository.findByThemeId.mockResolvedValue([template]);
      mockTemplateTagRepository.findByTemplateId.mockResolvedValue(currentTemplateTags);
      mockTagRepository.findByPopularity.mockResolvedValue(popularTags);
      mockTagRepository.findById.mockImplementation((id) => {
        return Promise.resolve(popularTags.find(tag => tag.id === id) || null);
      });
      
      // 调用方法
      const result = await service.recommendTagsForTemplate(templateId);
      
      // 验证结果
      expect(result).toHaveLength(3);
      expect(result).toEqual(expect.arrayContaining(popularTags));
      
      // 验证调用
      expect(mockLearningTemplateRepository.findById).toHaveBeenCalledWith(templateId);
      expect(mockLearningTemplateRepository.findByThemeId).toHaveBeenCalledWith(themeId);
      expect(mockTemplateTagRepository.findByTemplateId).toHaveBeenCalledWith(templateId);
      expect(mockTagRepository.findByPopularity).toHaveBeenCalled();
    });
  });

  describe('recommendTagsForContent', () => {
    it('应该基于内容推荐标签', async () => {
      // 模拟数据
      const title = '高效沟通技巧';
      const description = '学习如何在各种场景下进行有效沟通';
      const themeId = 10;
      
      // 模拟关键词搜索结果
      const communicationTags: Tag[] = [
        { id: 1, name: '沟通技巧', categoryId: 2, popularity: 85, createdAt: new Date(), updatedAt: new Date() },
        { id: 2, name: '人际关系', categoryId: 2, popularity: 80, createdAt: new Date(), updatedAt: new Date() }
      ];
      
      const effectiveTags: Tag[] = [
        { id: 3, name: '演讲', categoryId: 2, popularity: 72, createdAt: new Date(), updatedAt: new Date() },
        { id: 4, name: '倾听', categoryId: 2, popularity: 68, createdAt: new Date(), updatedAt: new Date() }
      ];
      
      // 设置模拟函数返回值
      mockTagRepository.searchByKeyword.mockImplementation((keyword) => {
        if (keyword.includes('沟通')) return Promise.resolve(communicationTags);
        if (keyword.includes('有效')) return Promise.resolve(effectiveTags);
        return Promise.resolve([]);
      });
      
      mockLearningTemplateRepository.findByThemeId.mockResolvedValue([]);
      
      mockTagRepository.findById.mockImplementation((id) => {
        const allTags = [...communicationTags, ...effectiveTags];
        return Promise.resolve(allTags.find(tag => tag.id === id) || null);
      });
      
      // 调用方法
      const result = await service.recommendTagsForContent(title, description, themeId);
      
      // 验证结果
      expect(result.length).toBeGreaterThan(0);
      expect(result).toEqual(expect.arrayContaining([...communicationTags, ...effectiveTags]));
      
      // 验证调用
      expect(mockTagRepository.searchByKeyword).toHaveBeenCalled();
      expect(mockLearningTemplateRepository.findByThemeId).toHaveBeenCalledWith(themeId);
    });
  });

  describe('extractKeywords', () => {
    it('应该从文本中提取关键词', () => {
      // 使用私有方法测试
      const extractKeywords = (service as any).extractKeywords.bind(service);
      
      const title = '高效沟通技巧';
      const description = '学习如何在各种场景下进行有效沟通';
      
      const keywords = extractKeywords(title, description);
      
      // 验证结果
      expect(keywords).toContain('高效');
      expect(keywords).toContain('沟通');
      expect(keywords).toContain('技巧');
      expect(keywords).toContain('学习');
      expect(keywords).toContain('如何');
      expect(keywords).toContain('各种');
      expect(keywords).toContain('场景');
      expect(keywords).toContain('进行');
      expect(keywords).toContain('有效');
      
      // 验证停用词被过滤
      expect(keywords).not.toContain('的');
      expect(keywords).not.toContain('在');
    });
  });
});
