import request from 'supertest';
import { app } from '../../../../../app';
import { sequelize } from '../../../../../infrastructure/persistence/sequelize/sequelize';
import { LearningPlan } from '../../../../../domain/models/content/learningPlan/LearningPlan';
import { LearningPlanRepository } from '../../../../../domain/repositories/content/learningPlan/LearningPlanRepository';
import { container } from '../../../../../container';

describe('LearningPlan API', () => {
  let learningPlanRepository: LearningPlanRepository;
  let testPlanId: number;
  
  beforeAll(async () => {
    // 获取仓库实例
    learningPlanRepository = container.resolve('learningPlanRepository');
    
    // 清理测试数据
    await sequelize.query('DELETE FROM learning_plan WHERE title LIKE "测试学习计划%"');
  });
  
  afterAll(async () => {
    // 清理测试数据
    await sequelize.query('DELETE FROM learning_plan WHERE title LIKE "测试学习计划%"');
  });
  
  describe('POST /api/v2/learning-plans', () => {
    it('should create a new learning plan', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans')
        .send({
          title: '测试学习计划 - 创建',
          description: '这是一个测试学习计划的描述',
          userId: 'test-user-1',
          targetDays: 7,
          dailyGoalExercises: 3,
          dailyGoalInsights: 5,
          dailyGoalMinutes: 15,
          isPublic: true,
          tags: ['测试', '学习计划']
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.title).toBe('测试学习计划 - 创建');
      expect(response.body.description).toBe('这是一个测试学习计划的描述');
      expect(response.body.userId).toBe('test-user-1');
      expect(response.body.targetDays).toBe(7);
      expect(response.body.completedDays).toBe(0);
      expect(response.body.progress).toBe(0);
      expect(response.body.dailyGoalExercises).toBe(3);
      expect(response.body.dailyGoalInsights).toBe(5);
      expect(response.body.dailyGoalMinutes).toBe(15);
      expect(response.body.status).toBe('not_started');
      expect(response.body.isPublic).toBe(true);
      expect(response.body.tags).toEqual(['测试', '学习计划']);
      expect(response.body.isDeleted).toBe(false);
      
      // 保存ID用于后续测试
      testPlanId = response.body.id;
    });
    
    it('should return 400 if title is missing', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans')
        .send({
          description: '这是一个测试学习计划的描述',
          userId: 'test-user-1'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should return 400 if userId is missing', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans')
        .send({
          title: '测试学习计划 - 无用户',
          description: '这是一个测试学习计划的描述'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('GET /api/v2/learning-plans/:id', () => {
    it('should get a learning plan by id', async () => {
      const response = await request(app)
        .get(`/api/v2/learning-plans/${testPlanId}`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.title).toBe('测试学习计划 - 创建');
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .get('/api/v2/learning-plans/99999');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('PUT /api/v2/learning-plans/:id', () => {
    it('should update a learning plan', async () => {
      const response = await request(app)
        .put(`/api/v2/learning-plans/${testPlanId}`)
        .send({
          title: '测试学习计划 - 已更新',
          description: '这是更新后的学习计划描述',
          targetDays: 10,
          dailyGoalMinutes: 20,
          isPublic: false
        });
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.title).toBe('测试学习计划 - 已更新');
      expect(response.body.description).toBe('这是更新后的学习计划描述');
      expect(response.body.targetDays).toBe(10);
      expect(response.body.dailyGoalMinutes).toBe(20);
      expect(response.body.isPublic).toBe(false);
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .put('/api/v2/learning-plans/99999')
        .send({
          title: '测试学习计划 - 不存在',
          description: '这是一个不存在的学习计划'
        });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('POST /api/v2/learning-plans/:id/start', () => {
    it('should start a learning plan', async () => {
      const response = await request(app)
        .post(`/api/v2/learning-plans/${testPlanId}/start`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.status).toBe('in_progress');
      expect(response.body.startDate).not.toBeNull();
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans/99999/start');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('POST /api/v2/learning-plans/:id/complete', () => {
    it('should complete a learning plan', async () => {
      const response = await request(app)
        .post(`/api/v2/learning-plans/${testPlanId}/complete`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.status).toBe('completed');
      expect(response.body.endDate).not.toBeNull();
      expect(response.body.completedDays).toBe(response.body.targetDays);
      expect(response.body.progress).toBe(100);
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans/99999/complete');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('POST /api/v2/learning-plans/:id/tags', () => {
    it('should add a tag to a learning plan', async () => {
      const response = await request(app)
        .post(`/api/v2/learning-plans/${testPlanId}/tags`)
        .send({
          tag: '新标签'
        });
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.tags).toContain('新标签');
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans/99999/tags')
        .send({
          tag: '新标签'
        });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('DELETE /api/v2/learning-plans/:id/tags/:tag', () => {
    it('should remove a tag from a learning plan', async () => {
      const response = await request(app)
        .delete(`/api/v2/learning-plans/${testPlanId}/tags/新标签`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.tags).not.toContain('新标签');
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .delete('/api/v2/learning-plans/99999/tags/新标签');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('DELETE /api/v2/learning-plans/:id', () => {
    it('should soft delete a learning plan', async () => {
      const response = await request(app)
        .delete(`/api/v2/learning-plans/${testPlanId}`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.isDeleted).toBe(true);
      expect(response.body.deletedAt).not.toBeNull();
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .delete('/api/v2/learning-plans/99999');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('POST /api/v2/learning-plans/:id/restore', () => {
    it('should restore a deleted learning plan', async () => {
      const response = await request(app)
        .post(`/api/v2/learning-plans/${testPlanId}/restore`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testPlanId);
      expect(response.body.isDeleted).toBe(false);
      expect(response.body.deletedAt).toBeNull();
    });
    
    it('should return 404 if learning plan not found', async () => {
      const response = await request(app)
        .post('/api/v2/learning-plans/99999/restore');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
});
