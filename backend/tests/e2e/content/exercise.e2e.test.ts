import request from 'supertest';
import app from '../../../index';
import { container } from '../../../infrastructure/config/containerConfig';
import jwt from 'jsonwebtoken';
import { Difficulty } from '../../../domain/models/content/Difficulty';
import { Visibility } from '../../../domain/models/content/Visibility';

// 测试前的准备工作
beforeAll(async () => {
  // 初始化依赖注入容器
  if (typeof (container as any).initializeContainer === 'function') {
    await (container as any).initializeContainer();
  }
});

// 生成测试用的JWT令牌
const generateToken = (userId: string, role: string): string => {
  return jwt.sign(
    { id: userId, role },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '1h' }
  );
};

describe('练习API端到端测试', () => {
  // 测试数据
  const testToken = generateToken('test-user', 'admin');
  let createdExerciseId: number;

  // 测试练习的创建
  describe('创建练习', () => {
    it('应该成功创建练习', async () => {
      const response = await request(app)
        .post('/api/v2/exercises')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          title: '测试练习',
          description: '这是一个测试练习的描述',
          expectedResult: '预期结果是完成测试',
          difficulty: Difficulty.MEDIUM,
          timeEstimateMinutes: 15,
          visibility: Visibility.PUBLIC,
          isOfficial: false,
          tags: ['标签1', '标签2']
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.title).toBe('测试练习');
      expect(response.body.tags).toContain('标签1');
      expect(response.body.tags).toContain('标签2');
      
      createdExerciseId = response.body.id;
    });
  });

  // 测试练习的查询
  describe('查询练习', () => {
    it('应该能够通过ID查询练习', async () => {
      const response = await request(app)
        .get(`/api/v2/exercises/${createdExerciseId}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(createdExerciseId);
      expect(response.body.title).toBe('测试练习');
    });

    it('应该能够搜索练习', async () => {
      const response = await request(app)
        .get('/api/v2/exercises')
        .query({ keyword: '测试' });

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body.some((exercise: any) => exercise.id === createdExerciseId)).toBe(true);
    });
  });

  // 测试练习的更新
  describe('更新练习', () => {
    it('应该能够更新练习', async () => {
      const response = await request(app)
        .put(`/api/v2/exercises/${createdExerciseId}`)
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          title: '更新后的练习',
          description: '这是更新后的练习描述',
          difficulty: Difficulty.HARD
        });

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(createdExerciseId);
      expect(response.body.title).toBe('更新后的练习');
      expect(response.body.description).toBe('这是更新后的练习描述');
      expect(response.body.difficulty).toBe(Difficulty.HARD);
    });
  });

  // 测试练习的发布
  describe('发布练习', () => {
    it('应该能够发布练习', async () => {
      const response = await request(app)
        .post(`/api/v2/exercises/${createdExerciseId}/publish`)
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(createdExerciseId);
      expect(response.body.isPublished).toBe(true);
    });
  });

  // 测试练习的软删除
  describe('软删除练习', () => {
    it('应该能够软删除练习', async () => {
      const response = await request(app)
        .delete(`/api/v2/exercises/${createdExerciseId}`)
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(204);

      // 验证练习已被软删除
      const getResponse = await request(app)
        .get(`/api/v2/exercises/${createdExerciseId}`);

      expect(getResponse.status).toBe(200);
      expect(getResponse.body.isDeleted).toBe(true);
    });
  });

  // 测试练习的恢复
  describe('恢复练习', () => {
    it('应该能够恢复已删除的练习', async () => {
      const response = await request(app)
        .post(`/api/v2/exercises/${createdExerciseId}/restore`)
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(createdExerciseId);
      expect(response.body.isDeleted).toBe(false);
    });
  });
});
