import request from 'supertest';
import { app } from '../../app';
import { sequelize } from '../../config/database';
import { models } from '../../models';
import { generateAuthToken } from '../../utils/auth';

describe('LearningTemplate API', () => {
  let authToken: string;
  let themeId: number;
  let templateId: number;
  let tagId: number;

  beforeAll(async () => {
    // 创建测试用户
    const user = await models.User.create({
      username: 'test_user',
      email: '<EMAIL>',
      password_hash: 'hash',
      role: 'admin',
      created_at: new Date(),
      updated_at: new Date()
    });

    // 生成认证令牌
    authToken = generateAuthToken(user);

    // 创建测试主题
    const theme = await models.Theme.create({
      name: 'Test Theme',
      description: 'Test Theme Description',
      status: 'active',
      created_at: new Date(),
      updated_at: new Date()
    });
    themeId = theme.id;

    // 创建测试标签
    const tag = await models.Tag.create({
      name: 'Test Tag',
      category_id: 1,
      created_at: new Date(),
      updated_at: new Date()
    });
    tagId = tag.id;
  });

  afterAll(async () => {
    // 清理测试数据
    await models.TemplateTag.destroy({ where: {} });
    await models.LearningTemplate.destroy({ where: {} });
    await models.Theme.destroy({ where: { id: themeId } });
    await models.Tag.destroy({ where: { id: tagId } });
    await models.User.destroy({ where: { email: '<EMAIL>' } });

    // 关闭数据库连接
    await sequelize.close();
  });

  describe('POST /api/v2/learning-templates', () => {
    it('should create a learning template', async () => {
      const response = await request(app)
        .post('/api/v2/learning-templates')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          themeId,
          title: 'Test Template',
          description: 'Test Description',
          coverImageUrl: 'http://example.com/image.jpg',
          difficulty: 'beginner',
          estimatedDays: 7,
          dailyGoalMinutes: 15,
          isOfficial: true,
          creatorId: 'user123',
          price: 0,
          tagIds: [tagId]
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.title).toBe('Test Template');
      expect(response.body.difficulty).toBe('beginner');
      expect(response.body.tagIds).toContain(tagId);

      templateId = response.body.id;
    });

    it('should return 400 if theme does not exist', async () => {
      const response = await request(app)
        .post('/api/v2/learning-templates')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          themeId: 9999,
          title: 'Test Template',
          description: 'Test Description',
          coverImageUrl: 'http://example.com/image.jpg',
          difficulty: 'beginner',
          estimatedDays: 7,
          dailyGoalMinutes: 15,
          isOfficial: true,
          creatorId: 'user123',
          price: 0
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('主题ID 9999 不存在');
    });
  });

  describe('GET /api/v2/learning-templates/:id', () => {
    it('should return a learning template by id', async () => {
      const response = await request(app)
        .get(`/api/v2/learning-templates/${templateId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', templateId);
      expect(response.body.title).toBe('Test Template');
      expect(response.body.difficulty).toBe('beginner');
      expect(response.body.tagIds).toContain(tagId);
    });

    it('should return 404 if template does not exist', async () => {
      const response = await request(app)
        .get('/api/v2/learning-templates/9999')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('学习模板ID 9999 不存在');
    });
  });

  describe('PUT /api/v2/learning-templates/:id', () => {
    it('should update a learning template', async () => {
      const response = await request(app)
        .put(`/api/v2/learning-templates/${templateId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Updated Template',
          description: 'Updated Description',
          coverImageUrl: 'http://example.com/updated.jpg',
          difficulty: 'intermediate',
          estimatedDays: 14,
          dailyGoalMinutes: 30,
          price: 9.99,
          tagIds: [tagId]
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', templateId);
      expect(response.body.title).toBe('Updated Template');
      expect(response.body.description).toBe('Updated Description');
      expect(response.body.difficulty).toBe('intermediate');
      expect(response.body.tagIds).toContain(tagId);
    });
  });

  describe('POST /api/v2/learning-templates/:id/publish', () => {
    it('should publish a learning template', async () => {
      const response = await request(app)
        .post(`/api/v2/learning-templates/${templateId}/publish`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', templateId);
      expect(response.body.status).toBe('published');
    });
  });

  describe('POST /api/v2/learning-templates/:id/archive', () => {
    it('should archive a learning template', async () => {
      const response = await request(app)
        .post(`/api/v2/learning-templates/${templateId}/archive`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', templateId);
      expect(response.body.status).toBe('archived');
    });
  });

  describe('DELETE /api/v2/learning-templates/:id', () => {
    it('should delete a learning template', async () => {
      const response = await request(app)
        .delete(`/api/v2/learning-templates/${templateId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(204);
    });
  });

  describe('POST /api/v2/learning-templates/:id/restore', () => {
    it('should restore a deleted learning template', async () => {
      const response = await request(app)
        .post(`/api/v2/learning-templates/${templateId}/restore`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', templateId);
      expect(response.body.deletedAt).toBeNull();
    });
  });

  describe('GET /api/v2/learning-templates', () => {
    it('should return a list of learning templates', async () => {
      const response = await request(app)
        .get('/api/v2/learning-templates')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('id');
      expect(response.body[0]).toHaveProperty('title');
    });

    it('should filter learning templates by theme', async () => {
      const response = await request(app)
        .get(`/api/v2/learning-templates?themeId=${themeId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].themeId).toBe(themeId);
    });
  });

  describe('GET /api/v2/learning-templates/search', () => {
    it('should search for learning templates by keyword', async () => {
      const response = await request(app)
        .get('/api/v2/learning-templates/search?keyword=Updated')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].title).toContain('Updated');
    });

    it('should return 400 if keyword is missing', async () => {
      const response = await request(app)
        .get('/api/v2/learning-templates/search')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('关键字不能为空');
    });
  });
});
