/**
 * DeadLetterQueueService单元测试
 */
import { DeadLetterQueueService } from '../../../infrastructure/events/DeadLetterQueueService';
import { Logger } from '../../../infrastructure/logging/Logger';
import { Sequelize, DataTypes, Model } from 'sequelize';

// 模拟Logger
class MockLogger implements Logger {
  public logs: { level: string; message: string; meta?: any }[] = [];
  
  debug(message: string, meta?: any): void {
    this.logs.push({ level: 'debug', message, meta });
  }
  
  info(message: string, meta?: any): void {
    this.logs.push({ level: 'info', message, meta });
  }
  
  warn(message: string, meta?: any): void {
    this.logs.push({ level: 'warn', message, meta });
  }
  
  error(message: string, meta?: any): void {
    this.logs.push({ level: 'error', message, meta });
  }
  
  fatal(message: string, meta?: any): void {
    this.logs.push({ level: 'fatal', message, meta });
  }
  
  setLevel(level: string): void {}
  
  getLevel(): string {
    return 'debug';
  }
  
  child(name: string): Logger {
    return this;
  }
  
  clear(): void {
    this.logs = [];
  }
}

// 模拟事件总线
class MockEventBus {
  public publishedEvents: { eventType: string; event: any }[] = [];
  
  async publish(eventType: string, event: any): Promise<void> {
    this.publishedEvents.push({ eventType, event });
  }
  
  clear(): void {
    this.publishedEvents = [];
  }
}

describe('DeadLetterQueueService', () => {
  let deadLetterQueueService: DeadLetterQueueService;
  let logger: MockLogger;
  let eventBus: MockEventBus;
  let sequelize: Sequelize;
  let DeadLetterQueueModel: any;
  
  beforeAll(async () => {
    // 创建内存数据库
    sequelize = new Sequelize('sqlite::memory:', {
      logging: false
    });
    
    // 定义死信队列模型
    DeadLetterQueueModel = sequelize.define('DeadLetterQueue', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      event_id: {
        type: DataTypes.STRING,
        allowNull: false
      },
      event_type: {
        type: DataTypes.STRING,
        allowNull: false
      },
      event_data: {
        type: DataTypes.JSON,
        allowNull: false
      },
      handler_name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      error_message: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      retry_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      status: {
        type: DataTypes.ENUM('pending', 'retrying', 'resolved', 'failed'),
        allowNull: false,
        defaultValue: 'pending'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      resolved_at: {
        type: DataTypes.DATE,
        allowNull: true
      }
    }, {
      tableName: 'dead_letter_queue',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    });
    
    // 同步模型到数据库
    await sequelize.sync({ force: true });
  });
  
  beforeEach(() => {
    logger = new MockLogger();
    eventBus = new MockEventBus();
    
    deadLetterQueueService = new DeadLetterQueueService(
      sequelize,
      logger
    );
  });
  
  afterEach(async () => {
    logger.clear();
    eventBus.clear();
    await DeadLetterQueueModel.destroy({ truncate: true });
  });
  
  afterAll(async () => {
    await sequelize.close();
  });
  
  test('应该成功添加事件到死信队列', async () => {
    const eventId = 'event-1';
    const eventType = 'test.event';
    const eventData = { foo: 'bar' };
    const handlerName = 'TestHandler';
    const errorMessage = 'Test error';
    
    const id = await deadLetterQueueService.addToDeadLetterQueue(
      eventId,
      eventType,
      eventData,
      handlerName,
      errorMessage
    );
    
    expect(id).toBeDefined();
    
    // 验证记录已添加到数据库
    const record = await DeadLetterQueueModel.findByPk(id);
    expect(record).not.toBeNull();
    expect(record.event_id).toBe(eventId);
    expect(record.event_type).toBe(eventType);
    expect(record.handler_name).toBe(handlerName);
    expect(record.error_message).toBe(errorMessage);
    expect(record.status).toBe('pending');
    expect(record.retry_count).toBe(0);
    
    // 验证日志已记录
    expect(logger.logs.some(log => 
      log.level === 'info' && 
      log.message.includes(`事件已添加到死信队列: ${eventType}`)
    )).toBe(true);
  });
  
  test('应该成功获取死信队列中的事件', async () => {
    // 添加测试数据
    await DeadLetterQueueModel.bulkCreate([
      {
        event_id: 'event-1',
        event_type: 'test.event',
        event_data: { foo: 'bar' },
        handler_name: 'TestHandler',
        error_message: 'Test error',
        status: 'pending'
      },
      {
        event_id: 'event-2',
        event_type: 'test.event',
        event_data: { foo: 'baz' },
        handler_name: 'TestHandler',
        error_message: 'Test error',
        status: 'retrying'
      },
      {
        event_id: 'event-3',
        event_type: 'other.event',
        event_data: { foo: 'qux' },
        handler_name: 'OtherHandler',
        error_message: 'Other error',
        status: 'pending'
      }
    ]);
    
    // 获取所有事件
    const allEvents = await deadLetterQueueService.getDeadLetterQueueEvents();
    expect(allEvents).toHaveLength(3);
    
    // 按状态过滤
    const pendingEvents = await deadLetterQueueService.getDeadLetterQueueEvents('pending');
    expect(pendingEvents).toHaveLength(2);
    
    // 按事件类型过滤
    const testEvents = await deadLetterQueueService.getDeadLetterQueueEvents(undefined, 'test.event');
    expect(testEvents).toHaveLength(2);
    
    // 组合过滤
    const pendingTestEvents = await deadLetterQueueService.getDeadLetterQueueEvents('pending', 'test.event');
    expect(pendingTestEvents).toHaveLength(1);
    expect(pendingTestEvents[0].event_id).toBe('event-1');
  });
  
  test('应该成功重试死信队列中的事件', async () => {
    // 添加测试数据
    const record = await DeadLetterQueueModel.create({
      event_id: 'event-1',
      event_type: 'test.event',
      event_data: { foo: 'bar' },
      handler_name: 'TestHandler',
      error_message: 'Test error',
      status: 'pending'
    });
    
    // 模拟事件总线
    const mockEventBus = {
      publish: jest.fn().mockResolvedValue(undefined)
    };
    
    // 重试事件
    const success = await deadLetterQueueService.retryEvent(record.id, mockEventBus);
    
    expect(success).toBe(true);
    expect(mockEventBus.publish).toHaveBeenCalledWith('test.event', { foo: 'bar' });
    
    // 验证记录状态已更新
    const updatedRecord = await DeadLetterQueueModel.findByPk(record.id);
    expect(updatedRecord.status).toBe('resolved');
    expect(updatedRecord.resolved_at).not.toBeNull();
    
    // 验证日志已记录
    expect(logger.logs.some(log => 
      log.level === 'info' && 
      log.message.includes(`死信队列事件重试成功: test.event`)
    )).toBe(true);
  });
  
  test('应该处理重试失败的情况', async () => {
    // 添加测试数据
    const record = await DeadLetterQueueModel.create({
      event_id: 'event-1',
      event_type: 'test.event',
      event_data: { foo: 'bar' },
      handler_name: 'TestHandler',
      error_message: 'Test error',
      status: 'pending'
    });
    
    // 模拟事件总线
    const mockEventBus = {
      publish: jest.fn().mockRejectedValue(new Error('Retry failed'))
    };
    
    // 重试事件
    const success = await deadLetterQueueService.retryEvent(record.id, mockEventBus);
    
    expect(success).toBe(false);
    expect(mockEventBus.publish).toHaveBeenCalledWith('test.event', { foo: 'bar' });
    
    // 验证记录状态已更新
    const updatedRecord = await DeadLetterQueueModel.findByPk(record.id);
    expect(updatedRecord.status).toBe('failed');
    expect(updatedRecord.error_message).toBe('Retry failed');
    
    // 验证日志已记录
    expect(logger.logs.some(log => 
      log.level === 'error' && 
      log.message.includes(`死信队列事件重试失败`)
    )).toBe(true);
  });
  
  test('应该成功标记事件为已解决', async () => {
    // 添加测试数据
    const record = await DeadLetterQueueModel.create({
      event_id: 'event-1',
      event_type: 'test.event',
      event_data: { foo: 'bar' },
      handler_name: 'TestHandler',
      error_message: 'Test error',
      status: 'pending'
    });
    
    // 标记为已解决
    const success = await deadLetterQueueService.resolveEvent(record.id);
    
    expect(success).toBe(true);
    
    // 验证记录状态已更新
    const updatedRecord = await DeadLetterQueueModel.findByPk(record.id);
    expect(updatedRecord.status).toBe('resolved');
    expect(updatedRecord.resolved_at).not.toBeNull();
    
    // 验证日志已记录
    expect(logger.logs.some(log => 
      log.level === 'info' && 
      log.message.includes(`死信队列事件已标记为已解决`)
    )).toBe(true);
  });
  
  test('应该成功清理已解决的事件', async () => {
    // 添加测试数据
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);
    
    await DeadLetterQueueModel.bulkCreate([
      {
        event_id: 'event-1',
        event_type: 'test.event',
        event_data: { foo: 'bar' },
        handler_name: 'TestHandler',
        error_message: 'Test error',
        status: 'resolved',
        resolved_at: twoHoursAgo
      },
      {
        event_id: 'event-2',
        event_type: 'test.event',
        event_data: { foo: 'baz' },
        handler_name: 'TestHandler',
        error_message: 'Test error',
        status: 'resolved',
        resolved_at: oneHourAgo
      },
      {
        event_id: 'event-3',
        event_type: 'test.event',
        event_data: { foo: 'qux' },
        handler_name: 'TestHandler',
        error_message: 'Test error',
        status: 'pending'
      }
    ]);
    
    // 清理90分钟前解决的事件
    const ninetyMinutesAgo = new Date(now.getTime() - 90 * 60 * 1000);
    const count = await deadLetterQueueService.cleanupResolvedEvents(ninetyMinutesAgo);
    
    expect(count).toBe(1); // 应该只清理event-1
    
    // 验证记录已清理
    const remainingRecords = await DeadLetterQueueModel.findAll();
    expect(remainingRecords).toHaveLength(2);
    expect(remainingRecords.some(r => r.event_id === 'event-1')).toBe(false);
    expect(remainingRecords.some(r => r.event_id === 'event-2')).toBe(true);
    expect(remainingRecords.some(r => r.event_id === 'event-3')).toBe(true);
    
    // 验证日志已记录
    expect(logger.logs.some(log => 
      log.level === 'info' && 
      log.message.includes(`已清理1条已解决的死信队列记录`)
    )).toBe(true);
  });
});
