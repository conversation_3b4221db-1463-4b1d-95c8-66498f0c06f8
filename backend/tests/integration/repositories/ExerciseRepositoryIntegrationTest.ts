/**
 * 练习仓库集成测试
 */

import { RepositoryIntegrationTestBase } from './RepositoryIntegrationTestBase';
import { ExerciseRepository } from '../../../domain/repositories/ExerciseRepository';
import { ExerciseRepositoryImpl } from '../../../infrastructure/repositories/ExerciseRepositoryImpl';
import { Exercise } from '../../../domain/entities/Exercise';
import { TestDataGenerator } from './TestDataGenerator';

/**
 * 练习仓库集成测试类
 */
export class ExerciseRepositoryIntegrationTest extends RepositoryIntegrationTestBase {
  /**
   * 练习仓库
   */
  private exerciseRepository: ExerciseRepository;

  /**
   * 测试数据生成器
   */
  private testDataGenerator: TestDataGenerator;

  /**
   * 测试练习数据
   */
  private testExercises: any[];

  /**
   * 测试用户ID
   */
  private testUserId: number;

  /**
   * 构造函数
   */
  constructor() {
    super();
    this.exerciseRepository = new ExerciseRepositoryImpl(this.unitOfWork, this.eventPublisher);
    this.testDataGenerator = new TestDataGenerator(this.sequelize);
    this.testExercises = [];
    this.testUserId = 0;
  }

  /**
   * 初始化测试数据
   */
  protected async initTestData(): Promise<void> {
    // 生成测试用户
    const users = await this.testDataGenerator.generateUsers(1);
    this.testUserId = users[0].id;

    // 生成测试练习数据
    this.testExercises = await this.testDataGenerator.generateExercises(5, this.testUserId);
  }

  /**
   * 测试根据ID查找练习
   */
  async testFindById(): Promise<void> {
    // 获取测试练习
    const testExercise = this.testExercises[0];

    // 查找练习
    const exercise = await this.exerciseRepository.findById(testExercise.id);

    // 断言
    expect(exercise).not.toBeNull();
    expect(exercise.id).toBe(testExercise.id);
    expect(exercise.title).toBe(testExercise.title);
    expect(exercise.description).toBe(testExercise.description);
    expect(exercise.content).toBe(testExercise.content);
    expect(exercise.creatorId).toBe(testExercise.creator_id);
    expect(exercise.isPublic).toBe(testExercise.is_public);
  }

  /**
   * 测试查找所有练习
   */
  async testFindAll(): Promise<void> {
    // 查找所有练习
    const exercises = await this.exerciseRepository.findAll();

    // 断言
    expect(exercises).not.toBeNull();
    expect(exercises.length).toBe(this.testExercises.length);
  }

  /**
   * 测试查找用户的练习
   */
  async testFindByCreatorId(): Promise<void> {
    // 查找用户的练习
    const exercises = await this.exerciseRepository.findByCreatorId(this.testUserId);

    // 断言
    expect(exercises).not.toBeNull();
    expect(exercises.length).toBe(this.testExercises.length);
    exercises.forEach(exercise => {
      expect(exercise.creatorId).toBe(this.testUserId);
    });
  }

  /**
   * 测试查找公开练习
   */
  async testFindPublic(): Promise<void> {
    // 查找公开练习
    const exercises = await this.exerciseRepository.findPublic();

    // 断言
    expect(exercises).not.toBeNull();
    // 公开练习数量应该等于测试练习中的公开练习数量
    const publicExercisesCount = this.testExercises.filter(e => e.is_public).length;
    expect(exercises.length).toBe(publicExercisesCount);
    exercises.forEach(exercise => {
      expect(exercise.isPublic).toBe(true);
    });
  }

  /**
   * 测试创建练习
   */
  async testCreate(): Promise<void> {
    // 开始事务
    await this.beginTransaction();

    try {
      // 创建练习
      const exercise = new Exercise(
        null,
        'New Exercise',
        'New Exercise Description',
        'New Exercise Content',
        this.testUserId,
        true
      );
      const createdExercise = await this.exerciseRepository.save(exercise);

      // 断言
      expect(createdExercise).not.toBeNull();
      expect(createdExercise.id).not.toBeNull();
      expect(createdExercise.title).toBe('New Exercise');
      expect(createdExercise.description).toBe('New Exercise Description');
      expect(createdExercise.content).toBe('New Exercise Content');
      expect(createdExercise.creatorId).toBe(this.testUserId);
      expect(createdExercise.isPublic).toBe(true);

      // 查找创建的练习
      const foundExercise = await this.exerciseRepository.findById(createdExercise.id);

      // 断言
      expect(foundExercise).not.toBeNull();
      expect(foundExercise.id).toBe(createdExercise.id);
      expect(foundExercise.title).toBe(createdExercise.title);
      expect(foundExercise.description).toBe(createdExercise.description);
      expect(foundExercise.content).toBe(createdExercise.content);
      expect(foundExercise.creatorId).toBe(createdExercise.creatorId);
      expect(foundExercise.isPublic).toBe(createdExercise.isPublic);

      // 提交事务
      await this.commitTransaction();
    } catch (error) {
      // 回滚事务
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * 测试更新练习
   */
  async testUpdate(): Promise<void> {
    // 开始事务
    await this.beginTransaction();

    try {
      // 获取测试练习
      const testExercise = this.testExercises[0];

      // 查找练习
      const exercise = await this.exerciseRepository.findById(testExercise.id);

      // 更新练习
      exercise.title = 'Updated Exercise';
      exercise.description = 'Updated Exercise Description';
      exercise.content = 'Updated Exercise Content';
      exercise.isPublic = !exercise.isPublic;
      const updatedExercise = await this.exerciseRepository.save(exercise);

      // 断言
      expect(updatedExercise).not.toBeNull();
      expect(updatedExercise.id).toBe(testExercise.id);
      expect(updatedExercise.title).toBe('Updated Exercise');
      expect(updatedExercise.description).toBe('Updated Exercise Description');
      expect(updatedExercise.content).toBe('Updated Exercise Content');
      expect(updatedExercise.isPublic).toBe(!testExercise.is_public);

      // 查找更新的练习
      const foundExercise = await this.exerciseRepository.findById(testExercise.id);

      // 断言
      expect(foundExercise).not.toBeNull();
      expect(foundExercise.id).toBe(testExercise.id);
      expect(foundExercise.title).toBe('Updated Exercise');
      expect(foundExercise.description).toBe('Updated Exercise Description');
      expect(foundExercise.content).toBe('Updated Exercise Content');
      expect(foundExercise.isPublic).toBe(!testExercise.is_public);

      // 提交事务
      await this.commitTransaction();
    } catch (error) {
      // 回滚事务
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * 测试删除练习
   */
  async testDelete(): Promise<void> {
    // 开始事务
    await this.beginTransaction();

    try {
      // 获取测试练习
      const testExercise = this.testExercises[0];

      // 查找练习
      const exercise = await this.exerciseRepository.findById(testExercise.id);

      // 删除练习
      await this.exerciseRepository.delete(exercise);

      // 查找删除的练习
      const foundExercise = await this.exerciseRepository.findById(testExercise.id);

      // 断言
      expect(foundExercise).toBeNull();

      // 提交事务
      await this.commitTransaction();
    } catch (error) {
      // 回滚事务
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * 测试练习完成事件发布
   */
  async testCompleteExercise(): Promise<void> {
    // 开始事务
    await this.beginTransaction();

    try {
      // 获取测试练习
      const testExercise = this.testExercises[1];

      // 查找练习
      const exercise = await this.exerciseRepository.findById(testExercise.id);

      // 模拟事件总线
      const mockEventBus = {
        publish: jest.fn().mockResolvedValue(undefined)
      };
      (this.eventPublisher as any).eventBus = mockEventBus;

      // 完成练习
      await exercise.complete(this.testUserId);
      await this.exerciseRepository.save(exercise);

      // 断言事件发布
      expect(mockEventBus.publish).toHaveBeenCalled();
      const publishCall = mockEventBus.publish.mock.calls[0];
      expect(publishCall[0]).toBe('ExerciseCompleted');
      expect(publishCall[1].aggregateId).toBe(testExercise.id);
      expect(publishCall[1].userId).toBe(this.testUserId);

      // 提交事务
      await this.commitTransaction();
    } catch (error) {
      // 回滚事务
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * 运行所有测试
   */
  async runTests(): Promise<void> {
    try {
      // 设置测试
      await this.setUp();

      // 运行测试
      await this.testFindById();
      await this.testFindAll();
      await this.testFindByCreatorId();
      await this.testFindPublic();
      await this.testCreate();
      await this.testUpdate();
      await this.testCompleteExercise();
      await this.testDelete();

      console.log('所有测试通过');
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 清理测试
      await this.tearDown();
    }
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const test = new ExerciseRepositoryIntegrationTest();
  test.runTests().catch(console.error);
}
