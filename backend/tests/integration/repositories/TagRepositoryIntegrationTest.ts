/**
 * 标签仓库集成测试
 */

import { RepositoryIntegrationTestBase } from './RepositoryIntegrationTestBase';
import { TagRepository } from '../../../domain/repositories/TagRepository';
import { TagRepositoryImpl } from '../../../infrastructure/repositories/TagRepositoryImpl';
import { Tag } from '../../../domain/entities/Tag';
import { TestDataGenerator } from './TestDataGenerator';

/**
 * 标签仓库集成测试类
 */
export class TagRepositoryIntegrationTest extends RepositoryIntegrationTestBase {
  /**
   * 标签仓库
   */
  private tagRepository: TagRepository;

  /**
   * 测试数据生成器
   */
  private testDataGenerator: TestDataGenerator;

  /**
   * 测试标签数据
   */
  private testTags: any[];

  /**
   * 构造函数
   */
  constructor() {
    super();
    this.tagRepository = new TagRepositoryImpl(this.unitOfWork, this.eventPublisher);
    this.testDataGenerator = new TestDataGenerator(this.sequelize);
    this.testTags = [];
  }

  /**
   * 初始化测试数据
   */
  protected async initTestData(): Promise<void> {
    // 生成测试标签数据
    this.testTags = await this.testDataGenerator.generateTags(5);
  }

  /**
   * 测试根据ID查找标签
   */
  async testFindById(): Promise<void> {
    // 获取测试标签
    const testTag = this.testTags[0];

    // 查找标签
    const tag = await this.tagRepository.findById(testTag.id);

    // 断言
    expect(tag).not.toBeNull();
    expect(tag.id).toBe(testTag.id);
    expect(tag.name).toBe(testTag.name);
    expect(tag.description).toBe(testTag.description);
  }

  /**
   * 测试查找所有标签
   */
  async testFindAll(): Promise<void> {
    // 查找所有标签
    const tags = await this.tagRepository.findAll();

    // 断言
    expect(tags).not.toBeNull();
    expect(tags.length).toBe(this.testTags.length);
  }

  /**
   * 测试创建标签
   */
  async testCreate(): Promise<void> {
    // 开始事务
    await this.beginTransaction();

    try {
      // 创建标签
      const tag = new Tag(null, 'New Tag', 'New Tag Description');
      const createdTag = await this.tagRepository.save(tag);

      // 断言
      expect(createdTag).not.toBeNull();
      expect(createdTag.id).not.toBeNull();
      expect(createdTag.name).toBe('New Tag');
      expect(createdTag.description).toBe('New Tag Description');

      // 查找创建的标签
      const foundTag = await this.tagRepository.findById(createdTag.id);

      // 断言
      expect(foundTag).not.toBeNull();
      expect(foundTag.id).toBe(createdTag.id);
      expect(foundTag.name).toBe(createdTag.name);
      expect(foundTag.description).toBe(createdTag.description);

      // 提交事务
      await this.commitTransaction();
    } catch (error) {
      // 回滚事务
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * 测试更新标签
   */
  async testUpdate(): Promise<void> {
    // 开始事务
    await this.beginTransaction();

    try {
      // 获取测试标签
      const testTag = this.testTags[0];

      // 查找标签
      const tag = await this.tagRepository.findById(testTag.id);

      // 更新标签
      tag.name = 'Updated Tag';
      tag.description = 'Updated Tag Description';
      const updatedTag = await this.tagRepository.save(tag);

      // 断言
      expect(updatedTag).not.toBeNull();
      expect(updatedTag.id).toBe(testTag.id);
      expect(updatedTag.name).toBe('Updated Tag');
      expect(updatedTag.description).toBe('Updated Tag Description');

      // 查找更新的标签
      const foundTag = await this.tagRepository.findById(testTag.id);

      // 断言
      expect(foundTag).not.toBeNull();
      expect(foundTag.id).toBe(testTag.id);
      expect(foundTag.name).toBe('Updated Tag');
      expect(foundTag.description).toBe('Updated Tag Description');

      // 提交事务
      await this.commitTransaction();
    } catch (error) {
      // 回滚事务
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * 测试删除标签
   */
  async testDelete(): Promise<void> {
    // 开始事务
    await this.beginTransaction();

    try {
      // 获取测试标签
      const testTag = this.testTags[0];

      // 查找标签
      const tag = await this.tagRepository.findById(testTag.id);

      // 删除标签
      await this.tagRepository.delete(tag);

      // 查找删除的标签
      const foundTag = await this.tagRepository.findById(testTag.id);

      // 断言
      expect(foundTag).toBeNull();

      // 提交事务
      await this.commitTransaction();
    } catch (error) {
      // 回滚事务
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * 测试根据名称查找标签
   */
  async testFindByName(): Promise<void> {
    // 获取测试标签
    const testTag = this.testTags[0];

    // 查找标签
    const tag = await this.tagRepository.findByName(testTag.name);

    // 断言
    expect(tag).not.toBeNull();
    expect(tag.id).toBe(testTag.id);
    expect(tag.name).toBe(testTag.name);
    expect(tag.description).toBe(testTag.description);
  }

  /**
   * 运行所有测试
   */
  async runTests(): Promise<void> {
    try {
      // 设置测试
      await this.setUp();

      // 运行测试
      await this.testFindById();
      await this.testFindAll();
      await this.testCreate();
      await this.testUpdate();
      await this.testDelete();
      await this.testFindByName();

      console.log('所有测试通过');
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 清理测试
      await this.tearDown();
    }
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const test = new TagRepositoryIntegrationTest();
  test.runTests().catch(console.error);
}
