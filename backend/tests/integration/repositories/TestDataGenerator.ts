/**
 * 测试数据生成器
 * 用于生成测试数据
 *
 * 升级4.0增强版本，添加了更多数据生成方法和关联数据生成功能
 */

import { Sequelize, Model, ModelCtor } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import { faker } from '@faker-js/faker/locale/zh_CN';
import { Logger } from '../../../infrastructure/logging/Logger';
import { ConsoleLogger } from '../../../infrastructure/logging/ConsoleLogger';

/**
 * 数据生成选项
 */
interface GenerationOptions {
  count?: number;
  isRandom?: boolean;
  prefix?: string;
  isDeleted?: boolean;
}

/**
 * 测试数据生成器类
 * 用于生成测试数据
 */
export class TestDataGenerator {
  /**
   * 日志记录器
   */
  private readonly logger: Logger;

  /**
   * 构造函数
   * @param sequelize 数据库连接
   */
  constructor(private readonly sequelize: Sequelize) {
    this.logger = new ConsoleLogger();
    this.logger.info('初始化测试数据生成器');
  }

  /**
   * 生成用户数据
   * @param options 生成选项
   * @returns 用户数据
   */
  async generateUsers(options: GenerationOptions = {}): Promise<any[]> {
    const { count = 5, isRandom = true, prefix = 'user', isDeleted = false } = options;
    this.logger.debug(`生成${count}个用户数据`);

    const users = [];

    for (let i = 0; i < count; i++) {
      const user = {
        username: isRandom ?
          faker.internet.userName() :
          `${prefix}_${i}_${uuidv4().substring(0, 8)}`,
        email: isRandom ?
          faker.internet.email() :
          `${prefix}_${i}_${uuidv4().substring(0, 8)}@example.com`,
        password: isRandom ?
          faker.internet.password() :
          `password_${i}`,
        display_name: isRandom ?
          faker.person.fullName() :
          `测试用户${i}`,
        is_admin: i === 0, // 第一个用户为管理员
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      users.push(user);
    }

    const createdUsers = await this.sequelize.models.User.bulkCreate(users);
    this.logger.debug(`成功创建${createdUsers.length}个用户`);
    return createdUsers;
  }

  /**
   * 生成用户角色数据
   * @param options 生成选项
   * @returns 角色数据
   */
  async generateRoles(options: GenerationOptions = {}): Promise<any[]> {
    const { count = 3, isRandom = false, prefix = 'role', isDeleted = false } = options;
    this.logger.debug(`生成${count}个角色数据`);

    const roleNames = ['admin', 'teacher', 'student', 'guest', 'moderator'];
    const roles = [];

    for (let i = 0; i < count; i++) {
      const role = {
        name: isRandom ?
          faker.helpers.arrayElement(roleNames) :
          i < roleNames.length ? roleNames[i] : `${prefix}_${i}`,
        description: isRandom ?
          faker.lorem.sentence() :
          `角色${i}描述`,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      roles.push(role);
    }

    const createdRoles = await this.sequelize.models.Role.bulkCreate(roles);
    this.logger.debug(`成功创建${createdRoles.length}个角色`);
    return createdRoles;
  }

  /**
   * 为用户分配角色
   * @param users 用户列表
   * @param roles 角色列表
   * @returns 用户角色关联数据
   */
  async assignRolesToUsers(users: any[], roles: any[]): Promise<any[]> {
    this.logger.debug(`为${users.length}个用户分配${roles.length}个角色`);

    const userRoles = [];

    // 第一个用户分配所有角色
    if (users.length > 0 && roles.length > 0) {
      for (const role of roles) {
        userRoles.push({
          user_id: users[0].id,
          role_id: role.id,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    // 其他用户随机分配1-2个角色
    for (let i = 1; i < users.length; i++) {
      const roleCount = Math.floor(Math.random() * 2) + 1;
      const selectedRoles = faker.helpers.arrayElements(roles, roleCount);

      for (const role of selectedRoles) {
        userRoles.push({
          user_id: users[i].id,
          role_id: role.id,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    const createdUserRoles = await this.sequelize.models.UserRole.bulkCreate(userRoles);
    this.logger.debug(`成功创建${createdUserRoles.length}个用户角色关联`);
    return createdUserRoles;
  }

  /**
   * 生成标签数据
   * @param options 生成选项
   * @returns 标签数据
   */
  async generateTags(options: GenerationOptions = {}): Promise<any[]> {
    const { count = 10, isRandom = true, prefix = 'tag', isDeleted = false } = options;
    this.logger.debug(`生成${count}个标签数据`);

    const tags = [];

    // 预定义的常用标签
    const commonTags = [
      { name: '学习', description: '与学习相关的内容' },
      { name: '技术', description: '技术相关的内容' },
      { name: '编程', description: '编程相关的内容' },
      { name: '语言', description: '语言学习相关的内容' },
      { name: '数学', description: '数学相关的内容' },
      { name: '物理', description: '物理相关的内容' },
      { name: '化学', description: '化学相关的内容' },
      { name: '生物', description: '生物相关的内容' },
      { name: '历史', description: '历史相关的内容' },
      { name: '地理', description: '地理相关的内容' },
      { name: '政治', description: '政治相关的内容' },
      { name: '经济', description: '经济相关的内容' },
      { name: '文化', description: '文化相关的内容' },
      { name: '艺术', description: '艺术相关的内容' },
      { name: '音乐', description: '音乐相关的内容' }
    ];

    for (let i = 0; i < count; i++) {
      let name, description;

      if (isRandom) {
        if (i < commonTags.length) {
          // 使用预定义的常用标签
          name = commonTags[i].name;
          description = commonTags[i].description;
        } else {
          // 生成随机标签
          name = faker.word.noun();
          description = faker.lorem.sentence();
        }
      } else {
        // 生成有规律的标签
        name = `${prefix}_${i}`;
        description = `标签${i}的描述`;
      }

      const tag = {
        name,
        description,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      tags.push(tag);
    }

    const createdTags = await this.sequelize.models.Tag.bulkCreate(tags);
    this.logger.debug(`成功创建${createdTags.length}个标签`);
    return createdTags;
  }

  /**
   * 生成练习数据
   * @param options 生成选项
   * @param userId 用户ID
   * @returns 练习数据
   */
  async generateExercises(options: GenerationOptions = {}, userId?: number): Promise<any[]> {
    const { count = 5, isRandom = true, prefix = 'exercise', isDeleted = false } = options;
    this.logger.debug(`生成${count}个练习数据`);

    const exercises = [];

    // 如果没有提供用户ID，创建一个测试用户
    if (!userId) {
      const users = await this.generateUsers({ count: 1 });
      userId = users[0].id;
    }

    // 预定义的练习类型
    const exerciseTypes = [
      '多选题', '单选题', '填空题', '判断题', '简答题', '论述题', '编程题', '实验题'
    ];

    // 预定义的难度级别
    const difficultyLevels = ['EASY', 'MEDIUM', 'HARD', 'EXPERT'];

    for (let i = 0; i < count; i++) {
      let title, description, content, difficulty;
      const isPublic = i % 2 === 0; // 偶数为公开，奇数为私有

      if (isRandom) {
        // 生成随机练习数据
        const exerciseType = faker.helpers.arrayElement(exerciseTypes);
        title = `${exerciseType}: ${faker.lorem.sentence(3).slice(0, -1)}`;
        description = faker.lorem.paragraph();
        content = faker.lorem.paragraphs(3);
        difficulty = faker.helpers.arrayElement(difficultyLevels);
      } else {
        // 生成有规律的练习数据
        title = `${prefix}_${i}_${uuidv4().substring(0, 8)}`;
        description = `练习${i}的描述`;
        content = `练习${i}的内容`;
        difficulty = difficultyLevels[i % difficultyLevels.length];
      }

      const exercise = {
        title,
        description,
        content,
        difficulty,
        creator_id: userId,
        is_public: isPublic,
        time_estimate_minutes: isRandom ? faker.number.int({ min: 5, max: 60 }) : (i + 1) * 10,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      exercises.push(exercise);
    }

    const createdExercises = await this.sequelize.models.Exercise.bulkCreate(exercises);
    this.logger.debug(`成功创建${createdExercises.length}个练习`);
    return createdExercises;
  }

  /**
   * 为练习分配标签
   * @param exercises 练习列表
   * @param tags 标签列表
   * @returns 练习标签关联数据
   */
  async assignTagsToExercises(exercises: any[], tags: any[]): Promise<any[]> {
    this.logger.debug(`为${exercises.length}个练习分配标签`);

    const exerciseTags = [];

    for (const exercise of exercises) {
      // 每个练习随机分配1-3个标签
      const tagCount = Math.floor(Math.random() * 3) + 1;
      const selectedTags = faker.helpers.arrayElements(tags, tagCount);

      for (const tag of selectedTags) {
        exerciseTags.push({
          exercise_id: exercise.id,
          tag_id: tag.id,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    const createdExerciseTags = await this.sequelize.models.ExerciseTag.bulkCreate(exerciseTags);
    this.logger.debug(`成功创建${createdExerciseTags.length}个练习标签关联`);
    return createdExerciseTags;
  }

  /**
   * 生成笔记数据
   * @param options 生成选项
   * @param userId 用户ID
   * @returns 笔记数据
   */
  async generateNotes(options: GenerationOptions = {}, userId?: number): Promise<any[]> {
    const { count = 5, isRandom = true, prefix = 'note', isDeleted = false } = options;
    this.logger.debug(`生成${count}个笔记数据`);

    const notes = [];

    // 如果没有提供用户ID，创建一个测试用户
    if (!userId) {
      const users = await this.generateUsers({ count: 1 });
      userId = users[0].id;
    }

    // 预定义的笔记类型
    const noteTypes = [
      '学习笔记', '读书笔记', '课堂笔记', '研究笔记', '会议笔记', '工作笔记', '思考笔记', '总结笔记'
    ];

    for (let i = 0; i < count; i++) {
      let title, content;
      const isPublic = i % 2 === 0; // 偶数为公开，奇数为私有

      if (isRandom) {
        // 生成随机笔记数据
        const noteType = faker.helpers.arrayElement(noteTypes);
        title = `${noteType}: ${faker.lorem.sentence(3).slice(0, -1)}`;
        content = faker.lorem.paragraphs(5);
      } else {
        // 生成有规律的笔记数据
        title = `${prefix}_${i}_${uuidv4().substring(0, 8)}`;
        content = `笔记${i}的内容`;
      }

      const note = {
        title,
        content,
        user_id: userId,
        is_public: isPublic,
        view_count: isRandom ? faker.number.int({ min: 0, max: 1000 }) : i * 10,
        like_count: isRandom ? faker.number.int({ min: 0, max: 100 }) : i * 5,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      notes.push(note);
    }

    const createdNotes = await this.sequelize.models.Note.bulkCreate(notes);
    this.logger.debug(`成功创建${createdNotes.length}个笔记`);
    return createdNotes;
  }

  /**
   * 为笔记分配标签
   * @param notes 笔记列表
   * @param tags 标签列表
   * @returns 笔记标签关联数据
   */
  async assignTagsToNotes(notes: any[], tags: any[]): Promise<any[]> {
    this.logger.debug(`为${notes.length}个笔记分配标签`);

    const noteTags = [];

    for (const note of notes) {
      // 每个笔记随机分配1-5个标签
      const tagCount = Math.floor(Math.random() * 5) + 1;
      const selectedTags = faker.helpers.arrayElements(tags, tagCount);

      for (const tag of selectedTags) {
        noteTags.push({
          note_id: note.id,
          tag_id: tag.id,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    const createdNoteTags = await this.sequelize.models.NoteTag.bulkCreate(noteTags);
    this.logger.debug(`成功创建${createdNoteTags.length}个笔记标签关联`);
    return createdNoteTags;
  }

  /**
   * 生成学习计划数据
   * @param options 生成选项
   * @param userId 用户ID
   * @returns 学习计划数据
   */
  async generateLearningPlans(options: GenerationOptions = {}, userId?: number): Promise<any[]> {
    const { count = 3, isRandom = true, prefix = 'plan', isDeleted = false } = options;
    this.logger.debug(`生成${count}个学习计划数据`);

    const learningPlans = [];

    // 如果没有提供用户ID，创建一个测试用户
    if (!userId) {
      const users = await this.generateUsers({ count: 1 });
      userId = users[0].id;
    }

    // 预定义的学习计划类型
    const planTypes = [
      '编程学习计划', '语言学习计划', '数学学习计划', '物理学习计划', '化学学习计划', '生物学习计划'
    ];

    for (let i = 0; i < count; i++) {
      let title, description;
      const isPublic = i % 2 === 0; // 偶数为公开，奇数为私有

      if (isRandom) {
        // 生成随机学习计划数据
        const planType = faker.helpers.arrayElement(planTypes);
        title = `${planType}: ${faker.lorem.sentence(3).slice(0, -1)}`;
        description = faker.lorem.paragraph();
      } else {
        // 生成有规律的学习计划数据
        title = `${prefix}_${i}_${uuidv4().substring(0, 8)}`;
        description = `学习计划${i}的描述`;
      }

      const learningPlan = {
        title,
        description,
        user_id: userId,
        is_public: isPublic,
        duration_days: isRandom ? faker.number.int({ min: 7, max: 90 }) : (i + 1) * 7,
        daily_goal_minutes: isRandom ? faker.number.int({ min: 15, max: 120 }) : 30,
        completion_percentage: isRandom ? faker.number.int({ min: 0, max: 100 }) : 0,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      learningPlans.push(learningPlan);
    }

    const createdLearningPlans = await this.sequelize.models.LearningPlan.bulkCreate(learningPlans);
    this.logger.debug(`成功创建${createdLearningPlans.length}个学习计划`);
    return createdLearningPlans;
  }

  /**
   * 为学习计划分配练习
   * @param learningPlans 学习计划列表
   * @param exercises 练习列表
   * @returns 学习计划练习关联数据
   */
  async assignExercisesToLearningPlans(learningPlans: any[], exercises: any[]): Promise<any[]> {
    this.logger.debug(`为${learningPlans.length}个学习计划分配练习`);

    const learningPlanExercises = [];

    for (const learningPlan of learningPlans) {
      // 每个学习计划随机分配3-10个练习
      const exerciseCount = Math.floor(Math.random() * 8) + 3;
      const selectedExercises = faker.helpers.arrayElements(exercises, exerciseCount);

      for (let i = 0; i < selectedExercises.length; i++) {
        const exercise = selectedExercises[i];
        learningPlanExercises.push({
          learning_plan_id: learningPlan.id,
          exercise_id: exercise.id,
          order: i + 1, // 按顺序排列
          is_completed: Math.random() < 0.3, // 30%的概率已完成
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    const createdLearningPlanExercises = await this.sequelize.models.LearningPlanExercise.bulkCreate(learningPlanExercises);
    this.logger.debug(`成功创建${createdLearningPlanExercises.length}个学习计划练习关联`);
    return createdLearningPlanExercises;
  }

  /**
   * 生成成就数据
   * @param options 生成选项
   * @returns 成就数据
   */
  async generateAchievements(options: GenerationOptions = {}): Promise<any[]> {
    const { count = 5, isRandom = true, prefix = 'achievement', isDeleted = false } = options;
    this.logger.debug(`生成${count}个成就数据`);

    const achievements = [];

    // 预定义的成就类型
    const achievementTypes = [
      { name: '学习大师', description: '完成100个练习', icon: 'master.png', points: 100 },
      { name: '知识探索者', description: '连续学习万30天', icon: 'explorer.png', points: 50 },
      { name: '勤奋学习者', description: '完成第一个学习计划', icon: 'diligent.png', points: 30 },
      { name: '笔记达人', description: '创建10篇笔记', icon: 'note_master.png', points: 40 },
      { name: '社区贡献者', description: '分享5篇公开笔记', icon: 'contributor.png', points: 60 },
      { name: '学习新手', description: '完成第一个练习', icon: 'beginner.png', points: 10 },
      { name: '持之以恒', description: '连续登录10天', icon: 'persistent.png', points: 20 },
      { name: '精通多门', description: '在三个不同类别完成练习', icon: 'versatile.png', points: 45 },
      { name: '高效学习者', description: '在一天内完成至少5个练习', icon: 'efficient.png', points: 35 },
      { name: '学习达人', description: '获得总分500分', icon: 'expert.png', points: 150 }
    ];

    for (let i = 0; i < count; i++) {
      let name, description, iconUrl, points;

      if (isRandom) {
        if (i < achievementTypes.length) {
          // 使用预定义的成就类型
          name = achievementTypes[i].name;
          description = achievementTypes[i].description;
          iconUrl = `/assets/images/achievements/${achievementTypes[i].icon}`;
          points = achievementTypes[i].points;
        } else {
          // 生成随机成就数据
          name = faker.word.adjective() + '的' + faker.word.noun();
          description = faker.lorem.sentence();
          iconUrl = `/assets/images/achievements/random_${i}.png`;
          points = faker.number.int({ min: 10, max: 200 });
        }
      } else {
        // 生成有规律的成就数据
        name = `${prefix}_${i}_${uuidv4().substring(0, 8)}`;
        description = `成就${i}的描述`;
        iconUrl = `/assets/images/achievements/${prefix}_${i}.png`;
        points = i * 10 + 10;
      }

      const achievement = {
        name,
        description,
        icon_url: iconUrl,
        points,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      achievements.push(achievement);
    }

    const createdAchievements = await this.sequelize.models.Achievement.bulkCreate(achievements);
    this.logger.debug(`成功创建${createdAchievements.length}个成就`);
    return createdAchievements;
  }

  /**
   * 为用户分配成就
   * @param users 用户列表
   * @param achievements 成就列表
   * @returns 用户成就关联数据
   */
  async assignAchievementsToUsers(users: any[], achievements: any[]): Promise<any[]> {
    this.logger.debug(`为${users.length}个用户分配成就`);

    const userAchievements = [];

    for (const user of users) {
      // 每个用户随机分配0-5个成就
      const achievementCount = Math.floor(Math.random() * 6);
      const selectedAchievements = faker.helpers.arrayElements(achievements, achievementCount);

      for (const achievement of selectedAchievements) {
        userAchievements.push({
          user_id: user.id,
          achievement_id: achievement.id,
          unlocked_at: faker.date.past(),
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    const createdUserAchievements = await this.sequelize.models.UserAchievement.bulkCreate(userAchievements);
    this.logger.debug(`成功创建${createdUserAchievements.length}个用户成就关联`);
    return createdUserAchievements;
  }

  /**
   * 生成徽章数据
   * @param options 生成选项
   * @returns 徽章数据
   */
  async generateBadges(options: GenerationOptions = {}): Promise<any[]> {
    const { count = 5, isRandom = true, prefix = 'badge', isDeleted = false } = options;
    this.logger.debug(`生成${count}个徽章数据`);

    const badges = [];

    // 预定义的徽章类型
    const badgeTypes = [
      { name: '编程大师', description: '完成所有编程练习', icon: 'programming.png', rarity: 'legendary' },
      { name: '数学天才', description: '完成所有数学练习', icon: 'math.png', rarity: 'epic' },
      { name: '语言大师', description: '完成所有语言练习', icon: 'language.png', rarity: 'rare' },
      { name: '科学家', description: '完成所有科学练习', icon: 'science.png', rarity: 'epic' },
      { name: '艺术家', description: '完成所有艺术练习', icon: 'art.png', rarity: 'uncommon' },
      { name: '全能选手', description: '完成所有类型的练习', icon: 'versatile.png', rarity: 'legendary' },
      { name: '初学者', description: '完成第一个练习', icon: 'beginner.png', rarity: 'common' },
      { name: '勤奋者', description: '连续学习万30天', icon: 'diligent.png', rarity: 'rare' },
      { name: '社区之星', description: '获得100个点赞', icon: 'community_star.png', rarity: 'epic' },
      { name: '知识分享者', description: '发布10篇公开笔记', icon: 'sharer.png', rarity: 'uncommon' }
    ];

    // 稀有度级别
    const rarities = ['common', 'uncommon', 'rare', 'epic', 'legendary'];

    for (let i = 0; i < count; i++) {
      let name, description, iconUrl, rarity;
      const isEquippable = i % 2 === 0; // 偶数可装备，奇数不可装备

      if (isRandom) {
        if (i < badgeTypes.length) {
          // 使用预定义的徽章类型
          name = badgeTypes[i].name;
          description = badgeTypes[i].description;
          iconUrl = `/assets/images/badges/${badgeTypes[i].icon}`;
          rarity = badgeTypes[i].rarity;
        } else {
          // 生成随机徽章数据
          name = faker.word.adjective() + '的' + faker.word.noun();
          description = faker.lorem.sentence();
          iconUrl = `/assets/images/badges/random_${i}.png`;
          rarity = faker.helpers.arrayElement(rarities);
        }
      } else {
        // 生成有规律的徽章数据
        name = `${prefix}_${i}_${uuidv4().substring(0, 8)}`;
        description = `徽章${i}的描述`;
        iconUrl = `/assets/images/badges/${prefix}_${i}.png`;
        rarity = rarities[i % rarities.length];
      }

      const badge = {
        name,
        description,
        icon_url: iconUrl,
        rarity,
        is_equippable: isEquippable,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      badges.push(badge);
    }

    const createdBadges = await this.sequelize.models.Badge.bulkCreate(badges);
    this.logger.debug(`成功创建${createdBadges.length}个徽章`);
    return createdBadges;
  }

  /**
   * 为用户分配徽章
   * @param users 用户列表
   * @param badges 徽章列表
   * @returns 用户徽章关联数据
   */
  async assignBadgesToUsers(users: any[], badges: any[]): Promise<any[]> {
    this.logger.debug(`为${users.length}个用户分配徽章`);

    const userBadges = [];

    for (const user of users) {
      // 每个用户随机分配0-3个徽章
      const badgeCount = Math.floor(Math.random() * 4);
      const selectedBadges = faker.helpers.arrayElements(badges, badgeCount);

      for (const badge of selectedBadges) {
        userBadges.push({
          user_id: user.id,
          badge_id: badge.id,
          awarded_at: faker.date.past(),
          is_equipped: Math.random() < 0.5 && badge.is_equippable, // 50%的概率装备（如果可装备）
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    const createdUserBadges = await this.sequelize.models.UserBadge.bulkCreate(userBadges);
    this.logger.debug(`成功创建${createdUserBadges.length}个用户徽章关联`);
    return createdUserBadges;
  }

  /**
   * 生成主题数据
   * @param options 生成选项
   * @returns 主题数据
   */
  async generateThemes(options: GenerationOptions = {}): Promise<any[]> {
    const { count = 3, isRandom = true, prefix = 'theme', isDeleted = false } = options;
    this.logger.debug(`生成${count}个主题数据`);

    const themes = [];

    // 预定义的主题类型
    const themeTypes = [
      { name: '深色模式', description: '暗色主题，适合夜间使用', icon: 'dark.png', css_class: 'theme-dark' },
      { name: '浅色模式', description: '亮色主题，适合白天使用', icon: 'light.png', css_class: 'theme-light' },
      { name: '蓝色模式', description: '蓝色主题，舒缓眼睛', icon: 'blue.png', css_class: 'theme-blue' },
      { name: '绿色模式', description: '绿色主题，自然风格', icon: 'green.png', css_class: 'theme-green' },
      { name: '紫色模式', description: '紫色主题，高贵风格', icon: 'purple.png', css_class: 'theme-purple' },
      { name: '橙色模式', description: '橙色主题，活力风格', icon: 'orange.png', css_class: 'theme-orange' }
    ];

    for (let i = 0; i < count; i++) {
      let name, description, iconUrl, cssClass;

      if (isRandom) {
        if (i < themeTypes.length) {
          // 使用预定义的主题类型
          name = themeTypes[i].name;
          description = themeTypes[i].description;
          iconUrl = `/assets/images/themes/${themeTypes[i].icon}`;
          cssClass = themeTypes[i].css_class;
        } else {
          // 生成随机主题数据
          name = faker.color.human() + '主题';
          description = faker.lorem.sentence();
          iconUrl = `/assets/images/themes/random_${i}.png`;
          cssClass = `theme-custom-${i}`;
        }
      } else {
        // 生成有规律的主题数据
        name = `${prefix}_${i}_${uuidv4().substring(0, 8)}`;
        description = `主题${i}的描述`;
        iconUrl = `/assets/images/themes/${prefix}_${i}.png`;
        cssClass = `theme-${prefix}-${i}`;
      }

      const theme = {
        name,
        description,
        icon_url: iconUrl,
        css_class: cssClass,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: isDeleted
      };

      themes.push(theme);
    }

    const createdThemes = await this.sequelize.models.Theme.bulkCreate(themes);
    this.logger.debug(`成功创建${createdThemes.length}个主题`);
    return createdThemes;
  }

  /**
   * 为用户分配主题
   * @param users 用户列表
   * @param themes 主题列表
   * @returns 用户主题关联数据
   */
  async assignThemesToUsers(users: any[], themes: any[]): Promise<any[]> {
    this.logger.debug(`为${users.length}个用户分配主题`);

    const userThemes = [];

    for (const user of users) {
      // 每个用户随机分配1-2个主题
      const themeCount = Math.floor(Math.random() * 2) + 1;
      const selectedThemes = faker.helpers.arrayElements(themes, themeCount);

      for (const theme of selectedThemes) {
        userThemes.push({
          user_id: user.id,
          theme_id: theme.id,
          is_active: userThemes.filter(ut => ut.user_id === user.id).length === 0, // 第一个主题为激活状态
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    const createdUserThemes = await this.sequelize.models.UserTheme.bulkCreate(userThemes);
    this.logger.debug(`成功创建${createdUserThemes.length}个用户主题关联`);
    return createdUserThemes;
  }
}
