/**
 * 仓库集成测试运行器
 * 用于运行所有仓库集成测试
 */

import { TagRepositoryIntegrationTest } from './TagRepositoryIntegrationTest';
import { ExerciseRepositoryIntegrationTest } from './ExerciseRepositoryIntegrationTest';
import { UserRepositoryIntegrationTest } from './UserRepositoryIntegrationTest';
import { NoteRepositoryIntegrationTest } from './NoteRepositoryIntegrationTest';
import { AchievementRepositoryIntegrationTest } from './AchievementRepositoryIntegrationTest';
import { LearningTemplateRepositoryIntegrationTest } from './LearningTemplateRepositoryIntegrationTest';

/**
 * 运行所有仓库集成测试
 */
async function runAllTests() {
  console.log('开始运行仓库集成测试...');

  try {
    // 运行标签仓库集成测试
    console.log('\n=== 运行标签仓库集成测试 ===');
    const tagTest = new TagRepositoryIntegrationTest();
    await tagTest.runTests();

    // 运行练习仓库集成测试
    console.log('\n=== 运行练习仓库集成测试 ===');
    const exerciseTest = new ExerciseRepositoryIntegrationTest();
    await exerciseTest.runTests();

    // 运行用户仓库集成测试
    console.log('\n=== 运行用户仓库集成测试 ===');
    const userTest = new UserRepositoryIntegrationTest();
    await userTest.runTests();

    // 运行笔记仓库集成测试
    console.log('\n=== 运行笔记仓库集成测试 ===');
    const noteTest = new NoteRepositoryIntegrationTest();
    await noteTest.runTests();

    // 运行成就仓库集成测试
    console.log('\n=== 运行成就仓库集成测试 ===');
    const achievementTest = new AchievementRepositoryIntegrationTest();
    await achievementTest.runTests();

    // 运行学习模板仓库集成测试
    console.log('\n=== 运行学习模板仓库集成测试 ===');
    const learningTemplateTest = new LearningTemplateRepositoryIntegrationTest();
    await learningTemplateTest.runTests();

    // 添加更多仓库集成测试...

    console.log('\n所有仓库集成测试通过');
  } catch (error) {
    console.error('\n仓库集成测试失败:', error);
    process.exit(1);
  }
}

// 运行所有测试
runAllTests().catch(console.error);
