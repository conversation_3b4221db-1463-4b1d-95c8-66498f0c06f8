import { Sequelize } from 'sequelize';
import { SequelizePermissionRepository } from '../../../../infrastructure/persistence/repositories/user/SequelizePermissionRepository';
import { UnitOfWork } from '../../../../infrastructure/persistence/UnitOfWork';
import { EventPublisher } from '../../../../infrastructure/events/EventPublisher';
import { Permission } from '../../../../domain/models/user/Permission';

// 模拟依赖
class MockUnitOfWork implements UnitOfWork {
  private transaction: any = null;

  begin(): Promise<void> {
    this.transaction = {};
    return Promise.resolve();
  }

  commit(): Promise<void> {
    this.transaction = null;
    return Promise.resolve();
  }

  rollback(): Promise<void> {
    this.transaction = null;
    return Promise.resolve();
  }

  getTransaction(): any {
    return this.transaction;
  }
}

class MockEventPublisher implements EventPublisher {
  publishedEvents: any[] = [];

  publish(event: any): Promise<void> {
    this.publishedEvents.push(event);
    return Promise.resolve();
  }

  clear(): void {
    this.publishedEvents = [];
  }
}

describe('SequelizePermissionRepository', () => {
  let permissionRepository: SequelizePermissionRepository;
  let unitOfWork: MockUnitOfWork;
  let eventPublisher: MockEventPublisher;
  let sequelize: any;
  let permissionModel: any;

  beforeEach(() => {
    // 模拟Sequelize和模型
    sequelize = {
      transaction: jest.fn().mockResolvedValue({}),
      define: jest.fn(),
      model: jest.fn()
    };

    permissionModel = {
      findByPk: jest.fn(),
      findOne: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      destroy: jest.fn()
    };

    // 创建依赖
    unitOfWork = new MockUnitOfWork();
    eventPublisher = new MockEventPublisher();

    // 创建仓库
    permissionRepository = new SequelizePermissionRepository(
      unitOfWork,
      eventPublisher,
      sequelize,
      permissionModel
    );
  });

  describe('findById', () => {
    it('should return null when permission not found', async () => {
      // 设置模拟返回值
      permissionModel.findByPk.mockResolvedValue(null);

      // 执行测试
      const result = await permissionRepository.findById(1);

      // 验证结果
      expect(result).toBeNull();
      expect(permissionModel.findByPk).toHaveBeenCalledWith(1);
    });

    it('should return permission when found', async () => {
      // 设置模拟返回值
      const mockPermissionData = {
        id: 1,
        name: 'create_user',
        description: '创建用户',
        resource_type: 'user',
        action: 'create',
        created_at: new Date(),
        updated_at: new Date()
      };

      permissionModel.findByPk.mockResolvedValue(mockPermissionData);

      // 执行测试
      const result = await permissionRepository.findById(1);

      // 验证结果
      expect(result).not.toBeNull();
      expect(result).toBeInstanceOf(Permission);
      expect(result.id).toBe(1);
      expect(result.name).toBe('create_user');
      expect(result.description).toBe('创建用户');
      expect(result.resourceType).toBe('user');
      expect(result.action).toBe('create');
    });
  });

  describe('findByName', () => {
    it('should return null when permission not found', async () => {
      // 设置模拟返回值
      permissionModel.findOne.mockResolvedValue(null);

      // 执行测试
      const result = await permissionRepository.findByName('nonexistent');

      // 验证结果
      expect(result).toBeNull();
      expect(permissionModel.findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { name: 'nonexistent' }
        })
      );
    });

    it('should return permission when found by name', async () => {
      // 设置模拟返回值
      const mockPermissionData = {
        id: 1,
        name: 'create_user',
        description: '创建用户',
        resource_type: 'user',
        action: 'create',
        created_at: new Date(),
        updated_at: new Date()
      };

      permissionModel.findOne.mockResolvedValue(mockPermissionData);

      // 执行测试
      const result = await permissionRepository.findByName('create_user');

      // 验证结果
      expect(result).not.toBeNull();
      expect(result).toBeInstanceOf(Permission);
      expect(result.id).toBe(1);
      expect(result.name).toBe('create_user');
      expect(result.description).toBe('创建用户');
      expect(result.resourceType).toBe('user');
      expect(result.action).toBe('create');
    });
  });

  describe('findByResourceType', () => {
    it('should return empty array when no permissions found', async () => {
      // 设置模拟返回值
      permissionModel.findAll.mockResolvedValue([]);

      // 执行测试
      const result = await permissionRepository.findByResourceType('nonexistent');

      // 验证结果
      expect(result).toEqual([]);
      expect(permissionModel.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { resource_type: 'nonexistent' }
        })
      );
    });

    it('should return permissions when found by resource type', async () => {
      // 设置模拟返回值
      const mockPermissionsData = [
        {
          id: 1,
          name: 'create_user',
          description: '创建用户',
          resource_type: 'user',
          action: 'create',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: 2,
          name: 'delete_user',
          description: '删除用户',
          resource_type: 'user',
          action: 'delete',
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      permissionModel.findAll.mockResolvedValue(mockPermissionsData);

      // 执行测试
      const result = await permissionRepository.findByResourceType('user');

      // 验证结果
      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(Permission);
      expect(result[0].id).toBe(1);
      expect(result[0].name).toBe('create_user');
      expect(result[0].resourceType).toBe('user');
      expect(result[1]).toBeInstanceOf(Permission);
      expect(result[1].id).toBe(2);
      expect(result[1].name).toBe('delete_user');
      expect(result[1].resourceType).toBe('user');
    });
  });

  describe('findAll', () => {
    it('should return empty array when no permissions found', async () => {
      // 设置模拟返回值
      permissionModel.findAll.mockResolvedValue([]);

      // 执行测试
      const result = await permissionRepository.findAll();

      // 验证结果
      expect(result).toEqual([]);
      expect(permissionModel.findAll).toHaveBeenCalled();
    });

    it('should return all permissions', async () => {
      // 设置模拟返回值
      const mockPermissionsData = [
        {
          id: 1,
          name: 'create_user',
          description: '创建用户',
          resource_type: 'user',
          action: 'create',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: 2,
          name: 'delete_user',
          description: '删除用户',
          resource_type: 'user',
          action: 'delete',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: 3,
          name: 'view_note',
          description: '查看笔记',
          resource_type: 'note',
          action: 'view',
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      permissionModel.findAll.mockResolvedValue(mockPermissionsData);

      // 执行测试
      const result = await permissionRepository.findAll();

      // 验证结果
      expect(result).toHaveLength(3);
      expect(result[0]).toBeInstanceOf(Permission);
      expect(result[0].id).toBe(1);
      expect(result[0].name).toBe('create_user');
      expect(result[1]).toBeInstanceOf(Permission);
      expect(result[1].id).toBe(2);
      expect(result[1].name).toBe('delete_user');
      expect(result[2]).toBeInstanceOf(Permission);
      expect(result[2].id).toBe(3);
      expect(result[2].name).toBe('view_note');
      expect(result[2].resourceType).toBe('note');
    });
  });

  describe('save', () => {
    it('should create a new permission', async () => {
      // 创建测试权限
      const permission = new Permission(
        0,
        'edit_user',
        '编辑用户',
        'user',
        'edit'
      );

      // 设置模拟返回值
      permissionModel.create.mockResolvedValue({
        id: 4,
        name: 'edit_user',
        description: '编辑用户',
        resource_type: 'user',
        action: 'edit',
        created_at: new Date(),
        updated_at: new Date()
      });

      // 执行测试
      await unitOfWork.begin();
      const savedPermission = await permissionRepository.save(permission);
      await unitOfWork.commit();

      // 验证结果
      expect(savedPermission).toBeInstanceOf(Permission);
      expect(savedPermission.id).toBe(4);
      expect(savedPermission.name).toBe('edit_user');
      expect(savedPermission.description).toBe('编辑用户');
      expect(savedPermission.resourceType).toBe('user');
      expect(savedPermission.action).toBe('edit');

      // 验证调用
      expect(permissionModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'edit_user',
          description: '编辑用户',
          resource_type: 'user',
          action: 'edit',
          created_at: expect.any(Date),
          updated_at: expect.any(Date)
        }),
        expect.any(Object)
      );
    });

    it('should update an existing permission', async () => {
      // 创建测试权限
      const permission = new Permission(
        3,
        'view_note',
        '查看笔记（已更新）',
        'note',
        'read'
      );

      // 设置模拟返回值
      permissionModel.update.mockResolvedValue([1]);

      // 执行测试
      await unitOfWork.begin();
      const updatedPermission = await permissionRepository.save(permission);
      await unitOfWork.commit();

      // 验证结果
      expect(updatedPermission).toBeInstanceOf(Permission);
      expect(updatedPermission.id).toBe(3);
      expect(updatedPermission.name).toBe('view_note');
      expect(updatedPermission.description).toBe('查看笔记（已更新）');
      expect(updatedPermission.resourceType).toBe('note');
      expect(updatedPermission.action).toBe('read');

      // 验证调用
      expect(permissionModel.update).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'view_note',
          description: '查看笔记（已更新）',
          resource_type: 'note',
          action: 'read',
          updated_at: expect.any(Date)
        }),
        expect.objectContaining({
          where: { id: 3 }
        })
      );
    });
  });

  describe('delete', () => {
    it('should delete a permission', async () => {
      // 创建测试权限
      const permission = new Permission(
        2,
        'delete_user',
        '删除用户',
        'user',
        'delete'
      );

      // 设置模拟返回值
      permissionModel.destroy.mockResolvedValue(1);

      // 执行测试
      await unitOfWork.begin();
      await permissionRepository.delete(permission);
      await unitOfWork.commit();

      // 验证调用
      expect(permissionModel.destroy).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 2 }
        })
      );
    });
  });
});
