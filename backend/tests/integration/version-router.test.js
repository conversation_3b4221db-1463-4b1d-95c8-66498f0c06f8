/**
 * 版本路由集成测试
 */
const request = require('supertest');
const { expect } = require('chai');
const express = require('express');
const versionRouter = require('../../middlewares/version-router.middleware');
const compatibilityLayer = require('../../middlewares/compatibility-layer.middleware');

describe('版本路由集成测试', () => {
  let app;

  beforeEach(() => {
    app = express();

    // 注册版本路由中间件
    app.use(versionRouter({
      defaultVersion: 'v1',
      versions: ['v1', 'v2'],
      deprecatedVersions: ['v1']
    }));

    // 注册兼容层中间件
    app.use(compatibilityLayer({
      sourceVersion: 'v1',
      targetVersion: 'v2',
      enabled: true
    }));

    // 注册测试路由
    app.get('/api/v1/test', (req, res) => {
      res.json({ version: req.apiVersion, message: 'v1' });
    });

    app.get('/api/v2/test', (req, res) => {
      res.json({ version: req.apiVersion, message: 'v2' });
    });

    // 注册带参数的测试路由
    app.get('/api/v1/test/:id', (req, res) => {
      res.json({ version: req.apiVersion, id: req.params.id, message: 'v1' });
    });

    app.get('/api/v2/test/:id', (req, res) => {
      res.json({ version: req.apiVersion, id: req.params.id, message: 'v2' });
    });

    // 注册软删除测试路由
    app.delete('/api/v1/test/:id', (req, res) => {
      res.json({ version: req.apiVersion, id: req.params.id, message: 'deleted' });
    });

    app.delete('/api/v2/test/:id/soft-delete', (req, res) => {
      res.json({ version: req.apiVersion, id: req.params.id, message: 'soft-deleted', deletedAt: new Date().toISOString() });
    });
  });

  it('应该路由到V1版本API', async () => {
    const res = await request(app).get('/api/v1/test');

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({ version: 'v1', message: 'v1' });
    expect(res.headers['x-api-deprecated']).to.equal('true');
  });

  it('应该路由到V2版本API', async () => {
    const res = await request(app).get('/api/v2/test');

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({ version: 'v2', message: 'v2' });
    expect(res.headers['x-api-deprecated']).to.be.undefined;
  });

  it('应该处理带参数的路由', async () => {
    const res = await request(app).get('/api/v1/test/123');

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({ version: 'v1', id: '123', message: 'v1' });
  });

  it('应该将V1版本的DELETE请求转换为V2版本的软删除', async () => {
    const res = await request(app).delete('/api/v1/test/123');

    expect(res.status).to.equal(200);
    expect(res.body.version).to.equal('v1');
    expect(res.body.id).to.equal('123');
    expect(res.body.message).to.equal('soft-deleted');
    expect(res.body.deletedAt).to.be.undefined; // 兼容层应该移除deletedAt字段
  });

  it('应该支持从请求头中提取版本', async () => {
    app = express();

    // 注册版本路由中间件，使用请求头提取版本
    app.use(versionRouter({
      defaultVersion: 'v1',
      versions: ['v1', 'v2'],
      versionExtractor: 'header',
      headerName: 'accept-version'
    }));

    // 注册测试路由
    app.get('/api/test', (req, res) => {
      res.json({ version: req.apiVersion, message: `version ${req.apiVersion}` });
    });

    const res = await request(app)
      .get('/api/test')
      .set('Accept-Version', 'v2');

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({ version: 'v2', message: 'version v2' });
  });

  it('应该支持从查询参数中提取版本', async () => {
    app = express();

    // 注册版本路由中间件，使用查询参数提取版本
    app.use(versionRouter({
      defaultVersion: 'v1',
      versions: ['v1', 'v2'],
      versionExtractor: 'query',
      queryParam: 'version'
    }));

    // 注册测试路由
    app.get('/api/test', (req, res) => {
      res.json({ version: req.apiVersion, message: `version ${req.apiVersion}` });
    });

    const res = await request(app)
      .get('/api/test?version=2');

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({ version: 'v2', message: 'version v2' });
  });
});
