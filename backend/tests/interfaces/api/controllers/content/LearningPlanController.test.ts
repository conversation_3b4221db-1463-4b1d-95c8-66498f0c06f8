import { LearningPlanController } from '../../../../../interfaces/api/controllers/content/LearningPlanController';
import { LearningPlanApplicationService } from '../../../../../application/services/content/learningPlan/LearningPlanApplicationService';
import { Request, Response } from 'express';

// 模拟依赖
const mockLearningPlanApplicationService = {
  createLearningPlan: jest.fn(),
  updateLearningPlan: jest.fn(),
  deleteLearningPlan: jest.fn(),
  restoreLearningPlan: jest.fn(),
  startLearningPlan: jest.fn(),
  completeLearningPlan: jest.fn(),
  pauseLearningPlan: jest.fn(),
  abandonLearningPlan: jest.fn(),
  setAsCurrent: jest.fn(),
  updateProgress: jest.fn(),
  addLearningPlanTag: jest.fn(),
  removeLearningPlanTag: jest.fn(),
  getLearningPlan: jest.fn(),
  searchLearningPlans: jest.fn(),
  getCurrentPlan: jest.fn(),
  getDeletedPlans: jest.fn()
};

// 模拟请求和响应
const mockRequest = () => {
  const req: Partial<Request> = {
    body: {},
    params: {},
    query: {},
    user: { id: 'user1', role: 'user' }
  };
  return req as Request;
};

const mockResponse = () => {
  const res: Partial<Response> = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis()
  };
  return res as Response;
};

const mockNext = jest.fn();

describe('LearningPlanController', () => {
  let learningPlanController: LearningPlanController;
  
  beforeEach(() => {
    jest.clearAllMocks();
    learningPlanController = new LearningPlanController(mockLearningPlanApplicationService as any);
  });

  describe('createLearningPlan', () => {
    it('should create a learning plan and return 201 status', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      req.body = {
        title: '测试学习计划',
        description: '这是一个测试学习计划',
        targetDays: 30,
        dailyGoalExercises: 2,
        dailyGoalInsights: 3,
        dailyGoalMinutes: 60,
        isPublic: true,
        tags: ['tag1', 'tag2'],
        setAsCurrent: true
      };
      
      const mockLearningPlanDto = {
        id: 1,
        userId: 'user1',
        title: '测试学习计划',
        description: '这是一个测试学习计划',
        targetDays: 30,
        completedDays: 0,
        progress: 0,
        status: 'created',
        isCurrent: true
      };
      
      mockLearningPlanApplicationService.createLearningPlan.mockResolvedValue(mockLearningPlanDto);
      
      // 执行
      await learningPlanController.createLearningPlan(req, res, mockNext);
      
      // 验证
      expect(mockLearningPlanApplicationService.createLearningPlan).toHaveBeenCalledWith({
        userId: 'user1',
        title: '测试学习计划',
        description: '这是一个测试学习计划',
        templateId: undefined,
        themeId: undefined,
        targetDays: 30,
        dailyGoalExercises: 2,
        dailyGoalInsights: 3,
        dailyGoalMinutes: 60,
        isPublic: true,
        tags: ['tag1', 'tag2'],
        setAsCurrent: true
      });
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(mockLearningPlanDto);
    });
    
    it('should handle errors and pass to next middleware', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      const error = new Error('创建学习计划失败');
      
      mockLearningPlanApplicationService.createLearningPlan.mockRejectedValue(error);
      
      // 执行
      await learningPlanController.createLearningPlan(req, res, mockNext);
      
      // 验证
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('getLearningPlan', () => {
    it('should get a learning plan and return 200 status', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      req.params = { id: '1' };
      
      const mockLearningPlanDto = {
        id: 1,
        userId: 'user1',
        title: '测试学习计划',
        description: '这是一个测试学习计划',
        targetDays: 30,
        completedDays: 0,
        progress: 0,
        status: 'created',
        isCurrent: true
      };
      
      mockLearningPlanApplicationService.getLearningPlan.mockResolvedValue(mockLearningPlanDto);
      
      // 执行
      await learningPlanController.getLearningPlan(req, res, mockNext);
      
      // 验证
      expect(mockLearningPlanApplicationService.getLearningPlan).toHaveBeenCalledWith({
        learningPlanId: 1
      });
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(mockLearningPlanDto);
    });
    
    it('should return 404 if learning plan not found', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      req.params = { id: '1' };
      
      mockLearningPlanApplicationService.getLearningPlan.mockResolvedValue(null);
      
      // 执行
      await learningPlanController.getLearningPlan(req, res, mockNext);
      
      // 验证
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: '学习计划不存在' });
    });
  });

  describe('updateLearningPlan', () => {
    it('should update a learning plan and return 200 status', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      req.params = { id: '1' };
      req.body = {
        title: '更新的学习计划',
        description: '这是一个更新的学习计划',
        targetDays: 45
      };
      
      const mockLearningPlanDto = {
        id: 1,
        userId: 'user1',
        title: '更新的学习计划',
        description: '这是一个更新的学习计划',
        targetDays: 45,
        completedDays: 0,
        progress: 0,
        status: 'created',
        isCurrent: true
      };
      
      mockLearningPlanApplicationService.updateLearningPlan.mockResolvedValue(mockLearningPlanDto);
      
      // 执行
      await learningPlanController.updateLearningPlan(req, res, mockNext);
      
      // 验证
      expect(mockLearningPlanApplicationService.updateLearningPlan).toHaveBeenCalledWith({
        learningPlanId: 1,
        userId: 'user1',
        title: '更新的学习计划',
        description: '这是一个更新的学习计划',
        targetDays: 45,
        coverImageUrl: undefined,
        dailyGoalExercises: undefined,
        dailyGoalInsights: undefined,
        dailyGoalMinutes: undefined,
        isPublic: undefined,
        tags: undefined
      });
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(mockLearningPlanDto);
    });
  });

  describe('deleteLearningPlan', () => {
    it('should delete a learning plan and return 204 status', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      req.params = { id: '1' };
      
      // 执行
      await learningPlanController.deleteLearningPlan(req, res, mockNext);
      
      // 验证
      expect(mockLearningPlanApplicationService.deleteLearningPlan).toHaveBeenCalledWith({
        learningPlanId: 1,
        userId: 'user1'
      });
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.end).toHaveBeenCalled();
    });
  });

  describe('getCurrentPlan', () => {
    it('should get current learning plan and return 200 status', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      
      const mockLearningPlanDto = {
        id: 1,
        userId: 'user1',
        title: '当前学习计划',
        description: '这是当前学习计划',
        targetDays: 30,
        completedDays: 5,
        progress: 16.67,
        status: 'in_progress',
        isCurrent: true
      };
      
      mockLearningPlanApplicationService.getCurrentPlan.mockResolvedValue(mockLearningPlanDto);
      
      // 执行
      await learningPlanController.getCurrentPlan(req, res, mockNext);
      
      // 验证
      expect(mockLearningPlanApplicationService.getCurrentPlan).toHaveBeenCalledWith('user1');
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(mockLearningPlanDto);
    });
    
    it('should return 404 if no current learning plan', async () => {
      // 准备
      const req = mockRequest();
      const res = mockResponse();
      
      mockLearningPlanApplicationService.getCurrentPlan.mockResolvedValue(null);
      
      // 执行
      await learningPlanController.getCurrentPlan(req, res, mockNext);
      
      // 验证
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: '当前学习计划不存在' });
    });
  });
});
