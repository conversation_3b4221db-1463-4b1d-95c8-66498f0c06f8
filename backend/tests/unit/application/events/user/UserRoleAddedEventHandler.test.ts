import { UserRoleAddedEventHandler } from '../../../../../application/events/user/UserRoleAddedEventHandler';
import { UserRoleAddedEvent } from '../../../../../domain/events/user/UserEvents';
import { User } from '../../../../../domain/models/user/User';
import { Role } from '../../../../../domain/models/user/Role';
import { CacheService } from '../../../../../services/cache.service';

// 模拟CacheService
jest.mock('../../../../../services/cache.service', () => ({
  clearUserCache: jest.fn().mockResolvedValue(true)
}));

describe('UserRoleAddedEventHandler', () => {
  let handler: UserRoleAddedEventHandler;
  let mockLogger: any;
  let mockWebSocketService: any;
  let originalLogger: any;
  let originalWebSocketService: any;

  beforeEach(() => {
    // 创建处理器实例
    handler = new UserRoleAddedEventHandler();

    // 创建模拟对象
    mockLogger = {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn()
    };

    mockWebSocketService = {
      sendToUser: jest.fn().mockResolvedValue(true),
      broadcast: jest.fn().mockResolvedValue(true)
    };

    // 保存原始依赖
    originalLogger = (handler as any).logger;
    originalWebSocketService = (handler as any).webSocketService;

    // 注入模拟依赖
    (handler as any).logger = mockLogger;
    (handler as any).webSocketService = mockWebSocketService;

    // 重置所有模拟
    jest.clearAllMocks();
  });

  afterEach(() => {
    // 恢复原始依赖
    (handler as any).logger = originalLogger;
    (handler as any).webSocketService = originalWebSocketService;
  });

  it('应该正确处理用户角色添加事件并清除缓存', async () => {
    // 创建模拟用户和角色
    const mockUser = {
      id: 101,
      username: 'testuser',
      email: { value: '<EMAIL>' }
    } as unknown as User;

    const mockRole = {
      id: 201,
      name: '高级用户',
      description: '拥有高级权限的用户'
    } as unknown as Role;

    // 创建测试事件
    const event = new UserRoleAddedEvent(mockUser, mockRole);

    // 调用处理方法
    await handler.handle(event);

    // 验证日志记录
    expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('处理用户角色添加事件'));
    expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('成功清除用户 101 的缓存'));

    // 验证缓存清除调用
    expect(CacheService.clearUserCache).toHaveBeenCalledWith(101);

    // 验证WebSocket通知
    expect(mockWebSocketService.sendToUser).toHaveBeenCalledWith(
      '101',
      expect.objectContaining({
        type: 'roleAdded',
        userId: 101,
        roleId: 201,
        roleName: '高级用户'
      })
    );
  });

  it('应该处理缓存清除失败的情况', async () => {
    // 模拟缓存清除失败
    (CacheService.clearUserCache as jest.Mock).mockResolvedValueOnce(false);

    // 创建模拟用户和角色
    const mockUser = {
      id: 102,
      username: 'testuser2',
      email: { value: '<EMAIL>' }
    } as unknown as User;

    const mockRole = {
      id: 202,
      name: '编辑',
      description: '内容编辑角色'
    } as unknown as Role;

    // 创建测试事件
    const event = new UserRoleAddedEvent(mockUser, mockRole);

    // 调用处理方法
    await handler.handle(event);

    // 验证警告日志
    expect(mockLogger.warn).toHaveBeenCalledWith(expect.stringContaining('清除用户 102 的缓存失败'));
  });

  it('应该处理WebSocket通知失败的情况', async () => {
    // 模拟WebSocket通知失败
    mockWebSocketService.sendToUser.mockResolvedValueOnce(false);

    // 创建模拟用户和角色
    const mockUser = {
      id: 103,
      username: 'testuser3',
      email: { value: '<EMAIL>' }
    } as unknown as User;

    const mockRole = {
      id: 203,
      name: '审核员',
      description: '内容审核角色'
    } as unknown as Role;

    // 创建测试事件
    const event = new UserRoleAddedEvent(mockUser, mockRole);

    // 调用处理方法
    await handler.handle(event);

    // 验证缓存清除调用
    expect(CacheService.clearUserCache).toHaveBeenCalledWith(103);

    // 验证WebSocket通知
    expect(mockWebSocketService.sendToUser).toHaveBeenCalledWith(
      '103',
      expect.any(Object)
    );
  });
});
