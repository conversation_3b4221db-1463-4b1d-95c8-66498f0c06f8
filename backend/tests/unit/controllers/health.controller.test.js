/**
 * 健康检查控制器单元测试
 */
const { expect } = require('chai');
const sinon = require('sinon');
const httpMocks = require('node-mocks-http');
const { sequelize } = require('../../../config/database');
const { redisClient } = require('../../../config/redis');
const healthController = require('../../../controllers/health.controller');
const apiResponse = require('../../../utils/apiResponse');
const logger = require('../../../config/logger');

describe('健康检查控制器', () => {
  let req, res;
  
  // 在每个测试前设置模拟对象
  beforeEach(() => {
    req = httpMocks.createRequest();
    res = httpMocks.createResponse();
    
    // 存根数据库和Redis
    sinon.stub(sequelize, 'authenticate').resolves();
    sinon.stub(sequelize, 'getDialect').returns('mysql');
    sequelize.config = {
      host: 'localhost',
      database: 'test_db',
      port: 3306,
      pool: {
        max: 10,
        min: 0,
        idle: 10000
      }
    };
    
    // 存根Redis客户端
    sinon.stub(redisClient, 'ping').resolves('PONG');
    sinon.stub(redisClient, 'info').resolves('redis_version:6.2.6\r\nredis_mode:standalone\r\nos:Linux\r\nuptime_in_seconds:3600\r\nconnected_clients:10\r\nused_memory_human:1.5M\r\nused_memory_peak_human:2M');
    redisClient.isOpen = true;
    
    // 存根API响应
    sinon.stub(apiResponse, 'success').returns(res);
    sinon.stub(apiResponse, 'error').returns(res);
    sinon.stub(apiResponse, 'custom').returns(res);
    
    // 存根日志记录
    sinon.stub(logger, 'error');
  });
  
  // 在每个测试后恢复存根
  afterEach(() => {
    sinon.restore();
  });
  
  describe('basicHealthCheck()', () => {
    it('应该返回基本健康状态', async () => {
      await healthController.basicHealthCheck(req, res);
      
      expect(apiResponse.success.calledOnce).to.be.true;
      expect(apiResponse.success.firstCall.args[0]).to.equal(res);
      expect(apiResponse.success.firstCall.args[1].status).to.equal('ok');
      expect(apiResponse.success.firstCall.args[1].service).to.equal('api');
      expect(apiResponse.success.firstCall.args[1].timestamp).to.be.a('string');
    });
  });
  
  describe('databaseHealthCheck()', () => {
    it('应该返回数据库健康状态', async () => {
      await healthController.databaseHealthCheck(req, res);
      
      expect(sequelize.authenticate.calledOnce).to.be.true;
      expect(apiResponse.success.calledOnce).to.be.true;
      expect(apiResponse.success.firstCall.args[0]).to.equal(res);
      expect(apiResponse.success.firstCall.args[1].status).to.equal('ok');
      expect(apiResponse.success.firstCall.args[1].service).to.equal('database');
      expect(apiResponse.success.firstCall.args[1].info).to.be.an('object');
      expect(apiResponse.success.firstCall.args[1].info.dialect).to.equal('mysql');
    });
    
    it('应该处理数据库错误', async () => {
      sequelize.authenticate.rejects(new Error('数据库连接失败'));
      
      await healthController.databaseHealthCheck(req, res);
      
      expect(sequelize.authenticate.calledOnce).to.be.true;
      expect(apiResponse.error.calledOnce).to.be.true;
      expect(apiResponse.error.firstCall.args[0]).to.equal(res);
      expect(apiResponse.error.firstCall.args[1].status).to.equal('error');
      expect(apiResponse.error.firstCall.args[1].service).to.equal('database');
      expect(apiResponse.error.firstCall.args[1].error).to.equal('数据库连接失败');
      expect(logger.error.calledOnce).to.be.true;
    });
  });
  
  describe('redisHealthCheck()', () => {
    it('应该返回Redis健康状态', async () => {
      await healthController.redisHealthCheck(req, res);
      
      expect(redisClient.ping.calledOnce).to.be.true;
      expect(redisClient.info.calledOnce).to.be.true;
      expect(apiResponse.success.calledOnce).to.be.true;
      expect(apiResponse.success.firstCall.args[0]).to.equal(res);
      expect(apiResponse.success.firstCall.args[1].status).to.equal('ok');
      expect(apiResponse.success.firstCall.args[1].service).to.equal('redis');
      expect(apiResponse.success.firstCall.args[1].ping).to.equal('PONG');
      expect(apiResponse.success.firstCall.args[1].info).to.be.an('object');
    });
    
    it('应该处理Redis未连接的情况', async () => {
      redisClient.isOpen = false;
      
      await healthController.redisHealthCheck(req, res);
      
      expect(redisClient.ping.called).to.be.false;
      expect(apiResponse.error.calledOnce).to.be.true;
      expect(apiResponse.error.firstCall.args[0]).to.equal(res);
      expect(apiResponse.error.firstCall.args[1].status).to.equal('error');
      expect(apiResponse.error.firstCall.args[1].service).to.equal('redis');
      expect(apiResponse.error.firstCall.args[1].error).to.equal('Redis客户端未连接');
      expect(logger.error.calledOnce).to.be.true;
    });
    
    it('应该处理Redis错误', async () => {
      redisClient.ping.rejects(new Error('Redis连接失败'));
      
      await healthController.redisHealthCheck(req, res);
      
      expect(redisClient.ping.calledOnce).to.be.true;
      expect(apiResponse.error.calledOnce).to.be.true;
      expect(apiResponse.error.firstCall.args[0]).to.equal(res);
      expect(apiResponse.error.firstCall.args[1].status).to.equal('error');
      expect(apiResponse.error.firstCall.args[1].service).to.equal('redis');
      expect(apiResponse.error.firstCall.args[1].error).to.equal('Redis连接失败');
      expect(logger.error.calledOnce).to.be.true;
    });
  });
  
  describe('systemHealthCheck()', () => {
    it('应该返回系统健康状态', async () => {
      await healthController.systemHealthCheck(req, res);
      
      expect(apiResponse.success.calledOnce).to.be.true;
      expect(apiResponse.success.firstCall.args[0]).to.equal(res);
      expect(apiResponse.success.firstCall.args[1].status).to.equal('ok');
      expect(apiResponse.success.firstCall.args[1].service).to.equal('system');
      expect(apiResponse.success.firstCall.args[1].os).to.be.an('object');
      expect(apiResponse.success.firstCall.args[1].memory).to.be.an('object');
      expect(apiResponse.success.firstCall.args[1].cpu).to.be.an('object');
      expect(apiResponse.success.firstCall.args[1].process).to.be.an('object');
    });
  });
  
  describe('fullHealthCheck()', () => {
    it('应该返回所有服务的健康状态', async () => {
      await healthController.fullHealthCheck(req, res);
      
      expect(sequelize.authenticate.calledOnce).to.be.true;
      expect(redisClient.ping.calledOnce).to.be.true;
      expect(apiResponse.custom.calledOnce).to.be.true;
      expect(apiResponse.custom.firstCall.args[0]).to.equal(res);
      expect(apiResponse.custom.firstCall.args[1].status).to.equal('ok');
      expect(apiResponse.custom.firstCall.args[1].services).to.be.an('object');
      expect(apiResponse.custom.firstCall.args[1].services.api).to.be.an('object');
      expect(apiResponse.custom.firstCall.args[1].services.database).to.be.an('object');
      expect(apiResponse.custom.firstCall.args[1].services.redis).to.be.an('object');
      expect(apiResponse.custom.firstCall.args[1].services.system).to.be.an('object');
    });
    
    it('应该处理数据库错误并标记为降级', async () => {
      sequelize.authenticate.rejects(new Error('数据库连接失败'));
      
      await healthController.fullHealthCheck(req, res);
      
      expect(sequelize.authenticate.calledOnce).to.be.true;
      expect(apiResponse.custom.calledOnce).to.be.true;
      expect(apiResponse.custom.firstCall.args[0]).to.equal(res);
      expect(apiResponse.custom.firstCall.args[1].status).to.equal('degraded');
      expect(apiResponse.custom.firstCall.args[1].services.database.status).to.equal('error');
      expect(apiResponse.custom.firstCall.args[1].services.database.error).to.equal('数据库连接失败');
    });
    
    it('应该处理Redis错误并标记为降级', async () => {
      redisClient.isOpen = false;
      
      await healthController.fullHealthCheck(req, res);
      
      expect(apiResponse.custom.calledOnce).to.be.true;
      expect(apiResponse.custom.firstCall.args[0]).to.equal(res);
      expect(apiResponse.custom.firstCall.args[1].status).to.equal('degraded');
      expect(apiResponse.custom.firstCall.args[1].services.redis.status).to.equal('error');
      expect(apiResponse.custom.firstCall.args[1].services.redis.error).to.equal('Redis客户端未连接');
    });
  });
});
