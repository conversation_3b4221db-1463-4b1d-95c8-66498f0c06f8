/**
 * 标准API响应格式
 */

// 成功响应
const success = (res, data = {}, message = 'Success', statusCode = 200) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

// 错误响应
const error = (res, message = 'Error', code = 'SERVER_ERROR', statusCode = 500, details = {}) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(statusCode).json({
    success: false,
    error: {
      code,
      message,
      details
    }
  });
};

// 创建成功响应
const created = (res, data = {}, message = 'Resource created successfully') => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(201).json({
    success: true,
    message,
    data
  });
};

// 无内容响应
const noContent = (res) => {
  return res.status(204).end();
};

// 未授权响应
const unauthorized = (res, message = 'Unauthorized access', details = {}) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(401).json({
    success: false,
    error: {
      code: 'UNAUTHORIZED',
      message,
      details
    }
  });
};

// 禁止访问响应
const forbidden = (res, message = 'Access forbidden', details = {}) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(403).json({
    success: false,
    error: {
      code: 'FORBIDDEN',
      message,
      details
    }
  });
};

// 资源不存在响应
const notFound = (res, message = 'Resource not found', details = {}) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message,
      details
    }
  });
};

// 参数无效响应
const badRequest = (res, message = 'Invalid parameters', details = {}) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(400).json({
    success: false,
    error: {
      code: 'INVALID_PARAMS',
      message,
      details
    }
  });
};

// 冲突响应
const conflict = (res, message = 'Resource already exists', details = {}) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(409).json({
    success: false,
    error: {
      code: 'DUPLICATE_ENTITY',
      message,
      details
    }
  });
};

// 验证错误响应
const validationError = (res, message = 'Validation failed', details = {}) => {
  // 确保正确的字符编码
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  return res.status(422).json({
    success: false,
    error: {
      code: 'VALIDATION_ERROR',
      message,
      details
    }
  });
};

module.exports = {
  success,
  error,
  created,
  noContent,
  unauthorized,
  forbidden,
  notFound,
  badRequest,
  conflict,
  validationError
};
