/**
 * 数据转换工具
 *
 * 此文件为兼容层，直接导出增强版数据转换工具的功能
 * 为了保持向后兼容性，保留了原有的API接口
 *
 * @deprecated 请使用 enhanced-data-transformer.js
 */

// 导入增强版数据转换工具
const enhancedTransformer = require('./enhanced-data-transformer');

// 显示弃用警告
console.warn('\x1b[33m%s\x1b[0m', '警告: dataTransformer.js 已弃用，请使用 enhanced-data-transformer.js');

// 导出兼容接口
module.exports = {
  snakeToCamel: enhancedTransformer.toCamelCase,
  camelToSnake: enhancedTransformer.toSnakeCase,
  transformToCamel: (obj) => enhancedTransformer.transformKeys(obj, enhancedTransformer.NamingStyle.CAMEL),
  transformToSnake: (obj) => enhancedTransformer.transformKeys(obj, enhancedTransformer.NamingStyle.SNAKE),
  createTransformMiddleware: enhancedTransformer.createTransformMiddleware
};
