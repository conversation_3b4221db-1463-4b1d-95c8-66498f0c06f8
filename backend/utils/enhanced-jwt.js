const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../config/logger');
const { redisClient } = require('../config/redis');

/**
 * 增强版JWT工具
 * 提供令牌生成、验证、刷新和撤销功能
 */
class EnhancedJWT {
  /**
   * 生成访问令牌和刷新令牌
   * @param {Object} payload - 要编码到令牌中的数据
   * @returns {Object} 包含访问令牌和刷新令牌的对象
   */
  generateTokens(payload) {
    try {
      // 生成令牌ID
      const jti = crypto.randomBytes(16).toString('hex');
      
      // 创建访问令牌
      const accessToken = jwt.sign(
        { ...payload, jti },
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
      );
      
      // 创建刷新令牌
      const refreshToken = jwt.sign(
        { userId: payload.id, jti },
        config.jwt.secret,
        { expiresIn: config.jwt.refreshExpiresIn }
      );
      
      // 如果Redis可用，存储令牌信息
      if (redisClient.isOpen) {
        // 存储令牌信息到Redis
        this._storeTokenInfo(payload.id, jti, config.jwt.refreshExpiresIn);
      }
      
      return {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn
      };
    } catch (error) {
      logger.error(`生成令牌失败: ${error.message}`);
      throw new Error('令牌生成失败');
    }
  }
  
  /**
   * 验证访问令牌
   * @param {String} token - 要验证的访问令牌
   * @returns {Object|null} 解码后的令牌数据或null（如果无效）
   */
  verifyAccessToken(token) {
    try {
      // 验证令牌
      const decoded = jwt.verify(token, config.jwt.secret);
      
      // 如果Redis可用，检查令牌是否被撤销
      if (redisClient.isOpen) {
        return this._checkTokenRevocation(decoded)
          .then(isRevoked => isRevoked ? null : decoded)
          .catch(error => {
            logger.error(`检查令牌撤销状态失败: ${error.message}`);
            return decoded; // 如果检查失败，仍然返回解码后的令牌
          });
      }
      
      return decoded;
    } catch (error) {
      logger.debug(`令牌验证失败: ${error.message}`);
      return null;
    }
  }
  
  /**
   * 验证刷新令牌并生成新的访问令牌
   * @param {String} refreshToken - 刷新令牌
   * @param {Object} userData - 用户数据（用于生成新令牌）
   * @returns {Promise<Object|null>} 新的令牌对象或null（如果刷新令牌无效）
   */
  async refreshTokens(refreshToken, userData) {
    try {
      // 验证刷新令牌
      const decoded = jwt.verify(refreshToken, config.jwt.secret);
      
      // 检查必要的字段
      if (!decoded.userId || !decoded.jti) {
        logger.warn('刷新令牌缺少必要字段');
        return null;
      }
      
      // 如果Redis可用，检查令牌是否被撤销
      if (redisClient.isOpen) {
        const isRevoked = await this._checkTokenRevocation(decoded);
        if (isRevoked) {
          logger.warn(`刷新令牌已被撤销: ${decoded.jti}`);
          return null;
        }
        
        // 撤销旧令牌
        await this._revokeToken(decoded.userId, decoded.jti);
      }
      
      // 生成新的令牌
      return this.generateTokens(userData);
    } catch (error) {
      logger.error(`刷新令牌失败: ${error.message}`);
      return null;
    }
  }
  
  /**
   * 撤销用户的所有令牌
   * @param {String} userId - 用户ID
   * @returns {Promise<Boolean>} 是否成功
   */
  async revokeAllUserTokens(userId) {
    try {
      if (!redisClient.isOpen) {
        logger.warn('Redis客户端未连接，无法撤销令牌');
        return false;
      }
      
      // 获取用户的所有令牌ID
      const tokenKey = `user:${userId}:tokens`;
      const tokenIds = await redisClient.sMembers(tokenKey);
      
      if (tokenIds.length === 0) {
        return true;
      }
      
      // 将所有令牌添加到撤销列表
      const multi = redisClient.multi();
      
      // 添加每个令牌到撤销列表
      for (const jti of tokenIds) {
        multi.set(`revoked:${jti}`, '1', { EX: config.jwt.refreshExpiresIn });
      }
      
      // 清空用户的令牌集合
      multi.del(tokenKey);
      
      // 执行批量操作
      await multi.exec();
      
      logger.info(`已撤销用户 ${userId} 的所有令牌，共 ${tokenIds.length} 个`);
      return true;
    } catch (error) {
      logger.error(`撤销用户令牌失败: ${error.message}`);
      return false;
    }
  }
  
  /**
   * 从请求头中提取令牌
   * @param {Object} req - Express请求对象
   * @returns {String|null} 提取的令牌或null
   */
  extractTokenFromHeader(req) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    
    return authHeader.split(' ')[1];
  }
  
  /**
   * 存储令牌信息到Redis
   * @param {String} userId - 用户ID
   * @param {String} jti - 令牌ID
   * @param {Number} expiresIn - 过期时间（秒）
   * @returns {Promise<void>}
   * @private
   */
  async _storeTokenInfo(userId, jti, expiresIn) {
    if (!redisClient.isOpen) {
      return;
    }
    
    try {
      const tokenKey = `user:${userId}:tokens`;
      
      // 将令牌ID添加到用户的令牌集合
      await redisClient.sAdd(tokenKey, jti);
      
      // 设置令牌集合的过期时间
      await redisClient.expire(tokenKey, expiresIn);
      
      logger.debug(`已存储令牌信息: ${userId}, ${jti}`);
    } catch (error) {
      logger.error(`存储令牌信息失败: ${error.message}`);
    }
  }
  
  /**
   * 检查令牌是否被撤销
   * @param {Object} decoded - 解码后的令牌数据
   * @returns {Promise<Boolean>} 是否被撤销
   * @private
   */
  async _checkTokenRevocation(decoded) {
    if (!redisClient.isOpen || !decoded.jti) {
      return false;
    }
    
    try {
      // 检查令牌是否在撤销列表中
      const isRevoked = await redisClient.get(`revoked:${decoded.jti}`);
      return !!isRevoked;
    } catch (error) {
      logger.error(`检查令牌撤销状态失败: ${error.message}`);
      return false;
    }
  }
  
  /**
   * 撤销特定令牌
   * @param {String} userId - 用户ID
   * @param {String} jti - 令牌ID
   * @returns {Promise<Boolean>} 是否成功
   * @private
   */
  async _revokeToken(userId, jti) {
    if (!redisClient.isOpen) {
      return false;
    }
    
    try {
      // 将令牌添加到撤销列表
      await redisClient.set(`revoked:${jti}`, '1', { EX: config.jwt.refreshExpiresIn });
      
      // 从用户的令牌集合中移除
      await redisClient.sRem(`user:${userId}:tokens`, jti);
      
      logger.debug(`已撤销令牌: ${userId}, ${jti}`);
      return true;
    } catch (error) {
      logger.error(`撤销令牌失败: ${error.message}`);
      return false;
    }
  }
}

module.exports = new EnhancedJWT();
