/**
 * 错误处理工具
 *
 * 此文件为兼容层，直接导出统一错误处理工具的功能
 * 为了保持向后兼容性，保留了原有的API接口
 *
 * @deprecated 请使用 unified-error.js
 */

// 导入统一错误处理工具
const unifiedError = require('./unified-error');

// 显示弃用警告
console.warn('\x1b[33m%s\x1b[0m', '警告: errorHandler.js 已弃用，请使用 unified-error.js');

// 导出兼容接口
module.exports = {
  AppError: unifiedError.AppError,
  createError: unifiedError.createError,
  handleError: unifiedError.handleError,
  handleApiError: unifiedError.handleApiError,
  handleValidationError: unifiedError.handleValidationError,
  handleDatabaseError: unifiedError.handleDatabaseError,
  handleAIServiceError: unifiedError.handleAIServiceError,
  handleUnauthorizedError: unifiedError.handleUnauthorizedError,
  handleNotFoundError: unifiedError.handleNotFoundError,
  handleBadRequestError: unifiedError.handleBadRequestError,
  handleConflictError: unifiedError.handleConflictError,
  handleServiceUnavailableError: unifiedError.handleServiceUnavailableError,
  handleThirdPartyError: unifiedError.handleThirdPartyError
};
