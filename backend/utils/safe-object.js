/**
 * 安全的对象操作工具
 * 提供安全的对象操作，防止原型污染和对象注入漏洞
 */

const logger = require('../config/logger');

/**
 * 安全地获取对象属性
 * 使用Object.hasOwnProperty验证属性是否存在于对象本身
 * @param {Object} obj 目标对象
 * @param {string} prop 属性名
 * @param {*} defaultValue 默认值（如果属性不存在）
 * @returns {*} 属性值或默认值
 */
function safeGet(obj, prop, defaultValue = undefined) {
  if (!obj || typeof obj !== 'object') {
    return defaultValue;
  }
  
  if (Object.prototype.hasOwnProperty.call(obj, prop)) {
    return obj[prop];
  }
  
  return defaultValue;
}

/**
 * 安全地设置对象属性
 * 防止设置原型链上的属性
 * @param {Object} obj 目标对象
 * @param {string} prop 属性名
 * @param {*} value 属性值
 * @returns {boolean} 是否成功设置
 */
function safeSet(obj, prop, value) {
  if (!obj || typeof obj !== 'object') {
    return false;
  }
  
  // 防止设置原型链上的属性
  if (prop === '__proto__' || prop === 'constructor' || prop === 'prototype') {
    logger.warn(`尝试设置危险属性: ${prop}`);
    return false;
  }
  
  obj[prop] = value;
  return true;
}

/**
 * 安全地合并对象
 * 防止原型污染
 * @param {Object} target 目标对象
 * @param {Object} source 源对象
 * @returns {Object} 合并后的对象
 */
function safeMerge(target, source) {
  if (!target || typeof target !== 'object') {
    return target;
  }
  
  if (!source || typeof source !== 'object') {
    return target;
  }
  
  const result = { ...target };
  
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      // 防止设置原型链上的属性
      if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
        logger.warn(`尝试合并危险属性: ${key}`);
        continue;
      }
      
      const value = source[key];
      
      if (value !== null && typeof value === 'object' && !Array.isArray(value) &&
          result[key] !== null && typeof result[key] === 'object' && !Array.isArray(result[key])) {
        // 递归合并嵌套对象
        result[key] = safeMerge(result[key], value);
      } else {
        result[key] = value;
      }
    }
  }
  
  return result;
}

/**
 * 创建安全的对象
 * 使用Object.create(null)创建没有原型的对象
 * @returns {Object} 没有原型的对象
 */
function createSafeObject() {
  return Object.create(null);
}

/**
 * 安全地解析JSON
 * 防止原型污染
 * @param {string} json JSON字符串
 * @param {Function} reviver 可选的转换函数
 * @returns {*} 解析后的值
 */
function safeJsonParse(json, reviver = undefined) {
  try {
    // 使用JSON.parse解析JSON字符串
    const parsed = JSON.parse(json, reviver);
    
    // 如果解析结果是对象，进行安全处理
    if (parsed && typeof parsed === 'object') {
      return sanitizeObject(parsed);
    }
    
    return parsed;
  } catch (error) {
    logger.error(`JSON解析失败: ${error.message}`);
    throw error;
  }
}

/**
 * 净化对象，防止原型污染
 * @param {Object} obj 需要净化的对象
 * @returns {Object} 净化后的对象
 */
function sanitizeObject(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  // 如果是数组，递归净化每个元素
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }
  
  // 创建没有原型的新对象
  const result = createSafeObject();
  
  // 复制所有自有属性
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      // 跳过危险属性
      if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
        logger.warn(`跳过危险属性: ${key}`);
        continue;
      }
      
      const value = obj[key];
      
      // 递归净化嵌套对象
      if (value !== null && typeof value === 'object') {
        result[key] = sanitizeObject(value);
      } else {
        result[key] = value;
      }
    }
  }
  
  return result;
}

module.exports = {
  safeGet,
  safeSet,
  safeMerge,
  createSafeObject,
  safeJsonParse,
  sanitizeObject
};
