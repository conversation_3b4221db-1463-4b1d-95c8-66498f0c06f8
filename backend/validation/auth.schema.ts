/**
 * 认证验证模式
 * 用于验证认证相关的请求
 */
import Joi from 'joi';

/**
 * 登录验证模式
 */
export const loginSchema = Joi.object({
  username: Joi.string().min(3).max(30),
  email: Joi.string().email(),
  phoneNumber: Joi.string().pattern(/^1[3-9]\d{9}$/),
  wechatOpenId: Joi.string(),
  password: Joi.string().min(6).max(30),
  deviceInfo: Joi.string()
}).or('username', 'email', 'phoneNumber', 'wechatOpenId');

/**
 * 刷新令牌验证模式
 */
export const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required()
});

/**
 * 验证邮箱验证模式
 */
export const verifyEmailSchema = Joi.object({
  email: Joi.string().email().required(),
  verificationCode: Joi.string().required()
});

/**
 * 验证手机号验证模式
 */
export const verifyPhoneSchema = Joi.object({
  phoneNumber: Joi.string().pattern(/^1[3-9]\d{9}$/).required(),
  verificationCode: Joi.string().required()
});
