// behaviors/data-loading-behavior.js
// 数据加载行为，用于广场页面和瀑布流组件

// 导入API工具
const { squareAPI, statisticsAPI, tagAPI } = require('../utils/api');
const app = getApp(); // 获取 App 实例

// 不再使用模拟数据，只使用真实数据

module.exports = Behavior({
  properties: {
    // 页面大小
    pageSize: {
      type: Number,
      value: 8
    }
  },

  data: {
    posts: [],
    leftPosts: [],
    rightPosts: [],
    isLoading: true,
    isLoadingMore: false,
    isRefreshing: false,
    hasMore: true,
    pageNum: 1,
    isLoginReady: false, // 使用 app 的状态
    showLoginPrompt: false // 控制登录提示 (如果需要)
  },

  attached() {
    // 检查用户认证状态，并保存到数据中
    this.checkAuthStatus();
  },

  methods: {
    /**
     * 检查用户认证状态
     */
    checkAuthStatus() {
      // 避免频繁检查
      const now = Date.now();
      if (now - this.data.lastAuthCheck < 30000) { // 30秒内不重复检查
        return this.data.isAuthenticated;
      }

      const token = wx.getStorageSync('token');
      const isAuthenticated = !!token;

      // 更新状态
      this.setData({
        isAuthenticated,
        lastAuthCheck: now
      });

      return isAuthenticated;
    },

    /**
     * 加载初始帖子
     * @param {string} categoryId - 当前分类 ID (由 Page 传入)
     * @param {boolean} forceRefresh - 是否强制刷新 (由 Page 传入)
     */
    loadInitialPosts(categoryId = 'all', forceRefresh = false) {
      // 如果不是强制刷新且正在加载，则阻止
      if (!forceRefresh && this.data.isLoading) {
        console.log('Behavior: 正在加载中，忽略 loadInitialPosts');
        return;
      }

      this.setData({
        isLoading: true,
        pageNum: 1,
        hasMore: true,
        posts: [], // 清空旧数据
        leftPosts: [],
        rightPosts: [],
        showLoginPrompt: false // 重置登录提示
      });

      // --- 修改：使用 ensureLoginReady ---
      app.ensureLoginReady((isLoggedIn, userInfo) => {
        console.log('Behavior: Login status ready:', isLoggedIn);
        // 更新 Behavior 的登录状态，供其他方法使用
        this.setData({ isLoginReady: isLoggedIn });

        const params = {
          tagId: categoryId === 'all' ? 'all' : categoryId,
          page: 1,
          pageSize: this.data.pageSize,
          sortBy: 'latest'
        };

        console.log('Behavior: 加载广场笔记，参数:', params, '登录状态:', isLoggedIn);

        // 不论登录与否都尝试调用，让后端处理权限或返回公共内容
        squareAPI.getNotes(params)
          .then(res => {
            if (res.success && res.data.notes) {
              const posts = res.data.notes.map(note => ({
                id: note.id,
                title: note.title,
                content: note.content,
                imageUrl: note.imageUrl || 'https://picsum.photos/id/10/600/800', // 默认图片
                userAvatar: note.userAvatar || 'https://picsum.photos/id/100/200/200', // 默认头像
                userName: note.userName || '匿名用户',
                likes: note.likes,
                comments: note.comments,
                isLiked: note.isLiked,
                category: note.tagName,
                tagId: note.tagId,
                isAiGenerated: note.isAiGenerated || false // 添加AI生成标识
              })); // 格式化 post
              const { leftPosts, rightPosts } = this.splitPostsIntoColumns(posts);
              this.setData({
                posts, leftPosts, rightPosts, isLoading: false,
                hasMore: posts.length >= this.data.pageSize
              });
              // 记录活动 (如果需要，检查 isLoginReady)
              // if (isLoggedIn) { this.recordViewActivity(); }
            } else {
              console.error('Behavior: API调用成功但数据格式不正确');
              this.setData({ isLoading: false, posts: [], leftPosts: [], rightPosts: [] });
              // 可以选择显示一个更具体的错误提示
            }
          })
          .catch(err => {
            console.error('Behavior: 获取广场笔记失败', err);
            this.setData({ isLoading: false, posts: [], leftPosts: [], rightPosts: [] });

            // 根据错误类型判断是否显示登录提示
            if (err && err.message && (err.message.includes('未登录') || err.message.includes('认证失败') || err.message.includes('需要登录'))) {
              console.log('Behavior: 检测到需要登录');
              this.setData({ showLoginPrompt: true });
              // 可以显示一个登录按钮或提示，让用户点击后跳转登录页
              wx.showToast({ title: '查看更多内容请先登录', icon: 'none' });
            } else {
              // 其他错误
              wx.showToast({ title: '加载失败，请稍后重试', icon: 'none' });
            }
          });
      });
    },

    /**
     * 加载广场标签
     */
    loadSquareTags() {
      console.log('开始加载广场标签');

      // 检查用户是否登录
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('用户未登录，尝试获取系统默认标签');
        // 用户未登录，直接使用系统默认标签
        return this.loadSystemDefaultTags();
      }

      // 用户已登录，获取当前学习计划的标签
      return tagAPI.getCurrentPlanTags()
        .then(res => {
          console.log('标签API返回结果:', res);
          if (res.success && res.data && res.data.tags) {
            // 将API返回的标签转换为页面需要的格式
            const tags = res.data.tags.map(tag => ({
              id: tag.id,
              name: tag.name
            }));

            console.log('处理后的标签列表:', tags);

            // 添加"推荐"标签
            const recommendTag = { id: 'all', name: '推荐' };

            // 确保"推荐"标签在中间位置
            const middleIndex = Math.floor(tags.length / 2);
            tags.splice(middleIndex, 0, recommendTag);

            return tags;
          } else {
            console.warn('API返回成功但数据格式不正确:', res);
            // 如果获取失败，使用备用方法
            return this.loadBackupTags();
          }
        })
        .catch(err => {
          console.error('获取当前学习计划标签失败', err);
          // 如果获取失败，使用备用方法
          return this.loadBackupTags();
        });
    },

    /**
     * 加载系统默认标签（适用于未登录用户）
     */
    loadSystemDefaultTags() {
      console.log('尝试获取系统默认标签');

      // 显示调试信息
      wx.showToast({
        title: '正在获取系统标签',
        icon: 'none',
        duration: 2000
      });

      return tagAPI.getSystemDefaultPlanTags()
        .then(res => {
          console.log('系统默认标签API返回结果:', res);
          if (res.success && res.data && res.data.tags) {
            // 将API返回的标签转换为页面需要的格式
            const tags = res.data.tags.map(tag => ({
              id: tag.id,
              name: tag.name
            }));

            console.log('处理后的系统默认标签列表:', tags);

            // 显示调试信息
            wx.showToast({
              title: `获取到${tags.length}个系统标签`,
              icon: 'none',
              duration: 2000
            });

            // 添加"推荐"标签
            const recommendTag = { id: 'all', name: '推荐' };

            // 确保"推荐"标签在中间位置
            const middleIndex = Math.floor(tags.length / 2);
            tags.splice(middleIndex, 0, recommendTag);

            return tags;
          } else {
            console.warn('系统默认标签API返回成功但数据格式不正确:', res);
            // 显示调试信息
            wx.showToast({
              title: '系统标签格式不正确',
              icon: 'none',
              duration: 2000
            });
            // 返回空数组，不使用模拟数据
            return [];
          }
        })
        .catch(err => {
          console.error('获取系统默认标签失败', err);
          // 显示调试信息
          wx.showToast({
            title: '获取系统标签失败',
            icon: 'none',
            duration: 2000
          });

          // 返回空数组，不使用模拟数据
          return [];
        });
    },

    /**
     * 加载备用标签（当无法获取当前学习计划标签时）
     */
    loadBackupTags() {
      return squareAPI.getTags()
        .then(res => {
          if (res.success && res.data.tags) {
            // 将API返回的标签转换为页面需要的格式
            const tags = res.data.tags.map(tag => ({
              id: tag.id,
              name: tag.name
            }));

            // 确保"推荐"标签在中间位置
            const recommendIndex = tags.findIndex(tag => tag.id === 'all');
            if (recommendIndex !== -1) {
              // 将"推荐"标签移到数组中间
              const recommendTag = tags.splice(recommendIndex, 1)[0];
              const middleIndex = Math.floor(tags.length / 2);
              tags.splice(middleIndex, 0, recommendTag);
            } else {
              // 如果没有推荐标签，添加一个
              const recommendTag = { id: 'all', name: '推荐' };
              const middleIndex = Math.floor(tags.length / 2);
              tags.splice(middleIndex, 0, recommendTag);
            }

            return tags;
          } else {
            // 如果仍然失败，尝试获取系统默认标签
            console.warn('获取广场标签失败，尝试获取系统默认标签');
            return this.loadSystemDefaultTags();
          }
        })
        .catch(err => {
          console.error('获取广场标签失败，尝试获取系统默认标签', err);
          return this.loadSystemDefaultTags();
        });
    },

    /**
     * 获取默认标签（已废弃，返回空数组）
     * @deprecated 不再使用模拟数据，只使用真实数据
     */
    getDefaultTags() {
      console.warn('getDefaultTags 方法已废弃，不再使用模拟数据');
      return [];
    },

    /**
     * 记录查看广场的学习活动
     */
    recordViewActivity() {
      // 检查用户是否登录
      const isAuthenticated = this.checkAuthStatus();
      if (!isAuthenticated) {
        console.log('用户未登录，不记录学习活动');
        return;
      }

      statisticsAPI.recordActivity({
        activityType: 'view_insight',
        contentType: 'note',
        details: {
          view: 'square',
          category: this.data.currentCategory,
          count: this.data.posts.length
        }
      }).catch(err => {
        console.error('记录学习活动失败', err);
      });
    },

    /**
     * 加载模拟数据（已废弃）
     * @deprecated 不再使用模拟数据，只使用真实数据
     */
    loadMockData() {
      console.warn('loadMockData 方法已废弃，不再使用模拟数据');
      this.setData({
        posts: [],
        leftPosts: [],
        rightPosts: [],
        isLoading: false,
        hasMore: false
      });
    },

    /**
     * 加载更多帖子
     */
    loadMorePosts() {
      if (this.data.isLoadingMore || !this.data.hasMore) {
        return;
      }
      this.setData({ isLoadingMore: true });
      const nextPage = this.data.pageNum + 1;

      const params = {
        tagId: this.data.currentCategory === 'all' ? 'all' : this.data.currentCategory,
        page: nextPage,
        pageSize: this.data.pageSize,
        sortBy: 'latest'
      };

      console.log('Behavior: 加载更多广场笔记，参数:', params);

      squareAPI.getNotes(params)
        .then(res => {
          if (res.success && res.data.notes) {
            const newPosts = res.data.notes.map(note => ({
              id: note.id,
              title: note.title,
              content: note.content,
              imageUrl: note.imageUrl || 'https://picsum.photos/id/10/600/800', // 默认图片
              userAvatar: note.userAvatar || 'https://picsum.photos/id/100/200/200', // 默认头像
              userName: note.userName || '匿名用户',
              likes: note.likes,
              comments: note.comments,
              isLiked: note.isLiked,
              category: note.tagName,
              tagId: note.tagId
            })); // 格式化 post
            const combinedPosts = this.data.posts.concat(newPosts);
            const { leftPosts, rightPosts } = this.splitPostsIntoColumns(combinedPosts);

            this.setData({
              posts: combinedPosts,
              leftPosts,
              rightPosts,
              pageNum: nextPage,
              isLoadingMore: false,
              hasMore: newPosts.length >= this.data.pageSize
            });
          } else {
            this.setData({ isLoadingMore: false, hasMore: false });
          }
        })
        .catch(err => {
          console.error('Behavior: 加载更多笔记失败', err);
          this.setData({ isLoadingMore: false });
          // 加载更多失败通常不需要提示，或者给一个轻量提示
        });
    },

    /**
     * 刷新数据
     */
    onRefresh() {
      if (this.data.isLoading) return;

      this.setData({
        isRefreshing: true
      });

      setTimeout(() => {
        this.loadInitialPosts();
        this.setData({
          isRefreshing: false
        });
      }, 1000);
    },

    /**
     * 根据分类获取帖子（已废弃）
     * @deprecated 不再使用模拟数据，只使用真实数据
     * @param {string} category - 分类名称
     * @param {number} page - 页码
     * @returns {Array} 帖子数组
     */
    getPostsByCategory(category, page) {
      console.warn('getPostsByCategory 方法已废弃，不再使用模拟数据');
      return [];
    },

    /**
     * 将帖子分为左右两列
     * @param {Array} posts - 所有帖子
     * @returns {Object} 包含左列和右列的对象
     */
    splitPostsIntoColumns(posts) {
      const leftPosts = [];
      const rightPosts = [];

      posts.forEach((post, index) => {
        if (index % 2 === 0) {
          leftPosts.push(post);
        } else {
          rightPosts.push(post);
        }
      });

      return { leftPosts, rightPosts };
    },

    /**
     * 点赞帖子
     */
    likePost(e) {
      if (!this.data.isLoginReady) {
        wx.showToast({ title: '请先登录', icon: 'none' });
        // 可选：跳转到登录页
        // setTimeout(() => wx.navigateTo({ url: '/pages/login/index' }), 1500);
        return;
      }
      const { postId } = e.currentTarget.dataset;
      const currentPosts = this.data.posts;
      const postIndex = currentPosts.findIndex(p => p.id === postId);

      if (postIndex === -1) return;

      const post = currentPosts[postIndex];
      const isLiked = post.isLiked;
      const newLikes = isLiked ? post.likes - 1 : post.likes + 1;

      // Optimistic UI update
      this.setData({
        [`posts[${postIndex}].isLiked`]: !isLiked,
        [`posts[${postIndex}].likes`]: newLikes
      });
      // Update columns as well for consistency
      this.updateColumnPosts(postId, { isLiked: !isLiked, likes: newLikes });

      // Call API
      const apiCall = isLiked ? squareAPI.unlikeNote(postId) : squareAPI.likeNote(postId);

      apiCall
        .then(res => {
          if (!res.success) {
            // Revert UI on failure
            console.error('点赞/取消点赞API失败:', res.message);
            this.setData({
              [`posts[${postIndex}].isLiked`]: isLiked,
              [`posts[${postIndex}].likes`]: post.likes
            });
            this.updateColumnPosts(postId, { isLiked: isLiked, likes: post.likes });
            wx.showToast({ title: res.message || '操作失败', icon: 'none' });
          } else {
            console.log('点赞/取消点赞成功');
            // Fetch updated like count from server if needed, or trust optimistic update
          }
        })
        .catch(err => {
          // Revert UI on error
          console.error('点赞/取消点赞请求错误:', err);
          this.setData({
            [`posts[${postIndex}].isLiked`]: isLiked,
            [`posts[${postIndex}].likes`]: post.likes
          });
          this.updateColumnPosts(postId, { isLiked: isLiked, likes: post.likes });
          wx.showToast({ title: '操作失败，请稍后重试', icon: 'none' });
        });
    },

    // Helper to update posts in left/right columns
    updateColumnPosts(postId, updates) {
      const leftIndex = this.data.leftPosts.findIndex(p => p.id === postId);
      if (leftIndex !== -1) {
        for (const key in updates) {
          this.setData({ [`leftPosts[${leftIndex}].${key}`]: updates[key] });
        }
      } else {
        const rightIndex = this.data.rightPosts.findIndex(p => p.id === postId);
        if (rightIndex !== -1) {
          for (const key in updates) {
            this.setData({ [`rightPosts[${rightIndex}].${key}`]: updates[key] });
          }
        }
      }
    },

    /**
     * 查看帖子详情
     */
    viewPostDetail(e) {
      const postId = e.currentTarget.dataset.id;

      // 检查用户是否登录
      const isAuthenticated = this.checkAuthStatus();
      if (!isAuthenticated) {
        console.log('用户未登录，提示登录');
        wx.showToast({
          title: '请先登录后查看详情',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      wx.showToast({
        title: '查看详情: ' + postId,
        icon: 'none'
      });

      // 实际项目中，这里会跳转到详情页
      // wx.navigateTo({
      //   url: `/pages/post-detail/index?id=${postId}`
      // });
    }
  }
});
