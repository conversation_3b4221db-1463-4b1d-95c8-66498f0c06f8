// behaviors/scroll-behavior.js
// 滚动处理行为，用于标签滚动栏组件

module.exports = Behavior({
  properties: {
    // 所有分类标签
    categories: {
      type: Array,
      value: []
    },
    // 当前选中的分类 (由 Page 传入, Behavior 仅读取)
    currentCategory: {
      type: String,
      value: 'all'
    },
    // 窗口宽度
    windowWidth: {
      type: Number,
      value: 375
    }
  },

  data: {
    // 由 Behavior 内部管理
    // 分类栏滚动位置
    categoryScrollLeft: 0,
    // 初始滚动位置 (用于 WXML 滚动视图)
    initialScrollLeft: 0,
    // 存储每个标签的位置信息
    categoryPositions: {},
    // 内部状态
    _isScrolling: false,
    _isManuallyChanging: false,
    _scrollThrottleTimer: null,
    _autoAlignTimer: null,
    _selectTimer: null,
    _manualChangeTimer: null,
    _lastScrollLeft: 0,
    _lastScrollTime: 0,
    _scrollEndCheckTimer: null,
    // 强制触发最终对齐的超时标识
    _finalAlignTimer: null,
    // 正在调整位置标志
    _isAdjusting: false
    // 注意：Behavior 不应直接管理 centerCategoryId，让 Page/Component 管理
    // centerCategoryId: {
    //   type: String,
    //   value: 'all'
    // },
  },

  // Behavior 的 lifetimes
  lifetimes: {
    attached() {
      // 获取屏幕宽度，用于计算中心位置
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        windowWidth: systemInfo.windowWidth
      });
    },
    ready() {
      // 使用延时是因为在布局完成后，元素位置需要一点时间来稳定
      // ready 在组件中执行，确保组件布局完成
      setTimeout(() => {
        // 初始时计算所有标签的位置
        this.getCategoryPositions();
        // 重置滚动位置到初始状态（通常是'all'）
        this.resetScrollToCenter(this.data.currentCategory || 'all');
      }, 300); // 延长初始化时间
    }
  },

  methods: {
    /**
     * 统一的滚动位置计算方法
     * @param {string} categoryId - 需要滚动到的标签ID
     * @return {Promise<number|null>} 返回一个Promise，成功时返回目标滚动位置
     */
    calculateScrollPosition(categoryId) {
      return new Promise(resolve => {
        const targetIndex = this.data.categories.findIndex(item => item.id === categoryId);
        if (targetIndex === -1) {
          console.warn(`[scroll-behavior] Category ${categoryId} not found in categories.`);
          resolve(null);
          return;
        }

        // 使用中心参考线作为参考点
        const query = wx.createSelectorQuery().in(this);
        query.select('#center-reference').boundingClientRect();
        query.selectAll('.category-item').boundingClientRect();
        query.selectViewport().scrollOffset(); // 获取滚动视图的滚动位置
        query.exec(res => {
          if (!res || !res[0] || !res[1] || !res[1].length || !res[2]) {
            console.warn('[scroll-behavior] Failed to get center reference, items or scroll offset.');
            resolve(null);
            return;
          }

          const centerReference = res[0];
          const items = res[1];
          const scrollInfo = res[2]; // scrollOffset is in scrollInfo
          const scrollOffset = scrollInfo.scrollLeft;
          const centerX = centerReference.left; // 使用参考线的精确位置

          const targetItem = items.find(item => item.id === `category-${categoryId}`);
          if (!targetItem) {
            console.warn(`[scroll-behavior] Category item element for ${categoryId} not found.`);
            resolve(null);
            return;
          }

          const itemCenterX = targetItem.left + targetItem.width / 2;
          let targetScrollLeft = scrollOffset + (itemCenterX - centerX);

          // 确保滚动位置不小于0
          if (targetScrollLeft < 0) {
            targetScrollLeft = 0;
          }

          console.log('[scroll-behavior] Calculate Scroll:',
            'Target:', categoryId,
            'Index:', targetIndex,
            'Width:', targetItem.width.toFixed(1),
            'Left:', targetItem.left.toFixed(1),
            'ItemCenter:', itemCenterX.toFixed(1),
            'CurrentScroll:', scrollOffset.toFixed(1),
            'TargetScroll:', targetScrollLeft.toFixed(1));

          resolve(targetScrollLeft);
        });
      });
    },

    /**
     * 执行滚动到指定标签 (内部方法)
     * @param {string} categoryId - 需要滚动到的标签ID
     * @param {boolean} animate - 是否使用动画效果
     * @param {boolean} verify - 滚动后是否需要验证并触发事件
     * @return {Promise<boolean>} 是否成功开始滚动
     */
    _executeScrollToCategory(categoryId, animate = true, verify = false) {
      // 如果有正在进行的滚动，且需要动画，不打断现有滚动
      if (this.data._isScrolling && animate) {
        console.log('[scroll-behavior] Scroll already in progress.');
        return Promise.resolve(false); // Already scrolling with animation
      }

      // 如果有正在进行的滚动，但不需要动画，可以打断
      if (this.data._isScrolling && !animate) {
        console.log('[scroll-behavior] Interrupting previous animated scroll.');
        // 清除所有计时器，确保不会有异步操作干扰
        this.clearAllTimers();
      }

      this.setData({ _isScrolling: true });
      console.log('[scroll-behavior] Execute Scroll Start:', categoryId, 'Animate:', animate, 'Verify:', verify);

      return this.calculateScrollPosition(categoryId).then(targetScrollLeft => {
        if (targetScrollLeft === null) {
          this.setData({ _isScrolling: false });
          return false;
        }

        // Update initialScrollLeft for the scroll-view component to use
        this.setData({
          initialScrollLeft: targetScrollLeft
        });

        // 记录目标位置，供后续验证使用
        this.data._targetScrollLeft = targetScrollLeft;

        // Let the component's scroll-view handle the actual scroll via initialScrollLeft property
        // We don't use scrollTo node API directly from behavior anymore

        // Simulate scroll completion and verification
        const scrollDuration = animate ? 300 : 50; // 稍微延长动画时间

        // 设置强制最终对齐的超时处理
        if (this.data._finalAlignTimer) {
          clearTimeout(this.data._finalAlignTimer);
        }

        this.data._finalAlignTimer = setTimeout(() => {
          // 强制结束滚动状态，无论滚动是否完成
          this.setData({ _isScrolling: false });

          // 调用组件的_forceCalibrationAfterScroll方法进行最终校准（如果存在此方法）
          if (this._forceCalibrationAfterScroll) {
            this._forceCalibrationAfterScroll();
          }
        }, scrollDuration + 100); // 给予更多额外时间确保滚动完成

        return new Promise(resolve => {
          setTimeout(() => {
            console.log('[scroll-behavior] Execute Scroll End (Simulated):', categoryId);
            if (verify) {
              // After scroll, verify and potentially trigger change event
              this._verifyAndNotifyCategoryChange(categoryId, targetScrollLeft);
            } else {
              // If verification is not needed, just end the scroll state
              this.setData({ _isScrolling: false });
            }
            resolve(true); // Indicate scroll command was processed
          }, scrollDuration);
        });

      }).catch(err => {
        console.error('[scroll-behavior] Error during scroll execution:', err);
        this.setData({ _isScrolling: false });
        return false;
      });
    },

    /**
     * 获取所有标签的位置信息 (组件初始化或需要时调用)
     */
    getCategoryPositions() {
      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.category-item').boundingClientRect();
      query.exec(res => {
        if (!res || !res[0]) return;
        const positions = {};
        res[0].forEach(item => {
          if (item.id) {
            const categoryId = item.id.replace('category-', '');
            positions[categoryId] = {
              left: item.left,
              width: item.width,
              center: item.left + item.width / 2
            };
          }
        });
        this.data.categoryPositions = positions;
      });
    },

    /**
     * 处理 WXML 中 scroll-view 的滚动事件 (bindscroll)
     */
    handleScroll(e) {
      const scrollLeft = e.detail.scrollLeft;
      const now = Date.now();

      // If manually triggered scroll is finishing, don't interfere yet
      if (this.data._isManuallyChanging) {
        return;
      }
      // If auto-scrolling/aligning, don't process manual scroll detection
      if (this.data._isScrolling) {
        return;
      }

      // 如果正在调整位置，不再处理滚动
      if (this.data._isAdjusting) {
        return;
      }

      this.data._lastScrollLeft = scrollLeft;
      this.data._lastScrollTime = now;

      // Throttle the center calculation
      if (!this.data._scrollThrottleTimer) {
        this.data._scrollThrottleTimer = setTimeout(() => {
          this.data._scrollThrottleTimer = null;

          // 查找接近中心的标签并更新UI
          this._findCenterTag(scrollLeft);
        }, 50); // Adjust throttle time as needed
      }

      // --- Scroll Stop Detection ---
      this.clearAutoAlignTimer(); // Clear previous alignment timer
      if (this.data._scrollEndCheckTimer) {
        clearTimeout(this.data._scrollEndCheckTimer);
      }
      this._checkScrollStop(0); // Start scroll stop detection
    },

    /**
     * 查找当前位于中心的标签
     * @param {number} scrollLeft - 当前滚动位置
     */
    _findCenterTag(scrollLeft) {
      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.category-item').boundingClientRect();
      query.select('#center-reference').boundingClientRect();
      query.exec(res => {
        if (!res || !res[0] || !res[0].length || !res[1]) {
          return;
        }

        const items = res[0];
        const centerReference = res[1];
        const centerX = centerReference.left; // 使用中心参考线的精确位置

        let closestItem = null;
        let minDistance = Infinity;

        items.forEach(item => {
          const itemCenterX = item.left + (item.width / 2);
          const distance = Math.abs(itemCenterX - centerX);

          if (distance < minDistance) {
            minDistance = distance;
            closestItem = item;
          }
        });

        if (closestItem && closestItem.id) {
          const categoryId = closestItem.id.replace('category-', '');

          // 如果组件有centerCategoryId属性，更新它
          if (this.data.hasOwnProperty('centerCategoryId') && this.data.centerCategoryId !== categoryId) {
            this.setData({
              centerCategoryId: categoryId
            });
          }
        }
      });
    },

    /**
     * (Internal) Recursively check if scrolling has stopped.
     */
    _checkScrollStop(attempt) {
      if (attempt > 5) return; // Max attempts

      this.data._scrollEndCheckTimer = setTimeout(() => {
        const timeSinceLastScroll = Date.now() - this.data._lastScrollTime;

        // If enough time passed, not manually changing, and not auto-scrolling
        if (timeSinceLastScroll >= 100 && !this.data._isManuallyChanging && !this.data._isScrolling && !this.data._isAdjusting) {
          if (attempt >= 1) { // Require at least one confirmation check
            console.log('[scroll-behavior] Scroll stopped, initiating auto-align.');
            this.data._scrollEndCheckTimer = null;

            // 使用标准的对齐方法，减少干扰
            // 只有当用户滚动距离较小时才自动对齐
            this._autoAlignToNearestTag(this.data._lastScrollLeft);
          } else {
            this._checkScrollStop(attempt + 1); // Check again
          }
        } else if (timeSinceLastScroll < 100) {
          // Still scrolling recently, check again later
          this._checkScrollStop(0);
        }
      }, 60 + attempt * 20); // Incremental delay
    },

    /**
     * (Internal) Clear auto-align timer.
     */
    clearAutoAlignTimer() {
      if (this.data._autoAlignTimer) {
        clearTimeout(this.data._autoAlignTimer);
        this.data._autoAlignTimer = null;
      }
    },

    /**
     * (Internal) Clear all behavior-internal timers.
     */
    clearAllTimers() {
      if (this.data._scrollThrottleTimer) clearTimeout(this.data._scrollThrottleTimer);
      if (this.data._autoAlignTimer) clearTimeout(this.data._autoAlignTimer);
      if (this.data._selectTimer) clearTimeout(this.data._selectTimer);
      if (this.data._manualChangeTimer) clearTimeout(this.data._manualChangeTimer);
      if (this.data._scrollEndCheckTimer) clearTimeout(this.data._scrollEndCheckTimer);
      if (this.data._finalAlignTimer) clearTimeout(this.data._finalAlignTimer);
      this.data._scrollThrottleTimer = null;
      this.data._autoAlignTimer = null;
      this.data._selectTimer = null;
      this.data._manualChangeTimer = null;
      this.data._scrollEndCheckTimer = null;
      this.data._finalAlignTimer = null;
      console.log('[scroll-behavior] All timers cleared.');
    },

    /**
     * (Internal) Automatically align to the nearest tag after scroll stops.
     */
    _autoAlignToNearestTag(currentScrollLeft) {
      if (this.data._isScrolling || this.data._isManuallyChanging || this.data._isAdjusting) {
        return; // Don't align if already scrolling or manually changing
      }

      // 标记正在调整
      this.data._isAdjusting = true;

      // Find the category mathematically closest to the center based on current scrollLeft
      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.category-item').boundingClientRect();
      query.select('#center-reference').boundingClientRect();
      query.selectViewport().scrollOffset();
      query.exec(res => {
        if (!res || !res[0] || !res[0].length || !res[1] || !res[2]) {
          console.warn('[scroll-behavior] AutoAlign: Cannot find items.');
          this.data._isAdjusting = false; // 重置调整标记
          return;
        }

        const items = res[0];
        const centerReference = res[1];
        const scrollInfo = res[2];
        const currentScrollLeft = scrollInfo.scrollLeft;
        const centerX = centerReference.left; // 使用中心参考线的精确位置

        // 如果在正常范围内，继续寻找最近的标签
        let closestItem = null;
        let minDistance = Infinity;

        items.forEach(item => {
          const itemViewportCenter = item.left + item.width / 2;
          const distance = Math.abs(itemViewportCenter - centerX);
          if (distance < minDistance) {
            minDistance = distance;
            closestItem = item;
          }
        });

        // 提高对齐阈值，只在标签偏移较大时才进行校正，避免干扰用户滚动
        if (closestItem && closestItem.id && minDistance > 20) {
          const categoryId = closestItem.id.replace('category-', '');
          console.log('[scroll-behavior] AutoAlign Target:', categoryId, 'Distance:', minDistance.toFixed(1));
          // Scroll to this category and verify/notify after scroll
          this._executeScrollToCategory(categoryId, true, true);
        } else if (closestItem && closestItem.id) {
          // 标签已经接近中心，只更新中心标签ID，不进行滚动
          const categoryId = closestItem.id.replace('category-', '');
          if (this.data.hasOwnProperty('centerCategoryId') && this.data.centerCategoryId !== categoryId) {
            this.setData({
              centerCategoryId: categoryId
            });
          }
          this.data._isAdjusting = false; // 重置调整标记
        } else {
          console.warn('[scroll-behavior] AutoAlign: Cannot find closest item.');
          this.data._isAdjusting = false; // 重置调整标记
        }
      });
    },

    /**
     * (Internal) Verify category position after scroll and notify parent component.
     * @param {string} categoryId - The intended category ID.
     * @param {number} expectedScrollLeft - The scroll position we aimed for.
     */
    _verifyAndNotifyCategoryChange(categoryId, expectedScrollLeft) {
      console.log('[scroll-behavior] Verifying position for:', categoryId);
      // Simple notification: Assume scroll finished correctly for now.
      // More robust verification could re-query element positions.

      // Reset scrolling flags first
      this.setData({
        _isScrolling: false,
        _isManuallyChanging: false
      });

      // 延迟重置调整标记，确保其他处理完成
      setTimeout(() => {
        this.data._isAdjusting = false;
      }, 100);

      // 如果组件有centerCategoryId属性，更新它
      if (this.data.hasOwnProperty('centerCategoryId')) {
        this.setData({
          centerCategoryId: categoryId
        });
      }

      // 调用组件的精确居中方法（如果存在），但给系统一些时间恢复
      if (this._ensurePreciseCentering) {
        setTimeout(() => {
          this._ensurePreciseCentering();
        }, 100);
      }

      // Check if the verified category is different from the one passed by the Page prop
      // It's the component's job to trigger the event, this behavior method signals readiness.
      if (this.triggerEvent) { // Check if 'this' has triggerEvent (i.e., it's the component instance)
        console.log('[scroll-behavior] Notifying parent component of change:', categoryId);
        // Check if category actually changed compared to the input prop 'currentCategory'
        // It's better for the page to handle this check.
        this.triggerEvent('change', { categoryId: categoryId });
      } else {
        console.warn('[scroll-behavior] Cannot trigger event. \'this\' context might not be component instance.');
      }
    },

    /**
     * 重置滚动位置到指定标签 (通常是 'all' 或当前选中项)
     * @param {string} categoryId - The ID to center on.
     */
    resetScrollToCenter(categoryId) {
      console.log('[scroll-behavior] Resetting scroll to center:', categoryId);
      // Ensure positions are calculated before scrolling
      this.getCategoryPositions();
      // Use a timeout to ensure positions are ready after getCategoryPositions async call
      setTimeout(() => {
        // Scroll without animation initially, don't verify (avoids potential loops on init)
        this._executeScrollToCategory(categoryId, false, false);
      }, 100); // 增加延迟时间
    },

    /**
     * 处理 WXML 中标签的点击事件 (bindtap)
     */
    handleTap(e) {
      const categoryId = e.currentTarget.dataset.category;
      if (!categoryId) return;

      // Prevent conflicting actions
      if (this.data._isScrolling || this.data._isManuallyChanging || this.data._isAdjusting) {
        console.log('[scroll-behavior] Tap ignored: Action in progress.');
        return;
      }

      // Clear any pending scroll stop checks/alignments
      this.clearAllTimers();
      this.setData({ _isManuallyChanging: true });

      console.log('[scroll-behavior] Category tapped:', categoryId);

      // Scroll to the tapped category, then verify and notify
      this._executeScrollToCategory(categoryId, true, true);

      // Add vibration feedback
      wx.vibrateShort({ type: 'medium' });

      // Reset manual changing flag after a delay (should be cleared by _verifyAndNotifyCategoryChange)
      // Adding a safety timeout just in case.
      if (this.data._manualChangeTimer) clearTimeout(this.data._manualChangeTimer);
      this.data._manualChangeTimer = setTimeout(() => {
        this.setData({ _isManuallyChanging: false });
      }, 500);
    },

    /**
     * (Internal) 检查滚动是否已达到边界
     * @param {number} scrollLeft - 当前滚动位置
     */
    _checkScrollBoundaries(scrollLeft) {
      // 如果正在滚动或手动更改，不执行检查
      if (this.data._isScrolling || this.data._isManuallyChanging || this.data._isAdjusting) {
        return;
      }

      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.category-item').boundingClientRect();
      query.select('.category-container').boundingClientRect();
      query.select('#center-reference').boundingClientRect();
      query.selectViewport().scrollOffset();
      query.exec(res => {
        if (!res || !res[0] || !res[0].length || !res[1] || !res[2] || !res[3]) {
          return;
        }

        const items = res[0];
        const container = res[1];
        const centerReference = res[2];
        const scrollInfo = res[3];
        const currentScrollLeft = scrollInfo.scrollLeft;
        const centerX = centerReference.left; // 使用中心参考线的精确位置

        // 找出最左和最右的标签
        let leftmostItem = null;
        let rightmostItem = null;

        items.forEach(item => {
          if (!leftmostItem || item.left < leftmostItem.left) {
            leftmostItem = item;
          }
          if (!rightmostItem || (item.left + item.width) > (rightmostItem.left + rightmostItem.width)) {
            rightmostItem = item;
          }
        });

        // 计算边界 - 使用更准确的相对容器位置
        const leftmostOffset = leftmostItem.left - container.left;
        const rightmostOffset = rightmostItem.left - container.left + rightmostItem.width;

        // 修改边界计算逻辑，确保首尾标签可以到达中心
        // 允许滚动到使最左/最右标签居中的位置
        const minScrollLeft = Math.max(0, leftmostOffset - centerX + (leftmostItem.width / 2) - 10); // 添加容差
        const maxScrollLeft = rightmostOffset - centerX - (rightmostItem.width / 2) + 10; // 添加容差

        // 检查是否超出边界
        if (currentScrollLeft < minScrollLeft && minScrollLeft > 0) {
          console.log('[scroll-behavior] Scroll hit left boundary, immediate alignment');
          this.clearAllTimers();
          this._executeScrollToCategory(leftmostItem.id.replace('category-', ''), true, true);
        } else if (currentScrollLeft > maxScrollLeft && maxScrollLeft > 0) {
          console.log('[scroll-behavior] Scroll hit right boundary, immediate alignment');
          this.clearAllTimers();
          this._executeScrollToCategory(rightmostItem.id.replace('category-', ''), true, true);
        }
      });
    },

    /**
     * (Internal) 强制将最近的标签对齐到中心
     * 与_autoAlignToNearestTag类似，但更加精确地强制居中
     * @param {number} currentScrollLeft - 当前滚动位置
     */
    _forceAlignToNearestCenter(currentScrollLeft) {
      if (this.data._isScrolling || this.data._isManuallyChanging || this.data._isAdjusting) {
        return; // 如果已经在滚动或手动更改中，不执行
      }

      // 标记正在调整
      this.data._isAdjusting = true;

      // 查找最接近中心的标签并强制居中
      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.category-item').boundingClientRect();
      query.select('#center-reference').boundingClientRect();
      query.selectViewport().scrollOffset();
      query.exec(res => {
        if (!res || !res[0] || !res[0].length || !res[1] || !res[2]) {
          console.warn('[scroll-behavior] ForceAlign: Cannot find items or center reference.');
          this.data._isAdjusting = false; // 重置调整标记
          return;
        }

        const items = res[0];
        const centerReference = res[1];
        const scrollInfo = res[2];
        const currentScrollLeft = scrollInfo.scrollLeft;
        const centerX = centerReference.left; // 使用中心参考线的精确位置

        // 找到最接近中心的标签
        let closestItem = null;
        let minDistance = Infinity;

        items.forEach(item => {
          const itemViewportCenter = item.left + item.width / 2;
          const distance = Math.abs(itemViewportCenter - centerX);
          if (distance < minDistance) {
            minDistance = distance;
            closestItem = item;
          }
        });

        if (closestItem && closestItem.id) {
          const categoryId = closestItem.id.replace('category-', '');

          // 提高偏移容差，仅在偏移较大时调整，避免干扰用户滚动
          // 如果标签已经非常接近中心（小于15px），可以不做处理
          if (minDistance < 15) {
            console.log('[scroll-behavior] Tag already centered enough:', categoryId);

            // 即使已经居中，也要更新UI状态
            if (this.data.hasOwnProperty('centerCategoryId') && this.data.centerCategoryId !== categoryId) {
              this.setData({
                centerCategoryId: categoryId
              });
            }
            this.data._isAdjusting = false; // 重置调整标记
            return;
          }

          console.log('[scroll-behavior] ForceAlign: Centering tag:', categoryId, 'Distance:', minDistance.toFixed(1));

          // 执行滚动到该标签，并验证/通知
          // 使用动画使对齐更平滑
          this._executeScrollToCategory(categoryId, true, true);
        } else {
          console.warn('[scroll-behavior] ForceAlign: Cannot find closest item.');
          this.data._isAdjusting = false; // 重置调整标记
        }
      });
    }
  }
});
