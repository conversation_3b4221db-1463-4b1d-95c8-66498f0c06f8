# 泡泡交互系统性能优化计划

## 优化目标

提升泡泡交互系统的性能，确保在低端设备上也能流畅运行，达到以下性能指标：
- 稳定60fps
- CPU使用率<20%
- 内存使用合理
- 无明显卡顿或闪烁

## 当前性能状况

根据现有测试数据，泡泡交互系统在以下方面存在性能瓶颈：
1. 在低端设备上帧率不稳定，平均约45fps
2. CPU使用率峰值达到35%
3. 大量泡泡同时显示时有明显卡顿
4. 拖动泡泡时有延迟感

## 优化策略

### 1. 渲染优化

#### 1.1 离屏Canvas优化
- 增强离屏Canvas预渲染机制
- 优化缓存策略，减少重复绘制
- 实现更精细的缓存失效机制

```javascript
// 优化离屏Canvas预渲染
_preRenderElements() {
  // 确保离屏Canvas已初始化
  if (!this.offscreenCanvas || !this.offscreenCtx) return;
  
  // 获取常用半径和颜色组合
  const commonSizes = [30, 40, 50, 60, 70];
  const commonColors = this.getThemes().map(theme => theme.color);
  
  // 预渲染常用组合
  commonSizes.forEach(radius => {
    commonColors.forEach(color => {
      const cacheKey = `bubble-${radius}-${color}`;
      
      // 如果缓存中已存在，跳过
      if (this.renderCache.has(cacheKey)) return;
      
      // 创建新的离屏Canvas
      const bubbleCanvas = wx.createOffscreenCanvas({
        type: '2d',
        width: radius * 2 * this.dpr,
        height: radius * 2 * this.dpr
      });
      
      const bubbleCtx = bubbleCanvas.getContext('2d');
      
      // 设置缩放
      bubbleCtx.scale(this.dpr, this.dpr);
      
      // 绘制泡泡
      this._drawBubbleToCanvas(bubbleCtx, radius, color);
      
      // 存入缓存
      this.renderCache.set(cacheKey, bubbleCanvas);
    });
  });
  
  console.log(`预渲染了 ${commonSizes.length * commonColors.length} 个常用泡泡`);
}
```

#### 1.2 渲染区域裁剪
- 实现视口裁剪，只渲染可见区域的泡泡
- 优化渲染顺序，减少重绘

```javascript
// 实现视口裁剪
_isElementVisible(element) {
  // 扩展视口范围，增加缓冲区
  const bufferSize = element.radius * 2;
  const extendedViewport = {
    left: -bufferSize,
    top: -bufferSize,
    right: this.canvasWidth + bufferSize,
    bottom: this.canvasHeight + bufferSize
  };
  
  // 检查元素是否在扩展视口内
  return (
    element.x + element.radius > extendedViewport.left &&
    element.x - element.radius < extendedViewport.right &&
    element.y + element.radius > extendedViewport.top &&
    element.y - element.radius < extendedViewport.bottom
  );
}

// 优化绘制方法
_drawElements() {
  // 清空画布
  this._clearCanvas();
  
  // 按Z轴排序元素
  const sortedElements = [...this.elements].sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0));
  
  // 只绘制可见元素
  for (let i = 0; i < sortedElements.length; i++) {
    if (this._isElementVisible(sortedElements[i])) {
      this._drawElement(sortedElements[i]);
    }
  }
}
```

### 2. 计算优化

#### 2.1 物理模拟优化
- 简化物理模拟算法
- 优化碰撞检测
- 减少不必要的计算

```javascript
// 优化碰撞检测
_checkCollisions() {
  // 使用空间分区优化碰撞检测
  const gridSize = 100; // 网格大小
  const grid = {}; // 空间网格
  
  // 将元素放入网格
  this.elements.forEach(element => {
    const gridX = Math.floor(element.x / gridSize);
    const gridY = Math.floor(element.y / gridSize);
    
    // 计算元素可能占据的网格
    const minGridX = Math.floor((element.x - element.radius) / gridSize);
    const maxGridX = Math.floor((element.x + element.radius) / gridSize);
    const minGridY = Math.floor((element.y - element.radius) / gridSize);
    const maxGridY = Math.floor((element.y + element.radius) / gridSize);
    
    // 将元素添加到所有可能的网格中
    for (let gx = minGridX; gx <= maxGridX; gx++) {
      for (let gy = minGridY; gy <= maxGridY; gy++) {
        const key = `${gx},${gy}`;
        if (!grid[key]) grid[key] = [];
        grid[key].push(element);
      }
    }
  });
  
  // 检测碰撞
  this.elements.forEach(element => {
    // 获取元素所在的网格
    const gridX = Math.floor(element.x / gridSize);
    const gridY = Math.floor(element.y / gridSize);
    
    // 检查周围的网格
    for (let gx = gridX - 1; gx <= gridX + 1; gx++) {
      for (let gy = gridY - 1; gy <= gridY + 1; gy++) {
        const key = `${gx},${gy}`;
        const cellElements = grid[key] || [];
        
        // 检查当前网格中的元素
        cellElements.forEach(other => {
          if (element !== other) {
            this._handleElementCollision(element, other);
          }
        });
      }
    }
  });
}
```

#### 2.2 动画帧率自适应
- 实现自适应帧率控制
- 根据设备性能调整动画复杂度

```javascript
// 自适应帧率控制
_adaptiveFrameRate() {
  // 获取当前FPS
  const currentFps = this.metrics.fps.current;
  
  // 根据FPS调整动画复杂度
  if (currentFps < 45) {
    // 低性能模式
    this.setPerformanceMode('low');
  } else if (currentFps < 55) {
    // 中性能模式
    this.setPerformanceMode('medium');
  } else {
    // 高性能模式
    this.setPerformanceMode('high');
  }
}

// 设置性能模式
setPerformanceMode(mode) {
  if (this.performanceMode === mode) return;
  
  this.performanceMode = mode;
  
  // 根据性能模式调整参数
  switch (mode) {
    case 'low':
      // 降低动画复杂度
      this.config.animationSpeedMultiplier *= 0.8;
      this.config.shadowBlur = 0;
      this.bubbleConfig.enableGlow = false;
      break;
    case 'medium':
      // 中等动画复杂度
      this.config.animationSpeedMultiplier = 0.064;
      this.config.shadowBlur = 2;
      this.bubbleConfig.enableGlow = false;
      break;
    case 'high':
      // 高动画复杂度
      this.config.animationSpeedMultiplier = 0.064;
      this.config.shadowBlur = 4;
      this.bubbleConfig.enableGlow = true;
      break;
  }
  
  console.log(`性能模式已切换为: ${mode}`);
}
```

### 3. 内存优化

#### 3.1 对象池优化
- 增强对象池管理
- 减少临时对象创建

```javascript
// 增强对象池管理
_initObjectPool() {
  this.elementPool = [];
  
  // 创建对象池
  for (let i = 0; i < this.poolSize; i++) {
    this.elementPool.push(this._createEmptyElement());
  }
  
  console.log(`对象池初始化完成，大小: ${this.poolSize}`);
}

// 从对象池获取元素
_getElementFromPool() {
  // 如果池为空，创建新元素
  if (this.elementPool.length === 0) {
    return this._createEmptyElement();
  }
  
  // 从池中取出元素
  return this.elementPool.pop();
}

// 将元素返回池中
_returnElementToPool(element) {
  // 重置元素状态
  this._resetElement(element);
  
  // 如果池未满，将元素返回池中
  if (this.elementPool.length < this.poolSize) {
    this.elementPool.push(element);
  }
}

// 创建空元素
_createEmptyElement() {
  return {
    id: `element-${Math.random().toString(36).substr(2, 9)}`,
    x: 0,
    y: 0,
    radius: 0,
    color: '',
    velocityX: 0,
    velocityY: 0,
    isActive: false
  };
}

// 重置元素状态
_resetElement(element) {
  element.x = 0;
  element.y = 0;
  element.radius = 0;
  element.color = '';
  element.velocityX = 0;
  element.velocityY = 0;
  element.isActive = false;
  element.isHovered = false;
  element.isClicked = false;
  element.isDragged = false;
  element.isDisappearing = false;
  element.opacity = 1;
  element.needsRedraw = true;
}
```

#### 3.2 资源释放优化
- 优化资源释放机制
- 减少内存泄漏

```javascript
// 优化资源释放
destroy() {
  console.log('CanvasBase: destroy 方法被调用');
  
  // 停止动画
  this.stopAnimation();
  
  // 清空元素
  this.elements.forEach(element => {
    if (element.fromPool) {
      this._returnElementToPool(element);
    }
  });
  this.elements = [];
  
  // 清空渲染缓存
  if (this.renderCache) {
    this.renderCache.clear();
  }
  
  // 释放离屏Canvas
  if (this.offscreenCanvas) {
    this.offscreenCanvas = null;
    this.offscreenCtx = null;
  }
  
  // 释放主Canvas
  this.canvas = null;
  this.ctx = null;
  
  // 释放引用
  this.page = null;
  this.getThemes = null;
  
  console.log('资源已释放');
}
```

### 4. 交互优化

#### 4.1 触摸响应优化
- 优化触摸事件处理
- 减少触摸延迟

```javascript
// 优化触摸响应
_initTouchEvents() {
  if (!this.canvas) return;
  
  // 使用触摸事件管理器
  const touchManager = {
    touchStartHandler: this._handleTouchStart.bind(this),
    touchMoveHandler: this._handleTouchMove.bind(this),
    touchEndHandler: this._handleTouchEnd.bind(this),
    touchCancelHandler: this._handleTouchCancel.bind(this)
  };
  
  // 注册触摸事件
  this.canvas.addEventListener('touchstart', touchManager.touchStartHandler);
  this.canvas.addEventListener('touchmove', touchManager.touchMoveHandler);
  this.canvas.addEventListener('touchend', touchManager.touchEndHandler);
  this.canvas.addEventListener('touchcancel', touchManager.touchCancelHandler);
  
  // 保存引用，用于后续移除
  this.touchManager = touchManager;
  
  console.log('触摸事件已初始化');
}

// 优化触摸开始处理
_handleTouchStart(e) {
  // 阻止默认行为
  e.preventDefault();
  
  // 获取触摸点
  const touch = e.touches[0];
  const x = touch.clientX;
  const y = touch.clientY;
  
  // 记录触摸开始时间
  this.touchStartTime = Date.now();
  
  // 查找触摸的元素
  const touchedElement = this._findElementAtPosition(x, y);
  
  if (touchedElement) {
    // 记录拖动开始状态
    this.draggedElement = touchedElement;
    this.isDragging = false;
    this.dragStartX = x;
    this.dragStartY = y;
    this.elementStartX = touchedElement.x;
    this.elementStartY = touchedElement.y;
    
    // 设置元素状态
    touchedElement.isHovered = true;
    
    // 触发元素触摸事件
    this._triggerElementEvent('touchstart', touchedElement, { x, y });
  }
}
```

#### 4.2 动画流畅度优化
- 优化动画过渡
- 减少动画抖动

```javascript
// 优化动画流畅度
_updateElements(deltaTime) {
  // 限制最大时间增量
  const clampedDeltaTime = Math.min(deltaTime, this.config.maxDeltaTime);
  
  // 使用时间插值平滑动画
  const smoothingFactor = 0.8;
  
  this.elements.forEach(element => {
    // 如果元素不活跃，跳过更新
    if (element.isActive === false) return;
    
    // 如果元素被拖动，跳过物理更新
    if (element.isDragged) return;
    
    // 保存上一帧位置
    element.lastX = element.x;
    element.lastY = element.y;
    
    // 计算新位置
    const newX = element.x + element.velocityX * clampedDeltaTime * this.config.animationSpeedMultiplier;
    const newY = element.y + element.velocityY * clampedDeltaTime * this.config.animationSpeedMultiplier;
    
    // 应用平滑插值
    element.x = element.x * (1 - smoothingFactor) + newX * smoothingFactor;
    element.y = element.y * (1 - smoothingFactor) + newY * smoothingFactor;
    
    // 边界碰撞检测
    this._handleBoundaryCollision(element);
    
    // 更新脉动效果
    if (element.pulsePhase !== undefined) {
      element.pulsePhase += (element.pulseSpeed || 0.001) * clampedDeltaTime;
      if (element.pulsePhase > Math.PI * 2) {
        element.pulsePhase -= Math.PI * 2;
      }
    }
    
    // 更新消失动画
    if (element.isDisappearing) {
      element.disappearProgress = Math.min(1, element.disappearProgress + 0.01);
      element.opacity = 1 - element.disappearProgress;
      
      // 如果完全消失，移除元素
      if (element.disappearProgress >= 1) {
        element.isActive = false;
        
        // 如果来自对象池，返回池中
        if (element.fromPool) {
          this._returnElementToPool(element);
        }
      }
    }
    
    // 标记需要重绘
    element.needsRedraw = true;
  });
  
  // 移除不活跃的元素
  this.elements = this.elements.filter(element => element.isActive !== false);
}
```

## 实施计划

### 阶段一：基础优化（2天）

1. **离屏Canvas优化**
   - 实现更高效的预渲染机制
   - 优化缓存策略

2. **对象池优化**
   - 增强对象池管理
   - 实现元素回收机制

### 阶段二：渲染优化（2天）

1. **渲染区域裁剪**
   - 实现视口裁剪
   - 优化渲染顺序

2. **动画流畅度优化**
   - 实现时间插值平滑
   - 减少动画抖动

### 阶段三：计算优化（2天）

1. **物理模拟优化**
   - 优化碰撞检测
   - 简化物理模拟算法

2. **自适应性能**
   - 实现自适应帧率控制
   - 根据设备性能调整动画复杂度

### 阶段四：测试与调优（2天）

1. **性能测试**
   - 在不同设备上进行测试
   - 收集性能数据

2. **调优**
   - 根据测试结果进行调优
   - 解决发现的问题

## 预期成果

1. **性能提升**：
   - 帧率提升至稳定60fps
   - CPU使用率降低至20%以下
   - 内存使用更加合理

2. **用户体验改善**：
   - 动画更加流畅
   - 交互响应更加迅速
   - 低端设备上也能获得良好体验

3. **代码质量提升**：
   - 更好的代码组织
   - 更高的可维护性
   - 更强的可扩展性

## 风险与应对

1. **兼容性问题**：
   - 风险：优化可能导致在某些设备上出现兼容性问题
   - 应对：在多种设备上进行充分测试，实现优雅降级

2. **性能与效果平衡**：
   - 风险：过度优化可能影响视觉效果
   - 应对：根据设备性能动态调整效果，保持视觉体验

3. **开发时间压力**：
   - 风险：优化工作可能超出预期时间
   - 应对：优先实施影响最大的优化，分阶段实施
