-- AIBUBB 数据库升级脚本 V3.0
-- 此脚本将当前数据库结构升级到V3.0规范

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 使用数据库
USE aibubb_db;

-- 开启事务
START TRANSACTION;

-- 第一阶段：创建基础表

-- 1. 创建level表（新表）
CREATE TABLE IF NOT EXISTS level (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '等级ID',
  level_number INT NOT NULL COMMENT '等级数值',
  name VARCHAR(50) NOT NULL COMMENT '等级名称',
  required_exp INT NOT NULL COMMENT '达到该等级所需总经验值',
  icon VARCHAR(50) COMMENT '等级图标',
  rewards JSON COMMENT '等级奖励',
  description TEXT COMMENT '等级描述',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_level_number (level_number),
  INDEX idx_required_exp (required_exp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户等级定义表';

-- 插入默认等级数据（使用INSERT IGNORE忽略重复数据）
INSERT IGNORE INTO level (level_number, name, required_exp, description)
VALUES
(1, '初学者', 0, '刚开始学习之旅'),
(2, '探索者', 100, '开始探索知识的奥秘'),
(3, '进取者', 300, '稳步提升自己'),
(4, '实践者', 600, '将知识付诸实践'),
(5, '精通者', 1000, '已经掌握了核心知识');

-- 2. 创建新的theme表（snake_case命名）
CREATE TABLE IF NOT EXISTS theme (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主题ID',
  name VARCHAR(50) NOT NULL COMMENT '主题名称',
  english_name VARCHAR(50) COMMENT '主题英文名称',
  description TEXT COMMENT '主题描述',
  icon VARCHAR(50) COMMENT '主题图标',
  color VARCHAR(20) COMMENT '主题颜色',
  cover_image_url VARCHAR(255) COMMENT '封面图片URL',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  parent_id INT COMMENT '父主题ID，用于主题分类',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_name (name),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order),
  INDEX idx_parent_id (parent_id),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (parent_id) REFERENCES theme(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习主题表';

-- 3. 创建新的tag_category表（snake_case命名）
CREATE TABLE IF NOT EXISTS tag_category (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
  name VARCHAR(50) NOT NULL COMMENT '分类名称',
  description TEXT COMMENT '分类描述',
  parent_id INT COMMENT '父分类ID',
  theme_id INT COMMENT '所属主题ID (可选，用于按主题过滤)',
  level INT DEFAULT 1 COMMENT '层级，1为顶级分类',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_name (name),
  INDEX idx_parent_id (parent_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_level (level),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (parent_id) REFERENCES tag_category(id) ON DELETE SET NULL,
  FOREIGN KEY (theme_id) REFERENCES theme(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签分类层级表';

-- 4. 创建新的user表（snake_case命名，BIGINT主键）
CREATE TABLE IF NOT EXISTS user (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户唯一标识，建议使用自增 BIGINT',
  openid VARCHAR(64) UNIQUE COMMENT '微信 OpenID',
  phone VARCHAR(20) UNIQUE COMMENT '手机号',
  nickname VARCHAR(50) COMMENT '用户昵称',
  avatar_url VARCHAR(255) COMMENT '头像URL',
  gender TINYINT COMMENT '性别：0未知，1男，2女',
  password_hash VARCHAR(100) COMMENT '密码哈希 (如果支持密码登录)',
  login_type ENUM('wechat', 'phone', 'github', 'apple') COMMENT '最近一次登录类型',
  last_login_at DATETIME COMMENT '最后登录时间',
  study_days INT DEFAULT 0 COMMENT '学习天数 (聚合数据)',
  level_id INT DEFAULT 1 COMMENT '用户等级ID',
  exp_points INT DEFAULT 0 COMMENT '经验值',
  is_admin BOOLEAN DEFAULT FALSE COMMENT '是否管理员',
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '用户状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_nickname (nickname),
  INDEX idx_level_id (level_id),
  INDEX idx_created_at (created_at),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (level_id) REFERENCES level(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- 5. 创建user_setting表（新表）
CREATE TABLE IF NOT EXISTS user_setting (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT NOT NULL COMMENT '用户ID',
  theme_preference VARCHAR(20) DEFAULT 'system' COMMENT '主题偏好：light/dark/system',
  privacy_settings JSON COMMENT '隐私设置 (JSON 结构需定义，未来可考虑拆分)',
  learning_preferences JSON COMMENT '学习偏好设置 (JSON 结构需定义，未来可考虑拆分)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_user_id (user_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户个性化设置表';

-- 6. 创建user_notification_setting表（新表）
CREATE TABLE IF NOT EXISTS user_notification_setting (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT NOT NULL COMMENT '用户ID',
  notification_type ENUM('like', 'comment', 'follow', 'achievement', 'system', 'reminder', 'template_update') NOT NULL COMMENT '通知类型',
  is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用此类通知',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  UNIQUE KEY uk_user_type (user_id, notification_type),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知偏好设置表';

-- 第二阶段：创建核心层次模块表

-- 开启事务
START TRANSACTION;

-- 1. 创建learning_template表（新表）
CREATE TABLE IF NOT EXISTS learning_template (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
  theme_id INT NOT NULL COMMENT '关联的主题ID',
  title VARCHAR(100) NOT NULL COMMENT '模板标题',
  description TEXT COMMENT '模板描述',
  cover_image_url VARCHAR(255) COMMENT '封面图片URL',
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度级别',
  estimated_days INT DEFAULT 7 COMMENT '预计完成天数',
  daily_goal_minutes INT DEFAULT 15 COMMENT '每日学习时间目标(分钟)',
  is_official BOOLEAN DEFAULT FALSE COMMENT '是否官方模板',
  creator_id BIGINT COMMENT '创建者ID（官方模板为NULL）',
  popularity INT DEFAULT 0 COMMENT '使用人数/受欢迎程度 (聚合数据)',
  rating DECIMAL(2,1) DEFAULT 5.0 COMMENT '评分(1-5，允许半星，聚合数据)',
  rating_count INT DEFAULT 0 COMMENT '评分人数 (聚合数据)',
  price DECIMAL(10,2) DEFAULT 0.00 COMMENT '价格（0表示免费）',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_theme_id (theme_id),
  INDEX idx_difficulty (difficulty),
  INDEX idx_is_official (is_official),
  INDEX idx_popularity (popularity),
  INDEX idx_creator_id (creator_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (theme_id) REFERENCES theme(id) ON DELETE CASCADE,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习模板定义表';

-- 2. 创建learning_plan表（snake_case命名）
CREATE TABLE IF NOT EXISTS learning_plan (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '计划ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  template_id INT COMMENT '关联的模板ID (可为空，表示自定义计划)',
  theme_id INT COMMENT '关联的主题ID (冗余字段，也可通过 template_id 获取)',
  title VARCHAR(100) NOT NULL COMMENT '计划标题',
  description TEXT COMMENT '计划描述',
  cover_image_url VARCHAR(255) COMMENT '封面图片URL',
  target_days INT DEFAULT 7 COMMENT '目标完成天数',
  completed_days INT DEFAULT 0 COMMENT '已完成天数 (聚合数据)',
  progress INT DEFAULT 0 COMMENT '进度百分比 (聚合数据, 从 user_content_progress 计算)',
  daily_goal_exercises INT DEFAULT 3 COMMENT '每日练习目标数量',
  daily_goal_insights INT DEFAULT 5 COMMENT '每日观点目标数量',
  daily_goal_minutes INT DEFAULT 15 COMMENT '每日学习时间目标(分钟)',
  status ENUM('not_started', 'in_progress', 'completed', 'paused', 'abandoned') DEFAULT 'not_started' COMMENT '状态',
  start_date DATE COMMENT '开始日期',
  end_date DATE COMMENT '结束日期',
  is_current BOOLEAN DEFAULT FALSE COMMENT '是否为当前活跃计划',
  is_system_default BOOLEAN DEFAULT FALSE COMMENT '是否为系统默认计划',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_user_id (user_id),
  INDEX idx_template_id (template_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_status (status),
  INDEX idx_is_current (is_current),
  INDEX idx_is_system_default (is_system_default),
  INDEX idx_is_public (is_public),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES learning_template(id) ON DELETE SET NULL,
  FOREIGN KEY (theme_id) REFERENCES theme(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习计划实例表';

-- 3. 创建tag表（snake_case命名）
CREATE TABLE IF NOT EXISTS tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
  name VARCHAR(50) NOT NULL COMMENT '标签名称',
  category_id INT COMMENT '关联的标签分类ID',
  creator_id BIGINT COMMENT '创建者ID (NULL表示官方)',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1, 可由算法或反馈调整)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)，用于排序和推荐',
  usage_count INT DEFAULT 0 COMMENT '使用次数 (聚合数据)',
  is_verified BOOLEAN DEFAULT FALSE COMMENT '是否经过验证 (例如由管理员)',
  is_official BOOLEAN DEFAULT TRUE COMMENT '是否官方标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_name (name),
  INDEX idx_category_id (category_id),
  INDEX idx_creator_id (creator_id),
  INDEX idx_weight (weight),
  INDEX idx_usage_count (usage_count),
  INDEX idx_is_verified (is_verified),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (category_id) REFERENCES tag_category(id) ON DELETE SET NULL,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识标签表';

-- 4. 创建tag_synonym表（snake_case命名）
CREATE TABLE IF NOT EXISTS tag_synonym (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '同义词ID',
  primary_tag_id INT NOT NULL COMMENT '主标签ID',
  synonym_name VARCHAR(50) NOT NULL COMMENT '同义词名称',
  similarity_score FLOAT DEFAULT 0.8 COMMENT '相似度得分(0-1)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_primary_tag_id (primary_tag_id),
  INDEX idx_synonym_name (synonym_name),
  UNIQUE KEY uk_primary_synonym (primary_tag_id, synonym_name),
  FOREIGN KEY (primary_tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签同义词/别名表';

-- 5. 创建template_tag表（snake_case命名）
CREATE TABLE IF NOT EXISTS template_tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
  template_id INT NOT NULL COMMENT '模板ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)',
  is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_template_id (template_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_primary (is_primary),
  UNIQUE KEY uk_template_tag (template_id, tag_id),
  FOREIGN KEY (template_id) REFERENCES learning_template(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习模板与标签的关联表';

-- 6. 创建plan_tag表（snake_case命名）
CREATE TABLE IF NOT EXISTS plan_tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)',
  is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_primary (is_primary),
  UNIQUE KEY uk_plan_tag (plan_id, tag_id),
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习计划与标签的关联表';

-- 提交事务
COMMIT;

-- 第三阶段：创建内容表

-- 开启事务
START TRANSACTION;

-- 1. 创建exercise表（snake_case命名）
CREATE TABLE IF NOT EXISTS exercise (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '练习ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  title VARCHAR(100) NOT NULL COMMENT '练习标题',
  description TEXT NOT NULL COMMENT '练习描述',
  expected_result TEXT COMMENT '预期结果',
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度级别',
  time_estimate_minutes INT DEFAULT 5 COMMENT '预计完成时间(分钟)',
  creator_id BIGINT COMMENT '创建者ID (NULL表示官方)',
  is_official BOOLEAN DEFAULT TRUE COMMENT '是否官方内容',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_tag_id (tag_id),
  INDEX idx_difficulty (difficulty),
  INDEX idx_creator_id (creator_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FULLTEXT INDEX ft_title_description (title, description),
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='练习内容表';

-- 2. 创建insight表（snake_case命名）
CREATE TABLE IF NOT EXISTS insight (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '观点ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  content TEXT NOT NULL COMMENT '观点内容',
  source VARCHAR(100) COMMENT '来源',
  background TEXT COMMENT '背景解释',
  creator_id BIGINT COMMENT '创建者ID (NULL表示官方)',
  is_official BOOLEAN DEFAULT TRUE COMMENT '是否官方内容',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_tag_id (tag_id),
  INDEX idx_creator_id (creator_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FULLTEXT INDEX ft_content (content),
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE,
  FOREIGN KEY (creator_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='观点内容表';

-- 3. 创建note表（snake_case命名）
CREATE TABLE IF NOT EXISTS note (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '笔记ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  title VARCHAR(100) NOT NULL COMMENT '笔记标题',
  content TEXT NOT NULL COMMENT '笔记内容',
  image_urls JSON COMMENT '配图URL数组',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
  like_count INT DEFAULT 0 COMMENT '点赞数 (聚合数据)',
  comment_count INT DEFAULT 0 COMMENT '评论数 (聚合数据)',
  view_count INT DEFAULT 0 COMMENT '查看次数 (聚合数据)',
  status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_public (is_public),
  INDEX idx_like_count (like_count),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FULLTEXT INDEX ft_title_content (title, content),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户笔记表';

-- 4. 创建daily_content表（snake_case命名）
CREATE TABLE IF NOT EXISTS daily_content (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '每日内容ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  day_number INT NOT NULL COMMENT '天数 (从1开始)',
  title VARCHAR(100) NOT NULL COMMENT '标题',
  content TEXT COMMENT '当天的主要文本内容或说明',
  content_type ENUM('text', 'exercise', 'insight', 'note', 'mixed') DEFAULT 'text' COMMENT '主要内容类型 (关联内容见 daily_content_relation)',
  is_completed BOOLEAN DEFAULT FALSE COMMENT '是否已完成 (基于 user_content_progress)',
  completion_date DATETIME COMMENT '完成时间 (聚合数据)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_day_number (day_number),
  INDEX idx_is_completed (is_completed),
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习计划每日安排表';

-- 5. 创建daily_content_relation表（新表）
CREATE TABLE IF NOT EXISTS daily_content_relation (
  id INT AUTO_INCREMENT PRIMARY KEY,
  daily_content_id INT NOT NULL COMMENT '每日内容ID',
  related_content_type ENUM('exercise', 'insight', 'note') NOT NULL COMMENT '关联内容的类型',
  related_content_id INT NOT NULL COMMENT '关联内容的ID',
  sort_order INT DEFAULT 0 COMMENT '关联内容的显示顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_daily_content_id (daily_content_id),
  INDEX idx_related_content (related_content_type, related_content_id),
  UNIQUE KEY uk_daily_content_relation (daily_content_id, related_content_type, related_content_id),
  FOREIGN KEY (daily_content_id) REFERENCES daily_content(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每日内容与具体学习内容的关联表';

-- 提交事务
COMMIT;

-- 第四阶段：创建游戏化元素表

-- 开启事务
START TRANSACTION;

-- 1. 创建achievement表（成就表）
CREATE TABLE IF NOT EXISTS achievement (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '成就ID',
  name VARCHAR(50) NOT NULL COMMENT '成就名称',
  description TEXT NOT NULL COMMENT '成就描述',
  icon VARCHAR(50) COMMENT '成就图标',
  category ENUM('learning', 'social', 'creation', 'special') NOT NULL COMMENT '成就类别',
  difficulty ENUM('easy', 'medium', 'hard', 'expert') NOT NULL COMMENT '难度',
  points INT DEFAULT 10 COMMENT '获得点数',
  condition_type VARCHAR(50) NOT NULL COMMENT '触发条件类型 (如 complete_exercises, consecutive_days)',
  condition_value JSON COMMENT '触发条件值 (JSON结构需定义，未来可考虑拆分)',
  is_hidden BOOLEAN DEFAULT FALSE COMMENT '是否隐藏成就',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_difficulty (difficulty),
  INDEX idx_is_hidden (is_hidden),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成就定义表';

-- 2. 创建user_achievement表（用户成就表）
CREATE TABLE IF NOT EXISTS user_achievement (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户成就ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  achievement_id INT NOT NULL COMMENT '成就ID',
  achieved_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  progress INT DEFAULT 100 COMMENT '完成进度百分比 (对于需要累积的成就)',
  is_notified BOOLEAN DEFAULT FALSE COMMENT '是否已通知用户',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_achievement_id (achievement_id),
  INDEX idx_achieved_at (achieved_at),
  UNIQUE KEY uk_user_achievement (user_id, achievement_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (achievement_id) REFERENCES achievement(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户已获成就记录表';

-- 3. 创建badge表（徽章表）
CREATE TABLE IF NOT EXISTS badge (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '徽章ID',
  name VARCHAR(50) NOT NULL COMMENT '徽章名称',
  description TEXT NOT NULL COMMENT '徽章描述',
  icon VARCHAR(50) NOT NULL COMMENT '徽章图标',
  category ENUM('theme', 'skill', 'event', 'special') NOT NULL COMMENT '徽章类别',
  rarity ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') DEFAULT 'common' COMMENT '稀有度',
  is_displayable BOOLEAN DEFAULT TRUE COMMENT '是否可在个人资料页展示',
  unlock_condition TEXT COMMENT '解锁条件描述 (给用户看)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_rarity (rarity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='徽章定义表';

-- 4. 创建user_badge表（用户徽章表）
CREATE TABLE IF NOT EXISTS user_badge (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户徽章ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  badge_id INT NOT NULL COMMENT '徽章ID',
  acquired_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  is_equipped BOOLEAN DEFAULT FALSE COMMENT '是否装备中 (用于个人资料页展示)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_badge_id (badge_id),
  INDEX idx_is_equipped (is_equipped),
  UNIQUE KEY uk_user_badge (user_id, badge_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (badge_id) REFERENCES badge(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户已获徽章记录表';

-- 提交事务
COMMIT;

-- 第五阶段：创建社区互动表

-- 开启事务
START TRANSACTION;

-- 1. 创建note_like表（笔记点赞表）
CREATE TABLE IF NOT EXISTS note_like (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '笔记点赞ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  note_id INT NOT NULL COMMENT '笔记ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_note_id (note_id),
  UNIQUE KEY uk_user_note (user_id, note_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (note_id) REFERENCES note(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='笔记点赞记录表';

-- 2. 创建tag_like表（标签点赞表）
CREATE TABLE IF NOT EXISTS tag_like (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签点赞ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  UNIQUE KEY uk_user_tag (user_id, tag_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签点赞记录表';

-- 3. 创建note_comment表（笔记评论表）
CREATE TABLE IF NOT EXISTS note_comment (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评论ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  note_id INT NOT NULL COMMENT '笔记ID',
  parent_id INT COMMENT '父评论ID（用于回复嵌套）',
  content TEXT NOT NULL COMMENT '评论内容',
  like_count INT DEFAULT 0 COMMENT '点赞数 (聚合数据)',
  status ENUM('active', 'hidden_by_user', 'hidden_by_admin', 'deleted') DEFAULT 'active' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at DATETIME NULL COMMENT '删除时间 (用于软删除)',
  INDEX idx_user_id (user_id),
  INDEX idx_note_id (note_id),
  INDEX idx_parent_id (parent_id),
  INDEX idx_status (status),
  INDEX idx_deleted_at (deleted_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (note_id) REFERENCES note(id) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES note_comment(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='笔记评论表';

-- 4. 创建comment_like表（评论点赞表）
CREATE TABLE IF NOT EXISTS comment_like (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评论点赞ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  comment_id INT NOT NULL COMMENT '评论ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_comment_id (comment_id),
  UNIQUE KEY uk_user_comment (user_id, comment_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (comment_id) REFERENCES note_comment(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论点赞记录表';

-- 5. 创建user_follow表（用户关注表）
CREATE TABLE IF NOT EXISTS user_follow (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关注ID',
  follower_id BIGINT NOT NULL COMMENT '关注者ID',
  following_id BIGINT NOT NULL COMMENT '被关注者ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_follower_id (follower_id),
  INDEX idx_following_id (following_id),
  UNIQUE KEY uk_follow (follower_id, following_id),
  FOREIGN KEY (follower_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (following_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关注关系表';

-- 6. 创建tag_feedback表（标签反馈表）
CREATE TABLE IF NOT EXISTS tag_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签反馈ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  feedback_type ENUM('relevance', 'suggestion', 'report_error', 'report_duplicate') NOT NULL COMMENT '反馈类型',
  content TEXT COMMENT '反馈内容',
  status ENUM('pending', 'reviewed', 'implemented', 'rejected') DEFAULT 'pending' COMMENT '处理状态',
  reviewer_id BIGINT COMMENT '处理人ID (管理员)',
  reviewed_at DATETIME COMMENT '处理时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_feedback_type (feedback_type),
  INDEX idx_status (status),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE,
  FOREIGN KEY (reviewer_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户对标签的反馈表';

-- 7. 创建notification表（通知表）
CREATE TABLE IF NOT EXISTS notification (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '通知ID',
  user_id BIGINT NOT NULL COMMENT '接收用户ID',
  sender_id BIGINT COMMENT '发送者ID (NULL表示系统)',
  notification_type ENUM('like', 'comment', 'follow', 'achievement', 'system', 'reminder', 'badge', 'level_up') NOT NULL COMMENT '通知类型',
  content TEXT NOT NULL COMMENT '通知内容',
  reference_type VARCHAR(50) COMMENT '引用对象类型 (如 note, comment, user, achievement)',
  reference_id VARCHAR(50) COMMENT '引用对象ID',
  is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
  read_at DATETIME COMMENT '阅读时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_sender_id (sender_id),
  INDEX idx_notification_type (notification_type),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (sender_id) REFERENCES user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知消息表';

-- 提交事务
COMMIT;

-- 第六阶段：创建学习追踪表

-- 开启事务
START TRANSACTION;

-- 1. 创建learning_activity表（学习活动表）
CREATE TABLE IF NOT EXISTS learning_activity (
  id BIGINT AUTO_INCREMENT COMMENT '活动ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  plan_id INT COMMENT '学习计划ID (某些活动可能无计划)',
  activity_type ENUM('exercise', 'insight', 'note', 'daily_content', 'template', 'bubble', 'plan_action', 'login', 'search') NOT NULL COMMENT '活动类型',
  content_id VARCHAR(50) COMMENT '关联内容/对象ID (VARCHAR 兼容不同类型ID)',
  action ENUM('view', 'complete', 'create', 'like', 'comment', 'share', 'start', 'pause', 'resume', 'abandon', 'tap', 'hold', 'merge', 'dismiss') NOT NULL COMMENT '具体行为',
  duration_seconds INT DEFAULT 0 COMMENT '持续时间(秒)',
  details JSON COMMENT '活动详情 (JSON结构需定义，避免滥用)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id, created_at), -- 主键必须包含分区函数中的列
  INDEX idx_user_id (user_id),
  INDEX idx_plan_id (plan_id),
  INDEX idx_activity_type (activity_type),
  INDEX idx_content_id (content_id),
  INDEX idx_action (action),
  INDEX idx_created_at (created_at)
  -- 注意：分区表不支持外键，所以移除了以下外键
  -- FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  -- FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习活动流水表 (大表，需分区)'
PARTITION BY RANGE (YEAR(created_at)) (
  PARTITION p_init VALUES LESS THAN (2024),
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION p2026 VALUES LESS THAN (2027)
);

-- 2. 创建daily_record表（每日记录表）
CREATE TABLE IF NOT EXISTS daily_record (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '每日记录ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  record_date DATE NOT NULL COMMENT '记录日期',
  total_time_minutes INT DEFAULT 0 COMMENT '学习总时间(分钟) (聚合数据)',
  exercises_completed INT DEFAULT 0 COMMENT '完成练习数 (聚合数据)',
  insights_viewed INT DEFAULT 0 COMMENT '查看观点数 (聚合数据)',
  notes_created INT DEFAULT 0 COMMENT '创建笔记数 (聚合数据)',
  exp_gained INT DEFAULT 0 COMMENT '获得经验值 (聚合数据)',
  streak_days INT DEFAULT 1 COMMENT '连续学习天数 (聚合数据)',
  mood ENUM('great', 'good', 'neutral', 'bad', 'terrible') COMMENT '心情记录',
  summary TEXT COMMENT '学习总结',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_record_date (record_date),
  UNIQUE KEY uk_user_date (user_id, record_date),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户每日学习活动聚合记录表';

-- 3. 创建user_content_progress表（用户内容进度表）
CREATE TABLE IF NOT EXISTS user_content_progress (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '进度ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  content_type ENUM('exercise', 'insight', 'daily_content', 'note') NOT NULL COMMENT '内容类型',
  content_id INT NOT NULL COMMENT '内容ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  progress INT DEFAULT 0 COMMENT '进度百分比 (例如对于练习的多步骤)',
  status ENUM('not_started', 'in_progress', 'completed', 'skipped') DEFAULT 'not_started' COMMENT '内容完成状态',
  completion_date DATETIME COMMENT '完成时间',
  last_interaction_at DATETIME COMMENT '最后交互时间',
  proficiency_level ENUM('not_started', 'beginner', 'familiar', 'proficient', 'expert') DEFAULT 'not_started' COMMENT '熟练度 (可由算法评估)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_content_type_id (content_type, content_id),
  INDEX idx_plan_id (plan_id),
  INDEX idx_status (status),
  UNIQUE KEY uk_user_content_plan (user_id, content_type, content_id, plan_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES learning_plan(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户对具体内容的学习进度表 (单一可信源)';

-- 4. 创建user_learning_stats表（用户学习统计表）
CREATE TABLE IF NOT EXISTS user_learning_stats (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  total_learning_time_minutes INT DEFAULT 0 COMMENT '总学习时间(分钟) (聚合数据)',
  total_exercises_completed INT DEFAULT 0 COMMENT '完成练习总数 (聚合数据)',
  total_insights_viewed INT DEFAULT 0 COMMENT '查看观点总数 (聚合数据)',
  total_notes_created INT DEFAULT 0 COMMENT '创建笔记总数 (聚合数据)',
  total_plans_completed INT DEFAULT 0 COMMENT '完成学习计划数 (聚合数据)',
  current_streak_days INT DEFAULT 0 COMMENT '当前连续学习天数 (聚合数据)',
  max_streak_days INT DEFAULT 0 COMMENT '最长连续学习天数 (聚合数据)',
  total_study_days INT DEFAULT 0 COMMENT '总学习天数 (聚合数据)',
  last_active_date DATE COMMENT '最后活跃日期 (聚合数据)',
  favorite_tags JSON COMMENT '喜爱的标签 (聚合数据, 结构需定义)',
  learning_patterns JSON COMMENT '学习模式数据 (聚合数据, 结构需定义)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_current_streak_days (current_streak_days),
  INDEX idx_total_study_days (total_study_days),
  UNIQUE KEY uk_user_id (user_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户全局学习统计聚合表';

-- 提交事务
COMMIT;

-- 第七阶段：创建系统配置表

-- 开启事务
START TRANSACTION;

-- 1. 创建system_config表（系统配置表）
CREATE TABLE IF NOT EXISTS system_config (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
  config_key VARCHAR(50) NOT NULL COMMENT '配置键 (例如: max_daily_exp)',
  config_value TEXT NOT NULL COMMENT '配置值',
  description TEXT COMMENT '配置描述',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否对客户端公开',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_config_key (config_key),
  INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统全局配置表';

-- 2. 创建feature_flag表（功能开关表）
CREATE TABLE IF NOT EXISTS feature_flag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '功能开关ID',
  flag_key VARCHAR(50) NOT NULL COMMENT '开关键 (例如: enable_template_market)',
  is_enabled BOOLEAN DEFAULT FALSE COMMENT '是否全局启用',
  description TEXT COMMENT '功能描述',
  user_percentage INT DEFAULT 100 COMMENT '灰度发布的用户百分比(0-100)',
  start_time DATETIME COMMENT '功能生效开始时间',
  end_time DATETIME COMMENT '功能生效结束时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_flag_key (flag_key),
  INDEX idx_is_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能开关控制表 (灰度发布)';

-- 3. 创建user_feature_access表（用户功能访问表）
CREATE TABLE IF NOT EXISTS user_feature_access (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '访问记录ID',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  feature_id INT NOT NULL COMMENT '功能开关ID',
  has_access BOOLEAN DEFAULT TRUE COMMENT '是否有访问权限 (独立于全局开关和百分比)',
  access_reason VARCHAR(50) COMMENT '特定访问原因 (例如: beta_tester, purchased)',
  granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  expires_at DATETIME COMMENT '过期时间 (用于临时权限)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_feature_id (feature_id),
  INDEX idx_has_access (has_access),
  UNIQUE KEY uk_user_feature (user_id, feature_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (feature_id) REFERENCES feature_flag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='特定用户功能访问权限表';

-- 提交事务
COMMIT;

-- 第八阶段：数据迁移逻辑

-- 开启事务
START TRANSACTION;

-- 1. 迁移Theme表数据到theme表（如果Theme表存在）
-- 使用条件语句，如果表不存在则跳过
SET @theme_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'Theme');
SET @sql_text = IF(@theme_exists > 0,
  'INSERT INTO theme (id, name, english_name, description, icon, color, sort_order, is_active, created_at, updated_at) SELECT id, name, english_name, description, icon, color, sort_order, is_active, created_at, updated_at FROM Theme',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 迁移TagCategory表数据到tag_category表（如果TagCategory表存在）
SET @tagcategory_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'TagCategory');
SET @sql_text = IF(@tagcategory_exists > 0,
  'INSERT INTO tag_category (id, name, description, parent_id, theme_id, level, sort_order, created_at, updated_at) SELECT id, name, description, parent_id, theme_id, level, sort_order, created_at, updated_at FROM TagCategory',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 创建用户ID映射表（临时表）
CREATE TEMPORARY TABLE user_id_mapping (
  old_id VARCHAR(32) NOT NULL,
  new_id BIGINT NOT NULL,
  PRIMARY KEY (old_id)
);

-- 4. 迁移User表数据到user表（如果User表存在）
SET @user_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'User');
SET @sql_text = IF(@user_exists > 0,
  'INSERT INTO user (openid, phone, nickname, avatar_url, gender, login_type, last_login_at, study_days, level_id, created_at, updated_at)
   SELECT
     CASE
       WHEN id LIKE ''phone_%'' THEN NULL
       WHEN id = ''system'' THEN NULL
       ELSE id
     END as openid,
     CASE
       WHEN id LIKE ''phone_%'' THEN SUBSTRING(id, 7)
       ELSE NULL
     END as phone,
     nickname,
     avatar_url,
     gender,
     login_type,
     last_login_at,
     study_days,
     level,
     created_at,
     updated_at
   FROM User',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 填充用户ID映射表（如果User表存在）
SET @user_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'User');
SET @sql_text = IF(@user_exists > 0,
  'INSERT INTO user_id_mapping (old_id, new_id)
   SELECT u.id, nu.id
   FROM User u
   JOIN user nu ON
     (u.id LIKE ''phone_%'' AND nu.phone = SUBSTRING(u.id, 7)) OR
     (u.id NOT LIKE ''phone_%'' AND u.id != ''system'' AND nu.openid = u.id) OR
     (u.id = ''system'' AND nu.id = 1)', -- 假设system用户映射到ID为1的用户
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 迁移LearningPlan表数据到learning_plan表（如果LearningPlan表存在）
SET @learningplan_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'LearningPlan');
SET @sql_text = IF(@learningplan_exists > 0,
  'INSERT INTO learning_plan (
    id, user_id, theme_id, title, description, target_days, completed_days,
    progress, status, start_date, end_date, is_current, is_system_default,
    daily_goal_exercises, daily_goal_insights, daily_goal_minutes,
    created_at, updated_at
  )
  SELECT
    lp.id,
    COALESCE(um.new_id, 1), -- 使用映射的新用户ID，如果找不到映射则使用ID 1
    lp.theme_id,
    lp.title,
    lp.description,
    lp.target_days,
    lp.completed_days,
    lp.progress,
    lp.status,
    lp.start_date,
    lp.end_date,
    lp.is_current,
    lp.is_system_default,
    lp.daily_goal_exercises,
    lp.daily_goal_insights,
    lp.daily_goal_time,
    lp.created_at,
    lp.updated_at
  FROM LearningPlan lp
  LEFT JOIN user_id_mapping um ON lp.user_id = um.old_id',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 迁移Tag表数据到tag表（如果Tag表存在）
SET @tag_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'Tag');
SET @sql_text = IF(@tag_exists > 0,
  'INSERT INTO tag (
    id, name, category_id, relevance_score, weight, usage_count,
    is_verified, sort_order, created_at, updated_at
  )
  SELECT
    id, name, category_id, relevance_score, weight, usage_count,
    is_verified, sort_order, created_at, updated_at
  FROM Tag',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 迁移PlanTag表数据到plan_tag表（如果PlanTag表存在）
SET @plantag_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'PlanTag');
SET @sql_text = IF(@plantag_exists > 0,
  'INSERT INTO plan_tag (
    id, plan_id, tag_id, relevance_score, weight, is_primary, sort_order, created_at
  )
  SELECT
    id, plan_id, tag_id, relevance_score, weight, is_primary, sort_order, created_at
  FROM PlanTag',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 9. 迁移TagSynonym表数据到tag_synonym表（如果TagSynonym表存在）
SET @tagsynonym_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'TagSynonym');
SET @sql_text = IF(@tagsynonym_exists > 0,
  'INSERT INTO tag_synonym (
    id, primary_tag_id, synonym_name, similarity_score, created_at
  )
  SELECT
    id, primary_tag_id, synonym_name, similarity_score, created_at
  FROM TagSynonym',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 10. 迁移DailyContent表数据到daily_content表（如果DailyContent表存在）
SET @dailycontent_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'aibubb_db' AND table_name = 'DailyContent');
SET @sql_text = IF(@dailycontent_exists > 0,
  'INSERT INTO daily_content (
    id, plan_id, day_number, title, content, is_completed, completion_date, created_at, updated_at
  )
  SELECT
    id, plan_id, day_number, title, content, is_completed, completed_at, created_at, updated_at
  FROM DailyContent',
  'SELECT 1');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 提交事务
COMMIT;

-- 注意：这是完整的数据库升级脚本，包括表结构创建和数据迁移逻辑
-- 在实际执行前，请确保已备份数据库，并在测试环境验证脚本的正确性
