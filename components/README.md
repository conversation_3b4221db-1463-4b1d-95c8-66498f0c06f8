# NebulaLearn UI 组件库

NebulaLearn UI 是一套基于微信小程序的UI组件库，提供了丰富的基础组件和业务组件，帮助开发者快速构建高质量的小程序应用。

## 组件分类

组件库按照功能和用途分为以下几类：

### 基础组件 (base)

基础组件是构建界面的基本元素，包括：

- **Button 按钮**：提供各种样式和状态的按钮
- **Card 卡片**：提供各种样式的卡片容器
- **Icon 图标**：提供丰富的图标集合
- **Text 文本**：提供各种样式的文本展示
- **Badge 徽章**：提供角标和徽章展示
- **Divider 分割线**：提供各种样式的分割线
- **Tag 标签**：提供各种样式的标签展示

### 表单组件 (form)

表单组件用于收集、验证和提交用户输入，包括：

- **Input 输入框**：提供各种样式和功能的输入框
- **Textarea 文本域**：提供多行文本输入
- **Radio 单选框**：提供单选项选择
- **Checkbox 复选框**：提供多选项选择
- **Switch 开关**：提供开关选择
- **Slider 滑块**：提供范围选择
- **Select 选择器**：提供下拉选择
- **DatePicker 日期选择器**：提供日期选择
- **TimePicker 时间选择器**：提供时间选择
- **Form 表单**：提供表单容器和验证

### 反馈组件 (feedback)

反馈组件用于向用户提供操作反馈，包括：

- **Toast 轻提示**：提供轻量级的反馈提示
- **Modal 模态框**：提供模态对话框
- **ActionSheet 动作面板**：提供底部弹出的操作菜单
- **Loading 加载**：提供加载状态展示
- **Result 结果**：提供操作结果展示
- **Empty 空状态**：提供空数据状态展示
- **Skeleton 骨架屏**：提供内容加载骨架屏

### 导航组件 (navigation)

导航组件用于页面导航和内容切换，包括：

- **Navbar 导航栏**：提供顶部导航栏
- **Tabbar 标签栏**：提供底部标签栏
- **Tabs 标签页**：提供内容标签页切换
- **Sidebar 侧边栏**：提供侧边导航
- **Steps 步骤条**：提供步骤展示
- **Pagination 分页**：提供分页控制
- **BackTop 回到顶部**：提供回到顶部功能

### 布局组件 (layout)

布局组件用于页面结构和内容布局，包括：

- **Grid 栅格**：提供栅格布局
- **Flex 弹性布局**：提供弹性布局
- **List 列表**：提供各种样式的列表
- **Panel 面板**：提供内容面板
- **Collapse 折叠面板**：提供可折叠的内容面板
- **Sticky 粘性布局**：提供粘性定位布局
- **Swiper 轮播**：提供内容轮播

### 业务组件 (business)

业务组件是针对特定业务场景的组件，包括：

- **ThemeCard 主题卡片**：展示主题信息的卡片
- **LearningPlanCard 学习计划卡片**：展示学习计划的卡片
- **ContentCard 内容卡片**：展示练习、观点、笔记等内容的卡片
- **TagSelector 标签选择器**：提供标签选择功能
- **ProgressBar 进度条**：提供学习进度展示
- **Calendar 日历**：提供学习日历展示
- **BubbleView 泡泡视图**：提供泡泡样式的内容展示

## 使用方法

### 引入组件

在页面的 JSON 配置文件中引入需要的组件：

```json
{
  "usingComponents": {
    "nl-button": "/components/base/button/index",
    "nl-card": "/components/base/card/index",
    "nl-input": "/components/form/input/index"
  }
}
```

### 使用组件

在页面的 WXML 文件中使用组件：

```html
<!-- 按钮组件 -->
<nl-button type="primary" text="确认" bind:click="handleConfirm"></nl-button>

<!-- 卡片组件 -->
<nl-card title="卡片标题" subtitle="卡片副标题">
  <view>卡片内容</view>
</nl-card>

<!-- 输入框组件 -->
<nl-input label="用户名" placeholder="请输入用户名" bind:input="handleInput"></nl-input>
```

## 设计规范

组件库遵循 NebulaLearn 设计系统，包括：

- **色彩系统**：主色调、辅助色、状态色等
- **排版规范**：字体、字号、行高等
- **间距规范**：内边距、外边距等
- **圆角规范**：不同级别的圆角大小
- **阴影规范**：不同级别的阴影效果
- **动画与过渡规范**：过渡时间、过渡曲线等

详细规范请参考 `styles/design-system.js` 文件。

## 组件文档

每个组件都有详细的文档，包括：

- **属性说明**：组件的属性及其用途
- **事件说明**：组件触发的事件及其参数
- **插槽说明**：组件的插槽及其用途
- **使用示例**：组件的基本用法和高级用法

## 贡献指南

欢迎为组件库贡献代码，请遵循以下步骤：

1. Fork 仓库
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 版本历史

- **v1.0.0**：基础组件库发布，包含按钮、卡片、输入框等基础组件
- **v1.1.0**：添加表单组件和反馈组件
- **v1.2.0**：添加导航组件和布局组件
- **v1.3.0**：添加业务组件
