// components/base/badge/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 徽章内容
    content: {
      type: String,
      value: ''
    },
    // 徽章类型
    type: {
      type: String,
      value: 'default' // default, primary, success, warning, error, info
    },
    // 徽章尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否为圆点徽章
    dot: {
      type: Boolean,
      value: false
    },
    // 徽章位置
    position: {
      type: String,
      value: 'top-right' // top-right, top-left, bottom-right, bottom-left
    },
    // 徽章偏移量
    offset: {
      type: Array,
      value: [0, 0]
    },
    // 是否显示徽章
    show: {
      type: Boolean,
      value: true
    },
    // 最大值，超过最大值会显示 {max}+
    max: {
      type: Number,
      value: 99
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 格式化徽章内容
     */
    formatContent() {
      const { content, max, dot } = this.data;

      if (dot) return '';

      if (!content) return '';

      const contentNumber = parseInt(content, 10);

      if (isNaN(contentNumber)) return content;

      return contentNumber > max ? `${max}+` : `${contentNumber}`;
    },

    /**
     * 计算徽章样式
     */
    getBadgeStyle() {
      const { offset, position, customStyle } = this.data;
      const [x, y] = offset;

      let positionStyle = '';

      switch (position) {
        case 'top-right':
          positionStyle = `top: ${-y}rpx; right: ${-x}rpx;`;
          break;
        case 'top-left':
          positionStyle = `top: ${-y}rpx; left: ${-x}rpx;`;
          break;
        case 'bottom-right':
          positionStyle = `bottom: ${-y}rpx; right: ${-x}rpx;`;
          break;
        case 'bottom-left':
          positionStyle = `bottom: ${-y}rpx; left: ${-x}rpx;`;
          break;
        default:
          positionStyle = `top: ${-y}rpx; right: ${-x}rpx;`;
      }

      return `${positionStyle} ${customStyle}`;
    }
  }
});
