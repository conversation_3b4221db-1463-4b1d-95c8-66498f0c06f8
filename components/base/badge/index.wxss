/* 徽章容器 */
.nl-badge {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

/* 徽章内容 */
.nl-badge__content {
  display: inline-block;
}

/* 徽章基础样式 */
.nl-badge__badge {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--white);
  border-radius: var(--radius-full);
  transform: translate(50%, -50%);
  transform-origin: 100% 0%;
  z-index: 10;
}

/* 徽章尺寸 */
.nl-badge__badge-small {
  height: 16px;
  min-width: 16px;
  padding: 0 4px;
  font-size: var(--font-size-xs);
}

.nl-badge__badge-medium {
  height: 20px;
  min-width: 20px;
  padding: 0 6px;
  font-size: var(--font-size-xs);
}

.nl-badge__badge-large {
  height: 24px;
  min-width: 24px;
  padding: 0 8px;
  font-size: var(--font-size-sm);
}

/* 圆点徽章 */
.nl-badge__badge-dot {
  height: 8px;
  width: 8px;
  min-width: 8px;
  padding: 0;
  border-radius: var(--radius-full);
}

.nl-badge__badge-dot.nl-badge__badge-small {
  height: 6px;
  width: 6px;
  min-width: 6px;
}

.nl-badge__badge-dot.nl-badge__badge-large {
  height: 10px;
  width: 10px;
  min-width: 10px;
}

/* 徽章类型 */
.nl-badge__badge-default {
  background-color: var(--grey-500);
}

.nl-badge__badge-primary {
  background-color: var(--primary-color);
}

.nl-badge__badge-success {
  background-color: var(--success-color);
}

.nl-badge__badge-warning {
  background-color: var(--warning-color);
}

.nl-badge__badge-error {
  background-color: var(--error-color);
}

.nl-badge__badge-info {
  background-color: var(--info-color);
}
