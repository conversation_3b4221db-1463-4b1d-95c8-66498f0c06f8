/**
 * 按钮组件
 * 提供各种样式和状态的按钮
 */
import { classNames, styleObject } from '../../../utils/component-utils';
import designSystem from '../../../styles/design-system';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 按钮类型
    type: {
      type: String,
      value: 'default' // default, primary, secondary, success, warning, error, text, link
    },
    // 按钮尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否为块级按钮（占满容器宽度）
    block: {
      type: Boolean,
      value: false
    },
    // 是否为圆形按钮
    round: {
      type: Boolean,
      value: false
    },
    // 是否为朴素按钮（无背景色，有边框）
    plain: {
      type: Boolean,
      value: false
    },
    // 是否为禁用状态
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否为加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 图标名称
    icon: {
      type: String,
      value: ''
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 按钮内容
    text: {
      type: String,
      value: ''
    },
    // 表单提交类型
    formType: {
      type: String,
      value: '' // submit, reset
    },
    // 开放能力
    openType: {
      type: String,
      value: '' // 微信开放能力
    },
    // 会话来源
    sessionFrom: {
      type: String,
      value: ''
    },
    // 发送内容
    sendMessageTitle: {
      type: String,
      value: ''
    },
    // 发送内容图片
    sendMessagePath: {
      type: String,
      value: ''
    },
    // 发送内容描述
    sendMessageImg: {
      type: String,
      value: ''
    },
    // 显示会话内消息卡片
    showMessageCard: {
      type: Boolean,
      value: false
    },
    // 客服会话内消息卡片标题
    appParameter: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    colors: designSystem.colors,
    baseClass: 'nl-button'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击按钮事件
     */
    handleClick(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      this.triggerEvent('click', e);
    },

    /**
     * 获取错误信息
     */
    handleError(e) {
      this.triggerEvent('error', e.detail);
    },

    /**
     * 获取用户信息回调
     */
    handleGetUserInfo(e) {
      this.triggerEvent('getuserinfo', e.detail);
    },

    /**
     * 获取手机号回调
     */
    handleGetPhoneNumber(e) {
      this.triggerEvent('getphonenumber', e.detail);
    },

    /**
     * 打开设置页面回调
     */
    handleOpenSetting(e) {
      this.triggerEvent('opensetting', e.detail);
    },

    /**
     * 获取用户头像回调
     */
    handleChooseAvatar(e) {
      this.triggerEvent('chooseavatar', e.detail);
    },

    /**
     * 获取客服消息回调
     */
    handleContact(e) {
      this.triggerEvent('contact', e.detail);
    },

    /**
     * 获取发票抬头回调
     */
    handleChooseInvoiceTitle(e) {
      this.triggerEvent('chooseinvoicetitle', e.detail);
    },

    /**
     * 获取用户信息回调
     */
    handleLaunchApp(e) {
      this.triggerEvent('launchapp', e.detail);
    },

    /**
     * 计算按钮样式类
     */
    getButtonClass() {
      const { baseClass, type, size, plain, round, block, disabled, loading } = this.data;

      return classNames(baseClass, {
        [`${type}`]: type,
        [`${size}`]: size,
        'plain': plain,
        'round': round,
        'block': block,
        'disabled': disabled,
        'loading': loading
      });
    },

    /**
     * 计算按钮样式
     */
    getButtonStyle() {
      const { type, plain, disabled, colors } = this.data;
      const style = {};

      // 根据类型设置颜色
      if (type !== 'default' && type !== 'text' && type !== 'link') {
        const colorSet = colors[type] || colors.primary;

        if (plain) {
          style.color = disabled ? colors.grey[400] : colorSet.main;
          style.borderColor = disabled ? colors.grey[300] : colorSet.main;
          style.backgroundColor = 'transparent';
        } else {
          style.color = colorSet.contrastText;
          style.backgroundColor = disabled ? colors.grey[300] : colorSet.main;
        }
      }

      return style;
    }
  }
});
