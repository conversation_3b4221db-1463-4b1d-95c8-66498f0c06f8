<button
  class="{{getButtonClass()}}"
  style="{{getButtonStyle()}} {{customStyle}}"
  form-type="{{formType}}"
  open-type="{{openType}}"
  session-from="{{sessionFrom}}"
  send-message-title="{{sendMessageTitle}}"
  send-message-path="{{sendMessagePath}}"
  send-message-img="{{sendMessageImg}}"
  show-message-card="{{showMessageCard}}"
  app-parameter="{{appParameter}}"
  disabled="{{disabled}}"
  hover-class="nl-button--hover"
  hover-start-time="20"
  hover-stay-time="70"
  bindtap="handleClick"
  binderror="handleError"
  bindgetuserinfo="handleGetUserInfo"
  bindgetphonenumber="handleGetPhoneNumber"
  bindopensetting="handleOpenSetting"
  bindchooseavatar="handleChooseAvatar"
  bindcontact="handleContact"
  bindchooseinvoicetitle="handleChooseInvoiceTitle"
  bindlaunchapp="handleLaunchApp"
>
  <view class="nl-button__content">
    <!-- 加载图标 -->
    <view wx:if="{{loading}}" class="nl-button__loading">
      <view class="nl-loading"></view>
    </view>
    
    <!-- 图标 -->
    <view wx:elif="{{icon}}" class="nl-button__icon">
      <view class="nl-icon nl-icon-{{icon}}"></view>
    </view>
    
    <!-- 文本内容 -->
    <view class="nl-button__text">
      <block wx:if="{{text}}">{{text}}</block>
      <slot wx:else></slot>
    </view>
  </view>
</button>
