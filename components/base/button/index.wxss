/* 按钮基础样式 */
.nl-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin: 0;
  padding: 0 var(--space-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  border-radius: var(--radius-sm);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-normal) var(--easing-standard);
  -webkit-appearance: none;
  -webkit-tap-highlight-color: transparent;
  overflow: hidden;
  height: 36px;
  min-width: 64px;
  white-space: nowrap;
}

/* 按钮内容布局 */
.nl-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 按钮文本 */
.nl-button__text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 按钮图标 */
.nl-button__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-xs);
  font-size: var(--font-size-md);
}

/* 按钮加载状态 */
.nl-button__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-xs);
}

/* 加载动画 */
.nl-loading {
  display: inline-block;
  width: var(--font-size-md);
  height: var(--font-size-md);
  border: 2px solid currentColor;
  border-radius: var(--radius-full);
  border-top-color: transparent;
  animation: nl-loading 0.8s linear infinite;
}

@keyframes nl-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 按钮尺寸 */
.nl-button.small {
  height: 28px;
  padding: 0 var(--space-sm);
  font-size: var(--font-size-xs);
  min-width: 48px;
}

.nl-button.medium {
  height: 36px;
  padding: 0 var(--space-md);
  font-size: var(--font-size-sm);
  min-width: 64px;
}

.nl-button.large {
  height: 44px;
  padding: 0 var(--space-lg);
  font-size: var(--font-size-md);
  min-width: 80px;
}

/* 块级按钮 */
.nl-button.block {
  display: flex;
  width: 100%;
}

/* 圆形按钮 */
.nl-button.round {
  border-radius: var(--radius-full);
}

/* 按钮类型 */
.nl-button.default {
  color: var(--text-color-primary);
  background-color: var(--grey-100);
  border-color: var(--grey-300);
}

.nl-button.primary {
  color: var(--white);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.nl-button.secondary {
  color: var(--white);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.nl-button.success {
  color: var(--white);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.nl-button.warning {
  color: var(--white);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.nl-button.error {
  color: var(--white);
  background-color: var(--error-color);
  border-color: var(--error-color);
}

.nl-button.text {
  color: var(--primary-color);
  background-color: var(--transparent);
  border-color: var(--transparent);
  padding: 0;
  min-width: auto;
  height: auto;
}

.nl-button.link {
  color: var(--primary-color);
  background-color: var(--transparent);
  border-color: var(--transparent);
  padding: 0;
  min-width: auto;
  height: auto;
  text-decoration: underline;
}

/* 朴素按钮 */
.nl-button.plain {
  background-color: var(--transparent);
}

.nl-button.plain.default {
  color: var(--text-color-primary);
  border-color: var(--grey-300);
}

.nl-button.plain.primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.nl-button.plain.secondary {
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.nl-button.plain.success {
  color: var(--success-color);
  border-color: var(--success-color);
}

.nl-button.plain.warning {
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.nl-button.plain.error {
  color: var(--error-color);
  border-color: var(--error-color);
}

/* 禁用状态 */
.nl-button.disabled {
  color: var(--text-color-disabled);
  background-color: var(--grey-200);
  border-color: var(--grey-300);
  cursor: not-allowed;
  pointer-events: none;
}

.nl-button.disabled.plain {
  color: var(--text-color-disabled);
  background-color: var(--transparent);
  border-color: var(--grey-300);
}

.nl-button.disabled.text,
.nl-button.disabled.link {
  color: var(--text-color-disabled);
  background-color: var(--transparent);
  border-color: var(--transparent);
}

/* 加载状态 */
.nl-button.loading {
  cursor: default;
  pointer-events: none;
  opacity: 0.8;
}

/* 悬停效果 */
.nl-button--hover {
  opacity: 0.8;
  transition: opacity var(--transition-fast) var(--easing-standard);
}

/* 按钮点击效果 */
.nl-button:active {
  opacity: 0.9;
  transition: opacity var(--transition-fastest) var(--easing-standard);
}
