/**
 * 卡片组件
 * 提供各种样式的卡片容器
 */
import { classNames, styleObject } from '../../../utils/component-utils';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 卡片标题
    title: {
      type: String,
      value: ''
    },
    // 卡片副标题
    subtitle: {
      type: String,
      value: ''
    },
    // 是否显示卡片阴影
    shadow: {
      type: String,
      value: 'always' // always, hover, never
    },
    // 卡片圆角大小
    radius: {
      type: String,
      value: 'medium' // none, small, medium, large
    },
    // 是否显示边框
    border: {
      type: Boolean,
      value: true
    },
    // 是否为紧凑型卡片
    compact: {
      type: Boolean,
      value: false
    },
    // 卡片内边距
    padding: {
      type: String,
      value: 'medium' // none, small, medium, large
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    // 是否显示头部
    showHeader: {
      type: Boolean,
      value: true
    },
    // 是否显示底部
    showFooter: {
      type: Boolean,
      value: false
    },
    // 是否显示封面图
    showCover: {
      type: Boolean,
      value: false
    },
    // 封面图URL
    coverUrl: {
      type: String,
      value: ''
    },
    // 封面图高度
    coverHeight: {
      type: String,
      value: '160px'
    },
    // 封面图填充模式
    coverMode: {
      type: String,
      value: 'aspectFill' // scaleToFill, aspectFit, aspectFill, widthFix, heightFix
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-card'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击卡片事件
     */
    handleClick(e) {
      if (this.data.clickable) {
        this.triggerEvent('click', e);
      }
    },

    /**
     * 计算卡片样式类
     */
    getCardClass() {
      const { baseClass, shadow, radius, border, compact, padding, clickable } = this.data;

      return classNames(baseClass, {
        [`shadow-${shadow}`]: shadow,
        [`radius-${radius}`]: radius,
        'no-border': !border,
        'compact': compact,
        [`padding-${padding}`]: padding,
        'clickable': clickable
      });
    },

    /**
     * 计算卡片样式
     */
    getCardStyle() {
      const { customStyle } = this.data;
      return customStyle;
    },

    /**
     * 计算封面图样式
     */
    getCoverStyle() {
      const { coverHeight } = this.data;
      return `height: ${coverHeight};`;
    }
  }
});
