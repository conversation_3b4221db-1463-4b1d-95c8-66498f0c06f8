<view class="{{getCardClass()}}" style="{{getCardStyle()}}" bindtap="handleClick">
  <!-- 卡片封面 -->
  <view wx:if="{{showCover && coverUrl}}" class="nl-card__cover">
    <image 
      src="{{coverUrl}}" 
      mode="{{coverMode}}" 
      style="{{getCoverStyle()}}"
      class="nl-card__cover-image"
    />
  </view>
  
  <!-- 卡片头部 -->
  <view wx:if="{{showHeader && (title || subtitle)}}" class="nl-card__header">
    <view class="nl-card__header-left">
      <view wx:if="{{title}}" class="nl-card__title">{{title}}</view>
      <view wx:if="{{subtitle}}" class="nl-card__subtitle">{{subtitle}}</view>
    </view>
    <view class="nl-card__header-right">
      <slot name="header-right"></slot>
    </view>
  </view>
  
  <!-- 卡片内容 -->
  <view class="nl-card__content">
    <slot></slot>
  </view>
  
  <!-- 卡片底部 -->
  <view wx:if="{{showFooter}}" class="nl-card__footer">
    <slot name="footer"></slot>
  </view>
</view>
