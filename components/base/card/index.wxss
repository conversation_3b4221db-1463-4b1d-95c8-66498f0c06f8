/* 卡片基础样式 */
.nl-card {
  position: relative;
  box-sizing: border-box;
  background-color: var(--bg-color-card);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all var(--transition-normal) var(--easing-standard);
  margin-bottom: var(--space-md);
}

/* 卡片边框 */
.nl-card {
  border: 1px solid var(--divider-color);
}

.nl-card.no-border {
  border: none;
}

/* 卡片阴影 */
.nl-card.shadow-always {
  box-shadow: var(--shadow-sm);
}

.nl-card.shadow-hover {
  box-shadow: none;
}

.nl-card.shadow-hover.clickable:active {
  box-shadow: var(--shadow-sm);
}

.nl-card.shadow-never {
  box-shadow: none;
}

/* 卡片圆角 */
.nl-card.radius-none {
  border-radius: var(--radius-none);
}

.nl-card.radius-small {
  border-radius: var(--radius-sm);
}

.nl-card.radius-medium {
  border-radius: var(--radius-md);
}

.nl-card.radius-large {
  border-radius: var(--radius-lg);
}

/* 卡片内边距 */
.nl-card.padding-none .nl-card__content {
  padding: 0;
}

.nl-card.padding-small .nl-card__content {
  padding: var(--space-sm);
}

.nl-card.padding-medium .nl-card__content {
  padding: var(--space-md);
}

.nl-card.padding-large .nl-card__content {
  padding: var(--space-lg);
}

/* 紧凑型卡片 */
.nl-card.compact .nl-card__header {
  padding: var(--space-sm) var(--space-sm);
}

.nl-card.compact .nl-card__content {
  padding: var(--space-sm) var(--space-sm);
}

.nl-card.compact .nl-card__footer {
  padding: var(--space-sm) var(--space-sm);
}

/* 可点击卡片 */
.nl-card.clickable {
  cursor: pointer;
}

.nl-card.clickable:active {
  opacity: 0.9;
  transition: opacity var(--transition-fastest) var(--easing-standard);
}

/* 卡片封面 */
.nl-card__cover {
  width: 100%;
  overflow: hidden;
}

.nl-card__cover-image {
  width: 100%;
  display: block;
}

/* 卡片头部 */
.nl-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  border-bottom: 1px solid var(--divider-color);
}

.nl-card__header-left {
  flex: 1;
  overflow: hidden;
}

.nl-card__header-right {
  flex-shrink: 0;
  margin-left: var(--space-md);
}

.nl-card__title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  line-height: var(--line-height-snug);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nl-card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  line-height: var(--line-height-snug);
  margin-top: var(--space-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 卡片内容 */
.nl-card__content {
  padding: var(--space-md);
}

/* 卡片底部 */
.nl-card__footer {
  padding: var(--space-sm) var(--space-md);
  border-top: 1px solid var(--divider-color);
}
