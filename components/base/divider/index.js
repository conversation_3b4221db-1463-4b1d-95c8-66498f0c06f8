// components/base/divider/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 分割线方向
    direction: {
      type: String,
      value: 'horizontal' // horizontal, vertical
    },
    // 分割线类型
    type: {
      type: String,
      value: 'solid' // solid, dashed, dotted
    },
    // 分割线颜色
    color: {
      type: String,
      value: '' // 默认使用 --divider-color
    },
    // 分割线粗细
    size: {
      type: String,
      value: 'normal' // thin, normal, thick
    },
    // 分割线长度（水平方向时为宽度，垂直方向时为高度）
    length: {
      type: String,
      value: '100%'
    },
    // 分割线边距
    margin: {
      type: String,
      value: 'normal' // none, small, normal, large
    },
    // 是否带文字
    withText: {
      type: Boolean,
      value: false
    },
    // 文字位置
    textPosition: {
      type: String,
      value: 'center' // left, center, right
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算分割线样式
     */
    getDividerStyle() {
      const { direction, type, color, size, length, margin, customStyle } = this.data;

      let style = '';

      // 设置颜色
      if (color) {
        if (direction === 'horizontal') {
          style += `background-color: ${color};`;
        } else {
          style += `border-left-color: ${color};`;
        }
      }

      // 设置长度
      if (length) {
        if (direction === 'horizontal') {
          style += `width: ${length};`;
        } else {
          style += `height: ${length};`;
        }
      }

      // 添加自定义样式
      if (customStyle) {
        style += customStyle;
      }

      return style;
    }
  }
});
