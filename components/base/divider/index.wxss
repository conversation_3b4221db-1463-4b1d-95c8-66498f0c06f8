/* 分割线基础样式 */
.nl-divider {
  display: block;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 水平分割线 */
.nl-divider-horizontal {
  width: 100%;
  height: 1px;
  background-color: var(--divider-color);
  position: relative;
}

/* 垂直分割线 */
.nl-divider-vertical {
  height: 100%;
  width: 1px;
  background-color: var(--divider-color);
  display: inline-block;
}

/* 分割线类型 */
.nl-divider-solid {
  /* 默认实线 */
}

.nl-divider-dashed {
  background-color: transparent;
}

.nl-divider-dashed.nl-divider-horizontal {
  border-top: 1px dashed var(--divider-color);
}

.nl-divider-dashed.nl-divider-vertical {
  border-left: 1px dashed var(--divider-color);
}

.nl-divider-dotted {
  background-color: transparent;
}

.nl-divider-dotted.nl-divider-horizontal {
  border-top: 1px dotted var(--divider-color);
}

.nl-divider-dotted.nl-divider-vertical {
  border-left: 1px dotted var(--divider-color);
}

/* 分割线粗细 */
.nl-divider-thin {
  /* 默认1px */
}

.nl-divider-normal.nl-divider-horizontal {
  height: 1px;
}

.nl-divider-normal.nl-divider-vertical {
  width: 1px;
}

.nl-divider-thick.nl-divider-horizontal {
  height: 2px;
}

.nl-divider-thick.nl-divider-vertical {
  width: 2px;
}

/* 分割线边距 */
.nl-divider-margin-none {
  margin: 0;
}

.nl-divider-margin-small.nl-divider-horizontal {
  margin: var(--space-xs) 0;
}

.nl-divider-margin-small.nl-divider-vertical {
  margin: 0 var(--space-xs);
}

.nl-divider-margin-normal.nl-divider-horizontal {
  margin: var(--space-sm) 0;
}

.nl-divider-margin-normal.nl-divider-vertical {
  margin: 0 var(--space-sm);
}

.nl-divider-margin-large.nl-divider-horizontal {
  margin: var(--space-md) 0;
}

.nl-divider-margin-large.nl-divider-vertical {
  margin: 0 var(--space-md);
}

/* 带文字的分割线 */
.nl-divider-with-text {
  display: flex;
  align-items: center;
  white-space: nowrap;
  background-color: transparent;
  border: none;
}

.nl-divider-with-text::before,
.nl-divider-with-text::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--divider-color);
}

.nl-divider-with-text.nl-divider-dashed::before,
.nl-divider-with-text.nl-divider-dashed::after {
  background-color: transparent;
  border-top: 1px dashed var(--divider-color);
}

.nl-divider-with-text.nl-divider-dotted::before,
.nl-divider-with-text.nl-divider-dotted::after {
  background-color: transparent;
  border-top: 1px dotted var(--divider-color);
}

.nl-divider-with-text.nl-divider-thick::before,
.nl-divider-with-text.nl-divider-thick::after {
  height: 2px;
}

.nl-divider__text {
  padding: 0 var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}

/* 文字位置 */
.nl-divider-with-text-left::before {
  flex: 0 1 10%;
}

.nl-divider-with-text-left::after {
  flex: 1 1 auto;
}

.nl-divider-with-text-right::before {
  flex: 1 1 auto;
}

.nl-divider-with-text-right::after {
  flex: 0 1 10%;
}
