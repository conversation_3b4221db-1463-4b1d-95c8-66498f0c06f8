// components/base/icon/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图标名称
    name: {
      type: String,
      value: ''
    },
    // 图标尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 图标颜色
    color: {
      type: String,
      value: 'default' // default, primary, secondary, success, warning, error, white
    },
    // 是否激活状态
    active: {
      type: Boolean,
      value: false
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 图标路径
    iconPath: '',
    // 图标尺寸映射
    sizeMap: {
      small: 32, // 16px
      medium: 40, // 20px
      large: 48 // 24px
    }
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      this.updateIconPath();
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'name, active': function (name, active) {
      this.updateIconPath();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新图标路径
     */
    updateIconPath() {
      const { name, active } = this.data;
      if (!name) {
        this.setData({ iconPath: '' });
        return;
      }

      // 构建图标路径
      const suffix = active ? '-active' : '';
      const iconPath = `/assets/icons/new/${name}${suffix}.png`;

      this.setData({ iconPath });
    },

    /**
     * 图标点击事件
     */
    handleTap() {
      this.triggerEvent('click');
    }
  }
});
