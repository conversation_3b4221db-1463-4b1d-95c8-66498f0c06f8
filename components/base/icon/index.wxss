/* 图标基础样式 */
.nl-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 图标尺寸 */
.nl-icon-small {
  width: 32rpx;
  height: 32rpx;
}

.nl-icon-medium {
  width: 40rpx;
  height: 40rpx;
}

.nl-icon-large {
  width: 48rpx;
  height: 48rpx;
}

/* 图标图片 */
.nl-icon__image {
  width: 100%;
  height: 100%;
}

/* 图标颜色 - 通过滤镜实现 */
.nl-icon-color-default {
  filter: none;
}

.nl-icon-color-primary {
  filter: drop-shadow(0 0 0 var(--primary-color));
}

.nl-icon-color-secondary {
  filter: drop-shadow(0 0 0 var(--secondary-color));
}

.nl-icon-color-success {
  filter: drop-shadow(0 0 0 var(--success-color));
}

.nl-icon-color-warning {
  filter: drop-shadow(0 0 0 var(--warning-color));
}

.nl-icon-color-error {
  filter: drop-shadow(0 0 0 var(--error-color));
}

.nl-icon-color-white {
  filter: drop-shadow(0 0 0 var(--white));
}

/* 激活状态 */
.nl-icon-active {
  transform: scale(1.1);
}
