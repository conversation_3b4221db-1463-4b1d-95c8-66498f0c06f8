<!-- 优化图片组件 -->
<view class="optimized-image {{round ? 'round' : ''}} {{className}}" style="width: {{width}}rpx; height: {{height}}rpx; {{customStyle}}">
  <!-- 图片加载中占位 -->
  <view class="image-placeholder" wx:if="{{!loaded && showPlaceholder}}">
    <view class="placeholder-content" style="background-color: {{placeholderColor}};">
      <view class="loading-indicator" wx:if="{{loading}}"></view>
    </view>
  </view>
  
  <!-- 图片加载失败占位 -->
  <view class="image-error" wx:if="{{loadError && showError}}">
    <view class="error-icon">!</view>
    <view class="error-text" wx:if="{{showErrorText}}">加载失败</view>
  </view>
  
  <!-- 实际图片 -->
  <image
    class="image {{fadeIn ? 'fade-in' : ''}} {{loaded ? 'loaded' : ''}}"
    src="{{optimizedSrc}}"
    mode="{{mode}}"
    lazy-load="{{lazyLoad}}"
    show-menu-by-longpress="{{showMenuByLongpress}}"
    webp="{{webp}}"
    binderror="handleError"
    bindload="handleLoad"
    bindtap="handleTap"
  ></image>
</view>
