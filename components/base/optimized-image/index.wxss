/* 优化图片组件样式 */

.optimized-image {
  position: relative;
  overflow: hidden;
  background-color: var(--nl-color-background-light);
}

.optimized-image.round {
  border-radius: 50%;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.placeholder-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--nl-color-primary);
  animation: loading-spin 0.8s linear infinite;
}

.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--nl-color-background-light);
  z-index: 1;
}

.error-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: var(--nl-color-error);
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.error-text {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
}

.image {
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.image.loaded {
  opacity: 1;
}

.image.fade-in {
  animation: fade-in 0.3s ease forwards;
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
