// components/base/tag/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 标签文本
    text: {
      type: String,
      value: ''
    },
    // 标签类型
    type: {
      type: String,
      value: 'default' // default, primary, success, warning, error, info
    },
    // 标签尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否为朴素标签（无背景色，有边框）
    plain: {
      type: Boolean,
      value: false
    },
    // 是否为圆角标签
    round: {
      type: Boolean,
      value: false
    },
    // 是否为标记样式
    mark: {
      type: Boolean,
      value: false
    },
    // 是否可关闭
    closable: {
      type: Boolean,
      value: false
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击标签
     */
    handleTap() {
      this.triggerEvent('click');
    },

    /**
     * 点击关闭按钮
     */
    handleClose(e) {
      e.stopPropagation();
      this.triggerEvent('close');
    }
  }
});
