/* 标签基础样式 */
.nl-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 var(--space-sm);
  font-size: var(--font-size-xs);
  line-height: 1;
  color: var(--white);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) var(--easing-standard);
}

/* 标签尺寸 */
.nl-tag-small {
  height: 20px;
  font-size: var(--font-size-xs);
}

.nl-tag-medium {
  height: 24px;
  font-size: var(--font-size-xs);
}

.nl-tag-large {
  height: 28px;
  font-size: var(--font-size-sm);
}

/* 标签类型 */
.nl-tag-default {
  background-color: var(--grey-500);
}

.nl-tag-primary {
  background-color: var(--primary-color);
}

.nl-tag-success {
  background-color: var(--success-color);
}

.nl-tag-warning {
  background-color: var(--warning-color);
}

.nl-tag-error {
  background-color: var(--error-color);
}

.nl-tag-info {
  background-color: var(--info-color);
}

/* 朴素标签 */
.nl-tag-plain {
  background-color: var(--transparent);
  border: 1px solid currentColor;
}

.nl-tag-plain.nl-tag-default {
  color: var(--grey-600);
}

.nl-tag-plain.nl-tag-primary {
  color: var(--primary-color);
}

.nl-tag-plain.nl-tag-success {
  color: var(--success-color);
}

.nl-tag-plain.nl-tag-warning {
  color: var(--warning-color);
}

.nl-tag-plain.nl-tag-error {
  color: var(--error-color);
}

.nl-tag-plain.nl-tag-info {
  color: var(--info-color);
}

/* 圆角标签 */
.nl-tag-round {
  border-radius: var(--radius-full);
}

/* 标记样式 */
.nl-tag-mark {
  border-radius: 0 var(--radius-full) var(--radius-full) 0;
}

/* 标签文本 */
.nl-tag__text {
  line-height: 1;
}

/* 关闭按钮 */
.nl-tag__close {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--space-xs);
  font-size: var(--font-size-sm);
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background-color: rgba(0, 0, 0, 0.1);
  transition: all var(--transition-fast) var(--easing-standard);
}

.nl-tag__close:hover {
  background-color: rgba(0, 0, 0, 0.2);
}
