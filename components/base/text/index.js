// components/base/text/index.js
const classNames = function () {
  const args = Array.from(arguments);
  const classList = [];

  args.forEach(arg => {
    if (!arg) return;

    const argType = typeof arg;

    if (argType === 'string' || argType === 'number') {
      classList.push(arg);
    } else if (Array.isArray(arg) && arg.length) {
      const inner = classNames.apply(null, arg);
      if (inner) {
        classList.push(inner);
      }
    } else if (argType === 'object') {
      for (const key in arg) {
        if (Object.prototype.hasOwnProperty.call(arg, key) && arg[key]) {
          classList.push(key);
        }
      }
    }
  });

  return classList.join(' ');
};

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 文本类型
    type: {
      type: String,
      value: 'body1' // h1, h2, h3, h4, h5, h6, body1, body2, caption
    },
    // 文本颜色
    color: {
      type: String,
      value: 'primary' // primary, secondary, disabled, hint, accent, success, warning, error
    },
    // 文本对齐方式
    align: {
      type: String,
      value: 'left' // left, center, right
    },
    // 文本变换
    transform: {
      type: String,
      value: 'none' // none, uppercase, lowercase, capitalize
    },
    // 文本截断
    truncate: {
      type: Boolean,
      value: false
    },
    // 多行文本截断行数
    lines: {
      type: Number,
      value: 0
    },
    // 是否加粗
    bold: {
      type: Boolean,
      value: false
    },
    // 是否斜体
    italic: {
      type: Boolean,
      value: false
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-text'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算文本样式类
     */
    getTextClass() {
      const { baseClass, type, color, align, transform, truncate, lines, bold, italic } = this.data;

      return classNames(baseClass, {
        [`${baseClass}--${type}`]: type,
        [`${baseClass}--${color}`]: color,
        [`${baseClass}--${align}`]: align,
        [`${baseClass}--${transform}`]: transform && transform !== 'none',
        [`${baseClass}--truncate`]: truncate && !lines,
        [`${baseClass}--truncate-${lines}`]: truncate && lines > 0,
        [`${baseClass}--bold`]: bold,
        [`${baseClass}--italic`]: italic
      });
    },

    /**
     * 计算文本样式
     */
    getTextStyle() {
      const { customStyle } = this.data;
      return customStyle;
    }
  }
});
