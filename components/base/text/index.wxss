/* 文本基础样式 */
.nl-text {
  display: inline-block;
  color: var(--text-color-primary);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
}

/* 文本类型 */
.nl-text--h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.nl-text--h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.nl-text--h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.nl-text--h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.nl-text--h5 {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
}

.nl-text--h6 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
}

.nl-text--body1 {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.nl-text--body2 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.nl-text--caption {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

/* 文本颜色 */
.nl-text--primary {
  color: var(--text-color-primary);
}

.nl-text--secondary {
  color: var(--text-color-secondary);
}

.nl-text--disabled {
  color: var(--text-color-disabled);
}

.nl-text--hint {
  color: var(--text-color-hint);
}

.nl-text--accent {
  color: var(--primary-color);
}

.nl-text--success {
  color: var(--success-color);
}

.nl-text--warning {
  color: var(--warning-color);
}

.nl-text--error {
  color: var(--error-color);
}

/* 文本对齐方式 */
.nl-text--left {
  text-align: left;
}

.nl-text--center {
  text-align: center;
}

.nl-text--right {
  text-align: right;
}

/* 文本变换 */
.nl-text--uppercase {
  text-transform: uppercase;
}

.nl-text--lowercase {
  text-transform: lowercase;
}

.nl-text--capitalize {
  text-transform: capitalize;
}

/* 文本截断 */
.nl-text--truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nl-text--truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nl-text--truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nl-text--truncate-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nl-text--truncate-5 {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 文本样式 */
.nl-text--bold {
  font-weight: var(--font-weight-bold);
}

.nl-text--italic {
  font-style: italic;
}
