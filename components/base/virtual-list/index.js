/**
 * 虚拟列表组件
 * 用于高效渲染大量数据的列表，只渲染可视区域内的元素
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 列表数据
     */
    items: {
      type: Array,
      value: [],
      observer: function (newVal) {
        if (newVal && newVal.length > 0) {
          this._handleItemsChange();
        }
      }
    },

    /**
     * 每项的高度（固定高度模式）
     */
    itemHeight: {
      type: Number,
      value: 100
    },

    /**
     * 是否使用动态高度
     */
    dynamic: {
      type: Boolean,
      value: false
    },

    /**
     * 动态高度计算函数名称（需在页面中定义）
     */
    itemHeightFunc: {
      type: String,
      value: ''
    },

    /**
     * 缓冲区大小（可视区域上下额外渲染的数量）
     */
    bufferSize: {
      type: Number,
      value: 5
    },

    /**
     * 列表项的唯一标识字段
     */
    itemKey: {
      type: String,
      value: 'id'
    },

    /**
     * 是否显示滚动条
     */
    showScrollbar: {
      type: Boolean,
      value: true
    },

    /**
     * 是否允许回弹效果
     */
    bounces: {
      type: Boolean,
      value: true
    },

    /**
     * 是否正在加载更多
     */
    loading: {
      type: Boolean,
      value: false
    },

    /**
     * 是否没有更多数据
     */
    noMore: {
      type: Boolean,
      value: false
    },

    /**
     * 没有更多数据的提示文本
     */
    noMoreText: {
      type: String,
      value: '没有更多数据了'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 容器高度
    containerHeight: 0,
    // 总内容高度
    totalHeight: 0,
    // 可见区域起始索引
    startIndex: 0,
    // 可见区域结束索引
    endIndex: 0,
    // 可见区域的元素
    visibleItems: [],
    // 起始偏移量
    startOffset: 0,
    // 滚动位置
    scrollTop: 0,
    // 每个元素的高度缓存（动态高度模式）
    itemHeightCache: {},
    // 是否已初始化
    initialized: false
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function () {
      this._initVirtualList();
    },

    detached: function () {
      // 清除可能的定时器或监听器
      if (this._resizeObserver) {
        this._resizeObserver.disconnect();
        this._resizeObserver = null;
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化虚拟列表
     */
    _initVirtualList: function () {
      const query = this.createSelectorQuery();
      query.select('.virtual-list').boundingClientRect();
      query.exec(res => {
        if (res && res[0]) {
          const containerHeight = res[0].height;

          this.setData({
            containerHeight,
            initialized: true
          }, () => {
            this._calculateInitialVisibleItems();
          });

          // 尝试使用ResizeObserver监听容器大小变化
          this._observeResize();
        }
      });
    },

    /**
     * 监听容器大小变化
     */
    _observeResize: function () {
      if (typeof wx.createSelectorQuery().in(this).select('.virtual-list').node === 'function') {
        try {
          const query = wx.createSelectorQuery().in(this);
          query.select('.virtual-list').node().exec(res => {
            const container = res[0].node;
            if (container && typeof wx.createIntersectionObserver === 'function') {
              // 使用IntersectionObserver模拟ResizeObserver
              this._resizeObserver = wx.createIntersectionObserver(this);
              this._resizeObserver
                .relativeToViewport()
                .observe('.virtual-list', entries => {
                  if (entries && entries.intersectionRect) {
                    const { height } = entries.intersectionRect;
                    if (height && height !== this.data.containerHeight) {
                      this.setData({
                        containerHeight: height
                      }, () => {
                        this._calculateVisibleItems(this.data.scrollTop);
                      });
                    }
                  }
                });
            }
          });
        } catch (err) {
          console.error('监听容器大小变化失败:', err);
        }
      }
    },

    /**
     * 处理列表数据变化
     */
    _handleItemsChange: function () {
      if (!this.data.initialized) return;

      // 计算总高度
      this._calculateTotalHeight();

      // 计算可见元素
      this._calculateVisibleItems(this.data.scrollTop);
    },

    /**
     * 计算总高度
     */
    _calculateTotalHeight: function () {
      const { items, itemHeight, dynamic, itemHeightCache } = this.data;

      let totalHeight = 0;

      if (dynamic) {
        // 动态高度模式
        items.forEach((item, index) => {
          const key = item[this.data.itemKey];
          const height = itemHeightCache[key] || itemHeight;
          totalHeight += height;
        });
      } else {
        // 固定高度模式
        totalHeight = items.length * itemHeight;
      }

      this.setData({
        totalHeight
      });
    },

    /**
     * 计算初始可见元素
     */
    _calculateInitialVisibleItems: function () {
      const { items, itemHeight, containerHeight, bufferSize } = this.data;

      if (!items || items.length === 0 || containerHeight === 0) return;

      // 计算可见区域能显示的项目数
      const visibleCount = Math.ceil(containerHeight / itemHeight) + bufferSize * 2;
      const endIndex = Math.min(visibleCount, items.length);

      this.setData({
        visibleItems: items.slice(0, endIndex),
        endIndex,
        totalHeight: items.length * itemHeight
      });
    },

    /**
     * 计算可见元素
     * @param {number} scrollTop - 滚动位置
     */
    _calculateVisibleItems: function (scrollTop) {
      const { items, itemHeight, containerHeight, bufferSize, dynamic, itemHeightCache } = this.data;

      if (!items || items.length === 0 || containerHeight === 0) return;

      let startIndex = 0;
      let endIndex = 0;
      let startOffset = 0;

      if (dynamic) {
        // 动态高度模式
        let currentHeight = 0;
        let found = false;

        // 找到起始索引
        for (let i = 0; i < items.length; i++) {
          const key = items[i][this.data.itemKey];
          const height = itemHeightCache[key] || itemHeight;

          if (currentHeight + height > scrollTop && !found) {
            startIndex = Math.max(0, i - bufferSize);
            startOffset = this._getItemOffsetByIndex(startIndex);
            found = true;
          }

          currentHeight += height;

          if (found && currentHeight > scrollTop + containerHeight + bufferSize * itemHeight) {
            endIndex = Math.min(i + bufferSize, items.length);
            break;
          }
        }

        if (!found) {
          startIndex = 0;
          endIndex = Math.min(Math.ceil(containerHeight / itemHeight) + bufferSize * 2, items.length);
          startOffset = 0;
        }
      } else {
        // 固定高度模式
        startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
        const visibleCount = Math.ceil(containerHeight / itemHeight) + bufferSize * 2;
        endIndex = Math.min(startIndex + visibleCount, items.length);
        startOffset = startIndex * itemHeight;
      }

      // 只有当范围变化时才更新
      if (startIndex !== this.data.startIndex || endIndex !== this.data.endIndex) {
        this.setData({
          startIndex,
          endIndex,
          visibleItems: items.slice(startIndex, endIndex),
          startOffset
        });
      }
    },

    /**
     * 获取指定索引项的偏移量
     * @param {number} index - 索引
     * @returns {number} 偏移量
     */
    _getItemOffsetByIndex: function (index) {
      const { items, itemHeight, dynamic, itemHeightCache } = this.data;

      if (index <= 0) return 0;

      let offset = 0;

      if (dynamic) {
        // 动态高度模式
        for (let i = 0; i < index; i++) {
          const key = items[i][this.data.itemKey];
          const height = itemHeightCache[key] || itemHeight;
          offset += height;
        }
      } else {
        // 固定高度模式
        offset = index * itemHeight;
      }

      return offset;
    },

    /**
     * 更新项目高度缓存（动态高度模式）
     * @param {string} key - 项目的唯一标识
     * @param {number} height - 项目高度
     */
    updateItemHeight: function (key, height) {
      if (!this.data.dynamic) return;

      const itemHeightCache = { ...this.data.itemHeightCache };
      itemHeightCache[key] = height;

      this.setData({
        itemHeightCache
      }, () => {
        // 重新计算总高度
        this._calculateTotalHeight();
        // 重新计算可见元素
        this._calculateVisibleItems(this.data.scrollTop);
      });
    },

    /**
     * 处理滚动事件
     * @param {Object} e - 事件对象
     */
    handleScroll: function (e) {
      const { scrollTop } = e.detail;

      // 更新滚动位置
      this.setData({
        scrollTop
      });

      // 计算可见元素
      this._calculateVisibleItems(scrollTop);

      // 触发滚动事件
      this.triggerEvent('scroll', {
        scrollTop,
        scrollHeight: this.data.totalHeight,
        scrollBottom: scrollTop + this.data.containerHeight
      });

      // 检查是否需要加载更多
      this._checkNeedLoadMore(scrollTop);
    },

    /**
     * 检查是否需要加载更多
     * @param {number} scrollTop - 滚动位置
     */
    _checkNeedLoadMore: function (scrollTop) {
      const { containerHeight, totalHeight, loading, noMore } = this.data;

      // 如果正在加载或没有更多数据，不触发加载更多
      if (loading || noMore) return;

      // 滚动到底部前的阈值（预加载）
      const threshold = 200;

      // 如果滚动位置加上容器高度加上阈值大于总高度，触发加载更多
      if (scrollTop + containerHeight + threshold >= totalHeight) {
        this.triggerEvent('loadmore');
      }
    },

    /**
     * 处理项目点击事件
     * @param {Object} e - 事件对象
     */
    handleItemTap: function (e) {
      const { index, item } = e.currentTarget.dataset;

      this.triggerEvent('itemtap', {
        index,
        item
      });
    },

    /**
     * 滚动到指定索引
     * @param {number} index - 索引
     * @param {boolean} animated - 是否使用动画
     */
    scrollToIndex: function (index, animated = true) {
      if (index < 0 || index >= this.data.items.length) return;

      const offset = this._getItemOffsetByIndex(index);

      this.setData({
        scrollTop: offset,
        scrollWithAnimation: animated
      });
    },

    /**
     * 滚动到指定位置
     * @param {number} offset - 偏移量
     * @param {boolean} animated - 是否使用动画
     */
    scrollToOffset: function (offset, animated = true) {
      this.setData({
        scrollTop: offset,
        scrollWithAnimation: animated
      });
    },

    /**
     * 重置列表
     */
    reset: function () {
      this.setData({
        scrollTop: 0,
        startIndex: 0,
        endIndex: 0,
        visibleItems: [],
        startOffset: 0,
        itemHeightCache: {}
      }, () => {
        this._calculateInitialVisibleItems();
      });
    }
  }
});
