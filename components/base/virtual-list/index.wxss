/* 虚拟列表组件样式 */

.virtual-list {
  position: relative;
  width: 100%;
  height: 100%;
}

.virtual-list-container {
  width: 100%;
  height: 100%;
}

.virtual-list-phantom {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  will-change: transform;
}

.virtual-list-item {
  width: 100%;
  position: relative;
}

.virtual-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.loading-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 4rpx solid var(--nl-color-border);
  border-top-color: var(--nl-color-primary);
  animation: loading-spin 0.8s linear infinite;
  margin-right: 10rpx;
}

.loading-text {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
}

.virtual-list-no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
