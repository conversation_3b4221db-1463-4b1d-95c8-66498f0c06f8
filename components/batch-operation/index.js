/**
 * 批量操作组件
 * 提供通用的批量操作功能，包括批量选择、批量删除和批量恢复
 */
Component({
  properties: {
    // 操作类型：'delete'(删除) 或 'restore'(恢复)
    operationType: {
      type: String,
      value: 'delete'
    },
    // 是否处于选择模式
    isSelectMode: {
      type: Boolean,
      value: false
    },
    // 已选择的项目ID数组
    selectedIds: {
      type: Array,
      value: []
    },
    // 总项目数
    totalCount: {
      type: Number,
      value: 0
    },
    // 内容类型名称（用于显示）
    contentTypeName: {
      type: String,
      value: '内容'
    }
  },

  methods: {
    // 切换选择模式
    toggleSelectMode() {
      this.triggerEvent('toggleSelectMode');
    },

    // 全选/取消全选
    toggleSelectAll() {
      this.triggerEvent('toggleSelectAll');
    },

    // 批量操作
    handleBatchOperation() {
      const { selectedIds, operationType, contentTypeName } = this.properties;

      if (selectedIds.length === 0) {
        wx.showToast({ title: '请先选择内容', icon: 'none' });
        return;
      }

      const isDelete = operationType === 'delete';
      const title = isDelete ? '批量删除确认' : '批量恢复确认';
      const content = isDelete
        ? `确定要删除选中的${selectedIds.length}项${contentTypeName}吗？删除后可在回收站恢复。`
        : `确定要恢复选中的${selectedIds.length}项${contentTypeName}吗？`;

      wx.showModal({
        title,
        content,
        confirmColor: isDelete ? '#ff6b6b' : '#4a90e2',
        success: res => {
          if (res.confirm) {
            this.triggerEvent('batchOperation', {
              type: operationType,
              ids: selectedIds
            });
          }
        }
      });
    }
  }
});
