<!-- components/batch-operation/index.wxml -->
<view class="batch-operation-container">
  <!-- 操作按钮 -->
  <view class="operation-buttons">
    <view wx:if="{{!isSelectMode}}" class="action-btn select-mode-btn" bindtap="toggleSelectMode">
      <text class="icon">☑</text>
      <text>批量操作</text>
    </view>
    <view wx:else class="action-btn exit-select-btn" bindtap="toggleSelectMode">
      <text class="icon">✕</text>
      <text>退出选择</text>
    </view>
  </view>

  <!-- 批量操作工具栏 -->
  <view class="batch-toolbar" wx:if="{{isSelectMode}}">
    <view class="selection-info">已选择 {{selectedIds.length}} 项</view>
    <view class="batch-actions">
      <view class="batch-btn select-all-btn" bindtap="toggleSelectAll">
        {{selectedIds.length === totalCount ? '取消全选' : '全选'}}
      </view>
      <view class="batch-btn {{operationType === 'delete' ? 'delete-btn' : 'restore-btn'}}" bindtap="handleBatchOperation">
        {{operationType === 'delete' ? '批量删除' : '批量恢复'}}
      </view>
    </view>
  </view>
</view>
