/* components/batch-operation/index.wxss */
.batch-operation-container {
  width: 100%;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.action-btn .icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.select-mode-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.exit-select-btn {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}

/* 批量操作工具栏样式 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f0f0f0;
  border-bottom: 1rpx solid #dddddd;
  margin-top: 20rpx;
}

.selection-info {
  font-size: 28rpx;
  color: #666666;
}

.batch-actions {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.select-all-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.restore-btn {
  color: #50e3c2;
  background-color: rgba(80, 227, 194, 0.1);
}

.delete-btn {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}
