// components/bubble-canvas/index.js
// 泡泡画布组件

// 导入基础画布组件
const CanvasBase = require('../canvas-base/index');

// 导入性能优化工具
const OffscreenRenderer = require('./offscreen-renderer');
const ObjectPool = require('./object-pool');
const SpatialGrid = require('./spatial-grid');
const AdaptivePerformance = require('./adaptive-performance');

/**
 * 泡泡画布组件
 * 继承自基础画布组件，实现泡泡特有的功能
 */
class BubbleCanvas extends CanvasBase {
  /**
   * 构造函数
   */
  constructor() {
    super();

    // 泡泡特有配置
    this.bubbleConfig = {
      pulseAmplitude: 0.03, // 脉动幅度
      pulseFrequency: 0.001, // 脉动频率
      enableGlow: true, // 是否启用光晕效果
      textSizeRatio: 0.3, // 文字大小与泡泡半径的比例
      glowOpacity: 0.3, // 光晕不透明度
      hoverScale: 1.15 // 悬停时的缩放比例
    };

    // 性能优化工具
    this.offscreenRenderer = null;
    this.objectPool = null;
    this.spatialGrid = null;
    this.adaptivePerformance = null;

    // 性能监控
    this._frameCounter = 0;
    this._lastPerformanceCheck = 0;
    this._performanceCheckInterval = 1000; // 1秒检查一次性能
  }

  /**
   * 设置泡泡特有配置
   * @param {Object} bubbleConfig - 泡泡配置
   * @param {number} bubbleConfig.pulseAmplitude - 脉动幅度
   * @param {number} bubbleConfig.pulseFrequency - 脉动频率
   * @param {boolean} bubbleConfig.enableGlow - 是否启用光晕效果
   */
  setBubbleConfig(bubbleConfig) {
    this.bubbleConfig = { ...this.bubbleConfig, ...bubbleConfig };
  }

  /**
   * 获取泡泡元素
   * @returns {Array} 泡泡元素数组
   */
  getBubbles() {
    return this.elements;
  }

  /**
   * 添加泡泡
   * @param {Object} bubbleData - 泡泡数据
   * @returns {Object} 新创建的泡泡
   */
  addBubble(bubbleData) {
    const bubble = {
      id: `bubble-${this.elements.length}`,
      text: bubbleData.text || '新泡泡',
      color: bubbleData.color || this.getRandomColor(),
      x: bubbleData.x || this.canvasWidth / 2,
      y: bubbleData.y || this.canvasHeight / 2,
      radius: bubbleData.radius || 50,
      velocityX: (Math.random() - 0.5) * this.config.baseSpeed,
      velocityY: (Math.random() - 0.5) * this.config.baseSpeed,
      isHovered: false,
      isClicked: false,
      pulsePhase: Math.random() * Math.PI * 2,
      pulseSpeed: 0.001 + Math.random() * 0.002,
      rotationSpeed: (Math.random() - 0.5) * 0.001,
      lastX: bubbleData.x || this.canvasWidth / 2,
      lastY: bubbleData.y || this.canvasHeight / 2,
      stuckFrames: 0,
      collisionCount: 0,
      isAccelerated: false,
      originalVelocityX: 0,
      originalVelocityY: 0,
      isDragged: false
    };

    this.elements.push(bubble);
    return bubble;
  }

  /**
   * 移除泡泡
   * @param {string} id - 泡泡ID
   * @param {boolean} animate - 是否播放消失动画
   * @returns {boolean} 是否成功移除
   */
  removeBubble(id, animate = true) {
    const index = this.elements.findIndex(bubble => bubble.id === id);
    if (index !== -1) {
      if (animate) {
        // 开始消失动画
        this.elements[index].isDisappearing = true;
        this.elements[index].disappearProgress = 0;
        this.elements[index].lifespan = 0; // 设置生命周期为0，表示立即开始消失
        return true;
      } else {
        // 立即移除
        this.elements.splice(index, 1);
        return true;
      }
    }
    return false;
  }

  /**
   * 添加带有消失动画的泡泡
   * @param {Object} bubbleData - 泡泡数据
   * @param {number} lifespan - 生命周期（毫秒），默认为无限
   * @returns {Object} 新创建的泡泡
   */
  addTemporaryBubble(bubbleData, lifespan = 5000) {
    // 创建泡泡
    const bubble = this.addBubble(bubbleData);

    // 设置生命周期
    bubble.lifespan = lifespan;
    bubble.creationTime = Date.now();

    return bubble;
  }

  /**
   * 让所有泡泡开始消失动画
   */
  fadeOutAllBubbles() {
    this.elements.forEach(bubble => {
      if (!bubble.isDisappearing) {
        bubble.isDisappearing = true;
        bubble.disappearProgress = 0;
      }
    });
  }

  /**
   * 创建泡泡爆炸效果
   * @param {number} x - 爆炸中心X坐标
   * @param {number} y - 爆炸中心Y坐标
   * @param {string} color - 爆炸颜色
   * @param {number} particleCount - 粒子数量
   */
  createBubbleExplosion(x, y, color = '#3B82F6', particleCount = 15) {
    // 创建临时粒子泡泡
    for (let i = 0; i < particleCount; i++) {
      // 随机角度和速度
      const angle = Math.random() * Math.PI * 2;
      const speed = this.config.baseSpeed * (1 + Math.random() * 2);
      const distance = 20 + Math.random() * 30;

      // 计算初始位置 - 略微偏离爆炸中心
      const offsetX = Math.cos(angle) * 5;
      const offsetY = Math.sin(angle) * 5;

      // 创建小泡泡
      const particleBubble = {
        text: '', // 无文字
        color: color,
        x: x + offsetX,
        y: y + offsetY,
        radius: 5 + Math.random() * 10, // 小半径
        velocityX: Math.cos(angle) * speed,
        velocityY: Math.sin(angle) * speed
      };

      // 添加临时泡泡，生命周期1-2秒
      this.addTemporaryBubble(particleBubble, 1000 + Math.random() * 1000);
    }
  }

  /**
   * 获取随机颜色
   * @returns {string} 颜色值
   */
  getRandomColor() {
    const colors = [
      '#3B82F6', // blue
      '#6366F1', // indigo
      '#8B5CF6', // purple
      '#EC4899', // pink
      '#EF4444', // red
      '#F97316', // orange
      '#F59E0B', // amber
      '#EAB308', // yellow
      '#84CC16', // lime
      '#22C55E', // green
      '#10B981', // emerald
      '#14B8A6', // teal
      '#06B6D4', // cyan
      '#0EA5E9' // sky
    ];

    return colors[Math.floor(Math.random() * colors.length)];
  }

  /**
   * 初始化性能优化工具
   * @private
   */
  _initPerformanceTools() {
    try {
      // 获取设备性能信息
      let devicePerformance = 'medium';
      try {
        const systemInfo = wx.getSystemInfoSync();
        const { benchmarkLevel } = systemInfo;

        if (benchmarkLevel) {
          if (benchmarkLevel >= 50) {
            devicePerformance = 'high';
          } else if (benchmarkLevel >= 30) {
            devicePerformance = 'medium';
          } else {
            devicePerformance = 'low';
          }
        }
      } catch (e) {
        console.warn('获取设备性能信息失败:', e);
      }

      // 根据设备性能调整缓存大小
      let maxCacheSize = 50;
      let cleanupInterval = 30000;

      switch (devicePerformance) {
        case 'high':
          maxCacheSize = 80;
          cleanupInterval = 60000;
          break;
        case 'medium':
          maxCacheSize = 50;
          cleanupInterval = 30000;
          break;
        case 'low':
          maxCacheSize = 30;
          cleanupInterval = 15000;
          break;
      }

      // 初始化离屏渲染器
      this.offscreenRenderer = new OffscreenRenderer({
        dpr: this.dpr,
        maxCacheSize: maxCacheSize,
        cleanupInterval: cleanupInterval,
        frequencyThreshold: 3,
        adaptiveCaching: true
      });
      this.offscreenRenderer.init();

      // 设置优先缓存的尺寸和颜色
      const prioritySizes = [30, 40, 50, 60]; // 最常用的尺寸
      const priorityColors = ['#3B82F6', '#6366F1', '#8B5CF6', '#EF4444', '#F97316']; // 最常用的颜色
      this.offscreenRenderer.setPriorities(prioritySizes, priorityColors);

      // 初始化对象池
      this.objectPool = new ObjectPool({
        initialSize: devicePerformance === 'low' ? 30 : 50,
        maxSize: devicePerformance === 'low' ? 100 : 200,
        factory: () => this._createEmptyBubble(),
        reset: bubble => this._resetBubble(bubble)
      });
      this.objectPool.init();

      // 初始化空间分区管理器
      this.spatialGrid = new SpatialGrid({
        width: this.canvasWidth,
        height: this.canvasHeight,
        cellSize: devicePerformance === 'low' ? 150 : 100 // 低端设备使用更大的网格尺寸，减少计算量
      });

      // 初始化自适应性能管理器
      this.adaptivePerformance = new AdaptivePerformance({
        performanceMonitor: this.performanceMonitor,
        onModeChange: mode => this._handlePerformanceModeChange(mode)
      });
      this.adaptivePerformance.init();

      // 预渲染常用泡泡
      this._preRenderCommonBubbles();

      console.log('性能优化工具初始化完成', {
        devicePerformance,
        maxCacheSize,
        cleanupInterval
      });
      return true;
    } catch (err) {
      console.error('初始化性能优化工具失败:', err);
      return false;
    }
  }

  /**
   * 创建空泡泡对象（用于对象池）
   * @returns {Object} 空泡泡对象
   * @private
   */
  _createEmptyBubble() {
    return {
      id: `bubble-${Math.random().toString(36).substr(2, 9)}`,
      text: '',
      color: '',
      x: 0,
      y: 0,
      radius: 0,
      velocityX: 0,
      velocityY: 0,
      isHovered: false,
      isClicked: false,
      pulsePhase: 0,
      pulseSpeed: 0,
      rotationSpeed: 0,
      lastX: 0,
      lastY: 0,
      stuckFrames: 0,
      collisionCount: 0,
      isAccelerated: false,
      originalVelocityX: 0,
      originalVelocityY: 0,
      isDragged: false,
      creationTime: 0,
      lifespan: Infinity,
      opacity: 1,
      isDisappearing: false,
      disappearProgress: 0,
      cacheKey: '',
      needsRedraw: true,
      isActive: true,
      fromPool: true
    };
  }

  /**
   * 重置泡泡对象（用于对象池回收）
   * @param {Object} bubble - 泡泡对象
   * @returns {Object} 重置后的泡泡对象
   * @private
   */
  _resetBubble(bubble) {
    bubble.text = '';
    bubble.color = '';
    bubble.x = 0;
    bubble.y = 0;
    bubble.radius = 0;
    bubble.velocityX = 0;
    bubble.velocityY = 0;
    bubble.isHovered = false;
    bubble.isClicked = false;
    bubble.pulsePhase = 0;
    bubble.pulseSpeed = 0;
    bubble.rotationSpeed = 0;
    bubble.lastX = 0;
    bubble.lastY = 0;
    bubble.stuckFrames = 0;
    bubble.collisionCount = 0;
    bubble.isAccelerated = false;
    bubble.originalVelocityX = 0;
    bubble.originalVelocityY = 0;
    bubble.isDragged = false;
    bubble.creationTime = 0;
    bubble.lifespan = Infinity;
    bubble.opacity = 1;
    bubble.isDisappearing = false;
    bubble.disappearProgress = 0;
    bubble.cacheKey = '';
    bubble.needsRedraw = true;
    bubble.isActive = true;

    return bubble;
  }

  /**
   * 预渲染常用泡泡
   * @private
   */
  _preRenderCommonBubbles() {
    if (!this.offscreenRenderer) return;

    // 获取设备性能信息
    let devicePerformance = 'medium';
    try {
      if (this.adaptivePerformance) {
        devicePerformance = this.adaptivePerformance.devicePerformance || 'medium';
      }
    } catch (e) {
      console.warn('获取设备性能信息失败:', e);
    }

    // 根据设备性能调整预渲染数量
    let commonSizes, commonColors;

    switch (devicePerformance) {
      case 'high':
        // 高性能设备 - 预渲染更多尺寸和颜色
        commonSizes = [30, 35, 40, 45, 50, 55, 60, 65, 70];
        commonColors = [
          '#3B82F6', // blue
          '#6366F1', // indigo
          '#8B5CF6', // purple
          '#EC4899', // pink
          '#EF4444', // red
          '#F97316', // orange
          '#F59E0B', // amber
          '#EAB308', // yellow
          '#84CC16', // lime
          '#22C55E', // green
          '#10B981', // emerald
          '#14B8A6', // teal
          '#06B6D4', // cyan
          '#0EA5E9' // sky
        ];
        break;
      case 'medium':
        // 中等性能设备 - 预渲染常用尺寸和颜色
        commonSizes = [30, 40, 50, 60, 70];
        commonColors = [
          '#3B82F6', // blue
          '#6366F1', // indigo
          '#8B5CF6', // purple
          '#EC4899', // pink
          '#EF4444', // red
          '#F97316', // orange
          '#F59E0B', // amber
          '#EAB308', // yellow
          '#22C55E', // green
          '#0EA5E9' // sky
        ];
        break;
      case 'low':
        // 低性能设备 - 只预渲染最常用的尺寸和颜色
        commonSizes = [30, 45, 60];
        commonColors = [
          '#3B82F6', // blue
          '#6366F1', // indigo
          '#8B5CF6', // purple
          '#EF4444', // red
          '#F97316' // orange
        ];
        break;
      default:
        // 默认 - 中等预渲染
        commonSizes = [30, 40, 50, 60, 70];
        commonColors = [
          '#3B82F6', // blue
          '#6366F1', // indigo
          '#8B5CF6', // purple
          '#EF4444', // red
          '#F97316', // orange
          '#F59E0B', // amber
          '#22C55E' // green
        ];
    }

    // 预渲染常用泡泡
    this.offscreenRenderer.preRenderBubbles(
      commonSizes,
      commonColors,
      (ctx, radius, color) => this._drawBubbleToOffscreenCanvas(ctx, radius, color),
      true // 强制优先渲染
    );

    console.log(`预渲染泡泡完成，设备性能: ${devicePerformance}, 尺寸数量: ${commonSizes.length}, 颜色数量: ${commonColors.length}`);
  }

  /**
   * 绘制泡泡到离屏Canvas
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} radius - 泡泡半径
   * @param {string} color - 泡泡颜色
   * @private
   */
  _drawBubbleToOffscreenCanvas(ctx, radius, color) {
    // 创建径向渐变 - 调整高光位置，增强立体感
    const highlightOffsetX = radius * 0.25;
    const highlightOffsetY = radius * 0.4;

    const gradient = ctx.createRadialGradient(
      radius - highlightOffsetX, radius - highlightOffsetY, 0, // 内部更亮的点
      radius, radius, radius // 外部
    );

    // 设置渐变颜色
    const baseColor = color || '#4f46e5';
    const brightestColor = this.lightenColor(baseColor, 60);
    const lighterColor = this.lightenColor(baseColor, 30);
    const mediumColor = this.lightenColor(baseColor, 10);
    const darkerColor = this.darkenColor(baseColor, 15);

    gradient.addColorStop(0, brightestColor);
    gradient.addColorStop(0.2, lighterColor);
    gradient.addColorStop(0.5, mediumColor);
    gradient.addColorStop(0.8, baseColor);
    gradient.addColorStop(1, darkerColor);

    // 绘制泡泡主体
    ctx.beginPath();
    ctx.arc(radius, radius, radius, 0, Math.PI * 2);
    ctx.fillStyle = gradient;
    ctx.fill();
  }

  /**
   * 处理性能模式变更
   * @param {string} mode - 性能模式：'high', 'medium', 'low'
   * @private
   */
  _handlePerformanceModeChange(mode) {
    console.log(`性能模式改变为: ${mode}`);
    // 根据需要调整渲染细节或行为
    // 例如，可以减少粒子效果，或降低动画帧率
    if (mode === 'low') {
      this.config.enableComplexEffects = false;
      // this.setFPS(20); // 降低帧率
    } else {
      this.config.enableComplexEffects = true;
      // this.setFPS(30); // 恢复帧率
    }
  }

  /**
   * 创建泡泡元素
   * @param {Array} themes - 主题数据
   * @returns {Array} 泡泡元素数组
   * @protected
   */
  _createElements(themes) {
    // 如果没有传入主题数据，尝试使用getThemes方法获取
    if (!themes || themes.length === 0) {
      if (typeof this.getThemes === 'function') {
        themes = this.getThemes();
        console.log('使用getThemes方法获取主题数据:', themes.length);
      }
    }

    const bubbles = [];
    const count = themes.length;

    if (count === 0) {
      console.warn('主题数据为空，无法创建泡泡');
      return bubbles;
    }

    // 计算底部菜单的上边界位置和顶部导航的下边界位置
    const tabbarTop = this.canvasHeight; // 底部菜单上边界设为画布底部
    const navBottom = 0; // 顶部导航下边界

    // 使用黄金螺旋分布算法，使泡泡分布更加自然
    const goldenAngle = Math.PI * (3 - Math.sqrt(5)); // 黄金角约为137.5度
    const centerX = this.canvasWidth / 2;
    const centerY = (tabbarTop - navBottom) / 2 + navBottom;

    // 计算最大半径，确保泡泡不会太大
    const maxRadius = Math.min(this.canvasWidth, this.canvasHeight) * 0.15;
    const minRadius = Math.min(this.canvasWidth, this.canvasHeight) * 0.08;

    // 预计算泡泡大小，以便更好地分布
    const bubbleSizes = [];
    for (let i = 0; i < count; i++) {
      const theme = themes[i % themes.length];

      // 基础半径范围
      let baseRadius = minRadius + Math.random() * (maxRadius - minRadius);

      // 根据文字长度动态调整泡泡大小
      const textLength = theme.name.length;
      if (textLength > 3) {
        // 当文字长度超过3个字符时，增加泡泡半径
        // 每多一个字符，增加基础半径的5%
        baseRadius += baseRadius * (textLength - 3) * 0.05;

        // 如果文字需要换行显示，额外增加泡泡大小以提供足够的垂直空间
        if (textLength >= 4) {
          const lineCount = Math.ceil(textLength / 2);
          // 当有多行时，额外增加半径
          if (lineCount > 1) {
            baseRadius += baseRadius * 0.1 * (lineCount - 1);
          }
        }
      }

      // 限制最大半径
      const radius = Math.min(baseRadius, maxRadius);
      bubbleSizes.push({ theme, radius });
    }

    // 按照半径从大到小排序，先放置大泡泡，再放置小泡泡
    bubbleSizes.sort((a, b) => b.radius - a.radius);

    // 已放置的泡泡，用于碰撞检测
    const placedBubbles = [];

    // 放置泡泡
    for (let i = 0; i < bubbleSizes.length; i++) {
      const { theme, radius } = bubbleSizes[i];

      // 使用黄金螺旋分布
      let angle = i * goldenAngle;
      const distance = radius + Math.sqrt(i) * radius * 0.8; // 距离随索引增加而增加

      // 初始位置
      let x = centerX + Math.cos(angle) * distance;
      let y = centerY + Math.sin(angle) * distance;

      // 碰撞检测和位置调整
      let attempts = 0;
      const maxAttempts = 50;
      let isOverlapping = true;

      while (isOverlapping && attempts < maxAttempts) {
        isOverlapping = false;

        // 检查与已放置泡泡的碰撞
        for (const placedBubble of placedBubbles) {
          const dx = x - placedBubble.x;
          const dy = y - placedBubble.y;
          let distance = Math.sqrt(dx * dx + dy * dy); // Changed const to let
          const minDistance = radius + placedBubble.radius + 5; // 添加5像素的间隔

          if (distance < minDistance) {
            isOverlapping = true;

            // 调整位置 - 增加距离和角度
            angle += goldenAngle * 0.5;
            distance += radius * 0.2;
            x = centerX + Math.cos(angle) * distance;
            y = centerY + Math.sin(angle) * distance;
            break;
          }
        }

        // 检查是否超出边界
        if (x - radius < 0) x = radius + 5;
        if (x + radius > this.canvasWidth) x = this.canvasWidth - radius - 5;
        if (y - radius < navBottom) y = navBottom + radius + 5;
        if (y + radius > tabbarTop) y = tabbarTop - radius - 5;

        attempts++;
      }

      // 如果尝试多次仍无法放置，使用备用策略
      if (attempts >= maxAttempts) {
        // 使用网格布局作为备用
        const gridColumns = 3;
        const gridRows = Math.ceil(count / gridColumns);
        const cellWidth = this.canvasWidth / gridColumns;
        const cellHeight = (tabbarTop - navBottom) / gridRows;

        const col = i % gridColumns;
        const row = Math.floor(i / gridColumns);

        // 添加随机偏移
        const offsetX = (Math.random() - 0.5) * cellWidth * 0.4;
        const offsetY = (Math.random() - 0.5) * cellHeight * 0.4;

        x = cellWidth * (col + 0.5) + offsetX;
        y = navBottom + cellHeight * (row + 0.5) + offsetY;
      }

      // 创建泡泡对象
      const bubble = {
        id: `bubble-${i}`,
        text: theme.name, // 只保留主标签
        color: theme.color || this.getRandomColor(),
        x,
        y,
        radius,
        // 使用更自然的初始速度 - 从中心向外的方向
        velocityX: (x - centerX) * 0.001 + (Math.random() - 0.5) * this.config.baseSpeed * 0.5,
        velocityY: (y - centerY) * 0.001 + (Math.random() - 0.5) * this.config.baseSpeed * 0.5,
        // 其他属性
        isHovered: false,
        isClicked: false,
        pulsePhase: Math.random() * Math.PI * 2,
        pulseSpeed: 0.001 + Math.random() * 0.002,
        rotationSpeed: 0, // 移除旋转，使动画更流畅
        lastX: x,
        lastY: y,
        stuckFrames: 0,
        // 性能优化相关属性
        collisionCount: 0, // 碰撞计数
        isAccelerated: false, // 是否处于加速状态
        originalVelocityX: 0, // 原始X速度
        originalVelocityY: 0, // 原始Y速度
        isDragged: false, // 是否被拖动
        // 新增属性
        creationTime: Date.now(), // 创建时间
        lifespan: Infinity, // 生命周期，无限表示永久存在
        opacity: 1, // 透明度
        isDisappearing: false, // 是否正在消失
        disappearProgress: 0, // 消失进度 (0-1)
        // 缓存相关
        cacheKey: `bubble-${Math.round(radius)}-${theme.color}`, // 缓存键
        needsRedraw: true // 是否需要重新绘制
      };

      // 添加到已放置泡泡列表和结果列表
      placedBubbles.push(bubble);
      bubbles.push(bubble);

      // 使用对象池管理
      if (this.elementPool && this.elementPool.length > 0) {
        // 标记为从对象池获取
        bubble.fromPool = true;
      }
    }

    console.log(`创建了 ${bubbles.length} 个泡泡，使用黄金螺旋分布算法`);
    return bubbles;
  }

  /**
   * 更新单个泡泡元素的状态
   * @private
   * @param {Object} bubble - 泡泡对象
   * @param {number} deltaTime - 时间差
   */
  _updateElement(bubble, deltaTime) {
    // 如果泡泡被点击或正在被拖动，则不更新位置
    if (bubble.isClicked || bubble.isDragged) return;

    // 处理泡泡消失动画
    if (bubble.isDisappearing) {
      this._updateDisappearingBubble(bubble, deltaTime);
      return;
    }

    // 检查泡泡生命周期 - 只在每60帧检查一次，减少Date.now()调用
    if (bubble.lifespan !== Infinity && this._frameCounter % 60 === 0) {
      const age = Date.now() - bubble.creationTime;
      if (age >= bubble.lifespan) {
        // 开始消失动画
        bubble.isDisappearing = true;
        bubble.disappearProgress = 0;
        return;
      }
    }

    // 获取当前性能模式
    const performanceMode = this.adaptivePerformance ? this.adaptivePerformance.currentMode : 'medium';

    // 根据性能模式调整物理模拟复杂度
    const physicsConfig = {
      high: {
        applyDrag: true,
        applyGravity: true,
        checkStuck: true,
        applyRandomness: true,
        checkSpeed: true
      },
      medium: {
        applyDrag: true,
        applyGravity: true,
        checkStuck: this._frameCounter % 2 === 0, // 每2帧检查一次
        applyRandomness: true,
        checkSpeed: this._frameCounter % 2 === 0 // 每2帧检查一次
      },
      low: {
        applyDrag: true,
        applyGravity: false,
        checkStuck: this._frameCounter % 3 === 0, // 每3帧检查一次
        applyRandomness: this._frameCounter % 3 === 0, // 每3帧检查一次
        checkSpeed: this._frameCounter % 3 === 0 // 每3帧检查一次
      }
    }[performanceMode] || {
      applyDrag: true,
      applyGravity: true,
      checkStuck: true,
      applyRandomness: true,
      checkSpeed: true
    };

    // 1. 应用基础速度 - 使用整数乘法和加法，避免浮点运算
    const speedMultiplier = this.config.animationSpeedMultiplier;
    const dx = bubble.velocityX * deltaTime * speedMultiplier;
    const dy = bubble.velocityY * deltaTime * speedMultiplier;
    bubble.x += dx;
    bubble.y += dy;

    // 2. 脉动效果 - 只在需要时更新，减少三角函数计算
    // 每4帧更新一次脉动效果，对视觉影响不大但能减少计算量
    if (this._frameCounter % 4 === 0) {
      bubble.pulsePhase += bubble.pulseSpeed * deltaTime;
      if (bubble.pulsePhase > Math.PI * 2) {
        bubble.pulsePhase -= Math.PI * 2;
      }
    }

    // 3. 应用轻微的环境阻力，使运动更自然
    if (physicsConfig.applyDrag) {
      const drag = 0.99; // 阻力系数
      bubble.velocityX *= drag;
      bubble.velocityY *= drag;
    }

    // 4. 应用轻微的重力效果，使泡泡有向上漂浮的趋势
    if (physicsConfig.applyGravity) {
      const gravity = -0.00001 * deltaTime; // 负值表示向上的力
      bubble.velocityY += gravity;
    }

    // 5. 检测泡泡是否卡住 - 只在需要时检查
    if (physicsConfig.checkStuck) {
      const dx = Math.abs(bubble.x - bubble.lastX);
      const dy = Math.abs(bubble.y - bubble.lastY);
      if (dx < 0.1 && dy < 0.1) {
        bubble.stuckFrames++;
        if (bubble.stuckFrames > 5) {
          // 随机改变速度方向，防止卡住
          const angle = Math.random() * Math.PI * 2;
          const speed = this.config.baseSpeed * 0.75; // 使用固定值，减少随机数生成
          bubble.velocityX = Math.cos(angle) * speed;
          bubble.velocityY = Math.sin(angle) * speed;
          bubble.stuckFrames = 0;
        }
      } else {
        bubble.stuckFrames = 0;
      }
    }

    // 6. 使用帧计数器控制随机性，减少每帧的计算量
    // 使用泡泡ID的最后一个字符的ASCII码作为偏移量，使每个泡泡在不同的帧上添加随机性
    const bubbleIdOffset = bubble.id.charCodeAt(bubble.id.length - 1) % 60;
    if (physicsConfig.applyRandomness && this._frameCounter % 60 === bubbleIdOffset) {
      // 每60帧，每个泡泡在不同的帧上添加随机性
      const randomFactor = 0.005;
      bubble.velocityX += (Math.random() - 0.5) * randomFactor;
      bubble.velocityY += (Math.random() - 0.5) * randomFactor;
    }

    // 7. 限制最大速度 - 只在需要时检查
    if (physicsConfig.checkSpeed) {
      const maxSpeed = this.config.baseSpeed * 1.5;
      const vx2 = bubble.velocityX * bubble.velocityX;
      const vy2 = bubble.velocityY * bubble.velocityY;
      const currentSpeed = Math.sqrt(vx2 + vy2);

      if (currentSpeed > maxSpeed) {
        const speedRatio = maxSpeed / currentSpeed;
        bubble.velocityX *= speedRatio;
        bubble.velocityY *= speedRatio;
      } else if (currentSpeed < this.config.baseSpeed * 0.1) { // Moved else if to the same line
        const angle = Math.random() * Math.PI * 2;
        const boostSpeed = this.config.baseSpeed * 0.15; // 使用固定值，减少随机数生成
        bubble.velocityX += Math.cos(angle) * boostSpeed;
        bubble.velocityY += Math.sin(angle) * boostSpeed;
      }
    }

    // 记录上一帧位置
    bubble.lastX = bubble.x;
    bubble.lastY = bubble.y;

    // 标记需要重绘
    bubble.needsRedraw = true;
  }

  /**
   * 更新所有元素
   * @param {number} deltaTime - 时间增量
   * @protected
   * @override
   */
  _updateElements(deltaTime) {
    // 增加帧计数器
    this._frameCounter++;

    // 更新自适应性能
    if (this.adaptivePerformance) {
      this.adaptivePerformance.adapt();
    }

    // 获取当前性能模式
    const performanceMode = this.adaptivePerformance ? this.adaptivePerformance.currentMode : 'medium';

    // 根据性能模式调整更新策略
    const updateStrategy = {
      high: {
        updateAllElements: true,
        useVisibleElementsOnly: false,
        skipFrames: 0
      },
      medium: {
        updateAllElements: true,
        useVisibleElementsOnly: false,
        skipFrames: 0
      },
      low: {
        updateAllElements: false,
        useVisibleElementsOnly: true,
        skipFrames: 1
      }
    }[performanceMode] || {
      updateAllElements: true,
      useVisibleElementsOnly: false,
      skipFrames: 0
    };

    // 低性能模式下，每隔一帧才更新所有元素
    if (updateStrategy.skipFrames > 0 && this._frameCounter % (updateStrategy.skipFrames + 1) !== 0) {
      // 只更新必要的元素（被点击、拖动或消失中的元素）
      for (let i = 0; i < this.elements.length; i++) {
        const element = this.elements[i];
        if (element.isActive !== false && (element.isClicked || element.isDragged || element.isDisappearing)) {
          this._updateElement(element, deltaTime);
          this._handleElementCollision(element);
        }
      }
      return;
    }

    // 更新空间分区
    if (this.spatialGrid) {
      this.spatialGrid.updateSize(this.canvasWidth, this.canvasHeight);
      this.spatialGrid.addElements(this.elements);
    }

    // 确定要更新的元素
    let elementsToUpdate = this.elements;

    // 如果使用可见元素优化，只更新可见元素
    if (updateStrategy.useVisibleElementsOnly) {
      elementsToUpdate = this._getVisibleElements();

      // 确保被点击、拖动或消失中的元素也被更新
      for (let i = 0; i < this.elements.length; i++) {
        const element = this.elements[i];
        if (element.isActive !== false && (element.isClicked || element.isDragged || element.isDisappearing) && !elementsToUpdate.includes(element)) {
          elementsToUpdate.push(element);
        }
      }
    }

    // 更新元素
    for (let i = 0; i < elementsToUpdate.length; i++) {
      const element = elementsToUpdate[i];
      if (element.isActive !== false) {
        this._updateElement(element, deltaTime);
      }
    }

    // 使用空间分区检测碰撞
    if (this.spatialGrid) {
      this.spatialGrid.checkCollisions((elementA, elementB) => {
        // 计算碰撞响应
        this._handleElementsCollision(elementA, elementB);
        return true;
      });
    }

    // 处理边界碰撞
    for (let i = 0; i < elementsToUpdate.length; i++) {
      const element = elementsToUpdate[i];
      if (element.isActive !== false) {
        this._handleElementCollision(element);
      }
    }

    // 移除不活跃的元素
    this.elements = this.elements.filter(element => element.isActive !== false);

    // 定期清理缓存和对象池（每30帧一次）
    if (this._frameCounter % 30 === 0) {
      // 清理缓存
      if (this.offscreenRenderer) {
        this.offscreenRenderer.periodicCleanup();
      }

      // 清理对象池
      if (this.objectPool) {
        this.objectPool.periodicCleanup();
      }
    }

    // 定期检查性能
    const now = Date.now();
    if (now - this._lastPerformanceCheck > this._performanceCheckInterval) {
      this._checkPerformance();
      this._lastPerformanceCheck = now;
    }
  }

  /**
   * 检查性能
   * @private
   */
  _checkPerformance() {
    if (!this.performanceMonitor) return;

    // 获取性能指标
    const metrics = this.performanceMonitor.getMetrics();

    // 获取缓存统计信息
    let cacheStats = null;
    if (this.offscreenRenderer) {
      cacheStats = this.offscreenRenderer.getCacheStats();
    }

    // 获取对象池统计信息
    let poolStats = null;
    if (this.objectPool) {
      poolStats = this.objectPool.getStats();
    }

    // 获取空间分区统计信息
    let gridStats = null;
    if (this.spatialGrid) {
      gridStats = {
        collisionChecks: this.spatialGrid.collisionChecks || 0,
        potentialCollisions: this.spatialGrid.potentialCollisions || 0,
        actualCollisions: this.spatialGrid.actualCollisions || 0
      };
    }

    // 获取自适应性能统计信息
    let adaptiveStats = null;
    if (this.adaptivePerformance) {
      adaptiveStats = this.adaptivePerformance.getStats();
    }

    // 输出性能指标
    if (this._frameCounter % 300 === 0) {
      console.log('性能指标:', {
        fps: metrics.fps.current.toFixed(1),
        avgFps: metrics.fps.avg.toFixed(1),
        minFps: metrics.fps.min.toFixed(1),
        maxFps: metrics.fps.max.toFixed(1),
        frameTime: metrics.frameTime.current.toFixed(2) + 'ms',
        avgFrameTime: metrics.frameTime.avg.toFixed(2) + 'ms',
        elements: this.elements.length,
        performanceMode: this.adaptivePerformance ? this.adaptivePerformance.currentMode : 'unknown',
        devicePerformance: this.adaptivePerformance ? this.adaptivePerformance.devicePerformance : 'unknown',
        cache: cacheStats ? {
          size: cacheStats.size,
          maxSize: cacheStats.maxSize,
          hitRate: cacheStats.hitRate,
          priorityCount: cacheStats.priorityCount
        } : null,
        pool: poolStats ? {
          size: poolStats.size,
          maxSize: poolStats.maxSize,
          usageRate: poolStats.usageRate
        } : null,
        grid: gridStats
      });

      // 如果FPS低于阈值，输出更详细的信息
      if (metrics.fps.current < 45) {
        console.warn('性能警告: FPS低于45，详细信息:', {
          cache: cacheStats,
          pool: poolStats,
          grid: gridStats,
          adaptive: adaptiveStats
        });
      }
    }
  }

  /**
   * 处理元素之间的碰撞
   * @param {Object} elementA - 元素A
   * @param {Object} elementB - 元素B
   * @private
   */
  _handleElementsCollision(elementA, elementB) {
    // 如果任一元素被点击或拖动，跳过碰撞处理
    if (elementA.isClicked || elementA.isDragged || elementB.isClicked || elementB.isDragged) {
      return;
    }

    // 获取当前性能模式
    const performanceMode = this.adaptivePerformance ? this.adaptivePerformance.currentMode : 'medium';

    // 根据性能模式调整碰撞响应复杂度
    const collisionConfig = {
      high: {
        applyFullPhysics: true,
        simplifiedSeparation: false
      },
      medium: {
        applyFullPhysics: true,
        simplifiedSeparation: false
      },
      low: {
        applyFullPhysics: false,
        simplifiedSeparation: true
      }
    }[performanceMode] || {
      applyFullPhysics: true,
      simplifiedSeparation: false
    };

    // 计算元素间距离
    const dx = elementB.x - elementA.x;
    const dy = elementB.y - elementA.y;

    // 使用平方距离进行初步检测，避免开方运算
    const distanceSquared = dx * dx + dy * dy;
    const minDistanceSquared = (elementA.radius + elementB.radius) * (elementA.radius + elementB.radius);

    // 如果平方距离大于最小碰撞距离的平方，则不发生碰撞
    if (distanceSquared >= minDistanceSquared) {
      return;
    }

    // 确认发生碰撞，计算实际距离
    const distance = Math.sqrt(distanceSquared);
    const minDistance = elementA.radius + elementB.radius;

    // 计算碰撞法线
    // 避免除以零，如果距离为0，使用随机方向
    let nx, ny;
    if (distance > 0.0001) {
      nx = dx / distance;
      ny = dy / distance;
    } else {
      const angle = Math.random() * Math.PI * 2;
      nx = Math.cos(angle);
      ny = Math.sin(angle);
    }

    // 计算重叠距离
    const overlap = minDistance - distance;

    // 分离元素，防止重叠
    if (collisionConfig.simplifiedSeparation) {
      // 简化版本：只应用固定的分离距离
      const separationFactor = 0.5; // 固定分离因子
      elementA.x -= nx * separationFactor;
      elementA.y -= ny * separationFactor;
      elementB.x += nx * separationFactor;
      elementB.y += ny * separationFactor;
    } else {
      // 完整版本：根据重叠距离计算分离
      const separationX = nx * overlap * 0.5;
      const separationY = ny * overlap * 0.5;
      elementA.x -= separationX;
      elementA.y -= separationY;
      elementB.x += separationX;
      elementB.y += separationY;
    }

    // 应用物理碰撞响应
    if (collisionConfig.applyFullPhysics) {
      // 计算相对速度
      const dvx = elementB.velocityX - elementA.velocityX;
      const dvy = elementB.velocityY - elementA.velocityY;

      // 计算相对速度在碰撞法线上的投影
      const dotProduct = dvx * nx + dvy * ny;

      // 如果元素相对靠近（不是相对远离）
      if (dotProduct > 0) {
        // 计算碰撞冲量
        const restitution = 0.8; // 弹性系数
        const impulse = (1 + restitution) * dotProduct / 2;

        // 应用冲量
        elementA.velocityX += impulse * nx;
        elementA.velocityY += impulse * ny;
        elementB.velocityX -= impulse * nx;
        elementB.velocityY -= impulse * ny;
      }
    } else {
      // 简化版本：简单的速度交换
      const tempVX = elementA.velocityX;
      const tempVY = elementA.velocityY;
      elementA.velocityX = elementB.velocityX * 0.8;
      elementA.velocityY = elementB.velocityY * 0.8;
      elementB.velocityX = tempVX * 0.8;
      elementB.velocityY = tempVY * 0.8;
    }

    // 标记需要重绘
    elementA.needsRedraw = true;
    elementB.needsRedraw = true;
  }

  /**
   * 更新正在消失的泡泡
   * @param {Object} bubble - 泡泡对象
   * @param {number} deltaTime - 时间增量
   * @private
   */
  _updateDisappearingBubble(bubble, deltaTime) {
    // 消失动画持续时间（毫秒）
    const disappearDuration = 1000;

    // 更新消失进度
    bubble.disappearProgress += deltaTime / disappearDuration;

    // 更新透明度
    bubble.opacity = 1 - bubble.disappearProgress;

    // 更新大小 - 泡泡逐渐变小
    bubble.currentRadius = bubble.radius * (1 - bubble.disappearProgress * 0.5);

    // 添加上升效果
    bubble.y -= deltaTime * 0.05;

    // 如果消失完成，将泡泡标记为不活跃
    if (bubble.disappearProgress >= 1) {
      bubble.opacity = 0;
      bubble.isActive = false;

      // 如果使用对象池，将泡泡返回对象池
      if (typeof this._returnElementToPool === 'function') {
        this._returnElementToPool(bubble);
      }
    }
  }

  /**
   * 处理泡泡碰撞
   * @param {Object} bubble - 泡泡对象
   * @protected
   */
  _handleElementCollision(bubble) {
    // 碰撞检测 - 左右边界
    let hasCollided = false;

    if (bubble.x - bubble.radius < 0) {
      bubble.x = bubble.radius;
      bubble.velocityX = Math.abs(bubble.velocityX) * 0.8; // 减少反弹速度
      hasCollided = true;
    } else if (bubble.x + bubble.radius > this.canvasWidth) {
      bubble.x = this.canvasWidth - bubble.radius;
      bubble.velocityX = -Math.abs(bubble.velocityX) * 0.8; // 减少反弹速度
      hasCollided = true;
    }

    // 碰撞检测 - 上下边界
    if (bubble.y - bubble.radius < 0) {
      bubble.y = bubble.radius;
      bubble.velocityY = Math.abs(bubble.velocityY) * 0.8; // 减少反弹速度
      hasCollided = true;
    } else if (bubble.y + bubble.radius > this.canvasHeight) {
      bubble.y = this.canvasHeight - bubble.radius;
      bubble.velocityY = -Math.abs(bubble.velocityY) * 0.8; // 减少反弹速度
      hasCollided = true;
    }

    // 如果发生碰撞且泡泡处于加速状态，增加碰撞计数
    if (hasCollided && bubble.isAccelerated) {
      bubble.collisionCount++;

      // 如果碰撞次数达到3次，恢复正常速度
      if (bubble.collisionCount >= 3) {
        // 恢复到原始速度
        if (bubble.originalVelocityX !== 0 || bubble.originalVelocityY !== 0) {
          bubble.velocityX = bubble.originalVelocityX;
          bubble.velocityY = bubble.originalVelocityY;
        } else {
          // 如果没有原始速度记录，生成一个新的随机速度
          const angle = Math.random() * Math.PI * 2;
          bubble.velocityX = Math.cos(angle) * this.config.baseSpeed * 0.8;
          bubble.velocityY = Math.sin(angle) * this.config.baseSpeed * 0.8;
        }

        // 重置加速状态和碰撞计数
        bubble.isAccelerated = false;
        bubble.collisionCount = 0;
        bubble.originalVelocityX = 0;
        bubble.originalVelocityY = 0;
      }
    }
  }

  /**
   * 绘制单个泡泡
   * @param {Object} bubble - 泡泡对象
   * @protected
   */
  _drawElement(bubble) {
    const ctx = this.ctx;
    if (!ctx || !bubble) return;

    // 如果泡泡不活跃，跳过绘制
    if (bubble.isActive === false) return;

    // 如果泡泡不需要重绘且不是动态状态，跳过绘制
    if (!bubble.needsRedraw &&
        !bubble.isDisappearing &&
        !bubble.isHovered &&
        !bubble.isClicked &&
        !bubble.isDragged) {
      return;
    }

    // 计算实际半径
    let actualRadius;
    if (bubble.isDisappearing) {
      // 如果正在消失，使用当前半径
      actualRadius = bubble.currentRadius || bubble.radius;
    } else {
      // 正常情况下，应用脉动效果
      const pulseScale = 1 + Math.sin(bubble.pulsePhase) * this.bubbleConfig.pulseAmplitude;
      const scaleMultiplier = bubble.isHovered ? this.bubbleConfig.hoverScale : pulseScale;
      actualRadius = bubble.radius * scaleMultiplier;
    }

    // 设置透明度
    const opacity = bubble.isDisappearing ? bubble.opacity : 1.0;

    // 保存当前状态
    ctx.save();
    ctx.globalAlpha = opacity;

    // 尝试使用离屏渲染器
    if (this.offscreenRenderer && !bubble.isDisappearing && !bubble.isHovered && !bubble.isClicked && !bubble.isDragged) {
      // 获取缓存的泡泡
      const cachedCanvas = this.offscreenRenderer.getCachedBubble(bubble.radius, bubble.color);

      if (cachedCanvas) {
        // 计算绘制位置（考虑元素半径）
        const drawX = bubble.x - actualRadius;
        const drawY = bubble.y - actualRadius;

        // 绘制缓存的图像
        ctx.drawImage(
          cachedCanvas,
          0, 0,
          actualRadius * 2 * this.dpr, actualRadius * 2 * this.dpr,
          drawX, drawY,
          actualRadius * 2, actualRadius * 2
        );

        // 绘制文字（文字不包含在缓存中）
        this._drawBubbleText(ctx, bubble, actualRadius);

        // 重置重绘标记
        bubble.needsRedraw = false;

        ctx.restore();
        return;
      } else {
        // 如果缓存中没有，添加到缓存
        // 检查是否为优先项
        const isPriority =
          (this.offscreenRenderer.prioritySizes && this.offscreenRenderer.prioritySizes.includes(Math.round(bubble.radius))) ||
          (this.offscreenRenderer.priorityColors && this.offscreenRenderer.priorityColors.includes(bubble.color));

        this.offscreenRenderer.addBubbleToCache(
          bubble.radius,
          bubble.color,
          (ctx, radius, color) => this._drawBubbleToOffscreenCanvas(ctx, radius, color),
          isPriority
        );
      }
    }

    // 如果没有缓存或不适合使用缓存，直接绘制
    this._drawBubbleDirect(ctx, bubble, actualRadius, opacity);

    // 重置重绘标记
    bubble.needsRedraw = false;

    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 绘制所有元素
   * @protected
   * @override
   */
  _drawElements() {
    // 如果没有上下文，直接返回
    if (!this.ctx) return;

    // 清空画布
    this._clearCanvas();

    // 使用视口裁剪优化
    const visibleElements = this._getVisibleElements();

    // 按Z轴排序元素
    const sortedElements = visibleElements.sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0));

    // 绘制所有可见元素
    for (let i = 0; i < sortedElements.length; i++) {
      this._drawElement(sortedElements[i]);
    }
  }

  /**
   * 获取可见元素
   * @returns {Array} 可见元素数组
   * @private
   */
  _getVisibleElements() {
    // 如果没有空间分区管理器，使用简单的视口裁剪
    if (!this.spatialGrid) {
      // 扩展视口范围，增加缓冲区
      const bufferSize = 100; // 缓冲区大小
      const extendedViewport = {
        left: -bufferSize,
        top: -bufferSize,
        right: this.canvasWidth + bufferSize,
        bottom: this.canvasHeight + bufferSize
      };

      // 过滤出可见元素
      return this.elements.filter(element =>
        // 检查元素是否在扩展视口内
        (
          element.x + element.radius > extendedViewport.left &&
          element.x - element.radius < extendedViewport.right &&
          element.y + element.radius > extendedViewport.top &&
          element.y - element.radius < extendedViewport.bottom
        )
      );
    }

    // 使用空间分区管理器进行更高效的视口裁剪
    // 更新空间分区
    this.spatialGrid.updateSize(this.canvasWidth, this.canvasHeight);
    this.spatialGrid.addElements(this.elements);

    // 获取视口范围内的网格单元格
    const bufferSize = Math.max(100, this.spatialGrid.cellSize); // 缓冲区大小至少为一个网格单元格
    const extendedViewport = {
      left: -bufferSize,
      top: -bufferSize,
      right: this.canvasWidth + bufferSize,
      bottom: this.canvasHeight + bufferSize
    };

    // 计算视口覆盖的网格单元格范围
    const minGridX = Math.floor(extendedViewport.left / this.spatialGrid.cellSize);
    const maxGridX = Math.ceil(extendedViewport.right / this.spatialGrid.cellSize);
    const minGridY = Math.floor(extendedViewport.top / this.spatialGrid.cellSize);
    const maxGridY = Math.ceil(extendedViewport.bottom / this.spatialGrid.cellSize);

    // 收集视口范围内的所有元素
    const visibleElements = new Set();

    // 遍历视口覆盖的网格单元格
    for (let gx = minGridX; gx <= maxGridX; gx++) {
      for (let gy = minGridY; gy <= maxGridY; gy++) {
        const key = `${gx},${gy}`;
        const cellElements = this.spatialGrid.grid[key] || [];

        // 将单元格中的元素添加到可见元素集合
        cellElements.forEach(element => {
          visibleElements.add(element);
        });
      }
    }

    // 转换为数组并返回
    return Array.from(visibleElements);
  }

  /**
   * 直接绘制泡泡（不使用缓存）
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {Object} bubble - 泡泡对象
   * @param {number} actualRadius - 实际半径
   * @param {number} opacity - 透明度
   * @private
   */
  _drawBubbleDirect(ctx, bubble, actualRadius, opacity) {
    // 创建径向渐变 - 调整高光位置，增强立体感
    // 高光位置偏移到左上方偏上，更符合圆形物体的光照规律
    const highlightOffsetX = actualRadius * 0.25; // 水平偏移量减小
    const highlightOffsetY = actualRadius * 0.4; // 垂直偏移量增大，使高光偏上

    const gradient = ctx.createRadialGradient(
      bubble.x - highlightOffsetX, bubble.y - highlightOffsetY, 0, // 内部更亮的点，从左上方偏上偏移
      bubble.x, bubble.y, actualRadius // 外部
    );

    // 设置渐变颜色 - 使用更多的颜色停靠点，增强光照效果和立体感
    const baseColor = bubble.color || '#4f46e5'; // 默认颜色，避免空值
    const brightestColor = this.lightenColor(baseColor, 60); // 高光区域，非常亮
    const lighterColor = this.lightenColor(baseColor, 30); // 过渡区域，较亮
    const mediumColor = this.lightenColor(baseColor, 10); // 中间亮度
    const darkerColor = this.darkenColor(baseColor, 15); // 边缘更暗，增强对比

    // 调整颜色停靠点分布，使高光区域更集中，边缘过渡更自然
    gradient.addColorStop(0, brightestColor); // 高光中心点，非常亮
    gradient.addColorStop(0.2, lighterColor); // 高光过渡区域
    gradient.addColorStop(0.5, mediumColor); // 中间过渡色
    gradient.addColorStop(0.8, baseColor); // 基础颜色
    gradient.addColorStop(1, darkerColor); // 边缘更暗

    // 设置阴影 - 增强泡泡的浮动感和立体感
    if (!bubble.isDisappearing) {
      // 使用基础颜色的半透明阴影，增强深度感
      ctx.shadowColor = this.hexToRgba(darkerColor, 0.5);
      // 增大模糊半径，使阴影更柔和
      ctx.shadowBlur = this.config.shadowBlur + 4;
      // 添加微小的向下偏移，增强悬浮感
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = actualRadius * 0.05;
    } else {
      // 消失动画时使用更轻的阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
    }

    // 绘制泡泡主体
    ctx.beginPath();
    ctx.arc(bubble.x, bubble.y, actualRadius, 0, Math.PI * 2);
    ctx.fillStyle = gradient;
    ctx.fill();

    // 如果泡泡被点击，绘制一个高亮边框
    if (bubble.isClicked) {
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 2;
      ctx.stroke();
    }

    // 如果泡泡正在被拖动，绘制拖动效果
    if (bubble.isDragged) {
      this._drawBubbleGlowEffects(ctx, bubble.x, bubble.y, actualRadius, baseColor);
    }

    // 绘制泡泡文字
    this._drawBubbleText(ctx, bubble, actualRadius);

    // 如果泡泡正在消失，添加粒子效果
    if (bubble.isDisappearing && bubble.disappearProgress < 0.7) {
      this._drawDisappearParticles(ctx, bubble);
    }
  }

  /**
   * 绘制泡泡文字
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {Object} bubble - 泡泡对象
   * @param {number} actualRadius - 实际半径
   * @private
   */
  _drawBubbleText(ctx, bubble, actualRadius) {
    // 移除文字阴影
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // 设置文字样式
    ctx.fillStyle = '#FFFFFF'; // 文字颜色
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // 根据泡泡大小调整字体大小
    const fontSize = Math.max(12, Math.min(24, actualRadius * this.bubbleConfig.textSizeRatio));
    ctx.font = `${fontSize}px Arial, sans-serif`;

    // 获取文字长度
    const textLength = bubble.text.length;

    // 检查文字长度，四个字及以上时考虑换行处理
    if (textLength >= 4) {
      // 文字换行处理 - 按照2个字一行进行均匀分割
      const lines = [];
      for (let i = 0; i < textLength; i += 2) {
        lines.push(bubble.text.substr(i, 2));
      }

      // 绘制多行文字
      lines.forEach((line, index) => {
        const lineCount = lines.length;
        // 调整行间距系数为1.1，保持适中的间距
        const lineSpacing = fontSize * 1.1;
        // 计算每行的垂直位置，使整体垂直居中
        const lineY = bubble.y - ((lineCount - 1) * lineSpacing / 2) + index * lineSpacing;
        ctx.fillText(line, bubble.x, lineY);
      });
    } else {
      // 对于3个字及以下的情况，保持单行显示
      ctx.fillText(bubble.text, bubble.x, bubble.y);
    }
  }

  /**
   * 绘制消失粒子效果
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {Object} bubble - 泡泡对象
   * @private
   */
  _drawDisappearParticles(ctx, bubble) {
    // 粒子数量随消失进度减少
    const particleCount = Math.max(5, Math.floor(10 * (1 - bubble.disappearProgress)));

    // 粒子大小
    const particleSize = bubble.radius * 0.1;

    // 粒子扩散范围随消失进度增加
    const spreadRange = bubble.radius * (0.5 + bubble.disappearProgress * 1.5);

    // 保存当前状态
    ctx.save();

    // 设置混合模式
    ctx.globalCompositeOperation = 'lighter';

    // 绘制粒子
    for (let i = 0; i < particleCount; i++) {
      // 计算粒子位置 - 随机分布在泡泡周围
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * spreadRange;
      const particleX = bubble.x + Math.cos(angle) * distance;
      const particleY = bubble.y + Math.sin(angle) * distance - bubble.disappearProgress * bubble.radius; // 向上飘动

      // 粒子透明度随消失进度减少
      const particleAlpha = (1 - bubble.disappearProgress) * 0.7;

      // 绘制粒子
      ctx.beginPath();
      ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
      ctx.fillStyle = this.hexToRgba(bubble.color, particleAlpha);
      ctx.fill();
    }

    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 绘制泡泡光晕效果
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} radius - 泡泡半径
   * @param {string} color - 基础颜色
   * @private
   */
  _drawBubbleGlowEffects(ctx, x, y, radius, color) {
    try {
      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;

      // 计算脉动效果
      const now = Date.now();
      const pulseValue = Math.sin(now * 0.005) * 0.2 + 0.8; // 0.6-1.0之间脉动

      // 保存当前状态
      ctx.save();

      // 设置混合模式为叠加，使光晕更亮
      ctx.globalCompositeOperation = 'lighter';

      // 创建由内而外的光晕效果
      // 泡泡外部的最大半径，随脉动变化
      const outerRadius = radius * (1.3 + pulseValue * 0.2);

      // 创建径向渐变，从泡泡边缘开始向外扩散
      const glowGradient = ctx.createRadialGradient(
        x, y, radius * 0.95, // 从泡泡边缘的里面一点开始
        x, y, outerRadius // 到外部最大半径
      );

      // 设置光晕颜色，由内而外透明度逐渐降低
      const alphaBase = 0.5 * pulseValue; // 透明度随脉动变化
      glowGradient.addColorStop(0, this.hexToRgba(color, alphaBase)); // 边缘处最亮
      glowGradient.addColorStop(0.3, this.hexToRgba(color, alphaBase * 0.6)); // 快速衰减
      glowGradient.addColorStop(0.7, this.hexToRgba(color, alphaBase * 0.2)); // 继续衰减
      glowGradient.addColorStop(1, this.hexToRgba(color, 0)); // 完全透明

      // 绘制光晕效果，但不覆盖泡泡内部
      // 先绘制一个大圆，包含整个光晕区域
      ctx.beginPath();
      ctx.arc(x, y, outerRadius, 0, Math.PI * 2);
      ctx.fillStyle = glowGradient;
      ctx.fill();

      // 恢复上下文状态
      ctx.restore();
    } catch (err) {
      console.error('绘制发光效果时出错', err);
      // 出错时不绘制发光效果
    }
  }

  /**
   * 绘制光环周围的光点粒子
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} innerRadius - 内半径
   * @param {number} outerRadius - 外半径
   * @param {string} color - 基础颜色
   * @param {number} pulseValue - 脉动值
   * @private
   */
  _drawGlowParticles(ctx, x, y, innerRadius, outerRadius, color, pulseValue) {
    try {
      // 光点数量 - 与旧组件保持一致
      const particleCount = 6;

      // 内外半径差
      const radiusRange = outerRadius - innerRadius;

      // 保存当前状态
      ctx.save();

      // 设置混合模式为叠加，使光点更亮
      ctx.globalCompositeOperation = 'lighter';

      // 绘制光点
      for (let i = 0; i < particleCount; i++) {
        // 计算光点位置 - 在内外圆之间随机分布，但更靠近泡泡
        const angle = (i / particleCount) * Math.PI * 2 + (Date.now() * 0.001) % (Math.PI * 2);
        const distance = innerRadius + radiusRange * 0.5 * Math.random(); // 减小分布范围
        const particleX = x + Math.cos(angle) * distance;
        const particleY = y + Math.sin(angle) * distance;

        // 光点大小随脉动变化，整体减小
        const particleSize = 1.5 + 2 * pulseValue * Math.random();

        // 光点透明度随脉动变化
        const particleAlpha = 0.3 * pulseValue * (1 - distance / outerRadius);

        // 创建光点渐变
        const particleGradient = ctx.createRadialGradient(
          particleX, particleY, 0,
          particleX, particleY, particleSize
        );

        // 设置光点颜色 - 使用泡泡自身的颜色
        const particleColor = this.lightenColor(color, 50); // 使用更亮的颜色
        particleGradient.addColorStop(0, this.hexToRgba(particleColor, particleAlpha));
        particleGradient.addColorStop(1, this.hexToRgba(particleColor, 0));

        // 绘制光点
        ctx.beginPath();
        ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
        ctx.fillStyle = particleGradient;
        ctx.fill();
      }

      // 恢复上下文状态
      ctx.restore();
    } catch (err) {
      console.error('绘制光点粒子时出错', err);
      // 出错时不绘制光点
    }
  }

  /**
   * 将十六进制颜色转换为RGBA格式
   * @param {string} hex - 十六进制颜色
   * @param {number} alpha - 透明度
   * @returns {string} - RGBA格式的颜色
   */
  hexToRgba(hex, alpha) {
    try {
      if (!hex || typeof hex !== 'string') {
        return `rgba(0, 0, 0, ${alpha || 1})`;
      }

      // 处理简写形式
      let cleanHex = hex.replace('#', '');
      if (cleanHex.length === 3) {
        cleanHex = cleanHex.split('').map(char => char + char).join('');
      }

      // 转换为RGB
      const r = parseInt(cleanHex.substring(0, 2), 16);
      const g = parseInt(cleanHex.substring(2, 4), 16);
      const b = parseInt(cleanHex.substring(4, 6), 16);

      // 返回RGBA格式
      return `rgba(${r}, ${g}, ${b}, ${alpha || 1})`;
    } catch (err) {
      console.error('颜色转换错误', err);
      return `rgba(0, 0, 0, ${alpha || 1})`;
    }
  }

  /**
   * 使颜色变亮
   * @param {string} hex - 十六进制颜色
   * @param {number} percent - 变亮百分比
   * @returns {string} - 变亮后的颜色
   */
  lightenColor(hex, percent) {
    try {
      if (!hex || typeof hex !== 'string') {
        return '#FFFFFF';
      }

      // 处理简写形式
      let cleanHex = hex.replace('#', '');
      if (cleanHex.length === 3) {
        cleanHex = cleanHex.split('').map(char => char + char).join('');
      }

      // 转换为RGB
      let r = parseInt(cleanHex.substring(0, 2), 16);
      let g = parseInt(cleanHex.substring(2, 4), 16);
      let b = parseInt(cleanHex.substring(4, 6), 16);

      // 增加亮度
      r = Math.min(255, Math.floor(r + (255 - r) * (percent / 100)));
      g = Math.min(255, Math.floor(g + (255 - g) * (percent / 100)));
      b = Math.min(255, Math.floor(b + (255 - b) * (percent / 100)));

      // 转换回十六进制
      const rHex = r.toString(16).padStart(2, '0');
      const gHex = g.toString(16).padStart(2, '0');
      const bHex = b.toString(16).padStart(2, '0');

      return `#${rHex}${gHex}${bHex}`;
    } catch (err) {
      console.error('颜色变亮错误', err);
      return hex;
    }
  }

  /**
   * 使颜色变暗
   * @param {string} hex - 十六进制颜色
   * @param {number} percent - 变暗百分比
   * @returns {string} - 变暗后的颜色
   */
  darkenColor(hex, percent) {
    try {
      // 转换为RGB
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);

      // 减少RGB分量
      const r2 = Math.max(0, Math.floor(r * (1 - percent / 100)));
      const g2 = Math.max(0, Math.floor(g * (1 - percent / 100)));
      const b2 = Math.max(0, Math.floor(b * (1 - percent / 100)));

      // 转换回十六进制
      const rHex = r2.toString(16).padStart(2, '0');
      const gHex = g2.toString(16).padStart(2, '0');
      const bHex = b2.toString(16).padStart(2, '0');

      return `#${rHex}${gHex}${bHex}`;
    } catch (err) {
      console.error('颜色变暗错误', err);
      return hex;
    }
  }

  /**
   * 获取随机颜色
   * @returns {string} 随机颜色
   */
  getRandomColor() {
    // 预定义的颜色数组 - 使用明亮、饱和的颜色
    const colors = [
      '#3B82F6', // 蓝色
      '#6366F1', // 靛蓝色
      '#8B5CF6', // 紫色
      '#EC4899', // 粉色
      '#EF4444', // 红色
      '#F97316', // 橙色
      '#F59E0B', // 琥珀色
      '#EAB308', // 黄色
      '#84CC16', // 酸橙色
      '#22C55E', // 绿色
      '#10B981', // 祖母绿
      '#14B8A6', // 蓝绿色
      '#06B6D4', // 青色
      '#0EA5E9' // 天蓝色
    ];

    // 随机选择一个颜色
    return colors[Math.floor(Math.random() * colors.length)];
  }

  /**
   * 处理触摸开始事件 - 重写基类方法
   * @param {Object} e - 触摸事件对象
   * @returns {Object|null} 交互结果
   */
  handleTouchStart(e) {
    if (!e.touches || e.touches.length === 0) return null;

    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 记录触摸开始时间和位置
    this.dragStartTime = Date.now();
    this.dragStartX = x;
    this.dragStartY = y;
    this.isDragging = false;
    this.dragDistance = 0;

    // 检测点击的是哪个泡泡
    for (const bubble of this.elements) {
      if (this._isPointInElement(x, y, bubble)) {
        // 记录拖动开始的泡泡和位置
        this.draggedElement = bubble;
        this.elementStartX = bubble.x;
        this.elementStartY = bubble.y;

        // 标记泡泡为拖动状态
        bubble.isDragged = true;

        // 记录原始速度
        bubble.originalVelocityX = bubble.velocityX;
        bubble.originalVelocityY = bubble.velocityY;

        // 不立即返回主题，等待触摸结束时判断是点击还是拖动
        return null;
      }
    }

    return null;
  }

  /**
   * 处理触摸移动事件 - 重写基类方法
   * @param {Object} e - 触摸事件对象
   * @returns {string} 鼠标样式
   */
  handleTouchMove(e) {
    if (!e.touches || e.touches.length === 0) return 'default';

    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 如果有正在拖动的泡泡
    if (this.draggedElement) {
      // 计算拖动距离
      const dragX = x - this.dragStartX;
      const dragY = y - this.dragStartY;
      this.dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

      // 如果拖动距离超过阈值，标记为拖动状态
      if (this.dragDistance > 5) {
        this.isDragging = true;
      }

      // 限制拖动距离不超过泡泡半径
      const maxDragDistance = this.draggedElement.radius;
      if (this.dragDistance > maxDragDistance) {
        // 按比例缩放拖动距离
        const scale = maxDragDistance / this.dragDistance;
        const limitedDragX = dragX * scale;
        const limitedDragY = dragY * scale;

        // 更新泡泡位置
        this.draggedElement.x = this.elementStartX + limitedDragX;
        this.draggedElement.y = this.elementStartY + limitedDragY;
      } else {
        // 直接更新泡泡位置
        this.draggedElement.x = this.elementStartX + dragX;
        this.draggedElement.y = this.elementStartY + dragY;
      }

      return 'grabbing';
    }

    // 如果没有拖动的泡泡，更新悬停状态
    let hasHoveredElement = false;
    for (const bubble of this.elements) {
      if (bubble.isDragged) continue; // 跳过正在拖动的泡泡

      if (this._isPointInElement(x, y, bubble)) {
        bubble.isHovered = true;
        hasHoveredElement = true;
      } else {
        bubble.isHovered = false;
      }
    }

    // 返回鼠标指针样式
    return hasHoveredElement ? 'pointer' : 'default';
  }

  /**
   * 处理触摸结束事件 - 重写基类方法
   * @returns {Object|null} 交互结果
   */
  handleTouchEnd() {
    // 如果有拖动的泡泡
    if (this.draggedElement) {
      const bubble = this.draggedElement;

      // 如果是拖动操作
      if (this.isDragging) {
        // 计算拖动向量
        const dragX = bubble.x - this.elementStartX;
        const dragY = bubble.y - this.elementStartY;
        const dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

        // 计算拖动距离与最大拖动距离(半径)的比例，用于调整加速度
        const dragRatio = Math.min(1.0, dragDistance / bubble.radius);

        if (dragDistance > 0) {
          // 计算反方向的单位向量
          const directionX = -dragX / dragDistance;
          const directionY = -dragY / dragDistance;

          // 计算当前速度的大小
          const currentSpeed = Math.sqrt(
            bubble.originalVelocityX * bubble.originalVelocityX +
            bubble.originalVelocityY * bubble.originalVelocityY
          );

          // 如果原始速度接近于0，使用基础速度
          const baseSpeed = currentSpeed > 0.01 ? currentSpeed : this.config.baseSpeed;

          // 加速速度为正常速度的3倍，并根据拖动距离比例调整
          const acceleratedSpeed = baseSpeed * 3.0 * dragRatio;

          // 应用新速度
          bubble.velocityX = directionX * acceleratedSpeed;
          bubble.velocityY = directionY * acceleratedSpeed;

          // 标记为加速状态
          bubble.isAccelerated = true;
          bubble.collisionCount = 0;
        }

        // 重置拖动状态
        bubble.isDragged = false;
        this.draggedElement = null;
        this.isDragging = false;

        // 拖动操作不触发点击事件
        return null;
      } else {
        // 如果是点击操作（没有明显拖动）
        // 获取泡泡主题配置
        const themes = this.getThemes ? this.getThemes() : [];

        // 找到匹配的主题
        const theme = themes.find(t => t.name === bubble.text);
        if (theme) {
          // 标记泡泡为点击状态和已完成状态
          bubble.isClicked = true;
          bubble.isCompleted = true;

          // 更新页面浮动按钮状态
          if (this.page && typeof this.page.updateFloatingButtonState === 'function') {
            this.page.updateFloatingButtonState();
          }

          // 重置拖动状态
          bubble.isDragged = false;
          this.draggedElement = null;

          // 返回主题和交互信息
          return {
            theme: theme,
            tagId: theme.id,
            interactionType: 'click',
            duration: Date.now() - this.dragStartTime,
            positionX: bubble.x,
            positionY: bubble.y
          };
        }
      }
    }

    // 重置所有泡泡的悬停状态
    for (const bubble of this.elements) {
      bubble.isHovered = false;
    }

    // 重置拖动状态
    this.draggedElement = null;
    this.isDragging = false;

    return null;
  }

  /**
   * 重置点击状态
   */
  resetInteractionState() {
    for (const bubble of this.elements) {
      if (bubble.isClicked) {
        // 为被点击的泡泡重新生成随机速度，保持与整体速度一致
        const angle = Math.random() * Math.PI * 2;
        bubble.velocityX = Math.cos(angle) * this.config.baseSpeed * 0.8;
        bubble.velocityY = Math.sin(angle) * this.config.baseSpeed * 0.8;
        bubble.isClicked = false;
        // 保持isCompleted状态不变，以确保清屏奖励功能正常工作
      }
    }
  }

  /**
   * 更新主题数据
   * @param {Array} themes - 主题数据
   * @returns {boolean} 是否更新成功
   */
  updateThemes(themes) {
    if (!themes || themes.length === 0) {
      console.warn('尝试更新空的主题数据');
      return false;
    }

    try {
      // 保存当前泡泡的位置和速度信息
      const bubbleStates = {};
      this.elements.forEach(bubble => {
        const id = bubble.id;
        bubbleStates[id] = {
          x: bubble.x,
          y: bubble.y,
          velocityX: bubble.velocityX,
          velocityY: bubble.velocityY,
          radius: bubble.radius,
          isClicked: bubble.isClicked,
          isDragged: bubble.isDragged
        };
      });

      // 重新初始化元素
      this.elements = this._createElements(themes);

      // 恢复泡泡的位置和速度
      this.elements.forEach((bubble, index) => {
        const oldId = `bubble-${index}`;
        if (bubbleStates[oldId]) {
          bubble.x = bubbleStates[oldId].x;
          bubble.y = bubbleStates[oldId].y;
          bubble.velocityX = bubbleStates[oldId].velocityX;
          bubble.velocityY = bubbleStates[oldId].velocityY;
          bubble.radius = bubbleStates[oldId].radius;
          bubble.isClicked = bubbleStates[oldId].isClicked;
          bubble.isDragged = bubbleStates[oldId].isDragged;
        }
      });

      return true;
    } catch (err) {
      console.error('更新主题数据失败:', err);
      return false;
    }
  }

  /**
   * 检查点是否在泡泡内
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {Object} bubble - 泡泡对象
   * @returns {boolean} 是否在泡泡内
   * @protected
   */
  _isPointInElement(x, y, bubble) {
    const distance = Math.sqrt(
      Math.pow(x - bubble.x, 2) +
      Math.pow(y - bubble.y, 2)
    );

    return distance <= bubble.radius;
  }

  /**
   * 模拟触摸事件
   * @param {number} x - 触摸点X坐标
   * @param {number} y - 触摸点Y坐标
   * @returns {Object|null} 交互结果
   */
  simulateTouch(x, y) {
    try {
      // 创建模拟的触摸事件对象
      const touchEvent = {
        touches: [{ clientX: x, clientY: y }]
      };

      // 模拟触摸开始
      this.handleTouchStart(touchEvent);

      // 随机决定是点击还是拖动
      const isDrag = Math.random() > 0.5;

      if (isDrag && this.draggedElement) {
        // 模拟拖动
        // 计算随机的拖动终点
        const dragEndX = x + (Math.random() - 0.5) * 100;
        const dragEndY = y + (Math.random() - 0.5) * 100;

        // 模拟拖动移动
        const moveEvent = {
          touches: [{ clientX: dragEndX, clientY: dragEndY }]
        };

        this.handleTouchMove(moveEvent);
      }

      // 模拟触摸结束
      const result = this.handleTouchEnd();

      // 如果有交互结果，创建泡泡爆炸效果
      if (result) {
        this.createBubbleExplosion(x, y, result.theme.color);
      }

      return result;
    } catch (err) {
      console.error('模拟触摸失败:', err);
      return null;
    }
  }
}

module.exports = BubbleCanvas;
