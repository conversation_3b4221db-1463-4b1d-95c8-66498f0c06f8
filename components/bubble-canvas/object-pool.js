// components/bubble-canvas/object-pool.js
// 泡泡对象池管理器

/**
 * 泡泡对象池管理器
 * 用于优化泡泡对象的创建和回收
 */
class ObjectPool {
  /**
   * 构造函数
   * @param {Object} options - 初始化选项
   * @param {number} options.initialSize - 初始池大小
   * @param {number} options.maxSize - 最大池大小
   * @param {Function} options.factory - 创建对象的工厂函数
   * @param {Function} options.reset - 重置对象的函数
   */
  constructor(options = {}) {
    this.pool = [];
    this.initialSize = options.initialSize || 50;
    this.maxSize = options.maxSize || 200;
    this.factory = options.factory || (() => ({}));
    this.reset = options.reset || (obj => obj);
    this.created = 0;
    this.reused = 0;
    this.released = 0;
    this.lastCleanupTime = Date.now();
    this.cleanupInterval = 30000; // 30秒清理一次
    this.cleanupThreshold = 0.5; // 使用率低于50%时清理
  }

  /**
   * 初始化对象池
   */
  init() {
    // 创建初始对象
    for (let i = 0; i < this.initialSize; i++) {
      const obj = this.factory();
      obj._pooled = true; // 标记为池对象
      this.pool.push(obj);
    }

    this.created = this.initialSize;
    console.log(`对象池初始化完成，大小: ${this.pool.length}`);
  }

  /**
   * 从池中获取对象
   * @returns {Object} 池对象
   */
  get() {
    // 如果池为空，创建新对象
    if (this.pool.length === 0) {
      const obj = this.factory();
      obj._pooled = true; // 标记为池对象
      this.created++;
      return obj;
    }

    // 从池中取出对象
    const obj = this.pool.pop();
    this.reused++;
    return obj;
  }

  /**
   * 将对象返回池中
   * @param {Object} obj - 要返回的对象
   */
  release(obj) {
    // 如果对象不是池对象，忽略
    if (!obj || !obj._pooled) return;

    // 重置对象状态
    this.reset(obj);

    // 如果池未满，将对象返回池中
    if (this.pool.length < this.maxSize) {
      this.pool.push(obj);
    }

    this.released++;
  }

  /**
   * 批量获取对象
   * @param {number} count - 要获取的对象数量
   * @returns {Array} 对象数组
   */
  getMultiple(count) {
    const objects = [];
    for (let i = 0; i < count; i++) {
      objects.push(this.get());
    }
    return objects;
  }

  /**
   * 批量释放对象
   * @param {Array} objects - 要释放的对象数组
   */
  releaseMultiple(objects) {
    if (!objects || !Array.isArray(objects)) return;

    objects.forEach(obj => this.release(obj));
  }

  /**
   * 清理对象池
   * @param {number} targetSize - 目标大小，默认为初始大小
   */
  cleanup(targetSize = this.initialSize) {
    // 如果池大小小于等于目标大小，不需要清理
    if (this.pool.length <= targetSize) return;

    // 计算要移除的数量
    const removeCount = this.pool.length - targetSize;

    // 移除多余的对象
    this.pool.splice(0, removeCount);

    console.log(`清理了 ${removeCount} 个对象，当前池大小: ${this.pool.length}`);
  }

  /**
   * 定期清理对象池
   */
  periodicCleanup() {
    const now = Date.now();

    // 如果距离上次清理时间不足清理间隔，跳过
    if (now - this.lastCleanupTime < this.cleanupInterval) return;

    // 计算使用率
    const usageRate = this.reused > 0 ? (this.released / this.reused) : 0;

    // 如果使用率低于阈值，清理对象池
    if (usageRate < this.cleanupThreshold) {
      this.cleanup();
    }

    // 更新最后清理时间
    this.lastCleanupTime = now;
  }

  /**
   * 获取对象池统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      size: this.pool.length,
      maxSize: this.maxSize,
      created: this.created,
      reused: this.reused,
      released: this.released,
      usageRate: this.reused > 0 ? (this.released / this.reused * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * 清空对象池
   */
  clear() {
    this.pool = [];
    console.log('对象池已清空');
  }

  /**
   * 销毁对象池
   */
  destroy() {
    this.clear();
    this.factory = null;
    this.reset = null;
  }
}

module.exports = ObjectPool;
