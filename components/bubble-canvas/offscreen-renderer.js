// components/bubble-canvas/offscreen-renderer.js
// 泡泡画布离屏渲染器

/**
 * 泡泡画布离屏渲染器
 * 用于优化泡泡画布的渲染性能
 */
class OffscreenRenderer {
  /**
   * 构造函数
   * @param {Object} options - 初始化选项
   * @param {number} options.dpr - 设备像素比
   * @param {number} options.maxCacheSize - 最大缓存大小
   * @param {number} options.cleanupInterval - 缓存清理间隔（毫秒）
   * @param {number} options.frequencyThreshold - 频率阈值，用于智能缓存
   * @param {boolean} options.adaptiveCaching - 是否启用自适应缓存
   */
  constructor(options = {}) {
    this.dpr = options.dpr || 1;
    this.renderCache = new Map();
    this.frequencyMap = new Map(); // 记录每个尺寸和颜色组合的使用频率
    this.isEnabled = true;
    this.maxCacheSize = options.maxCacheSize || 50;
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.lastCleanupTime = Date.now();
    this.cleanupInterval = options.cleanupInterval || 30000; // 30秒清理一次缓存
    this.frequencyThreshold = options.frequencyThreshold || 3; // 使用频率阈值
    this.adaptiveCaching = options.adaptiveCaching !== false; // 默认启用自适应缓存
    this.prioritySizes = []; // 优先缓存的尺寸
    this.priorityColors = []; // 优先缓存的颜色
  }

  /**
   * 初始化离屏渲染器
   * @returns {boolean} 是否初始化成功
   */
  init() {
    try {
      // 检查是否支持离屏Canvas
      if (!wx.createOffscreenCanvas) {
        console.warn('当前环境不支持离屏Canvas，将禁用缓存渲染');
        this.isEnabled = false;
        return false;
      }

      console.log('离屏渲染器初始化成功');
      return true;
    } catch (err) {
      console.error('离屏渲染器初始化失败', err);
      this.isEnabled = false;
      return false;
    }
  }

  /**
   * 设置优先缓存的尺寸和颜色
   * @param {Array} sizes - 优先缓存的尺寸数组
   * @param {Array} colors - 优先缓存的颜色数组
   */
  setPriorities(sizes = [], colors = []) {
    this.prioritySizes = sizes;
    this.priorityColors = colors;
    console.log(`设置了 ${sizes.length} 个优先尺寸和 ${colors.length} 个优先颜色`);
  }

  /**
   * 预渲染泡泡
   * @param {Array} commonSizes - 常用泡泡尺寸数组
   * @param {Array} commonColors - 常用泡泡颜色数组
   * @param {Function} drawFunction - 绘制函数
   * @param {boolean} forcePriority - 是否强制优先渲染
   */
  preRenderBubbles(commonSizes, commonColors, drawFunction, forcePriority = false) {
    if (!this.isEnabled) return;

    try {
      // 合并优先尺寸和颜色
      const sizes = [...new Set([...this.prioritySizes, ...commonSizes])];
      const colors = [...new Set([...this.priorityColors, ...commonColors])];

      // 如果启用了自适应缓存且不是强制优先渲染，则根据使用频率排序
      let sizesToRender = sizes;
      let colorsToRender = colors;

      if (this.adaptiveCaching && !forcePriority && this.frequencyMap.size > 0) {
        // 根据使用频率排序
        const sizeFrequency = new Map();
        const colorFrequency = new Map();

        // 统计尺寸和颜色的使用频率
        for (const [key, frequency] of this.frequencyMap.entries()) {
          const [, sizeStr, colorStr] = key.match(/bubble-(\d+)-(.+)/);
          const size = parseInt(sizeStr);

          // 更新尺寸频率
          sizeFrequency.set(size, (sizeFrequency.get(size) || 0) + frequency);

          // 更新颜色频率
          colorFrequency.set(colorStr, (colorFrequency.get(colorStr) || 0) + frequency);
        }

        // 按频率排序尺寸和颜色
        sizesToRender = sizes.sort((a, b) => (sizeFrequency.get(b) || 0) - (sizeFrequency.get(a) || 0));
        colorsToRender = colors.sort((a, b) => (colorFrequency.get(b) || 0) - (colorFrequency.get(a) || 0));

        // 限制预渲染数量，避免过多预渲染
        const maxPreRender = Math.floor(this.maxCacheSize * 0.8);
        const maxSizes = Math.min(sizesToRender.length, Math.ceil(Math.sqrt(maxPreRender)));
        const maxColors = Math.min(colorsToRender.length, Math.ceil(maxPreRender / maxSizes));

        sizesToRender = sizesToRender.slice(0, maxSizes);
        colorsToRender = colorsToRender.slice(0, maxColors);
      }

      // 预渲染常用组合
      let renderedCount = 0;
      sizesToRender.forEach(radius => {
        colorsToRender.forEach(color => {
          const cacheKey = `bubble-${radius}-${color}`;

          // 如果缓存中已存在，跳过
          if (this.renderCache.has(cacheKey)) return;

          // 如果缓存已满，清理最久未使用的项
          if (this.renderCache.size >= this.maxCacheSize) {
            this._cleanupCache(1);
          }

          // 创建新的离屏Canvas
          const bubbleCanvas = wx.createOffscreenCanvas({
            type: '2d',
            width: radius * 2 * this.dpr,
            height: radius * 2 * this.dpr
          });

          const bubbleCtx = bubbleCanvas.getContext('2d');

          // 设置缩放
          bubbleCtx.scale(this.dpr, this.dpr);

          // 绘制泡泡
          drawFunction(bubbleCtx, radius, color);

          // 存入缓存
          this.renderCache.set(cacheKey, {
            canvas: bubbleCanvas,
            lastUsed: Date.now(),
            priority: this.prioritySizes.includes(radius) || this.priorityColors.includes(color)
          });

          renderedCount++;
        });
      });

      console.log(`预渲染了 ${renderedCount} 个泡泡，当前缓存大小: ${this.renderCache.size}`);
    } catch (err) {
      console.error('预渲染泡泡失败', err);
    }
  }

  /**
   * 获取缓存的泡泡
   * @param {number} radius - 泡泡半径
   * @param {string} color - 泡泡颜色
   * @returns {Object|null} 缓存的Canvas或null
   */
  getCachedBubble(radius, color) {
    if (!this.isEnabled) return null;

    const roundedRadius = Math.round(radius);
    const cacheKey = `bubble-${roundedRadius}-${color}`;

    // 检查缓存中是否有该泡泡
    if (this.renderCache.has(cacheKey)) {
      const cacheItem = this.renderCache.get(cacheKey);

      // 更新最后使用时间
      cacheItem.lastUsed = Date.now();

      // 更新缓存命中计数
      this.cacheHits++;

      // 更新使用频率
      this._updateFrequency(roundedRadius, color);

      return cacheItem.canvas;
    }

    // 更新缓存未命中计数
    this.cacheMisses++;

    // 更新使用频率（即使未命中也记录）
    this._updateFrequency(roundedRadius, color);

    // 尝试找到最接近的尺寸
    if (this.adaptiveCaching) {
      // 查找最接近的尺寸
      const closestRadius = this._findClosestRadius(roundedRadius);
      if (closestRadius !== null) {
        const closestKey = `bubble-${closestRadius}-${color}`;
        if (this.renderCache.has(closestKey)) {
          const cacheItem = this.renderCache.get(closestKey);

          // 更新最后使用时间
          cacheItem.lastUsed = Date.now();

          // 更新缓存命中计数（近似命中）
          this.cacheHits++;

          console.log(`使用近似尺寸: ${closestRadius} 代替 ${roundedRadius}`);

          return cacheItem.canvas;
        }
      }
    }

    return null;
  }

  /**
   * 添加泡泡到缓存
   * @param {number} radius - 泡泡半径
   * @param {string} color - 泡泡颜色
   * @param {Function} drawFunction - 绘制函数
   * @param {boolean} isPriority - 是否为优先项
   * @returns {Object|null} 缓存的Canvas或null
   */
  addBubbleToCache(radius, color, drawFunction, isPriority = false) {
    if (!this.isEnabled) return null;

    try {
      const roundedRadius = Math.round(radius);
      const cacheKey = `bubble-${roundedRadius}-${color}`;

      // 如果已经在缓存中，直接返回
      if (this.renderCache.has(cacheKey)) {
        return this.renderCache.get(cacheKey).canvas;
      }

      // 如果启用了自适应缓存，检查是否应该缓存
      if (this.adaptiveCaching && !isPriority) {
        const frequency = this.frequencyMap.get(cacheKey) || 0;

        // 如果使用频率低于阈值且缓存已接近满，不缓存
        if (frequency < this.frequencyThreshold && this.renderCache.size > this.maxCacheSize * 0.8) {
          // 检查是否有接近的尺寸已经缓存
          const closestRadius = this._findClosestRadius(roundedRadius);
          if (closestRadius !== null) {
            const closestKey = `bubble-${closestRadius}-${color}`;
            if (this.renderCache.has(closestKey)) {
              // 已有接近尺寸的缓存，不需要再缓存
              return this.renderCache.get(closestKey).canvas;
            }
          }
        }
      }

      // 如果缓存已满，清理最久未使用的项
      if (this.renderCache.size >= this.maxCacheSize) {
        this._cleanupCache(1);
      }

      // 创建新的离屏Canvas
      const bubbleCanvas = wx.createOffscreenCanvas({
        type: '2d',
        width: roundedRadius * 2 * this.dpr,
        height: roundedRadius * 2 * this.dpr
      });

      const bubbleCtx = bubbleCanvas.getContext('2d');

      // 设置缩放
      bubbleCtx.scale(this.dpr, this.dpr);

      // 绘制泡泡
      drawFunction(bubbleCtx, roundedRadius, color);

      // 存入缓存
      this.renderCache.set(cacheKey, {
        canvas: bubbleCanvas,
        lastUsed: Date.now(),
        priority: isPriority || this.prioritySizes.includes(roundedRadius) || this.priorityColors.includes(color)
      });

      return bubbleCanvas;
    } catch (err) {
      console.error('添加泡泡到缓存失败', err);
      return null;
    }
  }

  /**
   * 更新使用频率
   * @param {number} radius - 泡泡半径
   * @param {string} color - 泡泡颜色
   * @private
   */
  _updateFrequency(radius, color) {
    const cacheKey = `bubble-${radius}-${color}`;

    // 更新使用频率
    const frequency = (this.frequencyMap.get(cacheKey) || 0) + 1;
    this.frequencyMap.set(cacheKey, frequency);

    // 如果是新的尺寸或颜色，考虑添加到优先列表
    if (frequency >= this.frequencyThreshold * 2) {
      if (!this.prioritySizes.includes(radius)) {
        this.prioritySizes.push(radius);
      }
      if (!this.priorityColors.includes(color)) {
        this.priorityColors.push(color);
      }
    }
  }

  /**
   * 查找最接近的半径
   * @param {number} radius - 目标半径
   * @returns {number|null} 最接近的半径或null
   * @private
   */
  _findClosestRadius(radius) {
    // 获取所有缓存的半径
    const cachedRadii = new Set();

    for (const key of this.renderCache.keys()) {
      const match = key.match(/bubble-(\d+)-/);
      if (match) {
        cachedRadii.add(parseInt(match[1]));
      }
    }

    if (cachedRadii.size === 0) return null;

    // 转换为数组并排序
    const radii = Array.from(cachedRadii).sort((a, b) => a - b);

    // 查找最接近的半径
    let closest = null;
    let minDiff = Infinity;

    for (const r of radii) {
      const diff = Math.abs(r - radius);

      // 如果差异小于10%，认为足够接近
      if (diff < radius * 0.1 && diff < minDiff) {
        minDiff = diff;
        closest = r;
      }
    }

    return closest;
  }

  /**
   * 清理缓存
   * @param {number} count - 要清理的项数，默认为0（自动决定）
   * @private
   */
  _cleanupCache(count = 0) {
    // 如果缓存为空，直接返回
    if (this.renderCache.size === 0) return;

    // 如果未指定清理数量，根据缓存大小决定
    const cleanupCount = count > 0 ? count : Math.ceil(this.renderCache.size * 0.2);

    // 获取所有缓存项
    const cacheItems = Array.from(this.renderCache.entries());

    // 分离优先项和非优先项
    const priorityItems = cacheItems.filter(([, item]) => item.priority);
    const nonPriorityItems = cacheItems.filter(([, item]) => !item.priority);

    // 如果非优先项足够清理，只清理非优先项
    if (nonPriorityItems.length >= cleanupCount) {
      // 按最后使用时间和使用频率排序
      nonPriorityItems.sort((a, b) => {
        const keyA = a[0];
        const keyB = b[0];
        const freqA = this.frequencyMap.get(keyA) || 0;
        const freqB = this.frequencyMap.get(keyB) || 0;

        // 首先按使用频率排序，然后按最后使用时间排序
        if (freqA !== freqB) {
          return freqA - freqB; // 低频率优先清理
        }

        return a[1].lastUsed - b[1].lastUsed; // 最久未使用优先清理
      });

      // 清理最久未使用的非优先项
      for (let i = 0; i < Math.min(cleanupCount, nonPriorityItems.length); i++) {
        this.renderCache.delete(nonPriorityItems[i][0]);
      }

      console.log(`清理了 ${Math.min(cleanupCount, nonPriorityItems.length)} 个非优先缓存项，当前缓存大小: ${this.renderCache.size}`);
    } else {
      // 需要清理一些优先项

      // 首先清理所有非优先项
      nonPriorityItems.forEach(([key]) => {
        this.renderCache.delete(key);
      });

      // 计算还需要清理的数量
      const remainingCount = cleanupCount - nonPriorityItems.length;

      if (remainingCount > 0 && priorityItems.length > 0) {
        // 按最后使用时间排序优先项
        priorityItems.sort((a, b) => a[1].lastUsed - b[1].lastUsed);

        // 清理最久未使用的优先项
        for (let i = 0; i < Math.min(remainingCount, priorityItems.length); i++) {
          this.renderCache.delete(priorityItems[i][0]);
        }

        console.log(`清理了 ${nonPriorityItems.length} 个非优先缓存项和 ${Math.min(remainingCount, priorityItems.length)} 个优先缓存项，当前缓存大小: ${this.renderCache.size}`);
      } else {
        console.log(`清理了 ${nonPriorityItems.length} 个非优先缓存项，当前缓存大小: ${this.renderCache.size}`);
      }
    }
  }

  /**
   * 定期清理缓存
   */
  periodicCleanup() {
    const now = Date.now();

    // 如果距离上次清理时间不足清理间隔，跳过
    if (now - this.lastCleanupTime < this.cleanupInterval) return;

    // 清理缓存
    this._cleanupCache();

    // 清理频率映射表，只保留高频使用的项
    if (this.frequencyMap.size > this.maxCacheSize * 2) {
      const frequencyEntries = Array.from(this.frequencyMap.entries())
        .sort((a, b) => a[1] - b[1]);

      // 清理低频使用的项，保留高频使用的项
      const cleanupCount = Math.ceil(this.frequencyMap.size * 0.3);
      for (let i = 0; i < Math.min(cleanupCount, frequencyEntries.length); i++) {
        this.frequencyMap.delete(frequencyEntries[i][0]);
      }

      console.log(`清理了 ${Math.min(cleanupCount, frequencyEntries.length)} 个低频使用项，当前频率映射大小: ${this.frequencyMap.size}`);
    }

    // 更新最后清理时间
    this.lastCleanupTime = now;
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    // 计算优先项数量
    const priorityCount = Array.from(this.renderCache.values())
      .filter(item => item.priority).length;

    // 获取频率最高的项
    const topFrequencyItems = Array.from(this.frequencyMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([key, freq]) => {
        const match = key.match(/bubble-(\d+)-(.+)/);
        if (match) {
          return {
            radius: match[1],
            color: match[2],
            frequency: freq
          };
        }
        return null;
      })
      .filter(item => item !== null);

    return {
      enabled: this.isEnabled,
      adaptiveCaching: this.adaptiveCaching,
      size: this.renderCache.size,
      maxSize: this.maxCacheSize,
      priorityCount,
      nonPriorityCount: this.renderCache.size - priorityCount,
      hits: this.cacheHits,
      misses: this.cacheMisses,
      hitRate: this.cacheHits + this.cacheMisses > 0
        ? (this.cacheHits / (this.cacheHits + this.cacheMisses) * 100).toFixed(2) + '%'
        : '0%',
      frequencyMapSize: this.frequencyMap.size,
      topFrequencyItems,
      prioritySizes: this.prioritySizes,
      priorityColors: this.priorityColors
    };
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.renderCache.clear();
    console.log('缓存已清空');
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.clearCache();
    this.isEnabled = false;
  }
}

module.exports = OffscreenRenderer;
