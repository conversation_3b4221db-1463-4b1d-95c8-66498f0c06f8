/**
 * 奖励动画模块
 * 提供清屏奖励动画效果
 */

/**
 * 创建清屏奖励动画
 * @param {CanvasRenderingContext2D} ctx - Canvas上下文
 * @param {number} width - 画布宽度
 * @param {number} height - 画布高度
 * @param {Object} options - 配置选项
 * @returns {Function} 动画渲染函数
 */
function createClearScreenReward(ctx, width, height, options = {}) {
  // 默认配置
  const config = {
    duration: 2000, // 动画持续时间（毫秒）
    particleCount: 100, // 粒子数量
    colors: ['#3B82F6', '#6366F1', '#8B5CF6', '#EC4899', '#F97316', '#FACC15'], // 粒子颜色
    size: { min: 5, max: 15 }, // 粒子大小范围
    speed: { min: 2, max: 5 }, // 粒子速度范围
    gravity: 0.1, // 重力
    friction: 0.98, // 摩擦力
    rotation: true, // 是否旋转
    fadeOut: true, // 是否淡出
    ...options
  };

  // 粒子数组
  const particles = [];
  
  // 动画开始时间
  let startTime = null;
  
  // 动画是否完成
  let isCompleted = false;
  
  // 创建粒子
  function createParticles() {
    particles.length = 0; // 清空数组
    
    for (let i = 0; i < config.particleCount; i++) {
      const size = config.size.min + Math.random() * (config.size.max - config.size.min);
      const color = config.colors[Math.floor(Math.random() * config.colors.length)];
      const angle = Math.random() * Math.PI * 2;
      const speed = config.speed.min + Math.random() * (config.speed.max - config.speed.min);
      const shape = Math.random() > 0.5 ? 'circle' : 'star';
      
      particles.push({
        x: width / 2,
        y: height / 2,
        size,
        color,
        velocityX: Math.cos(angle) * speed,
        velocityY: Math.sin(angle) * speed,
        rotation: Math.random() * Math.PI * 2,
        rotationSpeed: (Math.random() - 0.5) * 0.2,
        opacity: 1,
        shape
      });
    }
  }
  
  // 更新粒子
  function updateParticles(deltaTime) {
    for (let i = 0; i < particles.length; i++) {
      const p = particles[i];
      
      // 更新位置
      p.x += p.velocityX;
      p.y += p.velocityY;
      
      // 应用重力
      p.velocityY += config.gravity;
      
      // 应用摩擦力
      p.velocityX *= config.friction;
      p.velocityY *= config.friction;
      
      // 更新旋转
      if (config.rotation) {
        p.rotation += p.rotationSpeed;
      }
      
      // 更新透明度
      if (config.fadeOut) {
        const progress = (Date.now() - startTime) / config.duration;
        p.opacity = 1 - progress;
      }
    }
  }
  
  // 绘制粒子
  function drawParticles() {
    ctx.save();
    
    for (let i = 0; i < particles.length; i++) {
      const p = particles[i];
      
      ctx.save();
      ctx.globalAlpha = p.opacity;
      ctx.translate(p.x, p.y);
      ctx.rotate(p.rotation);
      
      if (p.shape === 'circle') {
        // 绘制圆形
        ctx.beginPath();
        ctx.arc(0, 0, p.size, 0, Math.PI * 2);
        ctx.fillStyle = p.color;
        ctx.fill();
      } else {
        // 绘制星形
        drawStar(ctx, 0, 0, p.size, 5, p.size * 0.5, p.color);
      }
      
      ctx.restore();
    }
    
    ctx.restore();
  }
  
  // 绘制星形
  function drawStar(ctx, x, y, outerRadius, points, innerRadius, color) {
    ctx.beginPath();
    
    for (let i = 0; i < points * 2; i++) {
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const angle = (i * Math.PI) / points;
      
      const pointX = x + radius * Math.sin(angle);
      const pointY = y + radius * Math.cos(angle);
      
      if (i === 0) {
        ctx.moveTo(pointX, pointY);
      } else {
        ctx.lineTo(pointX, pointY);
      }
    }
    
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();
  }
  
  // 绘制奖杯图标
  function drawTrophy(x, y, size, color) {
    ctx.save();
    
    // 绘制奖杯主体
    ctx.beginPath();
    ctx.moveTo(x - size * 0.3, y - size * 0.5);
    ctx.lineTo(x + size * 0.3, y - size * 0.5);
    ctx.lineTo(x + size * 0.25, y + size * 0.1);
    ctx.lineTo(x - size * 0.25, y + size * 0.1);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();
    
    // 绘制奖杯把手
    ctx.beginPath();
    ctx.arc(x - size * 0.4, y - size * 0.3, size * 0.15, Math.PI * 0.5, Math.PI * 1.5, false);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.arc(x + size * 0.4, y - size * 0.3, size * 0.15, Math.PI * 1.5, Math.PI * 0.5, false);
    ctx.stroke();
    
    // 绘制奖杯底座
    ctx.beginPath();
    ctx.moveTo(x - size * 0.2, y + size * 0.1);
    ctx.lineTo(x + size * 0.2, y + size * 0.1);
    ctx.lineTo(x + size * 0.3, y + size * 0.3);
    ctx.lineTo(x - size * 0.3, y + size * 0.3);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();
    
    ctx.restore();
  }
  
  // 绘制文本
  function drawText(text, x, y, fontSize, color) {
    ctx.save();
    
    ctx.font = `bold ${fontSize}px Arial, sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = color;
    
    // 添加文字阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;
    
    ctx.fillText(text, x, y);
    
    ctx.restore();
  }
  
  // 动画渲染函数
  function render(timestamp) {
    if (!startTime) {
      startTime = timestamp;
      createParticles();
    }
    
    const elapsed = timestamp - startTime;
    
    // 清空画布（使用透明背景，以便叠加在现有内容上）
    ctx.clearRect(0, 0, width, height);
    
    // 绘制半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillRect(0, 0, width, height);
    
    // 更新粒子
    updateParticles(elapsed);
    
    // 绘制粒子
    drawParticles();
    
    // 绘制中央奖杯和文字
    const centerY = height * 0.4;
    const progress = Math.min(1, elapsed / 500); // 0.5秒内完成缩放
    const scale = Math.min(1, progress * 1.2); // 稍微过冲一点
    
    ctx.save();
    ctx.translate(width / 2, centerY);
    ctx.scale(scale, scale);
    
    // 绘制光晕效果
    const glowRadius = 80 + Math.sin(elapsed * 0.005) * 10; // 光晕大小随时间变化
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, glowRadius);
    gradient.addColorStop(0, 'rgba(255, 215, 0, 0.8)');
    gradient.addColorStop(1, 'rgba(255, 215, 0, 0)');
    
    ctx.beginPath();
    ctx.arc(0, 0, glowRadius, 0, Math.PI * 2);
    ctx.fillStyle = gradient;
    ctx.fill();
    
    // 绘制奖杯
    drawTrophy(0, 0, 60, '#FFD700');
    
    ctx.restore();
    
    // 绘制文字（带有淡入效果）
    const textOpacity = Math.min(1, Math.max(0, (elapsed - 300) / 500)); // 0.3秒后开始淡入，0.5秒内完成
    
    ctx.save();
    ctx.globalAlpha = textOpacity;
    
    // 绘制主标题
    drawText('恭喜完成！', width / 2, height * 0.6, 36, '#FFFFFF');
    
    // 绘制副标题
    drawText('获得清屏奖励', width / 2, height * 0.67, 24, '#FFD700');
    
    ctx.restore();
    
    // 检查动画是否完成
    if (elapsed >= config.duration) {
      isCompleted = true;
    }
    
    return isCompleted;
  }
  
  // 返回渲染函数
  return render;
}

module.exports = {
  createClearScreenReward
};
