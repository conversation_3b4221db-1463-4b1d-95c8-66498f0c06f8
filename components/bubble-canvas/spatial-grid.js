// components/bubble-canvas/spatial-grid.js
// 空间分区管理器

/**
 * 空间分区管理器
 * 用于优化碰撞检测
 */
class SpatialGrid {
  /**
   * 构造函数
   * @param {Object} options - 初始化选项
   * @param {number} options.width - 画布宽度
   * @param {number} options.height - 画布高度
   * @param {number} options.cellSize - 网格单元格大小
   */
  constructor(options = {}) {
    this.width = options.width || 0;
    this.height = options.height || 0;
    this.cellSize = options.cellSize || 100;
    this.grid = {};
    this.collisionChecks = 0;
    this.potentialCollisions = 0;
    this.actualCollisions = 0;
  }

  /**
   * 更新网格尺寸
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   */
  updateSize(width, height) {
    this.width = width;
    this.height = height;
    this.clear();
  }

  /**
   * 获取元素所在的网格单元格
   * @param {Object} element - 元素对象
   * @returns {Array} 网格单元格坐标数组
   */
  getElementCells(element) {
    const cells = [];

    // 计算元素可能占据的网格
    const minGridX = Math.floor((element.x - element.radius) / this.cellSize);
    const maxGridX = Math.floor((element.x + element.radius) / this.cellSize);
    const minGridY = Math.floor((element.y - element.radius) / this.cellSize);
    const maxGridY = Math.floor((element.y + element.radius) / this.cellSize);

    // 收集所有可能的网格单元格
    for (let gx = minGridX; gx <= maxGridX; gx++) {
      for (let gy = minGridY; gy <= maxGridY; gy++) {
        cells.push(`${gx},${gy}`);
      }
    }

    return cells;
  }

  /**
   * 将元素添加到网格
   * @param {Array} elements - 元素数组
   */
  addElements(elements) {
    // 清空网格
    this.clear();

    // 将元素添加到网格
    elements.forEach(element => {
      // 获取元素所在的网格单元格
      const cells = this.getElementCells(element);

      // 将元素添加到所有可能的网格中
      cells.forEach(cell => {
        if (!this.grid[cell]) {
          this.grid[cell] = [];
        }
        this.grid[cell].push(element);
      });
    });
  }

  /**
   * 检测碰撞
   * @param {Function} collisionHandler - 碰撞处理函数
   */
  checkCollisions(collisionHandler) {
    // 重置计数器
    this.collisionChecks = 0;
    this.potentialCollisions = 0;
    this.actualCollisions = 0;

    // 遍历所有网格单元格
    Object.keys(this.grid).forEach(cell => {
      const elements = this.grid[cell];

      // 检查当前网格中的元素
      for (let i = 0; i < elements.length; i++) {
        for (let j = i + 1; j < elements.length; j++) {
          const elementA = elements[i];
          const elementB = elements[j];

          // 增加碰撞检查计数
          this.collisionChecks++;

          // 计算元素间距离
          const dx = elementB.x - elementA.x;
          const dy = elementB.y - elementA.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          // 检查是否碰撞
          if (distance < elementA.radius + elementB.radius) {
            // 增加潜在碰撞计数
            this.potentialCollisions++;

            // 调用碰撞处理函数
            const collision = collisionHandler(elementA, elementB);

            // 如果发生实际碰撞，增加计数
            if (collision) {
              this.actualCollisions++;
            }
          }
        }
      }
    });
  }

  /**
   * 获取元素的潜在碰撞对象
   * @param {Object} element - 元素对象
   * @returns {Array} 潜在碰撞对象数组
   */
  getPotentialCollisions(element) {
    const potentialCollisions = [];

    // 获取元素所在的网格单元格
    const cells = this.getElementCells(element);

    // 收集所有可能的碰撞对象
    cells.forEach(cell => {
      if (this.grid[cell]) {
        this.grid[cell].forEach(other => {
          if (element !== other && !potentialCollisions.includes(other)) {
            potentialCollisions.push(other);
          }
        });
      }
    });

    return potentialCollisions;
  }

  /**
   * 获取网格统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      cellCount: Object.keys(this.grid).length,
      cellSize: this.cellSize,
      collisionChecks: this.collisionChecks,
      potentialCollisions: this.potentialCollisions,
      actualCollisions: this.actualCollisions,
      efficiency: this.collisionChecks > 0
        ? ((1 - (this.collisionChecks / (this.width * this.height / (this.cellSize * this.cellSize)))) * 100).toFixed(2) + '%'
        : '0%'
    };
  }

  /**
   * 清空网格
   */
  clear() {
    this.grid = {};
  }

  /**
   * 销毁网格
   */
  destroy() {
    this.clear();
  }
}

module.exports = SpatialGrid;
