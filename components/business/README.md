# 业务组件

业务组件是基于基础组件封装的，用于特定业务场景的组件。

## 组件列表

### 学习计划相关组件

- **LearningPlanCard**: 学习计划卡片组件，用于展示学习计划信息
- **LearningProgress**: 学习进度可视化组件，用于展示学习计划的进度

### 内容展示相关组件

- **ContentModal**: 统一内容展示模态弹窗组件，用于展示练习、观点、笔记等内容
- **ContentCard**: 内容卡片组件，用于展示练习、观点、笔记等内容

### 用户中心相关组件

- **UserProfile**: 用户信息展示组件，用于展示用户基本信息、等级、经验值等
- **AchievementDisplay**: 成就与徽章展示组件，用于展示用户获得的成就和徽章

## 使用方法

### 引入组件

在页面的 JSON 配置文件中引入需要的组件：

```json
{
  "usingComponents": {
    "learning-plan-card": "/components/business/learning-plan-card/index",
    "learning-progress": "/components/business/learning-progress/index",
    "content-modal": "/components/business/content-modal/index",
    "content-card": "/components/business/content-card/index",
    "user-profile": "/components/business/user-profile/index",
    "achievement-display": "/components/business/achievement-display/index"
  }
}
```

### 使用组件

在页面的 WXML 文件中使用组件：

```html
<!-- 学习计划卡片 -->
<learning-plan-card 
  plan="{{planData}}" 
  bind:click="handlePlanClick"
  bind:detail="handleViewDetail"
  bind:continue="handleContinueLearning"
></learning-plan-card>

<!-- 学习进度可视化 -->
<learning-progress 
  plan="{{planData}}" 
  type="full"
  bind:dayclick="handleDayClick"
></learning-progress>

<!-- 内容展示模态弹窗 -->
<content-modal 
  visible="{{showContentModal}}"
  contentType="{{contentType}}"
  contentData="{{contentData}}"
  bind:close="handleCloseModal"
  bind:submit="handleSubmitContent"
  bind:complete="handleCompleteContent"
></content-modal>

<!-- 内容卡片 -->
<content-card 
  contentType="exercise"
  contentData="{{exerciseData}}"
  bind:click="handleContentClick"
  bind:view="handleViewContent"
  bind:edit="handleEditContent"
  bind:delete="handleDeleteContent"
></content-card>

<!-- 用户信息展示 -->
<user-profile 
  userData="{{userData}}" 
  type="full"
  bind:avatarclick="handleAvatarClick"
  bind:nameclick="handleNameClick"
  bind:levelclick="handleLevelClick"
></user-profile>

<!-- 成就与徽章展示 -->
<achievement-display 
  userId="{{userId}}"
  achievements="{{achievements}}"
  badges="{{badges}}"
  type="grid"
  bind:itemclick="handleItemClick"
></achievement-display>
```
