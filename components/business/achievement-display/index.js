/**
 * 成就与徽章展示组件
 * 用于展示用户获得的成就和徽章
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 用户ID
    userId: {
      type: String,
      value: ''
    },
    // 成就数据
    achievements: {
      type: Array,
      value: []
    },
    // 徽章数据
    badges: {
      type: Array,
      value: []
    },
    // 显示类型
    type: {
      type: String,
      value: 'grid' // grid, list, carousel
    },
    // 是否显示未获得的成就/徽章
    showLocked: {
      type: Boolean,
      value: true
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentTab: 'achievements', // achievements, badges
    isLoading: false,
    loadingFailed: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换标签页
     */
    switchTab(e) {
      const { tab } = e.currentTarget.dataset;
      this.setData({ currentTab: tab });
    },

    /**
     * 点击成就/徽章事件
     */
    handleItemClick(e) {
      const { type, id } = e.currentTarget.dataset;
      const item = type === 'achievement'
        ? this.data.achievements.find(item => item.id === id)
        : this.data.badges.find(item => item.id === id);

      this.triggerEvent('itemclick', { type, item });
    },

    /**
     * 获取组件样式类
     */
    getDisplayClass() {
      const { type } = this.data;
      return `nl-achievement-display nl-achievement-display-${type}`;
    },

    /**
     * 获取组件样式
     */
    getDisplayStyle() {
      return this.data.customStyle;
    },

    /**
     * 获取成就/徽章项样式类
     */
    getItemClass(item) {
      return `achievement-item ${item.unlocked ? 'unlocked' : 'locked'}`;
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  }
});
