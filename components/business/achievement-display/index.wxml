<view class="{{getDisplayClass()}}" style="{{getDisplayStyle()}}">
  <!-- 标签页切换 -->
  <view class="tabs">
    <view 
      class="tab {{currentTab === 'achievements' ? 'active' : ''}}" 
      data-tab="achievements" 
      bindtap="switchTab"
    >成就</view>
    <view 
      class="tab {{currentTab === 'badges' ? 'active' : ''}}" 
      data-tab="badges" 
      bindtap="switchTab"
    >徽章</view>
  </view>
  
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 加载失败状态 -->
  <view class="error-container" wx:elif="{{loadingFailed}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="loadData">点击重试</button>
  </view>
  
  <!-- 成就展示 -->
  <view class="achievements-container" wx:if="{{currentTab === 'achievements' && !isLoading && !loadingFailed}}">
    <!-- 网格视图 -->
    <block wx:if="{{type === 'grid'}}">
      <view class="achievements-grid">
        <view 
          wx:for="{{achievements}}" 
          wx:key="id"
          class="{{getItemClass(item)}}"
          data-type="achievement"
          data-id="{{item.id}}"
          bindtap="handleItemClick"
          wx:if="{{item.unlocked || showLocked}}"
        >
          <view class="achievement-icon">
            <image 
              src="{{item.iconUrl}}" 
              mode="aspectFit"
              class="{{item.unlocked ? '' : 'locked-image'}}"
            ></image>
            <view class="lock-icon" wx:if="{{!item.unlocked}}">🔒</view>
          </view>
          <view class="achievement-name">{{item.name}}</view>
          <view class="achievement-date" wx:if="{{item.unlocked && item.unlockedAt}}">
            {{formatDate(item.unlockedAt)}}
          </view>
        </view>
      </view>
    </block>
    
    <!-- 列表视图 -->
    <block wx:elif="{{type === 'list'}}">
      <view class="achievements-list">
        <view 
          wx:for="{{achievements}}" 
          wx:key="id"
          class="{{getItemClass(item)}}"
          data-type="achievement"
          data-id="{{item.id}}"
          bindtap="handleItemClick"
          wx:if="{{item.unlocked || showLocked}}"
        >
          <view class="achievement-icon">
            <image 
              src="{{item.iconUrl}}" 
              mode="aspectFit"
              class="{{item.unlocked ? '' : 'locked-image'}}"
            ></image>
            <view class="lock-icon" wx:if="{{!item.unlocked}}">🔒</view>
          </view>
          <view class="achievement-info">
            <view class="achievement-name">{{item.name}}</view>
            <view class="achievement-description">{{item.description}}</view>
            <view class="achievement-date" wx:if="{{item.unlocked && item.unlockedAt}}">
              获得于: {{formatDate(item.unlockedAt)}}
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 轮播视图 -->
    <block wx:elif="{{type === 'carousel'}}">
      <swiper class="achievements-carousel" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}">
        <swiper-item 
          wx:for="{{achievements}}" 
          wx:key="id"
          wx:if="{{item.unlocked || showLocked}}"
        >
          <view 
            class="{{getItemClass(item)}}"
            data-type="achievement"
            data-id="{{item.id}}"
            bindtap="handleItemClick"
          >
            <view class="achievement-icon">
              <image 
                src="{{item.iconUrl}}" 
                mode="aspectFit"
                class="{{item.unlocked ? '' : 'locked-image'}}"
              ></image>
              <view class="lock-icon" wx:if="{{!item.unlocked}}">🔒</view>
            </view>
            <view class="achievement-name">{{item.name}}</view>
            <view class="achievement-description">{{item.description}}</view>
            <view class="achievement-date" wx:if="{{item.unlocked && item.unlockedAt}}">
              获得于: {{formatDate(item.unlockedAt)}}
            </view>
          </view>
        </swiper-item>
      </swiper>
    </block>
  </view>
  
  <!-- 徽章展示 -->
  <view class="badges-container" wx:if="{{currentTab === 'badges' && !isLoading && !loadingFailed}}">
    <!-- 网格视图 -->
    <block wx:if="{{type === 'grid'}}">
      <view class="badges-grid">
        <view 
          wx:for="{{badges}}" 
          wx:key="id"
          class="{{getItemClass(item)}}"
          data-type="badge"
          data-id="{{item.id}}"
          bindtap="handleItemClick"
          wx:if="{{item.unlocked || showLocked}}"
        >
          <view class="badge-icon">
            <image 
              src="{{item.iconUrl}}" 
              mode="aspectFit"
              class="{{item.unlocked ? '' : 'locked-image'}}"
            ></image>
            <view class="lock-icon" wx:if="{{!item.unlocked}}">🔒</view>
          </view>
          <view class="badge-name">{{item.name}}</view>
          <view class="badge-date" wx:if="{{item.unlocked && item.unlockedAt}}">
            {{formatDate(item.unlockedAt)}}
          </view>
        </view>
      </view>
    </block>
    
    <!-- 列表视图 -->
    <block wx:elif="{{type === 'list'}}">
      <view class="badges-list">
        <view 
          wx:for="{{badges}}" 
          wx:key="id"
          class="{{getItemClass(item)}}"
          data-type="badge"
          data-id="{{item.id}}"
          bindtap="handleItemClick"
          wx:if="{{item.unlocked || showLocked}}"
        >
          <view class="badge-icon">
            <image 
              src="{{item.iconUrl}}" 
              mode="aspectFit"
              class="{{item.unlocked ? '' : 'locked-image'}}"
            ></image>
            <view class="lock-icon" wx:if="{{!item.unlocked}}">🔒</view>
          </view>
          <view class="badge-info">
            <view class="badge-name">{{item.name}}</view>
            <view class="badge-description">{{item.description}}</view>
            <view class="badge-date" wx:if="{{item.unlocked && item.unlockedAt}}">
              获得于: {{formatDate(item.unlockedAt)}}
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 轮播视图 -->
    <block wx:elif="{{type === 'carousel'}}">
      <swiper class="badges-carousel" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}">
        <swiper-item 
          wx:for="{{badges}}" 
          wx:key="id"
          wx:if="{{item.unlocked || showLocked}}"
        >
          <view 
            class="{{getItemClass(item)}}"
            data-type="badge"
            data-id="{{item.id}}"
            bindtap="handleItemClick"
          >
            <view class="badge-icon">
              <image 
                src="{{item.iconUrl}}" 
                mode="aspectFit"
                class="{{item.unlocked ? '' : 'locked-image'}}"
              ></image>
              <view class="lock-icon" wx:if="{{!item.unlocked}}">🔒</view>
            </view>
            <view class="badge-name">{{item.name}}</view>
            <view class="badge-description">{{item.description}}</view>
            <view class="badge-date" wx:if="{{item.unlocked && item.unlockedAt}}">
              获得于: {{formatDate(item.unlockedAt)}}
            </view>
          </view>
        </swiper-item>
      </swiper>
    </block>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!isLoading && !loadingFailed && ((currentTab === 'achievements' && achievements.length === 0) || (currentTab === 'badges' && badges.length === 0))}}">
    <view class="empty-icon">🏆</view>
    <text class="empty-text">{{currentTab === 'achievements' ? '暂无成就' : '暂无徽章'}}</text>
    <text class="empty-subtext">继续学习，解锁更多{{currentTab === 'achievements' ? '成就' : '徽章'}}</text>
  </view>
</view>
