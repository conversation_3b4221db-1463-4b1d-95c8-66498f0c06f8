/* 成就与徽章展示组件样式 */
.nl-achievement-display {
  width: 100%;
}

/* 标签页 */
.tabs {
  display: flex;
  margin-bottom: var(--space-md);
  border-bottom: 1px solid var(--divider-color);
}

.tab {
  flex: 1;
  text-align: center;
  padding: var(--space-sm) 0;
  font-size: var(--font-size-md);
  color: var(--text-color-secondary);
  position: relative;
}

.tab.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--grey-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}

/* 加载失败状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl) 0;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: var(--error-color);
  color: var(--white);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-text {
  margin-top: var(--space-sm);
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
}

.retry-button {
  margin-top: var(--space-md);
  padding: var(--space-xs) var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--white);
  background-color: var(--primary-color);
  border-radius: var(--radius-md);
}

/* 网格视图 */
.achievements-grid, .badges-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md);
  padding: var(--space-sm);
}

.nl-achievement-display-grid .achievement-item,
.nl-achievement-display-grid .badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  background-color: var(--bg-color-card);
  box-shadow: var(--shadow-xs);
  transition: all 0.2s ease;
}

.nl-achievement-display-grid .achievement-item:active,
.nl-achievement-display-grid .badge-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

.nl-achievement-display-grid .achievement-icon,
.nl-achievement-display-grid .badge-icon {
  width: 100rpx;
  height: 100rpx;
  position: relative;
  margin-bottom: var(--space-xs);
}

.nl-achievement-display-grid .achievement-name,
.nl-achievement-display-grid .badge-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  text-align: center;
  margin-bottom: 4rpx;
}

.nl-achievement-display-grid .achievement-date,
.nl-achievement-display-grid .badge-date {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  text-align: center;
}

/* 列表视图 */
.achievements-list, .badges-list {
  padding: var(--space-sm);
}

.nl-achievement-display-list .achievement-item,
.nl-achievement-display-list .badge-item {
  display: flex;
  padding: var(--space-md);
  margin-bottom: var(--space-sm);
  border-radius: var(--radius-md);
  background-color: var(--bg-color-card);
  box-shadow: var(--shadow-xs);
  transition: all 0.2s ease;
}

.nl-achievement-display-list .achievement-item:active,
.nl-achievement-display-list .badge-item:active {
  transform: translateX(4rpx);
  box-shadow: var(--shadow-sm);
}

.nl-achievement-display-list .achievement-icon,
.nl-achievement-display-list .badge-icon {
  width: 80rpx;
  height: 80rpx;
  position: relative;
  margin-right: var(--space-md);
}

.nl-achievement-display-list .achievement-info,
.nl-achievement-display-list .badge-info {
  flex: 1;
}

.nl-achievement-display-list .achievement-name,
.nl-achievement-display-list .badge-name {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: 4rpx;
}

.nl-achievement-display-list .achievement-description,
.nl-achievement-display-list .badge-description {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  margin-bottom: var(--space-xs);
}

.nl-achievement-display-list .achievement-date,
.nl-achievement-display-list .badge-date {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

/* 轮播视图 */
.achievements-carousel, .badges-carousel {
  height: 400rpx;
}

.nl-achievement-display-carousel .achievement-item,
.nl-achievement-display-carousel .badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-md);
  height: 100%;
}

.nl-achievement-display-carousel .achievement-icon,
.nl-achievement-display-carousel .badge-icon {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  margin-bottom: var(--space-md);
}

.nl-achievement-display-carousel .achievement-name,
.nl-achievement-display-carousel .badge-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  text-align: center;
  margin-bottom: var(--space-xs);
}

.nl-achievement-display-carousel .achievement-description,
.nl-achievement-display-carousel .badge-description {
  font-size: var(--font-size-md);
  color: var(--text-color-secondary);
  text-align: center;
  margin-bottom: var(--space-sm);
}

.nl-achievement-display-carousel .achievement-date,
.nl-achievement-display-carousel .badge-date {
  font-size: var(--font-size-sm);
  color: var(--text-color-tertiary);
  text-align: center;
}

/* 锁定状态 */
.achievement-item.locked,
.badge-item.locked {
  opacity: 0.7;
}

.locked-image {
  filter: grayscale(100%);
  opacity: 0.5;
}

.lock-icon {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  font-size: var(--font-size-md);
}

/* 图标图片 */
.achievement-icon image,
.badge-icon image {
  width: 100%;
  height: 100%;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl) 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: var(--space-md);
}

.empty-text {
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  margin-bottom: var(--space-xs);
}

.empty-subtext {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}
