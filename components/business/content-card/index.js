/**
 * 内容卡片组件
 * 用于展示练习、观点、笔记等内容
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 内容类型：exercise(练习), insight(观点), note(笔记)
    contentType: {
      type: String,
      value: ''
    },
    // 内容数据
    contentData: {
      type: Object,
      value: null,
      observer: function (newVal) {
        if (newVal) {
          this.setData({
            data: newVal
          });
        }
      }
    },
    // 卡片尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: true
    },
    // 是否显示标签
    showTags: {
      type: Boolean,
      value: true
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    data: null,
    typeIconMap: {
      'exercise': '📝',
      'insight': '💡',
      'note': '📒'
    },
    typeTextMap: {
      'exercise': '练习',
      'insight': '观点',
      'note': '笔记'
    },
    typeColorMap: {
      'exercise': 'var(--primary-color)',
      'insight': 'var(--warning-color)',
      'note': 'var(--success-color)'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击卡片事件
     */
    handleCardClick() {
      if (this.data.clickable) {
        this.triggerEvent('click', {
          contentType: this.data.contentType,
          contentData: this.data.data
        });
      }
    },

    /**
     * 点击查看按钮事件
     */
    handleView(e) {
      e.stopPropagation();
      this.triggerEvent('view', {
        contentType: this.data.contentType,
        contentData: this.data.data
      });
    },

    /**
     * 点击编辑按钮事件
     */
    handleEdit(e) {
      e.stopPropagation();
      this.triggerEvent('edit', {
        contentType: this.data.contentType,
        contentData: this.data.data
      });
    },

    /**
     * 点击删除按钮事件
     */
    handleDelete(e) {
      e.stopPropagation();
      this.triggerEvent('delete', {
        contentType: this.data.contentType,
        contentData: this.data.data
      });
    },

    /**
     * 获取卡片样式类
     */
    getCardClass() {
      const { size, contentType } = this.data;
      return `nl-content-card nl-content-card-${size} nl-content-card-${contentType}`;
    },

    /**
     * 获取卡片样式
     */
    getCardStyle() {
      return this.data.customStyle;
    },

    /**
     * 获取内容类型图标
     */
    getTypeIcon() {
      return this.data.typeIconMap[this.data.contentType] || '📄';
    },

    /**
     * 获取内容类型文本
     */
    getTypeText() {
      return this.data.typeTextMap[this.data.contentType] || '内容';
    },

    /**
     * 获取内容类型颜色
     */
    getTypeColor() {
      return this.data.typeColorMap[this.data.contentType] || 'var(--primary-color)';
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    /**
     * 截断文本
     */
    truncateText(text, length = 100) {
      if (!text) return '';

      if (text.length <= length) return text;

      return text.substring(0, length) + '...';
    }
  }
});
