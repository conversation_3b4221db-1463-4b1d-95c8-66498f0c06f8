<nl-card
  class="{{getCardClass()}}"
  style="{{getCardStyle()}}"
  clickable="{{clickable}}"
  bind:click="handleCardClick"
>
  <!-- 卡片内容 -->
  <view class="content-card-inner">
    <!-- 内容类型标识 -->
    <view class="content-type" style="color: {{getTypeColor()}}">
      <text class="type-icon">{{getTypeIcon()}}</text>
      <text class="type-text">{{getTypeText()}}</text>
      <text class="ai-generated-badge" wx:if="{{data.isAiGenerated}}">AI生成</text>
    </view>

    <!-- 内容标题 -->
    <view class="content-title" wx:if="{{data.title}}">
      {{data.title}}
    </view>

    <!-- 内容预览 -->
    <view class="content-preview">
      <block wx:if="{{contentType === 'exercise'}}">
        <view class="exercise-preview">
          <text>{{truncateText(data.content, 100)}}</text>
        </view>
      </block>

      <block wx:elif="{{contentType === 'insight'}}">
        <view class="insight-preview">
          <text>{{truncateText(data.content, 100)}}</text>
        </view>
        <view class="insight-source" wx:if="{{data.source}}">
          —— {{data.source}}
        </view>
      </block>

      <block wx:elif="{{contentType === 'note'}}">
        <view class="note-preview">
          <text>{{truncateText(data.content, 100)}}</text>
        </view>
      </block>
    </view>

    <!-- 标签 -->
    <view class="content-tags" wx:if="{{showTags && data.tags && data.tags.length > 0}}">
      <view
        class="content-tag"
        wx:for="{{data.tags}}"
        wx:key="index"
      >{{item}}</view>
    </view>

    <!-- 元数据 -->
    <view class="content-meta">
      <view class="meta-date">
        {{formatDate(data.createdAt || data.created_at)}}
      </view>

      <view class="meta-status" wx:if="{{data.status}}">
        <text class="status-text">{{data.status === 'completed' ? '已完成' : '未完成'}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="content-actions" wx:if="{{showActions}}">
      <view class="action-button view-button" catchtap="handleView">查看</view>
      <view class="action-button edit-button" catchtap="handleEdit" wx:if="{{contentType !== 'insight'}}">编辑</view>
      <view class="action-button delete-button" catchtap="handleDelete">删除</view>
    </view>
  </view>
</nl-card>
