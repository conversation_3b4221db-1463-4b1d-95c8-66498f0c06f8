/* 内容卡片样式 */
.nl-content-card {
  width: 100%;
  margin-bottom: var(--space-md);
}

/* 卡片内容 */
.content-card-inner {
  padding: var(--space-sm);
}

/* 卡片尺寸 */
.nl-content-card-small .content-card-inner {
  padding: var(--space-xs);
}

.nl-content-card-large .content-card-inner {
  padding: var(--space-md);
}

/* 内容类型标识 */
.content-type {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.type-icon {
  font-size: var(--font-size-lg);
  margin-right: var(--space-xs);
}

.type-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* AI生成标识 */
.ai-generated-badge {
  font-size: var(--font-size-xs);
  background-color: var(--primary-color);
  color: white;
  padding: 2rpx 8rpx;
  border-radius: var(--radius-sm);
  margin-left: var(--space-sm);
}

/* 内容标题 */
.content-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--space-xs);
}

/* 内容预览 */
.content-preview {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  margin-bottom: var(--space-sm);
  line-height: 1.6;
}

.exercise-preview, .insight-preview, .note-preview {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.insight-source {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  text-align: right;
  font-style: italic;
  margin-top: var(--space-xs);
}

/* 标签 */
.content-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: var(--space-xs);
}

.content-tag {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  background-color: var(--bg-color-tag);
  padding: 4rpx 12rpx;
  border-radius: var(--radius-sm);
  margin-right: var(--space-xs);
  margin-bottom: var(--space-xs);
}

/* 元数据 */
.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  margin-bottom: var(--space-xs);
}

.meta-status .status-text {
  padding: 2rpx 8rpx;
  border-radius: var(--radius-sm);
}

/* 操作按钮 */
.content-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--space-sm);
}

.action-button {
  font-size: var(--font-size-xs);
  padding: 6rpx 16rpx;
  border-radius: var(--radius-sm);
  margin-left: var(--space-xs);
}

.view-button {
  color: var(--primary-color);
  background-color: var(--primary-color-light);
}

.edit-button {
  color: var(--warning-color);
  background-color: var(--warning-color-light);
}

.delete-button {
  color: var(--error-color);
  background-color: var(--error-color-light);
}

/* 内容类型特定样式 */
.nl-content-card-exercise {
  border-left: 4rpx solid var(--primary-color);
}

.nl-content-card-insight {
  border-left: 4rpx solid var(--warning-color);
}

.nl-content-card-note {
  border-left: 4rpx solid var(--success-color);
}
