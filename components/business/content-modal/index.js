/**
 * 统一内容展示模态弹窗组件
 * 用于展示练习、观点、笔记等内容
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      value: false,
      observer: function (newVal) {
        if (newVal) {
          this.setData({ show: true });
          setTimeout(() => {
            this.setData({ animationVisible: true });
          }, 50);
        } else {
          this.setData({ animationVisible: false });
          setTimeout(() => {
            this.setData({ show: false });
          }, 300);
        }
      }
    },
    // 内容类型：exercise(练习), insight(观点), note(笔记)
    contentType: {
      type: String,
      value: ''
    },
    // 内容数据
    contentData: {
      type: Object,
      value: null
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      value: true
    },
    // 是否显示标题
    showTitle: {
      type: Boolean,
      value: true
    },
    // 自定义标题
    customTitle: {
      type: String,
      value: ''
    },
    // 是否显示底部按钮
    showFooter: {
      type: Boolean,
      value: true
    },
    // 是否需要滚动到底部才能完成
    requireScrollToBottom: {
      type: Boolean,
      value: false
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    show: false,
    animationVisible: false,
    isScrolledToBottom: false,
    userAnswer: '',
    selectedOption: '',
    isSubmitting: false,
    isCompleted: false,
    feedbackMessage: '',
    feedbackType: '' // success, error, info
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关闭弹窗
     */
    handleClose() {
      this.triggerEvent('close');
    },

    /**
     * 阻止冒泡
     */
    preventBubble() {
      // 阻止冒泡，防止点击内容区域时关闭弹窗
      return;
    },

    /**
     * 处理滚动事件
     */
    handleScroll(e) {
      if (this.data.requireScrollToBottom && !this.data.isScrolledToBottom) {
        const { scrollTop, scrollHeight, clientHeight } = e.detail;
        // 判断是否滚动到底部（允许10px的误差）
        if (scrollTop + clientHeight >= scrollHeight - 10) {
          this.setData({ isScrolledToBottom: true });
        }
      }
    },

    /**
     * 处理输入变化
     */
    handleInputChange(e) {
      this.setData({ userAnswer: e.detail.value });
    },

    /**
     * 处理选项选择
     */
    handleOptionSelect(e) {
      const { option } = e.currentTarget.dataset;
      this.setData({ selectedOption: option });
    },

    /**
     * 提交答案
     */
    handleSubmit() {
      const { contentType, contentData } = this.data;
      let answer = '';

      if (contentType === 'exercise') {
        // 根据练习类型获取答案
        if (contentData.type === 'text') {
          answer = this.data.userAnswer;
        } else if (contentData.type === 'choice') {
          answer = this.data.selectedOption;
        }
      }

      this.setData({ isSubmitting: true });

      // 触发提交事件
      this.triggerEvent('submit', {
        contentType,
        contentId: contentData.id,
        answer
      });

      // 模拟提交后的反馈
      setTimeout(() => {
        this.setData({
          isSubmitting: false,
          isCompleted: true,
          feedbackMessage: '提交成功！',
          feedbackType: 'success'
        });

        // 触发完成事件
        this.triggerEvent('complete', {
          contentType,
          contentId: contentData.id
        });
      }, 1000);
    },

    /**
     * 标记为已完成
     */
    handleMarkComplete() {
      const { contentType, contentData } = this.data;

      this.setData({ isSubmitting: true });

      // 触发完成事件
      this.triggerEvent('complete', {
        contentType,
        contentId: contentData.id
      });

      // 模拟提交后的反馈
      setTimeout(() => {
        this.setData({
          isSubmitting: false,
          isCompleted: true,
          feedbackMessage: '已标记为完成！',
          feedbackType: 'success'
        });
      }, 500);
    },

    /**
     * 获取内容标题
     */
    getContentTitle() {
      const { customTitle, contentType, contentData } = this.data;

      if (customTitle) return customTitle;

      if (!contentData) return '';

      if (contentType === 'exercise') {
        return contentData.title || '练习';
      } else if (contentType === 'insight') {
        return '观点';
      } else if (contentType === 'note') {
        return contentData.title || '笔记';
      }

      return '';
    },

    /**
     * 获取模态框样式类
     */
    getModalClass() {
      const { animationVisible, contentType } = this.data;
      return `content-modal ${animationVisible ? 'visible' : ''} content-modal-${contentType}`;
    },

    /**
     * 获取模态框样式
     */
    getModalStyle() {
      return this.data.customStyle;
    }
  }
});
