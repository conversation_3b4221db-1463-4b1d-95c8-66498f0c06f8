<view class="content-modal-container" wx:if="{{show}}">
  <!-- 遮罩层 -->
  <view class="modal-overlay {{animationVisible ? 'visible' : ''}}" bindtap="handleClose"></view>
  
  <!-- 模态框内容 -->
  <view class="{{getModalClass()}}" style="{{getModalStyle()}}" catchtap="preventBubble">
    <!-- 标题栏 -->
    <view class="modal-header" wx:if="{{showTitle}}">
      <view class="modal-title">{{getContentTitle()}}</view>
      <view class="modal-close" bindtap="handleClose" wx:if="{{showClose}}">×</view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view 
      class="modal-content" 
      scroll-y="{{true}}" 
      bindscroll="handleScroll"
    >
      <!-- 练习内容 -->
      <block wx:if="{{contentType === 'exercise' && contentData}}">
        <!-- 练习题目 -->
        <view class="exercise-question">
          <rich-text nodes="{{contentData.content}}"></rich-text>
        </view>
        
        <!-- 练习选项（选择题） -->
        <view class="exercise-options" wx:if="{{contentData.type === 'choice' && contentData.options}}">
          <view 
            wx:for="{{contentData.options}}" 
            wx:key="value"
            class="option-item {{selectedOption === item.value ? 'selected' : ''}}"
            data-option="{{item.value}}"
            bindtap="handleOptionSelect"
          >
            <view class="option-marker">{{item.label}}</view>
            <view class="option-content">{{item.text}}</view>
          </view>
        </view>
        
        <!-- 练习输入（文本题） -->
        <view class="exercise-input" wx:if="{{contentData.type === 'text'}}">
          <textarea 
            class="input-textarea" 
            placeholder="请输入你的答案" 
            value="{{userAnswer}}"
            bindinput="handleInputChange"
          ></textarea>
        </view>
      </block>
      
      <!-- 观点内容 -->
      <block wx:elif="{{contentType === 'insight' && contentData}}">
        <view class="insight-content">
          <rich-text nodes="{{contentData.content}}"></rich-text>
        </view>
        
        <view class="insight-source" wx:if="{{contentData.source}}">
          —— {{contentData.source}}
        </view>
      </block>
      
      <!-- 笔记内容 -->
      <block wx:elif="{{contentType === 'note' && contentData}}">
        <view class="note-content">
          <rich-text nodes="{{contentData.content}}"></rich-text>
        </view>
      </block>
      
      <!-- 反馈信息 -->
      <view class="feedback-message {{feedbackType}}" wx:if="{{feedbackMessage}}">
        {{feedbackMessage}}
      </view>
    </scroll-view>
    
    <!-- 底部按钮区域 -->
    <view class="modal-footer" wx:if="{{showFooter && !isCompleted}}">
      <!-- 练习提交按钮 -->
      <block wx:if="{{contentType === 'exercise'}}">
        <button 
          class="submit-button" 
          disabled="{{isSubmitting || (contentData.type === 'choice' && !selectedOption) || (contentData.type === 'text' && !userAnswer)}}" 
          bindtap="handleSubmit"
        >
          {{isSubmitting ? '提交中...' : '提交答案'}}
        </button>
      </block>
      
      <!-- 观点完成按钮 -->
      <block wx:elif="{{contentType === 'insight'}}">
        <button 
          class="complete-button" 
          bindtap="handleMarkComplete"
        >
          {{isSubmitting ? '处理中...' : '我已了解'}}
        </button>
      </block>
      
      <!-- 笔记完成按钮 -->
      <block wx:elif="{{contentType === 'note'}}">
        <button 
          class="complete-button" 
          disabled="{{requireScrollToBottom && !isScrolledToBottom}}" 
          bindtap="handleMarkComplete"
        >
          {{isSubmitting ? '处理中...' : (requireScrollToBottom && !isScrolledToBottom ? '请滚动到底部' : '标记为已读')}}
        </button>
      </block>
    </view>
    
    <!-- 完成后的底部区域 -->
    <view class="modal-footer" wx:if="{{showFooter && isCompleted}}">
      <button class="close-button" bindtap="handleClose">关闭</button>
    </view>
  </view>
</view>
