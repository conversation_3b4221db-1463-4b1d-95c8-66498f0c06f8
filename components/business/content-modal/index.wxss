/* 内容模态弹窗样式 */
.content-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

/* 遮罩层 */
.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-overlay.visible {
  opacity: 1;
}

/* 模态框 */
.content-modal {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 90vh;
  background-color: var(--bg-color-card);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  box-shadow: var(--shadow-lg);
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.content-modal.visible {
  transform: translateY(0);
}

/* 标题栏 */
.modal-header {
  padding: var(--space-md);
  border-bottom: 1px solid var(--divider-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.modal-close {
  font-size: 40rpx;
  color: var(--text-color-secondary);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-full);
}

.modal-close:active {
  background-color: var(--bg-color-hover);
}

/* 内容区域 */
.modal-content {
  flex: 1;
  padding: var(--space-md);
  max-height: 70vh;
}

/* 底部按钮区域 */
.modal-footer {
  padding: var(--space-md);
  border-top: 1px solid var(--divider-color);
  display: flex;
  justify-content: center;
}

/* 按钮样式 */
.submit-button, .complete-button, .close-button {
  width: 80%;
  height: 80rpx;
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-button {
  background-color: var(--primary-color);
  color: var(--white);
}

.submit-button[disabled] {
  background-color: var(--grey-300);
  color: var(--grey-600);
}

.complete-button {
  background-color: var(--success-color);
  color: var(--white);
}

.complete-button[disabled] {
  background-color: var(--grey-300);
  color: var(--grey-600);
}

.close-button {
  background-color: var(--grey-200);
  color: var(--text-color-primary);
}

/* 练习内容样式 */
.exercise-question {
  margin-bottom: var(--space-md);
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  line-height: 1.6;
}

.exercise-options {
  margin-bottom: var(--space-md);
}

.option-item {
  display: flex;
  padding: var(--space-sm);
  border: 1px solid var(--divider-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-sm);
  transition: all 0.2s ease;
}

.option-item.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color-light);
}

.option-marker {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-full);
  background-color: var(--grey-200);
  color: var(--text-color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: var(--space-sm);
}

.option-item.selected .option-marker {
  background-color: var(--primary-color);
  color: var(--white);
}

.option-content {
  flex: 1;
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  display: flex;
  align-items: center;
}

.exercise-input {
  margin-bottom: var(--space-md);
}

.input-textarea {
  width: 100%;
  height: 240rpx;
  border: 1px solid var(--divider-color);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
}

/* 观点内容样式 */
.insight-content {
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  line-height: 1.8;
  margin-bottom: var(--space-md);
}

.insight-source {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  text-align: right;
  font-style: italic;
}

/* 笔记内容样式 */
.note-content {
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  line-height: 1.6;
}

/* 反馈信息样式 */
.feedback-message {
  margin-top: var(--space-md);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  text-align: center;
}

.feedback-message.success {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.feedback-message.error {
  background-color: var(--error-color-light);
  color: var(--error-color);
}

.feedback-message.info {
  background-color: var(--info-color-light);
  color: var(--info-color);
}

/* 内容类型特定样式 */
.content-modal-exercise {
  /* 练习特定样式 */
}

.content-modal-insight {
  /* 观点特定样式 */
}

.content-modal-note {
  /* 笔记特定样式 */
}
