/**
 * 学习计划卡片组件
 * 用于展示学习计划信息，包括标题、描述、进度、标签等
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 学习计划数据
    plan: {
      type: Object,
      value: null,
      observer: function (newVal) {
        if (newVal) {
          this.setData({
            planData: newVal
          });
        }
      }
    },
    // 卡片尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: true
    },
    // 是否显示标签
    showTags: {
      type: Boolean,
      value: true
    },
    // 是否显示进度
    showProgress: {
      type: Boolean,
      value: true
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    planData: null,
    statusMap: {
      'not_started': '未开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'archived': '已归档'
    },
    statusColorMap: {
      'not_started': 'var(--info-color)',
      'in_progress': 'var(--primary-color)',
      'completed': 'var(--success-color)',
      'archived': 'var(--grey-500)'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击卡片事件
     */
    handleCardClick() {
      if (this.data.clickable) {
        this.triggerEvent('click', { plan: this.data.planData });
      }
    },

    /**
     * 点击详情按钮事件
     */
    handleViewDetail(e) {
      e.stopPropagation();
      this.triggerEvent('detail', { plan: this.data.planData });
    },

    /**
     * 点击继续学习按钮事件
     */
    handleContinue(e) {
      e.stopPropagation();
      this.triggerEvent('continue', { plan: this.data.planData });
    },

    /**
     * 获取卡片样式类
     */
    getCardClass() {
      const { size } = this.data;
      return `nl-learning-plan-card nl-learning-plan-card-${size}`;
    },

    /**
     * 获取卡片样式
     */
    getCardStyle() {
      return this.data.customStyle;
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      return this.data.statusMap[status] || '未知状态';
    },

    /**
     * 获取状态颜色
     */
    getStatusColor(status) {
      return this.data.statusColorMap[status] || 'var(--grey-500)';
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  }
});
