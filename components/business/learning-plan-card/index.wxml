<nl-card 
  class="{{getCardClass()}}" 
  style="{{getCardStyle()}}"
  clickable="{{clickable}}"
  bind:click="handleCardClick"
>
  <!-- 卡片内容 -->
  <view class="plan-card-content">
    <!-- 标题和状态 -->
    <view class="plan-header">
      <view class="plan-title">{{planData.title}}</view>
      <view class="plan-status" style="background-color: {{getStatusColor(planData.status)}}">
        {{getStatusText(planData.status)}}
      </view>
    </view>
    
    <!-- 描述 -->
    <view class="plan-description" wx:if="{{planData.description}}">
      {{planData.description}}
    </view>
    
    <!-- 主题和日期信息 -->
    <view class="plan-meta">
      <view class="plan-theme" wx:if="{{planData.themeName}}">
        <view class="theme-dot" style="background-color: {{planData.themeColor || '#3B82F6'}}"></view>
        <text>{{planData.themeName}}</text>
      </view>
      <view class="plan-date">
        {{formatDate(planData.createdAt)}}
      </view>
    </view>
    
    <!-- 进度条 -->
    <view class="plan-progress" wx:if="{{showProgress}}">
      <nl-progress 
        value="{{planData.progress || 0}}" 
        color="{{planData.status === 'completed' ? 'success' : 'primary'}}"
        showText="{{true}}"
      ></nl-progress>
    </view>
    
    <!-- 标签 -->
    <view class="plan-tags" wx:if="{{showTags && planData.tags && planData.tags.length > 0}}">
      <view 
        class="plan-tag" 
        wx:for="{{planData.tags}}" 
        wx:key="index"
      >{{item}}</view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="plan-actions" wx:if="{{showActions}}">
      <nl-button 
        type="default" 
        size="small" 
        text="查看详情" 
        bind:click="handleViewDetail"
      ></nl-button>
      <nl-button 
        type="primary" 
        size="small" 
        text="继续学习" 
        bind:click="handleContinue"
        wx:if="{{planData.status === 'in_progress'}}"
      ></nl-button>
    </view>
  </view>
</nl-card>
