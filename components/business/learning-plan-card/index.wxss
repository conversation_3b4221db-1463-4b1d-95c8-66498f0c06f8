/* 学习计划卡片样式 */
.nl-learning-plan-card {
  width: 100%;
  margin-bottom: var(--space-md);
}

/* 卡片内容 */
.plan-card-content {
  padding: var(--space-sm);
}

/* 卡片尺寸 */
.nl-learning-plan-card-small .plan-card-content {
  padding: var(--space-xs);
}

.nl-learning-plan-card-large .plan-card-content {
  padding: var(--space-md);
}

/* 标题和状态 */
.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.plan-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.plan-status {
  font-size: var(--font-size-xs);
  color: var(--white);
  padding: 4rpx 12rpx;
  border-radius: var(--radius-sm);
  margin-left: var(--space-xs);
}

/* 描述 */
.plan-description {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  margin-bottom: var(--space-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 主题和日期信息 */
.plan-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
}

.plan-theme {
  display: flex;
  align-items: center;
}

.theme-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

/* 进度条 */
.plan-progress {
  margin: var(--space-xs) 0;
}

/* 标签 */
.plan-tags {
  display: flex;
  flex-wrap: wrap;
  margin: var(--space-xs) 0;
}

.plan-tag {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  background-color: var(--bg-color-tag);
  padding: 4rpx 12rpx;
  border-radius: var(--radius-sm);
  margin-right: var(--space-xs);
  margin-bottom: var(--space-xs);
}

/* 操作按钮 */
.plan-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--space-sm);
}

.plan-actions nl-button {
  margin-left: var(--space-xs);
}
