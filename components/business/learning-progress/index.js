/**
 * 学习进度可视化组件
 * 用于展示学习计划的进度，包括日进度、周进度和总体进度
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 学习计划数据
    plan: {
      type: Object,
      value: null,
      observer: function (newVal) {
        if (newVal) {
          this.setData({
            planData: newVal
          });
          this.calculateProgress();
        }
      }
    },
    // 显示类型
    type: {
      type: String,
      value: 'full' // full, simple, calendar
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    planData: null,
    dailyProgress: [],
    weeklyProgress: 0,
    totalProgress: 0,
    daysCompleted: 0,
    totalDays: 0,
    currentDay: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算进度数据
     */
    calculateProgress() {
      const { planData } = this.data;
      if (!planData) return;

      // 计算总天数
      const totalDays = planData.targetDays || 7;

      // 计算已完成天数
      const daysCompleted = planData.completedDays || 0;

      // 计算当前天数（从开始日期到现在的天数）
      let currentDay = 0;
      if (planData.startDate) {
        const startDate = new Date(planData.startDate);
        const today = new Date();
        const diffTime = Math.abs(today - startDate);
        currentDay = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        currentDay = Math.min(currentDay, totalDays);
      }

      // 计算总体进度
      const totalProgress = Math.round((daysCompleted / totalDays) * 100);

      // 计算每日进度
      const dailyProgress = [];
      for (let i = 0; i < totalDays; i++) {
        const isCompleted = i < daysCompleted;
        const isCurrent = i === currentDay - 1;
        const isUpcoming = i >= currentDay;

        dailyProgress.push({
          day: i + 1,
          status: isCompleted ? 'completed' : (isCurrent ? 'current' : (isUpcoming ? 'upcoming' : 'missed')),
          date: this.calculateDayDate(planData.startDate, i)
        });
      }

      // 计算本周进度
      const weeklyProgress = this.calculateWeeklyProgress(dailyProgress);

      this.setData({
        dailyProgress,
        weeklyProgress,
        totalProgress,
        daysCompleted,
        totalDays,
        currentDay
      });
    },

    /**
     * 计算指定日期
     */
    calculateDayDate(startDateStr, dayOffset) {
      if (!startDateStr) return '';

      const startDate = new Date(startDateStr);
      const targetDate = new Date(startDate);
      targetDate.setDate(startDate.getDate() + dayOffset);

      return `${targetDate.getMonth() + 1}/${targetDate.getDate()}`;
    },

    /**
     * 计算本周进度
     */
    calculateWeeklyProgress(dailyProgress) {
      // 获取本周的日期范围
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0是周日，1是周一，以此类推
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)); // 调整为周一

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // 周日

      // 计算本周完成的天数
      let weeklyCompleted = 0;
      let weeklyTotal = 0;

      dailyProgress.forEach(day => {
        if (!day.date) return;

        const [month, date] = day.date.split('/').map(Number);
        const dayDate = new Date(today.getFullYear(), month - 1, date);

        if (dayDate >= startOfWeek && dayDate <= endOfWeek) {
          weeklyTotal++;
          if (day.status === 'completed') {
            weeklyCompleted++;
          }
        }
      });

      return weeklyTotal > 0 ? Math.round((weeklyCompleted / weeklyTotal) * 100) : 0;
    },

    /**
     * 点击日期事件
     */
    handleDayClick(e) {
      const { day } = e.currentTarget.dataset;
      this.triggerEvent('dayclick', { day });
    },

    /**
     * 获取组件样式类
     */
    getProgressClass() {
      const { type } = this.data;
      return `nl-learning-progress nl-learning-progress-${type}`;
    },

    /**
     * 获取组件样式
     */
    getProgressStyle() {
      return this.data.customStyle;
    },

    /**
     * 获取日期状态样式类
     */
    getDayStatusClass(status) {
      return `day-item day-item-${status}`;
    }
  }
});
