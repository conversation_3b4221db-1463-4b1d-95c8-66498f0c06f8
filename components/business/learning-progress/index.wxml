<view class="{{getProgressClass()}}" style="{{getProgressStyle()}}">
  <!-- 全功能进度视图 -->
  <block wx:if="{{type === 'full'}}">
    <!-- 总体进度 -->
    <view class="progress-section">
      <view class="section-title">总体进度</view>
      <view class="progress-info">
        <view class="progress-text">
          <text class="progress-value">{{daysCompleted}}</text>
          <text class="progress-total">/{{totalDays}}天</text>
        </view>
        <nl-progress value="{{totalProgress}}" showText="{{true}}"></nl-progress>
      </view>
    </view>
    
    <!-- 本周进度 -->
    <view class="progress-section">
      <view class="section-title">本周进度</view>
      <view class="progress-info">
        <nl-progress value="{{weeklyProgress}}" showText="{{true}}"></nl-progress>
      </view>
    </view>
    
    <!-- 日进度 -->
    <view class="progress-section">
      <view class="section-title">学习日历</view>
      <view class="daily-progress">
        <view 
          wx:for="{{dailyProgress}}" 
          wx:key="day"
          class="{{getDayStatusClass(item.status)}}"
          data-day="{{item.day}}"
          bindtap="handleDayClick"
        >
          <view class="day-number">{{item.day}}</view>
          <view class="day-date" wx:if="{{item.date}}">{{item.date}}</view>
          <view class="day-status-icon" wx:if="{{item.status === 'completed'}}">✓</view>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 简化进度视图 -->
  <block wx:elif="{{type === 'simple'}}">
    <view class="simple-progress">
      <view class="simple-progress-info">
        <text class="simple-progress-text">{{daysCompleted}}/{{totalDays}}天</text>
        <text class="simple-progress-percent">{{totalProgress}}%</text>
      </view>
      <nl-progress value="{{totalProgress}}" showText="{{false}}"></nl-progress>
    </view>
  </block>
  
  <!-- 日历进度视图 -->
  <block wx:elif="{{type === 'calendar'}}">
    <view class="calendar-progress">
      <view 
        wx:for="{{dailyProgress}}" 
        wx:key="day"
        class="{{getDayStatusClass(item.status)}}"
        data-day="{{item.day}}"
        bindtap="handleDayClick"
      >
        <view class="day-number">{{item.day}}</view>
        <view class="day-status-icon" wx:if="{{item.status === 'completed'}}">✓</view>
      </view>
    </view>
  </block>
</view>
