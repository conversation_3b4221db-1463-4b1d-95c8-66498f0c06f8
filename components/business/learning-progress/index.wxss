/* 学习进度可视化组件样式 */
.nl-learning-progress {
  width: 100%;
}

/* 进度区块 */
.progress-section {
  margin-bottom: var(--space-md);
}

.section-title {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  margin-bottom: var(--space-xs);
}

.progress-info {
  display: flex;
  flex-direction: column;
}

.progress-text {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--space-xs);
}

.progress-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.progress-total {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  margin-left: var(--space-xs);
}

/* 日进度 */
.daily-progress {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.day-item {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-sm);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.day-number {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.day-date {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
}

.day-status-icon {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: var(--success-color);
  color: var(--white);
  font-size: var(--font-size-xs);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 日期状态样式 */
.day-item-completed {
  background-color: var(--success-color-light);
  border: 1px solid var(--success-color);
  color: var(--success-color);
}

.day-item-current {
  background-color: var(--primary-color-light);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.day-item-upcoming {
  background-color: var(--bg-color-card);
  border: 1px solid var(--divider-color);
  color: var(--text-color-secondary);
}

.day-item-missed {
  background-color: var(--error-color-light);
  border: 1px solid var(--error-color);
  color: var(--error-color);
}

/* 简化进度视图 */
.simple-progress {
  padding: var(--space-xs);
}

.simple-progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-xs);
}

.simple-progress-text {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}

.simple-progress-percent {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

/* 日历进度视图 */
.calendar-progress {
  display: flex;
  justify-content: space-between;
  padding: var(--space-xs);
}

.nl-learning-progress-calendar .day-item {
  width: 60rpx;
  height: 60rpx;
}
