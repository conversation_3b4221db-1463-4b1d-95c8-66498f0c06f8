# 业务组件性能优化指南

本文档提供了针对业务组件的性能优化建议，特别是针对列表渲染和动画效果。

## 列表渲染优化

### 1. 使用虚拟列表

对于长列表，应该使用虚拟列表技术，只渲染可见区域的内容，减少DOM节点数量。

```javascript
// 在组件中实现虚拟列表
Component({
  properties: {
    items: Array
  },
  
  data: {
    visibleItems: [],
    startIndex: 0,
    endIndex: 20,
    itemHeight: 100, // 每个列表项的高度
    scrollTop: 0
  },
  
  methods: {
    onScroll(e) {
      const { scrollTop } = e.detail;
      this.setData({ scrollTop });
      this.updateVisibleItems();
    },
    
    updateVisibleItems() {
      const { scrollTop, itemHeight } = this.data;
      const { items } = this.properties;
      
      // 计算可见区域的起始和结束索引
      const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 5);
      const endIndex = Math.min(items.length, startIndex + 30);
      
      // 更新可见项
      this.setData({
        startIndex,
        endIndex,
        visibleItems: items.slice(startIndex, endIndex)
      });
    }
  }
});
```

```html
<scroll-view 
  scroll-y 
  style="height: 500px;" 
  bindscroll="onScroll"
>
  <view style="height: {{items.length * itemHeight}}px; position: relative;">
    <view 
      wx:for="{{visibleItems}}" 
      wx:key="id"
      style="position: absolute; top: {{(startIndex + index) * itemHeight}}px; height: {{itemHeight}}px; width: 100%;"
    >
      <!-- 列表项内容 -->
    </view>
  </view>
</scroll-view>
```

### 2. 使用 wx:key 优化列表更新

始终为列表项提供唯一的 `wx:key`，这样在列表更新时，微信小程序可以复用已有的组件实例，而不是重新创建。

```html
<view wx:for="{{items}}" wx:key="id">
  <!-- 列表项内容 -->
</view>
```

### 3. 避免频繁更新列表数据

避免在短时间内多次更新列表数据，可以使用节流或防抖技术来减少更新频率。

```javascript
// 使用节流函数
function throttle(fn, delay) {
  let timer = null;
  let lastTime = 0;
  
  return function(...args) {
    const now = Date.now();
    if (now - lastTime >= delay) {
      fn.apply(this, args);
      lastTime = now;
    } else if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, args);
        lastTime = Date.now();
        timer = null;
      }, delay - (now - lastTime));
    }
  };
}

// 在组件中使用
Component({
  attached() {
    this.updateList = throttle(this.updateList, 200);
  },
  
  methods: {
    updateList() {
      // 更新列表数据
    }
  }
});
```

### 4. 使用懒加载

对于图片或其他资源，使用懒加载技术，只在需要时加载资源。

```html
<image 
  wx:for="{{items}}" 
  wx:key="id"
  lazy-load="{{true}}"
  src="{{item.imageUrl}}"
></image>
```

### 5. 分页加载

对于大量数据，使用分页加载，而不是一次性加载所有数据。

```javascript
Component({
  data: {
    items: [],
    page: 1,
    pageSize: 20,
    hasMore: true,
    isLoading: false
  },
  
  methods: {
    loadMore() {
      if (this.data.isLoading || !this.data.hasMore) return;
      
      this.setData({ isLoading: true });
      
      // 加载下一页数据
      api.getItems({
        page: this.data.page,
        pageSize: this.data.pageSize
      }).then(res => {
        const newItems = res.data.items;
        
        this.setData({
          items: [...this.data.items, ...newItems],
          page: this.data.page + 1,
          hasMore: newItems.length === this.data.pageSize,
          isLoading: false
        });
      });
    }
  }
});
```

## 动画效果优化

### 1. 使用 CSS 动画代替 JS 动画

尽可能使用 CSS 动画，而不是 JS 动画，因为 CSS 动画可以利用硬件加速。

```css
.animate-item {
  transition: all 0.3s ease;
}

.animate-item.active {
  transform: scale(1.1);
}
```

```html
<view class="animate-item {{isActive ? 'active' : ''}}"></view>
```

### 2. 使用 transform 和 opacity 属性

在动画中，尽量使用 `transform` 和 `opacity` 属性，而不是改变元素的尺寸或位置，因为这些属性不会触发布局重排。

```css
/* 推荐 */
.item {
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.item.moved {
  transform: translateX(100px);
}

/* 不推荐 */
.item {
  left: 0;
  transition: left 0.3s ease;
  position: relative;
}

.item.moved {
  left: 100px;
}
```

### 3. 使用 will-change 属性

对于复杂的动画，可以使用 `will-change` 属性提前告知浏览器元素将要发生变化，让浏览器提前做好准备。

```css
.animate-item {
  will-change: transform, opacity;
}
```

### 4. 避免同时动画过多元素

避免同时对大量元素应用动画，可以考虑分批次应用动画。

```javascript
Component({
  methods: {
    animateItems() {
      const { items } = this.data;
      const batchSize = 5;
      
      for (let i = 0; i < items.length; i += batchSize) {
        setTimeout(() => {
          const animatedItems = [...this.data.animatedItems];
          
          for (let j = i; j < Math.min(i + batchSize, items.length); j++) {
            animatedItems.push(items[j].id);
          }
          
          this.setData({ animatedItems });
        }, i * 50);
      }
    }
  }
});
```

```html
<view 
  wx:for="{{items}}" 
  wx:key="id"
  class="item {{animatedItems.includes(item.id) ? 'animated' : ''}}"
></view>
```

### 5. 使用 requestAnimationFrame

对于需要使用 JS 实现的动画，使用 `requestAnimationFrame` 来优化性能。

```javascript
Component({
  methods: {
    startAnimation() {
      let start = null;
      const duration = 1000; // 动画持续时间（毫秒）
      
      const animate = timestamp => {
        if (!start) start = timestamp;
        const progress = Math.min((timestamp - start) / duration, 1);
        
        // 更新动画状态
        this.setData({
          animationProgress: progress
        });
        
        if (progress < 1) {
          this.animationId = requestAnimationFrame(animate);
        }
      };
      
      this.animationId = requestAnimationFrame(animate);
    },
    
    stopAnimation() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
        this.animationId = null;
      }
    }
  }
});
```

## 其他优化建议

### 1. 减少 setData 调用次数

尽量减少 `setData` 的调用次数，可以将多个数据更新合并为一次调用。

```javascript
// 不推荐
this.setData({ a: 1 });
this.setData({ b: 2 });
this.setData({ c: 3 });

// 推荐
this.setData({
  a: 1,
  b: 2,
  c: 3
});
```

### 2. 减少 setData 数据量

只更新必要的数据，避免更新整个对象或数组。

```javascript
// 不推荐
this.setData({
  items: this.data.items.map((item, index) => {
    if (index === targetIndex) {
      return { ...item, selected: true };
    }
    return item;
  })
});

// 推荐
this.setData({
  [`items[${targetIndex}].selected`]: true
});
```

### 3. 使用 computed 属性

对于需要计算的属性，可以在 JS 中计算，而不是在模板中计算。

```javascript
// 不推荐
<view wx:for="{{items}}" wx:key="id">
  <text>{{item.price * item.quantity}}</text>
</view>

// 推荐
Component({
  properties: {
    items: Array
  },
  
  observers: {
    'items': function(items) {
      this.setData({
        itemsWithTotal: items.map(item => ({
          ...item,
          total: item.price * item.quantity
        }))
      });
    }
  }
});

<view wx:for="{{itemsWithTotal}}" wx:key="id">
  <text>{{item.total}}</text>
</view>
```

### 4. 使用 IntersectionObserver 实现懒加载

使用 `IntersectionObserver` API 实现更精确的懒加载。

```javascript
Component({
  attached() {
    this.createIntersectionObserver()
      .relativeToViewport()
      .observe('.lazy-load-item', res => {
        if (res.intersectionRatio > 0) {
          // 元素进入可视区域，加载资源
          this.loadResource();
        }
      });
  },
  
  detached() {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }
});
```

### 5. 使用 Canvas 绘制复杂图形

对于复杂的图形或大量的图形元素，考虑使用 Canvas 绘制，而不是使用大量的 DOM 元素。

```javascript
Component({
  attached() {
    const ctx = wx.createCanvasContext('myCanvas', this);
    
    // 绘制图形
    ctx.beginPath();
    ctx.arc(100, 100, 50, 0, 2 * Math.PI);
    ctx.setFillStyle('red');
    ctx.fill();
    
    ctx.draw();
  }
});
```

```html
<canvas canvas-id="myCanvas" style="width: 300px; height: 200px;"></canvas>
```

## 性能测试与监控

### 1. 使用性能监控工具

使用微信小程序提供的性能监控工具，监控页面加载时间、渲染时间等指标。

```javascript
const performance = wx.getPerformance();
const observer = performance.createObserver((entryList) => {
  const entries = entryList.getEntries();
  entries.forEach((entry) => {
    console.log('性能指标:', entry);
  });
});

observer.observe({ entryTypes: ['render', 'script', 'navigation'] });
```

### 2. 定期进行性能测试

定期对应用进行性能测试，特别是在添加新功能或修改现有功能后。

### 3. 使用 Chrome DevTools 进行性能分析

使用 Chrome DevTools 的 Performance 面板分析小程序的性能瓶颈。

## 总结

通过以上优化措施，可以显著提升业务组件的性能，特别是在列表渲染和动画效果方面。在实际开发中，应该根据具体场景选择合适的优化策略，并通过性能测试验证优化效果。
