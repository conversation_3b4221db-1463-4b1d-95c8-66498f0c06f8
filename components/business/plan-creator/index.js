/**
 * 计划创建流程组件
 * 提供学习计划创建的分步引导界面
 */

// 导入API工具
const { learningPlanAPI, themeAPI, tagAPI } = require('../../../utils/api');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 是否显示组件
     */
    visible: {
      type: Boolean,
      value: false,
      observer: function (newVal) {
        if (newVal) {
          // 显示组件时，添加动画效果
          setTimeout(() => {
            this.setData({ animationVisible: true });
          }, 50);

          // 初始化数据
          this.initialize();
        } else {
          this.setData({ animationVisible: false });
        }
      }
    },

    /**
     * 初始数据（用于编辑模式）
     */
    initialData: {
      type: Object,
      value: null
    },

    /**
     * 模式（'create'或'edit'）
     */
    mode: {
      type: String,
      value: 'create'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    animationVisible: false,
    steps: ['主题选择', '标签选择', '计划设置', '预览确认'],
    currentStep: 0,

    // 主题选择
    themes: [],
    selectedTheme: null,

    // 标签选择
    tagCategories: [],
    tags: [],
    filteredTags: [],
    selectedTags: {},
    selectedTagsList: [],
    selectedTagsCount: 0,
    tagSearchText: '',
    selectedCategory: '全部',

    // 计划设置
    planTitle: '',
    planDescription: '',
    targetDays: 7,
    dailyGoalExercises: 3,
    dailyGoalInsights: 5,
    dailyGoalMinutes: 15,
    isPublic: false,

    // 选项数据
    durationOptions: [
      { value: 7, label: '7天' },
      { value: 14, label: '14天' },
      { value: 21, label: '21天' },
      { value: 30, label: '30天' }
    ],
    visibilityOptions: [
      { value: 'private', label: '私密' },
      { value: 'public', label: '公开' }
    ],

    // 状态
    submitting: false,
    savingDraft: false,
    errors: {}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    initialize() {
      // 重置数据
      this.resetData();

      // 加载主题数据
      this.loadThemes();

      // 如果是编辑模式，加载初始数据
      if (this.properties.mode === 'edit' && this.properties.initialData) {
        this.loadInitialData();
      }
    },

    /**
     * 重置数据
     */
    resetData() {
      this.setData({
        currentStep: 0,
        selectedTheme: null,
        selectedTags: {},
        selectedTagsList: [],
        selectedTagsCount: 0,
        tagSearchText: '',
        selectedCategory: '全部',
        planTitle: '',
        planDescription: '',
        targetDays: 7,
        dailyGoalExercises: 3,
        dailyGoalInsights: 5,
        dailyGoalMinutes: 15,
        isPublic: false,
        submitting: false,
        savingDraft: false,
        errors: {}
      });
    },

    /**
     * 加载主题数据
     */
    async loadThemes() {
      try {
        const app = getApp();
        const apiClient = app.globalData.apiClient;

        // 获取主题列表
        const response = await themeAPI(apiClient).getThemes();

        if (response && response.data) {
          this.setData({
            themes: response.data.map(theme => ({
              id: theme.id,
              name: theme.name,
              description: theme.description,
              color: theme.color || '#4CAF50',
              icon: theme.icon || 'book'
            }))
          });

          // 如果有主题数据，默认选择第一个
          if (this.data.themes.length > 0) {
            this.setData({
              selectedTheme: this.data.themes[0]
            });
          }
        }
      } catch (error) {
        console.error('加载主题数据失败:', error);
        wx.showToast({
          title: '加载主题数据失败',
          icon: 'none'
        });
      }
    },

    /**
     * 加载标签数据
     */
    async loadTags() {
      if (!this.data.selectedTheme) return;

      try {
        const app = getApp();
        const apiClient = app.globalData.apiClient;

        // 获取标签列表
        const response = await tagAPI(apiClient).getTags({
          themeId: this.data.selectedTheme.id
        });

        if (response && response.data) {
          // 提取标签分类
          const categories = ['全部'];
          const tags = response.data.map(tag => {
            if (tag.category && !categories.includes(tag.category)) {
              categories.push(tag.category);
            }
            return {
              id: tag.id,
              name: tag.name,
              category: tag.category || '其他'
            };
          });

          this.setData({
            tags,
            filteredTags: tags,
            tagCategories: categories.map(name => ({ name }))
          });
        }
      } catch (error) {
        console.error('加载标签数据失败:', error);
        wx.showToast({
          title: '加载标签数据失败',
          icon: 'none'
        });
      }
    },

    /**
     * 加载初始数据（编辑模式）
     */
    loadInitialData() {
      const { initialData } = this.properties;

      if (!initialData) return;

      // 设置主题
      if (initialData.themeId) {
        const theme = this.data.themes.find(t => t.id === initialData.themeId);
        if (theme) {
          this.setData({ selectedTheme: theme });
        }
      }

      // 设置标签
      if (initialData.tags && initialData.tags.length > 0) {
        const selectedTags = {};
        initialData.tags.forEach(tag => {
          selectedTags[tag.id] = true;
        });

        this.setData({
          selectedTags,
          selectedTagsList: initialData.tags,
          selectedTagsCount: initialData.tags.length
        });
      }

      // 设置计划信息
      this.setData({
        planTitle: initialData.title || '',
        planDescription: initialData.description || '',
        targetDays: initialData.targetDays || 7,
        dailyGoalExercises: initialData.dailyGoalExercises || 3,
        dailyGoalInsights: initialData.dailyGoalInsights || 5,
        dailyGoalMinutes: initialData.dailyGoalMinutes || 15,
        isPublic: initialData.isPublic || false
      });
    },

    /**
     * 处理步骤点击
     */
    handleStepClick(e) {
      const step = e.currentTarget.dataset.step;

      // 只允许点击已完成的步骤
      if (step < this.data.currentStep) {
        this.setData({ currentStep: step });
      }
    },

    /**
     * 处理取消
     */
    handleCancel() {
      this.triggerEvent('cancel');
    },

    /**
     * 处理主题选择
     */
    handleThemeSelect(e) {
      const theme = e.currentTarget.dataset.theme;

      this.setData({
        selectedTheme: theme,
        errors: {
          ...this.data.errors,
          theme: ''
        }
      });
    },

    /**
     * 处理标签搜索
     */
    handleTagSearch(e) {
      const searchText = e.detail.value;

      this.setData({
        tagSearchText: searchText
      });

      this.filterTags();
    },

    /**
     * 处理分类选择
     */
    handleCategorySelect(e) {
      const category = e.currentTarget.dataset.category;

      this.setData({
        selectedCategory: category
      });

      this.filterTags();
    },

    /**
     * 过滤标签
     */
    filterTags() {
      const { tags, tagSearchText, selectedCategory } = this.data;

      let filtered = tags;

      // 按搜索文本过滤
      if (tagSearchText) {
        filtered = filtered.filter(tag =>
          tag.name.toLowerCase().includes(tagSearchText.toLowerCase())
        );
      }

      // 按分类过滤
      if (selectedCategory !== '全部') {
        filtered = filtered.filter(tag => tag.category === selectedCategory);
      }

      this.setData({
        filteredTags: filtered
      });
    },

    /**
     * 处理标签切换
     */
    handleTagToggle(e) {
      const tag = e.currentTarget.dataset.tag;
      const selectedTags = { ...this.data.selectedTags };

      if (selectedTags[tag.id]) {
        delete selectedTags[tag.id];
      } else {
        selectedTags[tag.id] = true;
      }

      // 更新已选标签列表
      const selectedTagsList = this.data.tags.filter(t => selectedTags[t.id]);

      this.setData({
        selectedTags,
        selectedTagsList,
        selectedTagsCount: selectedTagsList.length,
        errors: {
          ...this.data.errors,
          tags: ''
        }
      });
    },

    /**
     * 处理标签移除
     */
    handleTagRemove(e) {
      const tagId = e.currentTarget.dataset.tagId;
      const selectedTags = { ...this.data.selectedTags };

      delete selectedTags[tagId];

      // 更新已选标签列表
      const selectedTagsList = this.data.tags.filter(t => selectedTags[t.id]);

      this.setData({
        selectedTags,
        selectedTagsList,
        selectedTagsCount: selectedTagsList.length
      });
    },

    /**
     * 处理标题输入
     */
    handleTitleInput(e) {
      this.setData({
        planTitle: e.detail.value,
        errors: {
          ...this.data.errors,
          title: ''
        }
      });
    },

    /**
     * 处理描述输入
     */
    handleDescriptionInput(e) {
      this.setData({
        planDescription: e.detail.value
      });
    },

    /**
     * 处理时长选择
     */
    handleDurationSelect(e) {
      const value = e.currentTarget.dataset.value;

      this.setData({
        targetDays: value
      });
    },

    /**
     * 处理练习数量变更
     */
    handleExercisesChange(e) {
      this.setData({
        dailyGoalExercises: e.detail.value
      });
    },

    /**
     * 处理观点数量变更
     */
    handleInsightsChange(e) {
      this.setData({
        dailyGoalInsights: e.detail.value
      });
    },

    /**
     * 处理学习时间变更
     */
    handleMinutesChange(e) {
      this.setData({
        dailyGoalMinutes: e.detail.value
      });
    },

    /**
     * 处理可见性变更
     */
    handleVisibilityChange(e) {
      this.setData({
        isPublic: e.detail.value === 'public'
      });
    },

    /**
     * 处理编辑计划
     */
    handleEditPlan() {
      // 返回到计划设置步骤
      this.setData({
        currentStep: 2
      });
    },

    /**
     * 处理上一步
     */
    handlePrevStep() {
      if (this.data.currentStep > 0) {
        this.setData({
          currentStep: this.data.currentStep - 1
        });
      }
    },

    /**
     * 处理下一步
     */
    handleNextStep() {
      // 验证当前步骤
      if (!this.validateCurrentStep()) {
        return;
      }

      // 如果是第一步完成，加载标签数据
      if (this.data.currentStep === 0) {
        this.loadTags();
      }

      // 进入下一步
      if (this.data.currentStep < this.data.steps.length - 1) {
        this.setData({
          currentStep: this.data.currentStep + 1
        });
      }
    },

    /**
     * 验证当前步骤
     */
    validateCurrentStep() {
      const { currentStep, selectedTheme, selectedTagsCount, planTitle } = this.data;
      const errors = {};

      if (currentStep === 0) {
        // 验证主题选择
        if (!selectedTheme) {
          errors.theme = '请选择一个学习主题';
        }
      } else if (currentStep === 1) {
        // 验证标签选择（可选）
      } else if (currentStep === 2) {
        // 验证计划设置
        if (!planTitle.trim()) {
          errors.title = '请输入计划标题';
        }
      }

      this.setData({ errors });

      return Object.keys(errors).length === 0;
    },

    /**
     * 处理保存草稿
     */
    async handleSaveDraft() {
      // 验证必填字段
      if (!this.data.selectedTheme || !this.data.planTitle.trim()) {
        wx.showToast({
          title: '请填写主题和标题',
          icon: 'none'
        });
        return;
      }

      this.setData({ savingDraft: true });

      try {
        // 构建草稿数据
        const draftData = this.buildPlanData();

        // 触发保存草稿事件
        this.triggerEvent('draft', { data: draftData });

        this.setData({ savingDraft: false });

        wx.showToast({
          title: '草稿保存成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('保存草稿失败:', error);

        this.setData({ savingDraft: false });

        wx.showToast({
          title: '保存草稿失败',
          icon: 'none'
        });
      }
    },

    /**
     * 处理提交
     */
    async handleSubmit() {
      // 验证所有步骤
      if (!this.validateAllSteps()) {
        return;
      }

      this.setData({ submitting: true });

      try {
        // 构建计划数据
        const planData = this.buildPlanData();

        // 触发提交事件
        this.triggerEvent('submit', { data: planData });

        this.setData({ submitting: false });
      } catch (error) {
        console.error('提交计划失败:', error);

        this.setData({ submitting: false });

        wx.showToast({
          title: '提交计划失败',
          icon: 'none'
        });
      }
    },

    /**
     * 验证所有步骤
     */
    validateAllSteps() {
      const { selectedTheme, planTitle } = this.data;
      const errors = {};

      // 验证主题选择
      if (!selectedTheme) {
        errors.theme = '请选择一个学习主题';
        this.setData({ currentStep: 0 });
        this.setData({ errors });
        return false;
      }

      // 验证计划设置
      if (!planTitle.trim()) {
        errors.title = '请输入计划标题';
        this.setData({ currentStep: 2 });
        this.setData({ errors });
        return false;
      }

      return true;
    },

    /**
     * 构建计划数据
     */
    buildPlanData() {
      const {
        selectedTheme,
        selectedTagsList,
        planTitle,
        planDescription,
        targetDays,
        dailyGoalExercises,
        dailyGoalInsights,
        dailyGoalMinutes,
        isPublic
      } = this.data;

      return {
        themeId: selectedTheme.id,
        title: planTitle,
        description: planDescription,
        targetDays,
        dailyGoalExercises,
        dailyGoalInsights,
        dailyGoalMinutes,
        isPublic,
        tags: selectedTagsList.map(tag => tag.id),
        mode: this.properties.mode
      };
    }
  }
});
