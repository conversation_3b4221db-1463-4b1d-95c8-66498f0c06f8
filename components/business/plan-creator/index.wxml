<!-- 计划创建流程组件 -->
<view class="plan-creator {{animationVisible ? 'visible' : ''}}" wx:if="{{visible}}">
  <!-- 顶部导航 -->
  <view class="creator-header">
    <view class="step-nav">
      <view 
        wx:for="{{steps}}" 
        wx:key="index"
        class="step-item {{currentStep >= index ? 'active' : ''}}"
        bindtap="handleStepClick"
        data-step="{{index}}"
      >
        <view class="step-number">{{index + 1}}</view>
        <view class="step-name">{{item}}</view>
      </view>
    </view>
    
    <view class="header-actions">
      <view class="close-btn" bindtap="handleCancel">×</view>
    </view>
  </view>
  
  <!-- 内容区域 -->
  <view class="creator-content">
    <!-- 主题选择步骤 -->
    <view class="step-content" wx:if="{{currentStep === 0}}">
      <view class="step-title">选择学习主题</view>
      <view class="theme-list">
        <view 
          wx:for="{{themes}}" 
          wx:key="id"
          class="theme-item {{selectedTheme.id === item.id ? 'selected' : ''}}"
          bindtap="handleThemeSelect"
          data-theme="{{item}}"
        >
          <view class="theme-icon" style="background-color: {{item.color}}">
            <nl-icon name="{{item.icon}}" size="24" color="#FFFFFF" />
          </view>
          <view class="theme-info">
            <view class="theme-name">{{item.name}}</view>
            <view class="theme-desc">{{item.description}}</view>
          </view>
          <view class="theme-check" wx:if="{{selectedTheme.id === item.id}}">
            <nl-icon name="check-circle" size="20" color="#4CAF50" />
          </view>
        </view>
      </view>
      <view class="error-message" wx:if="{{errors.theme}}">{{errors.theme}}</view>
    </view>
    
    <!-- 标签选择步骤 -->
    <view class="step-content" wx:if="{{currentStep === 1}}">
      <view class="step-title">选择学习标签</view>
      <view class="tag-search">
        <nl-input 
          placeholder="搜索标签" 
          value="{{tagSearchText}}"
          bindinput="handleTagSearch"
          leftIcon="search"
        />
      </view>
      <view class="tag-categories">
        <view 
          wx:for="{{tagCategories}}" 
          wx:key="name"
          class="category-item {{selectedCategory === item.name ? 'selected' : ''}}"
          bindtap="handleCategorySelect"
          data-category="{{item.name}}"
        >
          {{item.name}}
        </view>
      </view>
      <view class="tag-list">
        <view 
          wx:for="{{filteredTags}}" 
          wx:key="id"
          class="tag-item {{selectedTags[item.id] ? 'selected' : ''}}"
          bindtap="handleTagToggle"
          data-tag="{{item}}"
        >
          <view class="tag-name">{{item.name}}</view>
          <view class="tag-check" wx:if="{{selectedTags[item.id]}}">
            <nl-icon name="check" size="16" color="#FFFFFF" />
          </view>
        </view>
      </view>
      <view class="selected-tags-preview" wx:if="{{selectedTagsCount > 0}}">
        <view class="preview-title">已选择 {{selectedTagsCount}} 个标签</view>
        <view class="selected-tags-list">
          <view 
            wx:for="{{selectedTagsList}}" 
            wx:key="id"
            class="selected-tag-item"
          >
            <view class="tag-name">{{item.name}}</view>
            <view class="tag-remove" bindtap="handleTagRemove" data-tag-id="{{item.id}}">×</view>
          </view>
        </view>
      </view>
      <view class="error-message" wx:if="{{errors.tags}}">{{errors.tags}}</view>
    </view>
    
    <!-- 计划设置步骤 -->
    <view class="step-content" wx:if="{{currentStep === 2}}">
      <view class="step-title">设置学习计划</view>
      <view class="form-group">
        <view class="form-label">计划标题</view>
        <nl-input 
          placeholder="请输入计划标题" 
          value="{{planTitle}}"
          bindinput="handleTitleInput"
          maxlength="100"
        />
        <view class="input-counter">{{planTitle.length}}/100</view>
        <view class="error-message" wx:if="{{errors.title}}">{{errors.title}}</view>
      </view>
      
      <view class="form-group">
        <view class="form-label">计划描述</view>
        <nl-textarea 
          placeholder="请输入计划描述（选填）" 
          value="{{planDescription}}"
          bindinput="handleDescriptionInput"
          maxlength="1000"
          autoHeight
        />
        <view class="input-counter">{{planDescription.length}}/1000</view>
      </view>
      
      <view class="form-group">
        <view class="form-label">目标天数</view>
        <view class="duration-options">
          <view 
            wx:for="{{durationOptions}}" 
            wx:key="value"
            class="duration-option {{targetDays === item.value ? 'selected' : ''}}"
            bindtap="handleDurationSelect"
            data-value="{{item.value}}"
          >
            <view class="duration-value">{{item.label}}</view>
          </view>
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-label">每日目标</view>
        <view class="daily-goal-item">
          <view class="goal-label">练习数量</view>
          <nl-slider 
            min="1" 
            max="10" 
            value="{{dailyGoalExercises}}" 
            bindchange="handleExercisesChange"
            showValue
          />
        </view>
        <view class="daily-goal-item">
          <view class="goal-label">观点数量</view>
          <nl-slider 
            min="1" 
            max="10" 
            value="{{dailyGoalInsights}}" 
            bindchange="handleInsightsChange"
            showValue
          />
        </view>
        <view class="daily-goal-item">
          <view class="goal-label">学习时间（分钟）</view>
          <nl-slider 
            min="5" 
            max="60" 
            step="5"
            value="{{dailyGoalMinutes}}" 
            bindchange="handleMinutesChange"
            showValue
          />
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-label">计划可见性</view>
        <view class="visibility-options">
          <nl-radio 
            options="{{visibilityOptions}}" 
            value="{{isPublic ? 'public' : 'private'}}"
            bindchange="handleVisibilityChange"
          />
        </view>
      </view>
    </view>
    
    <!-- 预览确认步骤 -->
    <view class="step-content" wx:if="{{currentStep === 3}}">
      <view class="step-title">预览确认</view>
      <view class="preview-card">
        <view class="preview-header">
          <view class="preview-theme">
            <view class="theme-icon" style="background-color: {{selectedTheme.color}}">
              <nl-icon name="{{selectedTheme.icon}}" size="16" color="#FFFFFF" />
            </view>
            <view class="theme-name">{{selectedTheme.name}}</view>
          </view>
          <view class="preview-visibility">
            <nl-icon name="{{isPublic ? 'globe' : 'lock'}}" size="16" />
            <text>{{isPublic ? '公开' : '私密'}}</text>
          </view>
        </view>
        
        <view class="preview-title">{{planTitle}}</view>
        <view class="preview-description" wx:if="{{planDescription}}">{{planDescription}}</view>
        
        <view class="preview-tags" wx:if="{{selectedTagsCount > 0}}">
          <view 
            wx:for="{{selectedTagsList}}" 
            wx:key="id"
            class="preview-tag"
          >
            {{item.name}}
          </view>
        </view>
        
        <view class="preview-details">
          <view class="detail-item">
            <view class="detail-label">目标天数</view>
            <view class="detail-value">{{targetDays}}天</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">每日练习</view>
            <view class="detail-value">{{dailyGoalExercises}}个</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">每日观点</view>
            <view class="detail-value">{{dailyGoalInsights}}个</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">每日时间</view>
            <view class="detail-value">{{dailyGoalMinutes}}分钟</view>
          </view>
        </view>
      </view>
      
      <view class="preview-actions">
        <nl-button 
          type="text" 
          icon="edit" 
          bindtap="handleEditPlan"
        >编辑计划</nl-button>
      </view>
    </view>
  </view>
  
  <!-- 底部操作区 -->
  <view class="creator-footer">
    <nl-button 
      type="default" 
      bindtap="handlePrevStep"
      disabled="{{currentStep === 0}}"
    >上一步</nl-button>
    
    <view class="footer-right">
      <nl-button 
        type="text" 
        bindtap="handleSaveDraft"
        loading="{{savingDraft}}"
        wx:if="{{currentStep === 2 || currentStep === 3}}"
      >保存草稿</nl-button>
      
      <nl-button 
        type="primary" 
        bindtap="{{currentStep === 3 ? 'handleSubmit' : 'handleNextStep'}}"
        loading="{{submitting}}"
      >{{currentStep === 3 ? '创建计划' : '下一步'}}</nl-button>
    </view>
  </view>
</view>
