/* 计划创建流程组件样式 */

/* 主容器 */
.plan-creator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--nl-color-background);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.plan-creator.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 顶部导航 */
.creator-header {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid var(--nl-color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-nav {
  display: flex;
  align-items: center;
}

.step-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.step-item.active {
  opacity: 1;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: var(--nl-color-primary-light);
  color: var(--nl-color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  margin-right: 10rpx;
}

.step-item.active .step-number {
  background-color: var(--nl-color-primary);
  color: #FFFFFF;
}

.step-name {
  font-size: 28rpx;
  color: var(--nl-color-text);
}

.header-actions {
  display: flex;
  align-items: center;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: var(--nl-color-background-light);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: var(--nl-color-text-secondary);
}

/* 内容区域 */
.creator-content {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
}

.step-content {
  padding-bottom: 30rpx;
}

.step-title {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 30rpx;
}

/* 主题选择样式 */
.theme-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.theme-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: var(--nl-color-background-light);
  transition: all 0.3s ease;
}

.theme-item.selected {
  background-color: var(--nl-color-primary-light);
}

.theme-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 6rpx;
}

.theme-desc {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
}

.theme-check {
  margin-left: 20rpx;
}

/* 标签选择样式 */
.tag-search {
  margin-bottom: 20rpx;
}

.tag-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.category-item {
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: var(--nl-color-background-light);
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
  transition: all 0.3s ease;
}

.category-item.selected {
  background-color: var(--nl-color-primary);
  color: #FFFFFF;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.tag-item {
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: var(--nl-color-background-light);
  font-size: 28rpx;
  color: var(--nl-color-text);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.tag-item.selected {
  background-color: var(--nl-color-primary);
  color: #FFFFFF;
}

.tag-check {
  margin-left: 10rpx;
}

.selected-tags-preview {
  background-color: var(--nl-color-background-light);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 28rpx;
  color: var(--nl-color-text-secondary);
  margin-bottom: 20rpx;
}

.selected-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.selected-tag-item {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  background-color: var(--nl-color-primary-light);
  font-size: 24rpx;
  color: var(--nl-color-primary);
}

.tag-remove {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 计划设置样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--nl-color-text);
  margin-bottom: 16rpx;
}

.input-counter {
  text-align: right;
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
  margin-top: 8rpx;
}

.duration-options {
  display: flex;
  gap: 20rpx;
}

.duration-option {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: var(--nl-color-background-light);
  text-align: center;
  transition: all 0.3s ease;
}

.duration-option.selected {
  background-color: var(--nl-color-primary);
  color: #FFFFFF;
}

.daily-goal-item {
  margin-bottom: 20rpx;
}

.goal-label {
  font-size: 26rpx;
  color: var(--nl-color-text-secondary);
  margin-bottom: 10rpx;
}

.visibility-options {
  margin-top: 10rpx;
}

/* 预览确认样式 */
.preview-card {
  background-color: var(--nl-color-background-light);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.preview-theme {
  display: flex;
  align-items: center;
}

.preview-theme .theme-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.preview-theme .theme-name {
  font-size: 26rpx;
  margin-bottom: 0;
}

.preview-visibility {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
}

.preview-visibility text {
  margin-left: 6rpx;
}

.preview-title {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 16rpx;
}

.preview-description {
  font-size: 28rpx;
  color: var(--nl-color-text-secondary);
  margin-bottom: 20rpx;
}

.preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.preview-tag {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  background-color: var(--nl-color-primary-light);
  font-size: 24rpx;
  color: var(--nl-color-primary);
}

.preview-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.detail-item {
  background-color: var(--nl-color-background);
  border-radius: 8rpx;
  padding: 16rpx;
}

.detail-label {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--nl-color-text);
}

.preview-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

/* 底部操作区 */
.creator-footer {
  padding: 20rpx 30rpx;
  border-top: 1px solid var(--nl-color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

/* 错误信息 */
.error-message {
  color: var(--nl-color-error);
  font-size: 24rpx;
  margin-top: 8rpx;
}
