/**
 * 用户信息展示组件
 * 用于展示用户基本信息、等级、经验值等
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 用户数据
    userData: {
      type: Object,
      value: null,
      observer: function (newVal) {
        if (newVal) {
          this.setData({
            user: newVal
          });
          this.calculateExpProgress();
        }
      }
    },
    // 显示类型
    type: {
      type: String,
      value: 'full' // full, simple, mini
    },
    // 是否显示等级信息
    showLevel: {
      type: Boolean,
      value: true
    },
    // 是否显示统计信息
    showStats: {
      type: Boolean,
      value: true
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    user: null,
    expProgress: 0,
    currentExp: 0,
    nextLevelExp: 0,
    expToNextLevel: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算经验值进度
     */
    calculateExpProgress() {
      const { user } = this.data;
      if (!user) return;

      const currentExp = user.expPoints || 0;
      const currentLevel = user.level || 1;

      // 计算下一级所需经验值（示例算法，可根据实际需求调整）
      const baseExp = 100;
      const expGrowthFactor = 1.5;
      const nextLevelExp = Math.floor(baseExp * Math.pow(expGrowthFactor, currentLevel - 1));

      // 计算当前等级已获得经验值
      const prevLevelExp = currentLevel > 1 ? Math.floor(baseExp * Math.pow(expGrowthFactor, currentLevel - 2)) : 0;
      const currentLevelExp = currentExp - prevLevelExp;

      // 计算进度百分比
      const expProgress = Math.min(Math.round((currentLevelExp / (nextLevelExp - prevLevelExp)) * 100), 100);

      // 计算距离下一级所需经验值
      const expToNextLevel = nextLevelExp - currentLevelExp;

      this.setData({
        expProgress,
        currentExp: currentLevelExp,
        nextLevelExp: nextLevelExp - prevLevelExp,
        expToNextLevel
      });
    },

    /**
     * 点击头像事件
     */
    handleAvatarClick() {
      this.triggerEvent('avatarclick', { user: this.data.user });
    },

    /**
     * 点击用户名事件
     */
    handleNameClick() {
      this.triggerEvent('nameclick', { user: this.data.user });
    },

    /**
     * 点击等级事件
     */
    handleLevelClick() {
      this.triggerEvent('levelclick', {
        user: this.data.user,
        level: this.data.user.level,
        expPoints: this.data.user.expPoints
      });
    },

    /**
     * 获取组件样式类
     */
    getProfileClass() {
      const { type } = this.data;
      return `nl-user-profile nl-user-profile-${type}`;
    },

    /**
     * 获取组件样式
     */
    getProfileStyle() {
      return this.data.customStyle;
    },

    /**
     * 获取等级名称
     */
    getLevelName(level) {
      const levelNames = [
        '初学者',
        '学习者',
        '探索者',
        '实践者',
        '思考者',
        '创新者',
        '专家',
        '大师',
        '导师',
        '智者'
      ];

      if (!level) return levelNames[0];

      const index = Math.min(Math.max(level - 1, 0), levelNames.length - 1);
      return levelNames[index];
    }
  }
});
