<view class="{{getProfileClass()}}" style="{{getProfileStyle()}}">
  <!-- 完整用户信息视图 -->
  <block wx:if="{{type === 'full' && user}}">
    <view class="user-header">
      <!-- 头像 -->
      <view class="avatar-container" bindtap="handleAvatarClick">
        <image class="avatar" src="{{user.avatarUrl}}" wx:if="{{user.avatarUrl}}"></image>
        <view class="default-avatar" wx:else>
          <text class="default-avatar-text">{{user.nickName ? user.nickName.substring(0, 1).toUpperCase() : 'U'}}</text>
        </view>
      </view>
      
      <!-- 用户信息 -->
      <view class="user-info">
        <view class="nickname" bindtap="handleNameClick">{{user.nickName || '用户'}}</view>
        
        <!-- 等级信息 -->
        <view class="level-info" wx:if="{{showLevel}}" bindtap="handleLevelClick">
          <view class="level-badge">Lv.{{user.level || 1}}</view>
          <view class="level-name">{{getLevelName(user.level)}}</view>
        </view>
        
        <!-- 经验值进度条 -->
        <view class="exp-progress" wx:if="{{showLevel}}">
          <view class="exp-bar">
            <view class="exp-fill" style="width: {{expProgress}}%"></view>
          </view>
          <view class="exp-text">
            <text>{{currentExp}}/{{nextLevelExp}}</text>
            <text>还需{{expToNextLevel}}经验升级</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 用户统计信息 -->
    <view class="user-stats" wx:if="{{showStats}}">
      <view class="stat-item">
        <text class="stat-value">{{user.studyDays || 0}}</text>
        <text class="stat-label">学习天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{user.completedPlans || 0}}</text>
        <text class="stat-label">完成计划</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{user.badges || 0}}</text>
        <text class="stat-label">获得徽章</text>
      </view>
    </view>
  </block>
  
  <!-- 简化用户信息视图 -->
  <block wx:elif="{{type === 'simple' && user}}">
    <view class="simple-profile">
      <!-- 头像和用户名 -->
      <view class="simple-header">
        <image class="simple-avatar" src="{{user.avatarUrl}}" wx:if="{{user.avatarUrl}}"></image>
        <view class="simple-default-avatar" wx:else>
          <text class="default-avatar-text">{{user.nickName ? user.nickName.substring(0, 1).toUpperCase() : 'U'}}</text>
        </view>
        <view class="simple-info">
          <view class="simple-nickname">{{user.nickName || '用户'}}</view>
          <view class="simple-level" wx:if="{{showLevel}}">
            <text class="level-badge">Lv.{{user.level || 1}}</text>
            <text class="level-name">{{getLevelName(user.level)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 经验值进度条 -->
      <view class="simple-exp-progress" wx:if="{{showLevel}}">
        <view class="exp-bar">
          <view class="exp-fill" style="width: {{expProgress}}%"></view>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 迷你用户信息视图 -->
  <block wx:elif="{{type === 'mini' && user}}">
    <view class="mini-profile">
      <image class="mini-avatar" src="{{user.avatarUrl}}" wx:if="{{user.avatarUrl}}"></image>
      <view class="mini-default-avatar" wx:else>
        <text class="default-avatar-text">{{user.nickName ? user.nickName.substring(0, 1).toUpperCase() : 'U'}}</text>
      </view>
      <view class="mini-nickname">{{user.nickName || '用户'}}</view>
      <view class="mini-level" wx:if="{{showLevel}}">Lv.{{user.level || 1}}</view>
    </view>
  </block>
</view>
