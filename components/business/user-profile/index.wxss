/* 用户信息展示组件样式 */
.nl-user-profile {
  width: 100%;
}

/* 完整用户信息视图 */
.nl-user-profile-full {
  padding: var(--space-md);
  background-color: var(--bg-color-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.user-header {
  display: flex;
  margin-bottom: var(--space-md);
}

.avatar-container {
  margin-right: var(--space-md);
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
}

.default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
}

.default-avatar-text {
  font-size: var(--font-size-xl);
  color: var(--white);
  font-weight: var(--font-weight-bold);
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.nickname {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--space-xs);
}

.level-info {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.level-badge {
  font-size: var(--font-size-xs);
  color: var(--white);
  background-color: var(--primary-color);
  padding: 2rpx 10rpx;
  border-radius: var(--radius-sm);
  margin-right: var(--space-xs);
}

.level-name {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}

.exp-progress {
  margin-top: var(--space-xs);
}

.exp-bar {
  height: 10rpx;
  background-color: var(--grey-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: 4rpx;
}

.exp-fill {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

.exp-text {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: var(--space-md);
  border-top: 1px solid var(--divider-color);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  margin-top: 4rpx;
}

/* 简化用户信息视图 */
.nl-user-profile-simple {
  padding: var(--space-sm);
  background-color: var(--bg-color-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-xs);
}

.simple-profile {
  width: 100%;
}

.simple-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.simple-avatar, .simple-default-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: var(--space-sm);
}

.simple-default-avatar {
  background-color: var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
}

.simple-info {
  flex: 1;
}

.simple-nickname {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.simple-level {
  display: flex;
  align-items: center;
  margin-top: 4rpx;
}

.simple-exp-progress {
  margin-top: var(--space-xs);
}

/* 迷你用户信息视图 */
.nl-user-profile-mini {
  display: flex;
  align-items: center;
  padding: var(--space-xs);
}

.mini-profile {
  display: flex;
  align-items: center;
}

.mini-avatar, .mini-default-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: var(--space-xs);
}

.mini-default-avatar {
  background-color: var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
}

.mini-nickname {
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  margin-right: var(--space-xs);
}

.mini-level {
  font-size: var(--font-size-xs);
  color: var(--white);
  background-color: var(--primary-color);
  padding: 2rpx 8rpx;
  border-radius: var(--radius-sm);
}
