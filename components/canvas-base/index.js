// components/canvas-base/index.js
// 基础画布组件

// 导入工具类
const performanceMonitor = require('../../utils/performance-monitor');
const debugTools = require('../../utils/debug-tools');

/**
 * 基础画布组件
 * 提供共享的画布初始化、动画循环、事件处理等基础功能
 */
class CanvasBase {
  /**
   * 构造函数
   */
  constructor() {
    // 基础属性
    this.canvas = null; // Canvas元素
    this.ctx = null; // 2D上下文
    this.canvasWidth = 0; // 画布宽度
    this.canvasHeight = 0; // 画布高度
    this.dpr = 1; // 设备像素比
    this.elements = []; // 元素数组

    // 离屏Canvas相关
    this.offscreenCanvas = null; // 离屏Canvas元素
    this.offscreenCtx = null; // 离屏Canvas上下文
    this.renderCache = new Map(); // 渲染缓存
    this.cacheEnabled = true; // 是否启用缓存

    // 对象池
    this.elementPool = []; // 元素对象池
    this.poolSize = 50; // 对象池大小

    // 性能监控
    this.fpsHistory = []; // FPS历史记录
    this.fpsHistorySize = 60; // FPS历史记录大小
    this.lastFpsUpdateTime = 0; // 上次FPS更新时间
    this.performanceMode = 'auto'; // 性能模式：'high', 'medium', 'low', 'auto'

    // 动画相关
    this.animationTimer = null; // 动画计时器
    this.isAnimating = false; // 是否正在动画
    this.lastTimestamp = 0; // 上一帧时间戳
    this.frameCount = 0; // 帧计数器
    this.targetFps = 60; // 目标FPS
    this.adaptiveFps = true; // 是否启用自适应FPS

    // 交互相关
    this.draggedElement = null; // 当前拖动的元素
    this.isDragging = false; // 是否正在拖动
    this.dragStartX = 0; // 拖动开始X坐标
    this.dragStartY = 0; // 拖动开始Y坐标
    this.dragStartTime = 0; // 拖动开始时间
    this.lastTouchTime = 0; // 上次触摸时间，用于防止重复触发

    // 配置相关
    this.config = { // 默认配置
      animationSpeedMultiplier: 0.064,
      baseSpeed: 0.64,
      shadowBlur: 4,
      maxDeltaTime: 50, // 最大时间增量(毫秒)
      dragThreshold: 10, // 拖动阈值(像素)
      clickThreshold: 300 // 点击阈值(毫秒)
    };

    // 回调和引用
    this.page = null; // 页面实例引用
    this.getThemes = null; // 获取主题数据的函数
  }

  /**
   * 初始化画布
   * @param {Object} options - 初始化选项
   * @param {Object} options.page - 页面实例
   * @param {string} options.canvasId - 画布元素ID
   * @param {Function} options.getThemes - 获取主题数据的函数
   * @param {Object} options.config - 配置参数
   * @returns {Promise<boolean>} 是否初始化成功
   */
  init(options) {
    return new Promise((resolve, reject) => {
      try {
        // 保存页面实例和回调函数
        this.page = options.page;
        this.getThemes = options.getThemes || (() => []);

        // 合并配置
        if (options.config) {
          this.config = { ...this.config, ...options.config };
        }

        // 启动性能监控
        const performanceMonitor = require('../../utils/performance-monitor');
        performanceMonitor.start();

        // 获取Canvas元素和上下文
        const query = wx.createSelectorQuery().in(this.page);
        query.select(`#${options.canvasId}`)
          .fields({ node: true, size: true })
          .exec(res => {
            if (!res || !res[0] || !res[0].node) {
              console.error('获取画布节点失败', res);
              resolve(false);
              return;
            }

            try {
              const canvas = res[0].node;
              const ctx = canvas.getContext('2d');

              if (!ctx) {
                console.error('获取2d上下文失败');
                resolve(false);
                return;
              }

              this.canvas = canvas;
              this.ctx = ctx;

              // 设置画布尺寸，适配不同设备像素比
              this.dpr = wx.getSystemInfoSync().pixelRatio;
              canvas.width = res[0].width * this.dpr;
              canvas.height = res[0].height * this.dpr;
              ctx.scale(this.dpr, this.dpr);

              this.canvasWidth = res[0].width;
              this.canvasHeight = res[0].height;

              console.log('画布初始化完成', {
                width: this.canvasWidth,
                height: this.canvasHeight,
                dpr: this.dpr
              });

              // 初始化离屏Canvas
              this._initOffscreenCanvas();

              // 初始化对象池
              this._initObjectPool();

              // 初始化元素
              this._initElements();

              // 预渲染常用元素
              this._preRenderElements();

              // 初始化完成后，设置页面状态
              if (this.page && this.page.setData) {
                this.page.setData({ initialized: true });
              }

              // 记录调试信息
              const debugTools = require('../../utils/debug-tools');
              if (debugTools.isDebugMode) {
                debugTools.log('info', '画布初始化完成', {
                  width: this.canvasWidth,
                  height: this.canvasHeight,
                  dpr: this.dpr,
                  offscreenEnabled: !!this.offscreenCanvas
                });
              }

              resolve(true);
            } catch (err) {
              console.error('初始化Canvas上下文时出错', err);
              resolve(false);
            }
          });
      } catch (err) {
        console.error('创建选择器时出错', err);
        resolve(false);
      }
    });
  }

  /**
   * 初始化离屏Canvas
   * @private
   */
  _initOffscreenCanvas() {
    try {
      if (!this.cacheEnabled) {
        console.log('离屏渲染已禁用');
        return;
      }

      // 创建离屏Canvas
      this.offscreenCanvas = wx.createOffscreenCanvas({
        type: '2d',
        width: this.canvasWidth * this.dpr,
        height: this.canvasHeight * this.dpr
      });

      if (!this.offscreenCanvas) {
        console.warn('创建离屏Canvas失败，将禁用缓存渲染');
        this.cacheEnabled = false;
        return;
      }

      // 获取离屏Canvas上下文
      this.offscreenCtx = this.offscreenCanvas.getContext('2d');

      if (!this.offscreenCtx) {
        console.warn('获取离屏Canvas上下文失败，将禁用缓存渲染');
        this.cacheEnabled = false;
        this.offscreenCanvas = null;
        return;
      }

      // 设置离屏Canvas尺寸和缩放
      this.offscreenCtx.scale(this.dpr, this.dpr);

      console.log('离屏Canvas初始化完成', {
        width: this.canvasWidth,
        height: this.canvasHeight,
        dpr: this.dpr
      });
    } catch (err) {
      console.error('初始化离屏Canvas时出错', err);
      this.cacheEnabled = false;
      this.offscreenCanvas = null;
      this.offscreenCtx = null;
    }
  }

  /**
   * 初始化对象池
   * @private
   */
  _initObjectPool() {
    try {
      // 创建元素对象池
      this.elementPool = [];

      // 预先创建一些元素对象
      for (let i = 0; i < this.poolSize; i++) {
        this.elementPool.push(this._createEmptyElement());
      }

      console.log(`对象池初始化完成，大小: ${this.elementPool.length}`);
    } catch (err) {
      console.error('初始化对象池时出错', err);
      this.elementPool = [];
    }
  }

  /**
   * 创建空元素
   * @returns {Object} 空元素
   * @private
   */
  _createEmptyElement() {
    return {
      id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      x: 0,
      y: 0,
      radius: 0,
      color: '#000000',
      velocityX: 0,
      velocityY: 0,
      isActive: false
    };
  }

  /**
   * 从对象池获取元素
   * @returns {Object} 元素
   * @private
   */
  _getElementFromPool() {
    // 如果池中有可用元素，则返回
    if (this.elementPool.length > 0) {
      return this.elementPool.pop();
    }

    // 否则创建新元素
    return this._createEmptyElement();
  }

  /**
   * 将元素返回对象池
   * @param {Object} element - 元素
   * @private
   */
  _returnElementToPool(element) {
    // 重置元素状态
    element.isActive = false;

    // 返回对象池
    this.elementPool.push(element);
  }

  /**
   * 预渲染常用元素
   * @private
   */
  _preRenderElements() {
    if (!this.cacheEnabled || !this.offscreenCanvas || !this.offscreenCtx) {
      console.log('缓存渲染已禁用，跳过预渲染');
      return;
    }

    try {
      console.log('开始预渲染常用元素');

      // 预渲染不同大小和颜色的圆形
      const sizes = [20, 30, 40, 50, 60];
      const colors = ['#3B82F6', '#60A5FA', '#93C5FD', '#BFDBFE', '#DBEAFE'];

      sizes.forEach(size => {
        colors.forEach(color => {
          // 创建缓存键
          const cacheKey = `circle-${size}-${color}`;

          // 创建离屏Canvas
          const offscreen = wx.createOffscreenCanvas({
            type: '2d',
            width: size * 2 * this.dpr,
            height: size * 2 * this.dpr
          });

          const offCtx = offscreen.getContext('2d');
          offCtx.scale(this.dpr, this.dpr);

          // 绘制圆形
          offCtx.beginPath();
          offCtx.arc(size, size, size, 0, Math.PI * 2);
          offCtx.fillStyle = color;
          offCtx.fill();

          // 存储到缓存
          this.renderCache.set(cacheKey, offscreen);
        });
      });

      console.log(`预渲染完成，缓存大小: ${this.renderCache.size}`);
    } catch (err) {
      console.error('预渲染元素时出错', err);
    }
  }

  /**
   * 初始化元素
   * @private
   */
  _initElements() {
    // 获取主题数据
    const themes = this.getThemes ? this.getThemes() : [];

    if (!themes || themes.length === 0) {
      console.warn('主题数据为空，无法初始化元素');
      return;
    }

    // 创建元素
    this.elements = this._createElements(themes);

    // 启动动画循环
    this.startAnimation();
  }

  /**
   * 创建元素
   * @param {Array} themes - 主题数据
   * @returns {Array} 元素数组
   * @protected
   */
  _createElements(themes) {
    // 基类中提供默认实现，子类应该重写此方法
    return [];
  }

  /**
   * 开始动画循环
   * @param {boolean} enablePerformanceMonitoring - 是否启用性能监控
   * @returns {boolean} 是否成功启动
   */
  startAnimation(enablePerformanceMonitoring = false) {
    if (this.isAnimating) {
      return true; // 已经在运行
    }

    if (!this.canvas || !this.ctx) {
      console.error('Canvas或Context不可用，无法启动动画');
      return false;
    }

    // 初始化时间戳
    this.lastTimestamp = Date.now();
    this.isAnimating = true;
    this.animationTimer = null; // 清除旧的 timer id
    this.frameDropCount = 0; // 跟踪丢帧数量
    this.consecutiveErrorCount = 0; // 连续错误计数

    // 如果启用性能监控，启动性能监控器
    if (enablePerformanceMonitoring && performanceMonitor) {
      performanceMonitor.start();
      this.performanceMonitoringEnabled = true;
    }

    // 定义动画循环函数
    const animate = () => {
      // 在循环开始时再次检查状态，以防在请求动画帧后状态改变
      if (!this.isAnimating || !this.canvas || !this.ctx) {
        console.log('动画已停止或画布已失效，终止循环');
        if (this.animationTimer) { // 确保清除 animation frame
          this.canvas.cancelAnimationFrame(this.animationTimer);
          this.animationTimer = null;
        }
        return;
      }

      try {
        // 如果启用性能监控，记录帧
        if (this.performanceMonitoringEnabled && performanceMonitor) {
          performanceMonitor.recordFrame();
        }

        // 获取当前时间戳并计算时间增量
        const now = Date.now();
        let deltaTime = now - this.lastTimestamp;

        // 检测异常大的时间增量（可能是页面切换或设备休眠）
        if (deltaTime > 1000) {
          console.log(`检测到大的时间跳跃: ${deltaTime}ms，可能是页面切换或设备休眠`);
          deltaTime = 16; // 使用默认帧时间
          this.frameDropCount++;

          // 如果连续出现多次大的时间跳跃，重置动画状态
          if (this.frameDropCount > 5) {
            console.log('多次检测到大的时间跳跃，重置动画状态');
            this._resetAnimationState();
            this.frameDropCount = 0;
          }
        } else {
          this.frameDropCount = 0; // 重置跟踪计数
        }

        // 限制最大 deltaTime
        deltaTime = Math.min(deltaTime, this.config.maxDeltaTime);

        this.lastTimestamp = now;

        // 更新和绘制元素
        this._updateElements(deltaTime);
        this._drawElements();

        // 增加帧计数
        this.frameCount++;

        // 重置连续错误计数
        this.consecutiveErrorCount = 0;

        // 每300帧输出一次性能指标（如果启用性能监控）
        if (this.performanceMonitoringEnabled && performanceMonitor && this.frameCount % 300 === 0) {
          const metrics = performanceMonitor.getMetrics();
          console.log('性能指标:', {
            fps: `${metrics.fps.current} (平均: ${metrics.fps.avg.toFixed(1)})`,
            frameTime: `${metrics.frameTime.current.toFixed(2)}ms (平均: ${metrics.frameTime.avg.toFixed(2)}ms)`,
            memory: metrics.memory.current > 0 ? `${metrics.memory.current.toFixed(1)}%` : 'N/A'
          });
        }

        // 只有在 isAnimating 为 true 时才请求下一帧
        if (this.isAnimating) {
          this.animationTimer = this.canvas.requestAnimationFrame(animate);
        }

      } catch (error) {
        this.consecutiveErrorCount++;
        console.error(`动画循环错误 (连续第 ${this.consecutiveErrorCount} 次):`, error);

        // 如果连续错误次数过多，停止动画防止崩溃
        if (this.consecutiveErrorCount > 5) {
          console.error('动画循环连续错误过多，强制停止动画！');
          this.stopAnimation();
        } else {
          // 否则，继续尝试下一帧
          if (this.isAnimating) {
            this.animationTimer = this.canvas.requestAnimationFrame(animate);
          }
        }
      }
    };

    // 启动第一次动画帧请求
    this.animationTimer = this.canvas.requestAnimationFrame(animate);
    console.log('动画循环已启动');
    return true;
  }

  /**
   * 重置动画状态
   * @private
   */
  _resetAnimationState() {
    // 重置时间戳
    this.lastTimestamp = Date.now();

    // 重置元素速度（避免累积过大的速度）
    for (let i = 0; i < this.elements.length; i++) {
      try {
        const element = this.elements[i];
        if (!element) continue;

        // 保持方向，但重置速度大小
        const speed = Math.sqrt(element.velocityX * element.velocityX + element.velocityY * element.velocityY);
        const angle = Math.atan2(element.velocityY, element.velocityX);

        // 限制速度在合理范围内
        const newSpeed = Math.min(speed, this.config.baseSpeed * 2);

        element.velocityX = Math.cos(angle) * newSpeed;
        element.velocityY = Math.sin(angle) * newSpeed;
      } catch (err) {
        console.error('重置元素速度时出错', err);
        // 单个元素重置错误不影响其他元素
        continue;
      }
    }
  }

  /**
   * 暂停动画循环
   */
  pauseAnimation() {
    console.log('CanvasBase: pauseAnimation 调用，实际执行 stopAnimation');
    this.stopAnimation();
  }

  /**
   * 恢复动画循环
   */
  resumeAnimation() {
    console.log('CanvasBase: resumeAnimation 调用，实际执行 startAnimation');
    // 检查画布和上下文是否仍然有效
    if (!this.canvas || !this.ctx) {
      console.warn('尝试恢复动画，但画布或上下文已失效');
      return;
    }
    if (!this.isAnimating) { // 只有在停止状态才启动
      this.startAnimation();
    } else {
      console.log('动画已经在运行中，无需恢复');
    }
  }

  /**
   * 停止动画循环
   */
  stopAnimation() {
    if (!this.isAnimating) {
      return;
    }
    this.isAnimating = false;
    if (this.animationTimer && this.canvas && typeof this.canvas.cancelAnimationFrame === 'function') {
      this.canvas.cancelAnimationFrame(this.animationTimer);
      this.animationTimer = null;
      console.log('动画循环已停止');
    } else {
      console.log('尝试停止动画，但 timer 或 canvas 不可用');
    }

    // 如果启用了性能监控，停止它
    if (this.performanceMonitoringEnabled && performanceMonitor) {
      performanceMonitor.stop();
      this.performanceMonitoringEnabled = false;
    }
  }

  /**
   * 更新元素位置和状态
   * @param {number} deltaTime - 时间增量(毫秒)
   * @private
   */
  _updateElements(deltaTime) {
    try {
      // 限制最大时间增量，避免大的时间跳跃
      const clampedDeltaTime = Math.min(deltaTime, this.config.maxDeltaTime);

      // 使用计数器替代每帧随机计算
      this._frameCounter = (this._frameCounter || 0) + 1;
      const shouldAddRandomness = this._frameCounter % 50 === 0; // 每50帧而不是随机

      // 更新每个元素
      for (let i = 0; i < this.elements.length; i++) {
        try {
          const element = this.elements[i];

          if (!element) continue; // 跳过无效元素

          // 如果元素被点击或正在被拖动，则不更新位置
          if (element.isClicked || element.isDragged) continue;

          // 保存上一帧的位置，用于检测卡住
          if (typeof element.lastX === 'undefined') {
            element.lastX = element.x;
            element.lastY = element.y;
          } else {
            element.lastX = element.x;
            element.lastY = element.y;
          }

          // 更新元素
          this._updateElement(element, clampedDeltaTime);

          // 处理碰撞
          this._handleElementCollision(element);

          // 减少随机性计算，使用计数器控制
          if (shouldAddRandomness && typeof this._getPrecomputedRandom === 'function') {
            // 每50帧才添加随机性，而不是每帧都有概率添加
            element.velocityX += (this._getPrecomputedRandom(i * 2) - 0.5) * 0.01;
            element.velocityY += (this._getPrecomputedRandom(i * 2 + 1) - 0.5) * 0.01;
          }
        } catch (elemErr) {
          console.error('更新元素时出错', elemErr);
          // 单个元素更新错误不影响其他元素
          continue;
        }
      }
    } catch (err) {
      console.error('更新元素位置和状态时出错', err);
      // 出错时不中断整个动画循环
    }
  }

  /**
   * 更新单个元素
   * @param {Object} element - 元素对象
   * @param {number} deltaTime - 时间增量
   * @protected
   */
  _updateElement(element, deltaTime) {
    // 基类中提供默认实现，子类应该重写此方法
    // 默认实现：简单的位置更新
    element.x += element.velocityX * deltaTime * this.config.animationSpeedMultiplier;
    element.y += element.velocityY * deltaTime * this.config.animationSpeedMultiplier;
  }

  /**
   * 处理元素碰撞
   * @param {Object} element - 元素对象
   * @protected
   */
  _handleElementCollision(element) {
    // 基类中提供默认实现，子类应该重写此方法
    // 默认实现：简单的边界碰撞检测

    // 碰撞检测 - 左右边界
    if (element.x - element.radius < 0) {
      element.x = element.radius;
      element.velocityX = Math.abs(element.velocityX) * 0.8; // 减少反弹速度
    } else if (element.x + element.radius > this.canvasWidth) {
      element.x = this.canvasWidth - element.radius;
      element.velocityX = -Math.abs(element.velocityX) * 0.8; // 减少反弹速度
    }

    // 碰撞检测 - 上下边界
    if (element.y - element.radius < 0) {
      element.y = element.radius;
      element.velocityY = Math.abs(element.velocityY) * 0.8; // 减少反弹速度
    } else if (element.y + element.radius > this.canvasHeight) {
      element.y = this.canvasHeight - element.radius;
      element.velocityY = -Math.abs(element.velocityY) * 0.8; // 减少反弹速度
    }
  }

  /**
   * 绘制元素
   * @private
   */
  _drawElements() {
    // 清空画布
    this._clearCanvas();

    // 绘制每个元素
    for (let i = 0; i < this.elements.length; i++) {
      this._drawElement(this.elements[i]);
    }

    // 如果在调试模式下，绘制调试信息
    if (debugTools && debugTools.isDebugMode) {
      let metrics = null;

      // 如果启用了性能监控，获取性能指标
      if (this.performanceMonitoringEnabled && performanceMonitor) {
        metrics = performanceMonitor.getMetrics();
      }

      // 绘制调试信息
      debugTools.drawDebugInfo(this.ctx, this.canvas, this.elements, metrics);
    }
  }

  /**
   * 绘制单个元素
   * @param {Object} element - 元素对象
   * @protected
   */
  _drawElement(element) {
    // 如果元素无效，直接返回
    if (!element || !this.ctx) return;

    // 如果启用了缓存渲染，尝试使用缓存
    if (this.cacheEnabled && this.renderCache) {
      const cacheKey = `circle-${Math.round(element.radius)}-${element.color}`;

      // 检查缓存中是否有该元素
      if (this.renderCache.has(cacheKey)) {
        // 使用缓存的预渲染图像
        const cachedCanvas = this.renderCache.get(cacheKey);

        // 计算绘制位置（考虑元素半径）
        const drawX = element.x - element.radius;
        const drawY = element.y - element.radius;

        // 绘制缓存的图像
        this.ctx.drawImage(
          cachedCanvas,
          0, 0,
          element.radius * 2 * this.dpr, element.radius * 2 * this.dpr,
          drawX, drawY,
          element.radius * 2, element.radius * 2
        );

        // 如果元素处于悬停或拖动状态，添加额外效果
        if (element.isHovered || element.isDragged) {
          this._drawElementEffects(element);
        }

        return;
      }
    }

    // 如果没有缓存或缓存中没有该元素，直接绘制
    this._drawElementDirect(element);
  }

  /**
   * 直接绘制元素（不使用缓存）
   * @param {Object} element - 元素对象
   * @private
   */
  _drawElementDirect(element) {
    const ctx = this.ctx;

    ctx.save();

    // 绘制基本圆形
    ctx.beginPath();
    ctx.arc(element.x, element.y, element.radius, 0, Math.PI * 2);
    ctx.fillStyle = element.color || '#3B82F6';
    ctx.fill();

    // 如果元素处于悬停或拖动状态，添加额外效果
    if (element.isHovered || element.isDragged) {
      this._drawElementEffects(element);
    }

    ctx.restore();
  }

  /**
   * 绘制元素的额外效果
   * @param {Object} element - 元素对象
   * @private
   */
  _drawElementEffects(element) {
    const ctx = this.ctx;

    // 保存上下文状态
    ctx.save();

    // 绘制悬停效果
    if (element.isHovered) {
      // 绘制光晕
      const gradient = ctx.createRadialGradient(
        element.x, element.y, element.radius * 0.8,
        element.x, element.y, element.radius * 1.2
      );

      gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0.3)');

      ctx.beginPath();
      ctx.arc(element.x, element.y, element.radius * 1.2, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();
    }

    // 绘制拖动效果
    if (element.isDragged) {
      // 绘制轨迹
      ctx.beginPath();
      ctx.arc(element.x, element.y, element.radius * 1.3, 0, Math.PI * 2);
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
      ctx.lineWidth = 2;
      ctx.stroke();

      // 绘制内部光晕
      const gradient = ctx.createRadialGradient(
        element.x, element.y, 0,
        element.x, element.y, element.radius
      );

      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

      ctx.beginPath();
      ctx.arc(element.x, element.y, element.radius, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();
    }

    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 清空画布
   * @private
   */
  _clearCanvas() {
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
    }
  }

  /**
   * 处理触摸开始事件
   * @param {Object} e - 触摸事件对象
   * @returns {Object|null} 交互结果
   */
  handleTouchStart(e) {
    if (!e.touches || e.touches.length === 0) return null;

    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 记录拖动开始位置和时间
    this.dragStartX = x;
    this.dragStartY = y;
    this.dragStartTime = Date.now();
    this.isDragging = false;

    // 检测点击的是哪个元素
    for (let i = 0; i < this.elements.length; i++) {
      const element = this.elements[i];

      if (this._isPointInElement(x, y, element)) {
        // 记录拖动开始的元素和位置
        this.draggedElement = element;
        this.elementStartX = element.x;
        this.elementStartY = element.y;

        // 标记元素为拖动状态
        element.isDragged = true;

        // 记录原始速度
        element.originalVelocityX = element.velocityX;
        element.originalVelocityY = element.velocityY;

        // 暂停元素动画
        element.velocityX = 0;
        element.velocityY = 0;

        // 不立即返回主题，等待触摸结束时判断是点击还是拖动
        return null;
      }
    }

    return null;
  }

  /**
   * 处理触摸移动事件
   * @param {Object} e - 触摸事件对象
   * @returns {string} 鼠标样式
   */
  handleTouchMove(e) {
    if (!e.touches || e.touches.length === 0) return 'default';

    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 计算拖动距离
    const dragDistance = Math.sqrt(
      Math.pow(x - this.dragStartX, 2) +
      Math.pow(y - this.dragStartY, 2)
    );

    // 如果拖动距离超过阈值，标记为拖动状态
    if (dragDistance > this.config.dragThreshold) {
      this.isDragging = true;
    }

    // 如果有元素被拖动，更新其位置
    if (this.draggedElement && this.isDragging) {
      this.draggedElement.x = x;
      this.draggedElement.y = y;
      return 'grabbing';
    }

    // 检测鼠标是否悬停在元素上
    let isHovering = false;
    for (let i = 0; i < this.elements.length; i++) {
      const element = this.elements[i];

      // 重置所有元素的悬停状态
      element.isHovered = false;

      // 检查当前点是否在元素内
      if (this._isPointInElement(x, y, element)) {
        element.isHovered = true;
        isHovering = true;
      }
    }

    return isHovering ? 'pointer' : 'default';
  }

  /**
   * 处理触摸结束事件
   * @returns {Object|null} 交互结果
   */
  handleTouchEnd() {
    // 如果没有拖动元素，直接返回
    if (!this.draggedElement) return null;

    const element = this.draggedElement;
    const dragDuration = Date.now() - this.dragStartTime;

    // 如果是拖动操作（明显拖动或时间较长）
    if (this.isDragging || dragDuration > this.config.clickThreshold) {
      // 给元素一个随机速度，让它继续运动
      const angle = Math.random() * Math.PI * 2;
      element.velocityX = Math.cos(angle) * this.config.baseSpeed;
      element.velocityY = Math.sin(angle) * this.config.baseSpeed;

      // 重置拖动状态
      element.isDragged = false;
      this.draggedElement = null;
      this.isDragging = false;

      // 拖动操作不触发点击事件
      return null;
    } else {
      // 如果是点击操作（没有明显拖动）
      // 获取主题数据
      const themes = this.getThemes ? this.getThemes() : [];

      // 找到匹配的主题
      const theme = themes.find(t => t.name === element.text);
      if (theme) {
        // 标记元素为点击状态和已完成状态
        element.isClicked = true;
        element.isCompleted = true;

        // 更新页面浮动按钮状态
        if (this.page && typeof this.page.updateFloatingButtonState === 'function') {
          this.page.updateFloatingButtonState();
        }

        // 重置拖动状态
        element.isDragged = false;
        this.draggedElement = null;

        // 返回主题和交互信息
        return {
          theme: theme,
          tagId: theme.id,
          interactionType: 'click',
          duration: dragDuration,
          positionX: element.x,
          positionY: element.y
        };
      }
    }

    // 重置所有元素的悬停状态
    for (let i = 0; i < this.elements.length; i++) {
      this.elements[i].isHovered = false;
    }

    // 重置拖动状态
    this.draggedElement = null;
    this.isDragging = false;

    return null;
  }

  /**
   * 检查点是否在元素内
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {Object} element - 元素对象
   * @returns {boolean} 是否在元素内
   * @protected
   */
  _isPointInElement(x, y, element) {
    // 基类中提供默认实现，子类应该重写此方法
    // 默认实现：简单的圆形碰撞检测
    const distance = Math.sqrt(
      Math.pow(x - element.x, 2) +
      Math.pow(y - element.y, 2)
    );

    return distance <= element.radius;
  }

  /**
   * 更新主题数据
   * @param {Array} themes - 主题数据
   * @returns {boolean} 是否更新成功
   */
  updateThemes(themes) {
    if (!themes || themes.length === 0) {
      console.warn('尝试更新空的主题数据');
      return false;
    }

    console.log('更新主题数据', themes);

    // 保存当前元素的位置和速度信息
    const elementStates = {};
    this.elements.forEach(element => {
      const id = element.id;
      elementStates[id] = {
        x: element.x,
        y: element.y,
        velocityX: element.velocityX,
        velocityY: element.velocityY,
        radius: element.radius
      };
    });

    // 重新创建元素，但保持原有的位置和速度
    this.elements = this._createElements(themes);

    // 恢复元素的位置和速度
    this.elements.forEach((element, index) => {
      const oldId = `element-${index}`;
      if (elementStates[oldId]) {
        element.x = elementStates[oldId].x;
        element.y = elementStates[oldId].y;
        element.velocityX = elementStates[oldId].velocityX;
        element.velocityY = elementStates[oldId].velocityY;
        element.radius = elementStates[oldId].radius;
      }
    });

    return true;
  }

  /**
   * 重置交互状态
   */
  resetInteractionState() {
    // 重置所有元素的交互状态
    for (let i = 0; i < this.elements.length; i++) {
      const element = this.elements[i];

      if (element.isClicked) {
        // 为被点击的元素重新生成随机速度
        const angle = Math.random() * Math.PI * 2;
        element.velocityX = Math.cos(angle) * this.config.baseSpeed * 0.8;
        element.velocityY = Math.sin(angle) * this.config.baseSpeed * 0.8;
        element.isClicked = false;
      }

      element.isHovered = false;
      element.isDragged = false;
    }

    // 重置拖动状态
    this.draggedElement = null;
    this.isDragging = false;
  }

  /**
   * 检查所有元素是否已完成
   * @returns {boolean} 是否所有元素已完成
   */
  checkAllElementsCompleted() {
    // 如果没有元素，返回false
    if (!this.elements || this.elements.length === 0) {
      return false;
    }

    // 检查每个元素是否已完成
    for (let i = 0; i < this.elements.length; i++) {
      const element = this.elements[i];
      // 如果有任何元素未完成，返回false
      if (!element.isCompleted) {
        return false;
      }
    }

    // 所有元素均已完成
    return true;
  }

  /**
   * 显示清屏奖励动画
   * @returns {Promise<boolean>} 动画是否已完成
   */
  showClearScreenReward() {
    return new Promise((resolve) => {
      // 导入奖励动画模块
      const { createClearScreenReward } = require('../bubble-canvas/reward-animation');

      // 暂停当前动画
      this.pauseAnimation();

      // 创建奖励动画
      const rewardAnimation = createClearScreenReward(this.ctx, this.canvasWidth, this.canvasHeight);

      // 动画开始时间
      const startTime = Date.now();

      // 动画帧函数
      const animate = (timestamp) => {
        // 渲染奖励动画
        const isCompleted = rewardAnimation(timestamp);

        // 如果动画未完成，继续下一帧
        if (!isCompleted && Date.now() - startTime < 5000) { // 最多播放5秒
          this.animationTimer = this.canvas.requestAnimationFrame(animate);
        } else {
          // 动画完成或超时，恢复原有动画
          this.resumeAnimation();
          resolve(true);
        }
      };

      // 启动奖励动画
      this.animationTimer = this.canvas.requestAnimationFrame(animate);
    });
  }

  /**
   * 检查所有元素是否已完成
   * @returns {boolean} 是否所有元素都已完成
   */
  checkAllElementsCompleted() {
    // 检查是否有元素
    if (!this.elements || this.elements.length === 0) {
      return true; // 没有元素，视为全部完成
    }

    // 检查每个元素是否已完成
    for (let i = 0; i < this.elements.length; i++) {
      const element = this.elements[i];

      // 如果元素未完成（未被点击过），返回false
      if (!element.isCompleted && !element.isClicked) {
        return false;
      }
    }

    // 所有元素都已完成
    return true;
  }

  /**
   * 创建新的元素
   * @returns {boolean} 是否成功创建
   */
  createNewElements() {
    try {
      // 获取主题数据
      const themes = this.getThemes ? this.getThemes() : [];

      if (!themes || themes.length === 0) {
        console.warn('主题数据为空，无法创建新元素');
        return false;
      }

      // 清空现有元素
      this.elements = [];

      // 创建新元素
      this.elements = this._createElements(themes);

      console.log(`成功创建${this.elements.length}个新元素`);
      return true;
    } catch (err) {
      console.error('创建新元素失败', err);
      return false;
    }
  }

  /**
   * 销毁组件，释放所有资源
   */
  destroy() {
    console.log('CanvasBase: destroy 方法被调用');
    this.stopAnimation();
    this.elements = []; // 清空元素
    this.canvas = null;
    this.ctx = null;
    this.page = null;
    this.getThemes = null;
  }

  /**
   * 使颜色变亮
   * @param {string} color - 十六进制颜色
   * @param {number} percent - 变亮百分比
   * @returns {string} - 新颜色
   */
  lightenColor(color, percent) {
    try {
      if (!color || typeof color !== 'string') {
        return '#FFFFFF'; // 默认返回白色
      }

      // 如果颜色不是十六进制格式，尝试转换
      let hexColor = color;
      if (!hexColor.startsWith('#')) {
        hexColor = this._colorToHex(color);
      }

      // 解析颜色
      let r = parseInt(hexColor.substr(1, 2), 16);
      let g = parseInt(hexColor.substr(3, 2), 16);
      let b = parseInt(hexColor.substr(5, 2), 16);

      // 增加亮度
      r = Math.min(255, Math.floor(r * (100 + percent) / 100));
      g = Math.min(255, Math.floor(g * (100 + percent) / 100));
      b = Math.min(255, Math.floor(b * (100 + percent) / 100));

      // 转换回十六进制
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    } catch (err) {
      console.error('颜色转换失败', err);
      return color; // 出错时返回原始颜色
    }
  }

  /**
   * 使颜色变暗
   * @param {string} color - 十六进制颜色
   * @param {number} percent - 变暗百分比
   * @returns {string} - 新颜色
   */
  darkenColor(color, percent) {
    try {
      if (!color || typeof color !== 'string') {
        return '#000000'; // 默认返回黑色
      }

      // 如果颜色不是十六进制格式，尝试转换
      let hexColor = color;
      if (!hexColor.startsWith('#')) {
        hexColor = this._colorToHex(color);
      }

      // 解析颜色
      let r = parseInt(hexColor.substr(1, 2), 16);
      let g = parseInt(hexColor.substr(3, 2), 16);
      let b = parseInt(hexColor.substr(5, 2), 16);

      // 减少亮度
      r = Math.max(0, Math.floor(r * (100 - percent) / 100));
      g = Math.max(0, Math.floor(g * (100 - percent) / 100));
      b = Math.max(0, Math.floor(b * (100 - percent) / 100));

      // 转换回十六进制
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    } catch (err) {
      console.error('颜色转换失败', err);
      return color; // 出错时返回原始颜色
    }
  }

  /**
   * 获取预计算的随机数
   * @param {number} index - 索引
   * @returns {number} 0-1之间的随机数
   * @private
   */
  _getPrecomputedRandom(index) {
    // 懒初始化随机数数组
    if (!this._randomValues) {
      this._randomValues = new Array(200);
      for (let i = 0; i < 200; i++) {
        this._randomValues[i] = Math.random();
      }
    }

    // 循环使用预计算的随机数
    return this._randomValues[index % 200];
  }

  /**
   * 将颜色转换为十六进制格式
   * @param {string} color - 颜色字符串
   * @returns {string} - 十六进制颜色
   * @private
   */
  _colorToHex(color) {
    // 简单的颜色名称映射
    const colorMap = {
      'red': '#FF0000',
      'green': '#00FF00',
      'blue': '#0000FF',
      'yellow': '#FFFF00',
      'cyan': '#00FFFF',
      'magenta': '#FF00FF',
      'black': '#000000',
      'white': '#FFFFFF',
      'gray': '#808080'
    };

    // 检查是否是预定义颜色
    if (colorMap[color.toLowerCase()]) {
      return colorMap[color.toLowerCase()];
    }

    // 检查是否是rgb格式
    const rgbMatch = color.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    if (rgbMatch) {
      const r = parseInt(rgbMatch[1]);
      const g = parseInt(rgbMatch[2]);
      const b = parseInt(rgbMatch[3]);
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }

    // 如果无法识别，返回默认颜色
    return '#000000';
  }
}

module.exports = CanvasBase;
