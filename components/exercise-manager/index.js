/**
 * 练习管理组件
 * 用于管理用户的练习，支持软删除功能
 */
Component({
  properties: {
    userId: {
      type: String,
      value: ''
    },
    tagId: {
      type: String,
      value: ''
    },
    showRecycleBin: {
      type: Boolean,
      value: false
    }
  },

  data: {
    exercises: [],
    isLoading: false,
    isSelectMode: false,
    selectedExercises: [],
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    activeTab: 'all', // 'all', 'deleted'
    searchKeyword: ''
  },

  lifetimes: {
    attached() {
      // 加载练习列表
      this.loadExercises();
    }
  },

  methods: {
    // 加载练习列表
    loadExercises() {
      this.setData({ isLoading: true });

      const { page, pageSize } = this.data.pagination;
      const params = {
        page,
        pageSize,
        userId: this.properties.userId,
        tagId: this.properties.tagId,
        keyword: this.data.searchKeyword
      };

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[exercise-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '加载失败', icon: 'none' });
        return;
      }

      // 根据当前标签页加载不同数据
      const promise = this.data.activeTab === 'deleted'
        ? api.exercise.getDeletedExercises(params)
        : api.exercise.getExercises(params);

      promise.then(result => {
        this.setData({
          exercises: result.data || [],
          'pagination.total': result.total || 0,
          isLoading: false
        });
      }).catch(err => {
        console.error('加载练习失败:', err);
        wx.showToast({ title: '加载失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 切换标签页
    switchTab(e) {
      const tab = e.currentTarget.dataset.tab;
      if (tab === this.data.activeTab) return;

      this.setData({
        activeTab: tab,
        'pagination.page': 1,
        isSelectMode: false,
        selectedExercises: []
      }, () => {
        this.loadExercises();
      });
    },

    // 分页加载更多
    loadMore() {
      if (this.data.isLoading) return;

      const { page, pageSize, total } = this.data.pagination;
      if (page * pageSize >= total) return;

      this.setData({
        'pagination.page': page + 1
      }, () => {
        this.loadExercises();
      });
    },

    // 搜索练习
    handleSearch(e) {
      const keyword = e.detail.value;
      this.setData({
        searchKeyword: keyword,
        'pagination.page': 1
      }, () => {
        this.loadExercises();
      });
    },

    // 清除搜索
    clearSearch() {
      this.setData({
        searchKeyword: '',
        'pagination.page': 1
      }, () => {
        this.loadExercises();
      });
    },

    // 查看练习详情
    viewExerciseDetail(e) {
      const exerciseId = e.currentTarget.dataset.id;
      if (!exerciseId) return;

      // 触发查看详情事件
      this.triggerEvent('view', { id: exerciseId });
    },

    // 编辑练习
    editExercise(e) {
      const exerciseId = e.currentTarget.dataset.id;
      if (!exerciseId) return;

      // 触发编辑事件
      this.triggerEvent('edit', { id: exerciseId });
    },

    // 软删除练习
    softDeleteExercise(e) {
      const exerciseId = e.currentTarget.dataset.id;
      if (!exerciseId) return;

      wx.showModal({
        title: '删除确认',
        content: '确定要删除此练习吗？删除后可在回收站恢复。',
        success: res => {
          if (res.confirm) {
            this.performSoftDelete(exerciseId);
          }
        }
      });
    },

    // 执行软删除
    performSoftDelete(exerciseId) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[exercise-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '删除失败', icon: 'none' });
        return;
      }

      api.exercise.softDelete(exerciseId)
        .then(() => {
          wx.showToast({ title: '删除成功', icon: 'success' });
          // 重新加载列表
          this.loadExercises();
          // 触发删除事件
          this.triggerEvent('delete', { id: exerciseId });
        })
        .catch(err => {
          console.error('删除练习失败:', err);
          wx.showToast({ title: '删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    },

    // 创建新练习
    createExercise() {
      // 触发创建事件
      this.triggerEvent('create');
    },

    // 切换选择模式
    toggleSelectMode() {
      this.setData({
        isSelectMode: !this.data.isSelectMode,
        selectedExercises: []
      });
    },

    // 选择/取消选择练习
    toggleSelect(e) {
      if (!this.data.isSelectMode) return;

      const exerciseId = e.currentTarget.dataset.id;
      if (!exerciseId) return;

      const { selectedExercises } = this.data;
      const index = selectedExercises.indexOf(exerciseId);

      if (index === -1) {
        // 添加到选中列表
        selectedExercises.push(exerciseId);
      } else {
        // 从选中列表移除
        selectedExercises.splice(index, 1);
      }

      this.setData({ selectedExercises });
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (!this.data.isSelectMode) return;

      const { selectedExercises, exercises } = this.data;

      if (selectedExercises.length === exercises.length) {
        // 取消全选
        this.setData({ selectedExercises: [] });
      } else {
        // 全选
        this.setData({
          selectedExercises: exercises.map(exercise => exercise.id)
        });
      }
    },

    // 批量删除
    batchDelete() {
      const { selectedExercises } = this.data;
      if (selectedExercises.length === 0) {
        wx.showToast({ title: '请先选择练习', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '批量删除确认',
        content: `确定要删除选中的${selectedExercises.length}个练习吗？删除后可在回收站恢复。`,
        success: res => {
          if (res.confirm) {
            this.performBatchDelete(selectedExercises);
          }
        }
      });
    },

    // 执行批量删除
    performBatchDelete(exerciseIds) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[exercise-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '批量删除失败', icon: 'none' });
        return;
      }

      // 使用Promise.all执行批量删除
      Promise.all(exerciseIds.map(id => api.exercise.softDelete(id)))
        .then(() => {
          wx.showToast({ title: '批量删除成功', icon: 'success' });
          // 重新加载列表
          this.loadExercises();
          // 退出选择模式
          this.setData({
            isSelectMode: false,
            selectedExercises: []
          });
          // 触发批量删除事件
          this.triggerEvent('batchDelete', { ids: exerciseIds });
        })
        .catch(err => {
          console.error('批量删除练习失败:', err);
          wx.showToast({ title: '批量删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    }
  }
});
