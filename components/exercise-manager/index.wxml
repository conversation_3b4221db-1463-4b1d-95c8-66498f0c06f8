<view class="exercise-manager">
  <!-- 顶部操作栏 -->
  <view class="action-bar">
    <view class="left-actions">
      <view wx:if="{{isSelectMode}}" class="select-all" bindtap="toggleSelectAll">
        {{selectedExercises.length === exercises.length ? '取消全选' : '全选'}}
      </view>
      <view wx:else class="create-btn" bindtap="createExercise">
        <text class="icon">+</text> 新建练习
      </view>
    </view>
    
    <view class="right-actions">
      <view wx:if="{{isSelectMode}}" class="batch-actions">
        <view class="batch-delete" bindtap="batchDelete">删除</view>
        <view class="cancel-select" bindtap="toggleSelectMode">取消</view>
      </view>
      <view wx:else class="select-mode-btn" bindtap="toggleSelectMode">
        <text class="icon">☑</text> 选择
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索练习" 
        value="{{searchKeyword}}"
        confirm-type="search"
        bindconfirm="handleSearch"
      />
      <text wx:if="{{searchKeyword}}" class="clear-icon" bindtap="clearSearch">✕</text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === 'all' ? 'active' : ''}}" 
      data-tab="all" 
      bindtap="switchTab"
    >全部练习</view>
    <view 
      wx:if="{{showRecycleBin}}"
      class="tab {{activeTab === 'deleted' ? 'active' : ''}}" 
      data-tab="deleted" 
      bindtap="switchTab"
    >回收站</view>
  </view>

  <!-- 练习列表 -->
  <scroll-view 
    class="exercise-list-container"
    scroll-y="true"
    bindscrolltolower="loadMore"
    lower-threshold="50"
  >
    <view class="exercise-list">
      <view wx:if="{{isLoading && exercises.length === 0}}" class="loading-placeholder">
        <text>加载中...</text>
      </view>
      
      <view wx:elif="{{!isLoading && exercises.length === 0}}" class="empty-placeholder">
        <text>{{activeTab === 'deleted' ? '回收站为空' : '暂无练习'}}</text>
      </view>
      
      <block wx:else>
        <view 
          wx:for="{{exercises}}" 
          wx:key="id"
          class="exercise-item {{isSelectMode ? 'selectable' : ''}} {{selectedExercises.includes(item.id) ? 'selected' : ''}}"
          data-id="{{item.id}}"
          bindtap="{{isSelectMode ? 'toggleSelect' : 'viewExerciseDetail'}}"
        >
          <view class="exercise-content">
            <view class="exercise-title">{{item.title}}</view>
            <view class="exercise-description">{{item.description}}</view>
            <view class="exercise-meta">
              <text class="exercise-difficulty difficulty-{{item.difficulty}}">
                {{item.difficulty === 1 ? '简单' : (item.difficulty === 2 ? '中等' : '困难')}}
              </text>
              <text class="exercise-time" wx:if="{{item.timeEstimate}}">
                预计时间: {{item.timeEstimate}}分钟
              </text>
              <text class="exercise-date">{{activeTab === 'deleted' ? '删除于: ' : '创建于: '}}{{item.deleted_at || item.created_at}}</text>
            </view>
          </view>
          
          <view class="exercise-actions" wx:if="{{!isSelectMode && activeTab !== 'deleted'}}">
            <view class="action-btn edit-btn" catchtap="editExercise" data-id="{{item.id}}">
              <text class="icon">✎</text>
            </view>
            <view class="action-btn delete-btn" catchtap="softDeleteExercise" data-id="{{item.id}}">
              <text class="icon">🗑</text>
            </view>
          </view>
          
          <view class="select-indicator" wx:if="{{isSelectMode}}">
            <text class="icon">{{selectedExercises.includes(item.id) ? '☑' : '☐'}}</text>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 加载更多 -->
    <view wx:if="{{isLoading && exercises.length > 0}}" class="loading-more">
      <text>加载中...</text>
    </view>
  </scroll-view>
</view>
