/**
 * 复选框组件
 * 提供多选项选择功能
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 复选框值
    value: {
      type: Boolean,
      value: false
    },
    // 复选框标签
    label: {
      type: String,
      value: ''
    },
    // 复选框名称（用于表单提交）
    name: {
      type: String,
      value: ''
    },
    // 复选框形状
    shape: {
      type: String,
      value: 'square' // square, circle
    },
    // 复选框尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 复选框颜色类型
    type: {
      type: String,
      value: 'primary' // primary, success, warning, error, info
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      value: false
    },
    // 标签位置
    labelPosition: {
      type: String,
      value: 'right' // left, right
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 自定义图标
    icon: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-checkbox'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换复选框状态
     */
    handleToggle() {
      if (this.data.disabled || this.data.readonly) return;

      const newValue = !this.data.value;

      this.setData({
        value: newValue
      });

      this.triggerEvent('change', { value: newValue, name: this.data.name });
    },

    /**
     * 计算复选框样式类
     */
    getCheckboxClass() {
      const { baseClass, size, shape, type, disabled, readonly, value } = this.data;

      return `${baseClass} ${baseClass}--${size} ${baseClass}--${shape} ${baseClass}--${type} ${disabled ? baseClass + '--disabled' : ''} ${readonly ? baseClass + '--readonly' : ''} ${value ? baseClass + '--checked' : ''}`;
    }
  }
});
