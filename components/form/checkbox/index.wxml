<view class="nl-checkbox-container" bindtap="handleToggle" style="{{customStyle}}">
  <!-- 左侧标签 -->
  <view wx:if="{{label && labelPosition === 'left'}}" class="nl-checkbox-label">
    {{label}}
  </view>
  
  <!-- 复选框 -->
  <view class="{{getCheckboxClass()}}">
    <view class="nl-checkbox__inner">
      <view wx:if="{{value}}" class="nl-checkbox__icon">
        <view wx:if="{{!icon}}" class="nl-checkbox__check">✓</view>
        <view wx:else class="nl-icon nl-icon-{{icon}}"></view>
      </view>
    </view>
  </view>
  
  <!-- 右侧标签 -->
  <view wx:if="{{label && labelPosition === 'right'}}" class="nl-checkbox-label">
    {{label}}
  </view>
</view>
