/* 复选框容器 */
.nl-checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-sm);
}

/* 复选框标签 */
.nl-checkbox-label {
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  margin: 0 var(--space-sm);
  line-height: 1.5;
}

/* 复选框基础样式 */
.nl-checkbox {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  transition: all var(--transition-normal) var(--easing-standard);
}

/* 复选框内部容器 */
.nl-checkbox__inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1px solid var(--grey-400);
  background-color: var(--white);
  transition: all var(--transition-normal) var(--easing-standard);
}

/* 复选框尺寸 */
.nl-checkbox--small .nl-checkbox__inner {
  width: 14px;
  height: 14px;
}

.nl-checkbox--medium .nl-checkbox__inner {
  width: 18px;
  height: 18px;
}

.nl-checkbox--large .nl-checkbox__inner {
  width: 22px;
  height: 22px;
}

/* 复选框形状 */
.nl-checkbox--square .nl-checkbox__inner {
  border-radius: var(--radius-sm);
}

.nl-checkbox--circle .nl-checkbox__inner {
  border-radius: var(--radius-full);
}

/* 复选框类型 */
.nl-checkbox--primary.nl-checkbox--checked .nl-checkbox__inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.nl-checkbox--success.nl-checkbox--checked .nl-checkbox__inner {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.nl-checkbox--warning.nl-checkbox--checked .nl-checkbox__inner {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.nl-checkbox--error.nl-checkbox--checked .nl-checkbox__inner {
  background-color: var(--error-color);
  border-color: var(--error-color);
}

.nl-checkbox--info.nl-checkbox--checked .nl-checkbox__inner {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

/* 复选框状态 */
.nl-checkbox--disabled .nl-checkbox__inner {
  background-color: var(--grey-100);
  border-color: var(--grey-300);
  cursor: not-allowed;
}

.nl-checkbox--readonly .nl-checkbox__inner {
  cursor: default;
}

.nl-checkbox--checked .nl-checkbox__inner {
  border-color: transparent;
}

.nl-checkbox--disabled.nl-checkbox--checked .nl-checkbox__inner {
  background-color: var(--grey-300);
  border-color: var(--grey-300);
}

/* 复选框图标 */
.nl-checkbox__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-xs);
  line-height: 1;
}

.nl-checkbox--small .nl-checkbox__icon {
  font-size: 10px;
}

.nl-checkbox--medium .nl-checkbox__icon {
  font-size: 12px;
}

.nl-checkbox--large .nl-checkbox__icon {
  font-size: 14px;
}

/* 复选框选中标记 */
.nl-checkbox__check {
  display: flex;
  align-items: center;
  justify-content: center;
}
