/**
 * 输入框组件
 * 提供各种样式和功能的输入框
 */
import { classNames, styleObject, debounce } from '../../../utils/component-utils';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 输入框类型
    type: {
      type: String,
      value: 'text' // text, number, idcard, digit, password, nickname
    },
    // 输入框值
    value: {
      type: String,
      value: ''
    },
    // 输入框占位符
    placeholder: {
      type: String,
      value: '请输入'
    },
    // 占位符样式
    placeholderStyle: {
      type: String,
      value: ''
    },
    // 占位符类名
    placeholderClass: {
      type: String,
      value: 'input-placeholder'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      value: false
    },
    // 最大长度
    maxlength: {
      type: Number,
      value: 140
    },
    // 自动聚焦
    focus: {
      type: Boolean,
      value: false
    },
    // 确认类型
    confirmType: {
      type: String,
      value: 'done' // done, go, next, search, send
    },
    // 确认按钮文字颜色
    confirmHold: {
      type: Boolean,
      value: false
    },
    // 光标颜色
    cursorColor: {
      type: String,
      value: ''
    },
    // 光标位置
    cursor: {
      type: Number,
      value: -1
    },
    // 选择起始位置
    selectionStart: {
      type: Number,
      value: -1
    },
    // 选择结束位置
    selectionEnd: {
      type: Number,
      value: -1
    },
    // 是否调整键盘高度
    adjustPosition: {
      type: Boolean,
      value: true
    },
    // 是否显示清除按钮
    clearable: {
      type: Boolean,
      value: false
    },
    // 是否显示字数统计
    showCount: {
      type: Boolean,
      value: false
    },
    // 是否显示边框
    border: {
      type: Boolean,
      value: true
    },
    // 输入框尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 输入框形状
    shape: {
      type: String,
      value: 'square' // square, rounded, circle
    },
    // 输入框标签
    label: {
      type: String,
      value: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      value: false
    },
    // 输入框前缀图标
    prefixIcon: {
      type: String,
      value: ''
    },
    // 输入框后缀图标
    suffixIcon: {
      type: String,
      value: ''
    },
    // 是否显示错误状态
    error: {
      type: Boolean,
      value: false
    },
    // 错误提示文本
    errorMessage: {
      type: String,
      value: ''
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 是否使用防抖
    useDebounce: {
      type: Boolean,
      value: false
    },
    // 防抖延迟时间（毫秒）
    debounceTime: {
      type: Number,
      value: 300
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-input',
    focused: false,
    inputValue: '',
    showClear: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.setData({
        inputValue: this.data.value
      });

      // 创建防抖函数
      if (this.data.useDebounce) {
        this.debouncedInput = debounce(value => {
          this.triggerEvent('input', { value });
          this.triggerEvent('change', { value });
        }, this.data.debounceTime);
      }
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'value': function (value) {
      if (value !== this.data.inputValue) {
        this.setData({
          inputValue: value
        });
      }
    },
    'inputValue, clearable, focused': function (inputValue, clearable, focused) {
      this.setData({
        showClear: clearable && inputValue && focused
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 输入事件
     */
    handleInput(e) {
      const { value } = e.detail;
      this.setData({
        inputValue: value
      });

      if (this.data.useDebounce) {
        this.debouncedInput(value);
      } else {
        this.triggerEvent('input', { value });
        this.triggerEvent('change', { value });
      }
    },

    /**
     * 聚焦事件
     */
    handleFocus(e) {
      this.setData({
        focused: true
      });
      this.triggerEvent('focus', e.detail);
    },

    /**
     * 失焦事件
     */
    handleBlur(e) {
      this.setData({
        focused: false
      });
      this.triggerEvent('blur', e.detail);
    },

    /**
     * 确认事件
     */
    handleConfirm(e) {
      this.triggerEvent('confirm', e.detail);
    },

    /**
     * 键盘高度变化事件
     */
    handleKeyboardHeightChange(e) {
      this.triggerEvent('keyboardheightchange', e.detail);
    },

    /**
     * 清除输入内容
     */
    handleClear() {
      this.setData({
        inputValue: ''
      });
      this.triggerEvent('input', { value: '' });
      this.triggerEvent('change', { value: '' });
      this.triggerEvent('clear');
    },

    /**
     * 点击前缀图标
     */
    handlePrefixIconClick() {
      this.triggerEvent('prefixiconclick');
    },

    /**
     * 点击后缀图标
     */
    handleSuffixIconClick() {
      this.triggerEvent('suffixiconclick');
    },

    /**
     * 计算输入框样式类
     */
    getInputClass() {
      const { baseClass, size, shape, border, disabled, readonly, error, focused } = this.data;

      return classNames(baseClass, {
        [`${size}`]: size,
        [`${shape}`]: shape,
        'no-border': !border,
        'disabled': disabled,
        'readonly': readonly,
        'error': error,
        'focused': focused
      });
    },

    /**
     * 计算输入框样式
     */
    getInputStyle() {
      const { customStyle } = this.data;
      return customStyle;
    }
  }
});
