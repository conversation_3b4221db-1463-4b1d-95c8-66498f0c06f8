<view class="nl-input-container">
  <!-- 输入框标签 -->
  <view wx:if="{{label}}" class="nl-input-label">
    <text wx:if="{{required}}" class="nl-input-required">*</text>
    <text>{{label}}</text>
  </view>
  
  <view class="{{getInputClass()}}" style="{{getInputStyle()}}">
    <!-- 前缀图标 -->
    <view wx:if="{{prefixIcon}}" class="nl-input__prefix" bindtap="handlePrefixIconClick">
      <view class="nl-icon nl-icon-{{prefixIcon}}"></view>
    </view>
    
    <!-- 输入框 -->
    <input
      class="nl-input__field"
      type="{{type}}"
      value="{{inputValue}}"
      placeholder="{{placeholder}}"
      placeholder-style="{{placeholderStyle}}"
      placeholder-class="{{placeholderClass}}"
      disabled="{{disabled}}"
      maxlength="{{maxlength}}"
      focus="{{focus}}"
      confirm-type="{{confirmType}}"
      confirm-hold="{{confirmHold}}"
      cursor="{{cursor}}"
      cursor-color="{{cursorColor}}"
      selection-start="{{selectionStart}}"
      selection-end="{{selectionEnd}}"
      adjust-position="{{adjustPosition}}"
      bindinput="handleInput"
      bindfocus="handleFocus"
      bindblur="handleBlur"
      bindconfirm="handleConfirm"
      bindkeyboardheightchange="handleKeyboardHeightChange"
      readonly="{{readonly}}"
    />
    
    <!-- 清除按钮 -->
    <view wx:if="{{showClear}}" class="nl-input__clear" bindtap="handleClear">
      <view class="nl-input__clear-icon">×</view>
    </view>
    
    <!-- 后缀图标 -->
    <view wx:if="{{suffixIcon}}" class="nl-input__suffix" bindtap="handleSuffixIconClick">
      <view class="nl-icon nl-icon-{{suffixIcon}}"></view>
    </view>
    
    <!-- 字数统计 -->
    <view wx:if="{{showCount && maxlength > 0}}" class="nl-input__count">
      <text>{{inputValue.length}}</text>
      <text>/</text>
      <text>{{maxlength}}</text>
    </view>
  </view>
  
  <!-- 错误提示 -->
  <view wx:if="{{error && errorMessage}}" class="nl-input-error-message">
    {{errorMessage}}
  </view>
</view>
