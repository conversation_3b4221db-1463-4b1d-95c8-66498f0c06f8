/* 输入框容器 */
.nl-input-container {
  position: relative;
  margin-bottom: var(--space-md);
}

/* 输入框标签 */
.nl-input-label {
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  margin-bottom: var(--space-sm);
  display: flex;
  align-items: center;
}

/* 必填标记 */
.nl-input-required {
  color: var(--error-color);
  margin-right: var(--space-xs);
}

/* 输入框基础样式 */
.nl-input {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  background-color: var(--bg-color-paper);
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal) var(--easing-standard);
}

/* 输入框边框 */
.nl-input {
  border: 1px solid var(--divider-color);
}

.nl-input.no-border {
  border: none;
}

/* 输入框尺寸 */
.nl-input.small {
  height: 32px;
  font-size: var(--font-size-xs);
}

.nl-input.medium {
  height: 40px;
  font-size: var(--font-size-sm);
}

.nl-input.large {
  height: 48px;
  font-size: var(--font-size-md);
}

/* 输入框形状 */
.nl-input.square {
  border-radius: var(--radius-sm);
}

.nl-input.rounded {
  border-radius: var(--radius-md);
}

.nl-input.circle {
  border-radius: var(--radius-full);
}

/* 输入框状态 */
.nl-input.focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-light);
}

.nl-input.error {
  border-color: var(--error-color);
}

.nl-input.disabled {
  background-color: var(--grey-100);
  border-color: var(--grey-300);
  color: var(--text-color-disabled);
  cursor: not-allowed;
  pointer-events: none;
}

.nl-input.readonly {
  background-color: var(--grey-50);
  cursor: default;
}

/* 输入框字段 */
.nl-input__field {
  flex: 1;
  height: 100%;
  padding: 0 var(--space-sm);
  border: none;
  background: none;
  color: var(--text-color-primary);
  font-size: inherit;
  line-height: normal;
}

/* 输入框占位符 */
.input-placeholder {
  color: var(--text-color-hint);
}

/* 前缀图标 */
.nl-input__prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: var(--space-sm);
  color: var(--text-color-secondary);
}

/* 后缀图标 */
.nl-input__suffix {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: var(--space-sm);
  color: var(--text-color-secondary);
}

/* 清除按钮 */
.nl-input__clear {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: var(--space-xs);
  color: var(--text-color-disabled);
}

.nl-input__clear-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background-color: var(--grey-400);
  color: var(--white);
  font-size: var(--font-size-xs);
  line-height: 1;
}

/* 字数统计 */
.nl-input__count {
  padding-right: var(--space-sm);
  font-size: var(--font-size-xs);
  color: var(--text-color-hint);
}

/* 错误提示 */
.nl-input-error-message {
  font-size: var(--font-size-xs);
  color: var(--error-color);
  margin-top: var(--space-xs);
  line-height: var(--line-height-normal);
}
