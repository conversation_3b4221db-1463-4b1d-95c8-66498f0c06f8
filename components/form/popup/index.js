/**
 * 弹出层组件
 * 提供各种方向的弹出层
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹出层
    show: {
      type: Boolean,
      value: false,
      observer: function (newVal) {
        if (newVal) {
          this.enter();
        } else {
          this.leave();
        }
      }
    },
    // 弹出位置
    position: {
      type: String,
      value: 'bottom' // top, right, bottom, left, center
    },
    // 是否显示遮罩层
    overlay: {
      type: Boolean,
      value: true
    },
    // 点击遮罩是否关闭
    closeOnClickOverlay: {
      type: Boolean,
      value: true
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      value: false
    },
    // 关闭按钮位置
    closePosition: {
      type: String,
      value: 'top-right' // top-right, top-left, bottom-right, bottom-left
    },
    // 弹出层圆角
    round: {
      type: Boolean,
      value: false
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 弹出层宽度
    width: {
      type: String,
      value: ''
    },
    // 弹出层高度
    height: {
      type: String,
      value: ''
    },
    // 弹出层z-index
    zIndex: {
      type: Number,
      value: 100
    },
    // 动画时长，单位毫秒
    duration: {
      type: Number,
      value: 300
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 是否锁定背景滚动
    lockScroll: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-popup',
    inited: false,
    display: false,
    currentDuration: 0
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      if (this.data.show) {
        this.enter();
      }
    },
    detached() {
      this.setData({ inited: false });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 进入动画
     */
    enter() {
      const { duration } = this.data;

      this.setData({ display: true, inited: true, currentDuration: 0 });

      // 强制重绘
      setTimeout(() => {
        this.setData({ currentDuration: duration });
      }, 20);
    },

    /**
     * 离开动画
     */
    leave() {
      const { duration } = this.data;

      this.setData({ currentDuration: duration });

      setTimeout(() => {
        this.setData({ display: false });
      }, duration);
    },

    /**
     * 点击遮罩层
     */
    handleClickOverlay() {
      if (this.data.closeOnClickOverlay) {
        this.triggerEvent('close');
      }
    },

    /**
     * 点击关闭按钮
     */
    handleClickClose() {
      this.triggerEvent('close');
    },

    /**
     * 阻止冒泡
     */
    handleStopPropagation() {
      // 阻止冒泡，防止点击内容区域触发遮罩点击事件
    },

    /**
     * 计算弹出层样式类
     */
    getPopupClass() {
      const { baseClass, position, round, display } = this.data;

      return `${baseClass} ${baseClass}--${position} ${round ? baseClass + '--round' : ''} ${display ? baseClass + '--show' : ''}`;
    },

    /**
     * 计算弹出层样式
     */
    getPopupStyle() {
      const { zIndex, customStyle, width, height, currentDuration, position } = this.data;
      const style = [];

      style.push(`z-index: ${zIndex}`);
      style.push(`transition-duration: ${currentDuration}ms`);

      if (width && (position === 'left' || position === 'right' || position === 'center')) {
        style.push(`width: ${width}`);
      }

      if (height && (position === 'top' || position === 'bottom' || position === 'center')) {
        style.push(`height: ${height}`);
      }

      if (customStyle) {
        style.push(customStyle);
      }

      return style.join('; ');
    },

    /**
     * 计算遮罩层样式
     */
    getOverlayStyle() {
      const { zIndex, currentDuration } = this.data;
      return `z-index: ${zIndex}; transition-duration: ${currentDuration}ms;`;
    },

    /**
     * 计算关闭按钮样式类
     */
    getCloseClass() {
      const { baseClass, closePosition } = this.data;
      return `${baseClass}__close ${baseClass}__close--${closePosition}`;
    }
  }
});
