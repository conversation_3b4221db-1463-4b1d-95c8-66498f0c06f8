<view wx:if="{{inited}}" class="nl-popup-container">
  <!-- 遮罩层 -->
  <view 
    wx:if="{{overlay}}" 
    class="nl-popup-overlay {{display ? 'nl-popup-overlay--show' : ''}}" 
    style="{{getOverlayStyle()}}" 
    bindtap="handleClickOverlay"
  ></view>
  
  <!-- 弹出层 -->
  <view 
    class="{{getPopupClass()}}" 
    style="{{getPopupStyle()}}" 
    bindtap="handleStopPropagation"
  >
    <!-- 标题 -->
    <view wx:if="{{title}}" class="nl-popup__title">
      {{title}}
    </view>
    
    <!-- 关闭按钮 -->
    <view 
      wx:if="{{showClose}}" 
      class="{{getCloseClass()}}" 
      bindtap="handleClickClose"
    >
      <view class="nl-popup__close-icon">×</view>
    </view>
    
    <!-- 内容 -->
    <view class="nl-popup__content">
      <slot></slot>
    </view>
  </view>
</view>
