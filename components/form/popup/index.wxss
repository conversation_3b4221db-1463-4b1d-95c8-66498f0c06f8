/* 弹出层容器 */
.nl-popup-container {
  position: relative;
}

/* 遮罩层 */
.nl-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  opacity: 0;
  transition: opacity var(--transition-normal) var(--easing-standard);
  visibility: hidden;
}

.nl-popup-overlay--show {
  opacity: 1;
  visibility: visible;
}

/* 弹出层基础样式 */
.nl-popup {
  position: fixed;
  background-color: var(--white);
  transition: all var(--transition-normal) var(--easing-standard);
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  visibility: hidden;
}

/* 弹出层显示状态 */
.nl-popup--show {
  visibility: visible;
}

/* 弹出层位置 */
.nl-popup--top {
  top: 0;
  left: 0;
  width: 100%;
  transform: translateY(-100%);
}

.nl-popup--top.nl-popup--show {
  transform: translateY(0);
}

.nl-popup--right {
  top: 0;
  right: 0;
  height: 100%;
  transform: translateX(100%);
}

.nl-popup--right.nl-popup--show {
  transform: translateX(0);
}

.nl-popup--bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  transform: translateY(100%);
}

.nl-popup--bottom.nl-popup--show {
  transform: translateY(0);
}

.nl-popup--left {
  top: 0;
  left: 0;
  height: 100%;
  transform: translateX(-100%);
}

.nl-popup--left.nl-popup--show {
  transform: translateX(0);
}

.nl-popup--center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
}

.nl-popup--center.nl-popup--show {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

/* 圆角弹出层 */
.nl-popup--round.nl-popup--top {
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.nl-popup--round.nl-popup--right {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.nl-popup--round.nl-popup--bottom {
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}

.nl-popup--round.nl-popup--left {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.nl-popup--round.nl-popup--center {
  border-radius: var(--radius-lg);
}

/* 弹出层标题 */
.nl-popup__title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  text-align: center;
  padding: var(--space-md);
  border-bottom: 1px solid var(--divider-color);
}

/* 弹出层内容 */
.nl-popup__content {
  padding: var(--space-md);
}

/* 关闭按钮 */
.nl-popup__close {
  position: absolute;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  color: var(--text-color-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
}

.nl-popup__close--top-right {
  top: 10px;
  right: 10px;
}

.nl-popup__close--top-left {
  top: 10px;
  left: 10px;
}

.nl-popup__close--bottom-right {
  bottom: 10px;
  right: 10px;
}

.nl-popup__close--bottom-left {
  bottom: 10px;
  left: 10px;
}

.nl-popup__close-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
