/**
 * 进度条组件
 * 提供进度展示功能
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 进度值（0-100）
    value: {
      type: Number,
      value: 0,
      observer: function (newVal) {
        // 确保值在0-100之间
        const value = Math.max(0, Math.min(100, newVal));
        this.setData({ progressValue: value });
      }
    },
    // 进度条类型
    type: {
      type: String,
      value: 'line' // line, circle
    },
    // 进度条颜色类型
    color: {
      type: String,
      value: 'primary' // primary, success, warning, error, info
    },
    // 自定义颜色
    customColor: {
      type: String,
      value: ''
    },
    // 进度条尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否显示文字
    showText: {
      type: Boolean,
      value: true
    },
    // 文字位置
    textPosition: {
      type: String,
      value: 'right' // right, inside
    },
    // 自定义文字
    format: {
      type: String,
      value: ''
    },
    // 是否显示条纹
    striped: {
      type: Boolean,
      value: false
    },
    // 是否显示动画
    animated: {
      type: Boolean,
      value: false
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 轨道颜色
    trackColor: {
      type: String,
      value: ''
    },
    // 是否圆角
    rounded: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-progress',
    progressValue: 0
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化进度值
      this.setData({
        progressValue: Math.max(0, Math.min(100, this.data.value))
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算进度条样式类
     */
    getProgressClass() {
      const { baseClass, type, color, size, striped, animated, rounded, textPosition } = this.data;

      return `${baseClass} ${baseClass}--${type} ${baseClass}--${color} ${baseClass}--${size} ${striped ? baseClass + '--striped' : ''} ${animated ? baseClass + '--animated' : ''} ${rounded ? baseClass + '--rounded' : ''} ${textPosition === 'inside' ? baseClass + '--text-inside' : ''}`;
    },

    /**
     * 计算进度条样式
     */
    getProgressStyle() {
      const { progressValue, customColor, trackColor } = this.data;
      const style = {};

      if (customColor) {
        style['--progress-color'] = customColor;
      }

      if (trackColor) {
        style['--track-color'] = trackColor;
      }

      return Object.keys(style).map(key => `${key}: ${style[key]}`).join(';');
    },

    /**
     * 获取进度文本
     */
    getProgressText() {
      const { progressValue, format } = this.data;

      if (format) {
        return format.replace('{value}', progressValue);
      }

      return `${progressValue}%`;
    }
  }
});
