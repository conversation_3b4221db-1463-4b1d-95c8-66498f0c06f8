<view class="{{getProgressClass()}}" style="{{customStyle}}; {{getProgressStyle()}}">
  <!-- 线性进度条 -->
  <block wx:if="{{type === 'line'}}">
    <view class="nl-progress__track">
      <view class="nl-progress__bar" style="width: {{progressValue}}%"></view>
    </view>
    
    <!-- 右侧文字 -->
    <view wx:if="{{showText && textPosition === 'right'}}" class="nl-progress__text">
      {{getProgressText()}}
    </view>
    
    <!-- 内部文字 -->
    <view wx:if="{{showText && textPosition === 'inside' && progressValue > 10}}" class="nl-progress__text-inside">
      {{getProgressText()}}
    </view>
  </block>
  
  <!-- 圆形进度条 -->
  <block wx:if="{{type === 'circle'}}">
    <view class="nl-progress__circle">
      <!-- 使用微信小程序的canvas绘制圆形进度条 -->
      <canvas class="nl-progress__canvas" canvas-id="progressCanvas"></canvas>
      
      <!-- 中间文字 -->
      <view wx:if="{{showText}}" class="nl-progress__text-circle">
        {{getProgressText()}}
      </view>
    </view>
  </block>
</view>
