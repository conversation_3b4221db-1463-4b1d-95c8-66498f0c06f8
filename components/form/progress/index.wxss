/* 进度条容器 */
.nl-progress {
  display: flex;
  align-items: center;
  width: 100%;
  --progress-color: var(--primary-color);
  --track-color: var(--grey-200);
}

/* 进度条轨道 */
.nl-progress__track {
  flex: 1;
  height: 8px;
  background-color: var(--track-color);
  overflow: hidden;
  position: relative;
}

/* 进度条 */
.nl-progress__bar {
  height: 100%;
  background-color: var(--progress-color);
  transition: width 0.3s ease;
}

/* 进度文字 */
.nl-progress__text {
  margin-left: var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  white-space: nowrap;
}

/* 内部文字 */
.nl-progress__text-inside {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: var(--space-sm);
  font-size: var(--font-size-xs);
  color: var(--white);
}

/* 圆形进度条 */
.nl-progress--circle {
  width: 120px;
  height: 120px;
  position: relative;
}

.nl-progress__circle {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nl-progress__canvas {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.nl-progress__text-circle {
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
}

/* 进度条尺寸 */
.nl-progress--small .nl-progress__track {
  height: 4px;
}

.nl-progress--medium .nl-progress__track {
  height: 8px;
}

.nl-progress--large .nl-progress__track {
  height: 12px;
}

.nl-progress--small.nl-progress--circle {
  width: 80px;
  height: 80px;
}

.nl-progress--medium.nl-progress--circle {
  width: 120px;
  height: 120px;
}

.nl-progress--large.nl-progress--circle {
  width: 160px;
  height: 160px;
}

/* 进度条颜色 */
.nl-progress--primary {
  --progress-color: var(--primary-color);
}

.nl-progress--success {
  --progress-color: var(--success-color);
}

.nl-progress--warning {
  --progress-color: var(--warning-color);
}

.nl-progress--error {
  --progress-color: var(--error-color);
}

.nl-progress--info {
  --progress-color: var(--info-color);
}

/* 圆角进度条 */
.nl-progress--rounded .nl-progress__track {
  border-radius: var(--radius-full);
}

.nl-progress--rounded .nl-progress__bar {
  border-radius: var(--radius-full);
}

/* 条纹进度条 */
.nl-progress--striped .nl-progress__bar {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 16px 16px;
}

/* 动画进度条 */
.nl-progress--animated .nl-progress__bar {
  animation: nl-progress-stripes 1s linear infinite;
}

@keyframes nl-progress-stripes {
  from {
    background-position: 16px 0;
  }
  to {
    background-position: 0 0;
  }
}
