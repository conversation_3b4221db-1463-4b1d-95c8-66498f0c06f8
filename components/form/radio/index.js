/**
 * 单选框组件
 * 提供单选项选择功能
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 单选框值
    value: {
      type: Boolean,
      value: false
    },
    // 单选框标签
    label: {
      type: String,
      value: ''
    },
    // 单选框名称（用于表单提交）
    name: {
      type: String,
      value: ''
    },
    // 单选框尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 单选框颜色类型
    type: {
      type: String,
      value: 'primary' // primary, success, warning, error, info
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      value: false
    },
    // 标签位置
    labelPosition: {
      type: String,
      value: 'right' // left, right
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-radio'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 选择单选框
     */
    handleSelect() {
      if (this.data.disabled || this.data.readonly || this.data.value) return;

      this.setData({
        value: true
      });

      this.triggerEvent('change', { value: true, name: this.data.name });
    },

    /**
     * 计算单选框样式类
     */
    getRadioClass() {
      const { baseClass, size, type, disabled, readonly, value } = this.data;

      return `${baseClass} ${baseClass}--${size} ${baseClass}--${type} ${disabled ? baseClass + '--disabled' : ''} ${readonly ? baseClass + '--readonly' : ''} ${value ? baseClass + '--checked' : ''}`;
    }
  }
});
