<view class="nl-radio-container" bindtap="handleSelect" style="{{customStyle}}">
  <!-- 左侧标签 -->
  <view wx:if="{{label && labelPosition === 'left'}}" class="nl-radio-label">
    {{label}}
  </view>
  
  <!-- 单选框 -->
  <view class="{{getRadioClass()}}">
    <view class="nl-radio__inner">
      <view wx:if="{{value}}" class="nl-radio__dot"></view>
    </view>
  </view>
  
  <!-- 右侧标签 -->
  <view wx:if="{{label && labelPosition === 'right'}}" class="nl-radio-label">
    {{label}}
  </view>
</view>
