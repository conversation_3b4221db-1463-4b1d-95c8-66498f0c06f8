/* 单选框容器 */
.nl-radio-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-sm);
}

/* 单选框标签 */
.nl-radio-label {
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  margin: 0 var(--space-sm);
  line-height: 1.5;
}

/* 单选框基础样式 */
.nl-radio {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  transition: all var(--transition-normal) var(--easing-standard);
}

/* 单选框内部容器 */
.nl-radio__inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1px solid var(--grey-400);
  border-radius: var(--radius-full);
  background-color: var(--white);
  transition: all var(--transition-normal) var(--easing-standard);
}

/* 单选框尺寸 */
.nl-radio--small .nl-radio__inner {
  width: 14px;
  height: 14px;
}

.nl-radio--medium .nl-radio__inner {
  width: 18px;
  height: 18px;
}

.nl-radio--large .nl-radio__inner {
  width: 22px;
  height: 22px;
}

/* 单选框类型 */
.nl-radio--primary.nl-radio--checked .nl-radio__inner {
  border-color: var(--primary-color);
}

.nl-radio--success.nl-radio--checked .nl-radio__inner {
  border-color: var(--success-color);
}

.nl-radio--warning.nl-radio--checked .nl-radio__inner {
  border-color: var(--warning-color);
}

.nl-radio--error.nl-radio--checked .nl-radio__inner {
  border-color: var(--error-color);
}

.nl-radio--info.nl-radio--checked .nl-radio__inner {
  border-color: var(--info-color);
}

/* 单选框状态 */
.nl-radio--disabled .nl-radio__inner {
  background-color: var(--grey-100);
  border-color: var(--grey-300);
  cursor: not-allowed;
}

.nl-radio--readonly .nl-radio__inner {
  cursor: default;
}

/* 单选框圆点 */
.nl-radio__dot {
  width: 0;
  height: 0;
  border-radius: var(--radius-full);
  background-color: currentColor;
  transition: all var(--transition-normal) var(--easing-standard);
}

.nl-radio--small .nl-radio__dot {
  width: 6px;
  height: 6px;
}

.nl-radio--medium .nl-radio__dot {
  width: 8px;
  height: 8px;
}

.nl-radio--large .nl-radio__dot {
  width: 10px;
  height: 10px;
}

/* 单选框圆点颜色 */
.nl-radio--primary .nl-radio__dot {
  background-color: var(--primary-color);
}

.nl-radio--success .nl-radio__dot {
  background-color: var(--success-color);
}

.nl-radio--warning .nl-radio__dot {
  background-color: var(--warning-color);
}

.nl-radio--error .nl-radio__dot {
  background-color: var(--error-color);
}

.nl-radio--info .nl-radio__dot {
  background-color: var(--info-color);
}

.nl-radio--disabled .nl-radio__dot {
  background-color: var(--grey-400);
}
