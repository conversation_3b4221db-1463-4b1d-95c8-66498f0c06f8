/**
 * 滑块组件
 * 提供范围选择功能
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 滑块当前值
    value: {
      type: Number,
      value: 0,
      observer: function (newVal) {
        // 确保值在min和max之间
        const value = Math.max(this.data.min, Math.min(this.data.max, newVal));
        if (value !== this.data.sliderValue) {
          this.setData({ sliderValue: value });
          this.updateSliderPosition();
        }
      }
    },
    // 最小值
    min: {
      type: Number,
      value: 0
    },
    // 最大值
    max: {
      type: Number,
      value: 100
    },
    // 步长
    step: {
      type: Number,
      value: 1
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否显示值
    showValue: {
      type: Boolean,
      value: false
    },
    // 滑块颜色类型
    type: {
      type: String,
      value: 'primary' // primary, success, warning, error, info
    },
    // 自定义颜色
    activeColor: {
      type: String,
      value: ''
    },
    // 轨道颜色
    backgroundColor: {
      type: String,
      value: ''
    },
    // 滑块尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否显示刻度
    showTicks: {
      type: Boolean,
      value: false
    },
    // 刻度数量
    tickCount: {
      type: Number,
      value: 5
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 标签
    label: {
      type: String,
      value: ''
    },
    // 值格式化函数
    format: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    baseClass: 'nl-slider',
    sliderValue: 0,
    sliderLeft: 0,
    sliderWidth: 0,
    isDragging: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化滑块值
      this.setData({
        sliderValue: Math.max(this.data.min, Math.min(this.data.max, this.data.value))
      });

      // 获取滑块尺寸
      this.getSliderSize();
    },
    ready() {
      // 更新滑块位置
      this.updateSliderPosition();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 获取滑块尺寸
     */
    getSliderSize() {
      const query = this.createSelectorQuery();
      query.select('.nl-slider__track').boundingClientRect(rect => {
        if (rect) {
          this.setData({
            sliderWidth: rect.width
          });
          this.updateSliderPosition();
        }
      }).exec();
    },

    /**
     * 更新滑块位置
     */
    updateSliderPosition() {
      if (this.data.sliderWidth === 0) return;

      const { sliderValue, min, max, sliderWidth } = this.data;
      const percentage = (sliderValue - min) / (max - min);
      const left = percentage * sliderWidth;

      this.setData({
        sliderLeft: left
      });
    },

    /**
     * 处理滑块触摸开始
     */
    handleTouchStart(e) {
      if (this.data.disabled) return;

      this.setData({
        isDragging: true
      });

      this.updateValueByPosition(e.touches[0].pageX);
    },

    /**
     * 处理滑块触摸移动
     */
    handleTouchMove(e) {
      if (this.data.disabled || !this.data.isDragging) return;

      this.updateValueByPosition(e.touches[0].pageX);
    },

    /**
     * 处理滑块触摸结束
     */
    handleTouchEnd() {
      if (this.data.disabled) return;

      this.setData({
        isDragging: false
      });

      this.triggerEvent('change', { value: this.data.sliderValue });
    },

    /**
     * 处理滑块轨道点击
     */
    handleTrackClick(e) {
      if (this.data.disabled) return;

      this.updateValueByPosition(e.detail.x);
      this.triggerEvent('change', { value: this.data.sliderValue });
    },

    /**
     * 根据位置更新值
     */
    updateValueByPosition(pageX) {
      const query = this.createSelectorQuery();
      query.select('.nl-slider__track').boundingClientRect(rect => {
        if (!rect) return;

        const { min, max, step } = this.data;
        const sliderWidth = rect.width;
        const offsetX = pageX - rect.left;
        const percentage = Math.min(1, Math.max(0, offsetX / sliderWidth));

        // 计算步长对齐的值
        const range = max - min;
        const rawValue = percentage * range + min;
        const steps = Math.round((rawValue - min) / step);
        const value = min + steps * step;

        // 更新值
        this.setData({
          sliderValue: value,
          sliderLeft: percentage * sliderWidth
        });

        this.triggerEvent('input', { value });
      }).exec();
    },

    /**
     * 计算滑块样式类
     */
    getSliderClass() {
      const { baseClass, type, size, disabled, isDragging, showTicks } = this.data;

      return `${baseClass} ${baseClass}--${type} ${baseClass}--${size} ${disabled ? baseClass + '--disabled' : ''} ${isDragging ? baseClass + '--active' : ''} ${showTicks ? baseClass + '--with-ticks' : ''}`;
    },

    /**
     * 计算滑块样式
     */
    getSliderStyle() {
      const { activeColor, backgroundColor } = this.data;
      const style = {};

      if (activeColor) {
        style['--active-color'] = activeColor;
      }

      if (backgroundColor) {
        style['--background-color'] = backgroundColor;
      }

      return Object.keys(style).map(key => `${key}: ${style[key]}`).join(';');
    },

    /**
     * 获取格式化的值
     */
    getFormattedValue() {
      const { sliderValue, format } = this.data;

      if (format) {
        return format.replace('{value}', sliderValue);
      }

      return sliderValue;
    },

    /**
     * 生成刻度数组
     */
    getTicks() {
      const { min, max, tickCount } = this.data;
      const ticks = [];

      if (tickCount <= 1) return ticks;

      const step = (max - min) / (tickCount - 1);

      for (let i = 0; i < tickCount; i++) {
        const value = min + i * step;
        const percentage = i / (tickCount - 1) * 100;

        ticks.push({
          value,
          percentage
        });
      }

      return ticks;
    }
  }
});
