<view class="nl-slider-container" style="{{customStyle}}">
  <!-- 标签 -->
  <view wx:if="{{label}}" class="nl-slider-label">
    {{label}}
  </view>
  
  <view class="{{getSliderClass()}}" style="{{getSliderStyle()}}">
    <!-- 滑块轨道 -->
    <view class="nl-slider__track" bindtap="handleTrackClick">
      <!-- 已选择部分 -->
      <view class="nl-slider__progress" style="width: {{sliderLeft}}px"></view>
      
      <!-- 滑块手柄 -->
      <view 
        class="nl-slider__handle" 
        style="left: {{sliderLeft}}px"
        bindtouchstart="handleTouchStart"
        bindtouchmove="handleTouchMove"
        bindtouchend="handleTouchEnd"
        bindtouchcancel="handleTouchEnd"
      ></view>
      
      <!-- 刻度 -->
      <block wx:if="{{showTicks}}">
        <view 
          wx:for="{{getTicks()}}" 
          wx:key="index"
          class="nl-slider__tick {{sliderValue >= item.value ? 'nl-slider__tick--active' : ''}}"
          style="left: {{item.percentage}}%"
        ></view>
      </block>
    </view>
    
    <!-- 显示值 -->
    <view wx:if="{{showValue}}" class="nl-slider__value">
      {{getFormattedValue()}}
    </view>
  </view>
</view>
