/* 滑块容器 */
.nl-slider-container {
  margin-bottom: var(--space-md);
  --active-color: var(--primary-color);
  --background-color: var(--grey-200);
}

/* 滑块标签 */
.nl-slider-label {
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  margin-bottom: var(--space-sm);
}

/* 滑块基础样式 */
.nl-slider {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
}

/* 滑块轨道 */
.nl-slider__track {
  position: relative;
  flex: 1;
  height: 4px;
  background-color: var(--background-color);
  border-radius: var(--radius-full);
  cursor: pointer;
}

/* 滑块进度 */
.nl-slider__progress {
  position: absolute;
  height: 100%;
  background-color: var(--active-color);
  border-radius: var(--radius-full);
}

/* 滑块手柄 */
.nl-slider__handle {
  position: absolute;
  top: 50%;
  width: 16px;
  height: 16px;
  background-color: var(--white);
  border: 2px solid var(--active-color);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
  z-index: 1;
}

/* 滑块值 */
.nl-slider__value {
  margin-left: var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  min-width: 40px;
  text-align: right;
}

/* 滑块刻度 */
.nl-slider__tick {
  position: absolute;
  top: 50%;
  width: 4px;
  height: 4px;
  background-color: var(--background-color);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
}

.nl-slider__tick--active {
  background-color: var(--active-color);
}

/* 滑块尺寸 */
.nl-slider--small {
  height: 32px;
}

.nl-slider--small .nl-slider__track {
  height: 2px;
}

.nl-slider--small .nl-slider__handle {
  width: 12px;
  height: 12px;
}

.nl-slider--medium {
  height: 40px;
}

.nl-slider--medium .nl-slider__track {
  height: 4px;
}

.nl-slider--medium .nl-slider__handle {
  width: 16px;
  height: 16px;
}

.nl-slider--large {
  height: 48px;
}

.nl-slider--large .nl-slider__track {
  height: 6px;
}

.nl-slider--large .nl-slider__handle {
  width: 20px;
  height: 20px;
}

/* 滑块颜色 */
.nl-slider--primary {
  --active-color: var(--primary-color);
}

.nl-slider--success {
  --active-color: var(--success-color);
}

.nl-slider--warning {
  --active-color: var(--warning-color);
}

.nl-slider--error {
  --active-color: var(--error-color);
}

.nl-slider--info {
  --active-color: var(--info-color);
}

/* 滑块状态 */
.nl-slider--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nl-slider--disabled .nl-slider__track,
.nl-slider--disabled .nl-slider__handle {
  cursor: not-allowed;
}

.nl-slider--active .nl-slider__handle {
  box-shadow: 0 0 0 5px rgba(var(--active-color-rgb), 0.2);
}

/* 带刻度的滑块 */
.nl-slider--with-ticks .nl-slider__track {
  margin: 0 8px;
}
