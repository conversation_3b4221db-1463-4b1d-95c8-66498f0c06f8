// components/form/switch/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 开关值
    value: {
      type: Boolean,
      value: false
    },
    // 开关类型
    type: {
      type: String,
      value: 'default' // default, primary, success, warning, error, info
    },
    // 开关尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 开关标签
    label: {
      type: String,
      value: ''
    },
    // 标签位置
    labelPosition: {
      type: String,
      value: 'left' // left, right
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换开关状态
     */
    handleChange(e) {
      if (this.data.disabled || this.data.loading) return;

      const value = e.detail.value;

      this.triggerEvent('change', { value });
    }
  }
});
