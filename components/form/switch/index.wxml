<view class="nl-switch-container {{disabled ? 'nl-switch-disabled' : ''}}">
  <view wx:if="{{label && labelPosition === 'left'}}" class="nl-switch-label">{{label}}</view>
  
  <view class="nl-switch-wrapper nl-switch-{{size}} nl-switch-{{type}}">
    <view wx:if="{{loading}}" class="nl-switch-loading"></view>
    <switch 
      checked="{{value}}" 
      disabled="{{disabled || loading}}" 
      bindchange="handleChange"
      color="{{type === 'default' ? '' : ''}}"
    />
  </view>
  
  <view wx:if="{{label && labelPosition === 'right'}}" class="nl-switch-label">{{label}}</view>
</view>
