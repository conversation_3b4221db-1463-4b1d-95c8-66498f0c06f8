/* 开关容器 */
.nl-switch-container {
  display: flex;
  align-items: center;
}

/* 开关包装器 */
.nl-switch-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* 开关标签 */
.nl-switch-label {
  font-size: var(--font-size-sm);
  color: var(--text-color-primary);
  margin: 0 var(--space-sm);
}

/* 开关尺寸 */
.nl-switch-small {
  transform: scale(0.8);
  transform-origin: center;
}

.nl-switch-medium {
  /* 默认尺寸 */
}

.nl-switch-large {
  transform: scale(1.2);
  transform-origin: center;
}

/* 开关类型 - 通过设置switch的color属性实现 */
.nl-switch-default switch {
  /* 使用微信默认颜色 */
}

.nl-switch-primary switch {
  color: var(--primary-color) !important;
}

.nl-switch-success switch {
  color: var(--success-color) !important;
}

.nl-switch-warning switch {
  color: var(--warning-color) !important;
}

.nl-switch-error switch {
  color: var(--error-color) !important;
}

.nl-switch-info switch {
  color: var(--info-color) !important;
}

/* 禁用状态 */
.nl-switch-disabled {
  opacity: 0.5;
}

/* 加载状态 */
.nl-switch-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid var(--grey-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: nl-switch-loading 0.8s linear infinite;
  z-index: 1;
}

@keyframes nl-switch-loading {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
