/**
 * 观点管理组件
 * 用于管理用户的观点，支持软删除功能
 */
Component({
  properties: {
    userId: {
      type: String,
      value: ''
    },
    tagId: {
      type: String,
      value: ''
    },
    showRecycleBin: {
      type: Boolean,
      value: false
    }
  },

  data: {
    insights: [],
    isLoading: false,
    isSelectMode: false,
    selectedInsights: [],
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    activeTab: 'all', // 'all', 'deleted'
    searchKeyword: ''
  },

  lifetimes: {
    attached() {
      // 加载观点列表
      this.loadInsights();
    }
  },

  methods: {
    // 加载观点列表
    loadInsights() {
      this.setData({ isLoading: true });

      const { page, pageSize } = this.data.pagination;
      const params = {
        page,
        pageSize,
        userId: this.properties.userId,
        tagId: this.properties.tagId,
        keyword: this.data.searchKeyword
      };

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[insight-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '加载失败', icon: 'none' });
        return;
      }

      // 根据当前标签页加载不同数据
      const promise = this.data.activeTab === 'deleted'
        ? api.insight.getDeletedInsights(params)
        : api.insight.getInsights(params);

      promise.then(result => {
        this.setData({
          insights: result.data || [],
          'pagination.total': result.total || 0,
          isLoading: false
        });
      }).catch(err => {
        console.error('加载观点失败:', err);
        wx.showToast({ title: '加载失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 切换标签页
    switchTab(e) {
      const tab = e.currentTarget.dataset.tab;
      if (tab === this.data.activeTab) return;

      this.setData({
        activeTab: tab,
        'pagination.page': 1,
        isSelectMode: false,
        selectedInsights: []
      }, () => {
        this.loadInsights();
      });
    },

    // 分页加载更多
    loadMore() {
      if (this.data.isLoading) return;

      const { page, pageSize, total } = this.data.pagination;
      if (page * pageSize >= total) return;

      this.setData({
        'pagination.page': page + 1
      }, () => {
        this.loadInsights();
      });
    },

    // 搜索观点
    handleSearch(e) {
      const keyword = e.detail.value;
      this.setData({
        searchKeyword: keyword,
        'pagination.page': 1
      }, () => {
        this.loadInsights();
      });
    },

    // 清除搜索
    clearSearch() {
      this.setData({
        searchKeyword: '',
        'pagination.page': 1
      }, () => {
        this.loadInsights();
      });
    },

    // 查看观点详情
    viewInsightDetail(e) {
      const insightId = e.currentTarget.dataset.id;
      if (!insightId) return;

      // 触发查看详情事件
      this.triggerEvent('view', { id: insightId });
    },

    // 编辑观点
    editInsight(e) {
      const insightId = e.currentTarget.dataset.id;
      if (!insightId) return;

      // 触发编辑事件
      this.triggerEvent('edit', { id: insightId });
    },

    // 软删除观点
    softDeleteInsight(e) {
      const insightId = e.currentTarget.dataset.id;
      if (!insightId) return;

      wx.showModal({
        title: '删除确认',
        content: '确定要删除此观点吗？删除后可在回收站恢复。',
        success: res => {
          if (res.confirm) {
            this.performSoftDelete(insightId);
          }
        }
      });
    },

    // 执行软删除
    performSoftDelete(insightId) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[insight-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '删除失败', icon: 'none' });
        return;
      }

      api.insight.softDelete(insightId)
        .then(() => {
          wx.showToast({ title: '删除成功', icon: 'success' });
          // 重新加载列表
          this.loadInsights();
          // 触发删除事件
          this.triggerEvent('delete', { id: insightId });
        })
        .catch(err => {
          console.error('删除观点失败:', err);
          wx.showToast({ title: '删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    },

    // 创建新观点
    createInsight() {
      // 触发创建事件
      this.triggerEvent('create');
    },

    // 切换选择模式
    toggleSelectMode() {
      this.setData({
        isSelectMode: !this.data.isSelectMode,
        selectedInsights: []
      });
    },

    // 选择/取消选择观点
    toggleSelect(e) {
      if (!this.data.isSelectMode) return;

      const insightId = e.currentTarget.dataset.id;
      if (!insightId) return;

      const { selectedInsights } = this.data;
      const index = selectedInsights.indexOf(insightId);

      if (index === -1) {
        // 添加到选中列表
        selectedInsights.push(insightId);
      } else {
        // 从选中列表移除
        selectedInsights.splice(index, 1);
      }

      this.setData({ selectedInsights });
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (!this.data.isSelectMode) return;

      const { selectedInsights, insights } = this.data;

      if (selectedInsights.length === insights.length) {
        // 取消全选
        this.setData({ selectedInsights: [] });
      } else {
        // 全选
        this.setData({
          selectedInsights: insights.map(insight => insight.id)
        });
      }
    },

    // 批量删除
    batchDelete() {
      const { selectedInsights } = this.data;
      if (selectedInsights.length === 0) {
        wx.showToast({ title: '请先选择观点', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '批量删除确认',
        content: `确定要删除选中的${selectedInsights.length}个观点吗？删除后可在回收站恢复。`,
        success: res => {
          if (res.confirm) {
            this.performBatchDelete(selectedInsights);
          }
        }
      });
    },

    // 执行批量删除
    performBatchDelete(insightIds) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[insight-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '批量删除失败', icon: 'none' });
        return;
      }

      // 使用Promise.all执行批量删除
      Promise.all(insightIds.map(id => api.insight.softDelete(id)))
        .then(() => {
          wx.showToast({ title: '批量删除成功', icon: 'success' });
          // 重新加载列表
          this.loadInsights();
          // 退出选择模式
          this.setData({
            isSelectMode: false,
            selectedInsights: []
          });
          // 触发批量删除事件
          this.triggerEvent('batchDelete', { ids: insightIds });
        })
        .catch(err => {
          console.error('批量删除观点失败:', err);
          wx.showToast({ title: '批量删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    }
  }
});
