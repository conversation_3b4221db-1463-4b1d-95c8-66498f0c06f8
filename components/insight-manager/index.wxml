<view class="insight-manager">
  <!-- 顶部操作栏 -->
  <view class="action-bar">
    <view class="left-actions">
      <view wx:if="{{isSelectMode}}" class="select-all" bindtap="toggleSelectAll">
        {{selectedInsights.length === insights.length ? '取消全选' : '全选'}}
      </view>
      <view wx:else class="create-btn" bindtap="createInsight">
        <text class="icon">+</text> 新建观点
      </view>
    </view>
    
    <view class="right-actions">
      <view wx:if="{{isSelectMode}}" class="batch-actions">
        <view class="batch-delete" bindtap="batchDelete">删除</view>
        <view class="cancel-select" bindtap="toggleSelectMode">取消</view>
      </view>
      <view wx:else class="select-mode-btn" bindtap="toggleSelectMode">
        <text class="icon">☑</text> 选择
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索观点" 
        value="{{searchKeyword}}"
        confirm-type="search"
        bindconfirm="handleSearch"
      />
      <text wx:if="{{searchKeyword}}" class="clear-icon" bindtap="clearSearch">✕</text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === 'all' ? 'active' : ''}}" 
      data-tab="all" 
      bindtap="switchTab"
    >全部观点</view>
    <view 
      wx:if="{{showRecycleBin}}"
      class="tab {{activeTab === 'deleted' ? 'active' : ''}}" 
      data-tab="deleted" 
      bindtap="switchTab"
    >回收站</view>
  </view>

  <!-- 观点列表 -->
  <scroll-view 
    class="insight-list-container"
    scroll-y="true"
    bindscrolltolower="loadMore"
    lower-threshold="50"
  >
    <view class="insight-list">
      <view wx:if="{{isLoading && insights.length === 0}}" class="loading-placeholder">
        <text>加载中...</text>
      </view>
      
      <view wx:elif="{{!isLoading && insights.length === 0}}" class="empty-placeholder">
        <text>{{activeTab === 'deleted' ? '回收站为空' : '暂无观点'}}</text>
      </view>
      
      <block wx:else>
        <view 
          wx:for="{{insights}}" 
          wx:key="id"
          class="insight-item {{isSelectMode ? 'selectable' : ''}} {{selectedInsights.includes(item.id) ? 'selected' : ''}}"
          data-id="{{item.id}}"
          bindtap="{{isSelectMode ? 'toggleSelect' : 'viewInsightDetail'}}"
        >
          <view class="insight-content">
            <view class="insight-text">{{item.content}}</view>
            <view class="insight-meta">
              <text class="insight-source" wx:if="{{item.source}}">来源: {{item.source}}</text>
              <text class="insight-date">{{activeTab === 'deleted' ? '删除于: ' : '创建于: '}}{{item.deleted_at || item.created_at}}</text>
            </view>
          </view>
          
          <view class="insight-actions" wx:if="{{!isSelectMode && activeTab !== 'deleted'}}">
            <view class="action-btn edit-btn" catchtap="editInsight" data-id="{{item.id}}">
              <text class="icon">✎</text>
            </view>
            <view class="action-btn delete-btn" catchtap="softDeleteInsight" data-id="{{item.id}}">
              <text class="icon">🗑</text>
            </view>
          </view>
          
          <view class="select-indicator" wx:if="{{isSelectMode}}">
            <text class="icon">{{selectedInsights.includes(item.id) ? '☑' : '☐'}}</text>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 加载更多 -->
    <view wx:if="{{isLoading && insights.length > 0}}" class="loading-more">
      <text>加载中...</text>
    </view>
  </scroll-view>
</view>
