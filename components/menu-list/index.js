// components/menu-list/index.js
// 菜单列表组件

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    items: {
      type: Array,
      value: []
    },
    theme: {
      type: String,
      value: 'light' // 'light' 或 'dark'
    },
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理菜单项点击
     * @param {Object} e - 事件对象
     */
    handleItemClick(e) {
      const item = e.currentTarget.dataset.item;

      // 如果菜单项禁用，不处理点击
      if (item.disabled) {
        return;
      }

      // 触发点击事件
      this.triggerEvent('itemclick', { item });

      // 如果有URL，跳转到对应页面
      if (item.url) {
        wx.navigateTo({
          url: item.url,
          fail: err => {
            console.error('页面跳转失败', err);
          }
        });
      }
    }
  }
});
