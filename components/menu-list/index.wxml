<!-- components/menu-list/index.wxml -->
<!-- 菜单列表组件 -->

<view class="menu-list menu-list-{{theme}}" style="{{customStyle}}">
  <block wx:for="{{items}}" wx:key="id">
    <!-- 菜单项 -->
    <view 
      wx:if="{{item.type === 'item' || !item.type}}"
      class="menu-item {{item.disabled ? 'disabled' : ''}}"
      bindtap="handleItemClick"
      data-item="{{item}}"
      hover-class="menu-item-hover"
    >
      <!-- 图标 -->
      <view class="menu-item-icon" wx:if="{{item.icon}}">
        <image class="icon-image" src="{{item.icon}}" mode="aspectFit"></image>
      </view>
      
      <!-- 文本 -->
      <view class="menu-item-text">{{item.text}}</view>
      
      <!-- 徽章 -->
      <view class="menu-item-badge" wx:if="{{item.badge}}">
        <!-- 点徽章 -->
        <view 
          wx:if="{{item.badge.type === 'dot'}}" 
          class="badge-dot" 
          style="background-color: {{item.badge.color || '#ef4444'}};"
        ></view>
        
        <!-- 数字徽章 -->
        <view 
          wx:elif="{{item.badge.type === 'number'}}" 
          class="badge-number" 
          style="background-color: {{item.badge.color || '#ef4444'}};"
        >
          {{item.badge.value > 99 ? '99+' : item.badge.value}}
        </view>
        
        <!-- 文本徽章 -->
        <view 
          wx:elif="{{item.badge.type === 'text'}}" 
          class="badge-text" 
          style="background-color: {{item.badge.color || '#ef4444'}};"
        >
          {{item.badge.value}}
        </view>
      </view>
      
      <!-- 箭头 -->
      <view class="menu-item-arrow" wx:if="{{item.arrow !== false}}">
        <view class="arrow-icon"></view>
      </view>
    </view>
    
    <!-- 分组 -->
    <view wx:elif="{{item.type === 'group'}}">
      <!-- 分组标题 -->
      <view class="menu-group-title" wx:if="{{item.text}}">
        {{item.text}}
      </view>
      
      <!-- 分组内容 -->
      <view class="menu-group-content">
        <block wx:for="{{item.children}}" wx:key="id" wx:for-item="subItem">
          <view 
            class="menu-item {{subItem.disabled ? 'disabled' : ''}}"
            bindtap="handleItemClick"
            data-item="{{subItem}}"
            hover-class="menu-item-hover"
          >
            <!-- 图标 -->
            <view class="menu-item-icon" wx:if="{{subItem.icon}}">
              <image class="icon-image" src="{{subItem.icon}}" mode="aspectFit"></image>
            </view>
            
            <!-- 文本 -->
            <view class="menu-item-text">{{subItem.text}}</view>
            
            <!-- 徽章 -->
            <view class="menu-item-badge" wx:if="{{subItem.badge}}">
              <!-- 点徽章 -->
              <view 
                wx:if="{{subItem.badge.type === 'dot'}}" 
                class="badge-dot" 
                style="background-color: {{subItem.badge.color || '#ef4444'}};"
              ></view>
              
              <!-- 数字徽章 -->
              <view 
                wx:elif="{{subItem.badge.type === 'number'}}" 
                class="badge-number" 
                style="background-color: {{subItem.badge.color || '#ef4444'}};"
              >
                {{subItem.badge.value > 99 ? '99+' : subItem.badge.value}}
              </view>
              
              <!-- 文本徽章 -->
              <view 
                wx:elif="{{subItem.badge.type === 'text'}}" 
                class="badge-text" 
                style="background-color: {{subItem.badge.color || '#ef4444'}};"
              >
                {{subItem.badge.value}}
              </view>
            </view>
            
            <!-- 箭头 -->
            <view class="menu-item-arrow" wx:if="{{subItem.arrow !== false}}">
              <view class="arrow-icon"></view>
            </view>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 分割线 -->
    <view wx:elif="{{item.type === 'divider'}}" class="menu-divider"></view>
  </block>
</view>
