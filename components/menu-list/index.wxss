/* components/menu-list/index.wxss */
/* 菜单列表组件样式 */

/* 菜单列表 */
.menu-list {
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

/* 亮色主题 */
.menu-list-light {
  background-color: #ffffff;
  color: #1f2937;
}

/* 暗色主题 */
.menu-list-dark {
  background-color: #1f2937;
  color: #f9fafb;
}

/* 菜单项 */
.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
}

/* 菜单项悬停状态 */
.menu-item-hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.menu-list-dark .menu-item-hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 禁用菜单项 */
.menu-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 菜单项图标 */
.menu-item-icon {
  margin-right: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-image {
  width: 24px;
  height: 24px;
}

/* 菜单项文本 */
.menu-item-text {
  flex: 1;
  font-size: 16px;
}

.menu-list-dark .menu-item-text {
  color: #f9fafb;
}

/* 菜单项徽章 */
.menu-item-badge {
  margin-right: 8px;
}

/* 点徽章 */
.badge-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ef4444;
}

/* 数字徽章 */
.badge-number {
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: #ef4444;
  color: #ffffff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}

/* 文本徽章 */
.badge-text {
  height: 18px;
  border-radius: 9px;
  background-color: #ef4444;
  color: #ffffff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}

/* 菜单项箭头 */
.menu-item-arrow {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #9ca3af;
  border-right: 1px solid #9ca3af;
  transform: rotate(45deg);
}

.menu-list-dark .arrow-icon {
  border-color: #d1d5db;
}

/* 分组标题 */
.menu-group-title {
  padding: 8px 16px;
  font-size: 14px;
  color: #6b7280;
  background-color: #f9fafb;
}

.menu-list-dark .menu-group-title {
  color: #d1d5db;
  background-color: #111827;
}

/* 分组内容 */
.menu-group-content {
  
}

/* 分割线 */
.menu-divider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 0 16px;
}

.menu-list-dark .menu-divider {
  background-color: #374151;
}
