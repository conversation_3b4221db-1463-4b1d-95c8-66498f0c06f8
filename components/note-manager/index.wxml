<!-- components/note-manager/index.wxml -->
<view class="note-manager-container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">我的笔记</view>
    <view class="actions">
      <view wx:if="{{!isSelectMode}}" class="action-btn select-mode-btn" bindtap="toggleSelectMode">
        <text class="icon">☑</text>
        <text>批量操作</text>
      </view>
      <view wx:else class="action-btn exit-select-btn" bindtap="toggleSelectMode">
        <text class="icon">✕</text>
        <text>退出选择</text>
      </view>
      <view class="action-btn refresh-btn" bindtap="refreshList">
        <text class="icon">↻</text>
        <text>刷新</text>
      </view>
      <view class="action-btn create-btn" bindtap="createNote">
        <text class="icon">+</text>
        <text>新建</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索笔记" 
        value="{{searchKeyword}}"
        confirm-type="search"
        bindconfirm="handleSearch"
      />
      <text wx:if="{{searchKeyword}}" class="clear-icon" bindtap="clearSearch">✕</text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === 'all' ? 'active' : ''}}" 
      data-tab="all" 
      bindtap="switchTab"
    >全部笔记</view>
    <view 
      wx:if="{{showRecycleBin}}"
      class="tab {{activeTab === 'deleted' ? 'active' : ''}}" 
      data-tab="deleted" 
      bindtap="switchTab"
    >回收站</view>
  </view>

  <!-- 批量操作工具栏 -->
  <view class="batch-toolbar" wx:if="{{isSelectMode}}">
    <view class="selection-info">已选择 {{selectedNotes.length}} 篇</view>
    <view class="batch-actions">
      <view class="batch-btn select-all-btn" bindtap="toggleSelectAll">
        {{selectedNotes.length === notes.length ? '取消全选' : '全选'}}
      </view>
      <view wx:if="{{activeTab === 'all'}}" class="batch-btn delete-btn" bindtap="batchSoftDelete">批量删除</view>
      <view wx:else class="batch-btn restore-btn" bindtap="batchRestore">批量恢复</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{notes.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">{{activeTab === 'deleted' ? '回收站为空' : '暂无笔记'}}</text>
    <view wx:if="{{activeTab === 'all'}}" class="empty-action" bindtap="createNote">创建笔记</view>
  </view>

  <!-- 笔记列表 -->
  <view class="notes-container" wx:else>
    <view 
      class="note-item {{isSelectMode ? 'selectable' : ''}} {{selectedNotes.includes(item.id) ? 'selected' : ''}}"
      wx:for="{{notes}}" 
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="{{isSelectMode ? 'toggleSelectNote' : 'viewNoteDetail'}}"
    >
      <!-- 选择框 -->
      <view class="select-box" wx:if="{{isSelectMode}}">
        <view class="checkbox {{selectedNotes.includes(item.id) ? 'checked' : ''}}">
          <text wx:if="{{selectedNotes.includes(item.id)}}" class="check-icon">✓</text>
        </view>
      </view>

      <!-- 笔记信息 -->
      <view class="note-info">
        <view class="note-title">{{item.title}}</view>
        <view class="note-content">{{item.content}}</view>
        <view class="note-meta">
          <text class="note-time">{{activeTab === 'deleted' ? '删除时间: ' + item.deletedAt : '更新时间: ' + item.updatedAt}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="note-actions" wx:if="{{!isSelectMode}}" catchtap>
        <view wx:if="{{activeTab === 'all'}}" class="action-btn edit-btn" data-id="{{item.id}}" catchtap="editNote">编辑</view>
        <view wx:if="{{activeTab === 'all'}}" class="action-btn delete-btn" data-id="{{item.id}}" catchtap="softDeleteNote">删除</view>
        <view wx:else class="action-btn restore-btn" data-id="{{item.id}}" catchtap="restoreNote">恢复</view>
      </view>
    </view>
  </view>

  <!-- 分页控制 -->
  <view class="pagination" wx:if="{{notes.length > 0}}">
    <view class="page-btn prev {{pagination.page <= 1 ? 'disabled' : ''}}" bindtap="handlePrevPage">上一页</view>
    <view class="page-info">{{pagination.page}} / {{Math.ceil(pagination.total / pagination.pageSize)}}</view>
    <view class="page-btn next {{pagination.page * pagination.pageSize >= pagination.total ? 'disabled' : ''}}" bindtap="handleNextPage">下一页</view>
  </view>
</view>
