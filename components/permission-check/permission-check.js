// components/permission-check/permission-check.js

// 导入权限服务
const permissionService = require('../../utils/permission-service');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 需要的权限，可以是单个权限或权限数组
    permission: {
      type: null,
      value: null
    },
    // 需要的角色，可以是单个角色或角色数组
    role: {
      type: null,
      value: null
    },
    // 是否需要满足所有权限/角色，默认为false（只需满足一个）
    requireAll: {
      type: Boolean,
      value: false
    },
    // 是否在权限检查期间显示加载状态
    showLoading: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    hasPermission: false,
    isChecking: true
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      this.checkPermission();
    },
    detached() {
      // 组件销毁时的操作
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 检查权限
     */
    async checkPermission() {
      // 设置检查中状态
      this.setData({ isChecking: true });

      try {
        let hasPermission = false;

        // 如果指定了权限，检查权限
        if (this.properties.permission) {
          hasPermission = await permissionService.hasPermission(
            this.properties.permission,
            this.properties.requireAll
          );
        } else if (this.properties.role) { // 如果指定了角色，检查角色
          hasPermission = await permissionService.hasRole(
            this.properties.role,
            this.properties.requireAll
          );
        } else { // 如果既没有指定权限也没有指定角色，默认为有权限
          hasPermission = true;
        }

        // 更新权限状态
        this.setData({
          hasPermission,
          isChecking: false
        });
      } catch (error) {
        console.error('权限检查失败:', error);
        // 出错时默认无权限
        this.setData({
          hasPermission: false,
          isChecking: false
        });
      }
    },

    /**
     * 重新检查权限
     */
    refresh() {
      // 清除权限缓存
      permissionService.clearCache();
      // 重新检查权限
      this.checkPermission();
    }
  }
});
