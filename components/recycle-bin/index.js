/**
 * 回收站组件
 * 用于展示和管理已删除的内容
 */
Component({
  properties: {
    type: {
      type: String,
      value: 'note' // 'note', 'insight', 'exercise', 'tag', 'theme', 'dailyContent', 'learningPlan'
    },
    planId: {
      type: Number,
      value: null // 仅当type为'dailyContent'时需要
    }
  },

  data: {
    items: [],
    isLoading: false,
    isSelectMode: false,
    selectedItems: [],
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    emptyText: '回收站为空',
    typeDisplayName: '内容'
  },

  lifetimes: {
    attached() {
      // 设置类型显示名称
      this.setTypeDisplayName();
      // 加载已删除内容
      this.loadDeletedItems();
    }
  },

  methods: {
    // 设置类型显示名称
    setTypeDisplayName() {
      const typeMap = {
        note: '笔记',
        insight: '观点',
        exercise: '练习',
        tag: '标签',
        theme: '主题',
        dailyContent: '每日内容',
        learningPlan: '学习计划'
      };

      this.setData({
        typeDisplayName: typeMap[this.properties.type] || '内容',
        emptyText: `${typeMap[this.properties.type] || '内容'}回收站为空`
      });
    },

    // 加载已删除内容
    loadDeletedItems() {
      this.setData({ isLoading: true });

      const { page, pageSize } = this.data.pagination;
      const params = { page, pageSize };

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[recycle-bin] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '加载失败', icon: 'none' });
        return;
      }

      let promise;

      // 根据type调用不同API
      switch (this.properties.type) {
        case 'note':
          promise = api.note.getDeletedNotes(params);
          break;
        case 'insight':
          promise = api.insight.getDeletedInsights(params);
          break;
        case 'exercise':
          promise = api.exercise.getDeletedExercises(params);
          break;
        case 'tag':
          promise = api.tag.getDeletedTags(params);
          break;
        case 'theme':
          promise = api.theme.getDeletedThemes(params);
          break;
        case 'dailyContent':
          if (!this.properties.planId) {
            wx.showToast({ title: '缺少计划ID', icon: 'none' });
            this.setData({ isLoading: false });
            return;
          }
          promise = api.dailyContent.getDeletedContents(this.properties.planId, params);
          break;
        case 'learningPlan':
          promise = api.learningPlan.getDeletedPlans(params);
          break;
        default:
          wx.showToast({ title: '不支持的内容类型', icon: 'none' });
          this.setData({ isLoading: false });
          return;
      }

      promise.then(result => {
        this.setData({
          items: result.data,
          'pagination.total': result.total || 0,
          isLoading: false
        });
      }).catch(err => {
        console.error('加载已删除内容失败:', err);
        wx.showToast({ title: '加载失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 恢复内容
    handleRestore(e) {
      const itemId = e.currentTarget.dataset.id;
      if (!itemId) return;

      wx.showModal({
        title: '恢复确认',
        content: `确定要恢复此${this.data.typeDisplayName}吗？`,
        success: res => {
          if (res.confirm) {
            this.restoreItem(itemId);
          }
        }
      });
    },

    // 执行恢复操作
    restoreItem(itemId) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[recycle-bin] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '恢复失败', icon: 'none' });
        return;
      }

      let promise;

      // 根据type调用不同API
      switch (this.properties.type) {
        case 'note':
          promise = api.note.restoreNote(itemId);
          break;
        case 'insight':
          promise = api.insight.restoreInsight(itemId);
          break;
        case 'exercise':
          promise = api.exercise.restoreExercise(itemId);
          break;
        case 'tag':
          promise = api.tag.restoreTag(itemId);
          break;
        case 'theme':
          promise = api.theme.restoreTheme(itemId);
          break;
        case 'dailyContent':
          promise = api.dailyContent.restoreContent(itemId);
          break;
        case 'learningPlan':
          promise = api.learningPlan.restorePlan(itemId);
          break;
        default:
          wx.showToast({ title: '不支持的内容类型', icon: 'none' });
          this.setData({ isLoading: false });
          return;
      }

      promise.then(() => {
        wx.showToast({ title: '恢复成功', icon: 'success' });
        // 重新加载列表
        this.loadDeletedItems();
        // 触发恢复事件
        this.triggerEvent('restore', { id: itemId });
      }).catch(err => {
        console.error('恢复内容失败:', err);
        wx.showToast({ title: '恢复失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 永久删除
    handlePermanentDelete(e) {
      const itemId = e.currentTarget.dataset.id;
      if (!itemId) return;

      wx.showModal({
        title: '删除确认',
        content: `此操作将永久删除${this.data.typeDisplayName}，无法恢复，确定继续吗？`,
        confirmColor: '#ff0000',
        success: res => {
          if (res.confirm) {
            this.permanentDeleteItem(itemId);
          }
        }
      });
    },

    // 执行永久删除操作
    permanentDeleteItem(itemId) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[recycle-bin] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '永久删除失败', icon: 'none' });
        return;
      }

      // 调用永久删除API
      api.cleanup.permanentDelete(this.properties.type, itemId)
        .then(() => {
          wx.showToast({ title: '永久删除成功', icon: 'success' });
          // 重新加载列表
          this.loadDeletedItems();
          // 触发永久删除事件
          this.triggerEvent('permanentDelete', { id: itemId });
        })
        .catch(err => {
          console.error('永久删除失败:', err);
          wx.showToast({ title: '永久删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    },

    // 切换选择模式
    toggleSelectMode() {
      this.setData({
        isSelectMode: !this.data.isSelectMode,
        selectedItems: []
      });
    },

    // 选择/取消选择项目
    toggleSelectItem(e) {
      if (!this.data.isSelectMode) return;

      const itemId = e.currentTarget.dataset.id;
      if (!itemId) return;

      const selectedItems = [...this.data.selectedItems];
      const index = selectedItems.indexOf(itemId);

      if (index === -1) {
        selectedItems.push(itemId);
      } else {
        selectedItems.splice(index, 1);
      }

      this.setData({ selectedItems });
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (this.data.selectedItems.length === this.data.items.length) {
        // 取消全选
        this.setData({ selectedItems: [] });
      } else {
        // 全选
        const selectedItems = this.data.items.map(item => item.id);
        this.setData({ selectedItems });
      }
    },

    // 批量恢复
    handleBatchRestore() {
      const { selectedItems } = this.data;
      if (selectedItems.length === 0) {
        wx.showToast({ title: '请先选择内容', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '批量恢复确认',
        content: `确定要恢复选中的${selectedItems.length}项${this.data.typeDisplayName}吗？`,
        success: res => {
          if (res.confirm) {
            this.batchRestoreItems(selectedItems);
          }
        }
      });
    },

    // 执行批量恢复
    batchRestoreItems(ids) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[recycle-bin] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '批量恢复失败', icon: 'none' });
        return;
      }

      let promise;

      // 根据type调用不同API
      switch (this.properties.type) {
        case 'note':
          promise = api.note.batchRestoreNotes(ids);
          break;
        case 'insight':
          promise = api.insight.batchRestoreInsights(ids);
          break;
        case 'exercise':
          promise = api.exercise.batchRestoreExercises(ids);
          break;
        case 'tag':
          promise = api.tag.batchRestoreTags(ids);
          break;
        case 'theme':
          promise = api.theme.batchRestoreThemes(ids);
          break;
        case 'dailyContent':
          promise = api.dailyContent.batchRestoreContents(ids);
          break;
        case 'learningPlan':
          promise = api.learningPlan.batchRestorePlans(ids);
          break;
        default:
          wx.showToast({ title: '不支持的内容类型', icon: 'none' });
          this.setData({ isLoading: false });
          return;
      }

      promise.then(result => {
        wx.showToast({
          title: `已恢复${result.successCount || ids.length}项`,
          icon: 'success'
        });

        // 重新加载列表
        this.loadDeletedItems();

        // 退出选择模式
        this.setData({
          isSelectMode: false,
          selectedItems: []
        });

        // 触发批量恢复事件
        this.triggerEvent('batchRestore', { ids });
      }).catch(err => {
        console.error('批量恢复失败:', err);
        wx.showToast({ title: '批量恢复失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 批量永久删除
    handleBatchPermanentDelete() {
      const { selectedItems } = this.data;
      if (selectedItems.length === 0) {
        wx.showToast({ title: '请先选择内容', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '批量永久删除确认',
        content: `此操作将永久删除选中的${selectedItems.length}项${this.data.typeDisplayName}，无法恢复，确定继续吗？`,
        confirmColor: '#ff0000',
        success: res => {
          if (res.confirm) {
            this.batchPermanentDeleteItems(selectedItems);
          }
        }
      });
    },

    // 执行批量永久删除
    batchPermanentDeleteItems(ids) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[recycle-bin] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '批量永久删除失败', icon: 'none' });
        return;
      }

      // 调用批量永久删除API
      api.cleanup.batchPermanentDelete(this.properties.type, ids)
        .then(result => {
          wx.showToast({
            title: `已永久删除${result.deletedCount || ids.length}项`,
            icon: 'success'
          });

          // 重新加载列表
          this.loadDeletedItems();

          // 退出选择模式
          this.setData({
            isSelectMode: false,
            selectedItems: []
          });

          // 触发批量永久删除事件
          this.triggerEvent('batchPermanentDelete', { ids });
        })
        .catch(err => {
          console.error('批量永久删除失败:', err);
          wx.showToast({ title: '批量永久删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    },

    // 分页处理 - 上一页
    handlePrevPage() {
      if (this.data.pagination.page <= 1) return;

      this.setData({
        'pagination.page': this.data.pagination.page - 1
      }, () => {
        this.loadDeletedItems();
      });
    },

    // 分页处理 - 下一页
    handleNextPage() {
      const { page, pageSize, total } = this.data.pagination;
      if (page * pageSize >= total) return;

      this.setData({
        'pagination.page': page + 1
      }, () => {
        this.loadDeletedItems();
      });
    },

    // 刷新列表
    refreshList() {
      this.setData({
        'pagination.page': 1,
        selectedItems: [],
        isSelectMode: false
      }, () => {
        this.loadDeletedItems();
      });
    }
  }
});
