<!-- components/recycle-bin/index.wxml -->
<view class="recycle-bin-container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">{{typeDisplayName}}回收站</view>
    <view class="actions">
      <view wx:if="{{!isSelectMode}}" class="action-btn select-mode-btn" bindtap="toggleSelectMode">
        <text class="icon">☑</text>
        <text>批量操作</text>
      </view>
      <view wx:else class="action-btn exit-select-btn" bindtap="toggleSelectMode">
        <text class="icon">✕</text>
        <text>退出选择</text>
      </view>
      <view class="action-btn refresh-btn" bindtap="refreshList">
        <text class="icon">↻</text>
        <text>刷新</text>
      </view>
    </view>
  </view>

  <!-- 批量操作工具栏 -->
  <view class="batch-toolbar" wx:if="{{isSelectMode}}">
    <view class="selection-info">已选择 {{selectedItems.length}} 项</view>
    <view class="batch-actions">
      <view class="batch-btn select-all-btn" bindtap="toggleSelectAll">
        {{selectedItems.length === items.length ? '取消全选' : '全选'}}
      </view>
      <view class="batch-btn restore-btn" bindtap="handleBatchRestore">批量恢复</view>
      <view class="batch-btn delete-btn" bindtap="handleBatchPermanentDelete">批量删除</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{items.length === 0}}">
    <view class="empty-icon">🗑️</view>
    <text class="empty-text">{{emptyText}}</text>
  </view>

  <!-- 内容列表 -->
  <view class="items-container" wx:else>
    <view 
      class="item {{isSelectMode ? 'selectable' : ''}} {{selectedItems.includes(item.id) ? 'selected' : ''}}"
      wx:for="{{items}}" 
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="{{isSelectMode ? 'toggleSelectItem' : ''}}"
    >
      <!-- 选择框 -->
      <view class="select-box" wx:if="{{isSelectMode}}">
        <view class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}">
          <text wx:if="{{selectedItems.includes(item.id)}}" class="check-icon">✓</text>
        </view>
      </view>

      <!-- 内容信息 -->
      <view class="item-info">
        <view class="item-title">{{item.title}}</view>
        <view class="item-meta">
          <text class="delete-time">删除时间: {{item.deletedAt}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="item-actions" wx:if="{{!isSelectMode}}" catchtap>
        <view class="action-btn restore-btn" data-id="{{item.id}}" catchtap="handleRestore">恢复</view>
        <view class="action-btn delete-btn" data-id="{{item.id}}" catchtap="handlePermanentDelete">删除</view>
      </view>
    </view>
  </view>

  <!-- 分页控制 -->
  <view class="pagination" wx:if="{{items.length > 0}}">
    <view class="page-btn prev {{pagination.page <= 1 ? 'disabled' : ''}}" bindtap="handlePrevPage">上一页</view>
    <view class="page-info">{{pagination.page}} / {{Math.ceil(pagination.total / pagination.pageSize)}}</view>
    <view class="page-btn next {{pagination.page * pagination.pageSize >= pagination.total ? 'disabled' : ''}}" bindtap="handleNextPage">下一页</view>
  </view>
</view>
