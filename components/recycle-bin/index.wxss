/* components/recycle-bin/index.wxss */
.recycle-bin-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 标题栏样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.action-btn .icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.select-mode-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.exit-select-btn {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}

.refresh-btn {
  color: #50e3c2;
  background-color: rgba(80, 227, 194, 0.1);
}

/* 批量操作工具栏样式 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f0f0f0;
  border-bottom: 1rpx solid #dddddd;
}

.selection-info {
  font-size: 28rpx;
  color: #666666;
}

.batch-actions {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.select-all-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.restore-btn {
  color: #50e3c2;
  background-color: rgba(80, 227, 194, 0.1);
}

.delete-btn {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 空状态样式 */
.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
}

/* 内容列表样式 */
.items-container {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

.item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item.selectable {
  cursor: pointer;
}

.item.selected {
  background-color: rgba(74, 144, 226, 0.05);
  border: 1rpx solid #4a90e2;
}

.select-box {
  margin-right: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #dddddd;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox.checked {
  background-color: #4a90e2;
  border-color: #4a90e2;
}

.check-icon {
  color: #ffffff;
  font-size: 28rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.item-meta {
  font-size: 24rpx;
  color: #999999;
}

.delete-time {
  margin-right: 20rpx;
}

.item-actions {
  display: flex;
  gap: 10rpx;
}

.item-actions .action-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
}

/* 分页控制样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
}

.page-btn {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 8rpx;
}

.page-btn.disabled {
  color: #cccccc;
  background-color: #f0f0f0;
  cursor: not-allowed;
}

.page-info {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #666666;
}
