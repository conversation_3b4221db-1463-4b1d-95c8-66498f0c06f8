// components/star-canvas/index.js
// 星星画布组件

// 导入基础画布组件
const CanvasBase = require('../canvas-base/index');

/**
 * 星星画布组件
 * 继承自基础画布组件，实现星星特有的功能
 */
class StarCanvas extends CanvasBase {
  /**
   * 构造函数
   */
  constructor() {
    super();

    // 星星特有配置
    this.starConfig = {
      points: 5, // 星星角数
      innerRadiusRatio: 0.6, // 内半径比例
      cornerRadius: 15, // 角的圆角半径
      twinkleAmplitude: 0.5, // 闪烁幅度
      twinkleFrequency: 0.002, // 闪烁频率
      textSizeRatio: 0.25, // 文字大小与星星半径的比例
      hoverScale: 1.15 // 悬停时的缩放比例
    };

    // 背景相关
    this.backgroundStars = []; // 背景星星数组
    this.backgroundGradient = null; // 背景渐变
  }

  /**
   * 初始化画布
   * @param {Object} options - 初始化选项
   * @returns {Promise<boolean>} 是否初始化成功
   */
  init(options) {
    return super.init(options)
      .then(success => {
        if (success) {
          // 初始化背景星星
          this._initBackgroundStars(100);
          return true;
        }
        return false;
      });
  }

  /**
   * 设置星星特有配置
   * @param {Object} starConfig - 星星配置
   */
  setStarConfig(starConfig) {
    this.starConfig = { ...this.starConfig, ...starConfig };
  }

  /**
   * 获取星星元素
   * @returns {Array} 星星元素数组
   */
  getStars() {
    return this.elements;
  }

  /**
   * 添加星星
   * @param {Object} starData - 星星数据
   * @returns {Object} 新创建的星星
   */
  addStar(starData) {
    const star = {
      id: `star-${this.elements.length}`,
      text: starData.text || '新星星',
      color: starData.color || this.getRandomColor(),
      x: starData.x || this.canvasWidth / 2,
      y: starData.y || this.canvasHeight / 2,
      radius: starData.radius || 50,
      velocityX: (Math.random() - 0.5) * this.config.baseSpeed,
      velocityY: (Math.random() - 0.5) * this.config.baseSpeed,
      isHovered: false,
      isClicked: false,
      pulsePhase: Math.random() * Math.PI * 2,
      pulseSpeed: 0.001 + Math.random() * 0.002,
      rotationAngle: 0,
      rotationSpeed: 0,
      twinklePhase: Math.random() * Math.PI * 2,
      twinkleSpeed: 0.002 + Math.random() * 0.002,
      lastX: starData.x || this.canvasWidth / 2,
      lastY: starData.y || this.canvasHeight / 2,
      stuckFrames: 0,
      cornerRadius: Math.random() * 10 + 15,
      collisionCount: 0,
      isAccelerated: false,
      originalVelocityX: 0,
      originalVelocityY: 0,
      isDragged: false
    };

    this.elements.push(star);
    return star;
  }

  /**
   * 移除星星
   * @param {string} id - 星星ID
   * @returns {boolean} 是否成功移除
   */
  removeStar(id) {
    const index = this.elements.findIndex(star => star.id === id);
    if (index !== -1) {
      this.elements.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 设置背景星星数量
   * @param {number} count - 背景星星数量
   */
  setBackgroundStarsCount(count) {
    this._initBackgroundStars(count);
  }

  /**
   * 获取随机颜色
   * @returns {string} 颜色值
   */
  getRandomColor() {
    // 获取随机的柔和色彩
    const hue = Math.floor(Math.random() * 360);
    return `hsl(${hue}, 100%, 75%)`;
  }

  /**
   * 保存当前状态
   * @returns {Object} 保存的状态
   */
  saveState() {
    const state = {
      elements: this.elements.map(star => ({
        id: star.id,
        x: star.x,
        y: star.y,
        velocityX: star.velocityX,
        velocityY: star.velocityY,
        radius: star.radius,
        pulsePhase: star.pulsePhase,
        twinklePhase: star.twinklePhase,
        isClicked: star.isClicked,
        isDragged: star.isDragged,
        isHovered: star.isHovered
      })),
      timestamp: Date.now()
    };

    return state;
  }

  /**
   * 恢复保存的状态
   * @param {Object} state - 保存的状态
   */
  restoreState(state) {
    if (!state || !state.elements || !Array.isArray(state.elements)) {
      console.warn('无效的状态数据，无法恢复');
      return;
    }

    // 检查状态是否过期（超过5秒）
    const now = Date.now();
    if (now - state.timestamp > 5000) {
      console.log('状态数据已过期，不恢复位置');
      return;
    }

    // 恢复元素状态
    for (let i = 0; i < this.elements.length; i++) {
      const element = this.elements[i];
      const savedElement = state.elements.find(e => e.id === element.id);

      if (savedElement) {
        // 恢复位置和速度
        element.x = savedElement.x;
        element.y = savedElement.y;
        element.velocityX = savedElement.velocityX;
        element.velocityY = savedElement.velocityY;
        element.pulsePhase = savedElement.pulsePhase;
        element.twinklePhase = savedElement.twinklePhase;

        // 恢复交互状态
        element.isClicked = savedElement.isClicked;
        element.isDragged = savedElement.isDragged;
        element.isHovered = savedElement.isHovered;
      }
    }

    // 更新时间戳
    this.lastTimestamp = Date.now();
  }

  /**
   * 创建星星元素
   * @param {Array} themes - 主题数据
   * @returns {Array} 星星元素数组
   * @protected
   */
  _createElements(themes) {
    const stars = [];
    const count = themes.length;

    if (count === 0) {
      console.warn('主题数据为空，无法创建星星');
      return stars;
    }

    // 计算底部菜单的上边界位置和顶部导航的下边界位置
    const tabbarTop = this.canvasHeight; // 底部菜单上边界设为画布底部
    const navBottom = 0; // 顶部导航下边界

    // 为了确保星星均匀分布，使用网格布局
    const gridColumns = 3; // 列数
    const gridRows = Math.ceil(count / gridColumns); // 行数

    const cellWidth = this.canvasWidth / gridColumns;
    const cellHeight = (tabbarTop - navBottom) / gridRows;

    for (let i = 0; i < count; i++) {
      const theme = themes[i % themes.length];

      // 基础半径范围为40-60
      let baseRadius = 40 + Math.random() * 20;

      // 根据文字长度动态调整星星大小
      const textLength = theme.name.length;
      if (textLength > 3) {
        // 当文字长度超过3个字符时，增加星星半径
        // 每多一个字符，增加基础半径的5%
        baseRadius += baseRadius * (textLength - 3) * 0.05;

        // 如果文字需要换行显示，额外增加星星大小以提供足够的垂直空间
        if (textLength >= 4) {
          const lineCount = Math.ceil(textLength / 2);
          // 当有多行时，额外增加半径
          if (lineCount > 1) {
            baseRadius += baseRadius * 0.1 * (lineCount - 1);
          }
        }
      }

      const radius = baseRadius; // 使用调整后的半径

      // 计算在网格中的位置
      const col = i % gridColumns;
      const row = Math.floor(i / gridColumns);

      // 计算星星的初始位置，偏移网格中心位置以获得随机效果
      const offsetX = (Math.random() - 0.5) * cellWidth * 0.6;
      const offsetY = (Math.random() - 0.5) * cellHeight * 0.6;

      const x = cellWidth * (col + 0.5) + offsetX;
      const y = navBottom + cellHeight * (row + 0.5) + offsetY;

      // 获取随机的柔和色彩
      const hue = Math.floor(Math.random() * 360);
      const pastelColor = theme.color || `hsl(${hue}, 100%, 75%)`;

      stars.push({
        id: `star-${i}`,
        text: theme.name, // 只保留主标签
        color: pastelColor,
        x,
        y,
        radius,
        velocityX: (Math.random() - 0.5) * this.config.baseSpeed,
        velocityY: (Math.random() - 0.5) * this.config.baseSpeed,
        isHovered: false,
        isClicked: false,
        pulsePhase: Math.random() * Math.PI * 2,
        pulseSpeed: 0.001 + Math.random() * 0.002,
        rotationAngle: 0, // 固定旋转角度为0
        rotationSpeed: 0, // 旋转速度设为0，取消旋转
        twinklePhase: Math.random() * Math.PI * 2, // 闪烁相位
        twinkleSpeed: 0.002 + Math.random() * 0.002, // 闪烁速度
        lastX: x,
        lastY: y,
        stuckFrames: 0,
        cornerRadius: Math.random() * 10 + 15, // 圆角半径，更大的圆角
        // 相同的其他属性
        collisionCount: 0,
        isAccelerated: false,
        originalVelocityX: 0,
        originalVelocityY: 0,
        isDragged: false
      });
    }

    return stars;
  }

  /**
   * 更新单个星星
   * @param {Object} star - 星星对象
   * @param {number} deltaTime - 时间增量
   * @protected
   */
  _updateElement(star, deltaTime) {
    // 如果星星被点击或正在被拖动，则不更新位置
    if (star.isClicked || star.isDragged) return;

    // 应用速度
    star.x += star.velocityX * deltaTime * this.config.animationSpeedMultiplier;
    star.y += star.velocityY * deltaTime * this.config.animationSpeedMultiplier;

    // 脉动效果
    star.pulsePhase += star.pulseSpeed * deltaTime;
    if (star.pulsePhase > Math.PI * 2) {
      star.pulsePhase -= Math.PI * 2;
    }

    // 闪烁效果
    star.twinklePhase += star.twinkleSpeed * deltaTime;
    if (star.twinklePhase > Math.PI * 2) {
      star.twinklePhase -= Math.PI * 2;
    }

    // 检测星星是否卡住
    if (Math.abs(star.x - star.lastX) < 0.1 && Math.abs(star.y - star.lastY) < 0.1) {
      star.stuckFrames++;
      if (star.stuckFrames > 5) {
        // 随机改变速度方向，防止卡住
        const angle = Math.random() * Math.PI * 2;
        const speed = this.config.baseSpeed * (0.5 + Math.random() * 0.5);
        star.velocityX = Math.cos(angle) * speed;
        star.velocityY = Math.sin(angle) * speed;
        star.stuckFrames = 0;
      }
    } else {
      star.stuckFrames = 0;
    }

    // 随机添加一些微小的加速度变化，使运动更自然
    if (Math.random() < 0.02) {
      star.velocityX += (Math.random() - 0.5) * 0.01;
      star.velocityY += (Math.random() - 0.5) * 0.01;
    }

    // 限制最大速度
    const maxSpeed = this.config.baseSpeed * 1.5;
    const currentSpeed = Math.sqrt(star.velocityX * star.velocityX + star.velocityY * star.velocityY);
    if (currentSpeed > maxSpeed) {
      star.velocityX = (star.velocityX / currentSpeed) * maxSpeed;
      star.velocityY = (star.velocityY / currentSpeed) * maxSpeed;
    }

    // 记录上一帧位置
    star.lastX = star.x;
    star.lastY = star.y;
  }

  /**
   * 绘制元素
   * @private
   */
  _drawElements() {
    try {
      // 清空画布
      this._clearCanvas();

      // 绘制星空背景
      try {
        this._drawStarryBackground();
      } catch (bgErr) {
        console.error('绘制星空背景时出错', bgErr);
        // 背景绘制失败不影响星星的绘制
      }

      // 创建一个渲染队列，让交互中的星星在最上层
      const normalStars = [];
      const hoveredStars = [];
      const draggedStars = [];
      const clickedStars = [];

      // 将星星分配到不同的渲染队列
      for (let i = 0; i < this.elements.length; i++) {
        try {
          const star = this.elements[i];

          if (!star) continue; // 跳过无效星星

          if (star.isClicked) {
            clickedStars.push(star);
          } else if (star.isDragged) {
            draggedStars.push(star);
          } else if (star.isHovered) {
            hoveredStars.push(star);
          } else {
            normalStars.push(star);
          }
        } catch (starErr) {
          console.error('处理星星状态时出错', starErr);
          // 单个星星处理错误不影响其他星星
          continue;
        }
      }

      // 按顺序渲染：普通 -> 悬停 -> 拖动 -> 点击
      try {
        this._renderStarsList(normalStars);
        this._renderStarsList(hoveredStars);
        this._renderStarsList(draggedStars);
        this._renderStarsList(clickedStars);
      } catch (renderErr) {
        console.error('渲染星星列表时出错', renderErr);
        // 出错时尝试简单渲染所有星星
        this._renderAllStarsSimple();
      }
    } catch (err) {
      console.error('绘制元素时出错', err);
      // 错误时不重置整个渲染循环，尝试简单渲染
      this._renderAllStarsSimple();
    }
  }

  /**
   * 渲染星星列表
   * @param {Array} starsList - 要渲染的星星列表
   * @private
   */
  _renderStarsList(starsList) {
    // 按照大小排序，小的星星先绘制
    starsList.sort((a, b) => a.radius - b.radius);

    // 渲染每个星星
    for (let i = 0; i < starsList.length; i++) {
      this._drawElement(starsList[i]);
    }
  }

  /**
   * 绘制单个星星
   * @param {Object} star - 星星对象
   * @protected
   */
  _drawElement(star) {
    const ctx = this.ctx;
    if (!ctx) return;

    // 呼吸效果 - 脉动半径变化
    const pulseScale = 1 + Math.sin(star.pulsePhase) * 0.03;
    const scaleMultiplier = star.isHovered ? this.starConfig.hoverScale : pulseScale;
    const actualRadius = star.radius * scaleMultiplier;

    // 关闭闪烁效果 - 使用固定透明度
    // const twinkleValue = Math.abs(Math.sin(star.twinklePhase));
    // const opacity = 0.7 + twinkleValue * 0.3; // 0.7-1.0之间闪烁
    const opacity = 1.0; // 固定为完全不透明

    // 保存当前状态
    ctx.save();

    // 设置透明度
    ctx.globalAlpha = opacity;

    // 绘制星星形状
    this._drawStarShape(
      ctx,
      star.x,
      star.y,
      actualRadius,
      this.starConfig.points,
      this.starConfig.innerRadiusRatio,
      star.rotationAngle,
      star.color,
      star.isClicked,
      star.isDragged
    );

    // 绘制星星文字
    this._drawStarText(ctx, star, actualRadius);

    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 绘制星星形状
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} outerRadius - 外半径
   * @param {number} points - 星星角数
   * @param {number} innerRadiusRatio - 内半径比例
   * @param {number} rotation - 旋转角度
   * @param {string} color - 颜色
   * @param {boolean} isClicked - 是否被点击
   * @param {boolean} isDragged - 是否被拖动
   * @private
   */
  _drawStarShape(ctx, x, y, outerRadius, points, innerRadiusRatio, rotation, color, isClicked, isDragged) {
    try {
      // 参数验证
      if (!ctx || typeof outerRadius !== 'number' || outerRadius <= 0) {
        console.error('drawStar: 参数无效', { ctx, outerRadius });
        return;
      }

      // 计算内半径
      const innerRadius = outerRadius * innerRadiusRatio;

      // 保存上下文状态
      ctx.save();

      // 移动到中心点
      ctx.translate(x, y);

      // 应用旋转
      if (rotation) {
        ctx.rotate(rotation);
      }

      // 开始绘制路径
      ctx.beginPath();

      // 计算星星顶点
      const vertices = [];
      const angleIncrement = (Math.PI * 2) / (points * 2);

      for (let i = 0; i < points * 2; i++) {
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const angle = i * angleIncrement - Math.PI / 2; // 从上方开始
        vertices.push({
          x: radius * Math.cos(angle),
          y: radius * Math.sin(angle)
        });
      }

      // 绘制带圆角的星星路径
      const cornerRadius = Math.min(this.starConfig.cornerRadius, outerRadius * 0.2); // 圆角不超过半径的20%

      for (let i = 0; i < points * 2; i++) {
        const p1 = vertices[i];
        const p2 = vertices[(i + 1) % (points * 2)];
        const p3 = vertices[(i + 2) % (points * 2)];

        // 计算每条边的长度
        const edge1Length = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
        const edge2Length = Math.sqrt(Math.pow(p2.x - p3.x, 2) + Math.pow(p2.y - p3.y, 2));

        // 圆角距离不超过边长的一半
        const d1 = Math.min(cornerRadius, edge1Length / 2.5);
        const d2 = Math.min(cornerRadius, edge2Length / 2.5);

        // 防止除0错误
        if (edge1Length < 0.001 || edge2Length < 0.001) {
          if (i === 0) {
            ctx.moveTo(p1.x, p1.y);
          } else {
            ctx.lineTo(p1.x, p1.y);
          }
          continue;
        }

        // 计算方向向量
        const dir1 = {
          x: (p2.x - p1.x) / edge1Length,
          y: (p2.y - p1.y) / edge1Length
        };
        const dir2 = {
          x: (p2.x - p3.x) / edge2Length,
          y: (p2.y - p3.y) / edge2Length
        };

        // 计算控制点
        const cp1 = {
          x: p2.x - dir1.x * d1,
          y: p2.y - dir1.y * d1
        };
        const cp2 = {
          x: p2.x - dir2.x * d2,
          y: p2.y - dir2.y * d2
        };

        // 绘制线段和曲线
        if (i === 0) {
          ctx.moveTo(p1.x, p1.y);
        }

        ctx.lineTo(cp1.x, cp1.y);
        ctx.quadraticCurveTo(p2.x, p2.y, cp2.x, cp2.y);
      }

      // 闭合路径
      ctx.closePath();

      // 创建渐变填充
      try {
        // 创建径向渐变 - 从左上角偏移，模拟光照效果
        const gradient = ctx.createRadialGradient(
          -outerRadius * 0.3, -outerRadius * 0.3, 0, // 内部更亮的点，从左上角偏移
          0, 0, outerRadius // 外部
        );

        // 设置渐变颜色 - 使用更多的颜色停靠点，增强光照效果
        const baseColor = color || '#4f46e5'; // 默认颜色，避免空值
        const lighterColor = this.lightenColor(baseColor, 40); // 更亮
        const mediumColor = this.lightenColor(baseColor, 15); // 中间亮度
        const darkerColor = this.darkenColor(baseColor, 10); // 更暗

        gradient.addColorStop(0, lighterColor); // 中心最亮
        gradient.addColorStop(0.4, mediumColor); // 中间过渡色
        gradient.addColorStop(0.7, baseColor); // 基础颜色
        gradient.addColorStop(1, darkerColor); // 边缘较暗

        // 设置阴影
        ctx.shadowColor = this.hexToRgba(baseColor, 0.6);
        ctx.shadowBlur = this.config.shadowBlur + 2;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 填充星星
        ctx.fillStyle = gradient;
        ctx.fill();

        // 如果星星被点击，添加高亮边框
        if (isClicked) {
          ctx.strokeStyle = '#FFFFFF';
          ctx.lineWidth = 2;
          ctx.stroke();
        }

        // 星星拖动效果已暂时移除
        // 如果星星正在被拖动，不绘制额外的效果
        // if (isDragged) {
        //   this._drawStarGlowEffects(ctx, outerRadius, baseColor);
        // }
      } catch (err) {
        console.error('创建渐变或填充时出错', err);
        // 出错时使用简单填充
        ctx.fillStyle = color || '#4f46e5';
        ctx.fill();
      }

      // 恢复上下文状态
      ctx.restore();
    } catch (err) {
      console.error('绘制星星时出错', err);
      // 错误恢复：绘制一个简单的圆形代替
      this._drawFallbackStar(ctx, x, y, outerRadius, color);
    }
  }

  /**
   * 绘制星星光环效果
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} radius - 星星半径
   * @param {string} color - 基础颜色
   * @private
   */
  _drawStarGlowEffects(ctx, radius, color) {
    try {
      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;

      // 计算脉动效果
      const now = Date.now();
      const pulseValue = Math.sin(now * 0.005) * 0.2 + 0.8; // 0.6-1.0之间脉动

      // 创建外部光圈渐变
      const outerGlowRadius = radius * (1.05 + pulseValue * 0.14); // 光圈大小随脉动变化
      const glowGradient = ctx.createRadialGradient(
        0, 0, radius,
        0, 0, outerGlowRadius
      );

      // 设置外部光环颜色
      const outerAlpha = 0.5 * pulseValue;
      glowGradient.addColorStop(0, this.hexToRgba('#FFFFFF', 0));
      glowGradient.addColorStop(0.5, this.hexToRgba('#FFFFFF', outerAlpha * 0.5));
      glowGradient.addColorStop(1, this.hexToRgba('#FFFFFF', 0));

      // 使用叠加模式使光环更亮
      ctx.globalCompositeOperation = 'lighter';

      // 绘制外部光环
      ctx.beginPath();
      ctx.arc(0, 0, outerGlowRadius, 0, Math.PI * 2);
      ctx.fillStyle = glowGradient;
      ctx.fill();

      // 创建内部光环渐变
      const innerGlowRadius = radius * 0.9;
      const innerGlowGradient = ctx.createRadialGradient(
        0, 0, 0,
        0, 0, innerGlowRadius
      );

      // 设置内部光环颜色
      const innerAlpha = 0.6 * pulseValue;
      innerGlowGradient.addColorStop(0, this.hexToRgba('#FFFFFF', 0));
      innerGlowGradient.addColorStop(0.8, this.hexToRgba('#FFFFFF', innerAlpha * 0.3));
      innerGlowGradient.addColorStop(1, this.hexToRgba('#FFFFFF', innerAlpha));

      // 绘制内部光环
      ctx.beginPath();
      ctx.arc(0, 0, innerGlowRadius, 0, Math.PI * 2);
      ctx.fillStyle = innerGlowGradient;
      ctx.fill();

      // 恢复默认模式
      ctx.globalCompositeOperation = 'source-over';

      // 添加外部光点装饰，与泡泡效果保持一致
      this._drawGlowParticles(ctx, 0, 0, radius, outerGlowRadius, color, pulseValue);
    } catch (err) {
      console.error('绘制发光效果时出错', err);
      // 出错时不绘制发光效果
    }
  }

  /**
   * 绘制星星文字
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {Object} star - 星星对象
   * @param {number} radius - 星星半径
   * @private
   */
  _drawStarText(ctx, star, radius) {
    // 设置文字样式
    ctx.fillStyle = '#FFFFFF';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // 根据星星大小调整字体大小
    const fontSize = Math.max(12, Math.min(24, radius * this.starConfig.textSizeRatio));
    ctx.font = `${fontSize}px Arial, sans-serif`;

    // 获取文字长度
    const textLength = star.text.length;

    // 检查文字长度，四个字及以上时考虑换行处理
    if (textLength >= 4) {
      // 文字换行处理 - 按照2个字一行进行均匀分割
      const lines = [];
      for (let i = 0; i < textLength; i += 2) {
        lines.push(star.text.substr(i, 2));
      }

      // 绘制多行文字
      lines.forEach((line, index) => {
        const lineCount = lines.length;
        // 调整行间距系数为1.1，保持适中的间距
        const lineSpacing = fontSize * 1.1;
        // 计算每行的垂直位置，使整体垂直居中
        const lineY = star.y - ((lineCount - 1) * lineSpacing / 2) + index * lineSpacing;
        ctx.fillText(line, star.x, lineY);
      });
    } else {
      // 对于3个字及以下的情况，保持单行显示
      ctx.fillText(star.text, star.x, star.y);
    }
  }

  /**
   * 绘制光环周围的光点粒子
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} innerRadius - 内半径
   * @param {number} outerRadius - 外半径
   * @param {string} color - 基础颜色
   * @param {number} pulseValue - 脉动值
   * @private
   */
  _drawGlowParticles(ctx, x, y, innerRadius, outerRadius, color, pulseValue) {
    try {
      // 光点数量
      const particleCount = 8;

      // 内外半径差
      const radiusRange = outerRadius - innerRadius;

      // 绘制光点
      for (let i = 0; i < particleCount; i++) {
        // 计算光点位置 - 在内外圆之间随机分布，但更靠近星星
        const angle = (i / particleCount) * Math.PI * 2 + (Date.now() * 0.001) % (Math.PI * 2);
        const distance = innerRadius + radiusRange * 0.5 * Math.random(); // 减小分布范围
        const particleX = x + Math.cos(angle) * distance;
        const particleY = y + Math.sin(angle) * distance;

        // 光点大小随脉动变化，整体减小
        const particleSize = 1.5 + 2 * pulseValue * Math.random();

        // 光点透明度随脉动变化
        const particleAlpha = 0.3 * pulseValue * (1 - distance / outerRadius);

        // 创建光点渐变
        const particleGradient = ctx.createRadialGradient(
          particleX, particleY, 0,
          particleX, particleY, particleSize
        );

        // 设置光点颜色
        particleGradient.addColorStop(0, this.hexToRgba('#FFFFFF', particleAlpha));
        particleGradient.addColorStop(1, this.hexToRgba('#FFFFFF', 0));

        // 绘制光点
        ctx.beginPath();
        ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
        ctx.fillStyle = particleGradient;
        ctx.fill();
      }
    } catch (err) {
      console.error('绘制光点粒子时出错', err);
      // 出错时不绘制光点
    }
  }

  /**
   * 绘制光环周围的光点粒子（增强版）
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} innerRadius - 内半径
   * @param {number} outerRadius - 外半径
   * @param {string} color - 基础颜色
   * @param {number} pulseValue - 脉动值
   * @private
   */
  _drawGlowParticlesEnhanced(ctx, x, y, innerRadius, outerRadius, color, pulseValue) {
    try {
      // 光点数量
      const particleCount = 8;

      // 内外半径差
      const radiusRange = outerRadius - innerRadius;

      // 绘制光点
      for (let i = 0; i < particleCount; i++) {
        // 计算光点位置 - 在内外圆之间随机分布，但更靠近星星
        const angle = (i / particleCount) * Math.PI * 2 + (Date.now() * 0.001) % (Math.PI * 2);
        const distance = innerRadius + radiusRange * 0.5 * Math.random(); // 减小分布范围
        const particleX = x + Math.cos(angle) * distance;
        const particleY = y + Math.sin(angle) * distance;

        // 光点大小随脉动变化，整体减小
        const particleSize = 1.5 + 2 * pulseValue * Math.random();

        // 光点透明度随脉动变化
        const particleAlpha = 0.3 * pulseValue * (1 - distance / outerRadius);

        // 创建光点渐变
        const particleGradient = ctx.createRadialGradient(
          particleX, particleY, 0,
          particleX, particleY, particleSize
        );

        // 设置光点颜色
        particleGradient.addColorStop(0, this.hexToRgba('#FFFFFF', particleAlpha));
        particleGradient.addColorStop(1, this.hexToRgba('#FFFFFF', 0));

        // 绘制光点
        ctx.beginPath();
        ctx.arc(particleX, particleY, particleSize, 0, Math.PI * 2);
        ctx.fillStyle = particleGradient;
        ctx.fill();
      }
    } catch (err) {
      console.error('绘制光点粒子时出错', err);
      // 出错时不绘制光点
    }
  }

  /**
   * 在绘制失败时绘制备用星星形状（简单圆形）
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} radius - 半径
   * @param {string} color - 颜色
   * @private
   */
  _drawFallbackStar(ctx, x, y, radius, color) {
    try {
      ctx.save();
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fillStyle = color || '#4f46e5';
      ctx.fill();
      ctx.restore();
    } catch (err) {
      console.error('绘制备用星星形状也失败', err);
    }
  }

  /**
   * 简单渲染所有星星（出错时的备用方法）
   * @private
   */
  _renderAllStarsSimple() {
    try {
      const ctx = this.ctx;
      if (!ctx) return;

      // 简单绘制每个星星
      for (let i = 0; i < this.elements.length; i++) {
        try {
          const star = this.elements[i];
          if (!star) continue;

          ctx.beginPath();
          ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
          ctx.fillStyle = star.color || '#4f46e5';
          ctx.fill();
        } catch (err) {
          // 单个星星绘制失败不影响其他星星
          continue;
        }
      }
    } catch (err) {
      console.error('简单渲染星星时出错', err);
      // 此处不再尝试恢复，避免无限错误循环
    }
  }

  /**
   * 绘制星空背景
   * @private
   */
  _drawStarryBackground() {
    const ctx = this.ctx;
    if (!ctx) return;

    // 绘制背景星星
    if (this.backgroundStars.length > 0) {
      for (const star of this.backgroundStars) {
        // 更新闪烁相位
        star.twinklePhase += star.twinkleSpeed;
        if (star.twinklePhase > Math.PI * 2) {
          star.twinklePhase -= Math.PI * 2;
        }

        // 计算闪烁亮度 (0.2-0.7)
        const brightness = 0.2 + Math.abs(Math.sin(star.twinklePhase)) * 0.5;

        // 设置星星颜色
        ctx.fillStyle = `rgba(${star.color.r}, ${star.color.g}, ${star.color.b}, ${brightness})`;

        // 绘制星星 (简单的小圆点)
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }

  /**
   * 初始化背景星星
   * @param {number} count - 星星数量
   * @private
   */
  _initBackgroundStars(count) {
    const stars = [];

    // 创建指定数量的背景星星
    for (let i = 0; i < count; i++) {
      // 随机位置
      const x = Math.random() * this.canvasWidth;
      const y = Math.random() * this.canvasHeight;

      // 随机大小 (0.5-2.0)
      const size = 0.5 + Math.random() * 1.5;

      // 随机颜色 (白色到蓝色的变化)
      const r = 180 + Math.floor(Math.random() * 75); // 180-255
      const g = 180 + Math.floor(Math.random() * 75); // 180-255
      const b = 220 + Math.floor(Math.random() * 35); // 220-255
      const color = { r, g, b };

      // 随机闪烁速度
      const twinkleSpeed = 0.0005 + Math.random() * 0.002;

      // 随机初始相位
      const twinklePhase = Math.random() * Math.PI * 2;

      stars.push({ x, y, size, color, twinkleSpeed, twinklePhase });
    }

    this.backgroundStars = stars;
  }

  /**
   * 将十六进制颜色转换为RGBA格式
   * @param {string} hex - 十六进制颜色
   * @param {number} alpha - 透明度
   * @returns {string} - RGBA格式的颜色
   */
  hexToRgba(hex, alpha) {
    try {
      if (!hex || typeof hex !== 'string') {
        return `rgba(0, 0, 0, ${alpha || 1})`;
      }

      // 处理简写形式
      let cleanHex = hex.replace('#', '');
      if (cleanHex.length === 3) {
        cleanHex = cleanHex.split('').map(char => char + char).join('');
      }

      // 转换为RGB
      const r = parseInt(cleanHex.substring(0, 2), 16);
      const g = parseInt(cleanHex.substring(2, 4), 16);
      const b = parseInt(cleanHex.substring(4, 6), 16);

      // 返回RGBA格式
      return `rgba(${r}, ${g}, ${b}, ${alpha || 1})`;
    } catch (err) {
      console.error('颜色转换错误', err);
      return `rgba(0, 0, 0, ${alpha || 1})`;
    }
  }

  /**
   * 处理触摸开始事件 - 重写基类方法
   * @param {Object} e - 触摸事件对象
   * @returns {Object|null} 交互结果
   */
  handleTouchStart(e) {
    if (!e.touches || e.touches.length === 0) return null;

    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 记录触摸开始时间和位置
    this.dragStartTime = Date.now();
    this.dragStartX = x;
    this.dragStartY = y;
    this.isDragging = false;
    this.dragDistance = 0;

    // 检测点击的是哪个星星
    for (const star of this.elements) {
      if (this._isPointInElement(x, y, star)) {
        // 记录拖动开始的星星和位置
        this.draggedElement = star;
        this.elementStartX = star.x;
        this.elementStartY = star.y;

        // 标记星星为拖动状态
        star.isDragged = true;

        // 记录原始速度
        star.originalVelocityX = star.velocityX;
        star.originalVelocityY = star.velocityY;

        // 不立即返回主题，等待触摸结束时判断是点击还是拖动
        return null;
      }
    }

    return null;
  }

  /**
   * 处理触摸移动事件 - 重写基类方法
   * @param {Object} e - 触摸事件对象
   * @returns {string} 鼠标样式
   */
  handleTouchMove(e) {
    if (!e.touches || e.touches.length === 0) return 'default';

    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 如果有正在拖动的星星
    if (this.draggedElement) {
      // 计算拖动距离
      const dragX = x - this.dragStartX;
      const dragY = y - this.dragStartY;
      this.dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

      // 如果拖动距离超过阈值，标记为拖动状态
      if (this.dragDistance > 5) {
        this.isDragging = true;
      }

      // 限制拖动距离不超过星星半径
      const maxDragDistance = this.draggedElement.radius;
      if (this.dragDistance > maxDragDistance) {
        // 按比例缩放拖动距离
        const scale = maxDragDistance / this.dragDistance;
        const limitedDragX = dragX * scale;
        const limitedDragY = dragY * scale;

        // 更新星星位置
        this.draggedElement.x = this.elementStartX + limitedDragX;
        this.draggedElement.y = this.elementStartY + limitedDragY;
      } else {
        // 直接更新星星位置
        this.draggedElement.x = this.elementStartX + dragX;
        this.draggedElement.y = this.elementStartY + dragY;
      }

      return 'grabbing';
    }

    // 如果没有拖动的星星，更新悬停状态
    let hasHoveredElement = false;
    for (const star of this.elements) {
      if (star.isDragged) continue; // 跳过正在拖动的星星

      if (this._isPointInElement(x, y, star)) {
        star.isHovered = true;
        hasHoveredElement = true;
      } else {
        star.isHovered = false;
      }
    }

    // 返回鼠标指针样式
    return hasHoveredElement ? 'pointer' : 'default';
  }

  /**
   * 处理触摸结束事件 - 重写基类方法
   * @returns {Object|null} 交互结果
   */
  handleTouchEnd() {
    // 如果有拖动的星星
    if (this.draggedElement) {
      const star = this.draggedElement;

      // 如果是拖动操作
      if (this.isDragging) {
        // 计算拖动向量
        const dragX = star.x - this.elementStartX;
        const dragY = star.y - this.elementStartY;
        const dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

        // 计算拖动距离与最大拖动距离(半径)的比例，用于调整加速度
        const dragRatio = Math.min(1.0, dragDistance / star.radius);

        if (dragDistance > 0) {
          // 计算反方向的单位向量
          const directionX = -dragX / dragDistance;
          const directionY = -dragY / dragDistance;

          // 计算当前速度的大小
          const currentSpeed = Math.sqrt(
            star.originalVelocityX * star.originalVelocityX +
            star.originalVelocityY * star.originalVelocityY
          );

          // 如果原始速度接近于0，使用基础速度
          const baseSpeed = currentSpeed > 0.01 ? currentSpeed : this.config.baseSpeed;

          // 加速速度为正常速度的3倍，并根据拖动距离比例调整
          const acceleratedSpeed = baseSpeed * 3.0 * dragRatio;

          // 应用新速度
          star.velocityX = directionX * acceleratedSpeed;
          star.velocityY = directionY * acceleratedSpeed;

          // 标记为加速状态
          star.isAccelerated = true;
          star.collisionCount = 0;
        }

        // 重置拖动状态
        star.isDragged = false;
        this.draggedElement = null;
        this.isDragging = false;

        // 拖动操作不触发点击事件
        return null;
      } else {
        // 如果是点击操作（没有明显拖动）
        // 获取星星主题配置
        const themes = this.getThemes ? this.getThemes() : [];

        // 找到匹配的主题
        const theme = themes.find(t => t.name === star.text);
        if (theme) {
          // 标记星星为点击状态
          star.isClicked = true;

          // 重置拖动状态
          star.isDragged = false;
          this.draggedElement = null;

          // 返回主题和交互信息
          return {
            theme: theme,
            tagId: theme.id,
            interactionType: 'click',
            duration: Date.now() - this.dragStartTime,
            positionX: star.x,
            positionY: star.y
          };
        }
      }
    }

    // 重置所有星星的悬停状态
    for (const star of this.elements) {
      star.isHovered = false;
    }

    // 重置拖动状态
    this.draggedElement = null;
    this.isDragging = false;

    return null;
  }

  /**
   * 重置交互状态
   */
  resetInteractionState() {
    for (const star of this.elements) {
      if (star.isClicked) {
        // 为被点击的星星重新生成随机速度，保持与整体速度一致
        const angle = Math.random() * Math.PI * 2;
        star.velocityX = Math.cos(angle) * this.config.baseSpeed * 0.8;
        star.velocityY = Math.sin(angle) * this.config.baseSpeed * 0.8;
        star.isClicked = false;
      }
    }
  }

  /**
   * 检查点是否在星星内
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {Object} star - 星星对象
   * @returns {boolean} 是否在星星内
   * @protected
   */
  _isPointInElement(x, y, star) {
    // 使用简化的圆形碰撞检测
    // 对于星形，这是一个近似值，但对于交互来说已经足够好
    const distance = Math.sqrt(
      Math.pow(x - star.x, 2) +
      Math.pow(y - star.y, 2)
    );

    return distance <= star.radius;
  }
}

module.exports = StarCanvas;
