/**
 * 标签管理组件
 * 用于管理用户的标签，支持软删除功能
 */
Component({
  properties: {
    userId: {
      type: String,
      value: ''
    },
    showRecycleBin: {
      type: Boolean,
      value: false
    }
  },

  data: {
    tags: [],
    isLoading: false,
    isSelectMode: false,
    selectedTags: [],
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    activeTab: 'all', // 'all', 'deleted'
    searchKeyword: ''
  },

  lifetimes: {
    attached() {
      // 加载标签列表
      this.loadTags();
    }
  },

  methods: {
    // 加载标签列表
    loadTags() {
      this.setData({ isLoading: true });

      const { page, pageSize } = this.data.pagination;
      const params = {
        page,
        pageSize,
        userId: this.properties.userId,
        keyword: this.data.searchKeyword
      };

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[tag-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '加载失败', icon: 'none' });
        return;
      }

      // 根据当前标签页加载不同数据
      const promise = this.data.activeTab === 'deleted'
        ? api.tag.getDeletedTags(params)
        : api.tag.getTags(params);

      promise.then(result => {
        this.setData({
          tags: result.data || [],
          'pagination.total': result.total || 0,
          isLoading: false
        });
      }).catch(err => {
        console.error('加载标签失败:', err);
        wx.showToast({ title: '加载失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 切换标签页
    switchTab(e) {
      const tab = e.currentTarget.dataset.tab;
      if (tab === this.data.activeTab) return;

      this.setData({
        activeTab: tab,
        'pagination.page': 1,
        isSelectMode: false,
        selectedTags: []
      }, () => {
        this.loadTags();
      });
    },

    // 分页加载更多
    loadMore() {
      if (this.data.isLoading) return;

      const { page, pageSize, total } = this.data.pagination;
      if (page * pageSize >= total) return;

      this.setData({
        'pagination.page': page + 1
      }, () => {
        this.loadTags();
      });
    },

    // 搜索标签
    handleSearch(e) {
      const keyword = e.detail.value;
      this.setData({
        searchKeyword: keyword,
        'pagination.page': 1
      }, () => {
        this.loadTags();
      });
    },

    // 清除搜索
    clearSearch() {
      this.setData({
        searchKeyword: '',
        'pagination.page': 1
      }, () => {
        this.loadTags();
      });
    },

    // 查看标签详情
    viewTagDetail(e) {
      const tagId = e.currentTarget.dataset.id;
      if (!tagId) return;

      // 触发查看详情事件
      this.triggerEvent('view', { id: tagId });
    },

    // 编辑标签
    editTag(e) {
      const tagId = e.currentTarget.dataset.id;
      if (!tagId) return;

      // 触发编辑事件
      this.triggerEvent('edit', { id: tagId });
    },

    // 软删除标签
    softDeleteTag(e) {
      const tagId = e.currentTarget.dataset.id;
      if (!tagId) return;

      wx.showModal({
        title: '删除确认',
        content: '确定要删除此标签吗？删除后可在回收站恢复。',
        success: res => {
          if (res.confirm) {
            this.performSoftDelete(tagId);
          }
        }
      });
    },

    // 执行软删除
    performSoftDelete(tagId) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[tag-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '删除失败', icon: 'none' });
        return;
      }

      api.tag.softDelete(tagId)
        .then(() => {
          wx.showToast({ title: '删除成功', icon: 'success' });
          // 重新加载列表
          this.loadTags();
          // 触发删除事件
          this.triggerEvent('delete', { id: tagId });
        })
        .catch(err => {
          console.error('删除标签失败:', err);
          wx.showToast({ title: '删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    },

    // 创建新标签
    createTag() {
      // 触发创建事件
      this.triggerEvent('create');
    },

    // 切换选择模式
    toggleSelectMode() {
      this.setData({
        isSelectMode: !this.data.isSelectMode,
        selectedTags: []
      });
    },

    // 选择/取消选择标签
    toggleSelect(e) {
      if (!this.data.isSelectMode) return;

      const tagId = e.currentTarget.dataset.id;
      if (!tagId) return;

      const { selectedTags } = this.data;
      const index = selectedTags.indexOf(tagId);

      if (index === -1) {
        // 添加到选中列表
        selectedTags.push(tagId);
      } else {
        // 从选中列表移除
        selectedTags.splice(index, 1);
      }

      this.setData({ selectedTags });
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (!this.data.isSelectMode) return;

      const { selectedTags, tags } = this.data;

      if (selectedTags.length === tags.length) {
        // 取消全选
        this.setData({ selectedTags: [] });
      } else {
        // 全选
        this.setData({
          selectedTags: tags.map(tag => tag.id)
        });
      }
    },

    // 批量删除
    batchDelete() {
      const { selectedTags } = this.data;
      if (selectedTags.length === 0) {
        wx.showToast({ title: '请先选择标签', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '批量删除确认',
        content: `确定要删除选中的${selectedTags.length}个标签吗？删除后可在回收站恢复。`,
        success: res => {
          if (res.confirm) {
            this.performBatchDelete(selectedTags);
          }
        }
      });
    },

    // 执行批量删除
    performBatchDelete(tagIds) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[tag-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '批量删除失败', icon: 'none' });
        return;
      }

      // 使用Promise.all执行批量删除
      Promise.all(tagIds.map(id => api.tag.softDelete(id)))
        .then(() => {
          wx.showToast({ title: '批量删除成功', icon: 'success' });
          // 重新加载列表
          this.loadTags();
          // 退出选择模式
          this.setData({
            isSelectMode: false,
            selectedTags: []
          });
          // 触发批量删除事件
          this.triggerEvent('batchDelete', { ids: tagIds });
        })
        .catch(err => {
          console.error('批量删除标签失败:', err);
          wx.showToast({ title: '批量删除失败', icon: 'none' });
          this.setData({ isLoading: false });
        });
    }
  }
});
