.tag-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f8f8;
}

/* 顶部操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

.create-btn, .select-mode-btn, .select-all, .batch-delete, .cancel-select {
  padding: 6px 12px;
  font-size: 14px;
  border-radius: 4px;
  background-color: #f0f0f0;
  margin-right: 8px;
}

.create-btn {
  background-color: #e6f7ff;
  color: #1890ff;
}

.batch-delete {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.cancel-select {
  background-color: #f0f0f0;
  color: #666666;
}

.icon {
  margin-right: 4px;
}

/* 搜索栏 */
.search-bar {
  padding: 10px 15px;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 6px 12px;
}

.search-icon, .clear-icon {
  color: #999999;
  font-size: 14px;
}

.search-input {
  flex: 1;
  height: 20px;
  margin: 0 8px;
  font-size: 14px;
}

/* 标签页 */
.tabs {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #666666;
  position: relative;
}

.tab.active {
  color: #1890ff;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #1890ff;
}

/* 标签列表 */
.tag-list-container {
  flex: 1;
  background-color: #f8f8f8;
}

.tag-list {
  padding: 10px;
}

.tag-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 12px 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tag-item.selectable {
  padding-right: 40px;
  position: relative;
}

.tag-item.selected {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.tag-content {
  flex: 1;
}

.tag-name {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}

.tag-meta {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999999;
}

.tag-category {
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 8px;
}

.tag-date {
  color: #999999;
}

.tag-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  margin-left: 8px;
}

.edit-btn {
  background-color: #f0f0f0;
  color: #666666;
}

.delete-btn {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.select-indicator {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  color: #1890ff;
}

/* 加载和空状态 */
.loading-placeholder, .empty-placeholder, .loading-more {
  text-align: center;
  padding: 20px 0;
  color: #999999;
  font-size: 14px;
}
