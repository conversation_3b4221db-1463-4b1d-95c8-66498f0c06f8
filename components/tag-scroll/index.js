// components/tag-scroll/index.js
// 标签滚动栏组件 - 简单版本

// 配置是否输出调试信息
const DEBUG = false;

// 安全的日志输出函数
const log = DEBUG ? console.log : () => {};

Component({
  properties: {
    // 所有分类标签
    categories: {
      type: Array,
      value: [],
      observer: function (newVal) {
        if (!newVal || !Array.isArray(newVal)) return;

        // 有标签时，将数据保存到本地存储作为应急备用
        if (newVal.length > 0) {
          try {
            wx.setStorageSync('tag_scroll_last_tags', newVal);
          } catch (err) {
            console.error('[tag-scroll] 保存标签到本地存储失败:', err);
          }
        }
      }
    },
    // 当前选中的分类
    currentCategory: {
      type: String,
      value: 'all',
      observer: function (newVal, oldVal) {
        if (newVal !== oldVal && this.data.centerCategoryId !== newVal) {
          this.setData({ centerCategoryId: newVal }, () => {
            this._scrollToCenter(newVal);
          });
        }
      }
    },
    // 是否开启调试模式
    debugMode: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 当前居中显示的标签ID
    centerCategoryId: 'all',
    // 容器样式
    containerStyle: 'padding-left: 50%; padding-right: 50%;',
    // 内部状态
    _recoveryAttempted: false,
    _lastMoveTime: 0,
    currentScrollLeft: 0,
    scrollLeft: 0
  },

  lifetimes: {
    attached() {
      // 获取屏幕宽度
      const systemInfo = wx.getSystemInfoSync();
      this.screenWidth = systemInfo.windowWidth;
      this._isReady = false;
    },

    ready() {
      this._isReady = true;

      // 检查标签是否为空，如果为空则尝试恢复
      if (!this.data.categories || this.data.categories.length === 0) {
        this._attemptTagsRecovery();
      }

      // 初次渲染后，居中滚动到当前选中项
      this._scrollToCenter(this.data.centerCategoryId);
    }
  },

  methods: {
    /**
     * 尝试从本地存储恢复标签数据
     */
    _attemptTagsRecovery() {
      if (this.data._recoveryAttempted) return;
      this.data._recoveryAttempted = true;

      try {
        const savedTags = wx.getStorageSync('tag_scroll_last_tags');
        if (savedTags && Array.isArray(savedTags) && savedTags.length > 0) {
          log('[tag-scroll] 从存储恢复标签:', savedTags.length);

          // 触发页面更新categories
          this.triggerEvent('recovery', { tags: savedTags });
        }
      } catch (err) {
        console.error('[tag-scroll] 恢复标签失败:', err);
      }
    },

    /**
     * 处理标签点击
     */
    _handleTap(e) {
      // 获取点击的标签ID
      const categoryId = e.currentTarget.dataset.category;
      if (!categoryId) return;

      // 防止频繁点击
      const now = Date.now();
      if (now - this.data._lastMoveTime < 300) return;
      this.data._lastMoveTime = now;

      // 更新UI状态
      this.setData({
        centerCategoryId: categoryId
      }, () => {
        this._scrollToCenter(categoryId);
      });

      // 触发change事件
      this.triggerEvent('change', { category: categoryId });

      // 触感反馈
      wx.vibrateShort({ type: 'light' });
    },

    /**
     * 处理滚动事件
     */
    _handleScroll(e) {
      // 更新当前滚动位置
      this.setData({ currentScrollLeft: e.detail.scrollLeft });
    },

    /**
     * 根据屏幕中心位置重新计算 scrollLeft 并滚动到选中标签
     * 优化版：更精确地与center-indicator对齐
     */
    _scrollToCenter(categoryId) {
      if (!this._isReady) return;
      const id = categoryId || this.data.centerCategoryId;
      const query = wx.createSelectorQuery().in(this);

      // 获取 scroll-view、目标元素和中心指示器的位置
      query.select('.category-scroll').boundingClientRect();
      query.select(`#category-${id}`).boundingClientRect();

      // 尝试获取父页面中的中心指示器位置（如果存在）
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      query.exec(res => {
        if (!res || res.length < 2) return;
        const [scrollRect, itemRect] = res;

        // 元素中心相对 scroll-view 左侧的偏移
        const offsetLeft = itemRect.left - scrollRect.left;
        const itemCenter = offsetLeft + itemRect.width / 2;

        // scroll-view 可视区中心
        const targetCenter = scrollRect.width / 2;

        // 计算新的滚动位置，确保标签精确居中
        const newScrollLeft = (this.data.currentScrollLeft || 0) + itemCenter - targetCenter;

        // 使用动画滚动到新位置
        this.setData({
          scrollLeft: newScrollLeft,
          // 更新当前选中的标签ID
          centerCategoryId: id
        });

        // 触发精确定位事件，通知父组件标签已居中
        this.triggerEvent('centered', {
          categoryId: id,
          position: {
            left: itemRect.left,
            width: itemRect.width,
            center: itemRect.left + itemRect.width / 2
          }
        });

        // 记录日志（如果开启调试）
        if (this.data.debugMode) {
          console.log(`[tag-scroll] 标签 ${id} 已居中定位，scrollLeft: ${newScrollLeft}`);
        }
      });
    }
  }
});
