<!-- components/tag-scroll/index.wxml -->
<!-- 标签滚动栏组件 - 简单版 -->

<view class="tag-scroll-wrapper">
  <!-- 仅在调试模式时显示的中心轴线 -->
  <view class="debug-center-axis" wx:if="{{debugMode}}"></view>

  <!-- 内部中心指示器 - 与外部center-indicator配合使用 -->
  <view class="internal-center-indicator">
    <view class="indicator-line"></view>
  </view>

  <scroll-view
    class="category-scroll"
    scroll-x="true"
    enhanced="true"
    show-scrollbar="false"
    scroll-left="{{scrollLeft}}"
    scroll-with-animation="true"
    bindscroll="_handleScroll">
    <view class="category-container" style="{{containerStyle}}">
      <block wx:if="{{categories && categories.length > 0}}">
        <view
          wx:for="{{categories}}"
          wx:key="id"
          id="category-{{item.id}}"
          class="category-item {{item.id === currentCategory ? 'active' : ''}} {{item.id === centerCategoryId ? 'center' : ''}}"
          data-category="{{item.id}}"
          data-index="{{index}}"
          bindtap="_handleTap">
          <text>{{item.name}}</text>
          <!-- 当标签在中心位置时显示底部指示器 -->
          <view wx:if="{{item.id === centerCategoryId}}" class="item-center-indicator"></view>
        </view>
      </block>
      <block wx:else>
        <!-- 当没有标签时显示一个临时标签 -->
        <view
          id="category-all"
          class="category-item active center"
          data-category="all"
          data-index="0">
          <text>推荐</text>
          <view class="item-center-indicator"></view>
        </view>
      </block>
    </view>
  </scroll-view>
</view>
