/* components/tag-scroll/index.wxss */
/* 标签滚动栏组件样式 - 简化版 */

.tag-scroll-wrapper {
  width: 100%;
  height: 90rpx;
  position: relative;
  overflow: visible;
}

/* 调试用可视中心线 */
.debug-center-axis {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
  background-color: rgba(255, 0, 0, 0.3); /* 半透明红色 */
  pointer-events: none;
  z-index: 1000;
}

.category-scroll {
  width: 100%;
  white-space: nowrap;
  height: 90rpx;
  position: relative;
  z-index: 10;
  background-color: transparent;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

.category-container {
  display: inline-flex;
  height: 100%;
  align-items: center;
  justify-content: flex-start;
  min-width: max-content;
  /* 使用非对称内边距，为最后一个标签提供更多空间 */
  padding-left: 50%;
  padding-right: 60%;
  box-sizing: border-box;
}

.category-scroll .category-container .category-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
  height: 64rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
  background-color: transparent !important;
  border: none !important;
  border-radius: 0 !important;
}

.category-scroll .category-container .category-item.active {
  color: #3B82F6;
  font-weight: 500;
  background-color: transparent !important;
}

/* 中心标签样式增强 */
.category-scroll .category-container .category-item.center {
  transform: scale(1.1);
  transition: all 0.3s ease;
  color: #3B82F6;
  font-weight: 600;
}

/* 标签中心指示器 */
.item-center-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 16rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #3B82F6, #60a5fa);
  border-radius: 4rpx;
  transform: translateX(-50%);
  transition: all 0.3s ease;
  opacity: 1;
}

/* 内部中心指示器 - 与外部center-indicator配合 */
.internal-center-indicator {
  position: absolute;
  left: 50%;
  top: 0;
  height: 100%;
  width: 2rpx;
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 5;
  opacity: 0.5;
}

.internal-center-indicator .indicator-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(59, 130, 246, 0.3);
}
