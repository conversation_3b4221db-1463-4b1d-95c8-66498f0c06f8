Component({
  properties: {
    showSwitch: {
      type: Boolean,
      value: true
    }
  },

  data: {
    themeMode: 'light',
    currentTheme: 'light',
    currentThemeName: '浅色模式',
    themeModes: [
      { name: '浅色模式', value: 'light' },
      { name: '深色模式', value: 'dark' },
      { name: '跟随系统', value: 'system' }
    ]
  },

  lifetimes: {
    attached() {
      const app = getApp();
      // 获取当前主题模式
      const themeMode = app.globalData.themeMode;
      const currentTheme = app.globalData.currentTheme;

      // 更新主题名称
      const currentThemeName = this.getThemeNameByValue(themeMode);

      this.setData({
        themeMode,
        currentTheme,
        currentThemeName
      });

      // 注册主题变化的回调
      app.themeModeChangeCallback = theme => {
        this.setData({
          currentTheme: theme,
          currentThemeName: this.getThemeNameByValue(app.globalData.themeMode)
        });
        // 设置页面的data-theme属性
        this.updatePageTheme(theme);
      };

      // 初始化页面主题
      this.updatePageTheme(currentTheme);
    }
  },

  methods: {
    // 根据主题值获取主题名称
    getThemeNameByValue(value) {
      const mode = this.data.themeModes.find(item => item.value === value);
      return mode ? mode.name : '浅色模式';
    },

    // 切换主题模式
    switchThemeMode(e) {
      const index = e.detail.value;
      const mode = this.data.themeModes[index].value;
      const app = getApp();

      app.updateThemeMode(mode);
      this.setData({
        themeMode: mode,
        currentTheme: app.globalData.currentTheme,
        currentThemeName: this.data.themeModes[index].name
      });
    },

    // 更新页面的data-theme属性
    updatePageTheme(theme) {
      // 获取页面实例
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      if (currentPage && currentPage.selectComponent) {
        try {
          // 尝试获取页面根元素，并设置data-theme属性
          const pageSelector = currentPage.selectComponent('#page-container');
          if (pageSelector) {
            pageSelector.setData({ theme: theme });
          }

          // 设置页面根节点data-theme属性
          wx.createSelectorQuery().select('page').fields({
            node: true,
            properties: ['dataset']
          }, function (res) {
            if (res && res.node) {
              res.node.dataset.theme = theme;
            }
          }).exec();
        } catch (err) {
          console.error('无法设置页面主题:', err);
        }
      }

      // 通知页面主题已更新
      this.triggerEvent('themechange', { theme });
    },

    // 手动切换到浅色模式
    setLightTheme() {
      const app = getApp();
      app.updateThemeMode('light');
      this.setData({
        themeMode: 'light',
        currentTheme: 'light',
        currentThemeName: '浅色模式'
      });
    },

    // 手动切换到深色模式
    setDarkTheme() {
      const app = getApp();
      app.updateThemeMode('dark');
      this.setData({
        themeMode: 'dark',
        currentTheme: 'dark',
        currentThemeName: '深色模式'
      });
    }
  }
});