<!-- components/theme-manager/theme-manager.wxml -->
<view class="theme-manager">
  <block wx:if="{{showSwitch}}">
    <view class="theme-selector">
      <picker mode="selector" bindchange="switchThemeMode" range="{{themeModes}}" range-key="name">
        <view class="picker-view">
          <view class="theme-label">主题设置</view>
          <view class="theme-value">
            {{currentThemeName}}
            <text class="arrow">▼</text>
          </view>
        </view>
      </picker>
    </view>
  </block>
  
  <view class="theme-indicator">
    <view class="theme-icon {{currentTheme === 'dark' ? 'icon-moon' : 'icon-sun'}}"></view>
  </view>
  
  <slot></slot>
</view> 