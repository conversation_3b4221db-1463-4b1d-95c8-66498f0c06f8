/* components/theme-manager/theme-manager.wxss */
.theme-manager {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.theme-selector {
  padding: 20rpx 30rpx;
  background: var(--card-bg);
  border-radius: 16rpx;
  margin: 20rpx;
  backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 16rpx var(--card-shadow);
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.theme-label {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.theme-value {
  font-size: 26rpx;
  color: var(--text-accent);
  display: flex;
  align-items: center;
}

.arrow {
  font-size: 20rpx;
  margin-left: 8rpx;
}

.theme-indicator {
  display: flex;
  justify-content: flex-end;
  padding: 0 30rpx;
}

.theme-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.icon-sun:before {
  content: '';
  display: block;
  width: 24rpx;
  height: 24rpx;
  background: #f5a623;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(245, 166, 35, 0.8);
}

.icon-moon:before {
  content: '';
  display: block;
  width: 24rpx;
  height: 24rpx;
  background: #9ca3af;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(156, 163, 175, 0.6);
  transform: scale(0.8) translateX(6rpx);
} 