<!-- components/waterfall-content/index.wxml -->
<!-- 瀑布流内容区组件 -->

<view class="waterfall-container">
  <!-- 下拉刷新指示器 -->
  <view class="refresh-container" wx:if="{{isRefreshing}}">
    <view class="refresh-icon"></view>
    <text class="refresh-text">刷新中...</text>
  </view>

  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 瀑布流内容 -->
  <view class="waterfall-content" wx:if="{{!isLoading}}">
    <!-- 左列 -->
    <view class="waterfall-column left-column">
      <view
        class="post-card"
        wx:for="{{leftPosts}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="handleViewDetail">
        <image class="post-image" mode="widthFix" src="{{item.imageUrl}}" lazy-load="true"></image>
        <view class="post-info">
          <view class="post-title">
            {{item.title}}
            <text class="ai-badge" wx:if="{{item.isAiGenerated}}">AI</text>
          </view>
          <view class="post-content">{{item.content}}</view>
          <view class="post-footer">
            <view class="post-user">
              <image class="user-avatar" src="{{item.userAvatar}}"></image>
              <text class="user-name">{{item.userName}}</text>
            </view>
            <view
              class="post-like {{item.isLiked ? 'liked' : ''}}"
              catchtap="handleLike"
              data-id="{{item.id}}">
              <text class="like-count">{{item.likes}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 右列 -->
    <view class="waterfall-column right-column">
      <view
        class="post-card"
        wx:for="{{rightPosts}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="handleViewDetail">
        <image class="post-image" mode="widthFix" src="{{item.imageUrl}}" lazy-load="true"></image>
        <view class="post-info">
          <view class="post-title">
            {{item.title}}
            <text class="ai-badge" wx:if="{{item.isAiGenerated}}">AI</text>
          </view>
          <view class="post-content">{{item.content}}</view>
          <view class="post-footer">
            <view class="post-user">
              <image class="user-avatar" src="{{item.userAvatar}}"></image>
              <text class="user-name">{{item.userName}}</text>
            </view>
            <view
              class="post-like {{item.isLiked ? 'liked' : ''}}"
              catchtap="handleLike"
              data-id="{{item.id}}">
              <text class="like-count">{{item.likes}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多指示器 -->
  <view class="load-more-container" wx:if="{{!isLoading && posts.length > 0}}">
    <view wx:if="{{isLoadingMore}}" class="loading-more">
      <view class="loading-icon"></view>
      <text class="loading-text">加载更多...</text>
    </view>
    <view wx:elif="{{!hasMore}}" class="no-more">
      <text>没有更多内容了</text>
    </view>
    <view wx:else class="load-more" bindtap="handleLoadMore">
      <text>点击加载更多</text>
    </view>
  </view>

  <!-- 创建按钮 - 始终显示在最上层 -->
  <view class="create-button" bindtap="handleCreatePost" hover-class="button-hover" hover-stay-time="100" id="create-button-main">
    <text class="create-icon">+</text>
  </view>

  <!-- 备用创建按钮 - 当主按钮不可见时显示 -->
  <cover-view class="create-button backup-button" bindtap="handleCreatePost" hover-class="button-hover" hover-stay-time="100" id="create-button-backup">
    <cover-view class="create-icon">+</cover-view>
  </cover-view>
</view>
