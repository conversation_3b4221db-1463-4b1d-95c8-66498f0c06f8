/* components/waterfall-content/index.wxss */
/* 瀑布流内容区组件样式 */

.waterfall-container {
  width: 100%;
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 瀑布流布局 */
.waterfall-content {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.waterfall-column {
  width: 48%;
  display: flex;
  flex-direction: column;
}

/* 帖子卡片样式 */
.post-card {
  width: 100%;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-card:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.03);
}

.post-image {
  width: 100%;
  height: auto;
  display: block;
}

.post-info {
  padding: 20rpx;
}

.post-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

/* AI生成标识 */
.ai-badge {
  font-size: 20rpx;
  background-color: #4a90e2;
  color: white;
  padding: 2rpx 10rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
  line-height: 1.2;
}

.post-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.post-user {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.user-name {
  font-size: 24rpx;
  color: #999;
}

.post-like {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.post-like::before {
  content: "♡";
  font-size: 32rpx;
  margin-right: 6rpx;
}

.post-like.liked {
  color: #ff4d4f;
}

.post-like.liked::before {
  content: "♥";
}

/* 加载状态 */
.loading-container, .refresh-container, .loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-icon, .refresh-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .refresh-text {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多 */
.load-more-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
}

.load-more, .no-more {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  padding: 20rpx 0;
}

/* 创建按钮 */
.create-button {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #4a90e2;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
  z-index: 9999; /* 极高的z-index确保始终在最上层 */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  /* 增加可见性 */
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

.create-icon {
  color: #fff;
  font-size: 60rpx;
  font-weight: 300;
  line-height: 1;
}

.create-button:active, .create-button.button-hover {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.2);
}

/* 备用按钮样式 */
.backup-button {
  /* 使用cover-view确保在原生组件之上 */
  z-index: 99999 !important; /* 比主按钮更高的z-index */
}

/* 确保备用按钮的图标居中 */
.backup-button .create-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
