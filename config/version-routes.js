/**
 * 版本路由配置
 * 集中管理所有版本的路由
 */
const config = require('./config');

module.exports = {
  // 版本配置
  config: {
    defaultVersion: 'v1',
    versions: ['v1', 'v2'],
    deprecatedVersions: ['v1'],
    versionExtractor: 'url',
    headerName: 'accept-version',
    queryParam: 'version',
    enableCompatibilityLayer: true
  },
  
  // 路由配置
  routes: [
    {
      path: '/auth',
      versions: {
        v1: require('../routes/auth.routes'),
        v2: require('../routes/auth.routes') // 暂时使用V1版本
      }
    },
    {
      path: '/users',
      versions: {
        v1: require('../routes/user.routes'),
        v2: require('../routes/user.routes') // 暂时使用V1版本
      }
    },
    {
      path: '/tags',
      versions: {
        v1: require('../routes/tag.routes'),
        v2: require('../routes/tagV2.routes')
      }
    },
    {
      path: '/tag-categories',
      versions: {
        v1: require('../routes/tagCategory.routes'),
        v2: require('../routes/tagCategoryV2.routes')
      }
    },
    {
      path: '/insights',
      versions: {
        v1: require('../routes/insight.routes'),
        v2: require('../routes/insightV2.routes')
      }
    },
    {
      path: '/exercises',
      versions: {
        v1: require('../routes/exercise.routes'),
        v2: require('../routes/exerciseV2.routes')
      }
    },
    {
      path: '/notes',
      versions: {
        v1: require('../routes/note.routes'),
        v2: require('../routes/noteV2.routes')
      }
    },
    {
      path: '/themes',
      versions: {
        v1: require('../routes/theme.routes'),
        v2: require('../routes/themeV2.routes')
      }
    },
    {
      path: '/learning-plans',
      versions: {
        v1: require('../routes/learningPlan.routes'),
        v2: require('../routes/learningPlanV2.routes')
      }
    },
    {
      path: '/daily-contents',
      versions: {
        v1: null, // V1版本不支持
        v2: require('../routes/dailyContentV2.routes')
      }
    },
    {
      path: '/cleanup',
      versions: {
        v1: null, // V1版本不支持
        v2: require('../routes/cleanup.routes')
      }
    },
    {
      path: '/batch',
      versions: {
        v1: null, // V1版本不支持
        v2: require('../routes/batchOperation.routes')
      }
    },
    {
      path: '/statistics',
      versions: {
        v1: require('../routes/statistics.routes'),
        v2: require('../routes/statisticsV2.routes')
      }
    }
  ]
};
