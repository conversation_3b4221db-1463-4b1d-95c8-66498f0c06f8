/**
 * 版本路由配置 (前端模拟版)
 * 
 * 注意：此文件已从真实后端路由配置转换为前端模拟配置
 * 所有实际路由导入已被移除，仅保留结构用于前端开发参考
 */

module.exports = {
  // 版本配置
  config: {
    defaultVersion: 'v1',
    versions: ['v1', 'v2'],
    deprecatedVersions: ['v1'],
    versionExtractor: 'url',
    headerName: 'accept-version',
    queryParam: 'version',
    enableCompatibilityLayer: true
  },

  // 路由配置 (仅供前端参考，实际路由已被移除)
  routes: [
    {
      path: '/auth',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null // 真实后端路由已移除
      }
    },
    {
      path: '/users',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null // 真实后端路由已移除
      }
    },
    {
      path: '/tags',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null // 真实后端路由已移除
      }
    },
    {
      path: '/tag-categories',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null // 真实后端路由已移除
      }
    },
    {
      path: '/insights',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/exercises',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/notes',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/themes',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/learning-plans',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/daily-contents',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/cleanup',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/batch',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    },
    {
      path: '/statistics',
      versions: {
        v1: null, // 真实后端路由已移除
        v2: null  // 真实后端路由已移除
      }
    }
  ]
};
