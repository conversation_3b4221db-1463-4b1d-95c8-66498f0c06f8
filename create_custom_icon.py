#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from PIL import Image, ImageDraw

# 创建图标保存目录
ICON_DIR = "assets/icons/new"
os.makedirs(ICON_DIR, exist_ok=True)

# 图标尺寸
ICON_SIZE = 64
ICON_BG_COLOR = (255, 255, 255, 0)  # 透明背景

# 图标样式
ICON_STYLES = {
    "circle": "圆形",
    "square": "方形",
    "rounded_square": "圆角方形",
    "diamond": "菱形",
    "triangle": "三角形",
    "hexagon": "六边形", 
    "star": "星形",
    "cross": "十字形",
    "heart": "心形",
    "donut": "圆环"
}

# 常用颜色
COLORS = {
    "blue": "#3B82F6",
    "green": "#10B981",
    "yellow": "#F59E0B",
    "red": "#EF4444",
    "purple": "#8B5CF6",
    "pink": "#EC4899",
    "cyan": "#14B8A6",
    "orange": "#F97316",
    "indigo": "#6366F1",
    "violet": "#A855F7"
}

def create_circle_icon(draw, color, size):
    """绘制圆形图标"""
    radius = size // 2 - 4
    center = size // 2
    draw.ellipse((center-radius, center-radius, center+radius, center+radius), fill=color)

def create_square_icon(draw, color, size):
    """绘制方形图标"""
    margin = 8
    draw.rectangle((margin, margin, size-margin, size-margin), fill=color)

def create_rounded_square_icon(draw, color, size):
    """绘制圆角方形图标"""
    margin = 8
    radius = 8
    draw.rounded_rectangle((margin, margin, size-margin, size-margin), radius=radius, fill=color)

def create_diamond_icon(draw, color, size):
    """绘制菱形图标"""
    center = size // 2
    half_side = size // 2 - 8
    draw.polygon([
        (center, center - half_side),
        (center + half_side, center),
        (center, center + half_side),
        (center - half_side, center)
    ], fill=color)

def create_triangle_icon(draw, color, size):
    """绘制三角形图标"""
    margin = 8
    draw.polygon([
        (size//2, margin),
        (size-margin, size-margin),
        (margin, size-margin)
    ], fill=color)

def create_hexagon_icon(draw, color, size):
    """绘制六边形图标"""
    import math
    center = size // 2
    radius = size // 2 - 8
    points = []
    for i in range(6):
        angle = math.pi / 3 * i + math.pi / 6
        points.append((
            center + int(radius * math.cos(angle)),
            center + int(radius * math.sin(angle))
        ))
    draw.polygon(points, fill=color)

def create_star_icon(draw, color, size):
    """绘制星形图标"""
    import math
    center = size // 2
    outer_radius = size // 2 - 8
    inner_radius = outer_radius // 2
    points = []
    for i in range(10):
        angle = math.pi / 5 * i - math.pi / 2
        radius = outer_radius if i % 2 == 0 else inner_radius
        points.append((
            center + int(radius * math.cos(angle)),
            center + int(radius * math.sin(angle))
        ))
    draw.polygon(points, fill=color)

def create_cross_icon(draw, color, size):
    """绘制十字形图标"""
    center = size // 2
    width = 12
    margin = 8
    draw.rectangle((center - width//2, margin, center + width//2, size - margin), fill=color)
    draw.rectangle((margin, center - width//2, size - margin, center + width//2), fill=color)

def create_heart_icon(draw, color, size):
    """绘制心形图标"""
    center_x = size // 2
    center_y = size // 2
    width = size // 2 - 10
    height = width
    
    # 创建心形路径
    left_center = (center_x - width//4, center_y - height//4)
    right_center = (center_x + width//4, center_y - height//4)
    
    # 绘制两个圆形
    draw.ellipse((left_center[0] - width//4, left_center[1] - height//4, 
                 left_center[0] + width//4, left_center[1] + height//4), fill=color)
    draw.ellipse((right_center[0] - width//4, right_center[1] - height//4, 
                 right_center[0] + width//4, right_center[1] + height//4), fill=color)
    
    # 绘制三角形底部
    points = [
        (center_x - width//2, center_y),
        (center_x, center_y + height//2),
        (center_x + width//2, center_y)
    ]
    draw.polygon(points, fill=color)

def create_donut_icon(draw, color, size):
    """绘制圆环图标"""
    center = size // 2
    outer_radius = size // 2 - 8
    inner_radius = outer_radius // 2
    
    # 绘制外圆
    draw.ellipse((center - outer_radius, center - outer_radius, 
                 center + outer_radius, center + outer_radius), fill=color)
    
    # 绘制内圆（透明）
    draw.ellipse((center - inner_radius, center - inner_radius, 
                 center + inner_radius, center + inner_radius), fill=ICON_BG_COLOR)

# 样式函数映射
STYLE_FUNCTIONS = {
    "circle": create_circle_icon,
    "square": create_square_icon,
    "rounded_square": create_rounded_square_icon,
    "diamond": create_diamond_icon,
    "triangle": create_triangle_icon,
    "hexagon": create_hexagon_icon,
    "star": create_star_icon,
    "cross": create_cross_icon,
    "heart": create_heart_icon,
    "donut": create_donut_icon
}

def generate_icon(name, style, color):
    """生成单个图标的普通和激活状态"""
    if style not in STYLE_FUNCTIONS:
        print(f"错误: 未知样式 '{style}'")
        print(f"可用样式: {', '.join(STYLE_FUNCTIONS.keys())}")
        return False
        
    # 转换颜色
    try:
        if color.startswith('#'):
            # 十六进制颜色
            hex_color = color
        else:
            # 颜色名称
            if color not in COLORS:
                print(f"错误: 未知颜色 '{color}'")
                print(f"可用颜色: {', '.join(COLORS.keys())}")
                return False
            hex_color = COLORS[color]
            
        # 转换为RGB
        color_rgb = tuple(int(hex_color.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        active_color_rgb = tuple(max(0, c - 40) for c in color_rgb)
    except Exception as e:
        print(f"颜色转换错误: {e}")
        return False
    
    # 创建普通图标
    img = Image.new('RGBA', (ICON_SIZE, ICON_SIZE), ICON_BG_COLOR)
    draw = ImageDraw.Draw(img)
    STYLE_FUNCTIONS[style](draw, color_rgb, ICON_SIZE)
    filepath = os.path.join(ICON_DIR, f"{name}.png")
    img.save(filepath)
    print(f"创建图标: {filepath}")
    
    # 创建激活状态图标
    img_active = Image.new('RGBA', (ICON_SIZE, ICON_SIZE), ICON_BG_COLOR)
    draw_active = ImageDraw.Draw(img_active)
    STYLE_FUNCTIONS[style](draw_active, active_color_rgb, ICON_SIZE)
    filepath_active = os.path.join(ICON_DIR, f"{name}-active.png")
    img_active.save(filepath_active)
    print(f"创建激活图标: {filepath_active}")
    
    return True

def print_help():
    """打印帮助信息"""
    print("\n图标生成工具 - 用法:")
    print("python3 create_custom_icon.py <图标名称> <样式> <颜色>\n")
    
    print("参数:")
    print("  <图标名称>: 图标文件名称，例如 'new_icon'")
    print("  <样式>: 图标样式，可以是以下之一:")
    for style, desc in ICON_STYLES.items():
        print(f"    - {style}: {desc}")
    
    print("\n  <颜色>: 图标颜色，可以是以下之一:")
    for color_name, hex_value in COLORS.items():
        print(f"    - {color_name}: {hex_value}")
    print("    - 或直接使用十六进制颜色代码，例如 '#FF5733'\n")
    
    print("示例:")
    print("  python3 create_custom_icon.py chat circle blue")
    print("  python3 create_custom_icon.py arrow triangle '#00FF00'\n")

if __name__ == "__main__":
    # 显示帮助
    if len(sys.argv) == 1 or sys.argv[1] in ['-h', '--help', 'help']:
        print_help()
        sys.exit(0)
        
    # 检查参数
    if len(sys.argv) < 4:
        print("错误: 参数不足")
        print_help()
        sys.exit(1)
        
    name = sys.argv[1]
    style = sys.argv[2]
    color = sys.argv[3]
    
    print(f"开始创建图标 '{name}' ({style}, {color})...")
    if generate_icon(name, style, color):
        print("完成!")
    else:
        print("创建图标失败")
        sys.exit(1) 