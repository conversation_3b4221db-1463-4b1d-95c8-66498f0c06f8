version: "3.8"

# 通用服务配置
x-service-defaults: &service-defaults
  restart: unless-stopped
  networks:
    - aibubb-network
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"

services:
  # 后端API服务
  backend:
    <<: *service-defaults
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=${NODE_ENV:-production}
    image: aibubb-backend:${TAG:-latest}
    container_name: aibubb-backend
    ports:
      - "${PORT:-9090}:${PORT:-9090}"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - ./.env
    environment:
      # 只保留Docker特定的变量，覆盖.env中的相应设置
      - NODE_ENV=${NODE_ENV:-production}
      - DB_HOST=mysql
      - REDIS_URL=redis://redis:6379
      # 优化Node.js性能
      - NODE_OPTIONS=--max-old-space-size=${NODE_MEMORY:-1024}
    volumes:
      - ./backend:/usr/src/app  # 挂载整个后端代码目录
      - /usr/src/app/node_modules # 排除 node_modules，使用容器内的
      - ./backend/logs:/usr/src/app/logs # 保留日志目录挂载
    healthcheck:
      test: ["CMD", "node", "./scripts/healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '${BACKEND_CPU_LIMIT:-2}'
          memory: ${BACKEND_MEMORY_LIMIT:-2G}
        reservations:
          cpus: '${BACKEND_CPU_RESERVATION:-0.5}'
          memory: ${BACKEND_MEMORY_RESERVATION:-512M}
    # 使用ulimits提高性能
    ulimits:
      nofile:
        soft: 65536
        hard: 65536

  # MySQL数据库
  mysql:
    <<: *service-defaults
    image: mysql:8.0
    container_name: aibubb-mysql
    ports:
      - "${DB_PORT:-3306}:3306"
    env_file:
      - ./.env
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      # 性能优化参数
      MYSQL_INNODB_BUFFER_POOL_SIZE: ${MYSQL_INNODB_BUFFER_POOL_SIZE:-256M}
      MYSQL_MAX_CONNECTIONS: ${MYSQL_MAX_CONNECTIONS:-1000}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d:ro
      - ./mysql-conf:/etc/mysql/conf.d:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=${MYSQL_INNODB_BUFFER_POOL_SIZE:-256M}
      --max-connections=${MYSQL_MAX_CONNECTIONS:-1000}
      --innodb-flush-log-at-trx-commit=2
      --innodb-flush-method=O_DIRECT
      --innodb-file-per-table=1
      --skip-name-resolve
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u${DB_USER}", "-p${DB_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '${MYSQL_CPU_LIMIT:-2}'
          memory: ${MYSQL_MEMORY_LIMIT:-2G}
        reservations:
          cpus: '${MYSQL_CPU_RESERVATION:-0.5}'
          memory: ${MYSQL_MEMORY_RESERVATION:-512M}

  # Redis缓存
  redis:
    <<: *service-defaults
    image: redis:7-alpine
    container_name: aibubb-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: >
      redis-server /usr/local/etc/redis/redis.conf
      --appendonly yes
      --maxmemory ${REDIS_MAXMEMORY:-512mb}
      --maxmemory-policy ${REDIS_MAXMEMORY_POLICY:-allkeys-lru}
      --tcp-keepalive 60
      --tcp-backlog 511
      --timeout 0
      --databases 16
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '${REDIS_CPU_LIMIT:-1}'
          memory: ${REDIS_MEMORY_LIMIT:-1G}
        reservations:
          cpus: '${REDIS_CPU_RESERVATION:-0.1}'
          memory: ${REDIS_MEMORY_RESERVATION:-128M}

  # MCP MySQL服务器
  mcp-mysql-server:
    <<: *service-defaults
    build:
      context: ./mcp-mysql-server
      dockerfile: Dockerfile
    container_name: aibubb-mcp-mysql-server
    env_file:
      - ./.env
    environment:
      - DATABASE_URL=mysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
    depends_on:
      mysql:
        condition: service_healthy
    entrypoint: tail
    command: ["-f", "/dev/null"]
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

# 数据卷
volumes:
  mysql_data:
    name: aibubb-mysql-data
  redis-data:
    name: aibubb-redis-data

# 网络
networks:
  aibubb-network:
    name: aibubb-network
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
