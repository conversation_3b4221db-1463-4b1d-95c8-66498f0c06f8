#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}停止AIBUBB Docker容器...${NC}"

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose未安装${NC}"
    exit 1
fi

# 停止容器
echo -e "${YELLOW}优雅关闭容器...${NC}"
docker-compose down

# 检查是否成功停止
if [ $? -eq 0 ]; then
    echo -e "${GREEN}所有容器已成功停止!${NC}"
else
    echo -e "${RED}停止容器时出错，请检查日志${NC}"
fi
