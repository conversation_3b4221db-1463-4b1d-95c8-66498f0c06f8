# AIBUBB项目TypeScript代码规范

## 概述

本文档定义了AIBUBB项目中TypeScript代码的编写规范，旨在提高代码质量、可维护性和团队协作效率。所有团队成员在编写TypeScript代码时应遵循本规范。

## 文件命名与组织

1. **文件命名**：
   - 使用kebab-case（短横线命名法）命名文件，如`user-service.ts`
   - 类型定义文件使用`.d.ts`后缀，如`api-client.d.ts`
   - 测试文件使用`.test.ts`或`.spec.ts`后缀，如`user-service.test.ts`

2. **目录结构**：
   - 按功能或模块组织文件，而非按类型
   - 相关文件应放在同一目录下，如`utils/api-client/`

3. **导入顺序**：
   - 标准库导入
   - 第三方库导入
   - 项目内部导入
   - 相对路径导入
   - 每组导入之间空一行

## 类型定义

1. **接口与类型**：
   - 使用`interface`定义对象结构，使用`type`定义联合类型、交叉类型或类型别名
   - 接口名使用PascalCase（大驼峰命名法），如`UserProfile`
   - 为复杂对象提供接口定义，避免内联类型

2. **泛型**：
   - 使用有意义的泛型参数名，如`T`表示类型，`K`表示键，`V`表示值
   - 为泛型提供默认类型，如`<T = any>`

3. **枚举**：
   - 使用`enum`定义枚举类型，如`enum UserRole { Admin, User }`
   - 枚举名使用PascalCase，枚举值使用PascalCase或UPPER_SNAKE_CASE

4. **类型注解**：
   - 为函数参数和返回值提供类型注解
   - 为类的属性和方法提供类型注解
   - 为变量提供类型注解，除非类型可以被推断

## 函数与方法

1. **函数声明**：
   - 使用函数声明而非函数表达式，如`function foo() {}`
   - 为函数参数提供默认值，如`function foo(bar = 'baz') {}`
   - 使用可选参数而非多个重载，如`function foo(bar?: string) {}`

2. **箭头函数**：
   - 使用箭头函数表示匿名函数，如`const foo = () => {}`
   - 单行箭头函数省略花括号和return，如`const foo = () => 'bar'`

3. **异步函数**：
   - 使用`async/await`而非Promise链
   - 为异步函数提供返回类型注解，如`async function foo(): Promise<string> {}`

## 类与接口

1. **类声明**：
   - 类名使用PascalCase，如`UserService`
   - 为类的属性和方法提供访问修饰符（`public`、`private`、`protected`）
   - 使用`readonly`修饰只读属性

2. **接口实现**：
   - 类应显式实现接口，如`class UserService implements IUserService {}`
   - 接口应定义方法的签名，包括参数和返回类型

3. **构造函数**：
   - 使用参数属性简化构造函数，如`constructor(private readonly userRepository: UserRepository) {}`
   - 避免在构造函数中执行复杂逻辑，应使用工厂方法或初始化方法

## 注释与文档

1. **JSDoc注释**：
   - 为类、接口、方法和函数提供JSDoc注释
   - 包含描述、参数说明、返回值说明和示例
   - 使用`@param`、`@returns`、`@throws`等标签

2. **内联注释**：
   - 为复杂逻辑提供内联注释
   - 注释应解释"为什么"而非"是什么"
   - 使用`// 注释内容`格式

3. **TODO注释**：
   - 使用`// TODO: 注释内容`格式
   - 包含具体的任务描述和责任人

## 错误处理

1. **异常处理**：
   - 使用`try/catch`捕获异常
   - 避免空的`catch`块
   - 使用自定义错误类型，如`class ApiError extends Error {}`

2. **错误传播**：
   - 在适当的层级处理错误，避免错误冒泡到UI层
   - 使用错误边界处理UI层错误

3. **错误日志**：
   - 记录错误详情，包括错误消息、堆栈和上下文
   - 使用结构化日志格式

## 代码质量

1. **代码格式**：
   - 使用ESLint和Prettier格式化代码
   - 遵循项目的`.eslintrc.js`和`.prettierrc.js`配置

2. **代码复杂度**：
   - 函数应遵循单一职责原则
   - 避免深层嵌套，使用提前返回或提取函数
   - 避免过长函数，应拆分为多个小函数

3. **代码重用**：
   - 提取公共逻辑为工具函数或类
   - 使用组合而非继承
   - 使用高阶函数和装饰器模式

## 最佳实践

1. **空值处理**：
   - 使用`undefined`表示可选值，使用`null`表示有意义的空值
   - 使用可选链操作符`?.`和空值合并操作符`??`
   - 使用类型守卫检查空值，如`if (foo !== undefined) {}`

2. **不可变性**：
   - 使用`const`声明变量，除非需要重新赋值
   - 使用扩展运算符创建新对象或数组，而非修改现有对象或数组
   - 使用`readonly`修饰只读属性和数组

3. **类型安全**：
   - 避免使用`any`类型，除非绝对必要
   - 使用类型断言而非类型转换，如`foo as string`而非`<string>foo`
   - 使用类型守卫缩小类型范围，如`if (typeof foo === 'string') {}`

## 示例

### 接口定义

```typescript
/**
 * 用户接口
 */
export interface User {
  /**
   * 用户ID
   */
  id: number;
  
  /**
   * 用户名
   */
  username: string;
  
  /**
   * 电子邮件
   */
  email: string;
  
  /**
   * 角色
   */
  role: UserRole;
  
  /**
   * 创建时间
   */
  createdAt: string;
  
  /**
   * 更新时间
   */
  updatedAt: string;
}

/**
 * 用户角色枚举
 */
export enum UserRole {
  Admin = 'admin',
  User = 'user',
  Guest = 'guest'
}
```

### 类定义

```typescript
/**
 * 用户服务类
 */
export class UserService implements IUserService {
  /**
   * 构造函数
   * @param userRepository - 用户仓库
   */
  constructor(private readonly userRepository: UserRepository) {}
  
  /**
   * 获取用户
   * @param id - 用户ID
   * @returns 用户Promise
   * @throws {NotFoundError} 用户不存在时抛出
   */
  async getUser(id: number): Promise<User> {
    const user = await this.userRepository.findById(id);
    
    if (!user) {
      throw new NotFoundError(`User with ID ${id} not found`);
    }
    
    return user;
  }
  
  /**
   * 创建用户
   * @param data - 用户数据
   * @returns 创建的用户Promise
   * @throws {ValidationError} 数据验证失败时抛出
   */
  async createUser(data: CreateUserDto): Promise<User> {
    // 验证数据
    this.validateUserData(data);
    
    // 创建用户
    const user = await this.userRepository.create(data);
    
    return user;
  }
  
  /**
   * 验证用户数据
   * @param data - 用户数据
   * @throws {ValidationError} 数据验证失败时抛出
   */
  private validateUserData(data: CreateUserDto): void {
    if (!data.username) {
      throw new ValidationError('Username is required');
    }
    
    if (!data.email) {
      throw new ValidationError('Email is required');
    }
    
    // 更多验证...
  }
}
```

### 函数定义

```typescript
/**
 * 格式化日期
 * @param date - 日期对象或日期字符串
 * @param format - 格式字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string, format = 'YYYY-MM-DD'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // 格式化逻辑...
  
  return formattedDate;
}
```

## 参考资料

- [TypeScript官方文档](https://www.typescriptlang.org/docs/)
- [Google TypeScript风格指南](https://google.github.io/styleguide/tsguide.html)
- [TypeScript Deep Dive](https://basarat.gitbook.io/typescript/)

---

文档更新日期：{{current_date}}
