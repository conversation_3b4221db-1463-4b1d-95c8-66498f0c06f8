# 业务组件文档

本文档详细介绍了AIBUBB项目中的业务组件，包括组件的属性、事件、使用方法和示例代码。

## 目录

1. [学习计划相关组件](#学习计划相关组件)
   - [LearningPlanCard 学习计划卡片](#learningplancard-学习计划卡片)
   - [LearningProgress 学习进度可视化](#learningprogress-学习进度可视化)
2. [内容展示相关组件](#内容展示相关组件)
   - [ContentModal 统一内容展示模态弹窗](#contentmodal-统一内容展示模态弹窗)
   - [ContentCard 内容卡片](#contentcard-内容卡片)
3. [用户中心相关组件](#用户中心相关组件)
   - [UserProfile 用户信息展示](#userprofile-用户信息展示)
   - [AchievementDisplay 成就与徽章展示](#achievementdisplay-成就与徽章展示)

## 学习计划相关组件

### LearningPlanCard 学习计划卡片

学习计划卡片组件用于展示学习计划信息，包括标题、描述、进度、标签等。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| plan | Object | null | 学习计划数据 |
| size | String | 'medium' | 卡片尺寸，可选值：'small', 'medium', 'large' |
| showActions | Boolean | true | 是否显示操作按钮 |
| clickable | Boolean | true | 是否可点击 |
| showTags | Boolean | true | 是否显示标签 |
| showProgress | Boolean | true | 是否显示进度 |
| customStyle | String | '' | 自定义样式 |

#### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| click | 点击卡片时触发 | { plan: Object } |
| detail | 点击查看详情按钮时触发 | { plan: Object } |
| continue | 点击继续学习按钮时触发 | { plan: Object } |

#### 使用示例

```html
<learning-plan-card 
  plan="{{planData}}" 
  bind:click="handlePlanClick"
  bind:detail="handleViewDetail"
  bind:continue="handleContinueLearning"
></learning-plan-card>
```

```javascript
Page({
  data: {
    planData: {
      id: 1,
      title: '人际沟通技巧提升计划',
      description: '通过系统学习提升沟通能力，改善人际关系',
      themeName: '人际沟通',
      themeColor: '#3B82F6',
      status: 'in_progress',
      progress: 65,
      createdAt: '2025-05-15',
      tags: ['沟通技巧', '倾听', '表达', '冲突处理']
    }
  },
  
  handlePlanClick(e) {
    const { plan } = e.detail;
    console.log('点击了学习计划卡片:', plan);
  },
  
  handleViewDetail(e) {
    const { plan } = e.detail;
    console.log('查看学习计划详情:', plan);
  },
  
  handleContinueLearning(e) {
    const { plan } = e.detail;
    console.log('继续学习:', plan);
  }
})
```

### LearningProgress 学习进度可视化

学习进度可视化组件用于展示学习计划的进度，包括日进度、周进度和总体进度。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| plan | Object | null | 学习计划数据 |
| type | String | 'full' | 显示类型，可选值：'full', 'simple', 'calendar' |
| customStyle | String | '' | 自定义样式 |

#### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| dayclick | 点击日期时触发 | { day: Number } |

#### 使用示例

```html
<learning-progress 
  plan="{{planData}}" 
  type="full"
  bind:dayclick="handleDayClick"
></learning-progress>
```

```javascript
Page({
  data: {
    planData: {
      id: 1,
      title: '人际沟通技巧提升计划',
      targetDays: 7,
      completedDays: 4,
      startDate: '2025-05-15'
    }
  },
  
  handleDayClick(e) {
    const { day } = e.detail;
    console.log('点击了学习日期:', day);
  }
})
```

## 内容展示相关组件

### ContentModal 统一内容展示模态弹窗

统一内容展示模态弹窗组件用于展示练习、观点、笔记等内容。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| visible | Boolean | false | 是否显示弹窗 |
| contentType | String | '' | 内容类型：'exercise'(练习), 'insight'(观点), 'note'(笔记) |
| contentData | Object | null | 内容数据 |
| showClose | Boolean | true | 是否显示关闭按钮 |
| showTitle | Boolean | true | 是否显示标题 |
| customTitle | String | '' | 自定义标题 |
| showFooter | Boolean | true | 是否显示底部按钮 |
| requireScrollToBottom | Boolean | false | 是否需要滚动到底部才能完成 |
| customStyle | String | '' | 自定义样式 |

#### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| close | 关闭弹窗时触发 | 无 |
| submit | 提交答案时触发 | { contentType: String, contentId: Number, answer: String } |
| complete | 完成内容时触发 | { contentType: String, contentId: Number } |

#### 使用示例

```html
<content-modal 
  visible="{{showContentModal}}"
  contentType="{{contentType}}"
  contentData="{{contentData}}"
  bind:close="handleCloseModal"
  bind:submit="handleSubmitContent"
  bind:complete="handleCompleteContent"
></content-modal>
```

```javascript
Page({
  data: {
    showContentModal: false,
    contentType: 'exercise',
    contentData: {
      id: 1,
      title: '有效倾听练习',
      content: '请描述一次你认真倾听他人并理解对方感受的经历，以及这如何影响了沟通结果？',
      type: 'text'
    }
  },
  
  showModal() {
    this.setData({
      showContentModal: true
    });
  },
  
  handleCloseModal() {
    this.setData({
      showContentModal: false
    });
  },
  
  handleSubmitContent(e) {
    const { contentType, contentId, answer } = e.detail;
    console.log(`提交${contentType}答案:`, { contentId, answer });
  },
  
  handleCompleteContent(e) {
    const { contentType, contentId } = e.detail;
    console.log(`完成${contentType}:`, contentId);
  }
})
```

### ContentCard 内容卡片

内容卡片组件用于展示练习、观点、笔记等内容。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| contentType | String | '' | 内容类型：'exercise'(练习), 'insight'(观点), 'note'(笔记) |
| contentData | Object | null | 内容数据 |
| size | String | 'medium' | 卡片尺寸，可选值：'small', 'medium', 'large' |
| showActions | Boolean | true | 是否显示操作按钮 |
| clickable | Boolean | true | 是否可点击 |
| showTags | Boolean | true | 是否显示标签 |
| customStyle | String | '' | 自定义样式 |

#### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| click | 点击卡片时触发 | { contentType: String, contentData: Object } |
| view | 点击查看按钮时触发 | { contentType: String, contentData: Object } |
| edit | 点击编辑按钮时触发 | { contentType: String, contentData: Object } |
| delete | 点击删除按钮时触发 | { contentType: String, contentData: Object } |

#### 使用示例

```html
<content-card 
  contentType="exercise"
  contentData="{{exerciseData}}"
  bind:click="handleContentClick"
  bind:view="handleViewContent"
  bind:edit="handleEditContent"
  bind:delete="handleDeleteContent"
></content-card>
```

```javascript
Page({
  data: {
    exerciseData: {
      id: 1,
      title: '有效倾听练习',
      content: '请描述一次你认真倾听他人并理解对方感受的经历，以及这如何影响了沟通结果？',
      tags: ['倾听', '共情'],
      createdAt: '2025-05-16'
    }
  },
  
  handleContentClick(e) {
    const { contentType, contentData } = e.detail;
    console.log(`点击了${contentType}卡片:`, contentData);
  },
  
  handleViewContent(e) {
    const { contentType, contentData } = e.detail;
    console.log(`查看${contentType}详情:`, contentData);
  },
  
  handleEditContent(e) {
    const { contentType, contentData } = e.detail;
    console.log(`编辑${contentType}:`, contentData);
  },
  
  handleDeleteContent(e) {
    const { contentType, contentData } = e.detail;
    console.log(`删除${contentType}:`, contentData);
  }
})
```

## 用户中心相关组件

### UserProfile 用户信息展示

用户信息展示组件用于展示用户基本信息、等级、经验值等。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| userData | Object | null | 用户数据 |
| type | String | 'full' | 显示类型，可选值：'full', 'simple', 'mini' |
| showLevel | Boolean | true | 是否显示等级信息 |
| showStats | Boolean | true | 是否显示统计信息 |
| customStyle | String | '' | 自定义样式 |

#### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| avatarclick | 点击头像时触发 | { user: Object } |
| nameclick | 点击用户名时触发 | { user: Object } |
| levelclick | 点击等级时触发 | { user: Object, level: Number, expPoints: Number } |

#### 使用示例

```html
<user-profile 
  userData="{{userData}}" 
  type="full"
  bind:avatarclick="handleAvatarClick"
  bind:nameclick="handleNameClick"
  bind:levelclick="handleLevelClick"
></user-profile>
```

```javascript
Page({
  data: {
    userData: {
      id: 'user123',
      nickName: '学习达人',
      avatarUrl: 'https://example.com/avatar.jpg',
      level: 5,
      expPoints: 2500,
      studyDays: 45,
      completedPlans: 8,
      badges: 12
    }
  },
  
  handleAvatarClick(e) {
    const { user } = e.detail;
    console.log('点击了用户头像:', user);
  },
  
  handleNameClick(e) {
    const { user } = e.detail;
    console.log('点击了用户名:', user);
  },
  
  handleLevelClick(e) {
    const { level, expPoints } = e.detail;
    console.log('点击了用户等级:', { level, expPoints });
  }
})
```

### AchievementDisplay 成就与徽章展示

成就与徽章展示组件用于展示用户获得的成就和徽章。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| userId | String | '' | 用户ID |
| achievements | Array | [] | 成就数据 |
| badges | Array | [] | 徽章数据 |
| type | String | 'grid' | 显示类型，可选值：'grid', 'list', 'carousel' |
| showLocked | Boolean | true | 是否显示未获得的成就/徽章 |
| customStyle | String | '' | 自定义样式 |

#### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| itemclick | 点击成就/徽章时触发 | { type: String, item: Object } |

#### 使用示例

```html
<achievement-display 
  userId="{{userId}}"
  achievements="{{achievements}}"
  badges="{{badges}}"
  type="grid"
  bind:itemclick="handleItemClick"
></achievement-display>
```

```javascript
Page({
  data: {
    userId: 'user123',
    achievements: [
      {
        id: 1,
        name: '学习先锋',
        description: '完成第一个学习计划',
        iconUrl: 'https://example.com/achievement1.png',
        unlocked: true,
        unlockedAt: '2025-04-20'
      },
      {
        id: 2,
        name: '知识探索者',
        description: '完成5个不同主题的学习计划',
        iconUrl: 'https://example.com/achievement2.png',
        unlocked: false
      }
    ],
    badges: [
      {
        id: 1,
        name: '沟通达人',
        description: '完成人际沟通主题的所有学习计划',
        iconUrl: 'https://example.com/badge1.png',
        unlocked: true,
        unlockedAt: '2025-05-12'
      }
    ]
  },
  
  handleItemClick(e) {
    const { type, item } = e.detail;
    console.log(`点击了${type}:`, item);
  }
})
```

## 最佳实践

### 性能优化

1. **懒加载**: 对于列表中的组件，建议使用懒加载技术，只渲染可见区域的组件。
2. **数据缓存**: 对于频繁使用的数据，建议使用缓存技术，减少重复请求。
3. **避免频繁更新**: 避免频繁更新组件数据，特别是在列表中。
4. **合理使用事件**: 避免在组件中绑定过多事件，特别是滚动事件。

### 样式定制

1. **使用主题变量**: 组件样式使用CSS变量，可以通过修改根元素的CSS变量来定制组件样式。
2. **避免直接修改组件样式**: 避免直接修改组件的内部样式，应该使用组件提供的属性或CSS变量来定制样式。
3. **响应式设计**: 组件设计考虑了不同屏幕尺寸，可以在不同设备上正常显示。

### 组件组合

1. **组件嵌套**: 可以将多个组件组合使用，例如在卡片中嵌套进度条。
2. **数据共享**: 多个组件可以共享同一份数据，减少数据冗余。
3. **事件传递**: 组件之间可以通过事件传递数据，实现组件间的通信。

## 常见问题

### 组件不显示

1. 检查组件是否正确引入
2. 检查组件所需的数据是否正确传入
3. 检查组件是否有错误日志输出

### 事件不触发

1. 检查事件绑定是否正确
2. 检查事件名称是否正确
3. 检查组件内部是否正确触发了事件

### 样式异常

1. 检查是否有样式冲突
2. 检查是否正确设置了组件的尺寸和样式属性
3. 检查是否正确引入了样式文件
