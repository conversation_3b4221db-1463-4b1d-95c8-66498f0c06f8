# 基础组件样式更新计划

## 概述

本文档提供了将基础组件样式更新为符合《AIBUBB视觉设计文档V2.0》规范的详细计划。更新将遵循"页面升级保留原则"，在现有实现的基础上进行优化，而不是完全重写。

## 更新原则

1. **使用变量替换硬编码值**：将所有硬编码的颜色、尺寸、间距等替换为样式变量
2. **保持组件API不变**：确保组件的属性和事件不发生变化，保持向后兼容
3. **渐进式更新**：按照优先级分批更新组件，确保系统稳定
4. **双主题支持**：确保所有组件在亮色和暗色模式下都有良好的表现
5. **性能优先**：优化样式实现，减少不必要的计算和渲染

## 更新优先级

### 第一批（高优先级）

这些组件是最基础、使用最广泛的组件，需要首先更新：

1. **Button 按钮**
2. **Card 卡片**
3. **Text 文本**
4. **Icon 图标**
5. **Input 输入框**

### 第二批（中优先级）

这些组件在多个页面中使用，但不如第一批组件那么基础：

1. **Tag 标签**
2. **Badge 徽章**
3. **Switch 开关**
4. **Checkbox 复选框**
5. **Radio 单选框**
6. **Divider 分割线**

### 第三批（低优先级）

这些组件使用频率较低或者在特定场景下使用：

1. **Slider 滑块**
2. **Progress 进度条**
3. **Rate 评分**
4. **Collapse 折叠面板**
5. **Popup 弹出层**

## 具体更新任务

### Button 按钮组件更新

**文件路径**：`components/base/button/`

**更新内容**：

1. 更新样式文件 `index.wxss`：
   - 使用新的颜色变量（如 `var(--primary-color)`, `var(--error-color)` 等）
   - 使用新的尺寸变量（如 `var(--font-size-sm)`, `var(--space-sm)` 等）
   - 使用新的圆角变量（如 `var(--radius-sm)`, `var(--radius-md)` 等）
   - 使用新的过渡变量（如 `var(--transition-normal)`, `var(--easing-standard)` 等）

2. 更新脚本文件 `index.js`：
   - 确保样式计算方法使用新的变量
   - 优化样式计算逻辑，减少不必要的计算

3. 测试双主题：
   - 确保在亮色和暗色模式下按钮样式正确
   - 测试所有按钮类型和状态

### Card 卡片组件更新

**文件路径**：`components/base/card/`

**更新内容**：

1. 更新样式文件 `index.wxss`：
   - 使用新的背景色变量（如 `var(--bg-color-card)`, `var(--card-bg)` 等）
   - 使用新的阴影变量（如 `var(--shadow-sm)`, `var(--shadow-md)` 等）
   - 使用新的间距变量（如 `var(--space-md)`, `var(--space-lg)` 等）
   - 使用新的圆角变量（如 `var(--radius-md)`, `var(--radius-lg)` 等）

2. 更新脚本文件 `index.js`：
   - 确保样式计算方法使用新的变量
   - 优化卡片渲染性能

3. 测试双主题：
   - 确保在亮色和暗色模式下卡片样式正确
   - 测试所有卡片类型和状态

### Text 文本组件更新

**文件路径**：`components/base/text/`

**更新内容**：

1. 更新样式文件 `index.wxss`：
   - 使用新的文本颜色变量（如 `var(--text-color-primary)`, `var(--text-color-secondary)` 等）
   - 使用新的字体大小变量（如 `var(--font-size-md)`, `var(--font-size-lg)` 等）
   - 使用新的行高变量（如 `var(--line-height-normal)`, `var(--line-height-relaxed)` 等）

2. 更新脚本文件 `index.js`：
   - 确保样式计算方法使用新的变量
   - 优化文本渲染性能

3. 测试双主题：
   - 确保在亮色和暗色模式下文本样式正确
   - 测试所有文本类型和状态

### Icon 图标组件更新

**文件路径**：`components/base/icon/`

**更新内容**：

1. 更新样式文件 `index.wxss`：
   - 使用新的图标尺寸变量
   - 使用新的颜色变量（如 `var(--text-color-primary)`, `var(--primary-color)` 等）

2. 更新脚本文件 `index.js`：
   - 确保图标路径指向 `/assets/icons/new/` 目录
   - 实现图标颜色的动态设置

3. 测试双主题：
   - 确保在亮色和暗色模式下图标样式正确
   - 测试所有图标类型和状态

### Input 输入框组件更新

**文件路径**：`components/base/input/` 或 `components/form/input/`

**更新内容**：

1. 更新样式文件 `index.wxss`：
   - 使用新的边框颜色变量（如 `var(--divider-color)`, `var(--primary-color)` 等）
   - 使用新的背景色变量（如 `var(--bg-color-paper)`, `var(--bg-color-card)` 等）
   - 使用新的文本颜色变量（如 `var(--text-color-primary)`, `var(--text-color-secondary)` 等）
   - 使用新的间距变量（如 `var(--space-sm)`, `var(--space-md)` 等）
   - 使用新的圆角变量（如 `var(--radius-sm)`, `var(--radius-md)` 等）

2. 更新脚本文件 `index.js`：
   - 确保样式计算方法使用新的变量
   - 优化输入框交互体验

3. 测试双主题：
   - 确保在亮色和暗色模式下输入框样式正确
   - 测试所有输入框类型和状态

## 测试计划

对于每个更新的组件，需要进行以下测试：

1. **视觉测试**：
   - 在亮色模式下检查样式是否符合设计规范
   - 在暗色模式下检查样式是否符合设计规范
   - 检查不同状态（默认、悬停、激活、禁用等）的样式

2. **功能测试**：
   - 确保组件的所有功能正常工作
   - 确保组件的事件正确触发
   - 确保组件的属性正确应用

3. **性能测试**：
   - 测量组件的渲染性能
   - 测量组件的交互响应时间
   - 确保组件不会导致页面卡顿

## 实施时间表

| 阶段 | 时间 | 任务 |
|------|------|------|
| 准备阶段 | 第1周 | 创建样式变量文件，更新全局样式 |
| 第一批组件更新 | 第1-2周 | 更新高优先级组件 |
| 第二批组件更新 | 第2-3周 | 更新中优先级组件 |
| 第三批组件更新 | 第3-4周 | 更新低优先级组件 |
| 测试与修复 | 第4周 | 全面测试和问题修复 |

## 注意事项

1. 更新过程中，需要保持与现有页面的兼容性
2. 如果发现组件的设计有重大问题，需要与设计团队沟通后再进行修改
3. 所有更新都需要在测试环境中充分测试后再合并到主分支
4. 更新过程中，需要及时更新组件文档

## 附录：组件样式变量对照表

| 旧变量名 | 新变量名 | 说明 |
|---------|---------|------|
| `--text-primary` | `--text-color-primary` | 主要文本颜色 |
| `--text-secondary` | `--text-color-secondary` | 次要文本颜色 |
| `--text-light` | `--text-color-disabled` | 禁用文本颜色 |
| `--text-accent` | `--primary-color` | 强调文本颜色 |
| 无 | `--font-size-xs` | 超小字体大小 |
| 无 | `--font-size-sm` | 小字体大小 |
| 无 | `--font-size-md` | 中等字体大小 |
| 无 | `--font-size-lg` | 大字体大小 |
| 无 | `--font-size-xl` | 超大字体大小 |
| 无 | `--space-xs` | 超小间距 |
| 无 | `--space-sm` | 小间距 |
| 无 | `--space-md` | 中等间距 |
| 无 | `--space-lg` | 大间距 |
| 无 | `--space-xl` | 超大间距 |
| 无 | `--radius-sm` | 小圆角 |
| 无 | `--radius-md` | 中等圆角 |
| 无 | `--radius-lg` | 大圆角 |
| 无 | `--radius-full` | 完全圆角 |
