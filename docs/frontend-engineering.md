# NebulaLearn 前端工程化配置指南

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 正式版 |
| 创建日期 | 2025-05-15 |
| 最后更新 | 2025-05-15 |
| 作者 | NebulaLearn前端团队 |

## 1. 概述

本文档详细描述了NebulaLearn项目前端工程化的配置和使用方法，包括代码规范、构建工具、测试框架、版本控制和持续集成等方面。通过实施本指南，我们将提高代码质量、开发效率和团队协作能力。

## 2. 项目结构

### 2.1 目录结构

NebulaLearn前端项目采用以下目录结构：

```
├── api/                 # API客户端和请求处理
├── assets/              # 静态资源
├── components/          # 组件库
│   ├── base/            # 基础组件
│   ├── business/        # 业务组件
│   └── layout/          # 布局组件
├── config/              # 配置文件
├── constants/           # 常量定义
├── hooks/               # 自定义Hooks
├── models/              # 数据模型定义
├── pages/               # 页面组件
├── services/            # 业务服务层
├── store/               # 全局状态管理
├── styles/              # 全局样式
├── types/               # 类型定义
└── utils/               # 工具函数
```

### 2.2 文件命名规范

- **组件文件**：使用PascalCase命名，如`UserProfile.js`
- **页面文件**：使用kebab-case命名，如`user-profile.js`
- **工具函数**：使用camelCase命名，如`formatDate.js`
- **常量文件**：使用UPPER_SNAKE_CASE命名，如`API_ENDPOINTS.js`
- **样式文件**：与对应的组件或页面同名，如`UserProfile.wxss`

## 3. 代码规范

### 3.1 ESLint配置

我们使用ESLint进行代码规范检查，配置文件位于项目根目录的`.eslintrc.js`。

```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true
  },
  extends: [
    'eslint:recommended'
  ],
  rules: {
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
};
```

### 3.2 Prettier配置

我们使用Prettier进行代码格式化，配置文件位于项目根目录的`.prettierrc.js`。

```javascript
// .prettierrc.js
module.exports = {
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  semi: true,
  singleQuote: true,
  trailingComma: 'es5',
  bracketSpacing: true,
  arrowParens: 'avoid'
};
```

### 3.3 编辑器配置

为确保团队成员使用统一的编辑器配置，我们在项目根目录添加了`.editorconfig`文件。

```ini
# .editorconfig
root = true

[*]
charset = utf-8
indent_style = space
indent_size = 2
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

[*.md]
trim_trailing_whitespace = false
```

## 4. 构建与打包

### 4.1 微信小程序构建配置

微信小程序的构建配置位于`project.config.json`文件中，主要配置项包括：

- **setting**：编译设置，如ES6转换、代码压缩等
- **compileType**：编译类型，如miniprogram、plugin等
- **libVersion**：基础库版本
- **appid**：小程序的AppID

### 4.2 分包配置

为优化小程序的加载性能，我们采用分包加载策略，配置位于`app.json`文件中：

```json
{
  "pages": [
    "pages/index/index",
    "pages/profile/index"
  ],
  "subpackages": [
    {
      "root": "packages/learn",
      "pages": [
        "pages/plan-detail/index",
        "pages/exercise/index"
      ]
    },
    {
      "root": "packages/content",
      "pages": [
        "pages/note-management/index",
        "pages/insight-management/index"
      ]
    }
  ]
}
```

### 4.3 条件编译

为支持多环境开发，我们使用条件编译技术，通过在代码中添加特定注释来实现：

```javascript
// 开发环境
/* #ifdef DEV */
const API_BASE_URL = 'http://localhost:3000/api/v1';
/* #endif */

// 生产环境
/* #ifdef PROD */
const API_BASE_URL = 'https://api.nebulalearn.com/api/v1';
/* #endif */
```

## 5. 测试框架

### 5.1 Jest配置

我们使用Jest进行单元测试和集成测试，配置文件位于`jest.config.js`：

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],
  testPathIgnorePatterns: ['/node_modules/'],
  collectCoverage: true,
  collectCoverageFrom: [
    'utils/**/*.js',
    'services/**/*.js',
    '!**/__tests__/**'
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
};
```

### 5.2 单元测试规范

- 每个工具函数和服务应该有对应的单元测试
- 测试文件应该与被测试文件放在同一目录下，命名为`*.test.js`或`*.spec.js`
- 测试应该覆盖正常情况和边缘情况
- 测试应该独立，不依赖于其他测试的执行结果

### 5.3 组件测试

对于微信小程序组件，我们使用微信官方提供的miniprogram-simulate进行测试：

```javascript
// components/button/button.test.js
const simulate = require('miniprogram-simulate');
const path = require('path');

test('button组件点击事件', () => {
  const id = simulate.load(path.resolve(__dirname, './index'));
  const comp = simulate.render(id);
  const button = comp.querySelector('.button');
  
  // 模拟点击事件
  const mockFn = jest.fn();
  comp.addEventListener('click', mockFn);
  button.dispatchEvent('tap');
  
  // 验证事件是否被触发
  expect(mockFn).toHaveBeenCalled();
});
```

## 6. 版本控制与协作

### 6.1 Git分支策略

我们采用以下Git分支策略：

- **main**：主分支，用于生产环境部署
- **develop**：开发分支，用于集成开发中的功能
- **feature/xxx**：功能分支，用于开发新功能
- **bugfix/xxx**：修复分支，用于修复bug
- **release/x.x.x**：发布分支，用于准备发布

### 6.2 提交规范

我们使用Angular提交规范，格式如下：

```
<type>(<scope>): <subject>

<body>

<footer>
```

其中：
- **type**：提交类型，如feat、fix、docs、style、refactor、test、chore等
- **scope**：影响范围，如组件名、页面名等
- **subject**：简短描述
- **body**：详细描述
- **footer**：关闭issue等

### 6.3 代码审查流程

1. 开发者在feature分支上开发功能
2. 开发完成后，提交Pull Request到develop分支
3. 至少一名团队成员进行代码审查
4. 审查通过后，合并到develop分支
5. 定期将develop分支合并到main分支进行发布

## 7. 持续集成与部署

### 7.1 GitHub Actions配置

我们使用GitHub Actions进行持续集成和部署，配置文件位于`.github/workflows/ci.yml`：

```yaml
name: CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '14.x'
    - name: Install dependencies
      run: npm ci
    - name: Lint
      run: npm run lint
    - name: Test
      run: npm test
    - name: Build
      run: npm run build
```

### 7.2 自动化测试

在每次提交代码时，自动运行测试，确保代码质量：

```bash
# package.json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  }
}
```

### 7.3 自动化部署

对于main分支的提交，自动部署到生产环境：

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '14.x'
    - name: Install dependencies
      run: npm ci
    - name: Build
      run: npm run build
    - name: Deploy
      run: npm run deploy
```

## 8. 开发工具与插件

### 8.1 推荐的编辑器

- **Visual Studio Code**：推荐使用的主要编辑器
- **WebStorm**：可选的IDE

### 8.2 推荐的VS Code插件

- **ESLint**：代码规范检查
- **Prettier**：代码格式化
- **Vetur**：Vue文件支持
- **GitLens**：Git增强
- **Path Intellisense**：路径智能提示
- **Wechat Mini Program Development Tools**：微信小程序开发工具

### 8.3 开发环境设置

为确保团队成员使用统一的开发环境设置，我们在项目根目录添加了`.vscode/settings.json`文件：

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue"
  ],
  "prettier.singleQuote": true,
  "prettier.semi": true
}
```

## 9. 文档与注释

### 9.1 文档规范

- **README.md**：项目根目录下的主要文档，包含项目概述、安装说明、使用方法等
- **docs/**：存放详细文档，如API文档、架构文档、开发指南等
- **CHANGELOG.md**：记录版本变更历史

### 9.2 注释规范

- **文件头注释**：每个文件开头应该有描述文件用途的注释
- **函数注释**：每个函数应该有描述函数用途、参数和返回值的注释
- **复杂逻辑注释**：对于复杂的逻辑，应该添加详细的注释说明

```javascript
/**
 * 格式化日期
 * @param {Date} date - 要格式化的日期
 * @param {string} format - 格式化模板，如'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format) {
  // 实现逻辑
}
```

## 10. 性能优化

### 10.1 代码分割

通过分包加载和懒加载，减少初始加载时间：

```json
// app.json
{
  "pages": ["pages/index/index"],
  "subpackages": [
    {
      "root": "packages/user",
      "pages": ["pages/profile/index"]
    }
  ]
}
```

### 10.2 资源优化

- 图片压缩：使用工具压缩图片，减少体积
- 图标合并：使用雪碧图或字体图标，减少请求数
- 代码压缩：在生产环境中压缩代码，减少体积

### 10.3 缓存策略

- API数据缓存：使用内存缓存和本地存储缓存API数据
- 静态资源缓存：使用CDN和浏览器缓存静态资源

## 11. 安全性

### 11.1 数据安全

- 敏感数据加密：使用加密算法保护敏感数据
- HTTPS：使用HTTPS协议进行数据传输
- 防XSS：对用户输入进行过滤和转义

### 11.2 代码安全

- 依赖检查：定期检查依赖包的安全漏洞
- 代码审查：通过代码审查发现潜在的安全问题
- 权限控制：实现严格的权限控制机制

## 12. 最佳实践

### 12.1 代码组织

- **单一职责原则**：每个组件和函数应该只有一个职责
- **关注点分离**：将UI、业务逻辑和数据访问分离
- **模块化**：将代码组织成独立的模块，减少耦合

### 12.2 性能优化

- **减少重渲染**：避免不必要的状态更新和组件重渲染
- **懒加载**：延迟加载不立即需要的资源
- **缓存**：缓存计算结果和网络请求

### 12.3 错误处理

- **全局错误处理**：实现全局错误捕获机制
- **优雅降级**：在出现错误时提供备用UI
- **错误日志**：记录错误信息，便于排查问题

## 13. 附录

### 13.1 常用命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm test

# 构建生产版本
npm run build

# 代码规范检查
npm run lint

# 代码格式化
npm run format
```

### 13.2 常见问题解答

1. **Q: 如何添加新页面？**
   A: 在pages目录下创建新页面，并在app.json中注册。

2. **Q: 如何添加新组件？**
   A: 在components目录下创建新组件，并在使用的页面中引入。

3. **Q: 如何处理API请求？**
   A: 使用utils/api目录下的API客户端进行请求处理。

### 13.3 参考资源

- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [ESLint文档](https://eslint.org/docs/user-guide/getting-started)
- [Prettier文档](https://prettier.io/docs/en/index.html)
- [Jest文档](https://jestjs.io/docs/getting-started)
