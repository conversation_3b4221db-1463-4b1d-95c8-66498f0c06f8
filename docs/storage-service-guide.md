# 本地存储服务使用指南

本文档介绍了AIBUBB项目中本地存储服务的使用方法和最佳实践。

## 概述

本地存储服务(`StorageService`)提供了统一的接口来管理小程序的本地存储，具有以下特点：

- **统一的API接口**：提供`set`、`get`、`remove`、`clear`等方法
- **键名前缀管理**：自动为存储键添加前缀，避免命名冲突
- **过期时间管理**：支持设置数据的过期时间，自动清理过期数据
- **错误处理**：内置错误处理和日志记录
- **类型安全**：自动处理JSON序列化和反序列化

## 基本用法

### 导入存储服务

```javascript
// 使用默认存储实例
import { storage } from 'utils/storage';

// 或者导入StorageService类创建自定义实例
import { StorageService } from 'utils/storage-service';
```

### 存储数据

```javascript
// 使用默认过期时间(24小时)
storage.set('userPreferences', { theme: 'dark', fontSize: 'medium' });

// 指定过期时间(7天)
storage.set('userPreferences', { theme: 'dark', fontSize: 'medium' }, {
  expiry: 7 * 24 * 60 * 60 * 1000
});

// 永不过期
storage.set('appInstallId', 'abc123', { expiry: null });
```

### 获取数据

```javascript
// 获取数据，如果不存在或已过期则返回null
const preferences = storage.get('userPreferences');

// 指定默认值
const preferences = storage.get('userPreferences', { theme: 'light', fontSize: 'small' });
```

### 删除数据

```javascript
// 删除单个存储项
storage.remove('userPreferences');

// 清除所有带前缀的存储项
storage.clear();

// 清除所有存储项(包括其他前缀的)
storage.clear(false);
```

### 检查数据是否存在

```javascript
if (storage.has('userPreferences')) {
  // 数据存在且未过期
}
```

### 获取存储信息

```javascript
const info = storage.getInfo();
console.log(`当前存储使用情况: ${info.currentSize}KB / ${info.limitSize}KB`);
```

## 创建自定义存储实例

对于不同的数据类型，可以创建不同的存储实例，使用不同的前缀和过期时间：

```javascript
// 创建用户数据存储实例
const userStorage = new StorageService({
  prefix: 'user',
  defaultExpiry: 7 * 24 * 60 * 60 * 1000, // 7天
  enableLogging: true
});

// 创建缓存数据存储实例
const cacheStorage = new StorageService({
  prefix: 'cache',
  defaultExpiry: 30 * 60 * 1000, // 30分钟
  enableLogging: true
});
```

## 最佳实践

1. **使用前缀分类数据**：为不同类型的数据使用不同的存储实例和前缀
2. **设置合理的过期时间**：根据数据的重要性和时效性设置过期时间
3. **处理存储失败**：存储操作可能因存储空间不足等原因失败，应当处理返回值
4. **避免存储大量数据**：小程序的存储空间有限，避免存储大量数据
5. **定期清理**：定期调用`cleanExpired()`方法清理过期数据

## 从旧版本迁移

如果你正在使用直接调用`wx.setStorageSync`等API的代码，可以按照以下步骤迁移：

1. 导入存储服务：`import { storage } from 'utils/storage';`
2. 替换`wx.setStorageSync(key, value)`为`storage.set(key, value)`
3. 替换`wx.getStorageSync(key)`为`storage.get(key)`
4. 替换`wx.removeStorageSync(key)`为`storage.remove(key)`
5. 替换`wx.clearStorageSync()`为`storage.clear(false)`

## 示例

### 用户设置管理

```javascript
// 保存用户设置
function saveUserSettings(settings) {
  return storage.set('userSettings', settings);
}

// 获取用户设置
function getUserSettings() {
  return storage.get('userSettings', {
    theme: 'light',
    notifications: true,
    fontSize: 'medium'
  });
}

// 更新单个设置
function updateSetting(key, value) {
  const settings = getUserSettings();
  settings[key] = value;
  return saveUserSettings(settings);
}
```

### 缓存管理

```javascript
// 创建缓存存储实例
const cacheStorage = new StorageService({
  prefix: 'api_cache',
  defaultExpiry: 10 * 60 * 1000, // 10分钟
  enableLogging: true
});

// 缓存API响应
function cacheApiResponse(endpoint, params, data) {
  const key = `${endpoint}_${JSON.stringify(params)}`;
  return cacheStorage.set(key, data);
}

// 获取缓存的API响应
function getCachedApiResponse(endpoint, params) {
  const key = `${endpoint}_${JSON.stringify(params)}`;
  return cacheStorage.get(key);
}
```
