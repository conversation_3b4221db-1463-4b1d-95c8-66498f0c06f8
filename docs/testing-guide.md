# AIBUBB 测试指南

本文档提供了关于如何运行测试、提高测试覆盖率以及集成测试到开发流程的指南。

## 目录

- [测试框架](#测试框架)
- [运行测试](#运行测试)
- [编写测试](#编写测试)
- [测试覆盖率](#测试覆盖率)
- [CI/CD集成](#cicd集成)
- [最佳实践](#最佳实践)

## 测试框架

AIBUBB项目使用以下测试工具：

- **Jest**: 主要测试框架，用于运行单元测试和生成覆盖率报告
- **miniprogram-simulate**: 微信小程序组件模拟器，用于测试小程序组件

## 运行测试

### 基本测试命令

```bash
# 运行所有测试
npm test

# 监视模式（文件更改时自动运行测试）
npm run test:watch

# 生成覆盖率报告
npm run test:coverage

# 运行测试并生成覆盖率报告（CI环境）
npm run test:ci

# 检查测试覆盖率是否达到目标
npm run test:check-coverage

# 生成自定义覆盖率报告
npm run test:report
```

### 运行特定测试

```bash
# 运行特定文件的测试
npx jest path/to/test-file.test.js

# 运行匹配特定模式的测试
npx jest -t "test pattern"
```

### 使用自定义脚本

项目提供了一个自定义测试运行脚本，支持更多选项：

```bash
# 运行所有测试
node scripts/run-tests.js all

# 运行特定模式的测试
node scripts/run-tests.js all "api-client"

# 运行测试并生成覆盖率报告
node scripts/run-tests.js all --coverage
```

## 编写测试

### 测试文件位置

测试文件应放在`tests`目录中，与被测试的源文件保持相同的目录结构：

```
src/utils/api-client.js  -> tests/utils/api-client.test.js
```

### 测试文件命名

测试文件应以`.test.js`或`.spec.js`结尾。

### 基本测试结构

```javascript
/**
 * 测试描述
 */
describe('模块名称', () => {
  // 在每个测试前执行
  beforeEach(() => {
    // 设置测试环境
  });

  // 在每个测试后执行
  afterEach(() => {
    // 清理测试环境
  });

  // 测试用例
  test('应该正确执行某个功能', () => {
    // 准备测试数据
    const input = 'test';
    
    // 执行被测试的代码
    const result = someFunction(input);
    
    // 断言结果
    expect(result).toBe('expected output');
  });
});
```

### 模拟外部依赖

```javascript
// 模拟wx对象
global.wx = {
  request: jest.fn(),
  setStorageSync: jest.fn()
};

// 模拟模块
jest.mock('../../utils/api-client', () => ({
  get: jest.fn(),
  post: jest.fn()
}));

// 模拟函数实现
wx.request.mockImplementation(options => {
  setTimeout(() => {
    options.success({ data: { success: true } });
  }, 10);
});
```

## 测试覆盖率

### 覆盖率目标

项目设定了以下测试覆盖率目标：

- 语句覆盖率: 70%
- 分支覆盖率: 60%
- 函数覆盖率: 70%
- 行覆盖率: 70%

### 检查覆盖率

```bash
# 生成覆盖率报告
npm run test:coverage

# 检查覆盖率是否达到目标
npm run test:check-coverage

# 严格模式（未达到目标时返回错误码）
npm run test:check-coverage:strict
```

### 生成自定义覆盖率报告

```bash
# 生成自定义HTML覆盖率报告
npm run test:report
```

生成的报告位于`coverage/custom-report.html`，提供了更友好的界面和更详细的覆盖率信息。

## CI/CD集成

项目已配置GitHub Actions工作流，在以下情况下自动运行测试：

- 推送到`main`或`develop`分支
- 创建针对`main`或`develop`分支的Pull Request

工作流配置文件位于`.github/workflows/test.yml`。

### CI/CD流程

1. 检出代码
2. 设置Node.js环境
3. 安装依赖
4. 运行测试并生成覆盖率报告
5. 上传测试结果和覆盖率报告
6. 检查覆盖率是否达到目标

## 最佳实践

### 测试优先级

按照以下优先级编写测试：

1. 核心工具类（如`ApiClient`、`AuthService`、`TokenManager`等）
2. 关键业务逻辑（如学习计划、笔记、标签等功能）
3. UI组件（使用`miniprogram-simulate`）

### 测试策略

- **单元测试**: 测试单个函数或类的行为
- **集成测试**: 测试多个组件或模块之间的交互
- **快照测试**: 测试UI组件的渲染输出

### 提高测试覆盖率的技巧

1. 使用测试驱动开发（TDD）方法
2. 关注边界条件和异常情况
3. 使用参数化测试减少重复代码
4. 定期运行覆盖率报告，找出未覆盖的代码
5. 在代码审查中检查测试覆盖率

### 避免的做法

- 不要为了提高覆盖率而编写无意义的测试
- 不要测试第三方库或框架的内部实现
- 不要在测试中使用真实的API调用或数据库连接
- 不要编写脆弱的测试（依赖于特定的实现细节）
