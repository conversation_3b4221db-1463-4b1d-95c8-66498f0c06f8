#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from PIL import Image, ImageDraw

# 创建图标保存目录
ICON_DIR = "assets/icons/new"
os.makedirs(ICON_DIR, exist_ok=True)

# 图标尺寸
ICON_SIZE = 64
ICON_BG_COLOR = (255, 255, 255, 0)  # 透明背景

# 蓝色
BLUE_COLOR = (59, 130, 246)  # #3B82F6
BLUE_ACTIVE_COLOR = (29, 100, 216)  # 深蓝色

def create_book_icon(color):
    """创建书本图标"""
    # 创建基础图像
    img = Image.new('RGBA', (ICON_SIZE, ICON_SIZE), ICON_BG_COLOR)
    draw = ImageDraw.Draw(img)
    
    # 书本外形
    margin = 10
    width = ICON_SIZE - 2 * margin
    height = ICON_SIZE - 2 * margin
    
    # 书本主体
    points = [
        (margin, margin),  # 左上
        (margin + width, margin),  # 右上
        (margin + width, margin + height),  # 右下
        (margin, margin + height),  # 左下
    ]
    draw.polygon(points, fill=color)
    
    # 书脊
    spine_width = width // 8
    spine_points = [
        (margin, margin),  # 左上
        (margin + spine_width, margin + 2),  # 右上
        (margin + spine_width, margin + height - 2),  # 右下
        (margin, margin + height),  # 左下
    ]
    draw.polygon(spine_points, fill=tuple(max(0, c - 30) for c in color))
    
    # 书页线条
    line_color = (255, 255, 255, 180)
    for i in range(3):
        y = margin + 15 + i * 12
        draw.line([(margin + spine_width + 5, y), (margin + width - 5, y)], fill=line_color, width=2)
    
    return img

# 生成普通图标
book_icon = create_book_icon(BLUE_COLOR)
book_icon.save(os.path.join(ICON_DIR, "book.png"))
print(f"创建图标: {os.path.join(ICON_DIR, 'book.png')}")

# 生成激活图标
book_active_icon = create_book_icon(BLUE_ACTIVE_COLOR)
book_active_icon.save(os.path.join(ICON_DIR, "book-active.png"))
print(f"创建激活图标: {os.path.join(ICON_DIR, 'book-active.png')}")

print("完成！") 