# DDD基础设施完善指南

## 1. 概述

本文档详细描述了AIBUBB系统中领域驱动设计(DDD)基础设施的完善方案。基础设施层是DDD架构的重要组成部分，为领域层和应用层提供技术支持，包括持久化、事件处理、依赖注入等功能。

本文档将介绍以下几个方面的完善工作：

1. 领域事件机制的完善
2. 工作单元实现的增强
3. 依赖注入容器的优化
4. 仓库基类的完善

## 2. 领域事件机制完善

### 2.1 现有问题

当前的领域事件机制存在以下问题：

- 事件发布可靠性不足，事件持久化失败时仍会继续执行事件发布
- 缺乏事件处理重试机制和死信队列管理
- 事件监控和告警功能不完善

### 2.2 解决方案

#### 2.2.1 增强版事件总线

创建`EnhancedEventBusImpl`类，提供以下功能：

- 事件处理失败时的重试机制，使用指数退避策略
- 与事件监控服务集成，记录事件处理状态
- 与死信队列服务集成，处理最终失败的事件

```typescript
// infrastructure/events/EnhancedEventBusImpl.ts
export class EnhancedEventBusImpl implements EventBus {
  constructor(
    private readonly logger: Logger,
    private readonly monitoringService?: EventMonitoringService,
    private readonly deadLetterQueueService?: DeadLetterQueueService,
    private readonly retryConfig?: Partial<{
      maxRetries: number;
      initialDelay: number;
      maxDelay: number;
      backoffFactor: number;
    }>
  ) {
    // ...
  }
  
  // 实现事件处理重试机制
  private async processEventWithRetry(
    handler: EventHandler,
    event: any,
    eventType: string,
    eventId: string
  ): Promise<void> {
    // 使用指数退避策略重试
    // ...
  }
}
```

#### 2.2.2 增强版事件发布者

创建`EnhancedEventPublisherImpl`类，提供以下功能：

- 确保事件先持久化再发布
- 事件发布失败时的重试机制
- 与事件监控服务集成，记录事件发布状态

```typescript
// infrastructure/events/EnhancedEventPublisherImpl.ts
export class EnhancedEventPublisherImpl implements EventPublisher {
  constructor(
    private readonly eventBus: EventBus,
    private readonly logger: Logger,
    private readonly eventStore?: EventStore,
    private readonly retryConfig?: Partial<{
      maxRetries: number;
      initialDelay: number;
      maxDelay: number;
      backoffFactor: number;
    }>
  ) {
    // ...
  }
  
  // 确保事件先持久化再发布
  async publish(event: DomainEvent): Promise<void> {
    // 先存储事件
    if (this.eventStore) {
      try {
        await this.retryOperation(
          () => this.eventStore!.store(event),
          `存储事件 ${event.eventType}`
        );
      } catch (error) {
        // 存储失败时，不继续发布事件
        throw new Error(`事件存储失败，无法发布事件: ${error.message}`);
      }
    }
    
    // 再发布事件
    // ...
  }
}
```

#### 2.2.3 死信队列服务

创建`DeadLetterQueueService`类，提供以下功能：

- 存储最终失败的事件
- 提供重试和解决事件的接口
- 提供查询和清理死信队列的功能

```typescript
// infrastructure/events/DeadLetterQueueService.ts
export class DeadLetterQueueService {
  constructor(
    private readonly sequelize: Sequelize,
    private readonly logger: Logger
  ) {
    // 初始化死信队列模型
  }
  
  // 添加事件到死信队列
  async addToDeadLetterQueue(
    eventId: string,
    eventType: string,
    eventData: any,
    handlerName: string,
    errorMessage: string
  ): Promise<string> {
    // ...
  }
  
  // 重试死信队列中的事件
  async retryEvent(id: string, eventBus: any): Promise<boolean> {
    // ...
  }
}
```

#### 2.2.4 事件监控服务

创建`EventMonitoringService`类，提供以下功能：

- 记录事件发布和处理状态
- 提供事件处理性能统计
- 提供事件处理失败告警

```typescript
// infrastructure/monitoring/EventMonitoringService.ts
export class EventMonitoringService {
  constructor(private readonly logger: Logger) {
    // ...
  }
  
  // 记录事件发布
  recordEventPublished(eventId: string, eventType: string, event: any): void {
    // ...
  }
  
  // 记录事件处理
  recordEventProcessed(
    eventId: string,
    eventType: string,
    handlerName: string,
    success: boolean,
    error?: Error
  ): void {
    // ...
  }
}
```

## 3. 工作单元实现增强

### 3.1 现有问题

当前的工作单元实现存在以下问题：

- 缺乏聚合根跟踪功能，无法自动管理聚合根的变更
- 手动事务管理容易出错，依赖开发者在所有代码路径正确处理`commit`和`rollback`
- 缺乏事务嵌套支持

### 3.2 解决方案

#### 3.2.1 增强版工作单元接口

创建`EnhancedUnitOfWork`接口，扩展基本工作单元接口：

```typescript
// infrastructure/persistence/EnhancedUnitOfWork.ts
export interface EnhancedUnitOfWork extends UnitOfWork {
  // 注册新建的聚合根
  registerNew<T extends AggregateRoot>(aggregateRoot: T): void;
  
  // 注册修改的聚合根
  registerDirty<T extends AggregateRoot>(aggregateRoot: T): void;
  
  // 注册删除的聚合根
  registerRemoved<T extends AggregateRoot>(aggregateRoot: T): void;
  
  // 注册仓库
  registerRepository<T extends AggregateRoot>(repository: Repository<T>): void;
  
  // 提交所有注册的变更
  commitChanges(): Promise<void>;
  
  // 获取当前工作单元的ID
  getId(): string;
  
  // 获取当前工作单元的状态
  getStatus(): {
    newCount: number;
    dirtyCount: number;
    removedCount: number;
    repositoryCount: number;
  };
  
  // 清除当前工作单元的所有注册
  clear(): void;
}
```

#### 3.2.2 增强版Sequelize工作单元实现

创建`EnhancedSequelizeUnitOfWork`类，实现增强版工作单元接口：

```typescript
// infrastructure/persistence/EnhancedSequelizeUnitOfWork.ts
export class EnhancedSequelizeUnitOfWork implements EnhancedUnitOfWork {
  private id: string;
  private transaction: Transaction | null = null;
  private newAggregates: Set<AggregateRoot> = new Set();
  private dirtyAggregates: Set<AggregateRoot> = new Set();
  private removedAggregates: Set<AggregateRoot> = new Set();
  private repositories: Set<Repository<any>> = new Set();
  
  constructor(
    private readonly sequelize: Sequelize,
    private readonly logger: Logger,
    private readonly eventPublisher: EventPublisher
  ) {
    this.id = uuidv4();
  }
  
  // 在事务中运行工作
  async runInTransaction<T>(work: () => Promise<T>): Promise<T> {
    const isOuterTransaction = !this.transaction;
    
    if (isOuterTransaction) {
      await this.begin();
    }

    try {
      const result = await work();
      
      if (isOuterTransaction) {
        // 提交所有注册的变更
        await this.commitChanges();
        // 提交事务
        await this.commit();
      }
      
      return result;
    } catch (error) {
      if (isOuterTransaction && this.transaction) {
        await this.rollback();
      }
      throw error;
    } finally {
      if (isOuterTransaction) {
        this.clear();
      }
    }
  }
  
  // 提交所有注册的变更
  async commitChanges(): Promise<void> {
    // 使用注册的仓库保存聚合根
    // 发布所有聚合根的领域事件
    // ...
  }
}
```

## 4. 依赖注入容器优化

### 4.1 现有问题

当前的依赖注入容器存在以下问题：

- 容器配置分散，难以维护
- 缺乏生命周期管理功能
- 缺乏子容器支持

### 4.2 解决方案

#### 4.2.1 增强版容器接口

创建`EnhancedContainer`接口，扩展基本容器接口：

```typescript
// infrastructure/di/EnhancedContainer.ts
export interface EnhancedContainer extends Container {
  // 绑定接口到实现
  bindInterface<T>(token: string | symbol, implementation: string | symbol, options?: any): void;
  
  // 绑定工厂函数
  bindFactory<T>(token: string | symbol, factory: (container: EnhancedContainer) => T, options?: any): void;
  
  // 绑定常量值
  bindConstant<T>(token: string | symbol, value: T): void;
  
  // 创建子容器
  createChildContainer(): EnhancedContainer;
  
  // 获取所有绑定的标识符
  getAllBindings(): (string | symbol)[];
  
  // 解析类型
  resolve<T>(constructor: new (...args: any[]) => T): T;
  
  // 获取容器的唯一标识符
  getId(): string;
  
  // 获取容器的名称
  getName(): string;
  
  // 设置容器的名称
  setName(name: string): void;
  
  // 获取容器的父容器
  getParent(): EnhancedContainer | null;
  
  // 获取容器的所有子容器
  getChildren(): EnhancedContainer[];
  
  // 销毁容器
  dispose(): void;
}
```

#### 4.2.2 增强版容器实现

创建`EnhancedContainerImpl`类，实现增强版容器接口：

```typescript
// infrastructure/di/EnhancedContainerImpl.ts
export class EnhancedContainerImpl implements EnhancedContainer {
  private id: string;
  private name: string;
  private parent: EnhancedContainerImpl | null = null;
  private children: EnhancedContainerImpl[] = [];
  private bindings: Map<string | symbol, Binding> = new Map();
  
  constructor(name: string = 'root', private readonly logger?: Logger) {
    this.id = uuidv4();
    this.name = name;
  }
  
  // 解析类型
  resolve<T>(constructor: new (...args: any[]) => T): T {
    // 获取构造函数的参数类型
    const paramTypes = Reflect.getMetadata('design:paramtypes', constructor) || [];
    
    // 解析每个参数
    const params = paramTypes.map((paramType: any, index: number) => {
      // 获取参数的注入标识符
      const paramToken = Reflect.getMetadata('inject', constructor, `param:${index}`) || paramType;
      
      // 如果有绑定，则使用绑定的值
      if (this.has(paramToken)) {
        return this.get(paramToken);
      }
      
      // 如果参数类型是一个类，则递归解析
      if (typeof paramType === 'function' && this.isClass(paramType)) {
        return this.resolve(paramType);
      }
      
      // 如果无法解析参数，抛出错误
      throw new Error(`无法解析 ${constructor.name} 的参数 ${index}: ${paramType?.name || '未知类型'}`);
    });
    
    // 创建实例
    return new constructor(...params);
  }
}
```

#### 4.2.3 增强版容器配置器

创建`EnhancedContainerConfigurator`类，集中管理容器配置：

```typescript
// infrastructure/di/EnhancedContainerConfigurator.ts
export class EnhancedContainerConfigurator {
  // 创建并配置容器
  static createContainer(): EnhancedContainer {
    const container = new EnhancedContainerImpl('root');
    this.configureInfrastructure(container);
    return container;
  }
  
  // 配置基础设施
  static configureInfrastructure(container: EnhancedContainer): void {
    // 配置日志记录器
    // 配置Sequelize
    // 配置工作单元
    // 配置事件总线
    // 配置事件存储
    // 配置事件发布者
    // 配置事件监控服务
    // 配置死信队列服务
    // 配置事件处理器注册表
  }
  
  // 配置学习内容领域
  static configureContentDomain(container: EnhancedContainer): void {
    // 配置练习仓库
    // 配置笔记仓库
    // 配置学习计划仓库
    // 配置主题仓库
  }
  
  // 配置标签领域
  static configureTagDomain(container: EnhancedContainer): void {
    // 配置标签仓库
    // 配置标签分类仓库
  }
  
  // 配置用户领域
  static configureUserDomain(container: EnhancedContainer): void {
    // 配置用户仓库
    // 配置用户设置仓库
    // 配置角色仓库
    // 配置权限仓库
  }
}
```

## 5. 仓库基类完善

### 5.1 现有问题

当前的仓库基类存在以下问题：

- 缺乏通用的查询方法
- 缺乏分页支持
- 缺乏批量操作支持
- 缺乏日志记录

### 5.2 解决方案

#### 5.2.1 增强版仓库基类

创建`EnhancedRepositoryBase`类，提供更多功能和更好的类型安全：

```typescript
// infrastructure/persistence/repositories/EnhancedRepositoryBase.ts
export abstract class EnhancedRepositoryBase<T extends AggregateRoot, ID = any> implements Repository<T, ID> {
  constructor(
    protected readonly unitOfWork: UnitOfWork,
    protected readonly eventPublisher: EventPublisher,
    protected readonly logger: Logger
  ) {}
  
  // 保存实体
  async save(entity: T): Promise<T> {
    this.logger.debug(`保存实体: ${entity.constructor.name}#${entity.id}`);
    
    const savedEntity = await this.doSave(entity);
    
    // 发布领域事件
    if (entity.domainEvents.length > 0) {
      this.logger.debug(`发布实体事件: ${entity.constructor.name}#${entity.id}, ${entity.domainEvents.length}个事件`);
      await this.eventPublisher.publishAll(entity.domainEvents);
      entity.clearEvents();
    }
    
    return savedEntity;
  }
  
  // 分页查询
  async findWithPagination(page: number, pageSize: number): Promise<{
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    // 默认实现，子类可以覆盖
  }
  
  // 根据条件查询
  abstract findByCriteria(criteria: any): Promise<T[]>;
  
  // 根据条件查询单个实体
  async findOneByCriteria(criteria: any): Promise<T | null> {
    const results = await this.findByCriteria(criteria);
    return results.length > 0 ? results[0] : null;
  }
  
  // 批量保存实体
  async saveAll(entities: T[]): Promise<T[]> {
    const savedEntities: T[] = [];
    
    for (const entity of entities) {
      const savedEntity = await this.save(entity);
      savedEntities.push(savedEntity);
    }
    
    return savedEntities;
  }
  
  // 在事务中运行操作
  protected async runInTransaction<R>(operation: () => Promise<R>): Promise<R> {
    return this.unitOfWork.runInTransaction(operation);
  }
}
```

## 6. 实施步骤

### 6.1 领域事件机制完善

1. 创建`Logger`接口和`ConsoleLogger`实现
2. 创建`EventMonitoringService`类
3. 创建`DeadLetterQueueService`类
4. 创建`EnhancedEventBusImpl`类
5. 创建`EnhancedEventPublisherImpl`类
6. 更新容器配置，注册新的实现

### 6.2 工作单元实现增强

1. 创建`EnhancedUnitOfWork`接口
2. 创建`EnhancedSequelizeUnitOfWork`类
3. 更新容器配置，注册新的实现

### 6.3 依赖注入容器优化

1. 创建`EnhancedContainer`接口
2. 创建`EnhancedContainerImpl`类
3. 创建`EnhancedContainerConfigurator`类
4. 更新应用入口，使用新的容器

### 6.4 仓库基类完善

1. 更新`Repository`接口
2. 创建`EnhancedRepositoryBase`类
3. 创建领域特定的仓库实现（如`EnhancedSequelizeExerciseRepository`）
4. 更新容器配置，注册新的实现

## 7. 测试策略

### 7.1 单元测试

为每个新组件编写单元测试，确保其功能正确：

- 测试`EnhancedEventBusImpl`的事件发布和处理
- 测试`EnhancedEventPublisherImpl`的事件发布可靠性
- 测试`DeadLetterQueueService`的死信队列管理
- 测试`EnhancedSequelizeUnitOfWork`的事务管理和聚合根跟踪
- 测试`EnhancedContainerImpl`的依赖注入和生命周期管理
- 测试`EnhancedRepositoryBase`的通用查询和批量操作

### 7.2 集成测试

编写集成测试，验证组件之间的协作：

- 测试事件发布、持久化和处理的完整流程
- 测试工作单元和仓库的协作
- 测试容器配置和依赖注入

## 8. 总结

通过完善DDD基础设施，我们可以提高系统的可靠性、可维护性和可扩展性。这些改进将为领域层和应用层提供更好的技术支持，使开发人员能够更专注于业务逻辑的实现。

主要改进包括：

1. 增强领域事件机制，提高事件处理的可靠性
2. 增强工作单元实现，简化事务管理
3. 优化依赖注入容器，提高配置的可维护性
4. 完善仓库基类，提供更多通用功能

这些改进将为AIBUBB系统的DDD实施提供坚实的基础，支持系统的持续演进和扩展。
