# AIBUBB领域驱动设计开发指南

## 1. 概述

本文档提供了AIBUBB系统中领域驱动设计(DDD)的开发指南，旨在帮助开发团队理解和使用新的DDD架构和基础设施。

## 2. 架构概览

AIBUBB系统采用领域驱动设计架构，分为以下几层：

1. **领域层（Domain Layer）**：包含业务核心概念、实体、值对象、聚合根和领域事件。
2. **应用层（Application Layer）**：协调领域对象完成用户任务，包含应用服务和命令/查询处理器。
3. **基础设施层（Infrastructure Layer）**：提供技术支持，包括持久化、消息传递、依赖注入等。
4. **接口层（Interface Layer）**：处理用户交互，包括API控制器、视图模型等。

## 3. 领域层开发指南

### 3.1 实体和聚合根

实体是具有唯一标识的对象，聚合根是实体的一种特殊类型，作为聚合的入口点。

```typescript
// 实体基类
export abstract class Entity<ID> {
  constructor(public id: ID) {}
  
  equals(other: Entity<ID>): boolean {
    if (other === null || other === undefined) {
      return false;
    }
    if (this === other) {
      return true;
    }
    return this.id === other.id;
  }
}

// 聚合根基类
export abstract class AggregateRoot extends Entity<number> {
  private _domainEvents: DomainEvent[] = [];
  
  get domainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }
  
  protected addDomainEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }
  
  clearEvents(): void {
    this._domainEvents = [];
  }
}
```

### 3.2 值对象

值对象是没有唯一标识的对象，通过其属性值来区分。

```typescript
// 值对象示例
export class Address {
  constructor(
    public readonly street: string,
    public readonly city: string,
    public readonly state: string,
    public readonly zipCode: string,
    public readonly country: string
  ) {}
  
  equals(other: Address): boolean {
    if (other === null || other === undefined) {
      return false;
    }
    return (
      this.street === other.street &&
      this.city === other.city &&
      this.state === other.state &&
      this.zipCode === other.zipCode &&
      this.country === other.country
    );
  }
}
```

### 3.3 领域事件

领域事件表示领域中发生的重要事件，用于解耦聚合之间的通信。

```typescript
// 领域事件接口
export interface DomainEvent {
  readonly eventType: string;
  readonly occurredOn: Date;
}

// 领域事件示例
export class ExerciseCreatedEvent implements DomainEvent {
  readonly eventType = 'exercise.created';
  readonly occurredOn: Date;
  
  constructor(
    public readonly exerciseId: number,
    public readonly title: string,
    public readonly creatorId: string
  ) {
    this.occurredOn = new Date();
  }
}
```

## 4. 应用层开发指南

### 4.1 应用服务

应用服务协调领域对象完成用户任务，通常对应于用例。

```typescript
// 应用服务示例
export class ExerciseService {
  constructor(
    private readonly exerciseRepository: ExerciseRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}
  
  async createExercise(command: CreateExerciseCommand): Promise<number> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = new Exercise(
        0, // 新建实体ID为0
        command.title,
        command.description,
        command.expectedResult,
        command.difficulty,
        command.timeEstimateMinutes,
        command.creatorId,
        ContentStatus.DRAFT,
        Visibility.PRIVATE,
        false
      );
      
      // 添加标签
      command.tags.forEach(tag => exercise.addTag(tag));
      
      // 保存实体
      const savedExercise = await this.exerciseRepository.save(exercise);
      
      return savedExercise.id;
    });
  }
}
```

### 4.2 命令和查询

遵循命令查询职责分离(CQRS)原则，将命令和查询分开处理。

```typescript
// 命令示例
export class CreateExerciseCommand {
  constructor(
    public readonly title: string,
    public readonly description: string,
    public readonly expectedResult: string,
    public readonly difficulty: Difficulty,
    public readonly timeEstimateMinutes: number,
    public readonly creatorId: string,
    public readonly tags: string[]
  ) {}
}

// 查询示例
export class GetExerciseByIdQuery {
  constructor(public readonly id: number) {}
}
```

## 5. 基础设施层开发指南

### 5.1 仓库实现

仓库负责持久化和检索聚合根。

```typescript
// 使用增强版仓库基类实现仓库
export class EnhancedSequelizeExerciseRepository extends EnhancedRepositoryBase<Exercise, number> implements ExerciseRepository {
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    logger: Logger,
    private readonly sequelize: Sequelize,
    private readonly exerciseModel: any,
    private readonly exerciseTagModel: any
  ) {
    super(unitOfWork, eventPublisher, logger);
  }
  
  // 实现特定领域的查询方法
  async findByTagId(tagId: number): Promise<Exercise[]> {
    // 实现代码...
  }
  
  // 实现抽象方法
  protected async doSave(exercise: Exercise): Promise<Exercise> {
    // 实现代码...
  }
  
  protected async doDelete(exercise: Exercise): Promise<void> {
    // 实现代码...
  }
}
```

### 5.2 工作单元

工作单元管理事务和聚合根的变更跟踪。

```typescript
// 使用工作单元
async function createExerciseWithTags(
  exercise: Exercise,
  tags: string[],
  unitOfWork: EnhancedUnitOfWork,
  exerciseRepository: ExerciseRepository
): Promise<Exercise> {
  return unitOfWork.runInTransaction(async () => {
    // 注册仓库
    unitOfWork.registerRepository(exerciseRepository);
    
    // 注册新建的聚合根
    unitOfWork.registerNew(exercise);
    
    // 添加标签
    tags.forEach(tag => exercise.addTag(tag));
    
    // 提交所有变更
    await unitOfWork.commitChanges();
    
    return exercise;
  });
}
```

### 5.3 事件处理

使用事件总线和事件处理器处理领域事件。

```typescript
// 事件处理器示例
export class ExerciseCreatedEventHandler implements EventHandler {
  async handle(event: ExerciseCreatedEvent): Promise<void> {
    // 处理事件...
  }
}

// 注册事件处理器
eventBus.register('exercise.created', new ExerciseCreatedEventHandler());
```

### 5.4 依赖注入

使用容器管理依赖注入。

```typescript
// 获取容器
const container = ContainerProvider.getInstance().getContainer();

// 获取服务
const exerciseService = container.get<ExerciseService>('exerciseService');

// 使用服务
const exerciseId = await exerciseService.createExercise(command);
```

## 6. 接口层开发指南

### 6.1 控制器

控制器处理HTTP请求，调用应用服务，并返回响应。

```typescript
// 控制器示例
export class ExerciseController {
  constructor(private readonly exerciseService: ExerciseService) {}
  
  async createExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command = new CreateExerciseCommand(
        req.body.title,
        req.body.description,
        req.body.expectedResult,
        req.body.difficulty,
        req.body.timeEstimateMinutes,
        req.user.id,
        req.body.tags || []
      );
      
      const exerciseId = await this.exerciseService.createExercise(command);
      
      res.status(201).json({ id: exerciseId });
    } catch (error) {
      next(error);
    }
  }
}
```

### 6.2 视图模型

视图模型用于将领域模型转换为适合UI展示的格式。

```typescript
// 视图模型示例
export class ExerciseViewModel {
  constructor(
    public readonly id: number,
    public readonly title: string,
    public readonly description: string,
    public readonly expectedResult: string,
    public readonly difficulty: string,
    public readonly timeEstimateMinutes: number,
    public readonly creatorId: string,
    public readonly status: string,
    public readonly tags: string[],
    public readonly createdAt: Date,
    public readonly updatedAt: Date
  ) {}
  
  static fromDomain(exercise: Exercise): ExerciseViewModel {
    return new ExerciseViewModel(
      exercise.id,
      exercise.title,
      exercise.description,
      exercise.expectedResult,
      this.mapDifficultyToString(exercise.difficulty),
      exercise.timeEstimateMinutes,
      exercise.creatorId,
      this.mapStatusToString(exercise.status),
      exercise.tags,
      exercise.createdAt,
      exercise.updatedAt
    );
  }
  
  private static mapDifficultyToString(difficulty: Difficulty): string {
    // 映射代码...
  }
  
  private static mapStatusToString(status: ContentStatus): string {
    // 映射代码...
  }
}
```

## 7. 最佳实践

### 7.1 领域模型设计

- 使用充血模型，将行为和数据放在一起
- 确保聚合边界清晰，聚合之间通过ID引用
- 使用值对象表示没有唯一标识的概念
- 使用领域事件进行聚合之间的通信

### 7.2 仓库实现

- 使用增强版仓库基类简化实现
- 为特定领域需求添加自定义查询方法
- 确保仓库方法名称清晰表达意图
- 使用工作单元管理事务和变更跟踪

### 7.3 事件处理

- 使用领域事件解耦聚合
- 确保事件处理器是幂等的
- 使用事件存储持久化事件
- 使用死信队列处理失败的事件

### 7.4 依赖注入

- 使用容器管理依赖
- 按领域组织容器配置
- 使用接口而非具体实现
- 遵循依赖倒置原则

## 8. 迁移指南

从旧架构迁移到DDD架构时，请遵循以下步骤：

1. 识别领域概念和边界
2. 创建领域模型（实体、值对象、聚合根）
3. 实现仓库接口
4. 创建应用服务
5. 更新控制器使用新的应用服务
6. 逐步替换旧的实现

## 9. 测试策略

### 9.1 单元测试

- 测试领域模型的行为
- 使用模拟对象测试应用服务
- 测试仓库实现
- 测试事件处理器

### 9.2 集成测试

- 测试仓库与数据库的集成
- 测试事件发布和处理的完整流程
- 测试工作单元和事务管理

### 9.3 端到端测试

- 测试API端点
- 验证完整用例流程

## 10. 总结

领域驱动设计为AIBUBB系统提供了清晰的架构和设计方法，帮助我们构建更可维护、可扩展的系统。通过遵循本指南，开发团队可以更好地理解和使用DDD架构，提高代码质量和开发效率。
