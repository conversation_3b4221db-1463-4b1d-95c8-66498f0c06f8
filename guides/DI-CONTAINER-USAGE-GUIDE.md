# 依赖注入容器使用指南

## 1. 概述

本文档提供了AIBUBB项目中依赖注入(DI)容器的使用指南，旨在确保所有开发者遵循统一的DI容器使用规范，避免出现多个DI容器并存导致的问题。

## 2. 统一DI容器

AIBUBB项目使用统一的DI容器，位于`backend/infrastructure/di`目录下。该容器是一个适配器，内部使用了自定义容器(`ContainerImpl`)和InversifyJS容器，但对外提供了统一的接口。

### 2.1 导入容器

在任何需要使用DI容器的地方，都应该从统一的入口导入容器：

```typescript
import { container } from '../infrastructure/di';
```

**不要**从其他地方导入容器，例如：

```typescript
// 错误示例 - 不要这样做
import { container } from '../infrastructure/config/containerConfig';
import { container } from '../container';
```

### 2.2 注册依赖

注册依赖时，应该使用统一容器的接口：

```typescript
// 注册工厂函数
container.bind<MyService>('myService', (c) => {
  return new MyServiceImpl(
    c.get('dependency1'),
    c.get('dependency2')
  );
}, { singleton: true });

// 注册类
container.bindClass<MyService>('myService', MyServiceImpl, { singleton: true });

// 注册接口到实现
container.bindInterface<MyInterface>('myInterface', 'myImplementation', { singleton: true });
```

### 2.3 获取依赖

获取依赖时，应该使用统一容器的接口：

```typescript
const myService = container.get<MyService>('myService');
```

## 3. 模块配置

### 3.1 领域模块配置

每个领域模块都应该有自己的容器配置文件，例如`gamification.container.ts`、`learningTemplate.container.ts`等。这些配置文件应该导出一个配置函数，接受统一容器作为参数：

```typescript
import { Container } from '../infrastructure/config/Container';

export function configureMyDomain(container: Container): void {
  // 注册领域模块的依赖
  container.bind<MyRepository>('myRepository', (c) => {
    return new MyRepositoryImpl(
      c.get('unitOfWork'),
      c.get('eventPublisher')
    );
  }, { singleton: true });
  
  // 注册领域服务
  container.bind<MyDomainService>('myDomainService', (c) => {
    return new MyDomainServiceImpl(
      c.get('myRepository')
    );
  }, { singleton: true });
  
  // 注册应用服务
  container.bind<MyApplicationService>('myApplicationService', (c) => {
    return new MyApplicationServiceImpl(
      c.get('myRepository'),
      c.get('myDomainService'),
      c.get('unitOfWork')
    );
  }, { singleton: true });
}
```

### 3.2 统一容器入口

统一容器入口文件`backend/infrastructure/di/index.ts`会导入并初始化容器，并调用各个领域模块的配置函数：

```typescript
import { container } from './UnifiedContainer';
import { ContainerConfigurator } from './ContainerConfigurator';

// 初始化容器
ContainerConfigurator.initializeContainer(container);

// 导出容器
export { container };
```

## 4. 核心服务

以下核心服务是全局单例，应该通过统一容器获取：

### 4.1 UnitOfWork

`UnitOfWork`负责管理事务，确保所有数据库操作在同一个事务中执行：

```typescript
const unitOfWork = container.get<UnitOfWork>('unitOfWork');

// 开始事务
await unitOfWork.begin();

try {
  // 执行数据库操作
  
  // 提交事务
  await unitOfWork.commit();
} catch (error) {
  // 回滚事务
  await unitOfWork.rollback();
  throw error;
}
```

### 4.2 EventBus和EventPublisher

`EventBus`负责事件的发布和订阅，`EventPublisher`负责事件的发布：

```typescript
// 获取事件发布者
const eventPublisher = container.get<EventPublisher>('eventPublisher');

// 发布事件
await eventPublisher.publish(new MyEvent(aggregateId, payload));
```

## 5. 事件处理器

事件处理器应该继承`EventHandlerBase`，并实现`processEvent`和`sendNotification`方法：

```typescript
import { EventHandlerBase } from '../infrastructure/events/EventHandlerBase';
import { MyEvent } from '../domain/events/MyEvent';

export class MyEventHandler extends EventHandlerBase<MyEvent> {
  protected async processEvent(event: MyEvent): Promise<void> {
    // 处理事件的业务逻辑
  }
  
  protected async sendNotification(event: MyEvent): Promise<void> {
    // 发送WebSocket通知
    await this.sendUserNotification(event.userId.toString(), {
      type: 'myEvent',
      message: `事件通知: ${event.message}`
    });
    
    // 如果需要广播，可以使用sendBroadcastNotification
    await this.sendBroadcastNotification({
      type: 'myBroadcastEvent',
      message: `广播通知: ${event.message}`
    });
  }
}
```

事件处理器需要在容器中注册，并在`EventHandlerRegistry`中注册：

```typescript
// 在容器中注册事件处理器
container.bind('myEventHandler', () => new MyEventHandler());

// 在EventHandlerRegistry中注册事件处理器
// 这通常在ContainerConfigurator中完成
const registry = container.get<EventHandlerRegistry>('eventHandlerRegistry');
registry.registerHandler('MyEvent', 'myEventHandler');
```

## 6. 最佳实践

### 6.1 使用接口

尽量使用接口而不是具体实现，这样可以更容易地替换实现：

```typescript
// 定义接口
export interface MyService {
  doSomething(): Promise<void>;
}

// 实现接口
export class MyServiceImpl implements MyService {
  async doSomething(): Promise<void> {
    // 实现
  }
}

// 注册接口到实现
container.bindClass<MyService>('myService', MyServiceImpl);
```

### 6.2 单例服务

对于无状态的服务，应该使用单例模式，避免不必要的实例创建：

```typescript
container.bind<MyService>('myService', (c) => {
  return new MyServiceImpl();
}, { singleton: true });
```

### 6.3 避免循环依赖

避免循环依赖，例如A依赖B，B依赖C，C依赖A。如果无法避免，可以使用延迟加载：

```typescript
container.bind<A>('a', (c) => {
  return new A(() => c.get('b'));
}, { singleton: true });
```

### 6.4 测试

在测试中，可以使用模拟对象替换真实依赖：

```typescript
// 创建模拟对象
const mockRepository = {
  findById: jest.fn(),
  save: jest.fn()
};

// 替换容器中的依赖
container.bind<MyRepository>('myRepository', () => mockRepository);

// 获取被测试的服务
const service = container.get<MyService>('myService');

// 执行测试
await service.doSomething();

// 验证模拟对象的方法是否被调用
expect(mockRepository.findById).toHaveBeenCalled();
```

## 7. 常见问题

### 7.1 找不到依赖

如果遇到"未找到标识符的注册"错误，可能是因为依赖没有注册或者注册的名称不正确：

```
Error: 未找到标识符的注册: myService
```

解决方法：

1. 检查依赖是否已注册
2. 检查注册的名称是否正确
3. 检查导入的容器是否正确

### 7.2 循环依赖

如果遇到循环依赖错误，可能是因为A依赖B，B依赖C，C依赖A：

```
Error: 循环依赖: a -> b -> c -> a
```

解决方法：

1. 重构代码，避免循环依赖
2. 使用延迟加载
3. 使用事件机制代替直接依赖

### 7.3 多个实例

如果发现同一个服务有多个实例，可能是因为从不同的容器获取了依赖：

解决方法：

1. 确保所有代码都从统一容器获取依赖
2. 检查是否有代码从其他容器获取依赖
3. 确保服务注册为单例

## 8. 结论

遵循本指南中的规范和最佳实践，可以避免DI容器相关的问题，提高代码的可维护性和可测试性。如果有任何疑问，请参考源代码或咨询团队成员。
