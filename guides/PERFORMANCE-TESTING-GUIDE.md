# AIBUBB性能测试指南

## 概述

本指南介绍如何使用AIBUBB项目中的性能测试工具进行各种类型的性能测试，包括负载测试、压力测试、长稳测试和并发测试。这些测试可以帮助我们评估系统在不同负载条件下的性能和稳定性。

## 前提条件

在开始性能测试之前，请确保：

1. 已安装k6测试工具：运行`./install-k6.sh`脚本或按照[k6安装指南](https://k6.io/docs/getting-started/installation/)手动安装
2. 已安装Docker和Docker Compose：用于启动性能测试环境
3. 已安装所有项目依赖：`npm install`

## 快速开始

以下是运行性能测试的基本步骤：

1. 启动性能测试环境：
   ```bash
   npm run perf:env
   ```

2. 运行负载测试：
   ```bash
   npm run perf:test:load
   ```

3. 查看测试结果：
   测试结果将保存在`backend/test-reports/performance-tests`目录中，并可以通过Grafana仪表板查看（http://localhost:13000）。

## 测试环境

性能测试环境包括以下组件：

- **MySQL数据库**：用于存储测试数据（端口：13306）
- **Redis缓存**：用于缓存测试数据（端口：16379）
- **Grafana**：用于可视化测试结果（端口：13000）
- **Prometheus**：用于收集和存储指标数据（端口：19090）
- **InfluxDB**：用于存储k6测试结果（端口：18086）

启动测试环境：
```bash
npm run perf:env
```

重置数据库并填充测试数据：
```bash
npm run perf:env:reset
```

## 测试类型

AIBUBB项目支持以下类型的性能测试：

### 1. 负载测试（Load Test）

负载测试用于评估系统在预期负载下的性能。它模拟正常使用条件下的用户行为，帮助我们了解系统的响应时间、吞吐量和资源利用率。

**运行方法**：
```bash
npm run perf:test:load
```

**测试场景**：
- 逐渐增加到50个虚拟用户
- 保持50个虚拟用户一段时间
- 逐渐减少到0个虚拟用户

### 2. 压力测试（Stress Test）

压力测试用于评估系统在极限负载下的表现。它通过快速增加用户数到一个极高的值，测试系统的稳定性和错误处理能力。

**运行方法**：
```bash
npm run perf:test:stress
```

**测试场景**：
- 逐渐增加到500个虚拟用户
- 保持500个虚拟用户一段时间
- 逐渐减少到0个虚拟用户

### 3. 长稳测试（Soak Test）

长稳测试用于评估系统在长时间运行下的稳定性。它通过长时间运行测试，检测内存泄漏、资源耗尽和性能下降等问题。

**运行方法**：
```bash
npm run perf:test:soak
```

**测试场景**：
- 逐渐增加到50个虚拟用户
- 保持50个虚拟用户10小时
- 逐渐减少到0个虚拟用户

### 4. 并发测试（Concurrency Test）

并发测试用于评估系统在高并发场景下的表现。它通过模拟多个用户同时访问系统，测试系统的并发处理能力。

**运行方法**：
```bash
npm run perf:test:concurrency
```

**测试场景**：
- 逐渐增加到100个虚拟用户
- 保持100个虚拟用户一段时间
- 逐渐增加到300个虚拟用户
- 保持300个虚拟用户一段时间
- 逐渐增加到500个虚拟用户
- 保持500个虚拟用户一段时间
- 逐渐减少到0个虚拟用户

## 快速测试模式

对于开发和调试目的，可以使用快速测试模式，它会缩短测试时间：

```bash
npm run perf:test:quick
```

这个命令会运行负载测试，但使用较短的测试时间。

## 自定义测试参数

可以通过命令行参数自定义测试参数：

```bash
./run-performance-tests.sh --test=load --base-url=http://localhost:3000 --api-version=v2
```

**可用参数**：
- `--test`: 测试类型（load, stress, soak, concurrency, all）
- `--base-url`: API基础URL
- `--api-version`: API版本
- `--output-dir`: 输出目录
- `--auth-token`: 认证令牌
- `--quick`: 快速模式

## 测试结果分析

测试完成后，你可以通过以下方式查看测试结果：

1. **控制台输出**：测试运行时会在控制台输出基本指标
2. **JSON文件**：测试结果会保存为JSON文件，位于`backend/test-reports/performance-tests`目录
3. **Grafana仪表板**：访问http://localhost:13000查看可视化的测试结果（默认用户名/密码：admin/admin）

可以使用分析工具生成测试报告：

```bash
node backend/scripts/analyze-performance-results.js
```

**可用参数**：
- `--input-dir`: 输入目录
- `--output`: 输出文件
- `--format`: 输出格式（html, json, markdown）

## 测试指标

性能测试会收集以下指标：

1. **响应时间**：
   - 平均响应时间
   - P95响应时间（95%的请求响应时间）
   - P99响应时间（99%的请求响应时间）

2. **吞吐量**：
   - 每秒请求数（RPS）
   - 每秒迭代数

3. **错误率**：
   - HTTP请求失败率
   - 自定义错误率

4. **资源利用率**：
   - 内存使用
   - CPU使用

## 测试阈值

每种测试类型都定义了阈值，用于评估测试是否通过：

1. **负载测试**：
   - 95%的请求响应时间小于500ms
   - 请求失败率小于1%
   - 自定义错误率小于5%

2. **压力测试**：
   - 95%的请求响应时间小于2秒
   - 请求失败率小于10%
   - 自定义错误率小于15%

3. **长稳测试**：
   - 95%的请求响应时间小于500ms
   - 99%的请求响应时间小于1秒
   - 请求失败率小于1%
   - 自定义错误率小于5%
   - 内存泄漏指标平均值小于100

4. **并发测试**：
   - 95%的请求响应时间小于1秒
   - 请求失败率小于5%
   - 自定义错误率小于10%
   - 用户列表接口95%的响应时间小于500ms
   - 创建笔记接口95%的响应时间小于800ms

## 测试场景

测试脚本模拟了以下用户场景：

1. **获取用户列表**：`GET /api/v2/users`
2. **获取特定用户**：`GET /api/v2/users/{id}`
3. **获取观点列表**：`GET /api/v2/insights`
4. **创建笔记**：`POST /api/v2/notes`
5. **更新笔记**：`PUT /api/v2/notes/{id}`
6. **获取练习列表**：`GET /api/v2/exercises`
7. **获取学习计划列表**：`GET /api/v2/learning-plans`
8. **获取主题列表**：`GET /api/v2/themes`
9. **获取标签列表**：`GET /api/v2/tags`

## 最佳实践

1. **定期运行测试**：定期运行性能测试，监控系统性能的变化
2. **测试环境**：在与生产环境类似的环境中进行测试
3. **测试数据**：使用与生产环境类似的测试数据
4. **监控资源**：在测试过程中监控系统资源使用情况
5. **分析结果**：分析测试结果，识别性能瓶颈
6. **持续改进**：根据测试结果持续改进系统性能

## 故障排除

### k6未安装

如果遇到"k6未安装或不在PATH中"的错误，请按照[k6安装指南](https://k6.io/docs/getting-started/installation/)安装k6。

### 测试失败

如果测试失败，可能是因为：

1. API服务器未启动
2. 基础URL不正确
3. API版本不正确
4. 认证令牌无效
5. 系统性能不符合预期

请检查这些因素并重新运行测试。

### 内存不足

长稳测试和压力测试可能会消耗大量内存。如果遇到内存不足的问题，可以：

1. 减少虚拟用户数
2. 缩短测试时间
3. 增加系统内存

## 扩展和定制

如果需要扩展或定制性能测试，可以修改以下文件：

- `backend/scripts/performance-tests/load-test.js`：负载测试脚本
- `backend/scripts/performance-tests/stress-test.js`：压力测试脚本
- `backend/scripts/performance-tests/soak-test.js`：长稳测试脚本
- `backend/scripts/performance-tests/concurrency-test.js`：并发测试脚本
- `backend/scripts/performance-tests/utils.js`：工具函数
- `backend/scripts/run-performance-tests.js`：测试运行脚本
- `backend/scripts/analyze-performance-results.js`：结果分析脚本

## 参考资料

- [k6文档](https://k6.io/docs/)
- [性能测试最佳实践](https://k6.io/docs/testing-guides/api-load-testing/)
- [k6指标](https://k6.io/docs/using-k6/metrics/)
- [k6阈值](https://k6.io/docs/using-k6/thresholds/)
