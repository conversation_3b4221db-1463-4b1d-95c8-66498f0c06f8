# 领域事件处理器实现指南

本文档提供了关于领域事件处理器的实现和使用说明。

## 概述

领域事件是DDD架构中的重要概念，用于捕捉业务中的重要变化，支持松耦合的系统集成。领域事件处理器负责处理这些事件，执行相应的业务逻辑，并可以通过WebSocket向前端发送实时通知。

## 架构

领域事件机制由以下组件组成：

1. **领域事件（DomainEvent）**：表示领域中发生的事件，包含事件ID、类型、发生时间等信息。
2. **事件发布者（EventPublisher）**：负责发布领域事件，由聚合根调用。
3. **事件总线（EventBus）**：负责事件的发布和订阅，是事件发布者和事件处理器之间的中介。
4. **事件处理器（EventHandler）**：负责处理特定类型的事件，执行相应的业务逻辑。
5. **事件处理器注册表（EventHandlerRegistry）**：负责注册所有事件处理器。
6. **WebSocket服务（WebSocketService）**：负责向前端发送实时通知。

## 实现的功能

本次实现了以下功能：

1. **事件处理器基类（EventHandlerBase）**：提供了通用的事件处理逻辑和WebSocket通知功能。
2. **事件处理器注册表（EventHandlerRegistry）**：负责注册所有事件处理器。
3. **具体事件处理器**：
   - ExerciseCompletedEventHandler：处理练习完成事件
   - UserLeveledUpEventHandler：处理用户升级事件
   - AchievementUnlockedEventHandler：处理成就解锁事件
4. **WebSocket通知**：通过WebSocketService向前端发送实时通知。

## 使用方法

### 创建新的领域事件

1. 在 `domain/events` 目录下创建新的事件类，继承 `DomainEvent` 接口：

```typescript
export class MyNewEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'MyNewEvent';
  readonly aggregateType: string = 'MyAggregate';
  readonly version: number = 1;

  constructor(
    readonly aggregateId: number,
    // 其他属性...
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  get payload(): any {
    return {
      // 返回事件数据...
    };
  }
}
```

### 创建新的事件处理器

1. 在 `application/events` 目录下创建新的事件处理器类，继承 `EventHandlerBase`：

```typescript
export class MyNewEventHandler extends EventHandlerBase<MyNewEvent> {
  protected async processEvent(event: MyNewEvent): Promise<void> {
    // 实现事件处理逻辑...
  }

  protected async sendNotification(event: MyNewEvent): Promise<void> {
    // 实现通知逻辑...
    await this.sendUserNotification(userId, {
      type: 'myNewEvent',
      // 其他数据...
    });
  }
}
```

### 注册事件处理器

1. 在 `infrastructure/config/containerConfig.ts` 的 `configureEventHandlers` 函数中注册新的事件处理器：

```typescript
// 导入事件处理器
const { MyNewEventHandler } = require('../../application/events/MyNewEventHandler');

// 注册事件处理器
container.bind('myNewEventHandler', () => new MyNewEventHandler());
```

2. 在 `EventHandlerRegistry.ts` 的 `registerHandlers` 方法中添加新的事件处理器注册：

```typescript
this.registerHandler('MyNewEvent', 'myNewEventHandler');
```

### 发布事件

在领域模型或应用服务中发布事件：

```typescript
// 创建事件
const event = new MyNewEvent(
  aggregateId,
  // 其他参数...
);

// 发布事件
await this.eventPublisher.publish(event);
```

## 测试

### 测试事件处理器

使用提供的测试脚本测试事件处理器和WebSocket通知功能：

```bash
node backend/scripts/test-event-handlers.js
```

### 测试WebSocket客户端

使用提供的WebSocket客户端测试脚本测试WebSocket通知功能：

```bash
node backend/scripts/test-websocket-client.js
```

## 注意事项

1. 事件处理器应该是无状态的，可以并行执行。
2. 事件处理器应该处理异常，不应该让异常影响其他事件处理器的执行。
3. WebSocket通知应该考虑安全性，只向有权限的用户发送通知。
4. 事件处理器应该尽量轻量级，如果需要执行耗时操作，应该考虑使用异步任务。
