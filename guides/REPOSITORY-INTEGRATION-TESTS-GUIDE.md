# 仓库集成测试指南

## 概述

本文档介绍了AIBUBB项目中仓库层集成测试的设计、实现和使用方法。仓库集成测试用于验证仓库实现与数据库的实际交互，确保数据持久化和检索的正确性。

## 背景

在DDD架构中，仓库层负责领域实体的持久化和检索。仓库集成测试的目的是验证仓库实现是否能够正确地与数据库交互，包括：

1. 实体的创建、读取、更新和删除操作
2. 复杂查询的正确性
3. 事务管理的正确性
4. 领域事件的发布

这些测试使用真实的数据库连接（通常是测试数据库），而不是模拟对象，以确保测试的真实性。

## 架构设计

仓库集成测试架构包含以下组件：

1. **RepositoryIntegrationTestBase**：提供仓库集成测试的通用功能，包括数据库连接、事务管理等
2. **TestDataGenerator**：用于生成测试数据
3. **具体仓库测试类**：继承自RepositoryIntegrationTestBase，实现特定仓库的测试

### 类图

```
┌─────────────────┐     ┌─────────────────┐
│RepositoryInteg- │     │ TestDataGenerator│
│rationTestBase   │     │                  │
└────────┬────────┘     └─────────────────┘
         │                       ▲
         │                       │
         ▼                       │
┌────────┴────────┐     ┌────────┴────────┐
│ TagRepository-  │     │ ExerciseRepo-   │
│IntegrationTest  │     │sitoryIntegration│
└─────────────────┘     │Test             │
                        └─────────────────┘
```

## 使用方法

### 1. 设置测试数据库

在运行仓库集成测试之前，需要设置测试数据库。可以通过环境变量配置测试数据库连接：

```bash
# 设置测试数据库连接
export TEST_DB_HOST=localhost
export TEST_DB_PORT=3306
export TEST_DB_NAME=aibubb_test
export TEST_DB_USER=root
export TEST_DB_PASSWORD=password
export TEST_DB_LOGGING=false
```

如果不设置环境变量，测试将使用默认配置，即在主数据库名后加上`_test`后缀。

### 2. 运行所有仓库集成测试

使用以下命令运行所有仓库集成测试：

```bash
npm run test:repositories
```

### 3. 运行特定仓库集成测试

可以直接运行特定的仓库集成测试文件：

```bash
# 运行标签仓库集成测试
ts-node tests/integration/repositories/TagRepositoryIntegrationTest.ts

# 运行练习仓库集成测试
ts-node tests/integration/repositories/ExerciseRepositoryIntegrationTest.ts
```

## 创建新的仓库集成测试

### 1. 创建测试类

创建一个新的测试类，继承自RepositoryIntegrationTestBase：

```typescript
import { RepositoryIntegrationTestBase } from './RepositoryIntegrationTestBase';
import { MyRepository } from '../../../domain/repositories/MyRepository';
import { MyRepositoryImpl } from '../../../infrastructure/repositories/MyRepositoryImpl';
import { MyEntity } from '../../../domain/entities/MyEntity';
import { TestDataGenerator } from './TestDataGenerator';

export class MyRepositoryIntegrationTest extends RepositoryIntegrationTestBase {
  private myRepository: MyRepository;
  private testDataGenerator: TestDataGenerator;
  private testEntities: any[];

  constructor() {
    super();
    this.myRepository = new MyRepositoryImpl(this.unitOfWork, this.eventPublisher);
    this.testDataGenerator = new TestDataGenerator(this.sequelize);
    this.testEntities = [];
  }

  protected async initTestData(): Promise<void> {
    // 初始化测试数据
  }

  async testFindById(): Promise<void> {
    // 测试根据ID查找实体
  }

  async testCreate(): Promise<void> {
    // 测试创建实体
  }

  async testUpdate(): Promise<void> {
    // 测试更新实体
  }

  async testDelete(): Promise<void> {
    // 测试删除实体
  }

  async runTests(): Promise<void> {
    try {
      await this.setUp();
      await this.testFindById();
      await this.testCreate();
      await this.testUpdate();
      await this.testDelete();
      console.log('所有测试通过');
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      await this.tearDown();
    }
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const test = new MyRepositoryIntegrationTest();
  test.runTests().catch(console.error);
}
```

### 2. 更新测试数据生成器

如果需要生成新类型的测试数据，更新TestDataGenerator类：

```typescript
// 在TestDataGenerator类中添加新方法
async generateMyEntities(count: number = 5): Promise<any[]> {
  const entities = [];
  
  for (let i = 0; i < count; i++) {
    const entity = {
      name: `Entity ${i} ${uuidv4().substring(0, 8)}`,
      // 其他属性...
      created_at: new Date(),
      updated_at: new Date(),
      is_deleted: false
    };
    
    entities.push(entity);
  }
  
  return this.sequelize.models.MyEntity.bulkCreate(entities);
}
```

### 3. 更新测试运行器

将新的测试类添加到测试运行器中：

```typescript
// 在runRepositoryTests.ts中添加新的测试
import { MyRepositoryIntegrationTest } from './MyRepositoryIntegrationTest';

async function runAllTests() {
  // 其他测试...

  // 运行新的仓库集成测试
  console.log('\n=== 运行我的仓库集成测试 ===');
  const myTest = new MyRepositoryIntegrationTest();
  await myTest.runTests();
}
```

## 最佳实践

### 1. 使用事务管理测试

在测试中使用事务可以确保测试的隔离性和可重复性：

```typescript
// 开始事务
await this.beginTransaction();

try {
  // 测试代码...

  // 提交事务
  await this.commitTransaction();
} catch (error) {
  // 回滚事务
  await this.rollbackTransaction();
  throw error;
}
```

### 2. 清理测试数据

在测试完成后清理测试数据，以避免测试数据污染：

```typescript
protected async cleanTestData(): Promise<void> {
  // 清理测试数据
  await this.sequelize.models.MyEntity.destroy({ where: {}, force: true });
}
```

### 3. 使用断言

使用断言验证测试结果：

```typescript
// 断言
expect(entity).not.toBeNull();
expect(entity.id).toBe(testEntity.id);
expect(entity.name).toBe('Updated Name');
```

### 4. 测试领域事件发布

测试领域事件是否正确发布：

```typescript
// 模拟事件总线
const mockEventBus = {
  publish: jest.fn().mockResolvedValue(undefined)
};
(this.eventPublisher as any).eventBus = mockEventBus;

// 执行操作
await entity.doSomething();
await this.repository.save(entity);

// 断言事件发布
expect(mockEventBus.publish).toHaveBeenCalled();
const publishCall = mockEventBus.publish.mock.calls[0];
expect(publishCall[0]).toBe('EntityChanged');
expect(publishCall[1].aggregateId).toBe(entity.id);
```

## 故障排除

### 1. 测试数据库连接失败

如果测试数据库连接失败，可能是以下原因：

1. 测试数据库不存在
2. 测试数据库连接配置错误
3. 测试数据库用户权限不足

解决方法：

1. 确保测试数据库存在
2. 检查测试数据库连接配置
3. 确保测试数据库用户有足够的权限

### 2. 测试失败但没有明确错误

如果测试失败但没有明确错误，可能是以下原因：

1. 断言失败
2. 异步操作未正确处理
3. 事务未正确管理

解决方法：

1. 添加更详细的日志
2. 确保正确处理异步操作
3. 确保正确管理事务

### 3. 测试数据污染

如果测试数据污染导致测试不可重复，可能是以下原因：

1. 测试数据未正确清理
2. 事务未正确回滚

解决方法：

1. 确保在测试完成后清理测试数据
2. 确保在测试失败时回滚事务

## 参考资料

- [Jest文档](https://jestjs.io/docs/getting-started)
- [Sequelize文档](https://sequelize.org/master/)
- [领域驱动设计](https://en.wikipedia.org/wiki/Domain-driven_design)
- [测试驱动开发](https://en.wikipedia.org/wiki/Test-driven_development)
