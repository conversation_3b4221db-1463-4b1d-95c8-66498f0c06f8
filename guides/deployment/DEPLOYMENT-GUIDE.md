# AIBUBB部署指南

## 概述

本文档提供AIBUBB项目的完整部署指南，包括开发环境、测试环境和生产环境的部署方案。AIBUBB采用Docker容器化部署，支持快速启动和环境一致性。

## 系统要求

### 基础要求
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 至少2GB可用内存
- **磁盘**: 至少10GB可用磁盘空间
- **操作系统**: Linux/macOS/Windows (支持Docker)

### 生产环境额外要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **磁盘**: 50GB以上 SSD
- **网络**: 稳定的网络连接
- **域名**: 已备案的域名（如需HTTPS）

## 容器架构

AIBUBB Docker部署包含以下容器：

1. **aibubb-backend**: Node.js后端API服务
2. **aibubb-mysql**: MySQL 8.0数据库服务
3. **aibubb-redis**: Redis 7.0缓存服务
4. **mcp-mysql-server**: 数据库管理工具连接的辅助服务

## 快速开始

### 1. 获取代码

```bash
# 克隆项目
git clone [项目地址]
cd AIBUBB
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.development.example .env

# 编辑环境变量（必须配置）
vim .env
```

**必需配置项**：
```bash
# 数据库配置
DB_PASSWORD=your_strong_password
DB_ROOT_PASSWORD=your_root_password
DB_NAME=aibubb_db
DB_USER=aibubb_user

# JWT配置
JWT_SECRET=your_64_character_jwt_secret_key_here_make_it_very_secure

# AI服务配置（选择一个）
AI_PROVIDER=aliyun
DASHSCOPE_API_KEY=your_aliyun_api_key
# 或者
AI_PROVIDER=bytedance
ARK_API_KEY=your_bytedance_api_key
```

### 3. 启动服务

```bash
# 使用优化的启动脚本
./docker-start.sh

# 或者使用标准Docker Compose
docker-compose up -d
```

### 4. 验证部署

```bash
# 检查容器状态
docker-compose ps

# 检查API服务
curl http://localhost:9090/health

# 查看日志
docker-compose logs -f backend
```

### 5. 访问服务

- **API服务**: http://localhost:9090
- **API文档**: http://localhost:9090/api-docs
- **ReDoc文档**: http://localhost:9090/redoc

## 开发环境部署

### 开发原则
1. **始终使用Docker容器**：避免本地环境差异
2. **避免本地运行服务**：防止端口冲突和数据不一致
3. **统一配置管理**：确保所有配置指向Docker容器

### 开发流程

#### 前端开发
```bash
# 启动后端服务
docker-compose up -d

# 使用微信开发者工具打开项目
# API会自动连接到 http://localhost:9090
```

#### 后端开发
```bash
# 修改代码后重建容器
docker-compose down
docker-compose up -d --build backend

# 或使用开发脚本
./scripts/dev-container.sh
```

### 开发工具

#### 查看日志
```bash
# 查看所有容器日志
docker-compose logs

# 查看特定容器日志
docker-compose logs backend
docker-compose logs mysql
docker-compose logs redis

# 实时查看日志
docker-compose logs -f backend
```

#### 数据库操作
```bash
# 连接数据库
docker exec -it aibubb-mysql mysql -u$DB_USER -p$DB_PASSWORD $DB_NAME

# 备份数据库
./docker-backup.sh

# 恢复数据库
./docker-restore.sh backup_file.sql.gz
```

## 生产环境部署

### 服务器准备

#### 1. 系统更新
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 2. 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 3. 配置防火墙
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 生产部署步骤

#### 1. 部署前检查
```bash
# 运行部署前检查脚本
./pre-deployment-check.sh
```

#### 2. 环境配置
```bash
# 使用生产环境模板
cp .env.production.example .env

# 配置生产环境变量
vim .env
```

**生产环境关键配置**：
```bash
# 应用配置
NODE_ENV=production
PORT=9090

# 数据库配置（使用强密码）
DB_PASSWORD=your_very_strong_production_password
DB_ROOT_PASSWORD=your_very_strong_root_password

# JWT配置（使用64字符强密钥）
JWT_SECRET=your_64_character_production_jwt_secret_key_here_make_it_very_secure_and_random

# AI服务配置
AI_PROVIDER=aliyun
DASHSCOPE_API_KEY=your_production_api_key

# 日志配置
LOG_LEVEL=info
LOG_FILE=true
```

#### 3. 启动生产服务
```bash
# 使用优化的生产启动脚本
./docker-start-optimized.sh

# 验证部署
./post-deployment-verify.sh
```

#### 4. 配置反向代理（Nginx）

创建Nginx配置文件：
```nginx
# /etc/nginx/sites-available/aibubb
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:9090;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/aibubb /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 5. 配置SSL证书
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 申请SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 数据管理

### 数据持久化

所有数据通过Docker卷进行持久化：
- **aibubb-mysql-data**: MySQL数据库文件（命名卷）
- **aibubb-redis-data**: Redis数据文件（命名卷）
- **./backend/logs**: 应用日志文件（绑定挂载）

### 备份策略

#### 自动备份
```bash
# 设置定时备份
crontab -e
# 添加：0 2 * * * /path/to/AIBUBB/docker-backup.sh
```

#### 手动备份
```bash
# 备份数据库
./docker-backup.sh

# 备份文件位置
ls -la ./backups/
```

#### 恢复数据
```bash
# 从备份恢复
./docker-restore.sh ./backups/aibubb_backup_20250127_120000.sql.gz
```

## 监控和维护

### 健康检查

所有容器都配置了健康检查：
- **后端API**: 每30秒检查HTTP响应
- **MySQL**: 每10秒检查连接状态
- **Redis**: 每10秒检查连接状态

### 性能监控

#### 容器资源监控
```bash
# 查看容器资源使用
docker stats

# 查看容器详细信息
docker inspect aibubb-backend
```

#### 应用监控
```bash
# 查看API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:9090/health

# 查看数据库连接
docker exec aibubb-mysql mysqladmin -u$DB_USER -p$DB_PASSWORD status
```

### 日志管理

#### 日志查看
```bash
# 查看应用日志
docker-compose logs backend

# 查看系统日志
sudo journalctl -u docker
```

#### 日志轮转
```bash
# 配置Docker日志轮转
sudo vim /etc/docker/daemon.json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
```

## 故障排除

### 常见问题

#### 1. 容器无法启动
```bash
# 检查端口占用
sudo netstat -tulpn | grep :9090

# 检查磁盘空间
df -h

# 检查Docker状态
sudo systemctl status docker
```

#### 2. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose ps mysql

# 检查数据库日志
docker-compose logs mysql

# 测试数据库连接
docker exec aibubb-mysql mysqladmin -u$DB_USER -p$DB_PASSWORD ping
```

#### 3. Redis连接失败
```bash
# 检查Redis容器状态
docker-compose ps redis

# 测试Redis连接
docker exec aibubb-redis redis-cli ping
```

#### 4. API响应异常
```bash
# 检查后端日志
docker-compose logs backend

# 检查API健康状态
curl http://localhost:9090/health

# 检查环境变量
docker exec aibubb-backend env | grep -E "(DB_|JWT_|AI_)"
```

### 性能优化

#### 1. 数据库优化
```bash
# 查看慢查询
docker exec aibubb-mysql mysql -u$DB_USER -p$DB_PASSWORD -e "SHOW VARIABLES LIKE 'slow_query_log';"

# 优化MySQL配置
# 编辑 mysql-conf/my.cnf
```

#### 2. 缓存优化
```bash
# 查看Redis内存使用
docker exec aibubb-redis redis-cli info memory

# 配置Redis持久化
# 编辑 redis/redis.conf
```

## 安全配置

### 1. 环境变量安全
- 使用强密码（至少16字符）
- JWT密钥使用64字符随机字符串
- 定期轮换密钥和密码

### 2. 网络安全
- 配置防火墙规则
- 使用HTTPS加密传输
- 限制数据库和Redis的外部访问

### 3. 容器安全
- 定期更新Docker镜像
- 使用非root用户运行容器
- 限制容器资源使用

## 扩展和升级

### 水平扩展
```bash
# 扩展后端服务实例
docker-compose up -d --scale backend=3

# 配置负载均衡器
# 使用Nginx或HAProxy
```

### 版本升级
```bash
# 备份数据
./docker-backup.sh

# 拉取新版本
git pull origin main

# 重新构建和启动
docker-compose down
docker-compose up -d --build

# 验证升级
./post-deployment-verify.sh
```

## 小程序配置

### 域名配置
在微信小程序后台配置服务器域名：
- **request合法域名**: https://your-domain.com
- **socket合法域名**: wss://your-domain.com（如使用WebSocket）

### API基础URL配置
更新小程序中的API配置：
```javascript
// utils/api.js
const BASE_URL = 'https://your-domain.com/api/v2';
```

## 附录

### 有用的脚本
- `./docker-start.sh` - 启动开发环境
- `./docker-start-optimized.sh` - 启动生产环境
- `./docker-stop.sh` - 停止服务
- `./docker-backup.sh` - 备份数据库
- `./docker-restore.sh` - 恢复数据库
- `./pre-deployment-check.sh` - 部署前检查
- `./post-deployment-verify.sh` - 部署后验证
- `./validate-env.sh` - 环境变量验证

### 相关文档
- [数据库设计](./DATABASE-DESIGN.md)
- [数据库变更日志](./DATABASE-CHANGELOG.md)
- [项目架构](./PROJECT-ARCHITECTURE.md)
- [API设计规范](./API-DESIGN.md)
- [性能优化指南](./PERFORMANCE-OPTIMIZATION.md)

---

**最后更新**: 2025-01-27
**维护责任**: 运维团队
**支持联系**: 技术支持团队