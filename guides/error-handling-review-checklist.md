# 错误处理代码审查清单

本文档提供了一个错误处理代码审查清单，用于确保项目中的错误处理逻辑一致且符合最佳实践。

## 1. 控制器错误处理

### 1.1 基本检查

- [ ] 是否导入了正确的错误处理函数？
  ```javascript
  const { handleApiError, handleNotFoundError, ... } = require('../utils/errorHandler');
  ```

- [ ] 是否在所有控制器方法中使用了 try-catch 块？
  ```javascript
  const getUser = async (req, res) => {
    try {
      // 业务逻辑
    } catch (error) {
      return handleApiError(error, res, 'getUser');
    }
  };
  ```

- [ ] 是否在 catch 块中使用了 `handleApiError` 函数？
  ```javascript
  catch (error) {
    return handleApiError(error, res, 'methodName');
  }
  ```

- [ ] 是否提供了方法名称作为上下文？
  ```javascript
  return handleApiError(error, res, 'methodName'); // 正确
  return handleApiError(error, res); // 不完整
  ```

### 1.2 特定错误处理

- [ ] 是否使用了适当的错误处理函数处理特定类型的错误？
  - `handleNotFoundError` - 资源不存在
  - `handleBadRequestError` - 请求参数错误
  - `handleUnauthorizedError` - 未授权访问
  - `handleForbiddenError` - 禁止访问
  - `handleConflictError` - 资源冲突
  - `handleValidationError` - 验证错误

- [ ] 是否在返回错误响应前使用了 `return` 关键字？
  ```javascript
  if (!user) {
    return handleNotFoundError(res, '用户不存在');
  }
  ```

- [ ] 是否提供了清晰的错误消息？
  ```javascript
  // 不推荐
  return handleNotFoundError(res, '不存在');
  
  // 推荐
  return handleNotFoundError(res, '用户不存在');
  ```

## 2. 服务错误处理

### 2.1 基本检查

- [ ] 是否导入了 `createError` 函数？
  ```javascript
  const { createError } = require('../utils/errorHandler');
  ```

- [ ] 是否在服务方法中使用了 try-catch 块？
  ```javascript
  const getUser = async (userId) => {
    try {
      // 业务逻辑
    } catch (error) {
      // 错误处理
    }
  };
  ```

- [ ] 是否在 catch 块中检查了错误是否已经是格式化的错误？
  ```javascript
  catch (error) {
    if (error.code) {
      throw error; // 已经是格式化的错误，直接重新抛出
    }
    throw createError(`操作失败: ${error.message}`, 'OPERATION_ERROR', 500);
  }
  ```

### 2.2 错误创建

- [ ] 是否使用 `createError` 创建错误？
  ```javascript
  throw createError('用户不存在', 'USER_NOT_FOUND', 404);
  ```

- [ ] 是否提供了适当的错误代码和状态码？
  ```javascript
  // 不推荐
  throw createError('用户不存在');
  
  // 推荐
  throw createError('用户不存在', 'USER_NOT_FOUND', 404);
  ```

- [ ] 是否在错误消息中包含了足够的上下文信息？
  ```javascript
  // 不推荐
  throw createError('创建失败', 'CREATION_ERROR', 500);
  
  // 推荐
  throw createError(`创建用户失败: ${error.message}`, 'USER_CREATION_ERROR', 500);
  ```

## 3. 数据库操作

- [ ] 是否使用事务处理多个数据库操作？
  ```javascript
  const transaction = await sequelize.transaction();
  try {
    // 数据库操作
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw createError(`数据库操作失败: ${error.message}`, 'DATABASE_ERROR', 500);
  }
  ```

- [ ] 是否在事务失败时执行了回滚？
  ```javascript
  catch (error) {
    await transaction.rollback();
    throw createError(...);
  }
  ```

- [ ] 是否处理了特定类型的数据库错误？
  ```javascript
  if (error.name === 'SequelizeUniqueConstraintError') {
    throw createError('数据已存在', 'DUPLICATE_RECORD', 409);
  }
  ```

## 4. 日志记录

- [ ] 是否使用了适当的日志级别？
  - `logger.error()` - 严重错误
  - `logger.warn()` - 警告
  - `logger.info()` - 信息性消息
  - `logger.debug()` - 调试信息

- [ ] 是否避免了记录敏感信息？
  ```javascript
  // 不推荐
  logger.info(`用户登录: ${email}, 密码: ${password}`);
  
  // 推荐
  logger.info(`用户登录: ${email}`);
  ```

- [ ] 是否在日志中包含了足够的上下文信息？
  ```javascript
  logger.error(`[${context}] 操作失败: ${error.message}`);
  ```

## 5. 错误监控

- [ ] 是否在关键错误处理点使用了错误监控？
  ```javascript
  const { recordError } = require('../utils/errorMonitor');
  
  try {
    // 业务逻辑
  } catch (error) {
    recordError(error, req, { context: 'methodName' });
    return handleApiError(error, res, 'methodName');
  }
  ```

## 6. 边缘情况

- [ ] 是否处理了空值或未定义值？
  ```javascript
  const id = req.params.id || null;
  if (!id) {
    return handleBadRequestError(res, 'ID不能为空');
  }
  ```

- [ ] 是否处理了类型转换错误？
  ```javascript
  const id = parseInt(req.params.id);
  if (isNaN(id)) {
    return handleBadRequestError(res, 'ID必须是有效的数字');
  }
  ```

- [ ] 是否处理了异步操作的错误？
  ```javascript
  Promise.all([
    operation1(),
    operation2()
  ]).catch(error => {
    throw createError(`并行操作失败: ${error.message}`, 'PARALLEL_ERROR', 500);
  });
  ```

## 7. 一致性检查

- [ ] 是否在整个项目中使用了一致的错误处理方式？

- [ ] 是否使用了一致的错误代码和状态码？

- [ ] 是否使用了一致的错误消息格式？

## 8. 安全性检查

- [ ] 是否避免了在错误响应中泄露敏感信息？
  ```javascript
  // 不推荐
  throw createError(`数据库连接失败: ${dbConfig.password}`, 'DB_ERROR', 500);
  
  // 推荐
  throw createError('数据库连接失败', 'DB_ERROR', 500);
  ```

- [ ] 是否在生产环境中使用了通用错误消息？
  ```javascript
  const message = process.env.NODE_ENV === 'production'
    ? '服务器内部错误'
    : `数据库错误: ${error.message}`;
  ```

## 使用说明

在代码审查过程中，使用此清单检查每个文件中的错误处理逻辑。对于每个检查项，标记为：

- ✅ 符合要求
- ❌ 不符合要求
- ➖ 不适用

对于不符合要求的项目，提供具体的改进建议。
