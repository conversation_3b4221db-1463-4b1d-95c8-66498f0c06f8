# AIBUBB Docker优化部署指南

本文档提供了使用优化的Docker配置部署AIBUBB后端服务的详细说明。

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB可用内存（推荐4GB以上）
- 至少10GB可用磁盘空间
- 多核CPU（推荐4核以上）

## 优化特性

与标准部署相比，优化版本包含以下改进：

1. **资源限制与预留**：为每个容器设置CPU和内存限制，防止资源过度使用
2. **网络优化**：配置专用网络，提高容器间通信效率
3. **数据库优化**：优化MySQL配置，提高查询性能
4. **缓存优化**：优化Redis配置，提高缓存命中率
5. **健康检查增强**：改进健康检查机制，提高系统可靠性
6. **日志管理**：限制日志大小，防止磁盘空间耗尽
7. **环境变量管理**：更灵活的环境变量配置
8. **启动脚本增强**：智能检测系统资源，自动调整配置

## 快速开始

### 准备工作

1. 确保已安装Docker和Docker Compose
2. 克隆项目仓库（如果尚未克隆）
3. 进入项目目录

### 启动服务

```bash
# 使用优化的启动脚本
chmod +x docker-start-optimized.sh
./docker-start-optimized.sh
```

启动后，服务将在以下地址可用：
- API服务: http://localhost:9090
- API文档: http://localhost:9090/api-docs

### 停止服务

```bash
# 使用优化的停止脚本
chmod +x docker-stop-optimized.sh
./docker-stop-optimized.sh
```

## 配置说明

### 环境变量

优化版本使用`.env.docker.optimized`作为环境变量模板，包含以下主要配置组：

1. **服务器配置**：端口、环境、API前缀等
2. **数据库配置**：主机、端口、用户名、密码等
3. **Redis配置**：URL、密码、内存限制等
4. **资源限制配置**：各服务的CPU和内存限制
5. **AI提供商配置**：各AI服务的API密钥和模型选择

### 资源限制

默认资源限制如下：

| 服务 | CPU限制 | 内存限制 | CPU预留 | 内存预留 |
|------|---------|----------|---------|----------|
| 后端 | 2核     | 2GB      | 0.5核   | 512MB    |
| MySQL| 2核     | 2GB      | 0.5核   | 512MB    |
| Redis| 1核     | 1GB      | 0.1核   | 128MB    |

这些限制可以在`.env`文件中调整。

## 目录结构

优化版本使用以下目录结构：

```
/
├── backend/                # 后端代码目录
│   ├── logs/               # 日志目录
│   └── scripts/            # 脚本目录
├── mysql-conf/             # MySQL配置目录
│   └── my.cnf              # MySQL配置文件
├── mysql-init/             # MySQL初始化脚本目录
├── redis/                  # Redis配置目录
│   └── redis.conf          # Redis配置文件
├── .env                    # 环境变量文件
├── .env.docker.optimized   # 环境变量模板
├── docker-compose.optimized.yml  # 优化的Docker Compose配置
├── docker-start-optimized.sh     # 优化的启动脚本
└── docker-stop-optimized.sh      # 优化的停止脚本
```

## 性能调优

### MySQL调优

MySQL配置文件`mysql-conf/my.cnf`包含以下主要优化：

1. **缓冲池大小**：根据可用内存调整InnoDB缓冲池大小
2. **连接设置**：优化最大连接数和超时设置
3. **查询缓存**：禁用查询缓存，提高写入性能
4. **InnoDB设置**：优化InnoDB引擎参数
5. **日志设置**：配置二进制日志和慢查询日志

### Redis调优

Redis配置文件`redis/redis.conf`包含以下主要优化：

1. **内存管理**：设置最大内存和淘汰策略
2. **持久化**：配置AOF持久化
3. **网络设置**：优化TCP参数
4. **客户端设置**：调整最大客户端数量
5. **高级优化**：启用主动哈希等功能

### Node.js调优

后端服务配置包含以下主要优化：

1. **内存限制**：设置Node.js内存限制
2. **集群模式**：支持PM2集群模式
3. **文件描述符**：增加文件描述符限制
4. **健康检查**：增强健康检查机制

## 监控与日志

### 查看容器日志

```bash
# 查看所有容器日志
docker-compose -f docker-compose.optimized.yml logs

# 查看特定容器日志
docker-compose -f docker-compose.optimized.yml logs backend
docker-compose -f docker-compose.optimized.yml logs mysql
docker-compose -f docker-compose.optimized.yml logs redis

# 实时查看日志
docker-compose -f docker-compose.optimized.yml logs -f backend
```

### 监控容器状态

```bash
# 查看容器状态
docker-compose -f docker-compose.optimized.yml ps

# 查看容器资源使用情况
docker stats
```

## 故障排除

### 常见问题

1. **容器无法启动**
   - 检查端口冲突（9090, 3306, 6379）
   - 检查磁盘空间是否充足
   - 检查环境变量配置

2. **数据库连接失败**
   - 检查MySQL容器是否正常运行
   - 检查环境变量中的数据库配置
   - 查看MySQL日志：`docker-compose -f docker-compose.optimized.yml logs mysql`

3. **Redis连接失败**
   - 检查Redis容器是否正常运行
   - 查看Redis日志：`docker-compose -f docker-compose.optimized.yml logs redis`

4. **后端服务无响应**
   - 检查后端容器是否正常运行
   - 查看后端日志：`docker-compose -f docker-compose.optimized.yml logs backend`
   - 检查健康检查状态：`docker inspect --format='{{.State.Health.Status}}' aibubb-backend`

### 解决方法

1. **重启特定容器**
   ```bash
   docker-compose -f docker-compose.optimized.yml restart backend
   ```

2. **重建容器**
   ```bash
   docker-compose -f docker-compose.optimized.yml up -d --force-recreate backend
   ```

3. **检查容器日志**
   ```bash
   docker-compose -f docker-compose.optimized.yml logs --tail=100 backend
   ```

4. **进入容器内部**
   ```bash
   docker-compose -f docker-compose.optimized.yml exec backend sh
   ```

## 备份与恢复

### 备份数据库

```bash
# 手动备份
mkdir -p backups
docker-compose -f docker-compose.optimized.yml exec -T mysql mysqldump -u$DB_USER -p$DB_PASSWORD $DB_NAME | gzip > backups/aibubb_backup_$(date +%Y%m%d_%H%M%S).sql.gz
```

### 恢复数据库

```bash
# 从备份文件恢复
gunzip -c backups/aibubb_backup_YYYYMMDD_HHMMSS.sql.gz | docker-compose -f docker-compose.optimized.yml exec -T mysql mysql -u$DB_USER -p$DB_PASSWORD $DB_NAME
```

## 升级指南

### 升级Docker镜像

```bash
# 拉取最新代码
git pull

# 重建镜像
docker-compose -f docker-compose.optimized.yml build --no-cache

# 重启服务
docker-compose -f docker-compose.optimized.yml up -d
```

### 升级配置文件

1. 备份当前配置
   ```bash
   cp .env .env.backup
   cp docker-compose.optimized.yml docker-compose.optimized.yml.backup
   ```

2. 更新配置文件
   ```bash
   # 使用新的模板更新环境变量
   cp .env.docker.optimized .env
   # 编辑.env文件，保留原有的敏感信息
   ```

3. 重启服务
   ```bash
   ./docker-stop-optimized.sh
   ./docker-start-optimized.sh
   ```

## 安全建议

1. **使用强密码**：为数据库和Redis设置强密码
2. **限制端口访问**：只暴露必要的端口
3. **使用HTTPS**：在生产环境中配置HTTPS
4. **定期更新**：保持Docker镜像和依赖包的更新
5. **备份数据**：定期备份数据库和重要配置
