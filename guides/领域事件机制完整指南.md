# 领域事件机制完整指南

本文档提供了关于领域事件机制的完整指南，包括架构设计、实现细节、使用方法和最佳实践。

## 1. 架构概述

领域事件是DDD架构中的重要概念，用于捕捉业务中的重要变化，支持松耦合的系统集成。领域事件机制由以下组件组成：

### 1.1 核心组件

1. **领域事件（DomainEvent）**：表示领域中发生的事件，包含事件ID、类型、发生时间等信息。
2. **事件发布者（EventPublisher）**：负责发布领域事件，由聚合根调用。
3. **事件总线（EventBus）**：负责事件的发布和订阅，是事件发布者和事件处理器之间的中介。
4. **事件处理器（EventHandler）**：负责处理特定类型的事件，执行相应的业务逻辑。
5. **事件处理器注册表（EventHandlerRegistry）**：负责注册所有事件处理器。
6. **事件存储（EventStore）**：负责存储领域事件，支持事件重放和审计。
7. **事件监控服务（EventMonitoringService）**：负责监控事件处理情况，收集统计数据，并在出现异常时发出告警。

### 1.2 架构图

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  聚合根      │────▶│  事件发布者   │────▶│  事件总线    │
└─────────────┘     └─────────────┘     └──────┬──────┘
                                               │
                                               ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  事件存储    │◀────│  事件处理器   │◀────│  事件处理器  │
└─────────────┘     │  注册表      │     │  注册表      │
                    └─────────────┘     └─────────────┘
```

## 2. 领域事件

### 2.1 领域事件接口

领域事件接口定义了所有领域事件必须实现的属性和方法。

```typescript
export interface DomainEvent {
  eventId: string;
  eventType: string;
  occurredOn: Date;
  aggregateId: string | number;
  aggregateType: string;
  version: number;
  payload: any;
}
```

### 2.2 领域事件基类

领域事件基类提供了领域事件接口的基本实现，所有具体的领域事件都应该继承这个基类。

```typescript
export abstract class DomainEventBase implements DomainEvent {
  readonly eventId: string;
  readonly occurredOn: Date;
  readonly version: number;

  constructor(
    readonly eventType: string,
    readonly aggregateId: string | number,
    readonly aggregateType: string,
    readonly payload: any
  ) {
    this.eventId = uuidv4();
    this.occurredOn = new Date();
    this.version = 1;
  }
}
```

### 2.3 具体领域事件示例

```typescript
export class ExerciseCompletedEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'ExerciseCompleted';
  readonly aggregateType: string = 'Exercise';
  readonly version: number = 1;

  constructor(
    readonly aggregateId: number,
    readonly userId: number,
    readonly title: string,
    readonly points: number = 0,
    readonly isSpecialAchievement: boolean = false,
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  get payload(): any {
    return {
      userId: this.userId,
      title: this.title,
      points: this.points,
      isSpecialAchievement: this.isSpecialAchievement
    };
  }
}
```

## 3. 事件发布

### 3.1 事件发布者接口

事件发布者接口定义了发布领域事件的方法。

```typescript
export interface EventPublisher {
  publish(event: DomainEvent): Promise<void>;
  publishAll(events: DomainEvent[]): Promise<void>;
}
```

### 3.2 事件发布者实现

事件发布者实现负责将领域事件发布到事件总线，并可选地存储到事件存储。

```typescript
export class EventPublisherImpl implements EventPublisher {
  constructor(
    private readonly eventBus: EventBus,
    private readonly eventStore?: EventStore
  ) {}

  async publish(event: DomainEvent): Promise<void> {
    // 如果有事件存储，先存储事件
    if (this.eventStore) {
      try {
        await this.eventStore.store(event);
      } catch (error) {
        console.error(`存储事件失败: ${error.message}`);
        // 存储失败不应阻止事件发布
      }
    }
    
    // 发布事件到事件总线
    await this.eventBus.publish(event.eventType, event);
  }

  async publishAll(events: DomainEvent[]): Promise<void> {
    for (const event of events) {
      await this.publish(event);
    }
  }
}
```

### 3.3 在聚合根中发布事件

聚合根基类提供了发布领域事件的方法。

```typescript
export abstract class AggregateRootBase<T> extends EntityBase<T> implements AggregateRoot<T> {
  private _domainEvents: DomainEvent[] = [];

  get domainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  clearEvents(): void {
    this._domainEvents = [];
  }

  addEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }
}
```

在仓库中发布事件：

```typescript
export abstract class RepositoryBase<T extends AggregateRoot<ID>, ID> implements Repository<T, ID> {
  constructor(
    protected readonly unitOfWork: UnitOfWork,
    protected readonly eventPublisher: EventPublisher
  ) {}

  async save(entity: T): Promise<T> {
    const savedEntity = await this.doSave(entity);
    await this.eventPublisher.publishAll(entity.domainEvents);
    entity.clearEvents();
    return savedEntity;
  }
}
```

## 4. 事件处理

### 4.1 事件处理器接口

事件处理器接口定义了处理领域事件的方法。

```typescript
export interface EventHandler {
  handle(event: any): Promise<void>;
}
```

### 4.2 事件处理器基类

事件处理器基类提供了通用的事件处理逻辑和WebSocket通知功能。

```typescript
export abstract class EventHandlerBase<T extends DomainEvent> implements EventHandler {
  protected webSocketService: any;
  protected logger: any;

  constructor() {
    this.webSocketService = require('../../services/websocket.service');
    this.logger = require('../../config/logger');
  }

  async handle(event: any): Promise<void> {
    const startTime = Date.now();
    const eventType = event.eventType || 'unknown';
    const eventId = event.eventId || 'unknown';
    
    this.logger.info(`开始处理事件: ${eventType} (ID: ${eventId})`);
    
    try {
      // 执行具体的事件处理逻辑
      this.logger.debug(`执行事件处理逻辑: ${eventType}`);
      await this.processEvent(event);

      // 发送WebSocket通知（如果需要）
      this.logger.debug(`发送WebSocket通知: ${eventType}`);
      await this.sendNotification(event);
      
      const duration = Date.now() - startTime;
      this.logger.info(`事件处理完成: ${eventType} (ID: ${eventId}), 耗时: ${duration}ms`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`处理事件 ${eventType} (ID: ${eventId}) 时出错, 耗时: ${duration}ms`, {
        error: error.message,
        stack: error.stack,
        eventType,
        eventId,
        payload: event.payload
      });
      
      // 记录错误但不抛出，确保事件总线可以继续处理其他事件
    }
  }

  protected abstract processEvent(event: T): Promise<void>;

  protected async sendNotification(event: T): Promise<void> {
    // 默认实现为空，子类可以根据需要覆盖
  }

  protected async sendBroadcastNotification(data: any): Promise<boolean> {
    try {
      this.logger.debug(`发送广播通知: ${data.type}`);
      const result = await this.webSocketService.publish('broadcast', data);
      if (result) {
        this.logger.debug(`广播通知发送成功: ${data.type}`);
      } else {
        this.logger.warn(`广播通知发送失败: ${data.type}`);
      }
      return result;
    } catch (error) {
      this.logger.error(`发送广播通知时出错: ${data.type}`, {
        error: error.message,
        data
      });
      return false;
    }
  }

  protected async sendUserNotification(userId: string, data: any): Promise<boolean> {
    try {
      this.logger.debug(`发送用户通知: ${data.type}, 用户ID: ${userId}`);
      const result = await this.webSocketService.publish(`user:${userId}`, data);
      if (result) {
        this.logger.debug(`用户通知发送成功: ${data.type}, 用户ID: ${userId}`);
      } else {
        this.logger.warn(`用户通知发送失败: ${data.type}, 用户ID: ${userId}`);
      }
      return result;
    } catch (error) {
      this.logger.error(`发送用户通知时出错: ${data.type}, 用户ID: ${userId}`, {
        error: error.message,
        userId,
        data
      });
      return false;
    }
  }
}
```

### 4.3 具体事件处理器示例

```typescript
export class ExerciseCompletedEventHandler extends EventHandlerBase<ExerciseCompletedEvent> {
  protected async processEvent(event: ExerciseCompletedEvent): Promise<void> {
    console.log(`处理练习完成事件: 用户 ${event.userId} 完成了练习 ${event.aggregateId}`);
    
    // 这里可以添加其他业务逻辑，如更新统计数据、触发成就等
  }

  protected async sendNotification(event: ExerciseCompletedEvent): Promise<void> {
    // 向用户发送通知
    await this.sendUserNotification(event.userId.toString(), {
      type: 'exerciseCompleted',
      exerciseId: event.aggregateId,
      title: event.title,
      timestamp: new Date().toISOString(),
      points: event.points || 0,
      message: `恭喜你完成了练习: ${event.title}`
    });

    // 如果有特殊成就或里程碑，可以发送广播
    if (event.isSpecialAchievement) {
      await this.sendBroadcastNotification({
        type: 'specialAchievement',
        userId: event.userId,
        exerciseId: event.aggregateId,
        title: event.title,
        timestamp: new Date().toISOString(),
        message: `用户完成了特殊练习: ${event.title}`
      });
    }
  }
}
```

### 4.4 事件处理器注册

事件处理器注册表负责注册所有事件处理器。

```typescript
export class EventHandlerRegistry {
  constructor(
    private readonly eventBus: EventBus,
    private readonly container: Container
  ) {}

  registerHandlers(): void {
    console.log('注册事件处理器...');

    // 注册内容相关事件处理器
    this.registerHandler('ExerciseCreated', 'exerciseCreatedEventHandler');
    this.registerHandler('ExerciseCompleted', 'exerciseCompletedEventHandler');
    this.registerHandler('NoteCreated', 'noteCreatedEventHandler');
    this.registerHandler('NotePublished', 'notePublishedEventHandler');
    
    // 注册用户相关事件处理器
    this.registerHandler('UserLeveledUp', 'userLeveledUpEventHandler');
    this.registerHandler('AchievementUnlocked', 'achievementUnlockedEventHandler');
    this.registerHandler('BadgeAwarded', 'badgeAwardedEventHandler');

    // 注册学习相关事件处理器
    this.registerHandler('LearningPlanCreated', 'learningPlanCreatedEventHandler');
    this.registerHandler('LearningPlanCompleted', 'learningPlanCompletedEventHandler');
    this.registerHandler('ThemeCreated', 'themeCreatedEventHandler');

    console.log('事件处理器注册完成');
  }

  private registerHandler(eventType: string, handlerName: string): void {
    try {
      // 从容器中获取处理器实例
      if (this.container.has(handlerName)) {
        const handler = this.container.get<EventHandler>(handlerName);
        
        // 注册到事件总线
        this.eventBus.subscribe(eventType, handler);
        console.log(`已注册事件处理器: ${eventType} -> ${handlerName}`);
      } else {
        console.warn(`未找到事件处理器: ${handlerName}`);
      }
    } catch (error) {
      console.error(`注册事件处理器 ${handlerName} 失败:`, error);
    }
  }
}
```

## 5. 事件存储

### 5.1 事件存储接口

事件存储接口定义了存储和检索领域事件的方法。

```typescript
export interface EventStore {
  store(event: DomainEvent): Promise<void>;
  getEvents(aggregateId: string | number, aggregateType: string): Promise<DomainEvent[]>;
  getAllEvents(limit?: number, offset?: number): Promise<DomainEvent[]>;
  getEventsByType(eventType: string, limit?: number, offset?: number): Promise<DomainEvent[]>;
  getEventsByTimeRange(startDate: Date, endDate: Date, limit?: number, offset?: number): Promise<DomainEvent[]>;
}
```

### 5.2 数据库事件存储实现

```typescript
export class DatabaseEventStore implements EventStore {
  constructor(private readonly sequelize: Sequelize) {
    this.initializeModel();
  }

  private initializeModel(): void {
    // 初始化事件模型
  }

  async store(event: DomainEvent): Promise<void> {
    await EventModel.create({
      eventId: event.eventId,
      eventType: event.eventType,
      aggregateId: event.aggregateId.toString(),
      aggregateType: event.aggregateType,
      occurredOn: event.occurredOn,
      version: event.version || 1,
      payload: event.payload || {},
      metadata: {
        storedAt: new Date()
      }
    });
  }

  async getEvents(aggregateId: string | number, aggregateType: string): Promise<DomainEvent[]> {
    // 实现代码
  }

  async getAllEvents(limit: number = 100, offset: number = 0): Promise<DomainEvent[]> {
    // 实现代码
  }

  async getEventsByType(eventType: string, limit: number = 100, offset: number = 0): Promise<DomainEvent[]> {
    // 实现代码
  }

  async getEventsByTimeRange(
    startDate: Date,
    endDate: Date,
    limit: number = 100,
    offset: number = 0
  ): Promise<DomainEvent[]> {
    // 实现代码
  }
}
```

## 6. 事件监控

### 6.1 事件监控服务

事件监控服务负责监控事件处理情况，收集统计数据，并在出现异常时发出告警。

```typescript
export class EventMonitoringService {
  private eventStats: Map<string, EventStats> = new Map();
  private processingEvents: Map<string, { event: DomainEvent; startTime: number }> = new Map();
  private alertThreshold: number = 5000;
  private alertCallback: ((eventType: string, processingTime: number, event: DomainEvent) => void) | null = null;

  constructor(
    private readonly eventBus: EventBus,
    private readonly logger: any
  ) {
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // 监听所有事件的开始处理
    this.eventBus.subscribe('*', {
      handle: async (event: DomainEvent) => {
        this.onEventProcessingStart(event);
      }
    });

    // 添加事件处理完成回调
    if (this.eventBus.addEventProcessedCallback) {
      this.eventBus.addEventProcessedCallback((eventId, eventType, success, error) => {
        this.onEventProcessingComplete(eventId, success, error);
      });
    }
  }

  // 其他方法实现
}
```

## 7. 使用指南

### 7.1 创建新的领域事件

1. 在 `domain/events` 目录下创建新的事件类，实现 `DomainEvent` 接口：

```typescript
export class MyNewEvent implements DomainEvent {
  readonly eventId: string;
  readonly eventType: string = 'MyNewEvent';
  readonly aggregateType: string = 'MyAggregate';
  readonly version: number = 1;

  constructor(
    readonly aggregateId: number,
    // 其他属性...
    readonly occurredOn: Date = new Date()
  ) {
    this.eventId = uuidv4();
  }

  get payload(): any {
    return {
      // 返回事件数据...
    };
  }
}
```

### 7.2 创建新的事件处理器

1. 在 `application/events` 目录下创建新的事件处理器类，继承 `EventHandlerBase`：

```typescript
export class MyNewEventHandler extends EventHandlerBase<MyNewEvent> {
  protected async processEvent(event: MyNewEvent): Promise<void> {
    // 实现事件处理逻辑...
  }

  protected async sendNotification(event: MyNewEvent): Promise<void> {
    // 实现通知逻辑...
    await this.sendUserNotification(userId, {
      type: 'myNewEvent',
      // 其他数据...
    });
  }
}
```

### 7.3 注册事件处理器

1. 在 `infrastructure/config/containerConfig.ts` 的 `configureEventHandlers` 函数中注册新的事件处理器：

```typescript
// 导入事件处理器
const { MyNewEventHandler } = require('../../application/events/MyNewEventHandler');

// 注册事件处理器
container.bind('myNewEventHandler', () => new MyNewEventHandler());
```

2. 在 `EventHandlerRegistry.ts` 的 `registerHandlers` 方法中添加新的事件处理器注册：

```typescript
this.registerHandler('MyNewEvent', 'myNewEventHandler');
```

### 7.4 发布事件

在领域模型或应用服务中发布事件：

```typescript
// 创建事件
const event = new MyNewEvent(
  aggregateId,
  // 其他参数...
);

// 发布事件
await this.eventPublisher.publish(event);
```

或者在聚合根中添加事件：

```typescript
// 在聚合根方法中
public doSomething(): void {
  // 业务逻辑...
  
  // 添加事件
  this.addEvent(new MyNewEvent(this.id, ...));
}
```

然后在仓库中保存聚合根时，事件会被自动发布：

```typescript
// 在仓库中
async save(entity: MyAggregate): Promise<MyAggregate> {
  const savedEntity = await this.doSave(entity);
  await this.eventPublisher.publishAll(entity.domainEvents);
  entity.clearEvents();
  return savedEntity;
}
```

## 8. 最佳实践

### 8.1 事件命名

- 使用过去时态命名事件，如 `UserRegistered`、`OrderPlaced`、`PaymentProcessed`。
- 事件名称应该清晰地表达发生了什么，而不是为什么发生或者应该做什么。

### 8.2 事件内容

- 事件应该包含足够的信息，以便事件处理器可以处理事件，但不应该包含敏感信息。
- 事件应该是不可变的，一旦创建就不应该修改。
- 事件应该是自包含的，不应该依赖外部状态。

### 8.3 事件处理器

- 事件处理器应该是无状态的，可以并行执行。
- 事件处理器应该处理异常，不应该让异常影响其他事件处理器的执行。
- 事件处理器应该尽量轻量级，如果需要执行耗时操作，应该考虑使用异步任务。

### 8.4 事件存储

- 事件存储应该是持久化的，以便在系统重启后仍然可以访问事件历史。
- 事件存储应该支持按聚合ID、事件类型和时间范围查询事件。
- 事件存储应该支持事件重放，以便在需要时重建聚合状态。

### 8.5 性能考虑

- 对于高频事件，考虑使用批量处理或异步处理。
- 监控事件处理的性能，设置合理的告警阈值。
- 考虑使用消息队列或事件流平台（如Kafka）来处理大量事件。

## 9. 故障排除

### 9.1 常见问题

1. **事件没有被处理**
   - 检查事件处理器是否已正确注册
   - 检查事件类型是否与处理器注册的类型匹配
   - 检查事件总线是否正常工作

2. **事件处理器抛出异常**
   - 检查事件处理器的错误处理逻辑
   - 查看日志，了解异常详情
   - 修复异常并重新发布事件

3. **WebSocket通知没有发送**
   - 检查WebSocketService是否正常工作
   - 检查Redis连接是否正常
   - 检查事件处理器的sendNotification方法是否正确实现

### 9.2 调试技巧

1. **启用详细日志**
   - 设置日志级别为DEBUG或TRACE
   - 查看事件发布和处理的详细日志

2. **使用事件监控服务**
   - 查看事件处理统计信息
   - 检查处理中的事件
   - 查看处理时间过长的事件

3. **检查事件存储**
   - 查询事件存储，确认事件是否已存储
   - 检查事件内容是否正确

## 10. 结论

领域事件机制是DDD架构中的重要组成部分，它支持系统的松耦合集成，提高系统的可扩展性和可维护性。通过本指南，您应该能够理解领域事件机制的架构设计、实现细节和使用方法，并能够在项目中正确地应用领域事件。
