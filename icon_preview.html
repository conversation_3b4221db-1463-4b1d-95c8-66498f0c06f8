<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序图标库预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #3B82F6;
        }
        .description {
            max-width: 800px;
            margin: 0 auto 30px;
            text-align: center;
            color: #666;
            line-height: 1.6;
        }
        .icons-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .icon-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .icon-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .icon-preview {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        .icon-img {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }
        .icon-name {
            font-size: 14px;
            color: #333;
            text-align: center;
            word-break: break-word;
        }
        .filter-container {
            max-width: 800px;
            margin: 0 auto 30px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        .filter-input {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            width: 250px;
        }
        footer {
            margin-top: 50px;
            text-align: center;
            font-size: 14px;
            color: #999;
            padding: 20px;
        }
    </style>
</head>
<body>
    <h1>AI互动泡泡 - 小程序图标库</h1>
    <p class="description">本图标库包含52种不同图标，每种图标都有普通和激活状态两种版本，共104个PNG图标。图标采用不同形状和颜色，全部使用透明背景。</p>
    
    <div class="filter-container">
        <input type="text" id="search" class="filter-input" placeholder="搜索图标..." />
    </div>
    
    <div class="icons-container" id="icons-container">
        <!-- 图标将通过JavaScript动态加载 -->
    </div>

    <footer>
        <p>© 2025 AI互动泡泡团队 - 仅供内部使用</p>
    </footer>

    <script>
        // 图标列表
        const icons = [
            {name: 'home', text: '首页'},
            {name: 'profile', text: '个人中心'},
            {name: 'message', text: '消息'},
            {name: 'notification', text: '通知'},
            {name: 'settings', text: '设置'},
            {name: 'search', text: '搜索'},
            {name: 'favorite', text: '收藏'},
            {name: 'share', text: '分享'},
            {name: 'comment', text: '评论'},
            {name: 'like', text: '点赞'},
            {name: 'camera', text: '相机'},
            {name: 'gallery', text: '图库'},
            {name: 'location', text: '位置'},
            {name: 'calendar', text: '日历'},
            {name: 'clock', text: '时钟'},
            {name: 'cart', text: '购物车'},
            {name: 'order', text: '订单'},
            {name: 'wallet', text: '钱包'},
            {name: 'gift', text: '礼物'},
            {name: 'coupon', text: '优惠券'},
            {name: 'edit', text: '编辑'},
            {name: 'delete', text: '删除'},
            {name: 'add', text: '添加'},
            {name: 'minus', text: '减少'},
            {name: 'close', text: '关闭'},
            {name: 'menu', text: '菜单'},
            {name: 'list', text: '列表'},
            {name: 'grid', text: '网格'},
            {name: 'chart', text: '图表'},
            {name: 'graph', text: '图形'},
            {name: 'user', text: '用户'},
            {name: 'users', text: '用户组'},
            {name: 'lock', text: '锁定'},
            {name: 'unlock', text: '解锁'},
            {name: 'key', text: '钥匙'},
            {name: 'file', text: '文件'},
            {name: 'folder', text: '文件夹'},
            {name: 'download', text: '下载'},
            {name: 'upload', text: '上传'},
            {name: 'refresh', text: '刷新'},
            {name: 'play', text: '播放'},
            {name: 'pause', text: '暂停'},
            {name: 'stop', text: '停止'},
            {name: 'forward', text: '前进'},
            {name: 'backward', text: '后退'},
            {name: 'audio', text: '音频'},
            {name: 'video', text: '视频'},
            {name: 'wifi', text: '无线网络'},
            {name: 'bluetooth', text: '蓝牙'},
            {name: 'battery', text: '电池'},
            {name: 'book', text: '书本/学习'},
            {name: 'chat', text: '聊天'}
        ];

        const iconsContainer = document.getElementById('icons-container');
        const searchInput = document.getElementById('search');

        // 渲染图标
        function renderIcons(filter = '') {
            iconsContainer.innerHTML = '';
            
            const filteredIcons = icons.filter(icon => 
                icon.name.includes(filter.toLowerCase()) || 
                icon.text.includes(filter.toLowerCase())
            );
            
            filteredIcons.forEach(icon => {
                const card = document.createElement('div');
                card.className = 'icon-card';
                
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                const normalImg = document.createElement('img');
                normalImg.src = `assets/icons/new/${icon.name}.png`;
                normalImg.className = 'icon-img';
                normalImg.alt = icon.name;
                
                const activeImg = document.createElement('img');
                activeImg.src = `assets/icons/new/${icon.name}-active.png`;
                activeImg.className = 'icon-img';
                activeImg.alt = `${icon.name}-active`;
                
                preview.appendChild(normalImg);
                preview.appendChild(activeImg);
                
                const name = document.createElement('div');
                name.className = 'icon-name';
                name.textContent = `${icon.name} (${icon.text})`;
                
                card.appendChild(preview);
                card.appendChild(name);
                
                iconsContainer.appendChild(card);
            });
        }

        // 初始化
        renderIcons();

        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            renderIcons(e.target.value);
        });
    </script>
</body>
</html> 