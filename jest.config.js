module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',

  // 测试文件匹配模式
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],

  // 忽略的文件或目录
  testPathIgnorePatterns: ['/node_modules/'],

  // 覆盖率收集
  collectCoverage: true,
  collectCoverageFrom: [
    'utils/**/*.js',
    'services/**/*.js',
    '!**/__tests__/**',
    '!**/node_modules/**'
  ],

  // 覆盖率报告格式
  coverageReporters: ['text', 'lcov', 'html'],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },

  // 测试超时时间
  testTimeout: 30000,

  // 在每个测试文件执行前运行的设置文件
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],

  // 模块名称映射
  moduleNameMapper: {
    // 处理静态资源导入
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/__mocks__/fileMock.js',
    '\\.(css|less|scss|sass)$': '<rootDir>/__mocks__/styleMock.js'
  },

  // 转换器
  transform: {
    '^.+\\.js$': 'babel-jest'
  },

  // 全局变量
  globals: {
    wx: {
      // 模拟微信小程序API
      getSystemInfoSync: () => ({
        platform: 'devtools',
        windowWidth: 375,
        windowHeight: 667
      }),
      showToast: jest.fn(),
      showLoading: jest.fn(),
      hideLoading: jest.fn(),
      showModal: jest.fn(() => ({ confirm: true })),
      getStorageSync: jest.fn(),
      setStorageSync: jest.fn(),
      removeStorageSync: jest.fn(),
      request: jest.fn()
    },
    getCurrentPages: jest.fn(() => []),
    getApp: jest.fn(() => ({
      globalData: {}
    }))
  },

  // 是否显示每个测试的详细信息
  verbose: true
};
