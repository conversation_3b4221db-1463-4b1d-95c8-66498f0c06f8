# NebulaLearn 模拟数据和API服务

本目录包含NebulaLearn项目的模拟数据生成脚本和模拟API服务，用于前端开发阶段使用。

## 目录结构

```
mock-data/
  ├── api/                # 模拟API服务
  │   └── mockApiServer.js  # 模拟API服务器主文件
  ├── json/               # 生成的JSON格式模拟数据
  ├── scripts/            # 模拟数据生成脚本
  │   └── generateMockData.js  # 模拟数据生成主文件
  ├── sql/                # 生成的SQL格式模拟数据
  ├── package.json        # 项目依赖
  ├── setup.sh            # 安装和启动脚本
  └── README.md           # 说明文档
```

## 快速开始

### 安装依赖并生成数据

```bash
cd mock-data
chmod +x setup.sh
./setup.sh
```

或者手动执行以下步骤：

```bash
cd mock-data
npm install
node scripts/generateMockData.js
node api/mockApiServer.js
```

### 模拟API服务器

模拟API服务器默认运行在 http://localhost:3010，提供与实际后端API相同的接口。

## 模拟数据生成

模拟数据生成脚本会创建以下实体的数据：

- 用户 (users)
- 用户设置 (user_settings)
- 主题 (themes)
- 标签分类 (tag_categories)
- 标签 (tags)
- 学习模板 (learning_templates)
- 学习计划 (learning_plans)
- 练习 (exercises)
- 观点 (insights)
- 笔记 (notes)
- 每日内容 (daily_contents)
- 成就 (achievements)
- 徽章 (badges)

生成的数据会以JSON和SQL两种格式保存在相应目录中。

## 自定义数据生成

可以通过修改 `scripts/generateMockData.js` 文件来自定义生成的数据内容和数量。

例如，生成50个练习数据：

```javascript
await generateMockData.exercises(50);
```

## 模拟API使用

模拟API服务器提供与实际后端API相同的接口，可以通过以下方式使用：

### 示例请求

```javascript
// 获取主题列表
fetch('http://localhost:3010/api/v1/themes')
  .then(response => response.json())
  .then(data => console.log(data));

// 获取学习计划列表
fetch('http://localhost:3010/api/v1/learning-plans')
  .then(response => response.json())
  .then(data => console.log(data));
```

### 在小程序中使用

修改小程序的API基础URL配置，指向模拟API服务器：

```javascript
// app.js 或 utils/api.js
const apiBaseUrl = 'http://localhost:3010/api/v1';
```

## 注意事项

- 模拟数据仅用于开发阶段，不应用于生产环境
- 模拟API服务器不包含完整的业务逻辑，仅提供基本的CRUD操作
- 模拟数据之间的关联关系可能不完全符合实际业务需求，需要根据实际情况调整
