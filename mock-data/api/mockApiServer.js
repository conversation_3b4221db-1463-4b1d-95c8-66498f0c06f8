/**
 * 模拟API服务器
 * 提供与实际后端API相同的接口
 */

const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

// 创建Express应用
const app = express();
const PORT = 3010;

// 中间件
app.use(cors());
app.use(express.json());

// 加载模拟数据
const loadMockData = entityName => {
  try {
    const jsonPath = path.join(__dirname, `../json/${entityName}.json`);
    if (fs.existsSync(jsonPath)) {
      return JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    }
    return [];
  } catch (error) {
    console.error(`加载${entityName}数据失败:`, error);
    return [];
  }
};

// 通用错误处理
const errorHandler = (code, message) => ({
  success: false,
  error: { code, message }
});

// 通用成功响应
const successResponse = (data, message = '操作成功', meta = {}) => ({
  success: true,
  data,
  message,
  ...meta
});

// 模拟数据存储
const mockData = {
  users: [],
  user_settings: [],
  themes: [],
  tag_categories: [],
  tags: [],
  learning_templates: [],
  learning_plans: [],
  exercises: [],
  insights: [],
  notes: [],
  daily_contents: [],
  achievements: [],
  badges: []
};

// 初始化模拟数据
const initMockData = () => {
  Object.keys(mockData).forEach(key => {
    mockData[key] = loadMockData(key);
    console.log(`已加载${mockData[key].length}条${key}数据`);
  });
};

// API路由

// 主题相关API
app.get('/api/v1/themes', (req, res) => {
  try {
    // 处理查询参数
    const { is_active, sort_by, page = 1, pageSize = 20 } = req.query;
    let data = [...mockData.themes];

    // 应用筛选
    if (is_active !== undefined) {
      data = data.filter(t => t.is_active === (is_active === 'true'));
    }

    // 应用排序
    if (sort_by) {
      data.sort((a, b) => a[sort_by] - b[sort_by]);
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedData = data.slice(startIndex, startIndex + parseInt(pageSize));

    res.json(successResponse(paginatedData, '获取主题列表成功', {
      total: data.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    console.error('获取主题列表失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/themes/:id', (req, res) => {
  try {
    const { id } = req.params;
    const theme = mockData.themes.find(t => t.id === parseInt(id));

    if (!theme) {
      return res.status(404).json(errorHandler('THEME_NOT_FOUND', '主题不存在'));
    }

    res.json(successResponse(theme, '获取主题成功'));
  } catch (error) {
    console.error('获取主题详情失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 标签相关API
app.get('/api/v1/tags', (req, res) => {
  try {
    // 处理查询参数
    const { category_id, is_verified, is_official, page = 1, pageSize = 20 } = req.query;
    let data = [...mockData.tags];

    // 应用筛选
    if (category_id) {
      data = data.filter(t => t.category_id === parseInt(category_id));
    }
    if (is_verified !== undefined) {
      data = data.filter(t => t.is_verified === (is_verified === 'true'));
    }
    if (is_official !== undefined) {
      data = data.filter(t => t.is_official === (is_official === 'true'));
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedData = data.slice(startIndex, startIndex + parseInt(pageSize));

    res.json(successResponse(paginatedData, '获取标签列表成功', {
      total: data.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    console.error('获取标签列表失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/tags/:id', (req, res) => {
  try {
    const { id } = req.params;
    const tag = mockData.tags.find(t => t.id === parseInt(id));

    if (!tag) {
      return res.status(404).json(errorHandler('TAG_NOT_FOUND', '标签不存在'));
    }

    res.json(successResponse(tag, '获取标签成功'));
  } catch (error) {
    console.error('获取标签详情失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 学习计划相关API
app.get('/api/v1/learning-plans', (req, res) => {
  try {
    const { user_id, is_current, status, page = 1, pageSize = 20 } = req.query;
    let data = [...mockData.learning_plans];

    // 应用筛选
    if (user_id) {
      data = data.filter(p => p.user_id === parseInt(user_id));
    }
    if (is_current !== undefined) {
      data = data.filter(p => p.is_current === (is_current === 'true'));
    }
    if (status) {
      data = data.filter(p => p.status === status);
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedData = data.slice(startIndex, startIndex + parseInt(pageSize));

    res.json(successResponse(paginatedData, '获取学习计划列表成功', {
      total: data.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    console.error('获取学习计划列表失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/learning-plans/:id', (req, res) => {
  try {
    const { id } = req.params;
    const plan = mockData.learning_plans.find(p => p.id === parseInt(id));

    if (!plan) {
      return res.status(404).json(errorHandler('PLAN_NOT_FOUND', '学习计划不存在'));
    }

    res.json(successResponse(plan, '获取学习计划成功'));
  } catch (error) {
    console.error('获取学习计划详情失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.post('/api/v1/learning-plans', (req, res) => {
  try {
    const planData = req.body;
    const newId = mockData.learning_plans.length > 0 ? Math.max(...mockData.learning_plans.map(p => p.id)) + 1 : 1;

    const newPlan = {
      id: newId,
      ...planData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    mockData.learning_plans.push(newPlan);

    res.status(201).json(successResponse(newPlan, '创建学习计划成功'));
  } catch (error) {
    console.error('创建学习计划失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.put('/api/v1/learning-plans/:id', (req, res) => {
  try {
    const { id } = req.params;
    const planData = req.body;
    const planIndex = mockData.learning_plans.findIndex(p => p.id === parseInt(id));

    if (planIndex === -1) {
      return res.status(404).json(errorHandler('PLAN_NOT_FOUND', '学习计划不存在'));
    }

    const updatedPlan = {
      ...mockData.learning_plans[planIndex],
      ...planData,
      id: parseInt(id), // 确保 ID 不变
      updated_at: new Date().toISOString()
    };

    mockData.learning_plans[planIndex] = updatedPlan;

    res.json(successResponse(updatedPlan, '更新学习计划成功'));
  } catch (error) {
    console.error('更新学习计划失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.delete('/api/v1/learning-plans/:id', (req, res) => {
  try {
    const { id } = req.params;
    const planIndex = mockData.learning_plans.findIndex(p => p.id === parseInt(id));

    if (planIndex === -1) {
      return res.status(404).json(errorHandler('PLAN_NOT_FOUND', '学习计划不存在'));
    }

    // 软删除，设置 deleted_at 字段
    mockData.learning_plans[planIndex].deleted_at = new Date().toISOString();

    res.json(successResponse(null, '删除学习计划成功'));
  } catch (error) {
    console.error('删除学习计划失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.put('/api/v1/learning-plans/:id/activate', (req, res) => {
  try {
    const { id } = req.params;
    const planIndex = mockData.learning_plans.findIndex(p => p.id === parseInt(id));

    if (planIndex === -1) {
      return res.status(404).json(errorHandler('PLAN_NOT_FOUND', '学习计划不存在'));
    }

    // 将所有计划设置为非当前
    mockData.learning_plans.forEach(p => {
      if (p.user_id === mockData.learning_plans[planIndex].user_id) {
        p.is_current = false;
      }
    });

    // 将指定计划设置为当前
    mockData.learning_plans[planIndex].is_current = true;
    mockData.learning_plans[planIndex].updated_at = new Date().toISOString();

    res.json(successResponse(mockData.learning_plans[planIndex], '激活学习计划成功'));
  } catch (error) {
    console.error('激活学习计划失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/learning-plans/system/default', (req, res) => {
  try {
    const systemDefaultPlan = mockData.learning_plans.find(p => p.is_system_default === true);

    if (!systemDefaultPlan) {
      return res.status(404).json(errorHandler('PLAN_NOT_FOUND', '系统默认学习计划不存在'));
    }

    res.json(successResponse(systemDefaultPlan, '获取系统默认学习计划成功'));
  } catch (error) {
    console.error('获取系统默认学习计划失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 用户相关API
app.get('/api/v1/users/me', (req, res) => {
  try {
    // 模拟当前登录用户
    const currentUser = mockData.users[0];

    if (!currentUser) {
      return res.status(404).json(errorHandler('USER_NOT_FOUND', '用户不存在'));
    }

    res.json(successResponse(currentUser, '获取当前用户信息成功'));
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 练习相关API
app.get('/api/v1/exercises', (req, res) => {
  try {
    const { difficulty, is_verified, is_official, page = 1, pageSize = 20 } = req.query;
    let data = [...mockData.exercises];

    // 应用筛选
    if (difficulty) {
      data = data.filter(e => e.difficulty === difficulty);
    }
    if (is_verified !== undefined) {
      data = data.filter(e => e.is_verified === (is_verified === 'true'));
    }
    if (is_official !== undefined) {
      data = data.filter(e => e.is_official === (is_official === 'true'));
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedData = data.slice(startIndex, startIndex + parseInt(pageSize));

    res.json(successResponse(paginatedData, '获取练习列表成功', {
      total: data.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    console.error('获取练习列表失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/exercises/:id', (req, res) => {
  try {
    const { id } = req.params;
    const exercise = mockData.exercises.find(e => e.id === parseInt(id));

    if (!exercise) {
      return res.status(404).json(errorHandler('EXERCISE_NOT_FOUND', '练习不存在'));
    }

    res.json(successResponse(exercise, '获取练习成功'));
  } catch (error) {
    console.error('获取练习详情失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.post('/api/v1/exercises/:id/complete', (req, res) => {
  try {
    const { id } = req.params;
    const { rating, notes } = req.body;
    const exercise = mockData.exercises.find(e => e.id === parseInt(id));

    if (!exercise) {
      return res.status(404).json(errorHandler('EXERCISE_NOT_FOUND', '练习不存在'));
    }

    // 更新练习完成数据
    exercise.completion_count += 1;

    // 如果提供了评分，更新平均评分
    if (rating) {
      const totalRatings = exercise.completion_count * exercise.average_rating;
      exercise.average_rating = ((totalRatings + rating) / (exercise.completion_count)).toFixed(1);
    }

    res.json(successResponse({
      exercise_id: parseInt(id),
      completed_at: new Date().toISOString(),
      rating,
      notes
    }, '标记练习完成成功'));
  } catch (error) {
    console.error('标记练习完成失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 观点相关API
app.get('/api/v1/insights', (req, res) => {
  try {
    const { is_verified, is_official, page = 1, pageSize = 20 } = req.query;
    let data = [...mockData.insights];

    // 应用筛选
    if (is_verified !== undefined) {
      data = data.filter(i => i.is_verified === (is_verified === 'true'));
    }
    if (is_official !== undefined) {
      data = data.filter(i => i.is_official === (is_official === 'true'));
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedData = data.slice(startIndex, startIndex + parseInt(pageSize));

    res.json(successResponse(paginatedData, '获取观点列表成功', {
      total: data.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    console.error('获取观点列表失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/insights/:id', (req, res) => {
  try {
    const { id } = req.params;
    const insight = mockData.insights.find(i => i.id === parseInt(id));

    if (!insight) {
      return res.status(404).json(errorHandler('INSIGHT_NOT_FOUND', '观点不存在'));
    }

    res.json(successResponse(insight, '获取观点成功'));
  } catch (error) {
    console.error('获取观点详情失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.post('/api/v1/insights/:id/read', (req, res) => {
  try {
    const { id } = req.params;
    const insight = mockData.insights.find(i => i.id === parseInt(id));

    if (!insight) {
      return res.status(404).json(errorHandler('INSIGHT_NOT_FOUND', '观点不存在'));
    }

    // 更新观点阅读数据
    insight.read_count += 1;

    res.json(successResponse({
      insight_id: parseInt(id),
      read_at: new Date().toISOString()
    }, '标记观点已读成功'));
  } catch (error) {
    console.error('标记观点已读失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 笔记相关API
app.get('/api/v1/notes', (req, res) => {
  try {
    const { user_id, is_public, page = 1, pageSize = 20 } = req.query;
    let data = [...mockData.notes];

    // 应用筛选
    if (user_id) {
      data = data.filter(n => n.user_id === parseInt(user_id));
    }
    if (is_public !== undefined) {
      data = data.filter(n => n.is_public === (is_public === 'true'));
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedData = data.slice(startIndex, startIndex + parseInt(pageSize));

    res.json(successResponse(paginatedData, '获取笔记列表成功', {
      total: data.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    console.error('获取笔记列表失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/notes/:id', (req, res) => {
  try {
    const { id } = req.params;
    const note = mockData.notes.find(n => n.id === parseInt(id));

    if (!note) {
      return res.status(404).json(errorHandler('NOTE_NOT_FOUND', '笔记不存在'));
    }

    // 更新浏览数
    note.view_count += 1;

    res.json(successResponse(note, '获取笔记成功'));
  } catch (error) {
    console.error('获取笔记详情失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.post('/api/v1/notes', (req, res) => {
  try {
    const noteData = req.body;
    const newId = mockData.notes.length > 0 ? Math.max(...mockData.notes.map(n => n.id)) + 1 : 1;

    const newNote = {
      id: newId,
      ...noteData,
      view_count: 0,
      like_count: 0,
      comment_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    mockData.notes.push(newNote);

    res.status(201).json(successResponse(newNote, '创建笔记成功'));
  } catch (error) {
    console.error('创建笔记失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.put('/api/v1/notes/:id', (req, res) => {
  try {
    const { id } = req.params;
    const noteData = req.body;
    const noteIndex = mockData.notes.findIndex(n => n.id === parseInt(id));

    if (noteIndex === -1) {
      return res.status(404).json(errorHandler('NOTE_NOT_FOUND', '笔记不存在'));
    }

    const updatedNote = {
      ...mockData.notes[noteIndex],
      ...noteData,
      id: parseInt(id), // 确保 ID 不变
      updated_at: new Date().toISOString()
    };

    mockData.notes[noteIndex] = updatedNote;

    res.json(successResponse(updatedNote, '更新笔记成功'));
  } catch (error) {
    console.error('更新笔记失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.delete('/api/v1/notes/:id', (req, res) => {
  try {
    const { id } = req.params;
    const noteIndex = mockData.notes.findIndex(n => n.id === parseInt(id));

    if (noteIndex === -1) {
      return res.status(404).json(errorHandler('NOTE_NOT_FOUND', '笔记不存在'));
    }

    // 软删除，设置 deleted_at 字段
    mockData.notes[noteIndex].deleted_at = new Date().toISOString();

    res.json(successResponse(null, '删除笔记成功'));
  } catch (error) {
    console.error('删除笔记失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 每日内容相关API
app.get('/api/v1/learning-plans/:planId/daily-content', (req, res) => {
  try {
    const { planId } = req.params;
    const { date } = req.query;

    // 检查学习计划是否存在
    const plan = mockData.learning_plans.find(p => p.id === parseInt(planId));
    if (!plan) {
      return res.status(404).json(errorHandler('PLAN_NOT_FOUND', '学习计划不存在'));
    }

    // 获取该计划的每日内容
    let dailyContents = mockData.daily_contents.filter(dc => dc.plan_id === parseInt(planId));

    // 如果指定了日期，过滤出该日期的内容
    if (date) {
      dailyContents = dailyContents.filter(dc => dc.date === date);
    }

    // 按内容类型分组
    const groupedContents = {
      exercises: [],
      insights: []
    };

    // 填充完整的内容数据
    for (const content of dailyContents) {
      if (content.content_type === 'exercise') {
        const exercise = mockData.exercises.find(e => e.id === content.content_id);
        if (exercise) {
          groupedContents.exercises.push({
            ...content,
            exercise
          });
        }
      } else if (content.content_type === 'insight') {
        const insight = mockData.insights.find(i => i.id === content.content_id);
        if (insight) {
          groupedContents.insights.push({
            ...content,
            insight
          });
        }
      }
    }

    res.json(successResponse(groupedContents, '获取每日内容成功'));
  } catch (error) {
    console.error('获取每日内容失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 广场相关API
app.get('/api/v1/square/notes', (req, res) => {
  try {
    const { tag_id, page = 1, pageSize = 20 } = req.query;

    // 获取公开笔记
    let notes = mockData.notes.filter(n => n.is_public === true && !n.deleted_at);

    // 如果指定了标签，过滤出包含该标签的笔记
    // 注意：这里简化处理，实际应该查询关联表
    if (tag_id) {
      // 模拟标签关联，实际应该使用关联表
      notes = notes.filter(n => n.id % 3 === parseInt(tag_id) % 3); // 简化的关联逻辑
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedNotes = notes.slice(startIndex, startIndex + parseInt(pageSize));

    // 添加用户信息
    const notesWithUser = paginatedNotes.map(note => {
      const user = mockData.users.find(u => u.id === note.user_id);
      return {
        ...note,
        user: user ? { id: user.id, nickname: user.nickname, avatar_url: user.avatar_url } : null
      };
    });

    res.json(successResponse(notesWithUser, '获取广场笔记成功', {
      total: notes.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    console.error('获取广场笔记失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

app.get('/api/v1/square/tags', (req, res) => {
  try {
    // 获取热门标签，按使用次数排序
    const popularTags = [...mockData.tags]
      .sort((a, b) => b.usage_count - a.usage_count)
      .slice(0, 10);

    res.json(successResponse(popularTags, '获取广场标签成功'));
  } catch (error) {
    console.error('获取广场标签失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 认证相关API
app.post('/api/v1/auth/login', (req, res) => {
  try {
    const { code, userInfo } = req.body;

    // 模拟登录成功
    const token = 'mock_token_' + Date.now();
    const expiresIn = 7200; // 2小时过期
    const user = mockData.users[0];

    res.json(successResponse({
      token,
      expiresIn,
      user
    }, '登录成功'));
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`模拟API服务器运行在 http://localhost:${PORT}`);
  initMockData();
});

// 导出模块
module.exports = app;
