/**
 * 模拟数据生成脚本
 * 用于生成符合V3数据库结构的模拟数据
 */

// 导入依赖
const fs = require('fs');
const path = require('path');

// 模拟数据生成器
const generateMockData = {
  // 生成所有数据
  all: async () => {
    console.log('开始生成所有模拟数据...');

    // 按顺序生成数据，确保依赖关系正确
    await generateMockData.users();
    await generateMockData.userSettings();
    await generateMockData.themes();
    await generateMockData.tagCategories();
    await generateMockData.tags();
    await generateMockData.learningTemplates();
    await generateMockData.learningPlans();
    await generateMockData.exercises();
    await generateMockData.insights();
    await generateMockData.notes();
    await generateMockData.dailyContents();
    await generateMockData.achievements();
    await generateMockData.badges();

    console.log('所有模拟数据生成完成！');
  },

  // 生成用户数据
  users: async (count = 10) => {
    console.log(`生成${count}个用户数据...`);
    const users = [];

    // 生成用户数据
    for (let i = 1; i <= count; i++) {
      users.push({
        id: i,
        username: `user${i}`,
        nickname: `用户${i}`,
        avatar_url: `https://example.com/avatars/user${i}.jpg`,
        email: `user${i}@example.com`,
        phone: `1380000${i.toString().padStart(4, '0')}`,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }

    // 保存数据
    await saveData('users', users);
    console.log(`用户数据生成完成，共${users.length}条记录`);
    return users;
  },

  // 生成用户设置数据
  userSettings: async () => {
    console.log('生成用户设置数据...');
    const users = await loadData('users');
    const userSettings = [];

    for (const user of users) {
      userSettings.push({
        id: user.id,
        user_id: user.id,
        theme_mode: 'light',
        language: 'zh_CN',
        notification_enabled: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }

    // 保存数据
    await saveData('user_settings', userSettings);
    console.log(`用户设置数据生成完成，共${userSettings.length}条记录`);
    return userSettings;
  },

  // 生成主题数据
  themes: async (count = 5) => {
    console.log(`生成${count}个主题数据...`);
    const themes = [
      {
        id: 1,
        name: '平台指南',
        english_name: 'Platform Guide',
        description: '了解如何使用NebulaLearn平台的各项功能',
        icon: 'guide',
        color: '#4A90E2',
        cover_image_url: 'https://example.com/images/platform-guide.jpg',
        sort_order: 1,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        name: '职场技能',
        english_name: 'Career Skills',
        description: '提升职场竞争力的必备技能',
        icon: 'career',
        color: '#50E3C2',
        cover_image_url: 'https://example.com/images/career-skills.jpg',
        sort_order: 2,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 3,
        name: '人际沟通',
        english_name: 'Interpersonal Communication',
        description: '掌握高效沟通技巧，改善人际关系',
        icon: 'communication',
        color: '#F5A623',
        cover_image_url: 'https://example.com/images/interpersonal-communication.jpg',
        sort_order: 3,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    // 如果需要更多主题，生成额外的数据
    if (count > 3) {
      for (let i = 4; i <= count; i++) {
        themes.push({
          id: i,
          name: `主题${i}`,
          english_name: `Theme ${i}`,
          description: `这是主题${i}的描述`,
          icon: `theme${i}`,
          color: getRandomColor(),
          cover_image_url: `https://example.com/images/theme${i}.jpg`,
          sort_order: i,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('themes', themes);
    console.log(`主题数据生成完成，共${themes.length}条记录`);
    return themes;
  },

  // 生成学习模板数据
  learningTemplates: async (count = 10) => {
    console.log(`生成${count}个学习模板数据...`);
    const themes = await loadData('themes');
    const templates = [];

    // 预定义一些学习模板
    const predefinedTemplates = [
      {
        name: '高效沟通入门',
        theme_id: 3,
        description: '7天掌握基础沟通技巧，提升表达能力',
        target_days: 7,
        difficulty: 'beginner',
        estimated_minutes_per_day: 20
      },
      {
        name: '职场人际关系提升',
        theme_id: 3,
        description: '14天建立良好的职场人际关系网络',
        target_days: 14,
        difficulty: 'intermediate',
        estimated_minutes_per_day: 30
      },
      {
        name: '领导力培养计划',
        theme_id: 2,
        description: '21天培养基础领导能力，提升团队管理水平',
        target_days: 21,
        difficulty: 'advanced',
        estimated_minutes_per_day: 45
      },
      {
        name: '时间管理大师',
        theme_id: 2,
        description: '10天掌握高效时间管理技巧',
        target_days: 10,
        difficulty: 'intermediate',
        estimated_minutes_per_day: 25
      },
      {
        name: '平台使用指南',
        theme_id: 1,
        description: '3天快速了解平台所有功能',
        target_days: 3,
        difficulty: 'beginner',
        estimated_minutes_per_day: 15
      }
    ];

    // 添加预定义模板
    for (let i = 0; i < predefinedTemplates.length; i++) {
      const template = predefinedTemplates[i];
      templates.push({
        id: i + 1,
        theme_id: template.theme_id,
        name: template.name,
        description: template.description,
        cover_image_url: `https://example.com/images/templates/${i + 1}.jpg`,
        target_days: template.target_days,
        daily_goal_exercises: Math.floor(Math.random() * 3) + 1,
        daily_goal_insights: Math.floor(Math.random() * 5) + 3,
        daily_goal_minutes: template.estimated_minutes_per_day,
        difficulty: template.difficulty,
        is_official: true,
        is_public: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }

    // 如果需要更多模板，生成额外的数据
    if (count > predefinedTemplates.length) {
      for (let i = predefinedTemplates.length + 1; i <= count; i++) {
        const themeId = themes[Math.floor(Math.random() * themes.length)].id;
        const targetDays = [7, 10, 14, 21, 28][Math.floor(Math.random() * 5)];
        const difficulty = ['beginner', 'intermediate', 'advanced'][Math.floor(Math.random() * 3)];

        templates.push({
          id: i,
          theme_id: themeId,
          name: `学习模板${i}`,
          description: `这是学习模板${i}的描述，为期${targetDays}天的学习计划`,
          cover_image_url: `https://example.com/images/templates/${i}.jpg`,
          target_days: targetDays,
          daily_goal_exercises: Math.floor(Math.random() * 3) + 1,
          daily_goal_insights: Math.floor(Math.random() * 5) + 3,
          daily_goal_minutes: [15, 20, 30, 45, 60][Math.floor(Math.random() * 5)],
          difficulty: difficulty,
          is_official: Math.random() > 0.3,
          is_public: Math.random() > 0.2,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('learning_templates', templates);
    console.log(`学习模板数据生成完成，共${templates.length}条记录`);
    return templates;
  },

  // 生成学习计划数据
  learningPlans: async (count = 15) => {
    console.log(`生成${count}个学习计划数据...`);
    const users = await loadData('users');
    const templates = await loadData('learning_templates');
    const themes = await loadData('themes');
    const plans = [];

    // 为每个用户创建一些学习计划
    for (let userId = 1; userId <= Math.min(users.length, 5); userId++) {
      // 每个用户创建2-3个计划
      const userPlanCount = Math.floor(Math.random() * 2) + 2;

      for (let j = 0; j < userPlanCount && plans.length < count; j++) {
        const templateId = templates[Math.floor(Math.random() * templates.length)].id;
        const template = templates.find(t => t.id === templateId);
        const themeId = template.theme_id;

        // 计算进度
        const targetDays = template.target_days;
        const completedDays = Math.floor(Math.random() * (targetDays + 1));
        const progress = Math.floor((completedDays / targetDays) * 100);

        // 计算状态
        let status;
        if (completedDays === 0) status = 'not_started';
        else if (completedDays === targetDays) status = 'completed';
        else if (Math.random() > 0.8) status = 'paused';
        else if (Math.random() > 0.9) status = 'abandoned';
        else status = 'in_progress';

        // 计算日期
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - completedDays);
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + targetDays);

        plans.push({
          id: plans.length + 1,
          user_id: userId,
          template_id: templateId,
          theme_id: themeId,
          title: `${template.name} - 我的计划`,
          description: template.description,
          cover_image_url: template.cover_image_url,
          target_days: targetDays,
          completed_days: completedDays,
          progress: progress,
          daily_goal_exercises: template.daily_goal_exercises,
          daily_goal_insights: template.daily_goal_insights,
          daily_goal_minutes: template.daily_goal_minutes,
          status: status,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0],
          is_current: j === 0, // 第一个计划是当前计划
          is_system_default: false,
          is_public: Math.random() > 0.7,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 添加一个系统默认计划
    plans.push({
      id: plans.length + 1,
      user_id: null,
      template_id: 5, // 平台使用指南模板
      theme_id: 1, // 平台指南主题
      title: '平台使用指南 - 系统默认计划',
      description: '3天快速了解平台所有功能',
      cover_image_url: 'https://example.com/images/templates/5.jpg',
      target_days: 3,
      completed_days: 0,
      progress: 0,
      daily_goal_exercises: 1,
      daily_goal_insights: 3,
      daily_goal_minutes: 15,
      status: 'not_started',
      start_date: null,
      end_date: null,
      is_current: false,
      is_system_default: true,
      is_public: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    // 保存数据
    await saveData('learning_plans', plans);
    console.log(`学习计划数据生成完成，共${plans.length}条记录`);
    return plans;
  },

  // 生成练习数据
  exercises: async (count = 30) => {
    console.log(`生成${count}个练习数据...`);
    const tags = await loadData('tags');
    const exercises = [];

    // 预定义一些练习
    const predefinedExercises = [
      {
        title: '积极倾听练习',
        description: '与朋友进行15分钟对话，专注倾听并复述对方观点',
        content: '1. 找一位朋友或同事进行15分钟的对话\n2. 不要打断对方，专注倾听\n3. 在对方说完后，尝试复述他们的观点\n4. 询问你的理解是否正确\n5. 记录你的感受和发现',
        difficulty: 'beginner',
        estimated_minutes: 15,
        related_tags: [2, 3, 13] // 沟通技巧, 领导力, 冲突处理
      },
      {
        title: '时间块工作法',
        description: '使用番茄工作法完成一项重要任务，记录效率变化',
        content: '1. 选择一项需要专注完成的任务\n2. 设置25分钟的计时器\n3. 在这25分钟内，排除所有干扰，专注于任务\n4. 计时结束后，休息5分钟\n5. 重复以上步骤4次后，休息较长时间（15-30分钟）\n6. 记录你完成的工作量和感受',
        difficulty: 'beginner',
        estimated_minutes: 25,
        related_tags: [1, 6] // 时间管理, 初级
      },
      {
        title: '团队冲突解决模拟',
        description: '模拟解决团队中的意见分歧，练习冲突调解技巧',
        content: '1. 找2-3个朋友模拟一个团队冲突场景\n2. 场景：团队成员对项目方向有不同意见\n3. 你作为调解人，尝试引导大家达成共识\n4. 使用以下技巧：\n   - 确保每个人都有发言机会\n   - 寻找共同点\n   - 提出折中方案\n   - 关注问题而非人\n5. 记录解决过程和最终结果',
        difficulty: 'advanced',
        estimated_minutes: 45,
        related_tags: [3, 4, 8, 13] // 领导力, 团队协作, 高级, 冲突处理
      }
    ];

    // 添加预定义练习
    for (let i = 0; i < predefinedExercises.length; i++) {
      const exercise = predefinedExercises[i];
      exercises.push({
        id: i + 1,
        title: exercise.title,
        description: exercise.description,
        content: exercise.content,
        cover_image_url: `https://example.com/images/exercises/${i + 1}.jpg`,
        difficulty: exercise.difficulty,
        estimated_minutes: exercise.estimated_minutes,
        completion_count: Math.floor(Math.random() * 500),
        average_rating: (3 + Math.random() * 2).toFixed(1),
        is_verified: true,
        is_official: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      // 创建练习-标签关联
      for (const tagId of exercise.related_tags) {
        // 这里可以创建exercise_tag关联表数据
      }
    }

    // 如果需要更多练习，生成额外的数据
    if (count > predefinedExercises.length) {
      for (let i = predefinedExercises.length + 1; i <= count; i++) {
        const difficulty = ['beginner', 'intermediate', 'advanced'][Math.floor(Math.random() * 3)];
        const estimatedMinutes = [10, 15, 20, 30, 45, 60][Math.floor(Math.random() * 6)];

        exercises.push({
          id: i,
          title: `练习${i}`,
          description: `这是练习${i}的描述，预计需要${estimatedMinutes}分钟完成`,
          content: `练习${i}的详细内容和步骤：\n1. 第一步\n2. 第二步\n3. 第三步\n4. 记录你的感受和发现`,
          cover_image_url: `https://example.com/images/exercises/${i}.jpg`,
          difficulty: difficulty,
          estimated_minutes: estimatedMinutes,
          completion_count: Math.floor(Math.random() * 500),
          average_rating: (3 + Math.random() * 2).toFixed(1),
          is_verified: Math.random() > 0.2,
          is_official: Math.random() > 0.3,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('exercises', exercises);
    console.log(`练习数据生成完成，共${exercises.length}条记录`);
    return exercises;
  },

  // 生成观点数据
  insights: async (count = 30) => {
    console.log(`生成${count}个观点数据...`);
    const tags = await loadData('tags');
    const insights = [];

    // 预定义一些观点
    const predefinedInsights = [
      {
        title: '有效沟通的五个层次',
        content: '有效沟通包含五个层次：信息传递、情感表达、需求提出、观点协商和行动计划。大多数人只在前两个层次沟通，而真正有效的沟通需要覆盖所有五个层次。',
        source: '《非暴力沟通》 - 马歇尔·罗森堡',
        related_tags: [2, 3, 7] // 沟通技巧, 领导力, 中级
      },
      {
        title: '时间管理的四象限',
        content: '时间管理的四象限法将任务分为四类：重要且紧急、重要但不紧急、紧急但不重要、不重要也不紧急。将时间集中在重要但不紧急的任务上，可以显著提高生产力。',
        source: '《要事第一》 - 史蒂芬·柯维',
        related_tags: [1, 7] // 时间管理, 中级
      },
      {
        title: '团队发展的五个阶段',
        content: '团队发展通常经历五个阶段：组建期（Forming）、冲突期（Storming）、规范期（Norming）、执行期（Performing）和解散期（Adjourning）。了解这些阶段可以帮助领导者更好地引导团队发展。',
        source: '布鲁斯·塔克曼的团队发展理论',
        related_tags: [3, 4, 8] // 领导力, 团队协作, 高级
      }
    ];

    // 添加预定义观点
    for (let i = 0; i < predefinedInsights.length; i++) {
      const insight = predefinedInsights[i];
      insights.push({
        id: i + 1,
        title: insight.title,
        content: insight.content,
        source: insight.source,
        read_count: Math.floor(Math.random() * 1000),
        like_count: Math.floor(Math.random() * 500),
        is_verified: true,
        is_official: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      // 创建观点-标签关联
      for (const tagId of insight.related_tags) {
        // 这里可以创建insight_tag关联表数据
      }
    }

    // 如果需要更多观点，生成额外的数据
    if (count > predefinedInsights.length) {
      for (let i = predefinedInsights.length + 1; i <= count; i++) {
        insights.push({
          id: i,
          title: `观点${i}`,
          content: `这是观点${i}的内容，提供了关于某个主题的深入见解和启发。`,
          source: `来源${i}`,
          read_count: Math.floor(Math.random() * 1000),
          like_count: Math.floor(Math.random() * 500),
          is_verified: Math.random() > 0.2,
          is_official: Math.random() > 0.3,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('insights', insights);
    console.log(`观点数据生成完成，共${insights.length}条记录`);
    return insights;
  },

  // 生成笔记数据
  notes: async (count = 20) => {
    console.log(`生成${count}个笔记数据...`);
    const users = await loadData('users');
    const tags = await loadData('tags');
    const notes = [];

    // 预定义一些笔记
    const predefinedNotes = [
      {
        title: '我的沟通技巧学习笔记',
        content: '# 沟通技巧学习笔记\n\n## 积极倾听\n\n- 保持眼神接触\n- 不要打断对方\n- 提问来澄清理解\n- 复述对方观点\n\n## 非语言沟通\n\n- 注意身体语言\n- 保持开放姿态\n- 适当手势辅助表达\n\n## 有效反馈\n\n- 具体而非模糊\n- 关注行为而非人\n- 提供可行建议',
        user_id: 1,
        related_tags: [2, 6] // 沟通技巧, 初级
      },
      {
        title: '时间管理实践总结',
        content: '# 时间管理实践总结\n\n## 番茄工作法实践\n\n最近一周尝试了番茄工作法，效果显著：\n\n- 专注度提高了50%\n- 完成任务数量增加30%\n- 工作质量明显提升\n\n## 任务分类法\n\n将任务分为四类：\n\n1. 重要且紧急 - 立即处理\n2. 重要不紧急 - 安排时间处理\n3. 紧急不重要 - 委托他人\n4. 不重要不紧急 - 删除或延后\n\n这种分类法帮我更好地安排优先级。',
        user_id: 2,
        related_tags: [1, 7] // 时间管理, 中级
      }
    ];

    // 添加预定义笔记
    for (let i = 0; i < predefinedNotes.length; i++) {
      const note = predefinedNotes[i];
      notes.push({
        id: i + 1,
        title: note.title,
        content: note.content,
        user_id: note.user_id,
        view_count: Math.floor(Math.random() * 500),
        like_count: Math.floor(Math.random() * 200),
        comment_count: Math.floor(Math.random() * 50),
        is_public: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      // 创建笔记-标签关联
      for (const tagId of note.related_tags) {
        // 这里可以创建note_tag关联表数据
      }
    }

    // 如果需要更多笔记，生成额外的数据
    if (count > predefinedNotes.length) {
      for (let i = predefinedNotes.length + 1; i <= count; i++) {
        const userId = users[Math.floor(Math.random() * users.length)].id;
        notes.push({
          id: i,
          title: `笔记${i}`,
          content: `# 笔记${i}\n\n这是笔记${i}的内容，记录了我的学习心得和体会。\n\n## 主要收获\n\n- 第一点收获\n- 第二点收获\n- 第三点收获\n\n## 实践应用\n\n1. 应用场景一\n2. 应用场景二\n3. 应用场景三`,
          user_id: userId,
          view_count: Math.floor(Math.random() * 500),
          like_count: Math.floor(Math.random() * 200),
          comment_count: Math.floor(Math.random() * 50),
          is_public: Math.random() > 0.3,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('notes', notes);
    console.log(`笔记数据生成完成，共${notes.length}条记录`);
    return notes;
  },

  // 生成每日内容数据
  dailyContents: async (count = 30) => {
    console.log(`生成${count}个每日内容数据...`);
    const plans = await loadData('learning_plans');
    const exercises = await loadData('exercises');
    const insights = await loadData('insights');
    const dailyContents = [];

    // 为每个学习计划生成每日内容
    for (const plan of plans) {
      // 跳过系统默认计划
      if (plan.is_system_default) continue;

      // 为每个已完成的天数生成内容
      for (let day = 1; day <= plan.completed_days && dailyContents.length < count; day++) {
        // 计算日期
        const date = new Date(plan.start_date);
        date.setDate(date.getDate() + day - 1);
        const dateStr = date.toISOString().split('T')[0];

        // 生成练习
        for (let i = 0; i < plan.daily_goal_exercises; i++) {
          const exercise = exercises[Math.floor(Math.random() * exercises.length)];
          dailyContents.push({
            id: dailyContents.length + 1,
            plan_id: plan.id,
            content_type: 'exercise',
            content_id: exercise.id,
            day_number: day,
            date: dateStr,
            is_completed: Math.random() > 0.2,
            completion_time: Math.random() > 0.2 ? new Date(date.getTime() + Math.random() * 86400000).toISOString() : null,
            user_rating: Math.random() > 0.3 ? Math.floor(Math.random() * 5) + 1 : null,
            user_notes: Math.random() > 0.7 ? '完成练习的感受和反思...' : null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        }

        // 生成观点
        for (let i = 0; i < plan.daily_goal_insights; i++) {
          const insight = insights[Math.floor(Math.random() * insights.length)];
          dailyContents.push({
            id: dailyContents.length + 1,
            plan_id: plan.id,
            content_type: 'insight',
            content_id: insight.id,
            day_number: day,
            date: dateStr,
            is_completed: Math.random() > 0.3,
            completion_time: Math.random() > 0.3 ? new Date(date.getTime() + Math.random() * 86400000).toISOString() : null,
            user_rating: null,
            user_notes: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        }
      }

      // 为未完成的天数生成内容
      if (plan.status === 'in_progress') {
        const day = plan.completed_days + 1;
        const date = new Date(plan.start_date);
        date.setDate(date.getDate() + day - 1);
        const dateStr = date.toISOString().split('T')[0];

        // 生成练习
        for (let i = 0; i < plan.daily_goal_exercises && dailyContents.length < count; i++) {
          const exercise = exercises[Math.floor(Math.random() * exercises.length)];
          dailyContents.push({
            id: dailyContents.length + 1,
            plan_id: plan.id,
            content_type: 'exercise',
            content_id: exercise.id,
            day_number: day,
            date: dateStr,
            is_completed: false,
            completion_time: null,
            user_rating: null,
            user_notes: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        }

        // 生成观点
        for (let i = 0; i < plan.daily_goal_insights && dailyContents.length < count; i++) {
          const insight = insights[Math.floor(Math.random() * insights.length)];
          dailyContents.push({
            id: dailyContents.length + 1,
            plan_id: plan.id,
            content_type: 'insight',
            content_id: insight.id,
            day_number: day,
            date: dateStr,
            is_completed: false,
            completion_time: null,
            user_rating: null,
            user_notes: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        }
      }
    }

    // 保存数据
    await saveData('daily_contents', dailyContents);
    console.log(`每日内容数据生成完成，共${dailyContents.length}条记录`);
    return dailyContents;
  },

  // 生成标签分类数据
  tagCategories: async () => {
    console.log('生成标签分类数据...');
    const tagCategories = [
      {
        id: 1,
        name: '技能类型',
        description: '按技能类型分类',
        sort_order: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        name: '难度级别',
        description: '按难度级别分类',
        sort_order: 2,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 3,
        name: '应用场景',
        description: '按应用场景分类',
        sort_order: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    // 保存数据
    await saveData('tag_categories', tagCategories);
    console.log(`标签分类数据生成完成，共${tagCategories.length}条记录`);
    return tagCategories;
  },

  // 生成成就数据
  achievements: async (count = 15) => {
    console.log(`生成${count}个成就数据...`);
    const achievements = [];

    // 预定义一些成就
    const predefinedAchievements = [
      {
        name: '新手上路',
        description: '完成第一个学习计划',
        icon: 'achievement_beginner',
        requirement_type: 'plan_completion',
        requirement_value: 1,
        points: 10,
        is_hidden: false
      },
      {
        name: '坚持不懈',
        description: '连续7天完成学习任务',
        icon: 'achievement_streak',
        requirement_type: 'daily_streak',
        requirement_value: 7,
        points: 20,
        is_hidden: false
      },
      {
        name: '练习大师',
        description: '完成50个练习',
        icon: 'achievement_exercise',
        requirement_type: 'exercise_completion',
        requirement_value: 50,
        points: 30,
        is_hidden: false
      },
      {
        name: '智慧收集者',
        description: '阅读100个观点',
        icon: 'achievement_insight',
        requirement_type: 'insight_reading',
        requirement_value: 100,
        points: 30,
        is_hidden: false
      },
      {
        name: '分享达人',
        description: '发布10篇公开笔记',
        icon: 'achievement_share',
        requirement_type: 'note_publishing',
        requirement_value: 10,
        points: 25,
        is_hidden: false
      },
      {
        name: '完美主义者',
        description: '获得20个满分评价',
        icon: 'achievement_perfectionist',
        requirement_type: 'perfect_ratings',
        requirement_value: 20,
        points: 35,
        is_hidden: false
      },
      {
        name: '探索者',
        description: '尝试5个不同主题的学习计划',
        icon: 'achievement_explorer',
        requirement_type: 'theme_exploration',
        requirement_value: 5,
        points: 40,
        is_hidden: false
      },
      {
        name: '隐藏成就',
        description: '发现这个隐藏成就',
        icon: 'achievement_hidden',
        requirement_type: 'special',
        requirement_value: 1,
        points: 50,
        is_hidden: true
      }
    ];

    // 添加预定义成就
    for (let i = 0; i < predefinedAchievements.length; i++) {
      const achievement = predefinedAchievements[i];
      achievements.push({
        id: i + 1,
        name: achievement.name,
        description: achievement.description,
        icon: achievement.icon,
        requirement_type: achievement.requirement_type,
        requirement_value: achievement.requirement_value,
        points: achievement.points,
        is_hidden: achievement.is_hidden,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }

    // 如果需要更多成就，生成额外的数据
    if (count > predefinedAchievements.length) {
      for (let i = predefinedAchievements.length + 1; i <= count; i++) {
        const requirementTypes = ['plan_completion', 'daily_streak', 'exercise_completion', 'insight_reading', 'note_publishing', 'perfect_ratings', 'theme_exploration', 'special'];
        const requirementType = requirementTypes[Math.floor(Math.random() * requirementTypes.length)];

        achievements.push({
          id: i,
          name: `成就${i}`,
          description: `这是成就${i}的描述，完成特定条件即可获得`,
          icon: `achievement_${i}`,
          requirement_type: requirementType,
          requirement_value: Math.floor(Math.random() * 100) + 1,
          points: Math.floor(Math.random() * 50) + 10,
          is_hidden: Math.random() > 0.8,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('achievements', achievements);
    console.log(`成就数据生成完成，共${achievements.length}条记录`);
    return achievements;
  },

  // 生成徽章数据
  badges: async (count = 10) => {
    console.log(`生成${count}个徽章数据...`);
    const badges = [];

    // 预定义一些徽章
    const predefinedBadges = [
      {
        name: '沟通大师',
        description: '在沟通技巧方面表现出色',
        icon: 'badge_communication',
        category: 'skill',
        level: 'gold',
        requirement_description: '完成所有沟通相关的成就'
      },
      {
        name: '时间管家',
        description: '在时间管理方面表现出色',
        icon: 'badge_time',
        category: 'skill',
        level: 'silver',
        requirement_description: '完成大部分时间管理相关的成就'
      },
      {
        name: '团队领袖',
        description: '在团队协作方面表现出色',
        icon: 'badge_team',
        category: 'skill',
        level: 'bronze',
        requirement_description: '完成多个团队协作相关的成就'
      },
      {
        name: '早期支持者',
        description: '在平台早期阶段加入',
        icon: 'badge_early',
        category: 'special',
        level: 'gold',
        requirement_description: '在平台公测期间注册'
      },
      {
        name: '内容创作者',
        description: '为平台贡献优质内容',
        icon: 'badge_creator',
        category: 'contribution',
        level: 'silver',
        requirement_description: '发布20篇优质笔记'
      }
    ];

    // 添加预定义徽章
    for (let i = 0; i < predefinedBadges.length; i++) {
      const badge = predefinedBadges[i];
      badges.push({
        id: i + 1,
        name: badge.name,
        description: badge.description,
        icon: badge.icon,
        category: badge.category,
        level: badge.level,
        requirement_description: badge.requirement_description,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }

    // 如果需要更多徽章，生成额外的数据
    if (count > predefinedBadges.length) {
      for (let i = predefinedBadges.length + 1; i <= count; i++) {
        const categories = ['skill', 'achievement', 'special', 'contribution'];
        const levels = ['bronze', 'silver', 'gold', 'platinum'];

        badges.push({
          id: i,
          name: `徽章${i}`,
          description: `这是徽章${i}的描述，完成特定条件即可获得`,
          icon: `badge_${i}`,
          category: categories[Math.floor(Math.random() * categories.length)],
          level: levels[Math.floor(Math.random() * levels.length)],
          requirement_description: `完成特定条件${i}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('badges', badges);
    console.log(`徽章数据生成完成，共${badges.length}条记录`);
    return badges;
  },

  // 生成标签数据
  tags: async (count = 30) => {
    console.log(`生成${count}个标签数据...`);
    const tagCategories = await loadData('tag_categories');
    const tags = [];

    // 预定义一些标签
    const predefinedTags = [
      { name: '时间管理', category_id: 1 },
      { name: '沟通技巧', category_id: 1 },
      { name: '领导力', category_id: 1 },
      { name: '团队协作', category_id: 1 },
      { name: '问题解决', category_id: 1 },
      { name: '初级', category_id: 2 },
      { name: '中级', category_id: 2 },
      { name: '高级', category_id: 2 },
      { name: '专家', category_id: 2 },
      { name: '会议', category_id: 3 },
      { name: '演讲', category_id: 3 },
      { name: '谈判', category_id: 3 },
      { name: '面试', category_id: 3 },
      { name: '冲突处理', category_id: 3 }
    ];

    // 添加预定义标签
    for (let i = 0; i < predefinedTags.length; i++) {
      const tag = predefinedTags[i];
      tags.push({
        id: i + 1,
        name: tag.name,
        category_id: tag.category_id,
        relevance_score: Math.random().toFixed(2),
        weight: Math.random().toFixed(2),
        usage_count: Math.floor(Math.random() * 200),
        is_verified: true,
        is_official: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }

    // 如果需要更多标签，生成额外的数据
    if (count > predefinedTags.length) {
      for (let i = predefinedTags.length + 1; i <= count; i++) {
        const categoryId = tagCategories[Math.floor(Math.random() * tagCategories.length)].id;
        tags.push({
          id: i,
          name: `标签${i}`,
          category_id: categoryId,
          relevance_score: Math.random().toFixed(2),
          weight: Math.random().toFixed(2),
          usage_count: Math.floor(Math.random() * 200),
          is_verified: Math.random() > 0.3,
          is_official: Math.random() > 0.7,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    }

    // 保存数据
    await saveData('tags', tags);
    console.log(`标签数据生成完成，共${tags.length}条记录`);
    return tags;
  }
};

// 辅助函数：保存数据到JSON文件
const saveData = async (entityName, data) => {
  const jsonDir = path.join(__dirname, '../json');
  const sqlDir = path.join(__dirname, '../sql');

  // 确保目录存在
  if (!fs.existsSync(jsonDir)) {
    fs.mkdirSync(jsonDir, { recursive: true });
  }
  if (!fs.existsSync(sqlDir)) {
    fs.mkdirSync(sqlDir, { recursive: true });
  }

  // 保存为JSON
  const jsonPath = path.join(jsonDir, `${entityName}.json`);
  fs.writeFileSync(jsonPath, JSON.stringify(data, null, 2));

  // 生成SQL插入语句
  const sqlPath = path.join(sqlDir, `${entityName}.sql`);
  const sqlContent = generateSqlInserts(entityName, data);
  fs.writeFileSync(sqlPath, sqlContent);

  return data;
};

// 辅助函数：从JSON文件加载数据
const loadData = async entityName => {
  const jsonPath = path.join(__dirname, `../json/${entityName}.json`);

  if (fs.existsSync(jsonPath)) {
    const data = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    return data;
  }

  return [];
};

// 辅助函数：生成SQL插入语句
const generateSqlInserts = (tableName, data) => {
  if (!data || data.length === 0) return '';

  const columns = Object.keys(data[0]).join(', ');
  let sqlContent = `-- ${tableName} 表数据\n`;
  sqlContent += `INSERT INTO ${tableName} (${columns}) VALUES\n`;

  const rows = data.map(item => {
    const values = Object.values(item).map(value => {
      if (value === null) return 'NULL';
      if (typeof value === 'string') return `'${value.replace(/'/g, '\'\'')}'`;
      return value;
    }).join(', ');
    return `(${values})`;
  });

  sqlContent += rows.join(',\n') + ';\n';
  return sqlContent;
};

// 辅助函数：生成随机颜色
const getRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

// 导出模块
module.exports = generateMockData;

// 如果直接运行此脚本，则生成所有数据
if (require.main === module) {
  generateMockData.all().catch(console.error);
}
