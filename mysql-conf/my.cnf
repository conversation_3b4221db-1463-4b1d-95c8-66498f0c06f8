[mysqld]
# 基础设置
pid-file                = /var/run/mysqld/mysqld.pid
socket                  = /var/run/mysqld/mysqld.sock
datadir                 = /var/lib/mysql
secure-file-priv        = NULL

# 字符集设置
character-set-server    = utf8mb4
collation-server        = utf8mb4_unicode_ci

# 连接设置
max_connections         = ${MYSQL_MAX_CONNECTIONS:-1000}
max_connect_errors      = 1000
connect_timeout         = 10
wait_timeout            = 600
interactive_timeout     = 600
max_allowed_packet      = 64M

# 缓存设置
table_open_cache         = 2000
table_definition_cache   = 2000
thread_cache_size        = 32
query_cache_type         = 0
query_cache_size         = 0
max_prepared_stmt_count  = 1000000

# InnoDB设置
innodb_buffer_pool_size         = ${MYSQL_INNODB_BUFFER_POOL_SIZE:-256M}
innodb_buffer_pool_instances    = 4
innodb_flush_log_at_trx_commit  = 2
innodb_log_buffer_size          = 16M
innodb_log_file_size            = 128M
innodb_write_io_threads         = 8
innodb_read_io_threads          = 8
innodb_flush_method             = O_DIRECT
innodb_file_per_table           = 1
innodb_io_capacity              = 400
innodb_io_capacity_max          = 800
innodb_stats_on_metadata        = 0

# 临时表设置
tmp_table_size          = 32M
max_heap_table_size     = 32M

# 二进制日志
server-id               = 1
log_bin                 = mysql-bin
expire_logs_days        = 7
max_binlog_size         = 100M
binlog_format           = ROW
binlog_row_image        = minimal

# 慢查询日志
slow_query_log          = 1
slow_query_log_file     = /var/lib/mysql/mysql-slow.log
long_query_time         = 2
log_queries_not_using_indexes = 1

# 错误日志
log_error               = /var/lib/mysql/mysql-error.log

# 性能优化
sort_buffer_size        = 4M
join_buffer_size        = 4M
read_buffer_size        = 2M
read_rnd_buffer_size    = 2M
key_buffer_size         = 32M

# 其他优化
skip-name-resolve
skip-host-cache
performance_schema      = on
explicit_defaults_for_timestamp = 1

[client]
default-character-set   = utf8mb4

[mysql]
default-character-set   = utf8mb4
