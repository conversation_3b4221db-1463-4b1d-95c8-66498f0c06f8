-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS aibubb_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE aibubb_db;

-- 授予权限
GRANT ALL PRIVILEGES ON aibubb_db.* TO 'aibubb_user'@'%';
FLUSH PRIVILEGES;

-- 创建主题表
CREATE TABLE IF NOT EXISTS Theme (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主题ID',
  name VARCHAR(50) NOT NULL COMMENT '主题名称',
  english_name VARCHAR(50) COMMENT '主题英文名称',
  description TEXT COMMENT '主题描述',
  icon VARCHAR(10) COMMENT '主题图标',
  color VARCHAR(20) COMMENT '主题颜色',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建学习计划表
CREATE TABLE IF NOT EXISTS LearningPlan (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '计划ID',
  user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
  theme_id INT NOT NULL COMMENT '主题ID',
  title VARCHAR(100) NOT NULL COMMENT '计划标题',
  description TEXT COMMENT '计划描述',
  target_days INT DEFAULT 7 COMMENT '目标天数',
  completed_days INT DEFAULT 0 COMMENT '已完成天数',
  progress INT DEFAULT 0 COMMENT '进度百分比',
  status ENUM('not_started', 'in_progress', 'completed', 'paused') DEFAULT 'not_started' COMMENT '状态',
  start_date DATE COMMENT '开始日期',
  end_date DATE COMMENT '结束日期',
  is_current BOOLEAN DEFAULT FALSE COMMENT '是否为当前激活的计划',
  is_system_default BOOLEAN DEFAULT FALSE COMMENT '是否为系统默认计划',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_status (status),
  INDEX idx_is_current (is_current),
  INDEX idx_is_system_default (is_system_default),
  FOREIGN KEY (theme_id) REFERENCES Theme(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建标签分类表
CREATE TABLE IF NOT EXISTS TagCategory (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL COMMENT '分类名称',
  description TEXT COMMENT '分类描述',
  parent_id INT COMMENT '父分类ID，顶级分类为null',
  theme_id INT NOT NULL COMMENT '所属主题ID',
  level INT NOT NULL DEFAULT 1 COMMENT '层级，1为顶级分类，2为二级分类，以此类推',
  sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_parent_id (parent_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_level (level),
  FOREIGN KEY (parent_id) REFERENCES TagCategory(id) ON DELETE SET NULL,
  FOREIGN KEY (theme_id) REFERENCES Theme(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建标签表
CREATE TABLE IF NOT EXISTS Tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
  name VARCHAR(10) NOT NULL COMMENT '标签名称(2-4个汉字)',
  plan_id INT NOT NULL COMMENT '关联的学习计划ID',
  category_id INT COMMENT '关联的标签分类ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)，用于排序和推荐',
  usage_count INT DEFAULT 0 COMMENT '使用次数，记录标签被使用的频率',
  is_verified BOOLEAN DEFAULT FALSE COMMENT '是否经过验证，用于标记高质量标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_name (name),
  INDEX idx_category_id (category_id),
  INDEX idx_weight (weight),
  INDEX idx_usage_count (usage_count),
  INDEX idx_is_verified (is_verified),
  FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES TagCategory(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建计划标签关联表
CREATE TABLE IF NOT EXISTS PlanTag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)，用于排序和推荐',
  is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_primary (is_primary),
  UNIQUE KEY unique_plan_tag (plan_id, tag_id),
  FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建标签同义词表
CREATE TABLE IF NOT EXISTS TagSynonym (
  id INT AUTO_INCREMENT PRIMARY KEY,
  primary_tag_id INT NOT NULL COMMENT '主标签ID',
  synonym_name VARCHAR(10) NOT NULL COMMENT '同义词名称',
  similarity_score FLOAT NOT NULL DEFAULT 0.8 COMMENT '相似度得分(0-1)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_primary_tag_id (primary_tag_id),
  INDEX idx_synonym_name (synonym_name),
  FOREIGN KEY (primary_tag_id) REFERENCES Tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建每日内容表
CREATE TABLE IF NOT EXISTS DailyContent (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL COMMENT '学习计划ID',
  day_number INT NOT NULL COMMENT '天数',
  title VARCHAR(100) NOT NULL COMMENT '标题',
  content TEXT COMMENT '内容',
  is_completed BOOLEAN DEFAULT FALSE COMMENT '是否已完成',
  completed_at TIMESTAMP NULL COMMENT '完成时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_day_number (day_number),
  INDEX idx_is_completed (is_completed),
  FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入平台指南主题
INSERT INTO Theme (name, english_name, description, icon, color, sort_order, is_active)
VALUES ('平台指南', 'Platform Guide', '了解如何使用AIBUBB平台提升学习效率', '📱', '#6366F1', 0, TRUE);

-- 插入系统默认学习计划
INSERT INTO LearningPlan (user_id, theme_id, title, description, target_days, status, start_date, end_date, is_current, is_system_default)
VALUES ('system', 1, 'AIBUBB使用指南', '了解AIBUBB平台的核心功能和使用技巧，开启你的学习之旅', 7, 'in_progress', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), FALSE, TRUE);

-- 插入标签
INSERT INTO Tag (name, plan_id, relevance_score, weight, is_verified, sort_order)
VALUES
('平台介绍', 1, 0.95, 1.0, TRUE, 0),
('泡泡功能', 1, 0.90, 1.0, TRUE, 1),
('广场探索', 1, 0.85, 1.0, TRUE, 2),
('学习计划', 1, 0.80, 1.0, TRUE, 3),
('笔记技巧', 1, 0.75, 1.0, TRUE, 4);

-- 插入计划标签关联
INSERT INTO PlanTag (plan_id, tag_id, relevance_score, weight, is_primary, sort_order)
VALUES
(1, 1, 0.95, 1.0, TRUE, 0),
(1, 2, 0.90, 1.0, TRUE, 1),
(1, 3, 0.85, 1.0, TRUE, 2),
(1, 4, 0.80, 1.0, FALSE, 3),
(1, 5, 0.75, 1.0, FALSE, 4);

-- 插入每日内容
INSERT INTO DailyContent (plan_id, day_number, title, content, is_completed)
VALUES
(1, 1, '欢迎来到AIBUBB', '欢迎使用AIBUBB平台！今天我们将了解平台的基本功能和使用方法。', FALSE),
(1, 2, '探索泡泡功能', '泡泡是AIBUBB的核心功能，今天我们将学习如何与泡泡互动，获取学习内容。', FALSE),
(1, 3, '发现广场内容', '广场是分享和发现学习内容的地方，今天我们将探索如何浏览和参与广场讨论。', FALSE),
(1, 4, '创建学习计划', '学习计划可以帮助你系统地学习知识，今天我们将学习如何创建和管理学习计划。', FALSE),
(1, 5, '记录学习笔记', '笔记是记录学习心得的重要工具，今天我们将学习如何创建和分享笔记。', FALSE),
(1, 6, '个性化设置', '个性化设置可以让AIBUBB更符合你的使用习惯，今天我们将学习如何调整设置。', FALSE),
(1, 7, '进阶使用技巧', '恭喜你完成了基础学习！今天我们将分享一些进阶使用技巧，帮助你更好地使用AIBUBB。', FALSE);
