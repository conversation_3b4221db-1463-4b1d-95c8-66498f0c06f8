{"name": "nebula-learn", "version": "1.0.0", "description": "NebulaLearn - 个人软技能全方位提升平台", "scripts": {"dev": "echo '请使用微信开发者工具打开项目进行开发'", "integration:env": "./start-integration-test-env.sh", "integration:env:reset": "./start-integration-test-env.sh --reset-db", "integration:test": "cd backend && jest --testPathPattern=__tests__/integration", "integration:test:watch": "cd backend && jest --testPathPattern=__tests__/integration --watch", "e2e:env": "./backend/start-e2e-test-env.sh", "e2e:env:reset": "./backend/start-e2e-test-env.sh --reset-db", "e2e:test": "./backend/run-e2e-tests.sh", "e2e:test:watch": "./backend/run-e2e-tests.sh --watch", "test:e2e": "node backend/scripts/run-e2e-tests.js", "perf:env": "./start-perf-test-env.sh", "perf:env:reset": "./start-perf-test-env.sh --reset-db --seed-data", "perf:test": "./run-performance-tests.sh", "perf:test:load": "node backend/scripts/run-performance-tests.js --test=load", "perf:test:stress": "node backend/scripts/run-performance-tests.js --test=stress", "perf:test:soak": "node backend/scripts/run-performance-tests.js --test=soak", "perf:test:concurrency": "node backend/scripts/run-performance-tests.js --test=concurrency", "perf:test:quick": "node backend/scripts/run-performance-tests.js --test=load --quick", "perf:baseline": "node backend/scripts/establish-performance-baseline.js", "perf:schedule": "node backend/scripts/schedule-performance-tests.js", "perf:analyze": "node backend/scripts/analyze-performance-results.js"}, "dependencies": {"axios": "^1.9.0", "dompurify": "^3.2.5", "jsdom": "^26.1.0", "openai": "^4.96.2"}, "devDependencies": {"@types/jest": "^29.5.14", "jest": "^29.7.0", "jest-html-reporter": "^4.1.0", "supertest": "^6.3.4", "ts-jest": "^29.3.2", "typescript": "^5.8.3"}, "resolutions": {"validator": "^13.11.0", "semver": "^7.5.4"}, "preinstall": "npx npm-force-resolutions"}