// pages/plan-detail/index.js
// 学习计划详情页面

// 导入学习计划服务
const learningPlanService = require('../../utils/learning-plan-service');
// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    loadingFailed: false,
    planId: null,
    isSystemDefault: false,
    plan: null,
    tags: [],
    statistics: null,
    contentPlan: [],

    // 学习记录
    learningRecords: [],

    // 当前选中的标签页
    activeTab: 'overview', // overview, content, statistics

    // 图表数据
    chartData: {
      progress: [],
      timeSpent: [],
      activities: []
    },

    // 编辑模式
    isEditMode: false,
    editData: {
      title: '',
      description: '',
      targetDays: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取传递的计划ID
    const planId = options.id;
    // 检查是否是系统默认计划
    const isSystemDefault = options.isSystemDefault === 'true';

    if (!planId) {
      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: '缺少计划ID',
        icon: 'none'
      });

      return;
    }

    this.setData({
      planId,
      isSystemDefault
    });

    // 加载学习计划详情
    this.loadPlanDetails();

    // 加载学习记录
    this.loadLearningRecords();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示页面时刷新数据
    if (this.data.planId) {
      this.loadPlanDetails();
      this.loadLearningRecords();
    }
  },

  /**
   * 加载学习计划详情
   */
  async loadPlanDetails() {
    this.setData({ isLoading: true, loadingFailed: false });

    try {
      // 调用服务获取学习计划详情
      const response = await learningPlanService.getPlanById(this.data.planId, this.data.isSystemDefault);

      if (response.success) {
        const { plan, tags, contentPlan, statistics } = response.data;

        // 更新数据
        this.setData({
          plan,
          tags: tags || [],
          contentPlan: contentPlan || [],
          statistics: statistics || {
            exercisesCompleted: 0,
            insightsViewed: 0,
            notesCreated: 0,
            totalTimeSpent: 0
          },
          isLoading: false,

          // 初始化编辑数据
          editData: {
            title: plan.title,
            description: plan.description || '',
            targetDays: plan.targetDays || 7
          }
        });

        // 准备图表数据
        this.prepareChartData();
      } else {
        this.setData({
          isLoading: false,
          loadingFailed: true
        });

        wx.showToast({
          title: response.error || '获取学习计划详情失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取学习计划详情失败:', error);

      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: '获取学习计划详情失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载学习记录
   */
  async loadLearningRecords() {
    if (!this.data.planId) return;

    try {
      // 调用统计适配器获取学习记录
      const response = await statisticsAdapter.getLearningRecords({
        planId: this.data.planId,
        limit: 10
      });

      if (response.success) {
        this.setData({
          learningRecords: response.data.records || []
        });
      }
    } catch (error) {
      console.error('获取学习记录失败:', error);
    }
  },

  /**
   * 准备图表数据
   */
  prepareChartData() {
    const { plan, statistics } = this.data;

    if (!plan || !statistics) return;

    // 进度图表数据
    const progressData = [];
    // 假设我们有每天的进度数据
    // 这里使用模拟数据，实际应该从 API 获取
    const days = Math.min(plan.completedDays || 0, 7);
    for (let i = 0; i < days; i++) {
      progressData.push({
        day: `Day ${i + 1}`,
        progress: Math.min(Math.round((i + 1) / plan.targetDays * 100), 100)
      });
    }

    // 学习时间图表数据
    const timeSpentData = [];
    // 假设我们有每天的学习时间数据
    // 这里使用模拟数据，实际应该从 API 获取
    for (let i = 0; i < days; i++) {
      timeSpentData.push({
        day: `Day ${i + 1}`,
        minutes: Math.round(Math.random() * 60) + 15
      });
    }

    // 活动类型图表数据
    const activitiesData = [
      { type: '练习', count: statistics.exercisesCompleted || 0 },
      { type: '观点', count: statistics.insightsViewed || 0 },
      { type: '笔记', count: statistics.notesCreated || 0 }
    ];

    this.setData({
      'chartData.progress': progressData,
      'chartData.timeSpent': timeSpentData,
      'chartData.activities': activitiesData
    });
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  /**
   * 处理学习计划卡片点击事件
   */
  handlePlanClick(e) {
    const { plan } = e.detail;
    console.log('点击了学习计划卡片:', plan);
  },

  /**
   * 处理学习日期点击事件
   */
  handleDayClick(e) {
    const { day } = e.detail;
    console.log('点击了学习日期:', day);

    wx.showToast({
      title: `第${day}天学习内容`,
      icon: 'none'
    });
  },

  /**
   * 处理内容卡片点击事件
   */
  handleContentClick(e) {
    const { contentType, contentData } = e.detail;
    console.log(`点击了${contentType}卡片:`, contentData);

    // 这里可以根据内容类型跳转到不同的页面
    if (contentType === 'exercise') {
      wx.navigateTo({
        url: `/pages/exercise/detail?id=${contentData.id}`
      });
    } else if (contentType === 'insight') {
      wx.navigateTo({
        url: `/pages/insight/detail?id=${contentData.id}`
      });
    } else if (contentType === 'note') {
      wx.navigateTo({
        url: `/pages/note/detail?id=${contentData.id}`
      });
    }
  },

  /**
   * 处理查看内容详情事件
   */
  handleViewContent(e) {
    const { contentType, contentData } = e.detail;
    console.log(`查看${contentType}详情:`, contentData);

    // 这里可以根据内容类型跳转到不同的页面
    if (contentType === 'exercise') {
      wx.navigateTo({
        url: `/pages/exercise/detail?id=${contentData.id}`
      });
    } else if (contentType === 'insight') {
      wx.navigateTo({
        url: `/pages/insight/detail?id=${contentData.id}`
      });
    } else if (contentType === 'note') {
      wx.navigateTo({
        url: `/pages/note/detail?id=${contentData.id}`
      });
    }
  },

  /**
   * 重试加载
   */
  retryLoading() {
    this.loadPlanDetails();
  },

  /**
   * 激活学习计划（设为当前计划）
   */
  async activatePlan() {
    if (!this.data.plan || this.data.plan.isCurrent) return;

    wx.showLoading({ title: '正在切换...' });

    try {
      // 调用服务设置当前学习计划
      const response = await learningPlanService.setCurrentPlan(this.data.planId);

      wx.hideLoading();

      if (response.success) {
        // 更新本地数据
        const plan = this.data.plan;
        plan.isCurrent = true;

        this.setData({ plan });

        wx.showToast({
          title: '已设为当前计划',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: response.error || '切换失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('切换学习计划失败:', error);

      wx.showToast({
        title: '切换失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化日期
   */
  formatDate: function (dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  /**
   * 格式化时间（分钟转为小时和分钟）
   */
  formatTime(minutes) {
    if (!minutes) return '0分钟';

    if (minutes < 60) {
      return `${minutes}分钟`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours}小时`;
    }

    return `${hours}小时${remainingMinutes}分钟`;
  },

  /**
   * 开始学习
   */
  startLearning() {
    // 跳转到学习页面
    wx.navigateTo({
      url: `/pages/learn/index?planId=${this.data.planId}`
    });
  },

  /**
   * 分享学习计划
   */
  sharePlan() {
    // 显示分享菜单
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 删除学习计划
   */
  async deletePlan() {
    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个学习计划吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#e64340',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({ title: '正在删除...' });

          try {
            // 调用服务删除学习计划
            const response = await learningPlanService.deletePlan(this.data.planId);

            wx.hideLoading();

            if (response.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            } else {
              wx.showToast({
                title: response.error || '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            wx.hideLoading();
            console.error('删除学习计划失败:', error);

            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 进入编辑模式
   */
  enterEditMode() {
    this.setData({ isEditMode: true });
  },

  /**
   * 取消编辑
   */
  cancelEdit() {
    // 重置编辑数据
    this.setData({
      isEditMode: false,
      editData: {
        title: this.data.plan.title,
        description: this.data.plan.description || '',
        targetDays: this.data.plan.targetDays || 7
      }
    });
  },

  /**
   * 保存编辑
   */
  async saveEdit() {
    const { editData } = this.data;

    // 验证数据
    if (!editData.title.trim()) {
      wx.showToast({
        title: '标题不能为空',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '正在保存...' });

    try {
      // 调用服务更新学习计划
      const response = await learningPlanService.updatePlan(this.data.planId, editData);

      wx.hideLoading();

      if (response.success) {
        // 更新本地数据
        const plan = this.data.plan;
        plan.title = editData.title;
        plan.description = editData.description;
        plan.targetDays = editData.targetDays;

        this.setData({
          plan,
          isEditMode: false
        });

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: response.error || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存学习计划失败:', error);

      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 编辑数据变更
   */
  onEditDataChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`editData.${field}`]: value
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    const { plan } = this.data;

    return {
      title: `我正在学习「${plan ? plan.title : '学习计划'}」`,
      path: `/pages/plan-detail/index?id=${this.data.planId}&isSystemDefault=${this.data.isSystemDefault}`,
      imageUrl: '/assets/images/share-plan.png'
    };
  }
});
