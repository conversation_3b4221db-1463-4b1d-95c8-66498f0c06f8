/* pages/plan-generating/index.wxss */
page {
  background-color: #f8f9fa;
}

.container {
  padding: 40rpx 30rpx;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
  overflow: hidden;
}

/* 背景渐变效果 */
.container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(55, 117, 245, 0.05) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: -1;
}

/* 背景装饰元素 */
.bg-decoration {
  position: fixed;
  border-radius: 50%;
  z-index: -1;
  opacity: 0.6;
}

.bg-circle-1 {
  width: 400rpx;
  height: 400rpx;
  top: -100rpx;
  right: -100rpx;
  background: radial-gradient(circle, rgba(55, 117, 245, 0.1) 0%, rgba(55, 117, 245, 0) 70%);
}

.bg-circle-2 {
  width: 600rpx;
  height: 600rpx;
  bottom: -200rpx;
  left: -200rpx;
  background: radial-gradient(circle, rgba(55, 117, 245, 0.08) 0%, rgba(55, 117, 245, 0) 70%);
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  background: radial-gradient(circle, rgba(55, 117, 245, 0.05) 0%, rgba(55, 117, 245, 0) 70%);
}

/* 毛玻璃卡片 */
.glass-card {
  width: 100%;
  border-radius: 24rpx;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  margin-bottom: 30rpx;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 生成中状态 */
.generating-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
  text-align: center;
  height: 80vh;
}

.generating-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  position: relative;
}

/* AI大脑图标动画 */
.ai-brain-icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #3775F5, #5C9DFF);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite ease-in-out;
}

.brain-pulse {
  width: 50%;
  height: 50%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  position: absolute;
  animation: brain-pulse 2s infinite alternate ease-in-out;
}

.brain-circles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.circle {
  position: absolute;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  animation: circle-expand 3s infinite alternate ease-in-out;
}

.c1 {
  width: 30%;
  height: 30%;
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.c2 {
  width: 25%;
  height: 25%;
  top: 50%;
  right: 20%;
  animation-delay: 0.5s;
}

.c3 {
  width: 20%;
  height: 20%;
  bottom: 25%;
  left: 30%;
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(55, 117, 245, 0.4);
  }
  70% {
    box-shadow: 0 0 0 30rpx rgba(55, 117, 245, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(55, 117, 245, 0);
  }
}

@keyframes brain-pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.2);
    opacity: 0.6;
  }
}

@keyframes circle-expand {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.generating-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.generating-subtitle {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.generating-progress {
  width: 80%;
  margin-bottom: 30rpx;
}

.progress-bar {
  height: 12rpx;
  background-color: rgba(55, 117, 245, 0.1);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3775F5, #5C9DFF);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

.progress-percentage {
  font-size: 28rpx;
  color: #3775F5;
  display: block;
  text-align: right;
}

.generating-tips {
  font-size: 28rpx;
  color: #999;
  margin-top: 40rpx;
  max-width: 80%;
  line-height: 1.5;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
  height: 60vh;
  text-align: center;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: white;
  font-size: 80rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-subtext {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  max-width: 80%;
}

/* 计划结果容器 */
.plan-result-container {
  width: 100%;
}

.title-card {
  text-align: center;
  padding: 40rpx 30rpx;
}

.plan-title {
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.plan-theme-tag {
  display: inline-block;
  padding: 8rpx 20rpx;
  background: rgba(55, 117, 245, 0.1);
  color: #3775F5;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background: #3775F5;
  border-radius: 3rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.principle-content {
  padding: 10rpx 0;
}

/* 学习内容天数 */
.content-days {
  margin-top: 20rpx;
}

.day-item {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.day-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.day-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.day-number {
  font-size: 26rpx;
  font-weight: bold;
  color: #3775F5;
  background: rgba(55, 117, 245, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}

.day-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.day-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding-left: 30rpx;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.tag-item {
  padding: 8rpx 20rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  background: rgba(55, 117, 245, 0.1);
  color: #3775F5;
  border-radius: 30rpx;
  font-size: 24rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.action-button {
  min-width: 200rpx;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
  margin: 0 20rpx;
  padding: 0 40rpx;
  transition: opacity 0.3s ease;
}

.action-button:active {
  opacity: 0.8;
}

.back-button {
  background-color: #f5f5f5;
  color: #666;
}

.retry-button {
  background-color: #ff9500;
  color: white;
}

.submit-button {
  background: linear-gradient(90deg, #3775F5, #5C9DFF);
  color: white;
} 