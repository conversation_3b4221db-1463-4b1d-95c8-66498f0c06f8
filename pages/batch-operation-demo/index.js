// pages/batch-operation-demo/index.js
Page({
  data: {
    isSelectMode: false,
    selectedIds: [],
    items: [],
    isLoading: false,
    operationType: 'delete' // 'delete' 或 'restore'
  },

  onLoad() {
    this.loadItems();
  },

  // 加载示例数据
  loadItems() {
    this.setData({ isLoading: true });

    // 模拟API请求
    setTimeout(() => {
      // 生成示例数据
      const items = [];
      for (let i = 1; i <= 20; i++) {
        items.push({
          id: i,
          title: `示例项目 ${i}`,
          content: `这是示例项目 ${i} 的内容描述，用于演示批量操作功能。`,
          createdAt: '2025-05-10 10:00:00'
        });
      }

      this.setData({
        items,
        isLoading: false
      });
    }, 1000);
  },

  // 切换选择模式
  toggleSelectMode() {
    this.setData({
      isSelectMode: !this.data.isSelectMode,
      selectedIds: []
    });
  },

  // 全选/取消全选
  toggleSelectAll() {
    if (this.data.selectedIds.length === this.data.items.length) {
      // 取消全选
      this.setData({ selectedIds: [] });
    } else {
      // 全选
      const selectedIds = this.data.items.map(item => item.id);
      this.setData({ selectedIds });
    }
  },

  // 选择/取消选择项目
  toggleSelectItem(e) {
    if (!this.data.isSelectMode) return;

    const itemId = e.currentTarget.dataset.id;
    const selectedIds = [...this.data.selectedIds];
    const index = selectedIds.indexOf(itemId);

    if (index === -1) {
      selectedIds.push(itemId);
    } else {
      selectedIds.splice(index, 1);
    }

    this.setData({ selectedIds });
  },

  // 处理批量操作
  handleBatchOperation(e) {
    const { type, ids } = e.detail;

    wx.showModal({
      title: type === 'delete' ? '批量删除确认' : '批量恢复确认',
      content: type === 'delete'
        ? `确定要删除选中的${ids.length}项内容吗？`
        : `确定要恢复选中的${ids.length}项内容吗？`,
      success: res => {
        if (res.confirm) {
          // 模拟操作成功
          wx.showToast({
            title: type === 'delete' ? '删除成功' : '恢复成功',
            icon: 'success'
          });

          // 更新数据
          if (type === 'delete') {
            const items = this.data.items.filter(item => !ids.includes(item.id));
            this.setData({
              items,
              isSelectMode: false,
              selectedIds: []
            });
          }
        }
      }
    });
  },

  // 切换操作类型
  switchOperationType() {
    this.setData({
      operationType: this.data.operationType === 'delete' ? 'restore' : 'delete'
    });
  }
});
