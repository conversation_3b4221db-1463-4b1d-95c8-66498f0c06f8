<!-- pages/batch-operation-demo/index.wxml -->
<view class="batch-demo-container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">批量操作演示</view>
    <view class="actions">
      <view class="action-btn switch-btn" bindtap="switchOperationType">
        切换为{{operationType === 'delete' ? '恢复' : '删除'}}模式
      </view>
    </view>
  </view>

  <!-- 批量操作组件 -->
  <batch-operation
    operationType="{{operationType}}"
    isSelectMode="{{isSelectMode}}"
    selectedIds="{{selectedIds}}"
    totalCount="{{items.length}}"
    contentTypeName="内容"
    bind:toggleSelectMode="toggleSelectMode"
    bind:toggleSelectAll="toggleSelectAll"
    bind:batchOperation="handleBatchOperation"
  ></batch-operation>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 内容列表 -->
  <view class="items-container" wx:else>
    <view 
      class="item {{isSelectMode ? 'selectable' : ''}} {{selectedIds.includes(item.id) ? 'selected' : ''}}"
      wx:for="{{items}}" 
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="toggleSelectItem"
    >
      <!-- 选择框 -->
      <view class="select-box" wx:if="{{isSelectMode}}">
        <view class="checkbox {{selectedIds.includes(item.id) ? 'checked' : ''}}">
          <text wx:if="{{selectedIds.includes(item.id)}}" class="check-icon">✓</text>
        </view>
      </view>

      <!-- 内容信息 -->
      <view class="item-info">
        <view class="item-title">{{item.title}}</view>
        <view class="item-content">{{item.content}}</view>
        <view class="item-meta">
          <text class="item-time">创建时间: {{item.createdAt}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
