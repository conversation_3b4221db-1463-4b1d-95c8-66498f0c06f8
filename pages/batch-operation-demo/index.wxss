/* pages/batch-operation-demo/index.wxss */
.batch-demo-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 标题栏样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.switch-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 内容列表样式 */
.items-container {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

.item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item.selectable {
  cursor: pointer;
}

.item.selected {
  background-color: rgba(74, 144, 226, 0.05);
  border: 1rpx solid #4a90e2;
}

.select-box {
  margin-right: 20rpx;
  padding-top: 6rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #dddddd;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox.checked {
  background-color: #4a90e2;
  border-color: #4a90e2;
}

.check-icon {
  color: #ffffff;
  font-size: 28rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.item-content {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-meta {
  font-size: 24rpx;
  color: #999999;
}

.item-time {
  margin-right: 20rpx;
}
