/**
 * 组件示例页面
 */
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 按钮示例数据
    buttonTypes: ['default', 'primary', 'secondary', 'success', 'warning', 'error', 'text', 'link'],
    buttonSizes: ['small', 'medium', 'large'],

    // 卡片示例数据
    cardTitle: '学习计划',
    cardSubtitle: '30天掌握JavaScript基础',
    cardCoverUrl: 'https://picsum.photos/600/300',

    // 输入框示例数据
    inputValue: '',
    inputError: false,
    inputErrorMessage: ''
  },

  /**
   * 按钮点击事件
   */
  handleButtonClick(e) {
    const type = e.currentTarget.dataset.type;
    wx.showToast({
      title: `点击了${type}按钮`,
      icon: 'none'
    });
  },

  /**
   * 卡片点击事件
   */
  handleCardClick() {
    wx.showToast({
      title: '点击了卡片',
      icon: 'none'
    });
  },

  /**
   * 输入框输入事件
   */
  handleInput(e) {
    const { value } = e.detail;
    this.setData({
      inputValue: value
    });

    // 简单验证
    if (value && value.length < 3) {
      this.setData({
        inputError: true,
        inputErrorMessage: '输入内容至少需要3个字符'
      });
    } else {
      this.setData({
        inputError: false,
        inputErrorMessage: ''
      });
    }
  },

  /**
   * 输入框清除事件
   */
  handleInputClear() {
    this.setData({
      inputValue: '',
      inputError: false,
      inputErrorMessage: ''
    });
  }
});
