<view class="container">
  <view class="page-title">NebulaLearn UI 组件示例</view>
  
  <!-- 按钮组件示例 -->
  <view class="section">
    <view class="section-title">按钮组件</view>
    
    <view class="subsection">
      <view class="subsection-title">按钮类型</view>
      <view class="button-group">
        <nl-button 
          wx:for="{{buttonTypes}}" 
          wx:key="*this" 
          type="{{item}}" 
          text="{{item}}" 
          data-type="{{item}}"
          bind:click="handleButtonClick"
          class="demo-button"
        ></nl-button>
      </view>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">按钮尺寸</view>
      <view class="button-group">
        <nl-button 
          wx:for="{{buttonSizes}}" 
          wx:key="*this" 
          type="primary" 
          size="{{item}}" 
          text="{{item}}" 
          data-size="{{item}}"
          bind:click="handleButtonClick"
          class="demo-button"
        ></nl-button>
      </view>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">按钮状态</view>
      <view class="button-group">
        <nl-button 
          type="primary" 
          text="普通按钮" 
          class="demo-button"
        ></nl-button>
        <nl-button 
          type="primary" 
          text="禁用状态" 
          disabled="{{true}}" 
          class="demo-button"
        ></nl-button>
        <nl-button 
          type="primary" 
          text="加载状态" 
          loading="{{true}}" 
          class="demo-button"
        ></nl-button>
      </view>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">按钮变体</view>
      <view class="button-group">
        <nl-button 
          type="primary" 
          text="默认按钮" 
          class="demo-button"
        ></nl-button>
        <nl-button 
          type="primary" 
          text="朴素按钮" 
          plain="{{true}}" 
          class="demo-button"
        ></nl-button>
        <nl-button 
          type="primary" 
          text="圆形按钮" 
          round="{{true}}" 
          class="demo-button"
        ></nl-button>
        <nl-button 
          type="primary" 
          text="块级按钮" 
          block="{{true}}" 
          class="demo-button"
        ></nl-button>
      </view>
    </view>
  </view>
  
  <!-- 卡片组件示例 -->
  <view class="section">
    <view class="section-title">卡片组件</view>
    
    <view class="subsection">
      <view class="subsection-title">基础卡片</view>
      <nl-card 
        title="{{cardTitle}}" 
        subtitle="{{cardSubtitle}}"
      >
        <view class="card-content">
          这是一个基础卡片的内容区域，可以放置任意内容。
        </view>
      </nl-card>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">带封面的卡片</view>
      <nl-card 
        title="{{cardTitle}}" 
        subtitle="{{cardSubtitle}}"
        showCover="{{true}}"
        coverUrl="{{cardCoverUrl}}"
        coverHeight="150px"
      >
        <view class="card-content">
          这是一个带封面的卡片，可以展示图片内容。
        </view>
      </nl-card>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">可点击的卡片</view>
      <nl-card 
        title="{{cardTitle}}" 
        subtitle="{{cardSubtitle}}"
        clickable="{{true}}"
        bind:click="handleCardClick"
      >
        <view class="card-content">
          这是一个可点击的卡片，点击后会触发事件。
        </view>
      </nl-card>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">带底部的卡片</view>
      <nl-card 
        title="{{cardTitle}}" 
        subtitle="{{cardSubtitle}}"
        showFooter="{{true}}"
      >
        <view class="card-content">
          这是一个带底部的卡片，底部可以放置操作按钮。
        </view>
        <view slot="footer" class="card-footer">
          <nl-button type="text" text="取消"></nl-button>
          <nl-button type="primary" text="确认"></nl-button>
        </view>
      </nl-card>
    </view>
  </view>
  
  <!-- 输入框组件示例 -->
  <view class="section">
    <view class="section-title">输入框组件</view>
    
    <view class="subsection">
      <view class="subsection-title">基础输入框</view>
      <nl-input 
        label="用户名" 
        placeholder="请输入用户名"
        value="{{inputValue}}"
        bind:input="handleInput"
      ></nl-input>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">带图标的输入框</view>
      <nl-input 
        label="搜索" 
        placeholder="请输入搜索内容"
        prefixIcon="search"
      ></nl-input>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">可清除的输入框</view>
      <nl-input 
        label="邮箱" 
        placeholder="请输入邮箱地址"
        clearable="{{true}}"
        bind:clear="handleInputClear"
      ></nl-input>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">错误状态的输入框</view>
      <nl-input 
        label="密码" 
        type="password"
        placeholder="请输入密码"
        value="{{inputValue}}"
        error="{{inputError}}"
        errorMessage="{{inputErrorMessage}}"
        bind:input="handleInput"
      ></nl-input>
    </view>
    
    <view class="subsection">
      <view class="subsection-title">禁用和只读状态</view>
      <nl-input 
        label="禁用状态" 
        placeholder="禁用状态"
        disabled="{{true}}"
      ></nl-input>
      <nl-input 
        label="只读状态" 
        placeholder="只读状态"
        value="这是只读内容"
        readonly="{{true}}"
      ></nl-input>
    </view>
  </view>
</view>
