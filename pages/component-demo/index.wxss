/* 页面容器 */
.container {
  padding: 16px;
  background-color: #f5f5f5;
}

/* 页面标题 */
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
  text-align: center;
}

/* 组件区块 */
.section {
  margin-bottom: 32px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 区块标题 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

/* 子区块 */
.subsection {
  margin-bottom: 24px;
}

/* 子区块标题 */
.subsection-title {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  margin-bottom: 12px;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

/* 示例按钮 */
.demo-button {
  margin-bottom: 8px;
}

/* 卡片内容 */
.card-content {
  padding: 8px 0;
  color: #666;
  line-height: 1.5;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
