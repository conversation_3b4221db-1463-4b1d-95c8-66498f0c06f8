// pages/create-plan/index.js
// 创建学习计划页面

// 导入API工具
const { learningPlanAPI, themeAPI } = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: false,
    loadingFailed: false,
    isLoggedIn: false, // 用户是否已登录
    showLoginTransition: false // 是否显示登录过渡页面
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 使用全局登录检查方法
    const app = getApp();
    const isLoggedIn = app.checkLogin({
      from: 'create-plan',
      showTransition: true,
      success: () => {
        // 用户已登录，设置登录状态
        this.setData({
          isLoggedIn: true
        });
      },
      fail: () => {
        // 用户未登录，显示过渡页面
        console.log('用户未登录，显示过渡页面');
        this.setData({
          isLoading: false,
          isLoggedIn: false,
          showLoginTransition: true
        });
      }
    });

    // 如果未登录，不需要继续执行
    if (!isLoggedIn) {
      return;
    }
  },

  /**
   * 处理取消
   */
  handleCancel: function () {
    wx.navigateBack();
  },

  /**
   * 处理提交
   */
  handleSubmit: function (e) {
    const { data } = e.detail;

    console.log('提交计划数据:', data);

    // 检查是否处于测试模式
    const app = getApp();
    const isTestMode = app.globalData.isTestMode || false;

    // 构建跳转URL和参数
    const url = `/pages/plan-generating/index?title=${encodeURIComponent(data.title)}&themeId=${data.themeId}&duration=${data.targetDays}${isTestMode ? '&test=1' : ''}`;

    // 跳转到学习计划生成页面
    wx.navigateTo({
      url: url,
      fail: err => {
        console.error('跳转失败', err);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 处理保存草稿
   */
  handleSaveDraft: function (e) {
    const { data } = e.detail;

    console.log('保存草稿数据:', data);

    // 保存草稿到本地存储
    try {
      const drafts = wx.getStorageSync('plan_drafts') || [];
      drafts.push({
        ...data,
        timestamp: new Date().getTime()
      });

      // 只保留最近10个草稿
      if (drafts.length > 10) {
        drafts.shift();
      }

      wx.setStorageSync('plan_drafts', drafts);

      wx.showToast({
        title: '草稿保存成功',
        icon: 'success'
      });
    } catch (err) {
      console.error('保存草稿失败:', err);
      wx.showToast({
        title: '保存草稿失败',
        icon: 'none'
      });
    }
  },

  /**
   * 重试加载
   */
  retryLoading: function () {
    // 检查用户是否已登录
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('重试时发现用户未登录，显示过渡页面');

      // 设置未登录状态和显示过渡页面
      this.setData({
        isLoading: false,
        isLoggedIn: false,
        loadingFailed: false,
        showLoginTransition: true
      });

      // 延迟3秒后跳转到登录页面
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/phone?from=create-plan'
        });
      }, 3000);
      return;
    }

    // 用户已登录，重置页面状态
    this.setData({
      loadingFailed: false
    });
  }
});
