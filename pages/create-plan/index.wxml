<!--pages/create-plan/index.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-decoration bg-circle-1"></view>
  <view class="bg-decoration bg-circle-2"></view>
  <view class="bg-decoration bg-circle-3"></view>

  <!-- 未登录状态的过渡页面 -->
  <view class="login-transition-container" wx:if="{{showLoginTransition}}">
    <view class="login-icon">🔐</view>
    <text class="login-title">创建您的学习计划</text>
    <text class="login-subtitle">请先登录以继续操作</text>
    <view class="login-progress">
      <view class="login-progress-bar"></view>
    </view>
    <text class="login-hint">正在跳转到登录页面...</text>
  </view>

  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading && !showLoginTransition}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:if="{{loadingFailed && !isLoading && !showLoginTransition}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <text class="error-subtext">请确保已登录并检查网络连接</text>
    <button class="retry-button" bindtap="retryLoading">点击重试</button>
  </view>

  <!-- 表单内容 -->
  <view class="form-container" wx:if="{{!isLoading && !loadingFailed && !showLoginTransition}}">
    <!-- 使用计划创建流程组件 -->
    <plan-creator
      visible="{{true}}"
      mode="create"
      bind:cancel="handleCancel"
      bind:submit="handleSubmit"
      bind:draft="handleSaveDraft"
    />
  </view>
</view>
