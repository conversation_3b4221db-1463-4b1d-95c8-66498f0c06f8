// pages/exercise-detail/index.js
// 练习详情页面

// 导入API工具
const { exerciseAPI } = require('../../utils/api');
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    exerciseId: null,
    exercise: null,
    isLoading: true,
    loadingFailed: false,
    
    // 练习状态
    isStarted: false,
    isCompleted: false,
    currentStep: 0,
    userAnswers: [],
    score: 0,
    
    // 练习结果
    showResult: false,
    resultData: null,
    
    // 用户信息
    currentUser: null,
    
    // 主题模式
    isDarkMode: false,
    
    // 计时器
    startTime: null,
    timeSpent: 0,
    timer: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const exerciseId = options.id;
    
    if (!exerciseId) {
      this.setData({
        isLoading: false,
        loadingFailed: true
      });
      
      wx.showToast({
        title: '缺少练习ID',
        icon: 'none'
      });
      
      return;
    }

    this.setData({ exerciseId });
    
    // 获取主题模式
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    const isDarkMode = themeMode === 'dark' || 
                      (themeMode === 'system' && wx.getSystemInfoSync().theme === 'dark');
    
    this.setData({ isDarkMode });
    
    // 加载练习详情
    this.loadExerciseDetail();
    
    // 加载当前用户信息
    this.loadCurrentUser();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清理计时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  /**
   * 加载当前用户信息
   */
  async loadCurrentUser() {
    try {
      const isLoggedIn = await authService.isLoggedIn();
      if (isLoggedIn) {
        const userInfo = await authService.getCurrentUser();
        this.setData({ currentUser: userInfo });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  /**
   * 加载练习详情
   */
  async loadExerciseDetail() {
    this.setData({ isLoading: true, loadingFailed: false });

    try {
      // 获取API客户端
      const app = getApp();
      const api = app.globalData.api;

      if (!api) {
        throw new Error('API客户端未初始化');
      }

      // 调用API获取练习详情
      const response = await api.exercise.getExerciseById(this.data.exerciseId);

      if (response.success && response.data) {
        const exercise = response.data;
        
        this.setData({
          exercise,
          isLoading: false,
          userAnswers: new Array(exercise.questions ? exercise.questions.length : 0).fill(null)
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: exercise.title || '练习详情'
        });

      } else {
        throw new Error(response.error || '获取练习详情失败');
      }
    } catch (error) {
      console.error('加载练习详情失败:', error);
      
      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开始练习
   */
  startExercise() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isStarted: true,
      startTime: Date.now(),
      currentStep: 0
    });

    // 开始计时
    this.startTimer();

    // 轻微振动反馈
    wx.vibrateShort({ type: 'light' });
  },

  /**
   * 开始计时器
   */
  startTimer() {
    const timer = setInterval(() => {
      const timeSpent = Math.floor((Date.now() - this.data.startTime) / 1000);
      this.setData({ timeSpent });
    }, 1000);

    this.setData({ timer });
  },

  /**
   * 停止计时器
   */
  stopTimer() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
    }
  },

  /**
   * 选择答案
   */
  selectAnswer(e) {
    const { questionIndex, answerIndex } = e.currentTarget.dataset;
    const userAnswers = [...this.data.userAnswers];
    userAnswers[questionIndex] = answerIndex;
    
    this.setData({ userAnswers });

    // 轻微振动反馈
    wx.vibrateShort({ type: 'light' });
  },

  /**
   * 下一题
   */
  nextQuestion() {
    const currentStep = this.data.currentStep;
    const totalQuestions = this.data.exercise.questions.length;
    
    if (currentStep < totalQuestions - 1) {
      this.setData({
        currentStep: currentStep + 1
      });
    } else {
      // 最后一题，完成练习
      this.completeExercise();
    }
  },

  /**
   * 上一题
   */
  prevQuestion() {
    const currentStep = this.data.currentStep;
    
    if (currentStep > 0) {
      this.setData({
        currentStep: currentStep - 1
      });
    }
  },

  /**
   * 完成练习
   */
  async completeExercise() {
    // 停止计时
    this.stopTimer();

    // 计算得分
    const score = this.calculateScore();
    
    this.setData({
      isCompleted: true,
      score,
      showResult: true
    });

    // 提交练习结果
    await this.submitExerciseResult();
  },

  /**
   * 计算得分
   */
  calculateScore() {
    const { exercise, userAnswers } = this.data;
    
    if (!exercise.questions || exercise.questions.length === 0) {
      return 0;
    }

    let correctCount = 0;
    
    exercise.questions.forEach((question, index) => {
      if (userAnswers[index] === question.correctAnswer) {
        correctCount++;
      }
    });

    return Math.round((correctCount / exercise.questions.length) * 100);
  },

  /**
   * 提交练习结果
   */
  async submitExerciseResult() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) return;

      const resultData = {
        exerciseId: this.data.exerciseId,
        answers: this.data.userAnswers,
        score: this.data.score,
        timeSpent: this.data.timeSpent,
        completedAt: new Date().toISOString()
      };

      const response = await api.exercise.submitResult(resultData);
      
      if (response.success) {
        this.setData({
          resultData: response.data
        });

        // 显示完成提示
        wx.showToast({
          title: `练习完成！得分：${this.data.score}分`,
          icon: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('提交练习结果失败:', error);
    }
  },

  /**
   * 重新开始练习
   */
  restartExercise() {
    wx.showModal({
      title: '确认重新开始',
      content: '重新开始将清除当前进度，确定要继续吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            isStarted: false,
            isCompleted: false,
            currentStep: 0,
            userAnswers: new Array(this.data.exercise.questions.length).fill(null),
            score: 0,
            showResult: false,
            resultData: null,
            startTime: null,
            timeSpent: 0
          });

          // 清理计时器
          this.stopTimer();
        }
      }
    });
  },

  /**
   * 查看答案解析
   */
  showExplanation() {
    this.setData({
      showResult: false
    });
  },

  /**
   * 分享练习
   */
  shareExercise() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 格式化时间
   */
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      return `${remainingSeconds}秒`;
    }
  },

  /**
   * 获取难度文本
   */
  getDifficultyText(difficulty) {
    const difficultyMap = {
      'easy': '简单',
      'medium': '中等',
      'hard': '困难'
    };
    
    return difficultyMap[difficulty] || '未知';
  },

  /**
   * 获取难度颜色
   */
  getDifficultyColor(difficulty) {
    const colorMap = {
      'easy': '#52c41a',
      'medium': '#faad14',
      'hard': '#f5222d'
    };
    
    return colorMap[difficulty] || '#666';
  },

  /**
   * 重试加载
   */
  retryLoading() {
    this.loadExerciseDetail();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const exercise = this.data.exercise;

    return {
      title: exercise ? `练习：${exercise.title}` : '分享练习',
      path: `/pages/exercise-detail/index?id=${this.data.exerciseId}`,
      imageUrl: exercise && exercise.imageUrl ? exercise.imageUrl : ''
    };
  }
});
