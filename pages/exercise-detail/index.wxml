<!--pages/exercise-detail/index.wxml-->
<view class="container {{isDarkMode ? 'dark-mode' : ''}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:if="{{loadingFailed}}">
    <view class="error-icon">⚠️</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="retryLoading">重试</button>
  </view>

  <!-- 练习内容 -->
  <view class="exercise-content" wx:if="{{!isLoading && !loadingFailed && exercise}}">
    
    <!-- 练习未开始 - 介绍页面 -->
    <view class="exercise-intro" wx:if="{{!isStarted}}">
      <view class="intro-header">
        <view class="exercise-title">{{exercise.title}}</view>
        <view class="exercise-meta">
          <view class="meta-item">
            <text class="meta-label">难度：</text>
            <text class="meta-value difficulty-{{exercise.difficulty}}">{{getDifficultyText(exercise.difficulty)}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">预计时间：</text>
            <text class="meta-value">{{exercise.timeEstimate || 10}}分钟</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">题目数量：</text>
            <text class="meta-value">{{exercise.questions ? exercise.questions.length : 0}}题</text>
          </view>
        </view>
      </view>

      <view class="exercise-description">
        <text class="description-text">{{exercise.description}}</text>
      </view>

      <view class="exercise-tags" wx:if="{{exercise.tags && exercise.tags.length > 0}}">
        <view class="tag-item" wx:for="{{exercise.tags}}" wx:key="id">
          {{item.name}}
        </view>
      </view>

      <view class="start-section">
        <button class="start-button" bindtap="startExercise">
          开始练习
        </button>
        <view class="start-tips">
          <text class="tip-item">💡 建议在安静的环境中完成</text>
          <text class="tip-item">⏰ 请合理安排时间</text>
          <text class="tip-item">🎯 认真思考每个问题</text>
        </view>
      </view>
    </view>

    <!-- 练习进行中 -->
    <view class="exercise-progress" wx:if="{{isStarted && !isCompleted}}">
      <!-- 进度条 -->
      <view class="progress-header">
        <view class="progress-info">
          <text class="progress-text">第{{currentStep + 1}}题 / 共{{exercise.questions.length}}题</text>
          <text class="time-text">用时：{{formatTime(timeSpent)}}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{((currentStep + 1) / exercise.questions.length) * 100}}%"></view>
        </view>
      </view>

      <!-- 当前题目 -->
      <view class="question-container" wx:if="{{exercise.questions[currentStep]}}">
        <view class="question-content">
          <view class="question-number">第{{currentStep + 1}}题</view>
          <view class="question-text">{{exercise.questions[currentStep].question}}</view>
          
          <!-- 题目图片 -->
          <image 
            class="question-image" 
            wx:if="{{exercise.questions[currentStep].imageUrl}}"
            src="{{exercise.questions[currentStep].imageUrl}}" 
            mode="widthFix">
          </image>
        </view>

        <!-- 选项 -->
        <view class="options-container">
          <view 
            class="option-item {{userAnswers[currentStep] === index ? 'selected' : ''}}"
            wx:for="{{exercise.questions[currentStep].options}}" 
            wx:key="*this"
            bindtap="selectAnswer"
            data-question-index="{{currentStep}}"
            data-answer-index="{{index}}">
            <view class="option-label">{{String.fromCharCode(65 + index)}}</view>
            <view class="option-text">{{item}}</view>
          </view>
        </view>

        <!-- 导航按钮 -->
        <view class="navigation-buttons">
          <button 
            class="nav-button prev-button {{currentStep === 0 ? 'disabled' : ''}}" 
            bindtap="prevQuestion"
            disabled="{{currentStep === 0}}">
            上一题
          </button>
          <button 
            class="nav-button next-button {{userAnswers[currentStep] === null ? 'disabled' : ''}}" 
            bindtap="nextQuestion"
            disabled="{{userAnswers[currentStep] === null}}">
            {{currentStep === exercise.questions.length - 1 ? '完成' : '下一题'}}
          </button>
        </view>
      </view>
    </view>

    <!-- 练习结果 -->
    <view class="exercise-result" wx:if="{{isCompleted && showResult}}">
      <view class="result-header">
        <view class="result-icon">{{score >= 80 ? '🎉' : score >= 60 ? '👍' : '💪'}}</view>
        <view class="result-title">练习完成！</view>
        <view class="result-score">得分：{{score}}分</view>
        <view class="result-time">用时：{{formatTime(timeSpent)}}</view>
      </view>

      <view class="result-stats">
        <view class="stat-item">
          <text class="stat-label">正确率</text>
          <text class="stat-value">{{Math.round((score / 100) * exercise.questions.length)}}/{{exercise.questions.length}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">用时</text>
          <text class="stat-value">{{formatTime(timeSpent)}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">难度</text>
          <text class="stat-value">{{getDifficultyText(exercise.difficulty)}}</text>
        </view>
      </view>

      <view class="result-actions">
        <button class="action-button secondary" bindtap="showExplanation">
          查看解析
        </button>
        <button class="action-button primary" bindtap="restartExercise">
          重新练习
        </button>
        <button class="action-button secondary" bindtap="shareExercise">
          分享成绩
        </button>
      </view>
    </view>

    <!-- 答案解析 -->
    <view class="exercise-explanation" wx:if="{{isCompleted && !showResult}}">
      <view class="explanation-header">
        <view class="explanation-title">答案解析</view>
        <button class="back-result-button" bindtap="{{() => setData({showResult: true})}}">
          返回结果
        </button>
      </view>

      <view class="explanation-list">
        <view 
          class="explanation-item" 
          wx:for="{{exercise.questions}}" 
          wx:key="index">
          <view class="explanation-question">
            <view class="question-header">
              <text class="question-num">第{{index + 1}}题</text>
              <text class="answer-status {{userAnswers[index] === item.correctAnswer ? 'correct' : 'incorrect'}}">
                {{userAnswers[index] === item.correctAnswer ? '✓ 正确' : '✗ 错误'}}
              </text>
            </view>
            <text class="question-content">{{item.question}}</text>
          </view>

          <view class="explanation-answer">
            <view class="answer-info">
              <text class="your-answer">你的答案：{{item.options[userAnswers[index]] || '未作答'}}</text>
              <text class="correct-answer">正确答案：{{item.options[item.correctAnswer]}}</text>
            </view>
            <view class="explanation-text" wx:if="{{item.explanation}}">
              <text class="explanation-label">解析：</text>
              <text class="explanation-content">{{item.explanation}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
