/* pages/exercise-detail/index.wxss */

.container {
  min-height: 100vh;
  background-color: var(--bg-color-primary, #f8f9fa);
  color: var(--text-color-primary, #333);
}

.dark-mode {
  background-color: var(--bg-color-primary-dark, #1a1a1a);
  color: var(--text-color-primary-dark, #fff);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid var(--primary-color, #3775f5);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: var(--text-color-secondary, #666);
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: var(--text-color-secondary, #666);
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.retry-button {
  background-color: var(--primary-color, #3775f5);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 练习内容 */
.exercise-content {
  padding: 32rpx;
}

/* 练习介绍 */
.exercise-intro {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.intro-header {
  text-align: center;
}

.exercise-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  color: var(--text-color-primary, #333);
}

.exercise-meta {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-label {
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

.meta-value {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
}

.difficulty-easy {
  color: #52c41a;
}

.difficulty-medium {
  color: #faad14;
}

.difficulty-hard {
  color: #f5222d;
}

.exercise-description {
  background-color: var(--bg-color-secondary, #fff);
  padding: 32rpx;
  border-radius: 16rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
}

.description-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: var(--text-color-primary, #333);
}

.exercise-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: center;
}

.tag-item {
  background-color: var(--tag-bg-color, #f0f2f5);
  color: var(--primary-color, #3775f5);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid var(--primary-color, #3775f5);
}

.start-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.start-button {
  background-color: var(--primary-color, #3775f5);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 80rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.start-tips {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  align-items: center;
}

.tip-item {
  font-size: 26rpx;
  color: var(--text-color-secondary, #666);
}

/* 练习进度 */
.exercise-progress {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.progress-header {
  background-color: var(--bg-color-secondary, #fff);
  padding: 24rpx;
  border-radius: 12rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
}

.time-text {
  font-size: 26rpx;
  color: var(--text-color-secondary, #666);
}

.progress-bar {
  height: 8rpx;
  background-color: var(--bg-color-tertiary, #f0f0f0);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color, #3775f5);
  transition: width 0.3s ease;
}

/* 题目容器 */
.question-container {
  background-color: var(--bg-color-secondary, #fff);
  border-radius: 16rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
  overflow: hidden;
}

.question-content {
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
}

.question-number {
  font-size: 24rpx;
  color: var(--primary-color, #3775f5);
  font-weight: 500;
  margin-bottom: 16rpx;
}

.question-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: var(--text-color-primary, #333);
  margin-bottom: 24rpx;
}

.question-image {
  width: 100%;
  border-radius: 8rpx;
}

/* 选项 */
.options-container {
  padding: 32rpx;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid var(--border-color, #e0e0e0);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-item.selected {
  border-color: var(--primary-color, #3775f5);
  background-color: var(--primary-bg-light, #f0f7ff);
}

.option-label {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: var(--bg-color-tertiary, #f0f0f0);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  color: var(--text-color-secondary, #666);
  flex-shrink: 0;
}

.option-item.selected .option-label {
  background-color: var(--primary-color, #3775f5);
  color: white;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: var(--text-color-primary, #333);
}

/* 导航按钮 */
.navigation-buttons {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid var(--border-color, #e0e0e0);
}

.nav-button {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.prev-button {
  background-color: var(--bg-color-tertiary, #f0f0f0);
  color: var(--text-color-secondary, #666);
}

.next-button {
  background-color: var(--primary-color, #3775f5);
  color: white;
}

.nav-button.disabled {
  opacity: 0.5;
}

/* 练习结果 */
.exercise-result {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  text-align: center;
}

.result-header {
  background-color: var(--bg-color-secondary, #fff);
  padding: 48rpx 32rpx;
  border-radius: 16rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
}

.result-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.result-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: var(--text-color-primary, #333);
}

.result-score {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color, #3775f5);
  margin-bottom: 8rpx;
}

.result-time {
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

.result-stats {
  display: flex;
  justify-content: space-around;
  background-color: var(--bg-color-secondary, #fff);
  padding: 32rpx;
  border-radius: 16rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
}

.result-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-button {
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.action-button.primary {
  background-color: var(--primary-color, #3775f5);
  color: white;
}

.action-button.secondary {
  background-color: var(--bg-color-secondary, #fff);
  color: var(--text-color-primary, #333);
  border: 1rpx solid var(--border-color, #e0e0e0);
}

/* 答案解析 */
.exercise-explanation {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.explanation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
}

.explanation-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
}

.back-result-button {
  background-color: var(--bg-color-tertiary, #f0f0f0);
  color: var(--text-color-secondary, #666);
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
}

.explanation-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.explanation-item {
  background-color: var(--bg-color-secondary, #fff);
  border-radius: 16rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
  overflow: hidden;
}

.explanation-question {
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.question-num {
  font-size: 24rpx;
  color: var(--primary-color, #3775f5);
  font-weight: 500;
}

.answer-status {
  font-size: 24rpx;
  font-weight: 500;
}

.answer-status.correct {
  color: #52c41a;
}

.answer-status.incorrect {
  color: #f5222d;
}

.question-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color-primary, #333);
}

.explanation-answer {
  padding: 24rpx;
}

.answer-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.your-answer,
.correct-answer {
  font-size: 26rpx;
}

.your-answer {
  color: var(--text-color-secondary, #666);
}

.correct-answer {
  color: #52c41a;
  font-weight: 500;
}

.explanation-text {
  background-color: var(--bg-color-tertiary, #f8f9fa);
  padding: 16rpx;
  border-radius: 8rpx;
}

.explanation-label {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
  font-weight: 500;
}

.explanation-content {
  font-size: 26rpx;
  line-height: 1.6;
  color: var(--text-color-primary, #333);
}
