// index.js
// 泡泡组件业务逻辑

// 获取 App 实例
const app = getApp();
// 导入API工具
const { bubbleAPI, tagAPI, learningPlanAPI } = require('../../utils/api');
// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');

// 泡泡主题配置（将在初始化时从API获取）
let BUBBLE_THEMES = [];

// 动画参数
const ANIMATION_SPEED_MULTIPLIER = 0.03;
const ANIMATION_BASE_SPEED = 0.5;
const SHADOW_BLUR = 4;

// 导入新的工具类和组件
const ThemeManager = require('../../utils/theme-manager');
const CanvasManager = require('../../utils/canvas-manager');

// 导入旧的画布模块（用于兼容模式）
const BubbleCanvas = require('./bubble-canvas');
// 导入星星画布模块
const StarCanvas = require('./star-canvas');

Page({
  data: {
    initialized: false,
    currentTheme: null,
    showThemeModal: false,
    bubbleFocused: false,
    showGuide: false,
    lastUpdateTime: 0,
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 0, // 导航栏总高度
    tabBarHeight: 0, // 底部区域高度
    contentPaddingTop: 0, // 内容区域顶部填充
    contentPaddingBottom: 0, // 内容区域底部填充
    inputMessage: '', // 输入框消息
    loadingFailed: false, // 加载失败状态
    interfaceStyle: 'bubble', // 界面样式: bubble或star
    isDarkMode: false, // 是否为深色模式
    useNewImplementation: true, // 始终使用新组件实现
    initProgress: 0, // 初始化进度（0-100）
    isLoginReady: false, // 新增登录状态标志
    showLoginPrompt: false, // 是否显示登录提示
    floatingButtonActive: false // 底部浮动按钮是否激活
  },

  // 页面加载
  onLoad: function () {
    // 获取应用实例 (已在顶部声明)
    // const app = getApp();

    // 记录页面加载开始时间
    this._pageLoadStartTime = Date.now();
    console.time('首页加载耗时');
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;

    // 计算导航栏总高度 (状态栏 + 导航内容区域)
    const navBarHeight = statusBarHeight + 45; // 状态栏 + 导航栏内容高度(90rpx/2=45px)

    // 计算底部区域高度 (tabbar高度 + 底部安全区域)
    const safeAreaBottom = systemInfo.screenHeight - systemInfo.safeArea.bottom;
    const tabBarHeight = 50 + safeAreaBottom; // 100rpx/2=50px + 底部安全区域

    // 从本地存储加载界面样式设置
    let interfaceStyle = wx.getStorageSync('interfaceStyle') || 'bubble';
    const themeMode = wx.getStorageSync('themeMode') || 'light';

    // 判断是否为深色模式
    let isDarkMode = false;
    if (themeMode === 'dark') {
      isDarkMode = true;
      // 深色模式自动使用星星样式
      interfaceStyle = 'star';
    } else if (themeMode === 'system') {
      // 根据系统设置判断
      isDarkMode = systemInfo.theme === 'dark';
      // 根据系统深色模式设置界面样式
      if (isDarkMode) {
        interfaceStyle = 'star';
      } else {
        interfaceStyle = 'bubble';
      }
    } else {
      // 浅色模式使用气泡样式
      interfaceStyle = 'bubble';
    }

    // 将当前使用的界面样式保存到存储，以便在其他页面使用
    wx.setStorageSync('interfaceStyle', interfaceStyle);
    console.log('设置界面样式:', interfaceStyle, '深色模式:', isDarkMode);

    // 设置页面的data-theme属性，用于应用深色背景
    if (isDarkMode) {
      // 在测试模式下跳过需要登录的API
      if (!app.globalData.isTestMode) {
        wx.setNavigationBarColor({
          frontColor: '#ffffff',
          backgroundColor: '#0f0921' // 深紫色导航栏
        });

        // 设置页面背景色 - 深紫色
        wx.setBackgroundColor({
          backgroundColor: '#0f0921',
          success: function () {
            console.log('设置深紫色背景成功');
          },
          fail: function (err) {
            console.error('设置深紫色背景失败', err);
          }
        });
      } else {
        console.log('[Test Mode] Skipping NavigationBarColor and BackgroundColor set in onLoad (Dark)');
      }
      wx.setStorageSync('themeMode', 'dark');
    } else {
      // 在测试模式下跳过需要登录的API
      if (!app.globalData.isTestMode) {
        wx.setNavigationBarColor({
          frontColor: '#000000',
          backgroundColor: '#ffffff'
        });

        // 设置页面背景色
        wx.setBackgroundColor({
          backgroundColor: '#ffffff',
          success: function () {
            console.log('设置浅色背景成功');
          },
          fail: function (err) {
            console.error('设置浅色背景失败', err);
          }
        });
      } else {
        console.log('[Test Mode] Skipping NavigationBarColor and BackgroundColor set in onLoad (Light)');
      }
      wx.setStorageSync('themeMode', 'light');
    }

    // 设置数据
    this.setData({
      statusBarHeight,
      navBarHeight,
      tabBarHeight,
      contentPaddingBottom: tabBarHeight, // 仅底部菜单区域,
      interfaceStyle,
      isDarkMode,
      useNewImplementation: true // 始终使用新组件
    });

    // 设置导航栏样式
    app.updateNavigationBarStyle();

    // 设置初始化超时检测
    this.initTimeout = setTimeout(() => {
      if (!this.data.initialized && !this.data.loadingFailed) {
        console.error('初始化超时，显示重试选项');
        this.setData({ loadingFailed: true });
      }
    }, 10000);

    // 初始化主题管理器
    this.themeManager = new ThemeManager({
      api: { tagAPI },
      cacheExpiry: 30 * 60 * 1000 // 30分钟缓存过期时间
    });

    // 初始化画布管理器
    this.canvasManager = new CanvasManager({
      page: this,
      themeManager: this.themeManager
    });

    // --- 修改：检查登录状态 ---
    app.ensureLoginReady((isLoggedIn, userInfo) => {
      if (isLoggedIn) {
        console.log('Index: 登录状态就绪，开始加载主题和画布');
        this.setData({ isLoginReady: true, showLoginPrompt: false });
        this.loadThemesAndInitCanvas(); // 调用封装的加载函数
      } else {
        console.log('Index: 用户未登录');
        this.setData({ isLoginReady: false, showLoginPrompt: true });
        // 处理未登录情况，例如显示登录按钮或提示
        // 可以加载一些不需要登录的默认数据或显示占位符
        // this.loadFallbackData();
      }
    });

    this.checkAndShowGuide();

    // 初始化浮动按钮状态
    if (this.canvasManager && this.canvasManager.getCurrentCanvas()) {
      this.updateFloatingButtonState();
    }
  },

  // --- 新增：封装加载主题和初始化画布的逻辑 ---
  loadThemesAndInitCanvas() {
    // 设置初始化超时检测 (移到这里)
    this.initTimeout = setTimeout(() => {
      if (!this.data.initialized && !this.data.loadingFailed) {
        console.error('初始化超时，显示重试选项');
        this.setData({ loadingFailed: true });
      }
    }, 10000);

    this.themeManager.loadCurrentPlanTags(true)
      .then(themes =>
        // 创建对应类型的画布
        this.canvasManager.createCanvas(this.data.interfaceStyle, {
          canvasId: `${this.data.interfaceStyle}-canvas`,
          config: {
            animationSpeedMultiplier: ANIMATION_SPEED_MULTIPLIER * 2, // 调整速度乘数
            baseSpeed: ANIMATION_BASE_SPEED * 1.2,
            shadowBlur: SHADOW_BLUR
          },
          onProgress: progress => {
            // 更新初始化进度
            this.setData({ initProgress: progress });
          }
        })
      )
      .then(canvas => {
        console.log(`${this.data.interfaceStyle}画布创建成功`);
        this.setData({ initialized: true, loadingFailed: false }); // 成功后清除失败状态
        if (this.initTimeout) {
          clearTimeout(this.initTimeout);
          this.initTimeout = null;
        }

        // 更新浮动按钮状态
        this.updateFloatingButtonState();
      })
      .catch(err => {
        console.error('初始化画布失败:', err);
        this.setData({ loadingFailed: true });
        if (this.initTimeout) {
          clearTimeout(this.initTimeout);
          this.initTimeout = null;
        }
      });
  },

  // 页面显示
  onShow: function () {
    console.log('页面显示，检查是否需要重新加载主题数据');

    // 获取当前主题模式
    const app = getApp();
    const themeMode = wx.getStorageSync('themeMode') || 'light';

    // 始终更新当前页面的导航栏样式
    if (app && app.updateCurrentPageNavigationBar) {
      console.log('首页 - 更新导航栏样式');
      app.updateCurrentPageNavigationBar();
    }

    // --- 修改：调整动画恢复逻辑 ---
    // 检查登录状态和 Canvas 管理器
    const isLoggedIn = this.data.isLoginReady;
    const canvasManagerExists = !!this.canvasManager;
    const isTestMode = app.globalData.isTestMode;

    if (canvasManagerExists) {
      // 在测试模式下，无论是否登录都恢复动画
      // 在非测试模式下，仅在登录后恢复动画
      if (isTestMode || isLoggedIn) {
        console.log(`首页 onShow: 恢复动画 (isTestMode: ${isTestMode}, isLoggedIn: ${isLoggedIn})`);
        this.canvasManager.resumeAnimation();
      } else {
        console.log('首页 onShow: 未登录且非测试模式，不恢复动画');
      }
    } else {
      console.log('首页 onShow: canvasManager 不存在，无法恢复动画');
    }

    // --- 修改：调整登录状态检查和数据加载逻辑 ---
    // 检查登录状态是否就绪，如果未就绪，尝试重新检查
    if (!isLoggedIn) {
      app.ensureLoginReady((loginStatus, userInfo) => {
        const wasReady = this.data.isLoginReady;
        this.setData({ isLoginReady: loginStatus }); // 更新登录状态
        if (loginStatus && !wasReady) { // 仅当状态从未就绪变为就绪时加载
          console.log('Index (onShow): 登录状态变为就绪，加载主题和画布');
          this.setData({ showLoginPrompt: false });
          this.loadThemesAndInitCanvas();
        } else if (!loginStatus) {
          this.setData({ showLoginPrompt: true });
        }
      });
    }
    // 检查主题数据更新 (仅在登录或测试模式下有意义，因为需要画布)
    if ((isLoggedIn || isTestMode) && canvasManagerExists) {
      const lastUpdateTime = wx.getStorageSync('themeUpdateTime') || 0;
      if (lastUpdateTime > this.data.lastUpdateTime) {
        console.log('检测到主题数据已更新，重新加载');
        this.setData({ lastUpdateTime: lastUpdateTime });
        this.themeManager.loadCurrentPlanTags(true)
          .then(themes => {
            this.canvasManager.updateThemes(themes);
            // 更新浮动按钮状态
            this.updateFloatingButtonState();
          })
          .catch(err => console.error('更新主题数据失败:', err));
      }
    }

    // --- 保持原有主题和界面样式检查逻辑 ---
    const storedThemeMode = wx.getStorageSync('themeMode') || 'light';
    const systemInfo = wx.getSystemInfoSync();
    let newIsDarkMode = false;
    let shouldUpdateInterface = false;
    let newInterfaceStyle = this.data.interfaceStyle;

    if (storedThemeMode === 'dark') {
      newIsDarkMode = true;
      // 深色模式应该使用星星样式
      newInterfaceStyle = 'star';
      shouldUpdateInterface = (newInterfaceStyle !== this.data.interfaceStyle);
    } else if (storedThemeMode === 'system') {
      // 根据系统设置判断
      newIsDarkMode = systemInfo.theme === 'dark';
      // 根据系统深色模式设置界面样式
      if (newIsDarkMode) {
        newInterfaceStyle = 'star';
      } else {
        newInterfaceStyle = 'bubble';
      }
      shouldUpdateInterface = (newInterfaceStyle !== this.data.interfaceStyle);
    } else {
      // 浅色模式使用气泡样式
      newIsDarkMode = false;
      newInterfaceStyle = 'bubble';
      shouldUpdateInterface = (newInterfaceStyle !== this.data.interfaceStyle);
    }

    // 保存新的界面样式到本地存储
    if (shouldUpdateInterface) {
      wx.setStorageSync('interfaceStyle', newInterfaceStyle);
      console.log('更新界面样式:', newInterfaceStyle, '深色模式:', newIsDarkMode);
    }

    // 如果深色模式状态变更，更新视图
    if (newIsDarkMode !== this.data.isDarkMode || shouldUpdateInterface) {
      this.setData({
        isDarkMode: newIsDarkMode,
        interfaceStyle: newInterfaceStyle
      });

      // 重新初始化画布
      if (this.data.initialized) {
        if (this.data.useNewImplementation) {
          // 使用新的组件实现
          if (this.canvasManager) {
            this.canvasManager.switchCanvas(newInterfaceStyle, {
              canvasId: `${newInterfaceStyle}-canvas`,
              config: {
                animationSpeedMultiplier: ANIMATION_SPEED_MULTIPLIER * 2,
                baseSpeed: ANIMATION_BASE_SPEED * 1.2,
                shadowBlur: SHADOW_BLUR
              }
            }).catch(err => {
              console.error('切换画布失败:', err);
            });
          }
        } else {
          // 使用旧的实现
          this.initCanvas();
        }
      }
    }
  },

  // 页面隐藏
  onHide: function () {
    if (this.data.useNewImplementation) {
      // 使用新的组件实现
      if (this.canvasManager) {
        this.canvasManager.pauseAnimation();
      }
    } else {
      // 使用旧的实现
      if (this.bubbleCanvas) {
        this.bubbleCanvas.stopAnimation();
      }
      if (this.starCanvas) {
        this.starCanvas.stopAnimation();
      }
    }
  },

  // 页面卸载
  onUnload: function () {
    // 清除特性开关回调
    const app = getApp();
    if (app) {
      app.featureFlagChangeCallback = null;
    }

    if (this.data.useNewImplementation) {
      // 使用新的组件实现
      if (this.canvasManager) {
        this.canvasManager.cleanup();
        this.canvasManager = null;
      }

      if (this.themeManager) {
        this.themeManager = null;
      }
    } else {
      // 使用旧的实现
      if (this.bubbleCanvas) {
        this.bubbleCanvas.stopAnimation();
        this.bubbleCanvas = null;
      }
      if (this.starCanvas) {
        this.starCanvas.stopAnimation();
        this.starCanvas = null;
      }
    }

    // 清除超时计时器
    if (this.initTimeout) {
      clearTimeout(this.initTimeout);
      this.initTimeout = null;
    }
  },

  // 初始化画布
  initCanvas: function () {
    if (this.isInitializing) return;

    this.isInitializing = true;
    this.setData({ initialized: false, loadingFailed: false });

    try {
      // 根据界面样式选择对应的画布组件
      if (this.data.interfaceStyle === 'bubble') {
        // 清理可能存在的星星画布
        if (this.starCanvas) {
          this.starCanvas.stopAnimation();
          this.starCanvas = null;
        }
        // 创建气泡画布实例
        this.bubbleCanvas = new BubbleCanvas(this);
        // 初始化画布
        const initStarted = this.bubbleCanvas.init();
        if (!initStarted) {
          console.error('初始化气泡画布失败，准备重试');
          this.isInitializing = false;
          this.retryInitialization();
        }
      } else {
        // 清理可能存在的气泡画布
        if (this.bubbleCanvas) {
          this.bubbleCanvas.stopAnimation();
          this.bubbleCanvas = null;
        }
        // 创建星星画布实例
        this.starCanvas = new StarCanvas(this);
        // 初始化画布
        const initStarted = this.starCanvas.init();
        if (!initStarted) {
          console.error('初始化星星画布失败，准备重试');
          this.isInitializing = false;
          this.retryInitialization();
        }
      }

      // 5秒后检查是否初始化成功
      setTimeout(() => {
        if (!this.data.initialized) {
          console.warn('画布初始化超时，重试中...');
          this.isInitializing = false;
          this.retryInitialization();
        } else {
          this.isInitializing = false;
        }
      }, 5000);
    } catch (err) {
      console.error('初始化画布失败', err);
      this.isInitializing = false;
      this.retryInitialization();
    }
  },

  // 重试初始化
  retryInitialization: function () {
    if (this.retryAttempts >= 3) {
      this.setData({
        loadingFailed: true
      });
      return;
    }

    this.retryAttempts = (this.retryAttempts || 0) + 1;

    console.log(`重试初始化画布 (第${this.retryAttempts}次尝试)`);

    setTimeout(() => {
      this.initCanvas();
    }, 1000);
  },

  // 重新加载页面
  retryLoading: function () {
    this.setData({
      loadingFailed: false,
      initialized: false,
      initProgress: 0
    });

    this.retryAttempts = 0;

    // 清理现有资源
    if (this.data.useNewImplementation) {
      // 使用新的组件实现
      if (this.canvasManager) {
        this.canvasManager.cleanup();
        this.canvasManager = null;
      }

      // 初始化主题管理器
      this.themeManager = new ThemeManager({
        api: { tagAPI },
        cacheExpiry: 30 * 60 * 1000 // 30分钟缓存过期时间
      });

      // 初始化画布管理器
      this.canvasManager = new CanvasManager({
        page: this,
        themeManager: this.themeManager
      });

      // 加载主题数据并初始化画布
      this.themeManager.loadCurrentPlanTags(true)
        .then(themes =>
          // 创建对应类型的画布
          this.canvasManager.createCanvas(this.data.interfaceStyle, {
            canvasId: `${this.data.interfaceStyle}-canvas`,
            config: {
              animationSpeedMultiplier: ANIMATION_SPEED_MULTIPLIER * 2, // 调整速度乘数
              baseSpeed: ANIMATION_BASE_SPEED * 1.2,
              shadowBlur: SHADOW_BLUR
            },
            onProgress: progress => {
              // 更新初始化进度
              this.setData({ initProgress: progress });
            }
          })
        )
        .then(canvas => {
          console.log(`${this.data.interfaceStyle}画布创建成功`);
          this.setData({ initialized: true });

          // 清除超时计时器
          if (this.initTimeout) {
            clearTimeout(this.initTimeout);
            this.initTimeout = null;
          }

          // 更新浮动按钮状态
          this.updateFloatingButtonState();
        })
        .catch(err => {
          console.error('初始化画布失败:', err);
          this.setData({ loadingFailed: true });
        });
    } else {
      // 使用旧的实现
      // 清理现有资源
      if (this.bubbleCanvas) {
        this.bubbleCanvas.stopAnimation();
        this.bubbleCanvas = null;
      }
      if (this.starCanvas) {
        this.starCanvas.stopAnimation();
        this.starCanvas = null;
      }

      // 重新加载标签数据和初始化画布，使用强制刷新选项
      this.loadCurrentPlanTags(true).then(() => {
        if (BUBBLE_THEMES.length > 0) {
          this.initCanvas();
        }
      }).catch(() => {
        this.initCanvas();
      });
    }
  },

  // 处理触摸开始事件
  onTouchStart: function (e) {
    if (this.data.useNewImplementation) {
      // 使用新的组件实现
      if (this.canvasManager) {
        const result = this.canvasManager.handleTouchStart(e);
        if (result) {
          this.setData({
            currentTheme: result.theme,
            showThemeModal: true
          });
        }
      }
    } else {
      // 使用旧的实现
      // 根据界面样式调用对应画布的触摸处理函数
      if (this.data.interfaceStyle === 'bubble' && this.bubbleCanvas) {
        const result = this.bubbleCanvas.handleTouchStart(e);
        if (result) {
          this.setData({
            currentTheme: result.theme,
            showThemeModal: true
          });
        }
      } else if (this.data.interfaceStyle === 'star' && this.starCanvas) {
        const result = this.starCanvas.handleTouchStart(e);
        if (result) {
          this.setData({
            currentTheme: result.theme,
            showThemeModal: true
          });
        }
      }
    }
  },

  // 处理触摸移动事件
  onTouchMove: function (e) {
    if (this.data.useNewImplementation) {
      // 使用新的组件实现
      if (this.canvasManager) {
        const cursor = this.canvasManager.handleTouchMove(e);
        this.setData({
          bubbleFocused: cursor === 'pointer'
        });
      }
    } else {
      // 使用旧的实现
      // 根据界面样式调用对应画布的触摸处理函数
      if (this.data.interfaceStyle === 'bubble' && this.bubbleCanvas) {
        const cursor = this.bubbleCanvas.handleTouchMove(e);
        this.setData({
          bubbleFocused: cursor === 'pointer'
        });
      } else if (this.data.interfaceStyle === 'star' && this.starCanvas) {
        const cursor = this.starCanvas.handleTouchMove(e);
        this.setData({
          bubbleFocused: cursor === 'pointer'
        });
      }
    }
  },

  // 处理触摸结束事件
  onTouchEnd: function () {
    if (this.data.useNewImplementation) {
      // 使用新的组件实现
      if (this.canvasManager) {
        const result = this.canvasManager.handleTouchEnd();
        if (result) {
          this.setData({
            currentTheme: result.theme,
            showThemeModal: true
          });
        }
      }
    } else {
      // 使用旧的实现
      // 根据界面样式调用对应画布的触摸处理函数
      if (this.data.interfaceStyle === 'bubble' && this.bubbleCanvas) {
        const result = this.bubbleCanvas.handleTouchEnd();
        if (result) {
          this.setData({
            currentTheme: result.theme,
            showThemeModal: true
          });
        }
      } else if (this.data.interfaceStyle === 'star' && this.starCanvas) {
        const result = this.starCanvas.handleTouchEnd();
        if (result) {
          this.setData({
            currentTheme: result.theme,
            showThemeModal: true
          });
        }
      }
    }

    // 重置鼠标样式
    this.setData({
      bubbleFocused: false
    });
  },

  // 获取泡泡主题配置 - 为画布提供
  getBubbleThemes: function () {
    console.log('getBubbleThemes被调用，当前BUBBLE_THEMES:', BUBBLE_THEMES);

    // 如果BUBBLE_THEMES为空，尝试使用默认主题
    if (!BUBBLE_THEMES || BUBBLE_THEMES.length === 0) {
      console.warn('BUBBLE_THEMES为空，尝试使用默认主题');
      BUBBLE_THEMES = this.getDefaultBubbleThemes();
      console.log('使用默认主题后的BUBBLE_THEMES:', BUBBLE_THEMES);
    }

    return BUBBLE_THEMES;
  },

  // 从后端加载当前学习计划的标签数据
  loadCurrentPlanTags: function (forceRefresh = false) {
    if (!tagAPI || !tagAPI.getCurrentPlanTags) {
      console.error('tagAPI不可用');
      return Promise.reject('API不可用');
    }

    console.log('开始加载当前学习计划标签...', forceRefresh ? '(强制刷新)' : '');

    // 获取洗牌后的颜色数组
    const shuffledColors = this.getShuffledColors();

    return tagAPI.getCurrentPlanTags(forceRefresh)
      .then(res => {
        if (res && res.data && res.data.length > 0) {
          console.log('成功加载标签数据:', res.data);
          BUBBLE_THEMES = res.data.map((item, index) => ({
            id: item.id,
            name: item.name,
            englishName: item.englishName || '',
            description: item.description || '暂无描述',
            color: item.color || shuffledColors[index % shuffledColors.length]
          }));
          console.log('处理后的BUBBLE_THEMES:', BUBBLE_THEMES);

          // 更新主题更新时间
          wx.setStorageSync('themeUpdateTime', Date.now());
          this.setData({ lastUpdateTime: Date.now() });

          return BUBBLE_THEMES;
        } else {
          console.log('API返回的标签数据为空，尝试加载系统默认标签');
          // 如果当前计划没有标签，尝试获取系统默认标签
          return this.loadSystemDefaultTags();
        }
      })
      .catch(err => {
        console.error('加载标签失败', err);
        // 失败时尝试获取系统默认标签
        console.log('尝试获取系统默认标签');
        return this.loadSystemDefaultTags();
      });
  },

  // 加载系统默认标签
  loadSystemDefaultTags: function () {
    if (!tagAPI || !tagAPI.getSystemDefaultPlanTags) {
      console.error('tagAPI.getSystemDefaultPlanTags不可用');
      BUBBLE_THEMES = this.getFallbackBubbleThemes();
      return Promise.resolve(BUBBLE_THEMES);
    }

    console.log('开始加载系统默认标签...');

    // 获取洗牌后的颜色数组
    const shuffledColors = this.getShuffledColors();

    return tagAPI.getSystemDefaultPlanTags()
      .then(res => {
        if (res && res.success && res.data && res.data.tags && res.data.tags.length > 0) {
          console.log('成功从API获取系统默认标签:', res.data.tags);
          BUBBLE_THEMES = res.data.tags.map((item, index) => ({
            id: item.id,
            name: item.name,
            englishName: item.englishName || '',
            description: item.description || '暂无描述',
            color: item.color || shuffledColors[index % shuffledColors.length]
          }));
          console.log('使用系统默认标签更新BUBBLE_THEMES:', BUBBLE_THEMES);

          // 更新主题更新时间
          wx.setStorageSync('themeUpdateTime', Date.now());
          this.setData({ lastUpdateTime: Date.now() });

          return BUBBLE_THEMES;
        } else {
          // 如果没有系统默认标签数据，使用后备泡泡主题
          console.warn('系统默认标签数据为空，使用后备泡泡主题');
          BUBBLE_THEMES = this.getFallbackBubbleThemes();
          return BUBBLE_THEMES;
        }
      })
      .catch(err => {
        console.error('获取系统默认标签失败:', err);
        // 使用后备泡泡主题
        BUBBLE_THEMES = this.getFallbackBubbleThemes();
        return BUBBLE_THEMES;
      });
  },

  // 关闭主题详情弹窗
  closeThemeModal: function () {
    this.setData({
      showThemeModal: false
    });

    if (this.data.useNewImplementation) {
      // 使用新的组件实现
      if (this.canvasManager) {
        const canvas = this.canvasManager.getCurrentCanvas();
        if (canvas) {
          canvas.resetInteractionState();
        }
      }
    } else {
      // 使用旧的实现
      // 重置泡泡点击状态
      if (this.data.interfaceStyle === 'bubble' && this.bubbleCanvas) {
        this.bubbleCanvas.resetClickState();
      } else if (this.data.interfaceStyle === 'star' && this.starCanvas) {
        this.starCanvas.resetClickState();
      }
    }
  },

  // 开始学习
  startLearning: function () {
    if (!this.data.currentTheme) return;

    const tagId = this.data.currentTheme.id;
    // 跳转到学习页面
    wx.navigateTo({
      url: `/pages/theme-detail/index?tagId=${tagId}`
    });
    // 关闭弹窗
    this.closeThemeModal();
  },

  // 生成默认的泡泡主题配置
  getDefaultBubbleThemes: function () {
    console.log('尝试从API获取系统默认标签');

    // 获取洗牌后的颜色数组
    const shuffledColors = this.getShuffledColors();

    // 先尝试从API获取系统默认标签
    if (tagAPI && tagAPI.getSystemDefaultPlanTags) {
      // 同步方式存储结果
      let systemTags = [];

      // 发起异步请求
      tagAPI.getSystemDefaultPlanTags()
        .then(res => {
          if (res && res.success && res.data && res.data.tags && res.data.tags.length > 0) {
            console.log('成功从API获取系统默认标签:', res.data.tags);
            // 转换API返回的数据格式
            systemTags = res.data.tags.map((item, index) => ({
              id: item.id.toString(),
              name: item.name,
              englishName: item.englishName || '',
              description: item.description || '暂无描述',
              color: item.color || shuffledColors[index % shuffledColors.length]
            }));

            // 更新全局变量
            BUBBLE_THEMES = systemTags;
            console.log('更新BUBBLE_THEMES为系统默认标签:', BUBBLE_THEMES);

            // 更新主题更新时间
            wx.setStorageSync('themeUpdateTime', Date.now());
            this.setData({ lastUpdateTime: Date.now() });

            // 刷新画布
            if (this.data.interfaceStyle === 'bubble' && this.bubbleCanvas) {
              this.bubbleCanvas.updateBubbleThemes(BUBBLE_THEMES);
            } else if (this.data.interfaceStyle === 'star' && this.starCanvas) {
              this.starCanvas.updateStarThemes(BUBBLE_THEMES);
            }

            return systemTags;
          } else {
            console.error('API返回的系统默认标签数据无效:', res);
            return this.getFallbackBubbleThemes();
          }
        })
        .catch(err => {
          console.error('获取系统默认标签失败:', err);
          return this.getFallbackBubbleThemes();
        });
    } else {
      console.error('tagAPI不可用，无法获取系统默认标签');
      return this.getFallbackBubbleThemes();
    }

    // 返回临时的默认主题，在API请求成功后会被更新
    return this.getFallbackBubbleThemes();
  },

  // 提供后备的泡泡主题配置（仅在无法从API获取数据时使用）
  getFallbackBubbleThemes: function () {
    console.warn('使用后备泡泡主题（临时）');

    // 使用新的颜色，确保包含lime颜色
    return [
      {
        id: '101',
        name: '平台介绍',
        englishName: 'Intro',
        description: '了解AIBUBB平台的核心功能和使用技巧',
        color: this.colorMap.blue // '#3B82F6'
      },
      {
        id: '102',
        name: '泡泡功能',
        englishName: 'Bubbles',
        description: '探索交互式泡泡的使用方法',
        color: this.colorMap.lime // '#84CC16' - 使用lime颜色
      },
      {
        id: '103',
        name: '广场探索',
        englishName: 'Square',
        description: '发现社区中的精彩内容和讨论',
        color: this.colorMap.sky // '#0EA5E9'
      },
      {
        id: '104',
        name: '学习计划',
        englishName: 'Plans',
        description: '制定和管理个性化学习计划',
        color: this.colorMap.amber // '#F59E0B'
      },
      {
        id: '105',
        name: '笔记技巧',
        englishName: 'Notes',
        description: '掌握高效记录和组织学习笔记的方法',
        color: this.colorMap.purple // '#8B5CF6'
      }
    ];
  },

  // 颜色映射
  colorMap: {
    amber: '#F59E0B',
    blue: '#3B82F6',
    purple: '#8B5CF6',
    red: '#EF4444',
    yellow: '#EAB308',
    pink: '#EC4899',
    orange: '#F97316',
    indigo: '#6366F1',
    teal: '#14B8A6',
    brightYellow: '#FACC15',
    violet: '#8B5CF6',
    sky: '#0EA5E9',
    rose: '#E11D48',
    lime: '#84CC16',
    green: '#22c55e',
    cyan: '#06b6d4',
    fuchsia: '#d946ef'
  },

  // 获取随机颜色
  getRandomColor: function () {
    // lime颜色出现概率是其他颜色的3倍
    const colors = [
      '#3B82F6', '#8B5CF6', '#EF4444', '#F59E0B',
      '#EAB308', '#EC4899', '#F97316', '#6366F1',
      '#14B8A6', '#0EA5E9', '#22c55e',
      // lime颜色重复添加3次，增加其出现概率
      '#84CC16', '#84CC16', '#84CC16'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  },

  // 获取洗牌后的颜色数组
  getShuffledColors: function () {
    // 创建一个包含所有颜色的数组，lime颜色重复3次
    const colorKeys = Object.keys(this.colorMap);

    // 创建一个新数组，包含所有颜色，但lime颜色重复3次
    const extendedColorKeys = [...colorKeys];

    // 找到lime的索引
    const limeIndex = colorKeys.indexOf('lime');

    // 如果找到lime，则多添加两次
    if (limeIndex !== -1) {
      extendedColorKeys.push('lime', 'lime');
    }

    // 洗牌算法
    for (let i = extendedColorKeys.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [extendedColorKeys[i], extendedColorKeys[j]] = [extendedColorKeys[j], extendedColorKeys[i]];
    }

    return extendedColorKeys.map(key => this.colorMap[key]);
  },

  // 检测是否为浅色
  isLightColor: function (color) {
    // 预定义的浅色名称列表
    const lightColorNames = ['yellow', 'amber', 'lime', 'brightYellow', 'green', 'cyan'];
    if (typeof color === 'string' && lightColorNames.includes(color.toLowerCase())) {
      return true;
    }

    try {
      // 确保颜色是十六进制格式
      if (!color || typeof color !== 'string') {
        return false;
      }

      // 如果颜色不是十六进制格式，无法计算亮度
      if (!color.startsWith('#')) {
        return false;
      }

      // 移除#前缀
      const hexColor = color.replace('#', '');

      // 解析RGB值
      const r = parseInt(hexColor.substr(0, 2), 16);
      const g = parseInt(hexColor.substr(2, 2), 16);
      const b = parseInt(hexColor.substr(4, 2), 16);

      // 计算亮度 (基于人眼对不同颜色的感知)
      // 公式: (0.299*R + 0.587*G + 0.114*B)
      const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      // 亮度大于0.6认为是浅色
      return brightness > 0.6;
    } catch (err) {
      console.error('颜色亮度计算失败', err);
      return false;
    }
  },

  // 检查并显示新手引导
  checkAndShowGuide: function () {
    const hasShownGuide = wx.getStorageSync('hasShownGuide');
    if (!hasShownGuide) {
      // 延迟显示引导，确保页面加载完成
      setTimeout(() => {
        this.setData({
          showGuide: true
        });
        wx.setStorageSync('hasShownGuide', true);
      }, 1000);
    }
  },

  // 关闭引导
  closeGuide: function () {
    this.setData({
      showGuide: false
    });
  },

  // 阻止冒泡
  preventBubble: function (e) {
    // 阻止事件传递
  },

  // 点击分享
  onShareAppMessage: function () {
    return {
      title: '沟通实验室',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.png'
    };
  },

  // 圆形浮动按钮点击事件
  onFloatingButtonTap: function () {
    console.log('点击了圆形浮动按钮');

    // 检查是否有活跃的画布
    if (!this.canvasManager || !this.canvasManager.getCurrentCanvas()) {
      wx.showToast({
        title: '画布未初始化',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取当前画布
    const canvas = this.canvasManager.getCurrentCanvas();

    // 检查是否所有泡泡/星星都已完成
    const allElementsCompleted = canvas.checkAllElementsCompleted();

    if (allElementsCompleted) {
      // 所有元素已完成，先显示清屏奖励动画
      canvas.showClearScreenReward().then(() => {
        // 奖励动画完成后，加载下一组任务
        wx.showLoading({
          title: '加载中...',
          mask: true
        });

        // 重新加载主题数据
        this.themeManager.loadCurrentPlanTags(true)
          .then(themes => {
            // 更新画布主题
            this.canvasManager.updateThemes(themes);

            // 创建新的泡泡/星星
            canvas.createNewElements();

            wx.hideLoading();
            wx.showToast({
              title: '已加载新任务',
              icon: 'success',
              duration: 2000
            });

            // 更新按钮状态
            this.updateFloatingButtonState();
          })
          .catch(err => {
            console.error('加载新任务失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '加载失败，请重试',
              icon: 'none',
              duration: 2000
            });
          });
      });
    } else {
      // 还有未完成的元素，提示用户
      wx.showToast({
        title: '请先完成当前任务',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 更新浮动按钮状态
  updateFloatingButtonState: function () {
    // 检查是否有活跃的画布
    if (!this.canvasManager || !this.canvasManager.getCurrentCanvas()) {
      return;
    }

    // 获取当前画布
    const canvas = this.canvasManager.getCurrentCanvas();

    // 检查是否所有泡泡/星星都已完成
    const allElementsCompleted = canvas.checkAllElementsCompleted();

    // 更新按钮状态
    this.setData({
      floatingButtonActive: allElementsCompleted
    });
  },

  // 监听主题变化
  onThemeChange: function (newTheme) {
    console.log('首页接收到主题变化:', newTheme);

    // 更新页面主题状态
    const isDarkMode = newTheme === 'dark';
    const interfaceStyle = isDarkMode ? 'star' : 'bubble';

    this.setData({
      isDarkMode,
      interfaceStyle
    });

    // 设置页面的data-theme属性，用于应用深色背景
    if (isDarkMode) {
      // 设置页面的data-theme属性
      // wx.getSystemInfoSync().theme = 'dark'; // 不应直接修改 getSystemInfoSync 返回值

      // --- MODIFICATION START ---
      // 在测试模式下跳过需要登录的API
      if (!app.globalData.isTestMode) {
      // --- MODIFICATION END ---
        wx.setNavigationBarColor({
          frontColor: '#ffffff',
          backgroundColor: '#0f0921' // 深紫色导航栏
        });

        // 设置页面背景色 - 深紫色
        wx.setBackgroundColor({
          backgroundColor: '#0f0921',
          success: function () {
            console.log('设置深紫色背景成功');
          },
          fail: function (err) {
            console.error('设置深紫色背景失败', err);
          }
        });
      // --- MODIFICATION START ---
      } else {
        console.log('[Test Mode] Skipping NavigationBarColor and BackgroundColor set in onLoad (Dark)');
      }
      // --- MODIFICATION END ---
      wx.setStorageSync('themeMode', 'dark'); // 保存模式还是要的
    } else {
      // 设置页面的data-theme属性
      // wx.getSystemInfoSync().theme = 'light'; // 不应直接修改 getSystemInfoSync 返回值

      // --- MODIFICATION START ---
      // 在测试模式下跳过需要登录的API
      if (!app.globalData.isTestMode) {
      // --- MODIFICATION END ---
        wx.setNavigationBarColor({
          frontColor: '#000000',
          backgroundColor: '#ffffff'
        });

        // 设置页面背景色
        wx.setBackgroundColor({
          backgroundColor: '#ffffff',
          success: function () {
            console.log('设置浅色背景成功');
          },
          fail: function (err) {
            console.error('设置浅色背景失败', err);
          }
        });
      // --- MODIFICATION START ---
      } else {
        console.log('[Test Mode] Skipping NavigationBarColor and BackgroundColor set in onLoad (Light)');
      }
      // --- MODIFICATION END ---
      wx.setStorageSync('themeMode', 'light'); // 保存模式还是要的
    }

    // 切换显示的画布组件
    if (interfaceStyle === 'bubble' && this.bubbleCanvas) {
      // 显示气泡画布，隐藏星星画布
      if (this.starCanvas) {
        // 先暂停星星动画
        this.starCanvas.stopAnimation();
      }

      // 启动气泡动画
      this.bubbleCanvas.startAnimation();

      // 如果有主题数据，更新气泡主题
      if (BUBBLE_THEMES.length > 0 && typeof this.bubbleCanvas.updateBubbleThemes === 'function') {
        this.bubbleCanvas.updateBubbleThemes(BUBBLE_THEMES);
      }
    } else if (interfaceStyle === 'star' && this.starCanvas) {
      // 显示星星画布，隐藏气泡画布
      if (this.bubbleCanvas) {
        // 先暂停气泡动画
        this.bubbleCanvas.stopAnimation();
      }

      // 启动星星动画
      this.starCanvas.startAnimation();

      // 如果有主题数据，更新星星主题
      if (BUBBLE_THEMES.length > 0 && typeof this.starCanvas.updateStarThemes === 'function') {
        this.starCanvas.updateStarThemes(BUBBLE_THEMES);
      }
    }

    // 无需页面跳转，直接刷新数据
    this.loadCurrentPlanTags(true);
  },

  // --- 新增：用户点击登录提示按钮的事件处理函数 (示例) ---
  handleLoginPromptTap() {
    wx.navigateTo({ url: '/pages/login/index' }); // 跳转到统一登录入口页
  },

  // --- 新增：处理重试加载的函数 (示例) ---
  handleRetryLoad() {
    this.setData({ loadingFailed: false, initProgress: 0 }); // 重置状态
    if (this.data.isLoginReady) {
      this.loadThemesAndInitCanvas(); // 重新加载
    } else {
      // 如果未登录，再次触发登录检查
      app.ensureLoginReady((isLoggedIn, userInfo) => {
        if (isLoggedIn) {
          this.setData({ isLoginReady: true, showLoginPrompt: false });
          this.loadThemesAndInitCanvas();
        } else {
          this.setData({ showLoginPrompt: true, loadingFailed: true }); // 还是失败，显示登录提示
        }
      });
    }
  }
});