// 导入API工具
const { learningPlanAPI, themeAPI, tagAPI } = require('../../utils/api');
// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');

// 学习页面逻辑
Page({
  data: {
    activeView: 'plan', // 默认显示学习计划视图
    isLoading: true, // 加载状态
    loadingFailed: false, // 加载失败状态
    learningPlans: [], // 学习计划列表
    currentPlan: null, // 当前学习计划
    currentPlanIndex: 0, // 当前学习计划在列表中的索引
    planTags: [], // 当前计划的标签
    themes: [], // 可用主题列表
    modules: [], // 学习模块（基于学习计划和标签）
    // 练习记录示例数据
    practiceRecords: [],
    // 模版资源数据
    templateResources: [],
    prevToken: null, // 用于检测登录状态变化
    isDarkMode: false // 新增的黑暗模式状态
  },

  // 页面加载
  onLoad: function () {
    // 检查是否已有本地存储的用户信息和token
    const token = wx.getStorageSync('token');

    // 初始化页面状态
    this.setData({
      isLoading: true,
      loadingFailed: false,
      prevToken: token // 保存当前token状态
    });

    // 设置导航栏样式
    const app = getApp();
    app.updateNavigationBarStyle();

    // 加载数据
    this.loadData();
  },

  // 页面显示
  onShow: function () {
    // 获取当前主题模式
    const app = getApp();
    const themeMode = wx.getStorageSync('themeMode') || 'light';

    // 始终更新当前页面的导航栏样式
    if (app && app.updateCurrentPageNavigationBar) {
      console.log('学习页面 - 更新导航栏样式');
      app.updateCurrentPageNavigationBar();
    }

    // 检查token是否变化（登录状态变化）
    const token = wx.getStorageSync('token');
    const prevToken = this.data.prevToken;

    // 如果token状态变化，重新加载数据
    if (token !== prevToken) {
      this.setData({
        prevToken: token,
        isLoading: true,
        loadingFailed: false
      });

      // 每次显示页面时刷新数据
      this.loadData();
    }
  },

  // 加载数据
  loadData: function () {
    this.setData({ isLoading: true, loadingFailed: false });

    // 获取学习计划列表
    this.loadLearningPlans();

    // 加载模版资源
    this.loadTemplateResources();
  },

  // 加载学习计划列表
  loadLearningPlans: function () {
    console.log('开始加载学习计划列表');

    // 直接检查token，不使用app.checkLogin（避免自动跳转）
    const token = wx.getStorageSync('token');

    if (!token) {
      // 用户未登录，加载系统默认的"平台指南"学习计划
      console.log('未找到token，加载系统默认学习计划');
      this.loadSystemDefaultPlan();
      return;
    }

    // 用户已登录，加载用户的学习计划数据
    console.log('用户已登录，加载用户的学习计划数据');

    learningPlanAPI.getPlans()
      .then(res => {
        console.log('学习计划API返回结果:', res);
        if (res.success && res.data && res.data.plans) {
          // 对于已登录用户，只显示用户自己的计划
          const allPlans = res.data.plans || [];

          // 构建学习模块（基于学习计划）
          const modules = this.buildModulesFromPlans(allPlans);

          this.setData({
            learningPlans: allPlans,
            modules: modules,
            currentPlanIndex: 0,
            isLoading: false,
            loadingFailed: false
          });

          console.log('成功加载用户学习计划，模块数:', modules.length);
        } else {
          console.warn('API返回成功但数据格式不正确:', res);
          // 显示加载失败状态
          this.setData({
            isLoading: false,
            loadingFailed: true
          });

          wx.showToast({
            title: '加载失败，请重试',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取学习计划列表失败', err);
        // 显示加载失败状态
        this.setData({
          isLoading: false,
          loadingFailed: true
        });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载学习计划详情
  loadPlanDetails: function (planId) {
    console.log('加载学习计划详情，计划ID:', planId);

    // 如果已经有模块数据，保留现有模块
    if (this.data.modules && this.data.modules.length > 0) {
      console.log('已有模块数据，保留现有显示');
    }

    learningPlanAPI.getPlanById(planId)
      .then(res => {
        if (res.success && res.data.plan) {
          const plan = res.data.plan;
          const tags = res.data.tags || [];

          // 构建学习模块（基于学习计划）
          const modules = this.buildModulesFromPlans([plan]);

          this.setData({
            currentPlan: plan,
            planTags: tags,
            modules,
            isLoading: false,
            loadingFailed: false
          });

          // 加载练习记录
          this.loadPracticeRecords(planId);
        } else {
          console.warn('API返回成功但数据格式不正确');
          // 显示加载失败状态
          this.setData({
            isLoading: false,
            loadingFailed: true
          });

          wx.showToast({
            title: '加载失败，请重试',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取学习计划详情失败', err);

        // 显示加载失败状态
        this.setData({
          isLoading: false,
          loadingFailed: true
        });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载主题列表
  loadThemes: function () {
    // 默认主题数据（当API调用失败时使用）
    const DEFAULT_THEMES = [
      { id: 1, name: '人际沟通', englishName: 'Interpersonal Communication', description: '提升人际沟通能力，建立良好人际关系', icon: '👥', color: '#3B82F6' }
    ];

    // 先设置默认主题，减少白屏时间
    this.setData({
      themes: DEFAULT_THEMES
    });

    themeAPI.getThemes()
      .then(res => {
        if (res.success && res.data.themes && res.data.themes.length > 0) {
          this.setData({
            themes: res.data.themes,
            isLoading: false
          });
        } else {
          console.log('API返回成功但没有主题数据，使用默认主题');
          this.setData({
            isLoading: false
          });
        }
      })
      .catch(err => {
        console.error('获取主题列表失败', err);
        this.setData({
          isLoading: false
        });

        // 使用默认主题数据，不显示错误提示
        console.log('API请求失败，使用默认主题数据');
      });
  },

  // 加载练习记录
  loadPracticeRecords: function (planId) {
    // 从API获取练习记录
    console.log('尝试从API获取练习记录，计划ID:', planId);

    // 调用API获取练习记录
    statisticsAdapter.getActivities({
      contentType: 'plan',
      contentId: planId,
      limit: 5
    })
      .then(res => {
        if (res.success && res.data && res.data.activities && res.data.activities.length > 0) {
          console.log('成功获取练习记录:', res.data.activities.length);

          // 转换API返回的数据为练习记录格式
          const records = res.data.activities.map(activity => ({
            id: activity.id,
            title: activity.title || '学习活动',
            date: activity.createdAt ? new Date(activity.createdAt).toLocaleDateString() : '未知日期',
            duration: activity.duration ? `${activity.duration}分钟` : '未知时长',
            score: activity.score ? activity.score.toString() : '无',
            completionRate: activity.completionRate || 0,
            color: activity.color || '#6366F1'
          }));

          this.setData({
            practiceRecords: records
          });
        } else {
          console.log('API返回成功但没有练习记录数据，使用空记录');
          this.setData({
            practiceRecords: []
          });
        }
      })
      .catch(err => {
        console.error('获取练习记录失败:', err);
        this.setData({
          practiceRecords: []
        });
      });
  },

  // 从学习计划构建模块
  buildModulesFromPlans: function (plans) {
    // 颜色列表
    const colors = ['#6366F1', '#8B5CF6', '#22c55e', '#F59E0B', '#06b6d4'];
    // 图标列表
    const icons = ['📚', '🎯', '🧠', '📝', '💡'];

    // 根据学习计划创建模块
    return plans.map((plan, index) => {
      // 生成描述
      let description = plan.description || '开始你的学习之旅';

      // 如果没有描述，根据主题生成默认描述
      if (!description && plan.themeName) {
        description = `探索${plan.themeName}的核心内容和实践方法`;
      }

      return {
        id: plan.id,
        title: plan.title,
        description: description,
        icon: icons[index % icons.length],
        color: colors[index % colors.length],
        progress: plan.progress || 0,
        planId: plan.id,
        x: 0 // 初始化滑动位置
      };
    });
  },

  // 从标签构建学习模块 (保留此函数以兼容旧代码)
  buildModulesFromTags: function (tags, plan) {
    // 颜色列表
    const colors = ['#6366F1', '#8B5CF6', '#22c55e', '#F59E0B', '#06b6d4'];
    // 图标列表
    const icons = ['📱', '🔍', '🌐', '📝', '💡'];

    // 根据标签创建模块
    return tags.map((tag, index) => {
      // 根据标签名称生成不同的描述
      let description = '';
      if (tag.name === '平台介绍') {
        description = '了解AIBUBB平台的核心理念和功能';
      } else if (tag.name === '泡泡功能') {
        description = '探索首页泡泡的互动方式和学习体验';
      } else if (tag.name === '广场探索') {
        description = '发现和分享学习内容，与他人互动';
      } else if (tag.name === '学习计划') {
        description = '创建和管理你的个性化学习路径';
      } else if (tag.name === '笔记技巧') {
        description = '记录和整理你的学习心得';
      } else {
        description = `了解${tag.name}的核心内容`;
      }

      return {
        id: tag.id,
        title: tag.name,
        description: description,
        icon: icons[index % icons.length],
        color: colors[index % colors.length],
        progress: 0, // 新手引导进度从0开始
        tagId: tag.id,
        x: 0 // 初始化滑动位置
      };
    });
  },

  // 不再使用模拟数据，所有数据都从API获取
  // 此函数保留但不再使用，避免代码引用错误
  loadMockData: function (showFailure = false) {
    console.log('模拟数据加载函数已废弃，所有数据应从API获取');

    // 显示加载失败状态
    this.setData({
      isLoading: false,
      loadingFailed: true
    });

    // 提示用户重试
    wx.showToast({
      title: '加载失败，请重试',
      icon: 'none',
      duration: 2000
    });
  },

  // 加载模版资源
  loadTemplateResources: function () {
    console.log('加载模版资源');

    // 模拟模版资源数据（后续可以替换为API调用）
    const mockTemplates = [
      {
        id: 't1',
        title: '每日学习计划',
        type: '学习计划',
        description: '适合日常学习使用的计划模版，包含学习目标、时间安排和复习提醒',
        color: '#6366F1'
      },
      {
        id: 't2',
        title: '考试备考计划',
        type: '学习计划',
        description: '针对考试准备的专用模版，包含重点内容标记和模拟测试安排',
        color: '#8B5CF6'
      },
      {
        id: 't3',
        title: '技能学习记录',
        type: '学习记录',
        description: '用于记录技能学习过程的模版，包含练习时间和进步记录',
        color: '#22c55e'
      }
    ];

    this.setData({
      templateResources: mockTemplates
    });
  },

  // 使用模版
  useTemplate: function (e) {
    const templateId = e.currentTarget.dataset.id;
    const template = this.data.templateResources.find(item => item.id === templateId);

    if (!template) return;

    wx.showToast({
      title: `即将使用"${template.title}"模版`,
      icon: 'none',
      duration: 2000
    });

    // 后续可以添加使用模版的具体逻辑
    // 例如跳转到创建计划页面并预填充模版内容
  },

  // 切换视图
  switchView: function (e) {
    const view = e.currentTarget.dataset.view;
    this.setData({
      activeView: view
    });
  },

  // 查看学习计划详情
  viewPlanDetail: function (e) {
    const planId = e.currentTarget.dataset.id;

    if (!planId) {
      wx.showToast({
        title: '计划ID不存在',
        icon: 'none'
      });
      return;
    }

    // 记录查看计划详情的活动
    statisticsAdapter.recordLearningActivity({
      activityType: 'view_plan_detail',
      contentType: 'plan',
      contentId: planId,
      details: {
        planName: this.data.currentPlan ? this.data.currentPlan.title : '未知计划'
      }
    }).catch(err => {
      console.error('记录学习活动失败', err);
    });

    // 检查是否是系统默认计划
    const isSystemDefault = this.data.currentPlan && this.data.currentPlan.isSystemDefault;

    // 跳转到计划详情页面
    wx.navigateTo({
      url: `/pages/plan-detail/index?id=${planId}&isSystemDefault=${isSystemDefault || false}`
    });
  },

  // 查看练习记录详情
  viewRecordDetail: function (e) {
    const recordId = e.currentTarget.dataset.id;
    const record = this.data.practiceRecords.find(item => item.id === recordId);

    if (!record) return;

    wx.showToast({
      title: `${record.title}详情即将上线`,
      icon: 'none',
      duration: 2000
    });
  },

  // 创建新学习计划
  createNewPlan: function () {
    // 跳转到创建学习计划页面
    wx.navigateTo({
      url: '/pages/create-plan/index'
    });
  },

  // 切换学习计划
  switchPlan: function (e) {
    const index = e.detail.value;
    const planId = this.data.learningPlans[index].id;

    // 激活学习计划
    learningPlanAPI.activatePlan(planId)
      .then(res => {
        if (res.success) {
          wx.showToast({
            title: '切换学习计划成功',
            icon: 'success'
          });

          // 重新加载数据
          this.loadData();
        } else {
          wx.showToast({
            title: '切换学习计划失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('切换学习计划失败', err);
        wx.showToast({
          title: '切换学习计划失败',
          icon: 'none'
        });
      });
  },

  // 加载系统默认学习计划
  loadSystemDefaultPlan: function () {
    console.log('加载系统默认学习计划');

    // 显示加载状态
    this.setData({
      isLoading: true,
      loadingFailed: false
    });

    // 清除可能存在的模拟数据
    this.setData({
      modules: [],
      currentPlan: null,
      planTags: []
    });

    this.fetchSystemDefaultPlan();
  },

  // 从API获取系统默认学习计划
  fetchSystemDefaultPlan: function () {
    // 显示调试信息
    wx.showToast({
      title: '加载平台指南',
      icon: 'none',
      duration: 2000
    });

    // 尝试从API获取系统默认学习计划
    learningPlanAPI.getSystemDefaultPlan()
      .then(res => {
        console.log('系统默认学习计划API响应:', res);

        if (res.success && res.data && res.data.plan) {
          const plan = res.data.plan;
          const tags = res.data.tags || [];

          // 尝试修复可能的编码问题
          try {
            // 检查标题是否包含乱码的特征（如 å…¥é—¨）
            if (plan.title && (plan.title.includes('å') || plan.title.includes('æ'))) {
              console.log('检测到标题可能存在编码问题，尝试修复');
              // 替换已知的乱码模式
              if (plan.title.includes('AIBUBB å…¥é—¨æŒ‡å—')) {
                plan.title = 'AIBUBB 入门指南';
              }
            }

            // 检查描述是否包含乱码的特征
            if (plan.description && (plan.description.includes('æ') || plan.description.includes('ç'))) {
              console.log('检测到描述可能存在编码问题，尝试修复');
              // 如果是默认的学习计划描述，可以直接替换为已知的正确内容
              if (plan.description.includes('AIBUBBï¼Œè¿™æ˜¯ä¸€ä¸ªå¸®åŠ©ä½')) {
                plan.description = '欢迎使用AIBUBB平台！今天我们将了解平台的基本功能和使用方法。';
              }
            }
          } catch (e) {
            console.error('修复编码问题失败:', e);
          }

          console.log('成功从API获取系统默认学习计划:', plan.title);

          // 打印计划详情，帮助调试
          console.log('系统默认学习计划详情:', JSON.stringify(plan));
          console.log('系统默认学习计划标签数量:', tags.length);

          // 构建学习模块（基于学习计划）
          const modules = this.buildModulesFromPlans([plan]);
          console.log('构建的模块:', JSON.stringify(modules));

          // 更新界面
          this.setData({
            currentPlan: plan,
            planTags: tags,
            modules,
            learningPlans: [plan],
            currentPlanIndex: 0,
            isLoading: false,
            loadingFailed: false
          });

          console.log('成功加载系统默认学习计划，模块数:', modules.length);

          // 显示成功信息
          wx.showToast({
            title: '加载引导成功',
            icon: 'success',
            duration: 2000
          });

          return;
        } else {
          // 详细记录API返回的数据结构
          console.error('API返回数据格式不符合预期:', JSON.stringify(res));

          // 显示加载失败状态
          this.setData({
            isLoading: false,
            loadingFailed: true
          });

          // 提示用户重试
          wx.showToast({
            title: '加载失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error('获取系统默认学习计划失败:', err);

        // 显示加载失败状态
        this.setData({
          isLoading: false,
          loadingFailed: true
        });

        // 提示用户重试
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 单独加载系统默认学习计划的标签
  loadSystemDefaultPlanTags: function (plan) {
    console.log('单独加载系统默认学习计划标签');

    // 显示调试信息
    wx.showToast({
      title: '加载系统标签',
      icon: 'none',
      duration: 2000
    });

    // 从API获取系统默认学习计划的标签
    tagAPI.getSystemDefaultPlanTags()
      .then(res => {
        if (res.success && res.data && res.data.tags && res.data.tags.length > 0) {
          console.log('成功从API获取系统默认学习计划标签:', res.data.tags.length);

          const tags = res.data.tags;

          // 构建学习模块（基于学习计划）
          const modules = this.buildModulesFromPlans([plan]);

          // 设置当前计划
          if (!this.data.currentPlan) {
            this.setData({
              currentPlan: plan
            });
          }

          this.setData({
            planTags: tags,
            modules,
            learningPlans: [plan],
            currentPlanIndex: 0,
            isLoading: false,
            loadingFailed: false
          });

          console.log('成功加载系统默认学习计划，模块数:', modules.length);

          // 显示成功信息
          wx.showToast({
            title: '加载成功',
            icon: 'success',
            duration: 2000
          });
        } else {
          console.log('API返回成功但没有标签数据，显示加载失败');
          this.setData({
            isLoading: false,
            loadingFailed: true
          });

          wx.showToast({
            title: '加载失败',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error('获取系统默认学习计划标签失败:', err);
        this.setData({
          isLoading: false,
          loadingFailed: true
        });

        wx.showToast({
          title: '加载失败',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 重试加载
  retryLoading: function () {
    this.loadData();
  },

  // 触摸开始位置
  startX: 0,

  // 处理触摸开始
  handleTouchStart: function (e) {
    // 记录开始触摸的位置
    this.startX = e.touches[0].clientX;
    console.log('触摸开始位置:', this.startX);
  },

  // 处理触摸移动
  handleTouchMove: function (e) {
    const { index } = e.currentTarget.dataset;
    const modules = this.data.modules;
    const currentX = e.touches[0].clientX;
    const moveX = currentX - this.startX;

    // 获取当前卡片的位置
    const currentPosition = modules[index].x || 0;

    if (moveX <= 0) {
      // 向左滑动（显示按钮）
      // 计算应该移动的距离，最大为-100（按钮区域宽度）
      const newX = Math.max(-100, moveX);

      // 更新模块位置
      modules[index].x = newX;

      this.setData({
        modules: modules
      });
    } else if (currentPosition < 0) {
      // 向右滑动（隐藏按钮），仅当卡片已经滑出时才处理
      // 计算新位置，不超过0
      const newX = Math.min(0, currentPosition + moveX);

      // 更新模块位置
      modules[index].x = newX;

      this.setData({
        modules: modules
      });

      // 重置起始位置，避免连续滑动时位置计算错误
      this.startX = currentX;
    }
  },

  // 处理触摸结束
  handleTouchEnd: function (e) {
    const { index } = e.currentTarget.dataset;
    const modules = this.data.modules;

    if (!modules[index].x) {
      modules[index].x = 0;
    }

    // 判断滑动结束时的位置
    if (modules[index].x > -30) {
      // 如果滑动距离很小或者是向右滑动，则恢复原位
      this.hideActionButtons(index);
    } else {
      // 否则完全展开
      this.showActionButtons(index);
    }

    // 添加震动反馈，提供触觉反馈
    if (modules[index].x === 0 || modules[index].x === -100) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  // 处理长按
  handleLongPress: function (e) {
    const { index } = e.currentTarget.dataset;

    // 长按时显示操作按钮
    this.showActionButtons(index);
  },

  // 显示操作按钮
  showActionButtons: function (index) {
    const modules = this.data.modules;
    const currentPosition = modules[index].x || 0;

    // 先重置所有卡片位置
    modules.forEach((module, i) => {
      if (i !== index) {
        module.x = 0;
      }
    });

    // 然后设置当前卡片位置
    modules[index].x = -100; // 设置为按钮区域宽度

    this.setData({
      modules: modules
    });

    // 添加震动反馈
    wx.vibrateShort({
      type: 'medium'
    });

    // 如果是首次显示按钮，显示提示信息
    if (currentPosition === 0) {
      wx.showToast({
        title: '右滑可返回',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 隐藏操作按钮
  hideActionButtons: function (index) {
    const modules = this.data.modules;

    // 设置卡片回到原位
    modules[index].x = 0;

    this.setData({
      modules: modules
    });
  },

  // 隐藏所有操作按钮
  hideAllActionButtons: function () {
    const modules = this.data.modules;
    let needUpdate = false;

    // 检查是否有卡片处于滑出状态
    modules.forEach((module, index) => {
      if (module.x && module.x < 0) {
        module.x = 0;
        needUpdate = true;
      }
    });

    // 只有在有卡片需要复位时才更新数据
    if (needUpdate) {
      this.setData({
        modules: modules
      });
    }
  },

  // 阻止事件冒泡
  preventBubble: function (e) {
    // 阻止事件冒泡，不做任何处理
  },

  // 编辑模块
  editModule: function (e) {
    const moduleId = e.currentTarget.dataset.id;
    const module = this.data.modules.find(item => item.id === moduleId);

    if (!module) return;

    wx.showToast({
      title: `编辑${module.title}模块`,
      icon: 'none',
      duration: 2000
    });

    // 这里可以添加跳转到编辑页面的逻辑
    // wx.navigateTo({
    //   url: `/pages/edit-module/index?id=${moduleId}`
    // });
  },

  // 删除模块
  deleteModule: function (e) {
    const moduleId = e.currentTarget.dataset.id;
    const module = this.data.modules.find(item => item.id === moduleId);

    if (!module) return;

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${module.title}"模块吗？`,
      success: res => {
        if (res.confirm) {
          // 用户点击确定，执行删除操作
          this.performDeleteModule(moduleId);
        } else {
          // 用户点击取消，恢复卡片位置
          const index = this.data.modules.findIndex(item => item.id === moduleId);
          if (index !== -1) {
            this.hideActionButtons(index);
          }
        }
      }
    });
  },

  // 执行删除模块操作
  performDeleteModule: function (moduleId) {
    // 这里应该调用删除模块的API
    // 暂时使用本地删除模拟
    const modules = this.data.modules.filter(item => item.id !== moduleId);

    this.setData({
      modules: modules
    });

    wx.showToast({
      title: '删除成功',
      icon: 'success',
      duration: 2000
    });
  },

  // 处理主题变化
  onThemeChange: function (newTheme) {
    console.log('学习页面接收到主题变化:', newTheme);
    this.setData({
      isDarkMode: newTheme === 'dark'
    });
    // 如果有需要根据主题更新的 UI 元素，在这里处理
    // 例如：如果模块卡片的背景色需要根据主题改变
    // this.updateModuleCardStyles(newTheme);
  }
});