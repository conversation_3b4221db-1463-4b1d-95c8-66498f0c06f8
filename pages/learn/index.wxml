<!--pages/learn/index.wxml-->
<view class="container" bindtap="hideAllActionButtons" catchtouchmove="preventBubble">
  <!-- 固定的顶部区域 -->
  <view class="fixed-header">
    <!-- 顶部操作区域 -->
    <view class="header-actions">
      <!-- 添加学习计划按钮 -->
      <view class="add-plan-button" catchtap="createNewPlan">
        <text class="add-icon">+</text>
      </view>

      <!-- 视图切换按钮 -->
      <view class="view-toggle">
        <view class="toggle-button {{activeView === 'plan' ? 'active' : ''}}" bindtap="switchView" data-view="plan">
          <text>学习计划</text>
        </view>
        <view class="toggle-button {{activeView === 'template' ? 'active' : ''}}" bindtap="switchView" data-view="template">
          <text>模版资源</text>
        </view>
        <view class="toggle-button {{activeView === 'record' ? 'active' : ''}}" bindtap="switchView" data-view="record">
          <text>练习记录</text>
        </view>
      </view>
    </view>


  </view>

  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:if="{{loadingFailed && !isLoading}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <text class="error-subtext">请确保后端服务器已启动并运行在9090端口</text>
    <button class="retry-button" bindtap="retryLoading">点击重试</button>
  </view>

  <!-- 可滚动的内容区域 -->
  <scroll-view scroll-y class="scrollable-content" wx:if="{{!isLoading && !loadingFailed}}">
    <view class="content-container">
      <!-- 学习计划视图 -->
      <view wx:if="{{activeView === 'plan'}}">
        <!-- 模块列表 -->
        <view class="module-list">
          <view class="module-item" wx:for="{{modules}}" wx:key="id">
            <view class="card-container"
                  bindtouchstart="handleTouchStart"
                  bindtouchmove="handleTouchMove"
                  bindtouchend="handleTouchEnd"
                  bindlongpress="handleLongPress"
                  catchtap="preventBubble"
                  data-index="{{index}}"
                  data-id="{{item.id}}">

              <!-- 卡片内容 -->
              <view class="module-card" style="background-color: {{item.color}}; transform: translateX({{item.x || 0}}rpx);">
                <view class="module-header">
                  <view class="module-icon">{{item.icon}}</view>
                  <text class="module-title">{{item.title}}</text>
                </view>
                <view class="module-info">
                  <text class="module-desc">{{item.description}}</text>
                </view>
                <view class="module-progress">
                  <view class="progress-bar">
                    <view class="progress-fill" style="width: {{item.progress}}%;"></view>
                  </view>
                  <text class="progress-text">{{item.progress}}%</text>
                </view>
                <view class="button-container">
                  <button class="module-button" catchtap="viewPlanDetail" data-id="{{item.id}}">计划详情</button>
                </view>
              </view>

              <!-- 操作按钮区域 -->
              <view class="action-buttons" catchtap="preventBubble">
                <view class="edit-button" catchtap="editModule" data-id="{{item.id}}">✏️</view>
                <view class="delete-button" catchtap="deleteModule" data-id="{{item.id}}">🗑️</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 暂无内容提示 -->
        <view class="empty-state" wx:if="{{modules.length === 0 && !isLoading}}">
          <view class="empty-icon">📚</view>
          <view class="empty-text">暂无学习内容</view>
          <view class="empty-subtext">请创建学习计划</view>
          <button class="create-button" bindtap="createNewPlan">创建学习计划</button>
        </view>
      </view>

      <!-- 模版资源视图 -->
      <view wx:if="{{activeView === 'template'}}">
        <!-- 模版资源列表 -->
        <view class="template-list" wx:if="{{templateResources.length > 0}}">
          <view class="template-item" wx:for="{{templateResources}}" wx:key="id">
            <view class="template-card">
              <view class="template-header" style="background-color: {{item.color}}">
                <text class="template-title">{{item.title}}</text>
                <text class="template-type">{{item.type}}</text>
              </view>
              <view class="template-content">
                <text class="template-desc">{{item.description}}</text>
                <button class="template-button" bindtap="useTemplate" data-id="{{item.id}}">使用模版</button>
              </view>
            </view>
          </view>
        </view>

        <!-- 暂无模版提示 -->
        <view class="empty-state" wx:if="{{templateResources.length === 0 && !isLoading}}">
          <view class="empty-icon">📋</view>
          <view class="empty-text">暂无模版资源</view>
          <view class="empty-subtext">敬请期待更多模版资源</view>
        </view>
      </view>

      <!-- 练习记录视图 -->
      <view wx:if="{{activeView === 'record'}}">
        <!-- 练习记录列表 -->
        <view class="record-list" wx:if="{{practiceRecords.length > 0}}">
          <view class="record-item" wx:for="{{practiceRecords}}" wx:key="id">
            <view class="record-card">
              <view class="record-header" style="background-color: {{item.color}}">
                <text class="record-title">{{item.title}}</text>
                <text class="record-date">{{item.date}}</text>
              </view>
              <view class="record-content">
                <view class="record-stats">
                  <view class="stat-item">
                    <text class="stat-value">{{item.duration}}</text>
                    <text class="stat-label">练习时长</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-value">{{item.score}}</text>
                    <text class="stat-label">得分</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-value">{{item.completionRate}}%</text>
                    <text class="stat-label">完成率</text>
                  </view>
                </view>
                <button class="record-button" bindtap="viewRecordDetail" data-id="{{item.id}}">查看详情</button>
              </view>
            </view>
          </view>
        </view>

        <!-- 暂无记录提示 -->
        <view class="empty-state" wx:if="{{practiceRecords.length === 0 && !isLoading}}">
          <view class="empty-icon">📝</view>
          <view class="empty-text">暂无练习记录</view>
          <view class="empty-subtext">完成练习后将在此显示</view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>