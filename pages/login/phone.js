// pages/login/phone.js
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  data: {
    phone: '',
    password: '',
    confirmPassword: '',
    nickname: '',
    isRegister: false,
    isBinding: false, // 是否为绑定手机号模式
    isLoading: false,
    errorMsg: ''
  },

  onLoad(options) {
    // 保存来源页面，用于登录成功后返回
    this.sourcePage = options.from || '';

    // 如果有参数指定是注册页面
    if (options.register === 'true') {
      this.setData({
        isRegister: true
      });
      wx.setNavigationBarTitle({
        title: '手机号注册'
      });
    } else {
      // 默认显示为登录页面
      this.setData({
        isBinding: false,
        isRegister: false
      });
      wx.setNavigationBarTitle({
        title: '手机号登录'
      });
    }
  },

  // 切换到注册模式
  switchToRegister() {
    this.setData({
      isRegister: true,
      errorMsg: ''
    });
    wx.setNavigationBarTitle({
      title: '手机号注册'
    });
  },

  // 切换到登录模式
  switchToLogin() {
    this.setData({
      isRegister: false,
      errorMsg: ''
    });
    wx.setNavigationBarTitle({
      title: '手机号登录'
    });
  },

  // 切换到微信登录
  switchToWechatLogin() {
    // 保存当前页面的来源信息，以便微信登录成功后可以返回到正确的页面
    const fromPage = this.sourcePage || '';

    // 显示加载中
    this.setData({
      isLoading: true,
      errorMsg: ''
    });

    // 使用认证服务进行微信登录
    authService.loginWithWechat({ getUserInfo: true })
      .then(result => {
        if (result.success) {
          wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 2000
          });

          // 延迟返回，让用户看到成功提示
          setTimeout(() => {
            // 根据来源页面决定返回方式
            if (this.sourcePage) {
              // 如果有来源页面，则重定向到该页面
              wx.redirectTo({
                url: `/pages/${this.sourcePage}/index`
              });
            } else {
              // 否则跳转到"我的"页面
              wx.switchTab({
                url: '/pages/profile/index'
              });
            }
          }, 1500);
        } else {
          this.setData({
            errorMsg: result.error || '登录失败，请稍后再试',
            isLoading: false
          });
        }
      })
      .catch(err => {
        console.error('微信登录失败:', err);
        this.setData({
          errorMsg: err.message || '登录失败，请稍后再试',
          isLoading: false
        });
      });
  },

  // 输入手机号
  inputPhone(e) {
    this.setData({
      phone: e.detail.value,
      errorMsg: ''
    });
  },

  // 输入密码
  inputPassword(e) {
    this.setData({
      password: e.detail.value,
      errorMsg: ''
    });
  },

  // 输入确认密码
  inputConfirmPassword(e) {
    this.setData({
      confirmPassword: e.detail.value,
      errorMsg: ''
    });
  },

  // 输入昵称
  inputNickname(e) {
    this.setData({
      nickname: e.detail.value,
      errorMsg: ''
    });
  },

  // 验证手机号格式
  validatePhone() {
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!this.data.phone) {
      this.setData({ errorMsg: '请输入手机号' });
      return false;
    }
    if (!phoneReg.test(this.data.phone)) {
      this.setData({ errorMsg: '手机号格式不正确' });
      return false;
    }
    return true;
  },

  // 验证密码
  validatePassword() {
    if (!this.data.password) {
      this.setData({ errorMsg: '请输入密码' });
      return false;
    }
    if (this.data.password.length < 6) {
      this.setData({ errorMsg: '密码长度不能少于6位' });
      return false;
    }
    return true;
  },

  // 验证注册表单
  validateRegisterForm() {
    if (!this.validatePhone()) return false;
    if (!this.validatePassword()) return false;

    if (!this.data.confirmPassword) {
      this.setData({ errorMsg: '请确认密码' });
      return false;
    }
    if (this.data.password !== this.data.confirmPassword) {
      this.setData({ errorMsg: '两次输入的密码不一致' });
      return false;
    }

    return true;
  },

  // 验证登录表单
  validateLoginForm() {
    if (!this.validatePhone()) return false;
    if (!this.validatePassword()) return false;
    return true;
  },


  // 提交表单
  submitForm() {
    console.log('提交表单被点击');

    // 防止重复提交
    if (this.data.isLoading) {
      console.log('表单正在提交中，忽略重复点击');
      return;
    }

    // 根据当前模式验证表单
    let isValid = false;

    if (this.data.isRegister) {
      isValid = this.validateRegisterForm();
    } else {
      isValid = this.validateLoginForm();
    }

    if (!isValid) {
      console.log('表单验证失败:', this.data.errorMsg);
      return;
    }

    console.log('表单验证成功，开始提交');
    this.setData({
      isLoading: true,
      errorMsg: ''
    });

    if (this.data.isRegister) {
      // 注册
      console.log('开始调用注册API:', this.data.phone);
      authService.registerWithPhone(this.data.phone, this.data.password, this.data.confirmPassword, this.data.nickname)
        .then(result => {
          console.log('注册API返回结果:', result);
          if (result.success) {
            wx.showToast({
              title: '注册成功',
              icon: 'success',
              duration: 2000
            });

            // 延迟返回，让用户看到成功提示
            setTimeout(() => {
              // 切换到登录模式
              this.setData({
                isRegister: false,
                phone: this.data.phone, // 保留已输入的手机号
                password: '', // 清空密码
                errorMsg: '',
                isLoading: false
              });

              wx.setNavigationBarTitle({
                title: '手机号登录'
              });

              // 提示用户登录
              wx.showToast({
                title: '请使用注册的手机号登录',
                icon: 'none',
                duration: 2000
              });
            }, 1500);
          } else {
            console.error('注册失败:', result);
            this.setData({
              errorMsg: result.error || '注册失败，请稍后再试',
              isLoading: false
            });
          }
        })
        .catch(err => {
          console.error('注册API调用异常:', err);
          // 显示更友好的错误信息
          let errorMessage = '注册失败，请稍后再试';

          if (err.message) {
            if (err.message.includes('timeout') || err.message.includes('网络')) {
              errorMessage = '网络连接失败，请检查网络设置后重试';
            } else if (err.message.includes('已注册') || err.message.includes('已存在')) {
              errorMessage = '该手机号已注册，请直接登录';
            } else {
              errorMessage = err.message;
            }
          }

          this.setData({
            errorMsg: errorMessage,
            isLoading: false
          });

          // 显示错误提示
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          });
        });
    } else {
      // 登录
      console.log('开始调用登录API:', this.data.phone);
      authService.loginWithPhone(this.data.phone, this.data.password)
        .then(result => {
          console.log('登录API返回结果:', result);
          if (result.success) {
            wx.showToast({
              title: '登录成功',
              icon: 'success',
              duration: 2000
            });

            // 延迟返回，让用户看到成功提示
            setTimeout(() => {
              // 根据来源页面决定返回方式
              if (this.sourcePage) {
                // 如果有来源页面，则重定向到该页面
                wx.redirectTo({
                  url: `/pages/${this.sourcePage}/index`
                });
              } else {
                // 否则跳转到"我的"页面
                wx.switchTab({
                  url: '/pages/profile/index'
                });
              }
            }, 1500);
          } else {
            console.error('登录失败:', result);
            this.setData({
              errorMsg: result.error || '登录失败，请稍后再试',
              isLoading: false
            });
          }
        })
        .catch(err => {
          console.error('登录API调用异常:', err);
          // 显示更友好的错误信息
          let errorMessage = '登录失败，请稍后再试';

          if (err.message) {
            if (err.message.includes('timeout') || err.message.includes('网络')) {
              errorMessage = '网络连接失败，请检查网络设置后重试';
            } else if (err.message.includes('手机号或密码错误')) {
              errorMessage = '手机号或密码错误，请重新输入';
            } else {
              errorMessage = err.message;
            }
          }

          this.setData({
            errorMsg: errorMessage,
            isLoading: false
          });

          // 显示错误提示
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          });
        });
    }
  }


});
