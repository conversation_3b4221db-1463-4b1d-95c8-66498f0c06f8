<!--pages/login/phone.wxml-->
<view class="container">
  <view class="form-container">
    <view class="form-group">
      <view class="input-label">手机号</view>
      <input
        class="input-field"
        type="number"
        placeholder="请输入手机号"
        value="{{phone}}"
        bindinput="inputPhone"
        maxlength="11"
      />
    </view>

    <view class="form-group">
      <view class="input-label">密码</view>
      <input
        class="input-field"
        type="password"
        placeholder="请输入密码"
        value="{{password}}"
        bindinput="inputPassword"
      />
    </view>

    <block wx:if="{{isRegister}}">
      <view class="form-group">
        <view class="input-label">确认密码</view>
        <input
          class="input-field"
          type="password"
          placeholder="请再次输入密码"
          value="{{confirmPassword}}"
          bindinput="inputConfirmPassword"
        />
      </view>

      <view class="form-group">
        <view class="input-label">昵称 (选填)</view>
        <input
          class="input-field"
          type="text"
          placeholder="请输入昵称"
          value="{{nickname}}"
          bindinput="inputNickname"
        />
      </view>
    </block>

    <view class="error-message" wx:if="{{errorMsg}}">{{errorMsg}}</view>

    <button
      class="submit-button {{isLoading ? 'loading' : ''}}"
      bindtap="submitForm"
      hover-class="button-hover"
      disabled="{{isLoading}}"
      form-type="submit"
    >
      <view class="button-content">
        <view class="loading-spinner" wx:if="{{isLoading}}"></view>
        <text>{{isRegister ? '注册' : '手机号登录'}}</text>
      </view>
    </button>

    <view class="switch-mode">
      <text wx:if="{{isRegister}}">已有账号？</text>
      <text wx:else>还没有账号？</text>
      <text
        class="switch-link"
        bindtap="{{isRegister ? 'switchToLogin' : 'switchToRegister'}}"
      >
        {{isRegister ? '去登录' : '去注册'}}
      </text>
    </view>

    <view class="divider">
      <view class="divider-line"></view>
      <text class="divider-text">或</text>
      <view class="divider-line"></view>
    </view>

    <button class="wechat-button" bindtap="switchToWechatLogin">
      <view class="wechat-icon">
        <image src="/assets/icons/wechat.png" mode="aspectFit"></image>
      </view>
      <text>微信一键登录</text>
    </button>
  </view>

  <view class="footer">
    <text class="footer-text">登录即表示您同意</text>
    <text class="footer-link">《用户协议》</text>
    <text class="footer-text">和</text>
    <text class="footer-link">《隐私政策》</text>
  </view>
</view>
