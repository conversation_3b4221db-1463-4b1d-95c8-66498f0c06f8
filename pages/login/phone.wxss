/* pages/login/phone.wxss */
page {
  background: linear-gradient(180deg, #e0f2ff 0%, #f0e6ff 100%);
  min-height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 30rpx;
  box-sizing: border-box;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.form-container {
  margin-top: 30rpx;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: 40rpx 30rpx;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.form-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.input-field {
  width: 100%;
  height: 90rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 30rpx;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input-field:focus {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 8rpx rgba(59, 130, 246, 0.2);
}

.error-message {
  color: #e74c3c;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.submit-button {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  border-radius: 45rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(59, 130, 246, 0.2);
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.submit-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: 45rpx;
}

.submit-button:active, .submit-button.button-hover {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(59, 130, 246, 0.15);
  opacity: 0.9;
}

.submit-button.loading {
  opacity: 0.8;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.switch-mode {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.switch-link {
  color: #3B82F6;
  margin-left: 10rpx;
}

.footer {
  margin-top: auto;
  padding: 40rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

.footer-link {
  color: #3B82F6;
  display: inline;
}

/* 分隔线 */
.divider {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
}

.divider-line {
  flex: 1;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
}

.divider-text {
  margin: 0 20rpx;
  color: #999;
  font-size: 26rpx;
}

/* 微信登录按钮 */
.wechat-button {
  width: 100%;
  height: 90rpx;
  background: #07C160;
  border-radius: 45rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(7, 193, 96, 0.2);
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wechat-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: 45rpx;
}

.wechat-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.15);
}

.wechat-icon {
  margin-right: 12rpx;
  display: flex;
  align-items: center;
}

.wechat-icon image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(1.05);
}
