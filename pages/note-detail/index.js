// pages/note-detail/index.js
// 笔记详情页面

// 导入API工具
const { noteAPI } = require('../../utils/api');
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    noteId: null,
    note: null,
    isLoading: true,
    loadingFailed: false,
    isLiked: false,
    isCollected: false,
    showShareModal: false,
    showCommentModal: false,
    commentText: '',
    comments: [],
    isSubmittingComment: false,
    
    // 用户信息
    currentUser: null,
    isOwner: false,
    
    // 相关笔记
    relatedNotes: [],
    
    // 主题模式
    isDarkMode: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const noteId = options.id;
    
    if (!noteId) {
      this.setData({
        isLoading: false,
        loadingFailed: true
      });
      
      wx.showToast({
        title: '缺少笔记ID',
        icon: 'none'
      });
      
      return;
    }

    this.setData({ noteId });
    
    // 获取主题模式
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    const isDarkMode = themeMode === 'dark' || 
                      (themeMode === 'system' && wx.getSystemInfoSync().theme === 'dark');
    
    this.setData({ isDarkMode });
    
    // 加载笔记详情
    this.loadNoteDetail();
    
    // 加载当前用户信息
    this.loadCurrentUser();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示时刷新数据
    if (this.data.noteId) {
      this.loadNoteDetail();
    }
  },

  /**
   * 加载当前用户信息
   */
  async loadCurrentUser() {
    try {
      const isLoggedIn = await authService.isLoggedIn();
      if (isLoggedIn) {
        const userInfo = await authService.getCurrentUser();
        this.setData({ currentUser: userInfo });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  /**
   * 加载笔记详情
   */
  async loadNoteDetail() {
    this.setData({ isLoading: true, loadingFailed: false });

    try {
      // 获取API客户端
      const app = getApp();
      const api = app.globalData.api;

      if (!api) {
        throw new Error('API客户端未初始化');
      }

      // 调用API获取笔记详情
      const response = await api.note.getNoteById(this.data.noteId);

      if (response.success && response.data) {
        const note = response.data;
        
        // 检查是否为笔记作者
        const isOwner = this.data.currentUser && 
                       this.data.currentUser.id === note.userId;

        this.setData({
          note,
          isOwner,
          isLiked: note.isLiked || false,
          isCollected: note.isCollected || false,
          isLoading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: note.title || '笔记详情'
        });

        // 加载评论
        this.loadComments();
        
        // 加载相关笔记
        this.loadRelatedNotes();

      } else {
        throw new Error(response.error || '获取笔记详情失败');
      }
    } catch (error) {
      console.error('加载笔记详情失败:', error);
      
      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载评论列表
   */
  async loadComments() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) return;

      const response = await api.note.getComments(this.data.noteId);
      
      if (response.success && response.data) {
        this.setData({
          comments: response.data.comments || []
        });
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    }
  },

  /**
   * 加载相关笔记
   */
  async loadRelatedNotes() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api || !this.data.note) return;

      // 根据标签获取相关笔记
      const tagIds = this.data.note.tags ? this.data.note.tags.map(tag => tag.id) : [];
      
      if (tagIds.length === 0) return;

      const response = await api.note.getRelatedNotes(this.data.noteId, {
        tagIds,
        limit: 6
      });
      
      if (response.success && response.data) {
        this.setData({
          relatedNotes: response.data.notes || []
        });
      }
    } catch (error) {
      console.error('加载相关笔记失败:', error);
    }
  },

  /**
   * 点赞/取消点赞
   */
  async toggleLike() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const isLiked = this.data.isLiked;
    const note = this.data.note;

    // 乐观更新UI
    this.setData({
      isLiked: !isLiked,
      'note.likeCount': note.likeCount + (isLiked ? -1 : 1)
    });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = isLiked 
        ? await api.note.unlikeNote(this.data.noteId)
        : await api.note.likeNote(this.data.noteId);

      if (!response.success) {
        // 如果失败，回滚UI更新
        this.setData({
          isLiked: isLiked,
          'note.likeCount': note.likeCount
        });
        
        throw new Error(response.error || '操作失败');
      }

      // 轻微振动反馈
      wx.vibrateShort({ type: 'light' });

    } catch (error) {
      console.error('点赞操作失败:', error);
      
      // 回滚UI更新
      this.setData({
        isLiked: isLiked,
        'note.likeCount': note.likeCount
      });

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 收藏/取消收藏
   */
  async toggleCollect() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const isCollected = this.data.isCollected;

    // 乐观更新UI
    this.setData({
      isCollected: !isCollected
    });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = isCollected 
        ? await api.note.uncollectNote(this.data.noteId)
        : await api.note.collectNote(this.data.noteId);

      if (!response.success) {
        // 如果失败，回滚UI更新
        this.setData({
          isCollected: isCollected
        });
        
        throw new Error(response.error || '操作失败');
      }

      wx.showToast({
        title: isCollected ? '已取消收藏' : '已收藏',
        icon: 'success'
      });

    } catch (error) {
      console.error('收藏操作失败:', error);
      
      // 回滚UI更新
      this.setData({
        isCollected: isCollected
      });

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示评论弹窗
   */
  showCommentModal() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCommentModal: true,
      commentText: ''
    });
  },

  /**
   * 隐藏评论弹窗
   */
  hideCommentModal() {
    this.setData({
      showCommentModal: false,
      commentText: ''
    });
  },

  /**
   * 评论输入
   */
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    });
  },

  /**
   * 提交评论
   */
  async submitComment() {
    const commentText = this.data.commentText.trim();
    
    if (!commentText) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmittingComment: true });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.note.addComment(this.data.noteId, {
        content: commentText
      });

      if (response.success) {
        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });

        // 隐藏弹窗
        this.hideCommentModal();
        
        // 重新加载评论
        this.loadComments();
        
        // 更新评论数量
        const note = this.data.note;
        this.setData({
          'note.commentCount': (note.commentCount || 0) + 1
        });

      } else {
        throw new Error(response.error || '评论失败');
      }
    } catch (error) {
      console.error('提交评论失败:', error);
      
      wx.showToast({
        title: error.message || '评论失败',
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmittingComment: false });
    }
  },

  /**
   * 编辑笔记
   */
  editNote() {
    if (!this.data.isOwner) {
      wx.showToast({
        title: '只能编辑自己的笔记',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/note-edit/index?id=${this.data.noteId}`
    });
  },

  /**
   * 删除笔记
   */
  deleteNote() {
    if (!this.data.isOwner) {
      wx.showToast({
        title: '只能删除自己的笔记',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这篇笔记吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#e64340',
      success: async (res) => {
        if (res.confirm) {
          await this.performDelete();
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async performDelete() {
    wx.showLoading({ title: '删除中...' });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.note.deleteNote(this.data.noteId);

      if (response.success) {
        wx.hideLoading();
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('删除笔记失败:', error);
      
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示分享弹窗
   */
  showShareModal() {
    this.setData({ showShareModal: true });
  },

  /**
   * 隐藏分享弹窗
   */
  hideShareModal() {
    this.setData({ showShareModal: false });
  },

  /**
   * 查看相关笔记
   */
  viewRelatedNote(e) {
    const noteId = e.currentTarget.dataset.id;
    
    wx.navigateTo({
      url: `/pages/note-detail/index?id=${noteId}`
    });
  },

  /**
   * 重试加载
   */
  retryLoading() {
    this.loadNoteDetail();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const note = this.data.note;
    
    return {
      title: note ? note.title : '分享笔记',
      path: `/pages/note-detail/index?id=${this.data.noteId}`,
      imageUrl: note && note.imageUrl ? note.imageUrl : ''
    };
  }
});
