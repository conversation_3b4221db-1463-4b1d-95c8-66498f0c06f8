<!--pages/note-detail/index.wxml-->
<view class="container {{isDarkMode ? 'dark-mode' : ''}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:if="{{loadingFailed}}">
    <view class="error-icon">⚠️</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="retryLoading">重试</button>
  </view>

  <!-- 笔记内容 -->
  <view class="note-content" wx:if="{{!isLoading && !loadingFailed && note}}">
    <!-- 笔记头部 -->
    <view class="note-header">
      <view class="note-title">{{note.title}}</view>
      <view class="note-meta">
        <view class="author-info">
          <image class="author-avatar" src="{{note.userAvatar}}" mode="aspectFill"></image>
          <view class="author-details">
            <text class="author-name">{{note.userName}}</text>
            <text class="publish-time">{{note.createdAt}}</text>
          </view>
        </view>
        <view class="note-stats">
          <text class="stat-item">{{note.viewCount || 0}} 阅读</text>
          <text class="stat-item">{{note.likeCount || 0}} 点赞</text>
          <text class="stat-item">{{note.commentCount || 0}} 评论</text>
        </view>
      </view>
    </view>

    <!-- 笔记标签 -->
    <view class="note-tags" wx:if="{{note.tags && note.tags.length > 0}}">
      <view class="tag-item" wx:for="{{note.tags}}" wx:key="id">
        {{item.name}}
      </view>
    </view>

    <!-- 笔记正文 -->
    <view class="note-body">
      <!-- 笔记图片 -->
      <view class="note-images" wx:if="{{note.imageUrls && note.imageUrls.length > 0}}">
        <image 
          class="note-image" 
          wx:for="{{note.imageUrls}}" 
          wx:key="*this"
          src="{{item}}" 
          mode="widthFix"
          lazy-load="true"
          bindtap="previewImage"
          data-url="{{item}}"
          data-urls="{{note.imageUrls}}">
        </image>
      </view>

      <!-- 笔记文本内容 -->
      <view class="note-text">
        <text class="content-text">{{note.content}}</text>
      </view>
    </view>

    <!-- 操作按钮栏 -->
    <view class="action-bar">
      <view class="action-left">
        <view class="action-button {{isLiked ? 'liked' : ''}}" bindtap="toggleLike">
          <text class="action-icon">{{isLiked ? '❤️' : '🤍'}}</text>
          <text class="action-text">{{isLiked ? '已赞' : '点赞'}}</text>
        </view>
        <view class="action-button" bindtap="showCommentModal">
          <text class="action-icon">💬</text>
          <text class="action-text">评论</text>
        </view>
        <view class="action-button {{isCollected ? 'collected' : ''}}" bindtap="toggleCollect">
          <text class="action-icon">{{isCollected ? '⭐' : '☆'}}</text>
          <text class="action-text">{{isCollected ? '已收藏' : '收藏'}}</text>
        </view>
      </view>
      <view class="action-right">
        <view class="action-button" bindtap="showShareModal">
          <text class="action-icon">📤</text>
          <text class="action-text">分享</text>
        </view>
        <view class="action-button" wx:if="{{isOwner}}" bindtap="editNote">
          <text class="action-icon">✏️</text>
          <text class="action-text">编辑</text>
        </view>
        <view class="action-button danger" wx:if="{{isOwner}}" bindtap="deleteNote">
          <text class="action-icon">🗑️</text>
          <text class="action-text">删除</text>
        </view>
      </view>
    </view>

    <!-- 评论列表 -->
    <view class="comments-section" wx:if="{{comments.length > 0}}">
      <view class="section-title">评论 ({{comments.length}})</view>
      <view class="comment-list">
        <view class="comment-item" wx:for="{{comments}}" wx:key="id">
          <image class="comment-avatar" src="{{item.userAvatar}}" mode="aspectFill"></image>
          <view class="comment-content">
            <view class="comment-header">
              <text class="comment-author">{{item.userName}}</text>
              <text class="comment-time">{{item.createdAt}}</text>
            </view>
            <view class="comment-text">{{item.content}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 相关笔记 -->
    <view class="related-section" wx:if="{{relatedNotes.length > 0}}">
      <view class="section-title">相关笔记</view>
      <view class="related-list">
        <view 
          class="related-item" 
          wx:for="{{relatedNotes}}" 
          wx:key="id"
          bindtap="viewRelatedNote"
          data-id="{{item.id}}">
          <image class="related-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
          <view class="related-info">
            <text class="related-title">{{item.title}}</text>
            <text class="related-author">{{item.userName}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 评论弹窗 -->
  <view class="modal-overlay" wx:if="{{showCommentModal}}" bindtap="hideCommentModal">
    <view class="comment-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">添加评论</text>
        <text class="modal-close" bindtap="hideCommentModal">✕</text>
      </view>
      <view class="modal-body">
        <textarea 
          class="comment-input"
          placeholder="写下你的想法..."
          value="{{commentText}}"
          bindinput="onCommentInput"
          maxlength="500"
          auto-height>
        </textarea>
        <view class="input-counter">{{commentText.length}}/500</view>
      </view>
      <view class="modal-footer">
        <button class="cancel-button" bindtap="hideCommentModal">取消</button>
        <button 
          class="submit-button {{commentText.trim() ? 'active' : ''}}" 
          bindtap="submitComment"
          disabled="{{!commentText.trim() || isSubmittingComment}}">
          {{isSubmittingComment ? '发布中...' : '发布'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 分享弹窗 -->
  <view class="modal-overlay" wx:if="{{showShareModal}}" bindtap="hideShareModal">
    <view class="share-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">分享笔记</text>
        <text class="modal-close" bindtap="hideShareModal">✕</text>
      </view>
      <view class="share-options">
        <button class="share-option" open-type="share">
          <text class="share-icon">📱</text>
          <text class="share-text">分享给朋友</text>
        </button>
        <button class="share-option" bindtap="copyLink">
          <text class="share-icon">🔗</text>
          <text class="share-text">复制链接</text>
        </button>
      </view>
    </view>
  </view>
</view>
