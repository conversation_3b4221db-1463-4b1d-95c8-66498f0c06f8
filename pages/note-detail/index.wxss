/* pages/note-detail/index.wxss */

.container {
  min-height: 100vh;
  background-color: var(--bg-color-primary, #f8f9fa);
  color: var(--text-color-primary, #333);
}

.dark-mode {
  background-color: var(--bg-color-primary-dark, #1a1a1a);
  color: var(--text-color-primary-dark, #fff);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid var(--primary-color, #3775f5);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: var(--text-color-secondary, #666);
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: var(--text-color-secondary, #666);
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.retry-button {
  background-color: var(--primary-color, #3775f5);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 笔记内容 */
.note-content {
  padding: 0 32rpx 40rpx;
}

/* 笔记头部 */
.note-header {
  padding: 40rpx 0 32rpx;
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
}

.note-title {
  font-size: 44rpx;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 24rpx;
  color: var(--text-color-primary, #333);
}

.note-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
  margin-bottom: 4rpx;
}

.publish-time {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

.note-stats {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

/* 标签 */
.note-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin: 32rpx 0;
}

.tag-item {
  background-color: var(--tag-bg-color, #f0f2f5);
  color: var(--primary-color, #3775f5);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid var(--primary-color, #3775f5);
}

/* 笔记正文 */
.note-body {
  margin: 32rpx 0;
}

.note-images {
  margin-bottom: 32rpx;
}

.note-image {
  width: 100%;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.note-text {
  line-height: 1.8;
}

.content-text {
  font-size: 32rpx;
  color: var(--text-color-primary, #333);
  white-space: pre-wrap;
}

/* 操作按钮栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-top: 1rpx solid var(--border-color, #e0e0e0);
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
  margin: 32rpx 0;
}

.action-left,
.action-right {
  display: flex;
  gap: 32rpx;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.action-button:active {
  background-color: var(--bg-color-secondary, #f0f0f0);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

.action-button.liked .action-text,
.action-button.collected .action-text {
  color: var(--primary-color, #3775f5);
}

.action-button.danger .action-text {
  color: var(--danger-color, #e64340);
}

/* 评论区域 */
.comments-section,
.related-section {
  margin-top: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  color: var(--text-color-primary, #333);
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comment-item {
  display: flex;
  gap: 16rpx;
}

.comment-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.comment-author {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
}

.comment-time {
  font-size: 22rpx;
  color: var(--text-color-secondary, #666);
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color-primary, #333);
}

/* 相关笔记 */
.related-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.related-item {
  display: flex;
  gap: 16rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  background-color: var(--bg-color-secondary, #fff);
  border: 1rpx solid var(--border-color, #e0e0e0);
}

.related-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.related-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8rpx;
}

.related-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-author {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.comment-modal,
.share-modal {
  background-color: var(--bg-color-secondary, #fff);
  border-radius: 16rpx;
  margin: 32rpx;
  max-height: 80vh;
  width: calc(100% - 64rpx);
  max-width: 600rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
}

.modal-close {
  font-size: 32rpx;
  color: var(--text-color-secondary, #666);
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.6;
  resize: none;
}

.input-counter {
  text-align: right;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid var(--border-color, #e0e0e0);
}

.cancel-button,
.submit-button {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-button {
  background-color: var(--bg-color-secondary, #f0f0f0);
  color: var(--text-color-secondary, #666);
}

.submit-button {
  background-color: var(--primary-color, #3775f5);
  color: white;
}

.submit-button.active {
  opacity: 1;
}

.submit-button:not(.active) {
  opacity: 0.5;
}

/* 分享选项 */
.share-options {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.share-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
  border-radius: 8rpx;
  background-color: transparent;
  font-size: 28rpx;
}

.share-icon {
  font-size: 32rpx;
}

.share-text {
  color: var(--text-color-primary, #333);
}
