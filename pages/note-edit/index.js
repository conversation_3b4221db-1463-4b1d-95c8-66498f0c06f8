// pages/note-edit/index.js
// 笔记创建/编辑页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 笔记ID，编辑模式下有值，创建模式下为空
    noteId: '',
    // 笔记标题
    title: '',
    // 笔记内容
    content: '',
    // 关联标签ID
    tagId: '',
    // 关联标签列表
    tags: [],
    // 是否公开
    isPublic: true,
    // 是否为编辑模式
    isEditMode: false,
    // 是否正在加载
    isLoading: false,
    // 是否正在提交
    isSubmitting: false,
    // 是否显示标签选择器
    showTagSelector: false,
    // 可选标签列表
    availableTags: [],
    // 是否正在加载标签
    isLoadingTags: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '创建笔记'
    });

    // 检查是否有笔记ID参数（编辑模式）
    if (options.id) {
      this.setData({
        noteId: options.id,
        isEditMode: true
      });

      // 设置导航栏标题为编辑模式
      wx.setNavigationBarTitle({
        title: '编辑笔记'
      });

      // 加载笔记数据
      this.loadNoteData(options.id);
    }

    // 检查是否有标签ID参数
    if (options.tagId) {
      this.setData({
        tagId: options.tagId
      });
    }

    // 加载可用标签
    this.loadAvailableTags();
  },

  /**
   * 加载笔记数据
   * @param {string} noteId 笔记ID
   */
  loadNoteData: function (noteId) {
    this.setData({ isLoading: true });

    // 获取API客户端
    const app = getApp();
    const api = app.globalData.api;

    if (!api) {
      console.error('API客户端未初始化');
      this.setData({ isLoading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      return;
    }

    // 调用API获取笔记详情
    api.note.getNote(noteId)
      .then(res => {
        console.log('笔记数据:', res);
        if (res.success && res.data) {
          const note = res.data;
          this.setData({
            title: note.title || '',
            content: note.content || '',
            isPublic: note.isPublic || false,
            tags: note.tags || [],
            isLoading: false
          });
        } else {
          throw new Error('获取笔记数据失败');
        }
      })
      .catch(err => {
        console.error('加载笔记数据失败:', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 加载可用标签
   */
  loadAvailableTags: function () {
    this.setData({ isLoadingTags: true });

    // 获取API客户端
    const app = getApp();
    const api = app.globalData.api;

    if (!api) {
      console.error('API客户端未初始化');
      this.setData({ isLoadingTags: false });
      return;
    }

    // 调用API获取标签列表
    api.tag.getTags()
      .then(res => {
        console.log('标签数据:', res);
        if (res.success && res.data && res.data.tags) {
          this.setData({
            availableTags: res.data.tags,
            isLoadingTags: false
          });

          // 如果有tagId参数，设置为已选标签
          if (this.data.tagId) {
            const selectedTag = res.data.tags.find(tag => tag.id === this.data.tagId);
            if (selectedTag) {
              this.setData({
                tags: [selectedTag]
              });
            }
          }
        } else {
          throw new Error('获取标签数据失败');
        }
      })
      .catch(err => {
        console.error('加载标签数据失败:', err);
        this.setData({ isLoadingTags: false });
      });
  },

  /**
   * 处理标题输入
   */
  handleTitleInput: function (e) {
    this.setData({
      title: e.detail.value
    });
  },

  /**
   * 处理内容输入
   */
  handleContentInput: function (e) {
    this.setData({
      content: e.detail.value
    });
  },

  /**
   * 切换是否公开
   */
  togglePublic: function () {
    this.setData({
      isPublic: !this.data.isPublic
    });
  },

  /**
   * 显示标签选择器
   */
  showTagSelector: function () {
    this.setData({
      showTagSelector: true
    });
  },

  /**
   * 隐藏标签选择器
   */
  hideTagSelector: function () {
    this.setData({
      showTagSelector: false
    });
  },

  /**
   * 选择标签
   */
  selectTag: function (e) {
    const tagId = e.currentTarget.dataset.id;
    const tag = this.data.availableTags.find(t => t.id === tagId);
    
    if (tag) {
      // 检查标签是否已选择
      const isSelected = this.data.tags.some(t => t.id === tagId);
      
      if (isSelected) {
        // 如果已选择，则移除
        this.setData({
          tags: this.data.tags.filter(t => t.id !== tagId)
        });
      } else {
        // 如果未选择，则添加
        this.setData({
          tags: [...this.data.tags, tag]
        });
      }
    }
  },

  /**
   * 移除标签
   */
  removeTag: function (e) {
    const tagId = e.currentTarget.dataset.id;
    this.setData({
      tags: this.data.tags.filter(t => t.id !== tagId)
    });
  },

  /**
   * 保存笔记
   */
  saveNote: function () {
    // 验证表单
    if (!this.data.title.trim()) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      });
      return;
    }

    if (!this.data.content.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmitting: true });

    // 获取API客户端
    const app = getApp();
    const api = app.globalData.api;

    if (!api) {
      console.error('API客户端未初始化');
      this.setData({ isSubmitting: false });
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
      return;
    }

    // 构建笔记数据
    const noteData = {
      title: this.data.title,
      content: this.data.content,
      isPublic: this.data.isPublic,
      tagIds: this.data.tags.map(tag => tag.id)
    };

    // 根据模式调用不同的API
    const apiPromise = this.data.isEditMode
      ? api.note.updateNote(this.data.noteId, noteData)
      : api.note.createNote(noteData);

    apiPromise
      .then(res => {
        console.log('保存笔记结果:', res);
        if (res.success) {
          wx.showToast({
            title: this.data.isEditMode ? '更新成功' : '创建成功',
            icon: 'success'
          });

          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          throw new Error('保存笔记失败');
        }
      })
      .catch(err => {
        console.error('保存笔记失败:', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ isSubmitting: false });
      });
  },

  /**
   * 取消编辑
   */
  cancelEdit: function () {
    wx.showModal({
      title: '确认取消',
      content: '确定要放弃当前编辑吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  }
});
