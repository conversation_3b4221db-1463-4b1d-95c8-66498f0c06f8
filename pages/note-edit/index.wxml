<!-- pages/note-edit/index.wxml -->
<view class="note-edit-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 编辑表单 -->
  <view class="edit-form" wx:else>
    <!-- 标题输入 -->
    <view class="form-item">
      <input 
        class="title-input" 
        placeholder="请输入标题" 
        value="{{title}}" 
        bindinput="handleTitleInput"
        maxlength="100"
      />
    </view>

    <!-- 内容输入 -->
    <view class="form-item">
      <textarea 
        class="content-input" 
        placeholder="请输入内容" 
        value="{{content}}" 
        bindinput="handleContentInput"
        maxlength="5000"
        auto-height
      />
    </view>

    <!-- 标签选择 -->
    <view class="form-item">
      <view class="section-title">标签</view>
      <view class="tags-container">
        <view 
          class="tag-item" 
          wx:for="{{tags}}" 
          wx:key="id"
        >
          <text class="tag-name">{{item.name}}</text>
          <text class="tag-remove" data-id="{{item.id}}" catchtap="removeTag">×</text>
        </view>
        <view class="add-tag" bindtap="showTagSelector">
          <text class="add-icon">+</text>
          <text class="add-text">添加标签</text>
        </view>
      </view>
    </view>

    <!-- 公开设置 -->
    <view class="form-item">
      <view class="section-title">公开设置</view>
      <view class="public-switch" bindtap="togglePublic">
        <text class="switch-label">是否公开</text>
        <view class="switch-container {{isPublic ? 'active' : ''}}">
          <view class="switch-handle"></view>
        </view>
      </view>
      <text class="public-hint">{{isPublic ? '公开的笔记将显示在广场中' : '私密笔记仅自己可见'}}</text>
    </view>

    <!-- 底部按钮 -->
    <view class="form-actions">
      <button 
        class="cancel-btn" 
        bindtap="cancelEdit"
        hover-class="btn-hover"
      >取消</button>
      <button 
        class="save-btn {{isSubmitting ? 'disabled' : ''}}" 
        bindtap="saveNote"
        disabled="{{isSubmitting}}"
        hover-class="btn-hover"
      >{{isSubmitting ? '保存中...' : '保存'}}</button>
    </view>
  </view>

  <!-- 标签选择器弹窗 -->
  <view class="tag-selector-overlay" wx:if="{{showTagSelector}}" bindtap="hideTagSelector">
    <view class="tag-selector-container" catchtap>
      <view class="selector-header">
        <text class="selector-title">选择标签</text>
        <text class="selector-close" bindtap="hideTagSelector">×</text>
      </view>
      
      <view class="selector-content">
        <view class="loading-tags" wx:if="{{isLoadingTags}}">
          <view class="loading-spinner small"></view>
          <text class="loading-text">加载标签中...</text>
        </view>
        
        <view class="tag-list" wx:else>
          <view 
            class="selector-tag-item {{tags.some(t => t.id === item.id) ? 'selected' : ''}}" 
            wx:for="{{availableTags}}" 
            wx:key="id"
            data-id="{{item.id}}"
            bindtap="selectTag"
          >
            <text class="selector-tag-name">{{item.name}}</text>
            <text class="selector-tag-check" wx:if="{{tags.some(t => t.id === item.id)}}">✓</text>
          </view>
          
          <view class="empty-tags" wx:if="{{availableTags.length === 0}}">
            <text>暂无可用标签</text>
          </view>
        </view>
      </view>
      
      <view class="selector-footer">
        <button class="confirm-btn" bindtap="hideTagSelector">确定</button>
      </view>
    </view>
  </view>
</view>
