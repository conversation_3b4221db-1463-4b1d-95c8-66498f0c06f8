/* pages/note-edit/index.wxss */
.note-edit-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(74, 144, 226, 0.1);
  border-top-color: #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 编辑表单 */
.edit-form {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

/* 标题输入 */
.title-input {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 0 10rpx;
}

/* 内容输入 */
.content-input {
  width: 100%;
  min-height: 400rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  padding: 10rpx;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.tag-name {
  font-size: 24rpx;
  color: #4a90e2;
  margin-right: 10rpx;
}

.tag-remove {
  font-size: 28rpx;
  color: #4a90e2;
  font-weight: bold;
}

.add-tag {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.add-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 6rpx;
}

.add-text {
  font-size: 24rpx;
  color: #999;
}

/* 公开设置 */
.public-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

.switch-container {
  width: 80rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  border-radius: 20rpx;
  position: relative;
  transition: background-color 0.3s;
}

.switch-container.active {
  background-color: #4a90e2;
}

.switch-handle {
  width: 36rpx;
  height: 36rpx;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: transform 0.3s;
}

.switch-container.active .switch-handle {
  transform: translateX(40rpx);
}

.public-hint {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮 */
.form-actions {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn, .save-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.save-btn {
  background-color: #4a90e2;
  color: #fff;
}

.save-btn.disabled {
  background-color: #a0c8f0;
}

.btn-hover {
  opacity: 0.8;
}

/* 标签选择器 */
.tag-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tag-selector-container {
  width: 80%;
  max-height: 70vh;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.selector-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.selector-close {
  font-size: 40rpx;
  color: #999;
}

.selector-content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.selector-tag-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  min-width: 120rpx;
}

.selector-tag-item.selected {
  background-color: rgba(74, 144, 226, 0.1);
}

.selector-tag-name {
  font-size: 24rpx;
  color: #666;
}

.selector-tag-item.selected .selector-tag-name {
  color: #4a90e2;
}

.selector-tag-check {
  font-size: 24rpx;
  color: #4a90e2;
  margin-left: 10rpx;
}

.empty-tags {
  width: 100%;
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.loading-tags {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.selector-footer {
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #4a90e2;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}
