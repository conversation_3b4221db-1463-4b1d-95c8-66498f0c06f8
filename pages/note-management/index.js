// pages/note-management/index.js
Page({
  data: {
    activeTab: 'notes', // 'notes' 或 'trash'
    userId: '', // 当前用户ID
    isLoading: false
  },

  onLoad() {
    // 获取当前用户ID
    const app = getApp();
    const userId = app.globalData.userId || '';

    this.setData({
      userId,
      isLoading: true
    });

    // 检查用户登录状态
    if (!userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });

      // 跳转到登录页
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/index'
        });
      }, 2000);

      return;
    }

    this.setData({ isLoading: false });
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  // 处理笔记查看
  handleNoteView(e) {
    const noteId = e.detail.id;
    wx.navigateTo({
      url: `/pages/note-detail/index?id=${noteId}`
    });
  },

  // 处理笔记编辑
  handleNoteEdit(e) {
    const noteId = e.detail.id;
    wx.navigateTo({
      url: `/pages/note-edit/index?id=${noteId}`
    });
  },

  // 处理笔记创建
  handleNoteCreate() {
    wx.navigateTo({
      url: '/pages/note-edit/index'
    });
  },

  // 处理笔记删除
  handleNoteDelete(e) {
    // 可以在这里执行一些额外的操作，例如更新其他页面的数据
    wx.showToast({
      title: '删除成功',
      icon: 'success'
    });
  },

  // 处理笔记恢复
  handleNoteRestore(e) {
    // 可以在这里执行一些额外的操作，例如更新其他页面的数据
    wx.showToast({
      title: '恢复成功',
      icon: 'success'
    });

    // 刷新笔记列表
    this.selectComponent('#noteManager').refreshList();
  },

  // 处理批量删除
  handleBatchDelete(e) {
    // 可以在这里执行一些额外的操作，例如更新其他页面的数据
    wx.showToast({
      title: `已删除${e.detail.ids.length}项`,
      icon: 'success'
    });
  },

  // 处理批量恢复
  handleBatchRestore(e) {
    // 可以在这里执行一些额外的操作，例如更新其他页面的数据
    wx.showToast({
      title: `已恢复${e.detail.ids.length}项`,
      icon: 'success'
    });

    // 刷新笔记列表
    this.selectComponent('#noteManager').refreshList();
  }
});
