<!-- pages/note-management/index.wxml -->
<view class="note-management-container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">笔记管理</view>
  </view>

  <!-- 标签页 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === 'notes' ? 'active' : ''}}" 
      data-tab="notes" 
      bindtap="switchTab"
    >我的笔记</view>
    <view 
      class="tab {{activeTab === 'trash' ? 'active' : ''}}" 
      data-tab="trash" 
      bindtap="switchTab"
    >回收站</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 笔记管理组件 -->
  <view class="component-container" wx:if="{{activeTab === 'notes' && !isLoading}}">
    <note-manager 
      id="noteManager"
      userId="{{userId}}"
      showRecycleBin="{{false}}"
      bind:view="handleNoteView"
      bind:edit="handleNoteEdit"
      bind:create="handleNoteCreate"
      bind:delete="handleNoteDelete"
      bind:batchDelete="handleBatchDelete"
    ></note-manager>
  </view>

  <!-- 回收站组件 -->
  <view class="component-container" wx:if="{{activeTab === 'trash' && !isLoading}}">
    <recycle-bin 
      type="note"
      bind:restore="handleNoteRestore"
      bind:batchRestore="handleBatchRestore"
    ></recycle-bin>
  </view>
</view>
