<!-- pages/performance-test/index.wxml -->
<!-- 泡泡交互系统性能测试页面 -->

<view class="container">
  <!-- 泡泡画布 -->
  <view class="canvas-container">
    <bubble-canvas id="bubble-canvas"></bubble-canvas>

    <!-- 性能指标 -->
    <view class="performance-metrics {{showDebugInfo ? '' : 'hidden'}}">
      <view class="metric">
        <text class="metric-label">FPS:</text>
        <text class="metric-value {{fps < 45 ? 'warning' : (fps < 30 ? 'error' : '')}}">{{fps}}</text>
      </view>
      <view class="metric">
        <text class="metric-label">平均FPS:</text>
        <text class="metric-value">{{avgFps}}</text>
      </view>
      <view class="metric">
        <text class="metric-label">帧时间:</text>
        <text class="metric-value">{{frameTime}}ms</text>
      </view>
      <view class="metric">
        <text class="metric-label">泡泡数量:</text>
        <text class="metric-value">{{bubbleCount}}</text>
      </view>
    </view>
  </view>

  <!-- 控制面板 -->
  <view class="control-panel">
    <view class="panel-header">
      <text class="panel-title">性能测试控制面板</text>
      <view class="panel-actions">
        <button class="btn-icon" bindtap="toggleDebugInfo">
          <text class="icon">{{showDebugInfo ? '👁️' : '👁️‍🗨️'}}</text>
        </button>
      </view>
    </view>

    <view class="panel-body">
      <!-- 泡泡数量控制 -->
      <view class="control-group">
        <text class="control-label">泡泡数量: {{bubbleCount}}</text>
        <slider
          min="5"
          max="100"
          step="5"
          value="{{bubbleCount}}"
          show-value="{{false}}"
          bindchange="handleBubbleCountChange"
          disabled="{{isTestRunning}}"
        ></slider>
      </view>

      <!-- 性能模式选择 -->
      <view class="control-group">
        <text class="control-label">性能模式:</text>
        <radio-group class="radio-group" bindchange="handlePerformanceModeChange">
          <label class="radio-label">
            <radio value="auto" checked="{{performanceMode === 'auto'}}" disabled="{{isTestRunning}}" />
            <text>自动</text>
          </label>
          <label class="radio-label">
            <radio value="high" checked="{{performanceMode === 'high'}}" disabled="{{isTestRunning}}" />
            <text>高</text>
          </label>
          <label class="radio-label">
            <radio value="medium" checked="{{performanceMode === 'medium'}}" disabled="{{isTestRunning}}" />
            <text>中</text>
          </label>
          <label class="radio-label">
            <radio value="low" checked="{{performanceMode === 'low'}}" disabled="{{isTestRunning}}" />
            <text>低</text>
          </label>
        </radio-group>
      </view>

      <!-- 测试场景选择 -->
      <view class="control-group">
        <text class="control-label">测试场景:</text>
        <radio-group class="radio-group" bindchange="handleTestScenarioChange">
          <label class="radio-label">
            <radio value="basic" checked="{{testScenario === 'basic'}}" disabled="{{isTestRunning}}" />
            <text>基础场景 (10个泡泡)</text>
          </label>
          <label class="radio-label">
            <radio value="medium" checked="{{testScenario === 'medium'}}" disabled="{{isTestRunning}}" />
            <text>中等负载 (20个泡泡)</text>
          </label>
          <label class="radio-label">
            <radio value="high" checked="{{testScenario === 'high'}}" disabled="{{isTestRunning}}" />
            <text>高负载 (30个泡泡)</text>
          </label>
        </radio-group>
      </view>

      <!-- 交互模式选择 -->
      <view class="control-group">
        <text class="control-label">交互模式:</text>
        <radio-group class="radio-group" bindchange="handleInteractionModeChange">
          <label class="radio-label">
            <radio value="normal" checked="{{interactionMode === 'normal'}}" disabled="{{isTestRunning}}" />
            <text>正常交互</text>
          </label>
          <label class="radio-label">
            <radio value="frequent" checked="{{interactionMode === 'frequent'}}" disabled="{{isTestRunning}}" />
            <text>频繁交互</text>
          </label>
          <label class="radio-label">
            <radio value="extreme" checked="{{interactionMode === 'extreme'}}" disabled="{{isTestRunning}}" />
            <text>极端交互</text>
          </label>
        </radio-group>
      </view>

      <!-- 测试时长控制 -->
      <view class="control-group">
        <text class="control-label">测试时长: {{testDuration}}秒</text>
        <slider
          min="5"
          max="60"
          step="5"
          value="{{testDuration}}"
          show-value="{{false}}"
          bindchange="handleTestDurationChange"
          disabled="{{isTestRunning}}"
        ></slider>
      </view>

      <!-- 测试控制 -->
      <view class="control-group">
        <button
          class="btn-primary {{isTestRunning ? 'btn-disabled' : ''}}"
          bindtap="startTest"
          disabled="{{isTestRunning}}"
        >开始测试 ({{testDuration}}秒)</button>

        <button
          class="btn-secondary {{!isTestRunning ? 'btn-disabled' : ''}}"
          bindtap="stopTest"
          disabled="{{!isTestRunning}}"
        >停止测试</button>
      </view>

      <!-- 测试进度 -->
      <view class="progress-container" wx:if="{{isTestRunning}}">
        <progress percent="{{testProgress}}" stroke-width="3" />
        <text class="progress-text">测试进行中... {{testProgress.toFixed(0)}}%</text>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="test-results" wx:if="{{testResults}}">
    <view class="results-header">
      <text class="results-title">测试结果</text>
    </view>

    <view class="results-body">
      <view class="result-group">
        <view class="result-item highlight">
          <text class="result-label">性能得分:</text>
          <text class="result-value {{testResults.performanceScore < 60 ? 'error' : (testResults.performanceScore < 80 ? 'warning' : 'success')}}">{{testResults.performanceScore}} ({{testResults.performanceGrade}})</text>
        </view>
        <view class="result-item">
          <text class="result-label">平均FPS:</text>
          <text class="result-value {{testResults.avgFps < 45 ? 'warning' : (testResults.avgFps < 30 ? 'error' : 'success')}}">{{testResults.avgFps}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">最低FPS:</text>
          <text class="result-value {{testResults.minFps < 30 ? 'error' : (testResults.minFps < 45 ? 'warning' : 'success')}}">{{testResults.minFps}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">最高FPS:</text>
          <text class="result-value success">{{testResults.maxFps}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">平均帧时间:</text>
          <text class="result-value {{testResults.avgFrameTime > 20 ? 'warning' : (testResults.avgFrameTime > 33 ? 'error' : 'success')}}">{{testResults.avgFrameTime}}ms</text>
        </view>
        <view class="result-item">
          <text class="result-label">FPS稳定性:</text>
          <text class="result-value {{testResults.fpsStdDev > 10 ? 'warning' : (testResults.fpsStdDev > 15 ? 'error' : 'success')}}">±{{testResults.fpsStdDev}}</text>
        </view>
        <view class="result-item" wx:if="{{testResults.avgCpuUsage > 0}}">
          <text class="result-label">平均CPU使用率:</text>
          <text class="result-value {{testResults.avgCpuUsage > 30 ? 'warning' : (testResults.avgCpuUsage > 50 ? 'error' : 'success')}}">{{testResults.avgCpuUsage}}%</text>
        </view>
        <view class="result-item" wx:if="{{testResults.maxCpuUsage > 0}}">
          <text class="result-label">最大CPU使用率:</text>
          <text class="result-value {{testResults.maxCpuUsage > 50 ? 'warning' : (testResults.maxCpuUsage > 80 ? 'error' : 'success')}}">{{testResults.maxCpuUsage}}%</text>
        </view>
      </view>

      <view class="result-group">
        <text class="result-group-title">FPS分布</text>
        <view class="result-item">
          <text class="result-label">优秀 (≥55):</text>
          <text class="result-value success">{{testResults.fpsDistributionPercent.excellent}} ({{testResults.fpsDistribution.excellent}})</text>
        </view>
        <view class="result-item">
          <text class="result-label">良好 (45-54):</text>
          <text class="result-value">{{testResults.fpsDistributionPercent.good}} ({{testResults.fpsDistribution.good}})</text>
        </view>
        <view class="result-item">
          <text class="result-label">一般 (30-44):</text>
          <text class="result-value warning">{{testResults.fpsDistributionPercent.fair}} ({{testResults.fpsDistribution.fair}})</text>
        </view>
        <view class="result-item">
          <text class="result-label">较差 (&lt;30):</text>
          <text class="result-value error">{{testResults.fpsDistributionPercent.poor}} ({{testResults.fpsDistribution.poor}})</text>
        </view>
      </view>

      <view class="result-group">
        <text class="result-group-title">测试参数</text>
        <view class="result-item">
          <text class="result-label">泡泡数量:</text>
          <text class="result-value">{{testResults.bubbleCount}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">测试场景:</text>
          <text class="result-value">{{testResults.testScenario}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">交互模式:</text>
          <text class="result-value">{{testResults.interactionMode}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">性能模式:</text>
          <text class="result-value">{{testResults.performanceMode}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">测试时长:</text>
          <text class="result-value">{{testResults.duration}}秒</text>
        </view>
      </view>

      <view class="result-group">
        <text class="result-group-title">设备信息</text>
        <view class="result-item">
          <text class="result-label">设备型号:</text>
          <text class="result-value">{{deviceInfo.model}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">系统版本:</text>
          <text class="result-value">{{deviceInfo.system}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">性能等级:</text>
          <text class="result-value">{{deviceInfo.benchmarkLevel}}</text>
        </view>
        <view class="result-item" wx:if="{{testResults.batteryConsumption}}">
          <text class="result-label">电池消耗:</text>
          <text class="result-value {{testResults.batteryConsumption > 2 ? 'warning' : (testResults.batteryConsumption > 5 ? 'error' : 'success')}}">{{testResults.batteryConsumption}}%</text>
        </view>
      </view>

      <view class="result-summary">
        <text class="summary-title">性能评估</text>
        <text class="summary-text">
          {{testResults.performanceScore >= 90 ? '性能优秀！泡泡交互系统在当前配置下运行非常流畅，可以考虑增加泡泡数量或使用更高级的视觉效果。' :
            (testResults.performanceScore >= 80 ? '性能良好。泡泡交互系统在当前配置下运行流畅，用户体验良好。' :
              (testResults.performanceScore >= 70 ? '性能尚可。泡泡交互系统在当前配置下运行基本流畅，但在极端情况下可能出现轻微卡顿。' :
                (testResults.performanceScore >= 60 ? '性能一般。建议减少泡泡数量或使用低性能模式以提高流畅度。' :
                  (testResults.performanceScore >= 50 ? '性能较差。建议大幅减少泡泡数量并使用低性能模式。' :
                    '性能不佳。当前配置下泡泡交互系统运行不流畅，建议使用最低性能模式并减少泡泡数量至10个以下。'))))}}
        </text>

        <text class="summary-subtitle">优化建议</text>
        <view class="summary-tips">
          <view class="summary-tip" wx:if="{{testResults.avgFps < 45}}">
            • 减少泡泡数量可以显著提高帧率
          </view>
          <view class="summary-tip" wx:if="{{testResults.fpsStdDev > 10}}">
            • 帧率波动较大，建议使用低性能模式提高稳定性
          </view>
          <view class="summary-tip" wx:if="{{testResults.avgFrameTime > 20}}">
            • 帧时间较长，建议简化渲染效果
          </view>
          <view class="summary-tip" wx:if="{{testResults.avgCpuUsage > 30}}">
            • CPU使用率较高，建议优化计算逻辑
          </view>
          <view class="summary-tip" wx:if="{{testResults.bubbleCount > 30 && testResults.performanceScore < 70}}">
            • 当前泡泡数量({{testResults.bubbleCount}})可能过多，建议减少至20-30个
          </view>
          <view class="summary-tip" wx:if="{{testResults.performanceMode === 'auto' && testResults.performanceScore < 70}}">
            • 建议使用固定的低性能模式，而不是自动模式
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
