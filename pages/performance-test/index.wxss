/* pages/performance-test/index.wxss */
/* 泡泡交互系统性能测试页面样式 */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9fafb;
}

/* 画布容器 */
.canvas-container {
  position: relative;
  width: 100%;
  height: 40vh;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 性能指标 */
.performance-metrics {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 8px;
  color: #ffffff;
  font-size: 12px;
  transition: opacity 0.3s;
}

.performance-metrics.hidden {
  opacity: 0;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.metric-label {
  margin-right: 8px;
}

.metric-value {
  font-weight: bold;
}

.metric-value.warning {
  color: #f59e0b;
}

.metric-value.error {
  color: #ef4444;
}

.metric-value.success {
  color: #10b981;
}

/* 控制面板 */
.control-panel {
  flex: 1;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px 12px 0 0;
  margin-top: -12px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #1f2937;
}

.panel-actions {
  display: flex;
}

.btn-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
}

.icon {
  font-size: 20px;
}

.panel-body {
  padding-bottom: 16px;
}

/* 控制组 */
.control-group {
  margin-bottom: 16px;
}

.control-label {
  display: block;
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 8px;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
}

.radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #1f2937;
}

/* 按钮 */
.btn-primary {
  background-color: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #1f2937;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
}

.btn-disabled {
  opacity: 0.5;
}

/* 进度条 */
.progress-container {
  margin-top: 16px;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* 测试结果 */
.test-results {
  margin-top: 16px;
  background-color: #f3f4f6;
  border-radius: 8px;
  overflow: hidden;
}

.results-header {
  background-color: #3b82f6;
  padding: 12px 16px;
}

.results-title {
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
}

.results-body {
  padding: 16px;
}

.result-group {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.result-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.result-group-title {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #4b5563;
  margin-bottom: 8px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.result-item.highlight {
  background-color: #f3f4f6;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.result-item.highlight .result-label {
  font-size: 16px;
  font-weight: 500;
  color: #4b5563;
}

.result-item.highlight .result-value {
  font-size: 16px;
  font-weight: 700;
}

.result-label {
  font-size: 14px;
  color: #6b7280;
}

.result-value {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.result-summary {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.summary-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.summary-subtitle {
  display: block;
  font-size: 15px;
  font-weight: 500;
  color: #4b5563;
  margin-top: 16px;
  margin-bottom: 8px;
}

.summary-text {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
  display: block;
}

.summary-tips {
  margin-top: 8px;
}

.summary-tip {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
  margin-bottom: 4px;
}
