<!--pages/plan-detail/index.wxml-->
<view class="container {{isEditMode ? 'edit-mode' : ''}}">
  <!-- 背景装饰元素 -->
  <view class="bg-decoration bg-circle-1"></view>
  <view class="bg-decoration bg-circle-2"></view>
  <view class="bg-decoration bg-circle-3"></view>
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:if="{{loadingFailed && !isLoading}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="retryLoading">点击重试</button>
  </view>

  <!-- 计划详情内容 -->
  <block wx:if="{{!isLoading && !loadingFailed && plan}}">
    <!-- 编辑模式 -->
    <view class="edit-container" wx:if="{{isEditMode}}">
      <view class="edit-header">
        <text class="edit-title">编辑学习计划</text>
      </view>

      <view class="edit-form">
        <view class="form-item">
          <text class="form-label">标题</text>
          <input class="form-input" value="{{editData.title}}" bindinput="onEditDataChange" data-field="title" placeholder="请输入计划标题" />
        </view>

        <view class="form-item">
          <text class="form-label">描述</text>
          <textarea class="form-textarea" value="{{editData.description}}" bindinput="onEditDataChange" data-field="description" placeholder="请输入计划描述" />
        </view>

        <view class="form-item">
          <text class="form-label">目标天数</text>
          <input class="form-input" type="number" value="{{editData.targetDays}}" bindinput="onEditDataChange" data-field="targetDays" placeholder="请输入目标天数" />
        </view>
      </view>

      <view class="edit-actions">
        <button class="edit-btn cancel-btn" bindtap="cancelEdit">取消</button>
        <button class="edit-btn save-btn" bindtap="saveEdit">保存</button>
      </view>
    </view>

    <!-- 查看模式 -->
    <view class="plan-detail-container" wx:else>
    <!-- 计划基本信息 -->
    <learning-plan-card
      plan="{{plan}}"
      showActions="{{false}}"
      bind:click="handlePlanClick"
    ></learning-plan-card>

    <!-- 计划进度 -->
    <view class="glass-card">
      <view class="section-title">学习进度</view>
      <learning-progress
        plan="{{plan}}"
        type="full"
        bind:dayclick="handleDayClick"
      ></learning-progress>
    </view>

    <!-- 学习统计 -->
    <view class="glass-card">
      <view class="statistics-section">
        <view class="section-title">学习统计</view>
        <view class="statistics-grid">
          <view class="stat-item">
            <view class="stat-value">{{statistics.exercisesCompleted || 0}}</view>
            <view class="stat-label">练习完成</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{statistics.insightsViewed || 0}}</view>
            <view class="stat-label">观点查看</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{statistics.notesCreated || 0}}</view>
            <view class="stat-label">笔记创建</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{formatTime(statistics.totalTimeSpent)}}</view>
            <view class="stat-label">学习时长</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签列表 -->
    <view class="glass-card">
      <view class="tags-section">
        <view class="section-title">学习标签</view>
        <view class="tags-container">
          <view class="tag-item" wx:for="{{tags}}" wx:key="id">
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-button activate-button"
              bindtap="activatePlan"
              wx:if="{{!plan.isCurrent}}">
        设为当前计划
      </button>
      <button class="action-button current-button" wx:if="{{plan.isCurrent}}">
        当前学习计划
      </button>
    </view>

    <!-- 标签页导航 -->
    <view class="tabs">
      <view class="tab {{activeTab === 'overview' ? 'active' : ''}}" bindtap="switchTab" data-tab="overview">概览</view>
      <view class="tab {{activeTab === 'content' ? 'active' : ''}}" bindtap="switchTab" data-tab="content">内容</view>
      <view class="tab {{activeTab === 'statistics' ? 'active' : ''}}" bindtap="switchTab" data-tab="statistics">统计</view>
    </view>

    <!-- 标签页内容 -->
    <view class="tab-content">
      <!-- 概览标签页 -->
      <view class="tab-pane {{activeTab === 'overview' ? 'active' : ''}}">
        <!-- 最近学习记录 -->
        <view class="records-card">
          <view class="records-header">
            <text class="records-title">最近学习记录</text>
          </view>

          <view class="empty-records" wx:if="{{learningRecords.length === 0}}">
            <text>暂无学习记录</text>
          </view>

          <view class="records-list" wx:else>
            <view class="record-item" wx:for="{{learningRecords}}" wx:key="id">
              <view class="record-icon {{item.type}}"></view>
              <view class="record-content">
                <text class="record-title">{{item.title}}</text>
                <text class="record-time">{{formatDate(item.createdAt)}}</text>
              </view>
              <text class="record-duration">{{formatTime(item.duration || 0)}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 内容标签页 -->
      <view class="tab-pane {{activeTab === 'content' ? 'active' : ''}}">
        <view class="content-list">
          <view class="empty-content" wx:if="{{contentPlan.length === 0}}">
            <text>暂无内容计划</text>
          </view>

          <view wx:for="{{contentPlan}}" wx:key="id">
            <content-card
              contentType="{{item.type || 'note'}}"
              contentData="{{item}}"
              bind:click="handleContentClick"
              bind:view="handleViewContent"
            ></content-card>
          </view>
        </view>
      </view>

      <!-- 统计标签页 -->
      <view class="tab-pane {{activeTab === 'statistics' ? 'active' : ''}}">
        <!-- 这里可以添加图表组件，显示学习进度、时间分布等统计数据 -->
        <view class="chart-container">
          <view class="chart-card">
            <view class="chart-title">学习进度</view>
            <view class="chart-placeholder">
              <!-- 这里应该放置实际的图表组件 -->
              <text>进度图表</text>
            </view>
          </view>

          <view class="chart-card">
            <view class="chart-title">学习时间</view>
            <view class="chart-placeholder">
              <!-- 这里应该放置实际的图表组件 -->
              <text>时间图表</text>
            </view>
          </view>

          <view class="chart-card">
            <view class="chart-title">活动分布</view>
            <view class="chart-placeholder">
              <!-- 这里应该放置实际的图表组件 -->
              <text>活动图表</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    </view>
  </block>
</view>
