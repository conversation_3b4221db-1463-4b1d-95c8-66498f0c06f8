/* pages/plan-detail/index.wxss */

.container {
  padding: 0;
  width: 100%;
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 背景渐变效果 */
.container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(55, 117, 245, 0.05) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: -1;
}

/* 背景装饰元素 */
.bg-decoration {
  position: fixed;
  border-radius: 50%;
  z-index: -1;
  opacity: 0.6;
}

/* 编辑模式样式 */
.edit-container {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  margin: 30rpx;
  padding: 30rpx;
}

.edit-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.edit-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.edit-form {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.edit-btn {
  width: 180rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.save-btn {
  background-color: #3775f5;
  color: #fff;
}

.bg-circle-1 {
  width: 400rpx;
  height: 400rpx;
  top: -100rpx;
  right: -100rpx;
  background: radial-gradient(circle, rgba(55, 117, 245, 0.1) 0%, rgba(55, 117, 245, 0) 70%);
}

.bg-circle-2 {
  width: 600rpx;
  height: 600rpx;
  bottom: -200rpx;
  left: -200rpx;
  background: radial-gradient(circle, rgba(55, 117, 245, 0.08) 0%, rgba(55, 117, 245, 0) 70%);
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  background: radial-gradient(circle, rgba(55, 117, 245, 0.05) 0%, rgba(55, 117, 245, 0) 70%);
}

/* 加载状态 */
.loading-container {
  width: 100%;
  height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(55, 117, 245, 0.1);
  border-top: 4rpx solid #3775F5;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  box-shadow: 0 0 20rpx rgba(55, 117, 245, 0.1);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
  letter-spacing: 1rpx;
}

/* 错误状态 */
.error-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
  padding: 0 40rpx;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #ff7875, #ff4d4f);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(255, 77, 79, 0.2);
  position: relative;
  overflow: hidden;
}

.error-icon::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
  letter-spacing: 1rpx;
}

.retry-button {
  width: 240rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #3775F5, #5C9DFF);
  color: white;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(55, 117, 245, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.retry-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.retry-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(55, 117, 245, 0.2);
}

/* 计划详情容器 */
.plan-detail-container {
  width: 100%;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

/* 玻璃卡片基础样式 */
.glass-card {
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
}

/* 卡片内部装饰 */
.glass-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

/* 计划头部 */
.plan-header {
  width: 100%;
}

.plan-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.plan-theme {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.plan-status {
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: white;
}

.plan-status.not_started {
  background-color: #faad14;
}

.plan-status.in_progress {
  background-color: #3775F5;
}

.plan-status.completed {
  background-color: #52c41a;
}

.plan-status.paused {
  background-color: #bfbfbf;
}

/* 计划描述 */
.plan-description {
  width: 100%;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-top: 20rpx;
}

/* 进度部分 */
.plan-progress-section {
  width: 100%;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #3775F5;
  border-radius: 2rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: rgba(240, 240, 240, 0.6);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3775F5, #5C9DFF);
  border-radius: 6rpx;
  position: relative;
  transition: width 0.5s ease;
}

.progress-fill::after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 12rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(55, 117, 245, 0.5);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.progress-percentage {
  font-size: 36rpx;
  font-weight: 600;
  color: #3775F5;
  letter-spacing: -1rpx;
}

.progress-days {
  font-size: 28rpx;
  color: #666;
  align-self: flex-end;
}

.date-range {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 统计部分 */
.statistics-section {
  width: 100%;
}

.statistics-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.stat-item {
  width: 50%;
  padding: 20rpx 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.stat-item::after {
  content: "";
  position: absolute;
  width: 80%;
  height: 1px;
  bottom: 0;
  left: 10%;
  background: linear-gradient(90deg,
    rgba(55, 117, 245, 0),
    rgba(55, 117, 245, 0.1) 50%,
    rgba(55, 117, 245, 0)
  );
}

.stat-value {
  font-size: 42rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #3775F5, #5C9DFF);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  letter-spacing: 1rpx;
}

/* 标签部分 */
.tags-section {
  width: 100%;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.tag-item {
  padding: 12rpx 24rpx;
  background-color: rgba(240, 245, 255, 0.6);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 30rpx;
  margin: 8rpx;
  font-size: 26rpx;
  color: #3775F5;
  border: 1px solid rgba(55, 117, 245, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(55, 117, 245, 0.05);
  transition: all 0.3s ease;
}

.tag-item:active {
  transform: scale(0.95);
  background-color: rgba(55, 117, 245, 0.1);
}

/* 操作按钮 */
.action-buttons {
  width: 100%;
  padding: 40rpx 0 60rpx;
  display: flex;
  justify-content: center;
}

.action-button {
  width: 70%;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.activate-button {
  background: linear-gradient(135deg, #3775F5, #5C9DFF);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(55, 117, 245, 0.3);
}

.activate-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(55, 117, 245, 0.2);
}

.current-button {
  background-color: rgba(230, 247, 255, 0.7);
  color: #3775F5;
  border: 1rpx solid rgba(55, 117, 245, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* 标签页样式 */
.tabs {
  display: flex;
  background-color: #fff;
  margin: 30rpx 30rpx 0;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab.active {
  color: #3775f5;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #3775f5;
  border-radius: 2rpx;
}

.tab-content {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 0 0 16rpx 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.tab-pane {
  display: none;
  padding: 30rpx;
}

.tab-pane.active {
  display: block;
}

/* 学习记录卡片 */
.records-card {
  margin-bottom: 30rpx;
}

.records-header {
  margin-bottom: 20rpx;
}

.records-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.empty-records {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.records-list {
  margin-top: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.record-icon.exercise {
  background-color: #e6f7ff;
  color: #1890ff;
}

.record-icon.insight {
  background-color: #f6ffed;
  color: #52c41a;
}

.record-icon.note {
  background-color: #fff7e6;
  color: #fa8c16;
}

.record-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.record-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-duration {
  font-size: 24rpx;
  color: #666;
}

/* 内容列表 */
.content-list {
  margin-top: 20rpx;
}

.empty-content {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.content-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.content-item:last-child {
  border-bottom: none;
}

.content-day {
  font-size: 24rpx;
  color: #3775f5;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.content-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.content-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.content-status {
  display: inline-block;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.content-status.not_started {
  background-color: #f5f5f5;
  color: #999;
}

.content-status.in_progress {
  background-color: #e6f7ff;
  color: #1890ff;
}

.content-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

/* 图表容器 */
.chart-container {
  margin-top: 20rpx;
}

.chart-card {
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.chart-placeholder {
  height: 300rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}