<!--pages/plan-generating/index.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-decoration bg-circle-1"></view>
  <view class="bg-decoration bg-circle-2"></view>
  <view class="bg-decoration bg-circle-3"></view>

  <!-- 生成中状态 -->
  <view class="generating-container" wx:if="{{isGenerating}}">
    <view class="generating-icon">
      <view class="ai-brain-icon">
        <view class="brain-pulse"></view>
        <view class="brain-circles">
          <view class="circle c1"></view>
          <view class="circle c2"></view>
          <view class="circle c3"></view>
        </view>
      </view>
    </view>
    <text class="generating-title">AI正在生成学习计划</text>
    <text class="generating-subtitle">{{generatingStage}}</text>
    <view class="generating-progress">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{generatingProgress}}%;"></view>
      </view>
      <text class="progress-percentage">{{generatingProgress}}%</text>
    </view>
    <text class="generating-tips">{{generatingTips}}</text>
  </view>

  <!-- 生成失败状态 -->
  <view class="error-container" wx:if="{{generatingFailed && !isGenerating}}">
    <view class="error-icon">!</view>
    <text class="error-text">生成失败</text>
    <text class="error-subtext">{{errorMessage || '请稍后再试'}}</text>
    <view class="action-buttons">
      <button class="action-button retry-button" bindtap="retryGeneration">重新生成</button>
      <button class="action-button back-button" bindtap="goBack">返回修改</button>
    </view>
  </view>

  <!-- 计划生成结果内容 -->
  <view class="plan-result-container" wx:if="{{!isGenerating && !generatingFailed && planData}}">
    <!-- 计划标题卡片 -->
    <view class="glass-card title-card">
      <view class="plan-title">{{planData.enhancedTitle || planData.title}}</view>
      <view class="plan-theme-tag">{{theme || '人际沟通'}}</view>
    </view>

    <!-- 设计原则卡片 -->
    <view class="glass-card">
      <view class="section-title">学习设计原则</view>
      <view class="section-content principle-content">
        <text>{{planData.designPrinciple}}</text>
      </view>
    </view>

    <!-- 内容计划卡片 -->
    <view class="glass-card">
      <view class="section-title">学习内容安排</view>
      <view class="content-days">
        <view class="day-item" wx:for="{{planData.contentPlan}}" wx:key="day">
          <view class="day-header">
            <view class="day-number">Day {{item.day}}</view>
            <view class="day-title">{{item.title}}</view>
          </view>
          <view class="day-content">
            <text>{{item.content}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签列表 -->
    <view class="glass-card">
      <view class="section-title">学习标签</view>
      <view class="tags-container">
        <view class="tag-item" wx:for="{{planData.tags}}" wx:key="*this">
          <text>{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-button back-button" bindtap="modifyPlan">修改计划</button>
      <button class="action-button submit-button" bindtap="savePlan">确认使用</button>
    </view>
  </view>
</view> 