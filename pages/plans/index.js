// pages/plans/index.js
// 学习计划列表页面

// 导入学习计划服务
const learningPlanService = require('../../utils/learning-plan-service');
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    loadingFailed: false,
    isLoggedIn: false,
    plans: [],
    totalPlans: 0,

    // 筛选和排序选项
    filterOptions: {
      status: '', // 状态筛选：all, not_started, in_progress, completed, paused
      themeId: '', // 主题筛选
      sortBy: 'updatedAt', // 排序字段：createdAt, updatedAt, progress
      sortOrder: 'desc' // 排序方式：asc, desc
    },

    // 分页
    pagination: {
      page: 1,
      pageSize: 10,
      hasMore: false
    },

    // 视图模式
    viewMode: 'list', // list, grid

    // 主题列表（用于筛选）
    themes: [],

    // 显示筛选面板
    showFilterPanel: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    // 检查登录状态
    const isLoggedIn = await authService.isLoggedIn();
    this.setData({ isLoggedIn });

    if (!isLoggedIn) {
      this.setData({ isLoading: false });
      return;
    }

    // 加载学习计划列表
    this.loadPlans();

    // 加载主题列表（用于筛选）
    this.loadThemes();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    if (this.data.isLoggedIn) {
      this.loadPlans();
    }
  },

  /**
   * 加载学习计划列表
   */
  async loadPlans() {
    this.setData({ isLoading: true, loadingFailed: false });

    try {
      const { filterOptions, pagination } = this.data;

      // 构建查询参数
      const params = {
        status: filterOptions.status || undefined,
        themeId: filterOptions.themeId || undefined,
        sortBy: filterOptions.sortBy,
        sortOrder: filterOptions.sortOrder,
        page: pagination.page,
        pageSize: pagination.pageSize
      };

      // 调用服务获取学习计划列表
      const response = await learningPlanService.getUserPlans(params);

      if (response.success) {
        // 更新数据
        this.setData({
          plans: response.data.plans,
          totalPlans: response.data.pagination.total,
          'pagination.hasMore': response.data.pagination.page < response.data.pagination.totalPages,
          isLoading: false
        });
      } else {
        this.setData({
          isLoading: false,
          loadingFailed: true
        });

        wx.showToast({
          title: response.error || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载学习计划列表失败:', error);

      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 加载更多学习计划
   */
  async loadMorePlans() {
    if (!this.data.pagination.hasMore || this.data.isLoading) {
      return;
    }

    this.setData({
      'pagination.page': this.data.pagination.page + 1,
      isLoadingMore: true
    });

    try {
      const { filterOptions, pagination } = this.data;

      // 构建查询参数
      const params = {
        status: filterOptions.status || undefined,
        themeId: filterOptions.themeId || undefined,
        sortBy: filterOptions.sortBy,
        sortOrder: filterOptions.sortOrder,
        page: pagination.page,
        pageSize: pagination.pageSize
      };

      // 调用服务获取更多学习计划
      const response = await learningPlanService.getUserPlans(params);

      if (response.success) {
        // 更新数据
        this.setData({
          plans: [...this.data.plans, ...response.data.plans],
          'pagination.hasMore': response.data.pagination.page < response.data.pagination.totalPages,
          isLoadingMore: false
        });
      } else {
        this.setData({
          isLoadingMore: false
        });

        wx.showToast({
          title: response.error || '加载更多失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载更多学习计划失败:', error);

      this.setData({
        isLoadingMore: false
      });

      wx.showToast({
        title: '加载更多失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 加载主题列表
   */
  async loadThemes() {
    try {
      // 调用API获取主题列表
      const response = await wx.cloud.callFunction({
        name: 'getThemes'
      });

      if (response.result && response.result.success) {
        this.setData({
          themes: response.result.data.themes
        });
      }
    } catch (error) {
      console.error('加载主题列表失败:', error);
    }
  },

  /**
   * 切换视图模式
   */
  toggleViewMode() {
    this.setData({
      viewMode: this.data.viewMode === 'list' ? 'grid' : 'list'
    });
  },

  /**
   * 显示/隐藏筛选面板
   */
  toggleFilterPanel() {
    this.setData({
      showFilterPanel: !this.data.showFilterPanel
    });
  },

  /**
   * 应用筛选条件
   */
  applyFilter(e) {
    const { status, themeId, sortBy, sortOrder } = e.detail;

    this.setData({
      'filterOptions.status': status,
      'filterOptions.themeId': themeId,
      'filterOptions.sortBy': sortBy,
      'filterOptions.sortOrder': sortOrder,
      'pagination.page': 1,
      showFilterPanel: false
    });

    // 重新加载数据
    this.loadPlans();
  },

  /**
   * 重置筛选条件
   */
  resetFilter() {
    this.setData({
      'filterOptions.status': '',
      'filterOptions.themeId': '',
      'filterOptions.sortBy': 'updatedAt',
      'filterOptions.sortOrder': 'desc',
      'pagination.page': 1,
      showFilterPanel: false
    });

    // 重新加载数据
    this.loadPlans();
  },

  /**
   * 查看学习计划详情
   */
  viewPlanDetail(e) {
    const planId = e.currentTarget.dataset.id;

    if (!planId) {
      wx.showToast({
        title: '计划ID不存在',
        icon: 'none'
      });
      return;
    }

    // 跳转到计划详情页面
    wx.navigateTo({
      url: `/pages/plan-detail/index?id=${planId}`
    });
  },

  /**
   * 创建新学习计划
   */
  createNewPlan() {
    // 跳转到创建学习计划页面
    wx.navigateTo({
      url: '/pages/create-plan/index'
    });
  },

  /**
   * 设置当前学习计划
   */
  async setCurrentPlan(e) {
    const planId = e.currentTarget.dataset.id;

    if (!planId) {
      wx.showToast({
        title: '计划ID不存在',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '正在设置...' });

    try {
      // 调用服务设置当前学习计划
      const response = await learningPlanService.setCurrentPlan(planId);

      wx.hideLoading();

      if (response.success) {
        // 更新本地数据
        const plans = this.data.plans.map(plan => {
          if (plan.id === planId) {
            plan.isCurrent = true;
          } else {
            plan.isCurrent = false;
          }
          return plan;
        });

        this.setData({ plans });

        wx.showToast({
          title: '已设为当前计划',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: response.error || '设置失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('设置当前学习计划失败:', error);

      wx.showToast({
        title: '设置失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 删除学习计划
   */
  async deletePlan(e) {
    const planId = e.currentTarget.dataset.id;

    if (!planId) {
      wx.showToast({
        title: '计划ID不存在',
        icon: 'none'
      });
      return;
    }

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个学习计划吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#e64340',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({ title: '正在删除...' });

          try {
            // 调用服务删除学习计划
            const response = await learningPlanService.deletePlan(planId);

            wx.hideLoading();

            if (response.success) {
              // 更新本地数据
              const plans = this.data.plans.filter(plan => plan.id !== planId);

              this.setData({
                plans,
                totalPlans: this.data.totalPlans - 1
              });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: response.error || '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            wx.hideLoading();
            console.error('删除学习计划失败:', error);

            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 重试加载
   */
  retryLoading() {
    this.loadPlans();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    // 重置分页
    this.setData({
      'pagination.page': 1
    });

    // 重新加载数据
    this.loadPlans().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    this.loadMorePlans();
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '我的学习计划',
      path: '/pages/plans/index'
    };
  }
});
