<!--pages/plans/index.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-decoration bg-circle-1"></view>
  <view class="bg-decoration bg-circle-2"></view>
  <view class="bg-decoration bg-circle-3"></view>
  
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="title-container">
      <text class="page-title">学习计划</text>
      <text class="plan-count">共 {{totalPlans}} 个计划</text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="header-actions">
      <view class="action-btn" bindtap="toggleViewMode">
        <image class="action-icon" src="/assets/icons/{{viewMode === 'list' ? 'grid' : 'list'}}-view.png"></image>
      </view>
      <view class="action-btn" bindtap="toggleFilterPanel">
        <image class="action-icon" src="/assets/icons/filter.png"></image>
      </view>
      <view class="action-btn" bindtap="createNewPlan">
        <image class="action-icon" src="/assets/icons/add.png"></image>
      </view>
    </view>
  </view>
  
  <!-- 筛选面板 -->
  <view class="filter-panel {{showFilterPanel ? 'show' : ''}}">
    <view class="filter-header">
      <text class="filter-title">筛选与排序</text>
      <view class="filter-close" bindtap="toggleFilterPanel">×</view>
    </view>
    
    <view class="filter-section">
      <text class="section-title">状态</text>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.status === '' ? 'active' : ''}}" 
              data-value="" bindtap="selectStatus">全部</view>
        <view class="filter-option {{filterOptions.status === 'not_started' ? 'active' : ''}}" 
              data-value="not_started" bindtap="selectStatus">未开始</view>
        <view class="filter-option {{filterOptions.status === 'in_progress' ? 'active' : ''}}" 
              data-value="in_progress" bindtap="selectStatus">进行中</view>
        <view class="filter-option {{filterOptions.status === 'completed' ? 'active' : ''}}" 
              data-value="completed" bindtap="selectStatus">已完成</view>
        <view class="filter-option {{filterOptions.status === 'paused' ? 'active' : ''}}" 
              data-value="paused" bindtap="selectStatus">已暂停</view>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="section-title">主题</text>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.themeId === '' ? 'active' : ''}}" 
              data-value="" bindtap="selectTheme">全部</view>
        <view class="filter-option {{filterOptions.themeId === theme.id ? 'active' : ''}}" 
              wx:for="{{themes}}" wx:key="id" wx:for-item="theme"
              data-value="{{theme.id}}" bindtap="selectTheme">{{theme.name}}</view>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="section-title">排序</text>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.sortBy === 'updatedAt' ? 'active' : ''}}" 
              data-sort-by="updatedAt" bindtap="selectSort">最近更新</view>
        <view class="filter-option {{filterOptions.sortBy === 'createdAt' ? 'active' : ''}}" 
              data-sort-by="createdAt" bindtap="selectSort">创建时间</view>
        <view class="filter-option {{filterOptions.sortBy === 'progress' ? 'active' : ''}}" 
              data-sort-by="progress" bindtap="selectSort">完成进度</view>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="section-title">排序方式</text>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.sortOrder === 'desc' ? 'active' : ''}}" 
              data-sort-order="desc" bindtap="selectSortOrder">降序</view>
        <view class="filter-option {{filterOptions.sortOrder === 'asc' ? 'active' : ''}}" 
              data-sort-order="asc" bindtap="selectSortOrder">升序</view>
      </view>
    </view>
    
    <view class="filter-actions">
      <button class="filter-btn reset-btn" bindtap="resetFilter">重置</button>
      <button class="filter-btn apply-btn" bindtap="applyFilter">应用</button>
    </view>
  </view>
  
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 加载失败状态 -->
  <view class="error-container" wx:if="{{loadingFailed && !isLoading}}">
    <view class="error-icon">!</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="retryLoading">点击重试</button>
  </view>
  
  <!-- 未登录状态 -->
  <view class="not-logged-in" wx:if="{{!isLoggedIn && !isLoading}}">
    <image class="login-image" src="/assets/images/login-required.png"></image>
    <text class="login-text">请登录后查看您的学习计划</text>
    <navigator url="/pages/login/phone" class="login-btn">去登录</navigator>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!isLoading && !loadingFailed && isLoggedIn && plans.length === 0}}">
    <image class="empty-image" src="/assets/images/empty-plans.png"></image>
    <text class="empty-text">您还没有创建任何学习计划</text>
    <button class="create-btn" bindtap="createNewPlan">创建学习计划</button>
  </view>
  
  <!-- 列表视图 -->
  <view class="plans-list" wx:if="{{!isLoading && !loadingFailed && isLoggedIn && plans.length > 0 && viewMode === 'list'}}">
    <view class="plan-item" wx:for="{{plans}}" wx:key="id" bindtap="viewPlanDetail" data-id="{{item.id}}">
      <view class="plan-content">
        <view class="plan-header">
          <text class="plan-title">{{item.title}}</text>
          <view class="plan-status {{item.status}}">
            <text wx:if="{{item.status === 'not_started'}}">未开始</text>
            <text wx:elif="{{item.status === 'in_progress'}}">进行中</text>
            <text wx:elif="{{item.status === 'completed'}}">已完成</text>
            <text wx:elif="{{item.status === 'paused'}}">已暂停</text>
          </view>
        </view>
        
        <view class="plan-theme">{{item.themeName}}</view>
        
        <view class="plan-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%;"></view>
          </view>
          <text class="progress-text">{{item.progress}}%</text>
        </view>
        
        <view class="plan-dates">
          <text class="date-label">开始：</text>
          <text class="date-value">{{item.startDate || '未开始'}}</text>
          <text class="date-separator">|</text>
          <text class="date-label">目标：</text>
          <text class="date-value">{{item.targetDays}}天</text>
        </view>
      </view>
      
      <view class="plan-actions">
        <view class="action-icon {{item.isCurrent ? 'current' : ''}}" 
              catchtap="setCurrentPlan" data-id="{{item.id}}">
          <image src="/assets/icons/{{item.isCurrent ? 'current' : 'set-current'}}.png"></image>
          <text>{{item.isCurrent ? '当前' : '设为当前'}}</text>
        </view>
        <view class="action-icon delete" catchtap="deletePlan" data-id="{{item.id}}">
          <image src="/assets/icons/delete.png"></image>
          <text>删除</text>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{pagination.hasMore}}">
      <text wx:if="{{isLoadingMore}}">正在加载更多...</text>
      <text wx:else bindtap="loadMorePlans">点击加载更多</text>
    </view>
  </view>
  
  <!-- 网格视图 -->
  <view class="plans-grid" wx:if="{{!isLoading && !loadingFailed && isLoggedIn && plans.length > 0 && viewMode === 'grid'}}">
    <view class="grid-item" wx:for="{{plans}}" wx:key="id" bindtap="viewPlanDetail" data-id="{{item.id}}">
      <view class="grid-content">
        <view class="grid-header">
          <text class="grid-title">{{item.title}}</text>
          <view class="grid-status {{item.status}}"></view>
        </view>
        
        <view class="grid-theme">{{item.themeName}}</view>
        
        <view class="grid-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%;"></view>
          </view>
          <text class="progress-text">{{item.progress}}%</text>
        </view>
        
        <view class="grid-actions">
          <view class="grid-action {{item.isCurrent ? 'current' : ''}}" 
                catchtap="setCurrentPlan" data-id="{{item.id}}">
            <image src="/assets/icons/{{item.isCurrent ? 'current' : 'set-current'}}.png"></image>
          </view>
          <view class="grid-action delete" catchtap="deletePlan" data-id="{{item.id}}">
            <image src="/assets/icons/delete.png"></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{pagination.hasMore}}">
      <text wx:if="{{isLoadingMore}}">正在加载更多...</text>
      <text wx:else bindtap="loadMorePlans">点击加载更多</text>
    </view>
  </view>
  
  <!-- 创建按钮 -->
  <view class="floating-btn" bindtap="createNewPlan">
    <image src="/assets/icons/add-white.png"></image>
  </view>
</view>
