/* pages/plans/index.wxss */

/* 容器样式 */
.container {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  z-index: 0;
}

.bg-circle-1 {
  width: 600rpx;
  height: 600rpx;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  top: -200rpx;
  right: -200rpx;
}

.bg-circle-2 {
  width: 400rpx;
  height: 400rpx;
  background: linear-gradient(135deg, #ff0844 0%, #ffb199 100%);
  bottom: -100rpx;
  left: -100rpx;
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  background: linear-gradient(135deg, #13547a 0%, #80d0c7 100%);
  top: 40%;
  left: 60%;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 1;
}

.title-container {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.plan-count {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 筛选面板 */
.filter-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  height: 100%;
  background-color: #fff;
  box-shadow: -4rpx 0 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding: 30rpx;
  box-sizing: border-box;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.filter-panel.show {
  right: 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.filter-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
}

.filter-option.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 60rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.filter-btn {
  width: 45%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
}

.apply-btn {
  background-color: #1890ff;
  color: #fff;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  margin-top: 100rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载失败状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  margin-top: 100rpx;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 60rpx;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.retry-button {
  margin-top: 30rpx;
  padding: 16rpx 40rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
}

/* 未登录状态 */
.not-logged-in {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 500rpx;
  margin-top: 100rpx;
}

.login-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.login-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.login-btn {
  padding: 20rpx 60rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 500rpx;
  margin-top: 100rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.create-btn {
  padding: 20rpx 60rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
}

/* 列表视图 */
.plans-list {
  margin-top: 30rpx;
  position: relative;
  z-index: 1;
}

.plan-item {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
}

.plan-content {
  flex: 1;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.plan-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.plan-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 20rpx;
}

.plan-status.not_started {
  background-color: #f5f5f5;
  color: #999;
}

.plan-status.in_progress {
  background-color: #e6f7ff;
  color: #1890ff;
}

.plan-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.plan-status.paused {
  background-color: #fff7e6;
  color: #fa8c16;
}

.plan-theme {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.plan-progress {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-bar {
  flex: 1;
  height: 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #36cfc9);
  border-radius: 8rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  width: 60rpx;
  text-align: right;
}

.plan-dates {
  font-size: 24rpx;
  color: #999;
}

.date-label {
  color: #666;
}

.date-separator {
  margin: 0 16rpx;
}

.plan-actions {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 30rpx;
}

.action-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
}

.action-icon image {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.action-icon text {
  font-size: 20rpx;
  color: #666;
}

.action-icon.current text {
  color: #1890ff;
}

.action-icon.delete text {
  color: #ff4d4f;
}

/* 网格视图 */
.plans-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  margin-top: 30rpx;
  position: relative;
  z-index: 1;
}

.grid-item {
  width: calc(50% - 15rpx);
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  box-sizing: border-box;
}

.grid-content {
  position: relative;
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.grid-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.grid-status {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-left: 16rpx;
}

.grid-status.not_started {
  background-color: #999;
}

.grid-status.in_progress {
  background-color: #1890ff;
}

.grid-status.completed {
  background-color: #52c41a;
}

.grid-status.paused {
  background-color: #fa8c16;
}

.grid-theme {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.grid-progress {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.grid-actions {
  display: flex;
  justify-content: space-between;
}

.grid-action {
  padding: 10rpx;
}

.grid-action image {
  width: 36rpx;
  height: 36rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 浮动创建按钮 */
.floating-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #36cfc9);
  box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.floating-btn image {
  width: 50rpx;
  height: 50rpx;
}
