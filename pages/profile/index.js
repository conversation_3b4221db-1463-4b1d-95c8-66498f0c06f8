// pages/profile/index.js
// 导入API工具
const { userAPI } = require('../../utils/api');
// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    hasUserInfo: false,
    userInfo: null,
    userStats: {
      days: 0,
      courses: 0,
      level: 1,
      inProgress: 0,
      favorites: 0,
      posts: 0
    },
    isLoading: true,
    loadingFailed: false,
    unreadNotifications: 0,
    hasBadgeLeaderboard: true,
    theme: 'light',
    isDarkMode: false, // 添加深色模式状态
    isSocialExpanded: true, // 第一个卡片默认展开
    isContentExpanded: false, // 其他卡片默认折叠
    isSettingsExpanded: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.time('我的页面加载耗时');
    this._pageLoadStartTime = Date.now();

    // 使用认证服务检查登录状态
    const isLoggedIn = await authService.isLoggedIn();

    // 如果已登录，获取用户信息
    let userInfo = null;
    if (isLoggedIn) {
      userInfo = await authService.getCurrentUser();
    }

    const hasUserInfo = isLoggedIn && !!userInfo;

    // 获取主题模式
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    const isDarkMode = themeMode === 'dark' ||
                      (themeMode === 'system' && wx.getSystemInfoSync().theme === 'dark');

    // 设置默认的卡片展开状态和初始用户状态
    this.setData({
      isSocialExpanded: true,
      isContentExpanded: false,
      isSettingsExpanded: false,
      isLoading: !hasUserInfo, // 如果有缓存的用户信息，直接不显示加载状态
      loadingFailed: false,
      hasUserInfo: hasUserInfo,
      userInfo: hasUserInfo ? userInfo : null,
      isDarkMode: isDarkMode, // 设置深色模式状态
      userStats: {
        days: 0,
        courses: 0,
        level: 1,
        inProgress: 0,
        favorites: 0,
        posts: 0
      }
    });

    // 并行加载用户数据和统计数据
    this.loadUserDataParallel();

    // 检查通知状态
    this.checkNotifications();

    // 无论如何，确保5秒后关闭加载状态（防止永久加载）
    setTimeout(() => {
      if (this.data.isLoading) {
        console.warn('页面加载超时，强制关闭加载状态');
        this.setData({ isLoading: false });
      }
    }, 5000);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时执行的逻辑
    this.checkAndReloadUserData();
    this.checkNotifications();

    // 获取当前主题模式和app实例
    const app = getApp();
    const themeMode = wx.getStorageSync('themeMode') || 'light';

    // 始终更新当前页面的导航栏样式
    if (app && app.updateCurrentPageNavigationBar) {
      console.log('我的页面 - 更新导航栏样式');
      app.updateCurrentPageNavigationBar();
    }

    // 检查主题模式是否变更
    const isDarkMode = themeMode === 'dark' ||
                      (themeMode === 'system' && wx.getSystemInfoSync().theme === 'dark');

    if (isDarkMode !== this.data.isDarkMode) {
      this.setData({ isDarkMode: isDarkMode });
    }

    // 每次显示页面时，确保卡片状态符合默认设置
    // 注意：如果希望保留用户的折叠/展开操作，可以注释掉这段代码
    this.setData({
      isSocialExpanded: true,
      isContentExpanded: false,
      isSettingsExpanded: false
    });

    // 记录页面显示完成时间
    if (this._pageLoadStartTime) {
      const loadTime = Date.now() - this._pageLoadStartTime;
      console.log(`我的页面加载完成，总耗时: ${loadTime}ms`);
      this._pageLoadStartTime = null;
      console.timeEnd('我的页面加载耗时');
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新时更新数据
    this.loadUserData();
    this.checkNotifications();

    // 停止下拉刷新动画
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'AI互动泡泡 - 提升你的沟通技能',
      path: '/pages/index/index',
      imageUrl: '/assets/share-image.png'
    };
  },

  // 并行加载用户数据和统计数据
  async loadUserDataParallel() {
    console.time('用户数据加载耗时');
    // 记录数据加载时间
    this._lastDataLoadTime = Date.now();

    // 设置超时定时器，确保无论如何都会关闭加载状态
    const loadingTimeout = setTimeout(() => {
      console.warn('加载超时，强制关闭加载状态');
      this.setData({ isLoading: false });
    }, 5000); // 5秒超时

    // 使用认证服务检查登录状态
    const isLoggedIn = await authService.isLoggedIn();

    if (!isLoggedIn) {
      console.log('用户未登录，显示未登录状态');

      this.setData({
        hasUserInfo: false,
        userInfo: null,
        userStats: {
          days: 0,
          courses: 0,
          level: 1,
          inProgress: 0,
          favorites: 0,
          posts: 0
        },
        isLoading: false,
        loadingFailed: false
      });

      clearTimeout(loadingTimeout);
      return;
    }

    // 使用认证服务获取用户信息
    const userInfo = await authService.getCurrentUser();

    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true,
        isLoading: false // 先关闭加载状态，显示缓存数据
      });
    }

    // 并行发起两个API请求
    const userInfoPromise = userAPI.getUserInfo();
    const userStatsPromise = statisticsAdapter.getLearningStatistics();

    // 处理用户基本信息
    userInfoPromise
      .then(res => {
        console.log('获取用户信息成功:', res);
        if (res.success && res.data) {
          const userData = res.data;

          // 更新用户信息
          this.setData({
            hasUserInfo: true,
            userInfo: {
              nickName: userData.nickname,
              avatarUrl: userData.avatarUrl,
              gender: userData.gender
            },
            userStats: {
              ...this.data.userStats,
              days: userData.studyDays || 0,
              level: userData.level || 1
            },
            isLoading: false
          });

          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', {
            nickName: userData.nickname,
            avatarUrl: userData.avatarUrl,
            gender: userData.gender
          });
        } else {
          console.warn('API返回成功但数据格式不正确:', res);
          // 不立即设置模拟数据，等待统计数据请求完成
        }
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        // 不立即设置模拟数据，等待统计数据请求完成
      });

    // 处理用户统计数据
    userStatsPromise
      .then(res => {
        console.log('获取学习统计数据成功:', res);
        if (res.success && res.data) {
          const stats = res.data;

          // 更新用户统计数据
          this.setData({
            userStats: {
              ...this.data.userStats,
              courses: stats.completedCourses || 0,
              inProgress: stats.inProgressCourses || 0,
              favorites: stats.favorites || 0,
              posts: stats.posts || 0
            }
          });
        }
      })
      .catch(err => {
        console.error('获取学习统计数据失败:', err);
      });

    // 等待所有请求完成
    Promise.allSettled([userInfoPromise, userStatsPromise])
      .then(results => {
        console.timeEnd('用户数据加载耗时');
        clearTimeout(loadingTimeout); // 清除超时定时器

        // 检查是否都失败了
        const allFailed = results.every(result => result.status === 'rejected');

        if (allFailed) {
          console.error('所有API请求都失败了，使用模拟数据');
          this.setMockUserData();
        } else {
          // 至少有一个成功，确保加载状态已关闭
          this.setData({
            isLoading: false
          });
        }
      })
      .catch(err => {
        // 捕获Promise.allSettled可能的错误
        console.error('Promise.allSettled错误:', err);
        clearTimeout(loadingTimeout);
        this.setData({ isLoading: false });
      });
  },

  // 保留原方法以兼容其他可能的调用
  loadUserData: function () {
    this.loadUserDataParallel();
  },

  // 检查是否需要重新加载用户数据
  checkAndReloadUserData: function () {
    // 获取上次加载时间
    const lastLoadTime = this._lastDataLoadTime || 0;
    const now = Date.now();
    const timeDiff = now - lastLoadTime;

    // 如果距离上次加载时间超过5分钟，或者没有用户信息，则重新加载
    if (timeDiff > 5 * 60 * 1000 || !this.data.hasUserInfo) {
      console.log('数据需要刷新，重新加载用户数据');
      this._lastDataLoadTime = now;
      this.loadUserDataParallel();
    } else {
      console.log('数据刷新间隔不足5分钟，使用缓存数据');
    }
  },

  // 加载用户统计数据
  loadUserStats: function () {
    // 获取学习统计数据
    statisticsAdapter.getLearningStatistics()
      .then(res => {
        console.log('获取学习统计数据成功:', res);
        if (res.success && res.data) {
          const stats = res.data;

          // 更新用户统计数据
          this.setData({
            userStats: {
              ...this.data.userStats,
              courses: stats.completedCourses || 0,
              inProgress: stats.inProgressCourses || 0,
              favorites: stats.favorites || 0,
              posts: stats.posts || 0
            }
          });
        }
      })
      .catch(err => {
        console.error('获取学习统计数据失败:', err);
      });
  },

  // 获取用户信息（微信登录）
  async getUserProfile() {
    try {
      // 显示加载中
      this.setData({ isLoading: true });

      // 使用认证服务进行微信登录
      const result = await authService.loginWithWechat({ getUserInfo: true });

      if (result.success) {
        // 登录成功
        this.setData({
          hasUserInfo: true,
          userInfo: result.userInfo,
          isLoading: false
        });

        // 加载用户数据
        this.loadUserDataParallel();

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
      } else {
        // 登录失败
        this.setData({ isLoading: false });

        wx.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('微信登录失败', err);

      this.setData({ isLoading: false });

      wx.showToast({
        title: '登录失败，请稍后再试',
        icon: 'none'
      });
    }
  },

  // 跳转到手机号登录页面
  phoneLogin: function () {
    wx.navigateTo({
      url: '/pages/login/phone'
    });
  },

  // 设置模拟用户数据（仅在API调用失败时使用）
  setMockUserData: function () {
    console.log('使用模拟数据');

    // 检查是否已有本地存储的用户信息
    const userInfo = wx.getStorageSync('userInfo');
    const hasUserInfo = !!userInfo;
    const token = wx.getStorageSync('token');

    // 模拟统计数据（仅在已登录时使用）
    const mockStats = hasUserInfo ? {
      days: Math.floor(Math.random() * 30) + 1,
      courses: Math.floor(Math.random() * 10) + 1,
      level: Math.floor(Math.random() * 5) + 1,
      inProgress: Math.floor(Math.random() * 3) + 1,
      favorites: Math.floor(Math.random() * 15),
      posts: Math.floor(Math.random() * 5)
    } : {
      days: 0,
      courses: 0,
      level: 1,
      inProgress: 0,
      favorites: 0,
      posts: 0
    };

    // 确保立即关闭加载状态
    this.setData({
      hasUserInfo: hasUserInfo,
      userInfo: userInfo || null,
      userStats: mockStats,
      isLoading: false,
      // 只有在有token但API调用失败时才显示加载失败
      loadingFailed: hasUserInfo && !!token
    });

    // 只有在有token但API调用失败时才显示提示
    if (hasUserInfo && token) {
      wx.showToast({
        title: '获取用户信息失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 检查通知状态
  checkNotifications: function () {
    // 模拟获取未读通知数
    const unreadCount = Math.floor(Math.random() * 5);
    this.setData({
      unreadNotifications: unreadCount
    });
  },

  // 页面导航
  navigateTo: function (e) {
    const url = e.currentTarget.dataset.url;

    // 如果是新功能，显示"即将上线"提示
    if (url.indexOf('invite') > -1 ||
        url.indexOf('leaderboard') > -1 ||
        url.indexOf('plans') > -1 ||
        url.indexOf('history') > -1 ||
        url.indexOf('notifications') > -1) {

      // 如果是排行榜，移除新标记
      if (url.indexOf('leaderboard') > -1) {
        this.setData({
          hasBadgeLeaderboard: false
        });
      }

      wx.showToast({
        title: '该功能即将上线',
        icon: 'none'
      });
      return;
    }

    // 导航到指定页面
    wx.navigateTo({
      url: url,
      fail: () => {
        wx.showToast({
          title: '页面开发中',
          icon: 'none'
        });
      }
    });
  },

  // 切换主题
  toggleTheme: function () {
    const newTheme = this.data.theme === 'light' ? 'dark' : 'light';
    this.setData({
      theme: newTheme
    });

    wx.showToast({
      title: newTheme === 'light' ? '已切换为浅色主题' : '已切换为深色主题',
      icon: 'none'
    });
  },

  // 联系客服
  contactUs: function () {
    wx.showModal({
      title: '联系客服',
      content: '如有任何问题，请发送邮件至\<EMAIL>',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 关于我们
  aboutUs: function () {
    wx.showModal({
      title: '关于AI互动泡泡',
      content: 'AI互动泡泡是一款帮助用户提升沟通技能的应用，通过有趣的互动方式，让用户掌握倾听、同理心、表达、赞美和反馈等五大核心沟通技能。',
      showCancel: false,
      confirmText: '了解更多'
    });
  },

  // 用户反馈
  provideFeedback: function () {
    wx.showModal({
      title: '用户反馈',
      content: '感谢您的使用！请给我们提出宝贵意见和建议。',
      confirmText: '我要反馈',
      success(res) {
        if (res.confirm) {
          wx.showToast({
            title: '反馈功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      confirmColor: '#e74c3c',
      success: async res => {
        if (res.confirm) {
          try {
            // 显示加载中
            wx.showLoading({
              title: '正在退出...',
              mask: true
            });

            // 使用认证服务进行登出
            await authService.logout();

            // 更新页面状态
            this.setData({
              hasUserInfo: false,
              userInfo: null,
              userStats: {
                days: 0,
                courses: 0,
                level: 1,
                inProgress: 0,
                favorites: 0,
                posts: 0
              }
            });

            // 隐藏加载中
            wx.hideLoading();

            // 显示提示
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            });
          } catch (err) {
            console.error('退出登录失败:', err);

            // 隐藏加载中
            wx.hideLoading();

            // 显示错误提示
            wx.showToast({
              title: '退出失败，请稍后再试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 切换折叠/展开状态
  toggleSection: function (e) {
    const section = e.currentTarget.dataset.section;
    let stateKey = '';

    switch (section) {
      case 'social':
        stateKey = 'isSocialExpanded';
        break;
      case 'content':
        stateKey = 'isContentExpanded';
        break;
      case 'settings':
        stateKey = 'isSettingsExpanded';
        break;
      // 未来可能添加的其他卡片在这里添加case
    }

    if (stateKey) {
      // 准备更新的状态对象
      const newState = {};

      // 简单地切换当前卡片的展开/折叠状态
      newState[stateKey] = !this.data[stateKey];

      // 应用状态更新
      this.setData(newState);
    }
  },

  // 切换主题模式
  toggleThemeMode: function (e) {
    // 阻止事件冒泡，确保不影响页面滑动
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    const currentThemeMode = wx.getStorageSync('themeMode') || 'light';
    const newThemeMode = currentThemeMode === 'dark' ? 'light' : 'dark';
    const themeName = newThemeMode === 'dark' ? '深色' : '浅色';

    // 获取app实例
    const app = getApp();

    // 显示加载提示
    wx.showLoading({
      title: `切换到${themeName}模式...`,
      mask: true
    });

    // 调用app.js中的updateThemeMode方法更新主题
    if (app && app.updateThemeMode) {
      app.updateThemeMode(newThemeMode);
    } else {
      // 如果app实例不可用，则直接保存到本地存储
      wx.setStorageSync('themeMode', newThemeMode);
    }

    // 更新当前页面状态
    this.setData({
      isDarkMode: newThemeMode === 'dark'
    });

    // 延迟执行，给用户一个视觉反馈
    setTimeout(() => {
      // 隐藏加载提示
      wx.hideLoading();

      // 显示切换成功的提示
      wx.showToast({
        title: `已切换到${themeName}模式`,
        icon: 'success',
        duration: 1500
      });

      // 使用更温和的方式刷新当前页面，避免页面闪动和跳转
      const currentPages = getCurrentPages();
      const currentPage = currentPages[currentPages.length - 1];

      // 尝试直接刷新当前页面的数据，而不进行页面跳转
      if (currentPage && typeof currentPage.onShow === 'function') {
        try {
          // 调用当前页面的onShow方法来刷新数据
          currentPage.onShow();
          console.log('通过调用onShow方法刷新页面数据');
        } catch (err) {
          console.error('调用onShow方法刷新页面失败:', err);
        }
      }

      // 通知其他可能的监听器主题发生了变化
      if (app.themeModeChangeCallback) {
        app.themeModeChangeCallback(newThemeMode);
      }
    }, 800);
  },

  /**
   * 监听 App 主题变化
   * @param {string} newTheme 新的主题模式 ('light' or 'dark')
   */
  onThemeChange: function (newTheme) {
    console.log('个人资料页面接收到主题变化:', newTheme);
    this.setData({
      isDarkMode: newTheme === 'dark'
    });

    // 仅更新 UI 相关内容，移除数据加载
    // 不再调用 loadUserStats()，因为主题变化不应触发需要登录的数据加载
    // if (this.data.isLoginReady) {
    //   this.loadUserStats();
    // }
    console.log('Profile theme updated, skipping data reload on theme change.');
    // 如果有其他纯粹基于主题的 UI 更新逻辑，可以放在这里
    // 例如，手动更新某些组件的样式（如果它们不自动响应 data-theme）

    // 可能需要更新导航栏，但这应该由 app.js 统一处理
    // getApp().updateCurrentPageNavigationBar();
  }
});