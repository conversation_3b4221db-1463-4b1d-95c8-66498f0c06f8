<!--pages/profile/index.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="glass-card profile-header">
    <!-- 主题切换开关 - 添加在左上角 -->
    <view class="theme-toggle-switch" catchtap="toggleThemeMode">
      <view class="switch-track {{isDarkMode ? 'dark' : 'light'}}">
        <view class="switch-handle {{isDarkMode ? 'right' : 'left'}}">
          <image class="switch-icon" src="{{isDarkMode ? '/assets/icons/new/light-mode.png' : '/assets/icons/new/dark-mode.png'}}"></image>
        </view>
      </view>
    </view>

    <!-- 用户信息内容 - 始终显示，不再受isLoading控制 -->
    <view class="user-content">
      <!-- 加载中状态 - 作为覆盖层显示 -->
      <view class="loading-overlay" wx:if="{{isLoading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 加载失败状态 -->
      <view class="error-container" wx:if="{{loadingFailed && !isLoading && hasUserInfo}}">
        <view class="error-icon">!</view>
        <text class="error-text">加载失败</text>
        <button class="retry-button" bindtap="loadUserData">点击重试</button>
      </view>
      <view class="avatar-container">
        <image class="avatar" src="{{userInfo.avatarUrl}}" wx:if="{{hasUserInfo && userInfo.avatarUrl}}"></image>
        <open-data type="userAvatarUrl" class="avatar" wx:elif="{{hasUserInfo}}"></open-data>
        <view class="avatar default-avatar" wx:else>
          <text class="default-avatar-text">AI</text>
        </view>
      </view>
      <view class="user-info">
        <text class="nickname" wx:if="{{hasUserInfo && userInfo.nickName}}">{{userInfo.nickName}}</text>
        <open-data type="userNickName" class="nickname" wx:elif="{{hasUserInfo}}"></open-data>
        <view class="nickname" wx:else></view>
        <view class="user-status" wx:if="{{hasUserInfo}}">学习中</view>
        <button class="login-button" bindtap="getUserProfile" wx:if="{{!hasUserInfo}}">微信登录</button>
      </view>

      <!-- 用户数据概览 -->
      <view class="user-stats" wx:if="{{hasUserInfo}}">
        <view class="stat-item">
          <text class="stat-value">{{userStats.days || 0}}</text>
          <text class="stat-label">学习天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.courses || 0}}</text>
          <text class="stat-label">完成课程</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.level || 0}}</text>
          <text class="stat-label">技能等级</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 核心功能区域 -->
  <view class="core-functions">
    <view class="function-card" bindtap="navigateTo" data-url="/pages/profile/learning">
      <image class="function-icon" src="/assets/icons/new/book.png"></image>
      <text class="function-text">我的学习</text>
      <text class="function-data">{{userStats.inProgress || 0}}个进行中</text>
    </view>

    <view class="function-card" bindtap="navigateTo" data-url="/pages/profile/favorites">
      <image class="function-icon" src="/assets/icons/new/favorite.png"></image>
      <text class="function-text">我的收藏</text>
      <text class="function-data">{{userStats.favorites || 0}}个收藏</text>
    </view>

    <view class="function-card" bindtap="navigateTo" data-url="/pages/profile/my-posts">
      <image class="function-icon" src="/assets/icons/new/edit.png"></image>
      <text class="function-text">我的发布</text>
      <text class="function-data">{{userStats.posts || 0}}篇内容</text>
    </view>
  </view>

  <!-- 社交扩展区域 -->
  <view class="glass-card menu-section">
    <view class="section-title" bindtap="toggleSection" data-section="social">
      <text>社交互动</text>
      <view class="toggle-icon {{isSocialExpanded ? 'expanded' : ''}}">
        <view class="arrow-icon"></view>
      </view>
    </view>

    <view class="menu-list {{isSocialExpanded ? 'expanded' : 'collapsed'}}">
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/profile/invite">
        <view class="menu-text">邀请好友</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/profile/leaderboard">
        <view class="menu-text">学习排行</view>
        <view class="menu-badge" wx:if="{{hasBadgeLeaderboard}}">新</view>
        <view class="menu-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 内容管理区域 -->
  <view class="glass-card menu-section">
    <view class="section-title" bindtap="toggleSection" data-section="content">
      <text>内容管理</text>
      <view class="toggle-icon {{isContentExpanded ? 'expanded' : ''}}">
        <view class="arrow-icon"></view>
      </view>
    </view>

    <view class="menu-list {{isContentExpanded ? 'expanded' : 'collapsed'}}">
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/profile/plans">
        <view class="menu-text">学习计划</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/note-management/index">
        <view class="menu-text">笔记管理</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/profile/history">
        <view class="menu-text">历史记录</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/batch-operation-demo/index">
        <view class="menu-text">批量操作演示</view>
        <view class="menu-badge">新</view>
        <view class="menu-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 设置区域 -->
  <view class="glass-card menu-section">
    <view class="section-title" bindtap="toggleSection" data-section="settings">
      <text>设置与帮助</text>
      <view class="toggle-icon {{isSettingsExpanded ? 'expanded' : ''}}">
        <view class="arrow-icon"></view>
      </view>
    </view>

    <view class="menu-list {{isSettingsExpanded ? 'expanded' : 'collapsed'}}">
      <view class="menu-item" bindtap="navigateTo" data-url="/pages/profile/settings">
        <view class="menu-text">账号设置</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-url="/pages/profile/notifications">
        <view class="menu-text">通知中心</view>
        <view class="menu-badge" wx:if="{{unreadNotifications > 0}}">{{unreadNotifications}}</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="toggleTheme">
        <view class="menu-text">主题切换</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="aboutUs">
        <view class="menu-text">关于我们</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="contactUs">
        <view class="menu-text">联系客服</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item" bindtap="phoneLogin">
        <view class="menu-text">{{hasUserInfo ? '绑定手机号' : '手机号登录'}}</view>
        <view class="menu-arrow">›</view>
      </view>

      <view class="menu-item logout-item" bindtap="logout" wx:if="{{hasUserInfo}}">
        <view class="menu-text logout-text">退出登录</view>
        <view class="menu-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text class="brand">AI互动泡泡</text>
    <text class="version-info">版本 1.0.0</text>
    <text class="feedback-link" bindtap="provideFeedback">用户反馈</text>
  </view>
</view>