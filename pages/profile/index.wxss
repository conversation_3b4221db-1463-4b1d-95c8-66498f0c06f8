/* pages/profile/index.wxss */
page {
  background: linear-gradient(180deg, #e0f2ff 0%, #f0e6ff 100%);
  min-height: 100vh;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 0;
  box-sizing: border-box;
}

/* 玻璃卡片基础样式 */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: 24rpx;
  margin: 0 16rpx 20rpx 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  width: calc(100% - 32rpx);
  box-sizing: border-box;
}

.glass-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

/* 用户信息区域 */
.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 20rpx;
  margin-top: 16rpx;
  text-align: center;
  min-height: 400rpx;
  position: relative;
  box-sizing: border-box;
}

/* 主题切换开关 */
.theme-toggle-switch {
  position: absolute;
  top: 30rpx;
  right: 16rpx;
  z-index: 10;
  pointer-events: auto;
  transform: scale(1.2);
  transform-origin: top right;
}

.switch-track {
  width: 55rpx;
  height: 30rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 2rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.switch-track.light {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(230, 230, 230, 0.8);
}

.switch-track.dark {
  background: rgba(50, 50, 70, 0.6);
  border: 1px solid rgba(50, 50, 70, 0.8);
}

.switch-handle {
  width: 38rpx;
  height: 38rpx;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  top: 50%;
  transform: translateY(-50%);
}

.switch-handle.left {
  left: -6rpx;
}

.switch-handle.right {
  right: -6rpx;
}

.switch-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(5px);
  border-radius: 20rpx;
}

/* 深色模式下的加载覆盖层 */
page[data-theme="dark"] .loading-overlay {
  background-color: rgba(30, 30, 40, 0.7);
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 20rpx;
}

.retry-button {
  background-color: #3B82F6;
  color: #ffffff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  border: none;
}

/* 用户信息内容容器 */
.user-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.avatar-container {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  align-self: center;
  margin-left: auto;
  margin-right: auto;
}

.avatar {
  width: 100%;
  height: 100%;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-avatar-text {
  color: #ffffff;
  font-size: 60rpx;
  font-weight: bold;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.user-status {
  font-size: 24rpx;
  color: #666666;
  background: rgba(59, 130, 246, 0.1);
  padding: 4rpx 20rpx;
  border-radius: 30rpx;
  margin-bottom: 20rpx;
}

.login-button {
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  border: none;
  margin-top: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(59, 130, 246, 0.2);
  min-width: 168rpx;
  max-width: 70%;
  align-self: center;
}

/* 用户数据概览 */
.user-stats {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 30rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #3B82F6;
  margin-bottom: 6rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 退出登录按钮样式 */
.logout-item {
  margin-top: 10rpx;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 10rpx;
}

.logout-text {
  color: #e74c3c;
  font-weight: 500;
}

/* 核心功能区域 */
.core-functions {
  display: flex;
  justify-content: space-between;
  width: calc(100% - 32rpx);
  margin: 0 16rpx 16rpx 16rpx;
}

.function-card {
  width: 32%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;
}

.function-card:active {
  transform: translateY(2rpx) scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

.function-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.function-text {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.function-data {
  font-size: 22rpx;
  color: #666666;
  margin-top: 8rpx;
}

/* 功能菜单区域 */
.menu-section {
  margin: 0 16rpx 16rpx 16rpx;
  width: calc(100% - 32rpx);
  box-sizing: border-box;
  padding: 0 0 10rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  position: relative;
  z-index: 2;
  border-radius: 10rpx;
  transition: background-color 0.2s ease;
}

.section-title::before {
  content: "";
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #3B82F6, #8B5CF6);
  margin-right: 16rpx;
  margin-left: 6rpx;
  border-radius: 4rpx;
  display: inline-block;
}

.section-title text {
  display: flex;
  align-items: center;
  text-align: left;
  font-weight: 600;
}

.section-title:active {
  background-color: rgba(0, 0, 0, 0.03);
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  margin-left: auto;
  transition: transform 0.3s ease;
  position: relative;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.arrow-icon {
  width: 14rpx;
  height: 14rpx;
  border-right: 3rpx solid rgba(150, 150, 150, 0.7);
  border-bottom: 3rpx solid rgba(150, 150, 150, 0.7);
  transform: rotate(45deg);
  transition: all 0.2s ease;
  position: relative;
  top: -4rpx;
}

.section-title:active .arrow-icon {
  border-color: #3B82F6;
  width: 16rpx;
  height: 16rpx;
}

.menu-list {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0 12rpx;
}

.menu-list.collapsed {
  max-height: 0;
  opacity: 0;
  margin-top: -10rpx;
  transform: translateY(-8rpx);
}

.menu-list.expanded {
  max-height: 1000rpx; /* 足够大的高度以显示所有内容 */
  opacity: 1;
  margin-top: 0;
  transform: translateY(0);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 28rpx 24rpx;
  border-bottom: 1px solid rgba(238, 238, 238, 0.3);
  transition: all 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: rgba(255, 255, 255, 0.4);
  transform: translateX(4rpx);
}

.menu-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
  padding-left: 24rpx;
}

.menu-badge {
  font-size: 22rpx;
  color: #ffffff;
  background-color: #ff4d4f;
  height: 32rpx;
  min-width: 32rpx;
  padding: 0 8rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  transform: scale(0.8);
}

.menu-arrow {
  font-size: 36rpx;
  color: #cccccc;
  margin-right: 16rpx;
}

/* 版本信息 */
.footer {
  text-align: center;
  padding: 40rpx 0;
  color: rgba(102, 102, 102, 0.8);
  font-size: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.brand {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  color: #3B82F6;
}

.version-info {
  margin-bottom: 10rpx;
}

.feedback-link {
  color: #8B5CF6;
  text-decoration: underline;
}