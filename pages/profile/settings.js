const app = getApp();
// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  data: {
    // 界面样式选项
    interfaceStyles: ['泡泡界面', '星星界面'],
    interfaceStyleIndex: 0,

    // 隐私设置
    allowDataCollection: true,

    // 当前主题
    theme: 'light'
  },

  onLoad: function () {
    this.loadUserSettings();
  },

  onShow: function () {
    // 获取当前主题
    const currentTheme = app.getTheme ? app.getTheme() : 'light';
    this.setData({ theme: currentTheme });
  },

  // 加载用户设置
  loadUserSettings: function () {
    try {
      // 从本地存储获取用户设置
      const interfaceStyle = wx.getStorageSync('interfaceStyle') || 'bubble';
      const allowDataCollection = wx.getStorageSync('allowDataCollection');

      // 设置界面样式索引
      this.setData({
        interfaceStyleIndex: interfaceStyle === 'bubble' ? 0 : 1,
        allowDataCollection: allowDataCollection !== false // 默认为true
      });
    } catch (e) {
      console.error('加载用户设置失败', e);
    }
  },

  // 界面样式变更
  onInterfaceStyleChange: function (e) {
    const index = e.detail.value;
    const style = index === 0 ? 'bubble' : 'star';

    this.setData({
      interfaceStyleIndex: index
    });

    // 保存设置到本地存储
    wx.setStorageSync('interfaceStyle', style);

    // 通知应用更新界面样式
    if (app.updateInterfaceStyle) {
      app.updateInterfaceStyle(style);
    }

    // 提示用户重启生效
    wx.showToast({
      title: '设置已保存，重启页面后生效',
      icon: 'none',
      duration: 2000
    });
  },

  // 主题变更事件处理
  onThemeChanged: function (e) {
    const { theme } = e.detail;
    console.log('主题已变更为:', theme);

    // 更新页面主题
    this.setData({ theme });
  },

  // 数据收集设置变更
  onDataCollectionChange: function (e) {
    const allow = e.detail.value;

    this.setData({
      allowDataCollection: allow
    });

    // 保存设置到本地存储
    wx.setStorageSync('allowDataCollection', allow);

    wx.showToast({
      title: allow ? '已开启数据收集' : '已关闭数据收集',
      icon: 'none',
      duration: 1500
    });
  },

  // 清除缓存
  clearCache: function () {
    wx.showModal({
      title: '确认清除缓存',
      content: '这将清除所有本地缓存数据，但不会影响您的账号设置',
      success: res => {
        if (res.confirm) {
          // 清除除了用户设置之外的缓存
          const settingsToKeep = {
            interfaceStyle: wx.getStorageSync('interfaceStyle'),
            themeMode: wx.getStorageSync('themeMode'),
            allowDataCollection: wx.getStorageSync('allowDataCollection')
          };

          // 清除缓存
          wx.clearStorageSync();

          // 恢复保留的设置
          for (const key in settingsToKeep) {
            if (settingsToKeep[key] !== undefined) {
              wx.setStorageSync(key, settingsToKeep[key]);
            }
          }

          wx.showToast({
            title: '缓存已清除',
            icon: 'success',
            duration: 1500
          });
        }
      }
    });
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出当前账号吗？',
      success: async res => {
        if (res.confirm) {
          try {
            // 显示加载中
            wx.showLoading({
              title: '正在退出...',
              mask: true
            });

            // 使用认证服务进行登出
            await authService.logout();

            // 隐藏加载中
            wx.hideLoading();

            // 返回到首页
            wx.switchTab({
              url: '/pages/index/index'
            });
          } catch (err) {
            console.error('退出登录失败:', err);

            // 隐藏加载中
            wx.hideLoading();

            // 显示错误提示
            wx.showToast({
              title: '退出失败，请稍后再试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 导航到修改密码页面
  navigateToChangePwd: function () {
    wx.navigateTo({
      url: '/pages/profile/change-password'
    });
  },

  // 导航到隐私政策页面
  navigateToPrivacyPolicy: function () {
    wx.navigateTo({
      url: '/pages/profile/privacy-policy'
    });
  },

  // 导航到用户协议页面
  navigateToUserAgreement: function () {
    wx.navigateTo({
      url: '/pages/profile/user-agreement'
    });
  }
});