<!--pages/profile/settings.wxml-->
<view class="container" id="page-container" data-theme="{{theme}}">
  <view class="settings-header">
    <text class="settings-title">账号设置</text>
  </view>

  <!-- 设置区域 -->
  <view class="settings-section">
    <view class="section-title">个性化设置</view>
    
    <!-- 界面样式选择 -->
    <view class="setting-item">
      <view class="setting-label">界面样式</view>
      <view class="setting-value">
        <picker bindchange="onInterfaceStyleChange" value="{{interfaceStyleIndex}}" range="{{interfaceStyles}}">
          <view class="picker-value">
            {{interfaceStyles[interfaceStyleIndex]}}
            <text class="arrow-right">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 主题管理器组件替代原主题模式选择器 -->
    <theme-manager bindthemechange="onThemeChanged"></theme-manager>
  </view>

  <view class="settings-section">
    <view class="section-title">隐私设置</view>
    
    <!-- 数据收集 -->
    <view class="setting-item">
      <view class="setting-label">学习数据收集</view>
      <view class="setting-value">
        <switch checked="{{allowDataCollection}}" bindchange="onDataCollectionChange" color="#3B82F6" />
      </view>
    </view>
  </view>

  <view class="settings-section">
    <view class="section-title">账号管理</view>
    
    <!-- 修改密码 -->
    <view class="setting-item" bindtap="navigateToChangePwd">
      <view class="setting-label">修改密码</view>
      <view class="setting-value">
        <text class="arrow-right">›</text>
      </view>
    </view>
    
    <!-- 隐私政策 -->
    <view class="setting-item" bindtap="navigateToPrivacyPolicy">
      <view class="setting-label">隐私政策</view>
      <view class="setting-value">
        <text class="arrow-right">›</text>
      </view>
    </view>
    
    <!-- 用户协议 -->
    <view class="setting-item" bindtap="navigateToUserAgreement">
      <view class="setting-label">用户协议</view>
      <view class="setting-value">
        <text class="arrow-right">›</text>
      </view>
    </view>
  </view>

  <!-- 清除缓存按钮 -->
  <view class="action-button-container">
    <button class="action-button" bindtap="clearCache">清除缓存</button>
  </view>

  <!-- 退出登录按钮 -->
  <view class="action-button-container">
    <button class="action-button logout-button" bindtap="logout">退出登录</button>
  </view>
</view> 