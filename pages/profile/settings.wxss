/* pages/profile/settings.wxss */

.container {
  padding: 0 30rpx;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  background-color: var(--bg-gradient-start);
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.settings-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.settings-section {
  background-color: var(--card-bg);
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx var(--card-shadow);
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 20rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid var(--divider-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 30rpx;
  color: var(--text-primary);
}

.setting-value {
  display: flex;
  align-items: center;
}

.picker-value {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: var(--text-secondary);
}

.arrow-right {
  font-size: 36rpx;
  margin-left: 10rpx;
  color: var(--text-light);
}

.action-button-container {
  margin: 30rpx 0;
}

.action-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: var(--card-bg);
  color: var(--text-primary);
  font-size: 32rpx;
  border-radius: 45rpx;
  box-shadow: 0 2rpx 10rpx var(--card-shadow);
  backdrop-filter: blur(10px);
}

.logout-button {
  background-color: #ff4d4f;
  color: #fff;
}

/* 移除旧的深色模式媒体查询，现在使用CSS变量和data-theme属性实现 */ 