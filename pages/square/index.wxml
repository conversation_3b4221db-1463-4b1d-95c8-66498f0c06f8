<!--pages/square/index.wxml-->
<view class="container">
  <!-- 轮盘式标签栏 -->
  <view class="category-container">
    <!-- 中心高亮区域 - 固定在中间 -->
    <view class="center-highlight">
      <!-- 中心指示器动画效果 -->
      <view class="center-indicator"></view>

      <!-- 中心底部指示器 -->
      <view class="center-bottom-indicator"></view>
    </view>

    <!-- 左侧渐变遮罩 -->
    <view class="category-mask left"></view>

    <!-- 标签加载指示器 -->
    <view class="tag-loading" wx:if="{{tagLoading}}">
      <view class="loading-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
      <text class="loading-text">加载标签中...</text>
    </view>

    <!-- 使用tag-scroll组件 -->
    <tag-scroll
      id="tag-scroll"
      categories="{{allCategories}}"
      currentCategory="{{currentCategory}}"
      bind:change="onCategoryChange"
      bind:recovery="onTagsRecovered"
      bind:centered="onTagCentered"
      debugMode="{{false}}">
    </tag-scroll>

    <!-- 右侧渐变遮罩 -->
    <view class="category-mask right"></view>
  </view>

  <!-- 瀑布流内容区 -->
  <waterfall-content
    id="waterfall-content"
    currentCategory="{{currentCategory}}">
  </waterfall-content>
</view>