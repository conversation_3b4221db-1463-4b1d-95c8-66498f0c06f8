/* pages/square/index.wxss */
page {
  --theme-color: #3B82F6;
  --bg-color: #f8f8f8;
  --card-color: #fff;
  --text-color: #333;
  --text-secondary: #666;
  --text-light: #999;
  --border-color: rgba(255, 255, 255, 0.2);
  --tabbar-height: 100rpx;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: var(--bg-color);
  padding-bottom: var(--tabbar-height);
}

/* 自定义顶部导航栏 */
.custom-nav-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 深色模式下的导航栏样式 */
page[data-theme="dark"] .custom-nav-bar {
  background-color: var(--graphite-bg); /* 使用石墨色 */
  box-shadow: 0 2rpx 10rpx var(--graphite-shadow);
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  position: relative;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 超炫酷轮播式标签栏 */
.category-container {
  position: relative;
  width: 100%;
  height: 140rpx;
  background: linear-gradient(180deg, rgba(255,255,255,0.18) 0%, rgba(255,255,255,0.08) 100%);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  overflow: hidden;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 中心高亮区域 */
.center-highlight {
  position: absolute;
  left: 50%;
  top: 0;
  height: 100%;
  width: 140rpx;
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 3;
}

/* 中心指示器 - 高亮效果 - 增强版 */
.center-indicator {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: radial-gradient(
    ellipse at center,
    rgba(59, 130, 246, 0.15) 0%,    /* 增强中心亮度 */
    rgba(59, 130, 246, 0.08) 40%,   /* 增强中间区域亮度 */
    rgba(255, 255, 255, 0) 80%
  );

  /* 添加更平滑的脉冲动画效果 */
  animation: pulse 3s infinite cubic-bezier(0.4, 0, 0.2, 1);

  /* 添加微弱的发光效果 */
  box-shadow: inset 0 0 20rpx rgba(59, 130, 246, 0.2);

  /* 确保指示器在最上层 */
  z-index: 4;
}

/* 中心底部指示器 - 横线 - 增强版 */
.center-bottom-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #3B82F6, #60a5fa);
  border-radius: 6rpx;
  transform: translateX(-50%);

  /* 增强阴影效果 */
  box-shadow:
    0 2rpx 10rpx rgba(59, 130, 246, 0.6),
    0 0 4rpx rgba(255, 255, 255, 0.8);

  /* 添加更平滑的呼吸动画效果 */
  animation: breathe 2s infinite cubic-bezier(0.4, 0, 0.2, 1);

  /* 确保指示器在最上层 */
  z-index: 5;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    width: 100%;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    width: 110%;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.7;
    width: 100%;
    transform: scale(1);
  }
}

@keyframes breathe {
  0% {
    opacity: 0.7;
    width: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(59, 130, 246, 0.4);
  }
  50% {
    opacity: 1;
    width: 50rpx;
    box-shadow: 0 2rpx 15rpx rgba(59, 130, 246, 0.7), 0 0 5rpx rgba(255, 255, 255, 0.9);
  }
  100% {
    opacity: 0.7;
    width: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(59, 130, 246, 0.4);
  }
}

/* 左右渐变遮罩 - 增强版 */
.category-mask {
  position: absolute;
  top: 0;
  width: 200rpx;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.category-mask.left {
  left: 0;
  background: linear-gradient(90deg,
    rgba(255,255,255,0.25) 0%,
    rgba(255,255,255,0.15) 40%,
    rgba(255,255,255,0) 100%);
}

.category-mask.right {
  right: 0;
  background: linear-gradient(270deg,
    rgba(255,255,255,0.25) 0%,
    rgba(255,255,255,0.15) 40%,
    rgba(255,255,255,0) 100%);
}

/* 页面级别的滚动容器样式，不应影响组件内部样式 */
.page-category-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  z-index: 1;
}

/* 左右占位元素，确保首尾元素可以居中 */
.category-spacer {
  display: inline-block;
  width: calc(50vw - 50rpx); /* 调整宽度确保推荐标签居中 */
  height: 1px;
  flex-shrink: 0; /* 防止被压缩 */
}

.category-list {
  display: inline-flex;
  align-items: center;
  height: 100%;
  padding: 0;
}

/* 页面级别的标签样式，不应影响组件内部样式 */
.page-category-item {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  padding: 0 30rpx;
  margin: 0 15rpx;
  transition: all 0.25s cubic-bezier(0.25, 0.1, 0.25, 1);
  transform-style: preserve-3d;
  perspective: 1000rpx;
  will-change: transform, opacity;
  /* 默认状态是缩小的 */
  transform: scale(0.8);
  opacity: 0.6;
}

.category-item-inner {
  font-size: 28rpx;
  color: rgba(80, 80, 80, 0.7);
  font-weight: 400;
  transition: all 0.25s cubic-bezier(0.25, 0.1, 0.25, 1);
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.05);
  letter-spacing: 1rpx;
  white-space: nowrap;
}

/* 当标签在中心位置时 */
.category-item.center {
  transform: scale(1.3) translateZ(30rpx);
  opacity: 1;
  z-index: 5;
}

.category-item.center .category-item-inner {
  color: var(--theme-color);
  font-weight: 600;
  text-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
  letter-spacing: 2rpx;
}

/* 当标签被选中时 */
.category-item.active .category-item-inner {
  color: var(--theme-color);
}

/* 底部指示器 - 仅在选中时显示 */
.category-indicator {
  position: absolute;
  bottom: 6rpx;
  width: 40rpx;
  height: 8rpx;
  background: linear-gradient(90deg, #3B82F6, #60a5fa);
  border-radius: 8rpx;
  transform: scaleX(0);
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: indicatorAppear 0.5s forwards;
  box-shadow: 0 2rpx 10rpx rgba(59, 130, 246, 0.4);
}

@keyframes indicatorAppear {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    transform: scaleX(1.2);
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

/* 悬停效果 */
.category-item:active {
  opacity: 0.8;
}

/* 瀑布流容器 */
.waterfall-container {
  padding: 20rpx 16rpx;
  box-sizing: border-box;
  height: calc(100vh - 80rpx - var(--tabbar-height));
}

/* 瀑布流布局 */
.waterfall {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.waterfall-column {
  width: 48.5%;
  display: flex;
  flex-direction: column;
}

/* 卡片样式 */
.card {
  margin-bottom: 20rpx;
  background-color: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: transform 0.2s ease;
  padding: 0;
}

.card:active {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.3);
}

.post-image {
  width: 100%;
  height: auto;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  background-color: #f5f5f5;
  display: block;
}

.post-content {
  padding: 20rpx;
}

.post-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 10rpx;
  line-height: 1.4;
  /* 最多两行，超出省略 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16rpx;
  /* 最多三行，超出省略 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-user {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.user-avatar {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.user-name {
  font-size: 24rpx;
  color: var(--text-secondary);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.like-container {
  display: flex;
  align-items: center;
}

.like-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 6rpx;
}

.like-count {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 分类标签样式 */
.post-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-top: 16rpx;
}

/* 各分类标签颜色 */
.listening {
  background-color: #3B82F6;
}

.empathy {
  background-color: #8B5CF6;
}

.expression {
  background-color: #22c55e;
}

.appreciation {
  background-color: #F59E0B;
}

.feedback {
  background-color: #06b6d4;
}

/* 点赞图标样式 */
.like-icon {
  font-size: 28rpx;
  color: #ccc;
  margin-right: 6rpx;
}

.like-icon.liked {
  color: #ff4757;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  width: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(59, 130, 246, 0.1);
  border-top-color: var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
  border-width: 4rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: var(--text-light);
}

.loading-text.small {
  font-size: 24rpx;
  margin-top: 10rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 加载更多 */
.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
  color: var(--text-light);
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 30rpx;
}

.refresh-button {
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  color: #fff;
  background-color: var(--theme-color);
  border-radius: 40rpx;
  border: none;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片动画 */
.post-item {
  animation: fadeIn 0.5s ease-out forwards;
}

/* 标签加载状态样式 */
.tag-loading {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #6366F1;
  margin: 0 8rpx;
  animation: dot-bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-text {
  font-size: 24rpx;
  color: #6366F1;
}

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}