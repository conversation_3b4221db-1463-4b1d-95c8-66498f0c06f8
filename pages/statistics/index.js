// pages/statistics/index.js

// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    statistics: null,
    dailyRecords: [],
    currentTab: 'overview', // 当前标签页：overview, daily, activities, trend
    activities: [],
    activitiesPage: 1,
    activitiesPageSize: 20,
    hasMoreActivities: true,
    isLoadingActivities: false,
    trendDays: 30, // 趋势天数
    trendData: [], // 趋势数据
    averageLearningTime: 0, // 平均学习时间
    maxLearningTime: 0, // 最长学习时间
    effectiveLearningDays: 0 // 有效学习天数
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 如果是概览标签页，使用概览API
    if (this.data.currentTab === 'overview') {
      this.loadOverview();
    } else {
      this.loadStatistics();
      this.loadDailyRecords();
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 记录查看统计页面的活动
    this.recordViewActivity();
  },

  /**
   * 加载学习概览
   */
  loadOverview: function () {
    this.setData({ isLoading: true });

    return statisticsAdapter.getLearningOverview()
      .then(res => {
        if (res.success && res.data) {
          this.setData({
            statistics: res.data.statistics,
            dailyRecords: res.data.recentTrend || [],
            isLoading: false
          });
        } else {
          this.setData({ isLoading: false });
          wx.showToast({
            title: '获取学习概览失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取学习概览失败', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '获取学习概览失败',
          icon: 'none'
        });
      });
  },

  /**
   * 加载学习统计数据
   */
  loadStatistics: function () {
    this.setData({ isLoading: true });

    statisticsAdapter.getLearningStatistics()
      .then(res => {
        if (res.success && res.data) {
          this.setData({
            statistics: res.data,
            isLoading: false
          });
        } else {
          this.setData({ isLoading: false });
          wx.showToast({
            title: '获取统计数据失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取学习统计数据失败', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '获取统计数据失败',
          icon: 'none'
        });
      });
  },

  /**
   * 加载每日学习记录
   */
  loadDailyRecords: function () {
    // 获取最近30天的记录
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const params = {
      startDate: this.formatDate(thirtyDaysAgo),
      endDate: this.formatDate(today)
    };

    statisticsAdapter.getDailyRecords(params)
      .then(res => {
        if (res.success && res.data && res.data.records) {
          this.setData({
            dailyRecords: res.data.records
          });
        } else {
          wx.showToast({
            title: '获取每日记录失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取每日学习记录失败', err);
        wx.showToast({
          title: '获取每日记录失败',
          icon: 'none'
        });
      });
  },

  /**
   * 加载学习活动列表
   */
  loadActivities: function (refresh = false) {
    if (this.data.isLoadingActivities && !refresh) return;

    const page = refresh ? 1 : this.data.activitiesPage;

    this.setData({
      isLoadingActivities: true
    });

    const params = {
      page,
      pageSize: this.data.activitiesPageSize
    };

    statisticsAdapter.getActivities(params)
      .then(res => {
        if (res.success && res.data) {
          let activities = refresh ? res.data.activities : [...this.data.activities, ...res.data.activities];

          // 预处理活动类型
          activities = activities.map(activity => ({
            ...activity,
            activityTypeText: this.formatActivityType(activity.activityType)
          }));

          this.setData({
            activities,
            activitiesPage: page + 1,
            hasMoreActivities: activities.length < res.data.pagination.total,
            isLoadingActivities: false
          });
        } else {
          this.setData({ isLoadingActivities: false });
          wx.showToast({
            title: '获取活动列表失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取学习活动列表失败', err);
        this.setData({ isLoadingActivities: false });
        wx.showToast({
          title: '获取活动列表失败',
          icon: 'none'
        });
      });
  },

  /**
   * 切换标签页
   */
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;

    if (tab === this.data.currentTab) return;

    this.setData({ currentTab: tab });

    // 根据标签加载相应数据
    if (tab === 'overview') {
      this.loadOverview();
    } else if (tab === 'activities' && this.data.activities.length === 0) {
      this.loadActivities(true);
    } else if (tab === 'trend' && this.data.trendData.length === 0) {
      this.loadTrend();
    }
  },

  /**
   * 加载更多活动
   */
  loadMoreActivities: function () {
    if (!this.data.hasMoreActivities || this.data.isLoadingActivities) return;
    this.loadActivities();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    // 根据当前标签刷新相应数据
    if (this.data.currentTab === 'overview') {
      this.loadOverview().finally(() => {
        wx.stopPullDownRefresh();
      });
    } else if (this.data.currentTab === 'activities') {
      this.loadActivities(true);
      wx.stopPullDownRefresh();
    } else if (this.data.currentTab === 'trend') {
      this.loadTrend().finally(() => {
        wx.stopPullDownRefresh();
      });
    } else {
      Promise.all([
        this.loadStatistics(),
        this.loadDailyRecords()
      ]).finally(() => {
        wx.stopPullDownRefresh();
      });
    }
  },

  /**
   * 加载学习趋势
   */
  loadTrend: function () {
    this.setData({ isLoading: true });

    return statisticsAdapter.getLearningTrend(this.data.trendDays)
      .then(res => {
        if (res.success && res.data && res.data.trend) {
          const trendData = res.data.trend;

          // 计算趋势摘要数据
          let averageLearningTime = 0;
          let maxLearningTime = 0;
          let effectiveLearningDays = 0;

          if (trendData.length > 0) {
            // 计算平均学习时间
            const totalTime = trendData.reduce((sum, item) => sum + item.timeSpent, 0);
            averageLearningTime = (totalTime / trendData.length).toFixed(1);

            // 计算最长学习时间
            maxLearningTime = Math.max(...trendData.map(item => item.timeSpent));

            // 计算有效学习天数
            effectiveLearningDays = trendData.filter(item => item.timeSpent > 0).length;
          }

          this.setData({
            trendData,
            averageLearningTime,
            maxLearningTime,
            effectiveLearningDays,
            isLoading: false
          });
        } else {
          this.setData({
            isLoading: false,
            averageLearningTime: 0,
            maxLearningTime: 0,
            effectiveLearningDays: 0
          });
          wx.showToast({
            title: '获取学习趋势失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取学习趋势失败', err);
        this.setData({
          isLoading: false,
          averageLearningTime: 0,
          maxLearningTime: 0,
          effectiveLearningDays: 0
        });
        wx.showToast({
          title: '获取学习趋势失败',
          icon: 'none'
        });
      });
  },

  /**
   * 更新趋势天数
   */
  updateTrendDays: function (e) {
    const days = parseInt(e.detail.value);
    if (days !== this.data.trendDays) {
      this.setData({ trendDays: days });
      this.loadTrend();
    }
  },

  /**
   * 记录查看统计页面的活动
   */
  recordViewActivity: function () {
    statisticsAdapter.recordLearningActivity({
      activityType: 'view_insight',
      contentType: 'plan',
      details: {
        view: 'statistics',
        tab: this.data.currentTab
      }
    }).catch(err => {
      console.error('记录学习活动失败', err);
    });
  },

  /**
   * 格式化日期为YYYY-MM-DD
   */
  formatDate: function (date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化活动类型为中文
   */
  formatActivityType: function (type) {
    const typeMap = {
      'login': '登录',
      'view_exercise': '查看练习',
      'complete_exercise': '完成练习',
      'view_insight': '查看观点',
      'create_note': '创建笔记',
      'like_note': '点赞笔记',
      'comment_note': '评论笔记',
      'bubble_interaction': '泡泡互动',
      'share_content': '分享内容'
    };
    return typeMap[type] || type;
  }
});
