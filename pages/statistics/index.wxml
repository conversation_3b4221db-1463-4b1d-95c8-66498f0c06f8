<!--pages/statistics/index.wxml-->
<view class="container">
  <!-- 顶部标签栏 -->
  <view class="tabs">
    <view class="tab {{currentTab === 'overview' ? 'active' : ''}}" bindtap="switchTab" data-tab="overview">总览</view>
    <view class="tab {{currentTab === 'daily' ? 'active' : ''}}" bindtap="switchTab" data-tab="daily">每日记录</view>
    <view class="tab {{currentTab === 'activities' ? 'active' : ''}}" bindtap="switchTab" data-tab="activities">学习活动</view>
    <view class="tab {{currentTab === 'trend' ? 'active' : ''}}" bindtap="switchTab" data-tab="trend">学习趋势</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>

  <!-- 总览标签页 -->
  <view class="tab-content" wx:if="{{currentTab === 'overview' && !isLoading}}">
    <view class="stats-card">
      <view class="stats-header">学习概况</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{statistics.totalStudyDays || 0}}</text>
          <text class="stats-label">总学习天数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.currentStreak || 0}}</text>
          <text class="stats-label">当前连续天数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.longestStreak || 0}}</text>
          <text class="stats-label">最长连续天数</text>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="stats-header">学习内容</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{statistics.completedExercises || 0}}</text>
          <text class="stats-label">完成练习</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.viewedInsights || 0}}</text>
          <text class="stats-label">查看观点</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.createdNotes || 0}}</text>
          <text class="stats-label">创建笔记</text>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="stats-header">学习计划</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{statistics.activePlans || 0}}</text>
          <text class="stats-label">活跃计划</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.completedPlans || 0}}</text>
          <text class="stats-label">完成计划</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.totalTimeSpent || 0}}</text>
          <text class="stats-label">总学习时间(分钟)</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 每日记录标签页 -->
  <view class="tab-content" wx:if="{{currentTab === 'daily' && !isLoading}}">
    <view class="daily-list">
      <view class="daily-item" wx:for="{{dailyRecords}}" wx:key="date">
        <view class="daily-date">{{item.date}}</view>
        <view class="daily-details">
          <view class="daily-detail">
            <text class="detail-label">学习时间:</text>
            <text class="detail-value">{{item.timeSpent}}分钟</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">完成练习:</text>
            <text class="detail-value">{{item.exercisesCompleted}}个</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">查看观点:</text>
            <text class="detail-value">{{item.insightsViewed}}个</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">创建笔记:</text>
            <text class="detail-value">{{item.notesCreated}}个</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">泡泡互动:</text>
            <text class="detail-value">{{item.bubbleInteractions}}次</text>
          </view>
        </view>
      </view>
      <view class="empty-tip" wx:if="{{dailyRecords.length === 0}}">
        暂无每日学习记录
      </view>
    </view>
  </view>

  <!-- 学习活动标签页 -->
  <view class="tab-content" wx:if="{{currentTab === 'activities' && !isLoading}}">
    <view class="activity-list">
      <view class="activity-item" wx:for="{{activities}}" wx:key="id">
        <view class="activity-time">{{item.createdAt}}</view>
        <view class="activity-content">
          <view class="activity-type">{{item.activityTypeText || item.activityType}}</view>
          <view class="activity-detail" wx:if="{{item.planTitle}}">
            <text class="detail-label">学习计划:</text>
            <text class="detail-value">{{item.planTitle}}</text>
          </view>
          <view class="activity-detail" wx:if="{{item.contentType}}">
            <text class="detail-label">内容类型:</text>
            <text class="detail-value">{{item.contentType}}</text>
          </view>
          <view class="activity-detail" wx:if="{{item.duration}}">
            <text class="detail-label">持续时间:</text>
            <text class="detail-value">{{item.duration}}秒</text>
          </view>
        </view>
      </view>
      <view class="load-more" wx:if="{{hasMoreActivities && !isLoadingActivities}}" bindtap="loadMoreActivities">
        加载更多
      </view>
      <view class="loading" wx:if="{{isLoadingActivities}}">
        加载中...
      </view>
      <view class="empty-tip" wx:if="{{activities.length === 0 && !isLoadingActivities}}">
        暂无学习活动记录
      </view>
    </view>
  </view>

  <!-- 学习趋势标签页 -->
  <view class="tab-content" wx:if="{{currentTab === 'trend' && !isLoading}}">
    <view class="trend-controls">
      <view class="trend-selector">
        <text>天数：</text>
        <picker mode="selector" range="{{[7, 14, 30, 60, 90]}}" value="{{2}}" bindchange="updateTrendDays">
          <view class="picker">
            {{trendDays}}天
          </view>
        </picker>
      </view>
    </view>

    <view class="trend-chart">
      <!-- 这里可以使用微信小程序的图表组件或自定义实现 -->
      <view class="trend-data">
        <view class="trend-item" wx:for="{{trendData}}" wx:key="date">
          <view class="trend-date">{{item.date}}</view>
          <view class="trend-bar" style="height: {{item.timeSpent > 0 ? (item.timeSpent > 100 ? 100 : item.timeSpent) : 5}}px"></view>
          <view class="trend-value">{{item.timeSpent}}分钟</view>
        </view>
      </view>
    </view>

    <view class="trend-summary">
      <view class="trend-summary-item">
        <text class="summary-label">平均学习时间：</text>
        <text class="summary-value">{{averageLearningTime}}分钟/天</text>
      </view>
      <view class="trend-summary-item">
        <text class="summary-label">最长学习时间：</text>
        <text class="summary-value">{{maxLearningTime}}分钟</text>
      </view>
      <view class="trend-summary-item">
        <text class="summary-label">有效学习天数：</text>
        <text class="summary-value">{{effectiveLearningDays}}/{{trendData.length}}天</text>
      </view>
    </view>

    <view class="empty-tip" wx:if="{{trendData.length === 0}}">
      暂无学习趋势数据
    </view>
  </view>
</view>
