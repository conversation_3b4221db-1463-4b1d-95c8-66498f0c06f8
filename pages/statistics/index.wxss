/* pages/statistics/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 标签栏样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #3B82F6;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #3B82F6;
  border-radius: 3rpx;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 统计卡片样式 */
.stats-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-header {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 15rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #3B82F6;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 每日记录样式 */
.daily-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.daily-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.daily-item:last-child {
  border-bottom: none;
}

.daily-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.daily-details {
  display: flex;
  flex-wrap: wrap;
}

.daily-detail {
  width: 50%;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.detail-label {
  color: #666;
  margin-right: 10rpx;
}

.detail-value {
  color: #3B82F6;
  font-weight: 500;
}

/* 活动列表样式 */
.activity-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activity-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.activity-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.activity-detail {
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.load-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #3B82F6;
}

.empty-tip {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 趋势页面样式 */
.trend-controls {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-selector {
  display: flex;
  align-items: center;
}

.trend-selector text {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.picker {
  font-size: 28rpx;
  color: #3B82F6;
  font-weight: 500;
  padding: 5rpx 20rpx;
  background-color: #f0f7ff;
  border-radius: 8rpx;
}

.trend-chart {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.trend-data {
  display: flex;
  align-items: flex-end;
  height: 240rpx;
  padding: 20rpx 0;
  overflow-x: auto;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
  min-width: 60rpx;
}

.trend-date {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 10rpx;
  transform: rotate(-45deg);
  white-space: nowrap;
  margin-top: 20rpx;
}

.trend-bar {
  width: 30rpx;
  background: linear-gradient(to top, #3B82F6, #60a5fa);
  border-radius: 4rpx;
  transition: height 0.3s;
}

.trend-value {
  font-size: 20rpx;
  color: #666;
  margin-top: 10rpx;
}

.trend-summary {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.trend-summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.summary-label {
  color: #666;
}

.summary-value {
  color: #3B82F6;
  font-weight: 500;
}
