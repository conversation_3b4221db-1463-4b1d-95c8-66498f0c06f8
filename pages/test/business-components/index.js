// pages/test/business-components/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 测试数据
    isLoading: false,

    // 学习计划测试数据
    testPlan: {
      id: 1,
      title: '人际沟通技巧提升计划',
      description: '通过系统学习提升沟通能力，改善人际关系',
      themeName: '人际沟通',
      themeColor: '#3B82F6',
      status: 'in_progress',
      progress: 65,
      createdAt: '2025-05-15',
      tags: ['沟通技巧', '倾听', '表达', '冲突处理'],
      targetDays: 7,
      completedDays: 4,
      startDate: '2025-05-15'
    },

    // 内容测试数据
    testExercise: {
      id: 1,
      title: '有效倾听练习',
      content: '请描述一次你认真倾听他人并理解对方感受的经历，以及这如何影响了沟通结果？',
      type: 'text',
      difficulty: 2,
      tags: ['倾听', '共情'],
      createdAt: '2025-05-16',
      status: 'not_completed'
    },

    testInsight: {
      id: 2,
      content: '真正的倾听不仅是听到对方说的话，更是理解对方未说出口的感受和需求。',
      source: '《非暴力沟通》',
      tags: ['倾听', '共情'],
      createdAt: '2025-05-17'
    },

    testNote: {
      id: 3,
      title: '有效沟通的五个要素',
      content: '1. 专注倾听\n2. 清晰表达\n3. 情绪管理\n4. 提问技巧\n5. 非语言沟通',
      tags: ['沟通技巧', '笔记'],
      createdAt: '2025-05-18'
    },

    // 用户测试数据
    testUser: {
      id: 'user123',
      nickName: '学习达人',
      avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
      level: 5,
      expPoints: 2500,
      studyDays: 45,
      completedPlans: 8,
      badges: 12
    },

    // 成就和徽章测试数据
    testAchievements: [
      {
        id: 1,
        name: '学习先锋',
        description: '完成第一个学习计划',
        iconUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        unlocked: true,
        unlockedAt: '2025-04-20'
      },
      {
        id: 2,
        name: '知识探索者',
        description: '完成5个不同主题的学习计划',
        iconUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        unlocked: true,
        unlockedAt: '2025-05-10'
      },
      {
        id: 3,
        name: '学习大师',
        description: '累计学习时间超过100小时',
        iconUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        unlocked: false
      }
    ],

    testBadges: [
      {
        id: 1,
        name: '沟通达人',
        description: '完成人际沟通主题的所有学习计划',
        iconUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        unlocked: true,
        unlockedAt: '2025-05-12'
      },
      {
        id: 2,
        name: '思考者',
        description: '创建超过30条观点',
        iconUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        unlocked: true,
        unlockedAt: '2025-05-05'
      },
      {
        id: 3,
        name: '笔记大师',
        description: '创建超过50条笔记',
        iconUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        unlocked: false
      }
    ],

    // 模态框控制
    showContentModal: false,
    currentContentType: '',
    currentContentData: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 页面加载时的逻辑
  },

  /**
   * 学习计划卡片点击事件
   */
  handlePlanClick: function (e) {
    const { plan } = e.detail;
    console.log('点击了学习计划卡片:', plan);
    wx.showToast({
      title: '点击了学习计划卡片',
      icon: 'none'
    });
  },

  /**
   * 查看学习计划详情事件
   */
  handleViewPlanDetail: function (e) {
    const { plan } = e.detail;
    console.log('查看学习计划详情:', plan);
    wx.showToast({
      title: '查看学习计划详情',
      icon: 'none'
    });
  },

  /**
   * 继续学习事件
   */
  handleContinueLearning: function (e) {
    const { plan } = e.detail;
    console.log('继续学习:', plan);
    wx.showToast({
      title: '继续学习',
      icon: 'none'
    });
  },

  /**
   * 学习日期点击事件
   */
  handleDayClick: function (e) {
    const { day } = e.detail;
    console.log('点击了学习日期:', day);
    wx.showToast({
      title: `点击了第${day}天`,
      icon: 'none'
    });
  },

  /**
   * 内容卡片点击事件
   */
  handleContentClick: function (e) {
    const { contentType, contentData } = e.detail;
    console.log(`点击了${contentType}卡片:`, contentData);

    // 打开内容模态框
    this.setData({
      showContentModal: true,
      currentContentType: contentType,
      currentContentData: contentData
    });
  },

  /**
   * 查看内容详情事件
   */
  handleViewContent: function (e) {
    const { contentType, contentData } = e.detail;
    console.log(`查看${contentType}详情:`, contentData);

    // 打开内容模态框
    this.setData({
      showContentModal: true,
      currentContentType: contentType,
      currentContentData: contentData
    });
  },

  /**
   * 编辑内容事件
   */
  handleEditContent: function (e) {
    const { contentType, contentData } = e.detail;
    console.log(`编辑${contentType}:`, contentData);
    wx.showToast({
      title: `编辑${contentType}`,
      icon: 'none'
    });
  },

  /**
   * 删除内容事件
   */
  handleDeleteContent: function (e) {
    const { contentType, contentData } = e.detail;
    console.log(`删除${contentType}:`, contentData);
    wx.showToast({
      title: `删除${contentType}`,
      icon: 'none'
    });
  },

  /**
   * 关闭内容模态框事件
   */
  handleCloseModal: function () {
    this.setData({
      showContentModal: false
    });
  },

  /**
   * 提交内容事件
   */
  handleSubmitContent: function (e) {
    const { contentType, contentId, answer } = e.detail;
    console.log(`提交${contentType}答案:`, { contentId, answer });
  },

  /**
   * 完成内容事件
   */
  handleCompleteContent: function (e) {
    const { contentType, contentId } = e.detail;
    console.log(`完成${contentType}:`, contentId);
  },

  /**
   * 用户头像点击事件
   */
  handleAvatarClick: function (e) {
    const { user } = e.detail;
    console.log('点击了用户头像:', user);
    wx.showToast({
      title: '点击了用户头像',
      icon: 'none'
    });
  },

  /**
   * 用户名点击事件
   */
  handleNameClick: function (e) {
    const { user } = e.detail;
    console.log('点击了用户名:', user);
    wx.showToast({
      title: '点击了用户名',
      icon: 'none'
    });
  },

  /**
   * 用户等级点击事件
   */
  handleLevelClick: function (e) {
    const { level, expPoints } = e.detail;
    console.log('点击了用户等级:', { level, expPoints });
    wx.showToast({
      title: `点击了Lv.${level}`,
      icon: 'none'
    });
  },

  /**
   * 成就/徽章点击事件
   */
  handleItemClick: function (e) {
    const { type, item } = e.detail;
    console.log(`点击了${type}:`, item);
    wx.showToast({
      title: `点击了${type}: ${item.name}`,
      icon: 'none'
    });
  }
});
