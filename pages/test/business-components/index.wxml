<!--pages/test/business-components/index.wxml-->
<view class="container">
  <view class="page-header">
    <view class="page-title">业务组件测试</view>
    <view class="page-subtitle">测试各种业务组件的功能和样式</view>
  </view>

  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 组件测试区域 -->
  <view class="test-container" wx:else>
    <!-- 学习计划相关组件 -->
    <view class="section">
      <view class="section-title">学习计划相关组件</view>

      <!-- 学习计划卡片 -->
      <view class="subsection">
        <view class="subsection-title">学习计划卡片</view>
        <learning-plan-card
          plan="{{testPlan}}"
          bind:click="handlePlanClick"
          bind:detail="handleViewPlanDetail"
          bind:continue="handleContinueLearning"
        ></learning-plan-card>
      </view>

      <!-- 学习进度可视化 -->
      <view class="subsection">
        <view class="subsection-title">学习进度可视化（完整版）</view>
        <learning-progress
          plan="{{testPlan}}"
          type="full"
          bind:dayclick="handleDayClick"
        ></learning-progress>
      </view>

      <view class="subsection">
        <view class="subsection-title">学习进度可视化（简化版）</view>
        <learning-progress
          plan="{{testPlan}}"
          type="simple"
          bind:dayclick="handleDayClick"
        ></learning-progress>
      </view>

      <view class="subsection">
        <view class="subsection-title">学习进度可视化（日历版）</view>
        <learning-progress
          plan="{{testPlan}}"
          type="calendar"
          bind:dayclick="handleDayClick"
        ></learning-progress>
      </view>
    </view>

    <!-- 内容展示相关组件 -->
    <view class="section">
      <view class="section-title">内容展示相关组件</view>

      <!-- 内容卡片 -->
      <view class="subsection">
        <view class="subsection-title">练习卡片</view>
        <content-card
          contentType="exercise"
          contentData="{{testExercise}}"
          bind:click="handleContentClick"
          bind:view="handleViewContent"
          bind:edit="handleEditContent"
          bind:delete="handleDeleteContent"
        ></content-card>
      </view>

      <view class="subsection">
        <view class="subsection-title">观点卡片</view>
        <content-card
          contentType="insight"
          contentData="{{testInsight}}"
          bind:click="handleContentClick"
          bind:view="handleViewContent"
          bind:edit="handleEditContent"
          bind:delete="handleDeleteContent"
        ></content-card>
      </view>

      <view class="subsection">
        <view class="subsection-title">笔记卡片</view>
        <content-card
          contentType="note"
          contentData="{{testNote}}"
          bind:click="handleContentClick"
          bind:view="handleViewContent"
          bind:edit="handleEditContent"
          bind:delete="handleDeleteContent"
        ></content-card>
      </view>

      <!-- 内容模态弹窗（通过点击卡片触发） -->
      <content-modal
        visible="{{showContentModal}}"
        contentType="{{currentContentType}}"
        contentData="{{currentContentData}}"
        bind:close="handleCloseModal"
        bind:submit="handleSubmitContent"
        bind:complete="handleCompleteContent"
      ></content-modal>
    </view>

    <!-- 用户中心相关组件 -->
    <view class="section">
      <view class="section-title">用户中心相关组件</view>

      <!-- 用户信息展示 -->
      <view class="subsection">
        <view class="subsection-title">用户信息展示（完整版）</view>
        <user-profile
          userData="{{testUser}}"
          type="full"
          bind:avatarclick="handleAvatarClick"
          bind:nameclick="handleNameClick"
          bind:levelclick="handleLevelClick"
        ></user-profile>
      </view>

      <view class="subsection">
        <view class="subsection-title">用户信息展示（简化版）</view>
        <user-profile
          userData="{{testUser}}"
          type="simple"
          bind:avatarclick="handleAvatarClick"
          bind:nameclick="handleNameClick"
          bind:levelclick="handleLevelClick"
        ></user-profile>
      </view>

      <view class="subsection">
        <view class="subsection-title">用户信息展示（迷你版）</view>
        <user-profile
          userData="{{testUser}}"
          type="mini"
          bind:avatarclick="handleAvatarClick"
          bind:nameclick="handleNameClick"
          bind:levelclick="handleLevelClick"
        ></user-profile>
      </view>

      <!-- 成就与徽章展示 -->
      <view class="subsection">
        <view class="subsection-title">成就与徽章展示（网格视图）</view>
        <achievement-display
          userId="{{testUser.id}}"
          achievements="{{testAchievements}}"
          badges="{{testBadges}}"
          type="grid"
          bind:itemclick="handleItemClick"
        ></achievement-display>
      </view>

      <view class="subsection">
        <view class="subsection-title">成就与徽章展示（列表视图）</view>
        <achievement-display
          userId="{{testUser.id}}"
          achievements="{{testAchievements}}"
          badges="{{testBadges}}"
          type="list"
          bind:itemclick="handleItemClick"
        ></achievement-display>
      </view>

      <!-- 菜单列表 -->
      <view class="subsection">
        <view class="subsection-title">菜单列表</view>
        <navigator url="/pages/test/business-components/menu-list" class="nav-button">
          查看菜单列表组件
        </navigator>
      </view>
    </view>
  </view>
</view>
