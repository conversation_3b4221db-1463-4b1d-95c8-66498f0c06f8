/* pages/test/business-components/index.wxss */
.container {
  padding: 30rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-color-primary);
}

.page-subtitle {
  font-size: 28rpx;
  color: var(--text-color-secondary);
  margin-top: 10rpx;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--grey-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: var(--text-color-secondary);
}

/* 测试区域 */
.test-container {
  width: 100%;
}

/* 区块样式 */
.section {
  margin-bottom: 60rpx;
  padding-bottom: 40rpx;
  border-bottom: 1px solid var(--divider-color);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color-primary);
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid var(--primary-color);
}

.subsection {
  margin-bottom: 40rpx;
}

.subsection-title {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-color-secondary);
  margin-bottom: 20rpx;
}

/* 导航按钮 */
.nav-button {
  display: block;
  background-color: var(--primary-color, #3775F5);
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 20rpx;
}
