// pages/test/business-components/menu-list.js
// 菜单列表组件测试页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    theme: 'light',
    menuItems: [
      {
        id: 'item1',
        text: '个人信息',
        icon: '/assets/icons/new/user.png',
        arrow: true,
        url: '/pages/profile/index'
      },
      {
        id: 'item2',
        text: '消息通知',
        icon: '/assets/icons/new/bell.png',
        badge: {
          type: 'number',
          value: 5,
          color: '#3775F5'
        },
        arrow: true
      },
      {
        id: 'item3',
        text: '学习记录',
        icon: '/assets/icons/new/book.png',
        arrow: true
      },
      {
        type: 'divider'
      },
      {
        type: 'group',
        text: '设置',
        children: [
          {
            id: 'setting1',
            text: '账号安全',
            icon: '/assets/icons/new/shield.png',
            arrow: true
          },
          {
            id: 'setting2',
            text: '隐私设置',
            icon: '/assets/icons/new/lock.png',
            arrow: true
          },
          {
            id: 'setting3',
            text: '通知设置',
            icon: '/assets/icons/new/bell.png',
            badge: {
              type: 'dot',
              color: '#EF4444'
            },
            arrow: true
          }
        ]
      },
      {
        type: 'divider'
      },
      {
        id: 'item4',
        text: '关于我们',
        icon: '/assets/icons/new/info.png',
        arrow: true
      },
      {
        id: 'item5',
        text: '退出登录',
        icon: '/assets/icons/new/logout.png',
        arrow: false
      }
    ]
  },

  /**
   * 切换主题
   */
  toggleTheme() {
    this.setData({
      theme: this.data.theme === 'light' ? 'dark' : 'light'
    });
  },

  /**
   * 处理菜单项点击
   * @param {Object} e - 事件对象
   */
  handleItemClick(e) {
    const item = e.detail.item;
    console.log('点击菜单项:', item);

    // 如果是退出登录
    if (item.id === 'item5') {
      wx.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: res => {
          if (res.confirm) {
            console.log('用户点击确定');
            // 执行退出登录逻辑
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
    }
  }
});
