<!-- pages/test/business-components/menu-list.wxml -->
<!-- 菜单列表组件测试页面 -->

<view class="container {{theme === 'dark' ? 'dark' : ''}}">
  <view class="header">
    <text class="title">菜单列表组件</text>
    <view class="actions">
      <button class="btn-theme" bindtap="toggleTheme">
        {{theme === 'light' ? '切换到暗色主题' : '切换到亮色主题'}}
      </button>
    </view>
  </view>
  
  <view class="content">
    <menu-list 
      items="{{menuItems}}" 
      theme="{{theme}}" 
      bind:itemclick="handleItemClick"
    ></menu-list>
  </view>
</view>
