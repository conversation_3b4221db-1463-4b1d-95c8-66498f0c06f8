/* pages/test/business-components/menu-list.wxss */
/* 菜单列表组件测试页面样式 */

.container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 16px;
  transition: background-color 0.3s;
}

.container.dark {
  background-color: #111827;
}

.header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #1f2937;
}

.container.dark .title {
  color: #f9fafb;
}

.actions {
  display: flex;
}

.btn-theme {
  background-color: #3775F5;
  color: #ffffff;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
  line-height: 1;
}

.content {
  margin-bottom: 16px;
}
