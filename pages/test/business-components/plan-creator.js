// pages/test/business-components/plan-creator.js
// 计划创建流程组件测试页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    showCreator: false,
    creatorMode: 'create',
    initialData: null,
    testResults: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 模拟API客户端
    const app = getApp();
    if (!app.globalData.apiClient) {
      app.globalData.apiClient = {
        get: this.mockApiGet,
        post: this.mockApiPost,
        put: this.mockApiPut,
        delete: this.mockApiDelete
      };
    }
  },

  /**
   * 显示创建模式
   */
  showCreateMode() {
    this.setData({
      showCreator: true,
      creatorMode: 'create',
      initialData: null
    });
  },

  /**
   * 显示编辑模式
   */
  showEditMode() {
    // 模拟初始数据
    const initialData = {
      themeId: 1,
      title: '测试计划',
      description: '这是一个测试计划的描述',
      targetDays: 14,
      dailyGoalExercises: 5,
      dailyGoalInsights: 7,
      dailyGoalMinutes: 30,
      isPublic: true,
      tags: [
        { id: 1, name: '沟通技巧' },
        { id: 3, name: '演讲' }
      ]
    };

    this.setData({
      showCreator: true,
      creatorMode: 'edit',
      initialData
    });
  },

  /**
   * 处理取消
   */
  handleCancel() {
    this.setData({
      showCreator: false
    });
  },

  /**
   * 处理提交
   */
  handleSubmit(e) {
    const { data } = e.detail;

    console.log('提交计划数据:', data);

    this.setData({
      testResults: {
        type: '提交',
        data
      },
      showCreator: false
    });

    wx.showToast({
      title: '计划创建成功',
      icon: 'success'
    });
  },

  /**
   * 处理保存草稿
   */
  handleSaveDraft(e) {
    const { data } = e.detail;

    console.log('保存草稿数据:', data);

    this.setData({
      testResults: {
        type: '草稿',
        data
      }
    });
  },

  /**
   * 模拟API GET请求
   */
  mockApiGet(url, params) {
    console.log('模拟GET请求:', url, params);

    // 模拟主题数据
    if (url === '/themes') {
      return Promise.resolve({
        data: [
          {
            id: 1,
            name: '人际沟通',
            description: '提升沟通技巧，建立良好人际关系',
            color: '#4CAF50',
            icon: 'message'
          },
          {
            id: 2,
            name: '职场技能',
            description: '掌握职场必备技能，提升职业竞争力',
            color: '#2196F3',
            icon: 'briefcase'
          },
          {
            id: 3,
            name: '个人成长',
            description: '培养良好习惯，实现自我提升',
            color: '#9C27B0',
            icon: 'user'
          },
          {
            id: 4,
            name: '情绪管理',
            description: '学习情绪调节技巧，保持心理健康',
            color: '#FF9800',
            icon: 'heart'
          }
        ]
      });
    }

    // 模拟标签数据
    if (url === '/tags') {
      return Promise.resolve({
        data: [
          { id: 1, name: '沟通技巧', category: '技能' },
          { id: 2, name: '倾听', category: '技能' },
          { id: 3, name: '演讲', category: '技能' },
          { id: 4, name: '情商', category: '能力' },
          { id: 5, name: '冲突处理', category: '能力' },
          { id: 6, name: '团队协作', category: '能力' },
          { id: 7, name: '职场社交', category: '场景' },
          { id: 8, name: '家庭关系', category: '场景' },
          { id: 9, name: '朋友交往', category: '场景' },
          { id: 10, name: '初学者', category: '级别' },
          { id: 11, name: '进阶', category: '级别' },
          { id: 12, name: '专业', category: '级别' }
        ]
      });
    }

    return Promise.reject(new Error('未知的API请求'));
  },

  /**
   * 模拟API POST请求
   */
  mockApiPost(url, data) {
    console.log('模拟POST请求:', url, data);
    return Promise.resolve({ success: true });
  },

  /**
   * 模拟API PUT请求
   */
  mockApiPut(url, data) {
    console.log('模拟PUT请求:', url, data);
    return Promise.resolve({ success: true });
  },

  /**
   * 模拟API DELETE请求
   */
  mockApiDelete(url) {
    console.log('模拟DELETE请求:', url);
    return Promise.resolve({ success: true });
  }
});
