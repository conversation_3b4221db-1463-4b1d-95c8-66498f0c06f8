<!-- 计划创建流程组件测试页面 -->
<view class="container">
  <view class="header">
    <view class="title">计划创建流程组件测试</view>
  </view>
  
  <view class="content">
    <view class="button-group">
      <button class="btn-primary" bindtap="showCreateMode">创建模式</button>
      <button class="btn-secondary" bindtap="showEditMode">编辑模式</button>
    </view>
    
    <view class="test-results" wx:if="{{testResults}}">
      <view class="results-title">测试结果 ({{testResults.type}})</view>
      <view class="results-content">
        <view class="result-item">
          <view class="item-label">主题ID:</view>
          <view class="item-value">{{testResults.data.themeId}}</view>
        </view>
        <view class="result-item">
          <view class="item-label">标题:</view>
          <view class="item-value">{{testResults.data.title}}</view>
        </view>
        <view class="result-item">
          <view class="item-label">描述:</view>
          <view class="item-value">{{testResults.data.description || '无'}}</view>
        </view>
        <view class="result-item">
          <view class="item-label">目标天数:</view>
          <view class="item-value">{{testResults.data.targetDays}}天</view>
        </view>
        <view class="result-item">
          <view class="item-label">每日练习:</view>
          <view class="item-value">{{testResults.data.dailyGoalExercises}}个</view>
        </view>
        <view class="result-item">
          <view class="item-label">每日观点:</view>
          <view class="item-value">{{testResults.data.dailyGoalInsights}}个</view>
        </view>
        <view class="result-item">
          <view class="item-label">每日时间:</view>
          <view class="item-value">{{testResults.data.dailyGoalMinutes}}分钟</view>
        </view>
        <view class="result-item">
          <view class="item-label">可见性:</view>
          <view class="item-value">{{testResults.data.isPublic ? '公开' : '私密'}}</view>
        </view>
        <view class="result-item">
          <view class="item-label">标签:</view>
          <view class="item-value">{{testResults.data.tags.length}}个</view>
        </view>
        <view class="result-item">
          <view class="item-label">模式:</view>
          <view class="item-value">{{testResults.data.mode}}</view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 计划创建流程组件 -->
  <plan-creator
    visible="{{showCreator}}"
    mode="{{creatorMode}}"
    initialData="{{initialData}}"
    bind:cancel="handleCancel"
    bind:submit="handleSubmit"
    bind:draft="handleSaveDraft"
  />
</view>
