/* 计划创建流程组件测试页面样式 */

.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: var(--nl-color-background);
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--nl-color-text);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.button-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn-primary {
  flex: 1;
  background-color: var(--nl-color-primary);
  color: #FFFFFF;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 20rpx 0;
}

.btn-secondary {
  flex: 1;
  background-color: var(--nl-color-background-light);
  color: var(--nl-color-text);
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 20rpx 0;
}

.test-results {
  background-color: var(--nl-color-background-light);
  border-radius: 12rpx;
  padding: 30rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 20rpx;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.result-item {
  display: flex;
  align-items: flex-start;
}

.item-label {
  width: 160rpx;
  font-size: 28rpx;
  color: var(--nl-color-text-secondary);
}

.item-value {
  flex: 1;
  font-size: 28rpx;
  color: var(--nl-color-text);
  word-break: break-all;
}
