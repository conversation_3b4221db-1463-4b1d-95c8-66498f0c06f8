// pages/test/form-components.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // Checkbox测试数据
    checkbox1: false,
    checkbox2: true,

    // Radio测试数据
    radio1: true,
    radio2: false,
    radio3: false,

    // Progress测试数据
    progress1: 30,
    progress2: 50,
    progress3: 70,

    // Slider测试数据
    slider1: 20,
    slider2: 50,

    // Popup测试数据
    showPopup1: false,
    showPopup2: false,
    showPopup3: false,
    showPopup4: false,
    showPopup5: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * Checkbox变化事件
   */
  handleCheckboxChange: function (e) {
    const { value, name } = e.detail;
    this.setData({
      [name]: value
    });
  },

  /**
   * Radio变化事件
   */
  handleRadioChange: function (e) {
    const { value, name } = e.detail;

    // 如果选中了某个单选框，则取消选中其他单选框
    if (value) {
      if (name === 'radio1') {
        this.setData({
          radio1: true,
          radio2: false,
          radio3: false
        });
      } else if (name === 'radio2') {
        this.setData({
          radio1: false,
          radio2: true,
          radio3: false
        });
      } else if (name === 'radio3') {
        this.setData({
          radio1: false,
          radio2: false,
          radio3: true
        });
      }
    }
  },

  /**
   * Progress变化事件
   */
  handleProgressChange: function () {
    // 增加进度
    this.setData({
      progress1: Math.min(100, this.data.progress1 + 10),
      progress2: Math.min(100, this.data.progress2 + 10),
      progress3: Math.min(100, this.data.progress3 + 10)
    });
  },

  /**
   * Slider变化事件
   */
  handleSliderChange: function (e) {
    const { value } = e.detail;
    const name = e.currentTarget.dataset.name;

    this.setData({
      [name]: value
    });
  },

  /**
   * 显示弹出层
   */
  showPopup: function (e) {
    const position = e.currentTarget.dataset.position;

    this.setData({
      [`showPopup${position}`]: true
    });
  },

  /**
   * 关闭弹出层
   */
  closePopup: function (e) {
    const position = e.currentTarget.dataset.position;

    this.setData({
      [`showPopup${position}`]: false
    });
  }
});
