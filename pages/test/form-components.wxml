<view class="container">
  <view class="page-title">表单组件测试</view>
  
  <!-- Checkbox测试 -->
  <view class="section">
    <view class="section-title">Checkbox 复选框</view>
    <view class="section-content">
      <nl-checkbox 
        label="默认复选框" 
        name="checkbox1" 
        value="{{checkbox1}}" 
        bind:change="handleCheckboxChange"
      ></nl-checkbox>
      
      <nl-checkbox 
        label="选中状态" 
        name="checkbox2" 
        value="{{checkbox2}}" 
        bind:change="handleCheckboxChange"
      ></nl-checkbox>
      
      <nl-checkbox 
        label="成功类型" 
        type="success" 
        value="{{true}}"
      ></nl-checkbox>
      
      <nl-checkbox 
        label="警告类型" 
        type="warning" 
        value="{{true}}"
      ></nl-checkbox>
      
      <nl-checkbox 
        label="错误类型" 
        type="error" 
        value="{{true}}"
      ></nl-checkbox>
      
      <nl-checkbox 
        label="禁用状态" 
        disabled="{{true}}" 
        value="{{false}}"
      ></nl-checkbox>
      
      <nl-checkbox 
        label="禁用选中状态" 
        disabled="{{true}}" 
        value="{{true}}"
      ></nl-checkbox>
      
      <nl-checkbox 
        label="圆形复选框" 
        shape="circle" 
        value="{{true}}"
      ></nl-checkbox>
    </view>
  </view>
  
  <!-- Radio测试 -->
  <view class="section">
    <view class="section-title">Radio 单选框</view>
    <view class="section-content">
      <nl-radio 
        label="选项一" 
        name="radio1" 
        value="{{radio1}}" 
        bind:change="handleRadioChange"
      ></nl-radio>
      
      <nl-radio 
        label="选项二" 
        name="radio2" 
        value="{{radio2}}" 
        bind:change="handleRadioChange"
      ></nl-radio>
      
      <nl-radio 
        label="选项三" 
        name="radio3" 
        value="{{radio3}}" 
        bind:change="handleRadioChange"
      ></nl-radio>
      
      <nl-radio 
        label="成功类型" 
        type="success" 
        value="{{true}}"
      ></nl-radio>
      
      <nl-radio 
        label="警告类型" 
        type="warning" 
        value="{{true}}"
      ></nl-radio>
      
      <nl-radio 
        label="错误类型" 
        type="error" 
        value="{{true}}"
      ></nl-radio>
      
      <nl-radio 
        label="禁用状态" 
        disabled="{{true}}" 
        value="{{false}}"
      ></nl-radio>
      
      <nl-radio 
        label="禁用选中状态" 
        disabled="{{true}}" 
        value="{{true}}"
      ></nl-radio>
    </view>
  </view>
  
  <!-- Progress测试 -->
  <view class="section">
    <view class="section-title">Progress 进度条</view>
    <view class="section-content">
      <nl-progress 
        value="{{progress1}}" 
        color="primary"
      ></nl-progress>
      
      <nl-progress 
        value="{{progress2}}" 
        color="success" 
        striped="{{true}}"
      ></nl-progress>
      
      <nl-progress 
        value="{{progress3}}" 
        color="warning" 
        striped="{{true}}" 
        animated="{{true}}"
      ></nl-progress>
      
      <nl-progress 
        value="80" 
        color="error" 
        showText="{{false}}"
      ></nl-progress>
      
      <nl-progress 
        value="60" 
        color="info" 
        textPosition="inside"
      ></nl-progress>
      
      <view class="button-container">
        <nl-button 
          text="增加进度" 
          type="primary" 
          bind:click="handleProgressChange"
        ></nl-button>
      </view>
    </view>
  </view>
  
  <!-- Slider测试 -->
  <view class="section">
    <view class="section-title">Slider 滑块</view>
    <view class="section-content">
      <nl-slider 
        value="{{slider1}}" 
        label="基础滑块" 
        showValue="{{true}}" 
        data-name="slider1" 
        bind:change="handleSliderChange"
      ></nl-slider>
      
      <nl-slider 
        value="{{slider2}}" 
        label="带刻度滑块" 
        showValue="{{true}}" 
        showTicks="{{true}}" 
        tickCount="5" 
        data-name="slider2" 
        bind:change="handleSliderChange"
      ></nl-slider>
      
      <nl-slider 
        value="30" 
        label="成功类型" 
        type="success" 
        showValue="{{true}}"
      ></nl-slider>
      
      <nl-slider 
        value="50" 
        label="警告类型" 
        type="warning" 
        showValue="{{true}}"
      ></nl-slider>
      
      <nl-slider 
        value="70" 
        label="错误类型" 
        type="error" 
        showValue="{{true}}"
      ></nl-slider>
      
      <nl-slider 
        value="40" 
        label="禁用状态" 
        disabled="{{true}}" 
        showValue="{{true}}"
      ></nl-slider>
    </view>
  </view>
  
  <!-- Popup测试 -->
  <view class="section">
    <view class="section-title">Popup 弹出层</view>
    <view class="section-content">
      <view class="button-container">
        <nl-button 
          text="顶部弹出" 
          type="primary" 
          data-position="1" 
          bind:click="showPopup"
        ></nl-button>
        
        <nl-button 
          text="右侧弹出" 
          type="success" 
          data-position="2" 
          bind:click="showPopup"
        ></nl-button>
        
        <nl-button 
          text="底部弹出" 
          type="warning" 
          data-position="3" 
          bind:click="showPopup"
        ></nl-button>
        
        <nl-button 
          text="左侧弹出" 
          type="error" 
          data-position="4" 
          bind:click="showPopup"
        ></nl-button>
        
        <nl-button 
          text="中间弹出" 
          type="info" 
          data-position="5" 
          bind:click="showPopup"
        ></nl-button>
      </view>
      
      <!-- 顶部弹出层 -->
      <nl-popup 
        show="{{showPopup1}}" 
        position="top" 
        bind:close="closePopup" 
        data-position="1" 
        height="30%"
      >
        <view class="popup-content">
          <view class="popup-title">顶部弹出层</view>
          <view class="popup-text">这是一个顶部弹出的弹出层</view>
          <nl-button 
            text="关闭" 
            type="primary" 
            data-position="1" 
            bind:click="closePopup"
          ></nl-button>
        </view>
      </nl-popup>
      
      <!-- 右侧弹出层 -->
      <nl-popup 
        show="{{showPopup2}}" 
        position="right" 
        bind:close="closePopup" 
        data-position="2" 
        width="70%"
      >
        <view class="popup-content">
          <view class="popup-title">右侧弹出层</view>
          <view class="popup-text">这是一个右侧弹出的弹出层</view>
          <nl-button 
            text="关闭" 
            type="success" 
            data-position="2" 
            bind:click="closePopup"
          ></nl-button>
        </view>
      </nl-popup>
      
      <!-- 底部弹出层 -->
      <nl-popup 
        show="{{showPopup3}}" 
        position="bottom" 
        bind:close="closePopup" 
        data-position="3" 
        round="{{true}}"
      >
        <view class="popup-content">
          <view class="popup-title">底部弹出层</view>
          <view class="popup-text">这是一个底部弹出的弹出层</view>
          <nl-button 
            text="关闭" 
            type="warning" 
            data-position="3" 
            bind:click="closePopup"
          ></nl-button>
        </view>
      </nl-popup>
      
      <!-- 左侧弹出层 -->
      <nl-popup 
        show="{{showPopup4}}" 
        position="left" 
        bind:close="closePopup" 
        data-position="4" 
        width="70%"
      >
        <view class="popup-content">
          <view class="popup-title">左侧弹出层</view>
          <view class="popup-text">这是一个左侧弹出的弹出层</view>
          <nl-button 
            text="关闭" 
            type="error" 
            data-position="4" 
            bind:click="closePopup"
          ></nl-button>
        </view>
      </nl-popup>
      
      <!-- 中间弹出层 -->
      <nl-popup 
        show="{{showPopup5}}" 
        position="center" 
        bind:close="closePopup" 
        data-position="5" 
        round="{{true}}" 
        showClose="{{true}}"
      >
        <view class="popup-content">
          <view class="popup-title">中间弹出层</view>
          <view class="popup-text">这是一个中间弹出的弹出层</view>
          <nl-button 
            text="关闭" 
            type="info" 
            data-position="5" 
            bind:click="closePopup"
          ></nl-button>
        </view>
      </nl-popup>
    </view>
  </view>
</view>
