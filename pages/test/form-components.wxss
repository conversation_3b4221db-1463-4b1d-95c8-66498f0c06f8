/* 页面容器 */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

/* 分区 */
.section {
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 分区标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #eee;
}

/* 分区内容 */
.section-content {
  padding: 30rpx;
}

/* 按钮容器 */
.button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 20rpx;
}

/* 弹出层内容 */
.popup-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 弹出层标题 */
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 弹出层文本 */
.popup-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}
