// pages/test/index.js
// 测试页面

// 导入工具类
const ThemeManager = require('../../utils/theme-manager');
const CanvasManager = require('../../utils/canvas-manager');
const debugTools = require('../../utils/debug-tools');
const performanceMonitor = require('../../utils/performance-monitor');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    initialized: false,
    loadingFailed: false,
    interfaceStyle: 'bubble', // 'bubble' 或 'star'
    isDarkMode: false,
    bubbleFocused: false,
    showThemeModal: false,
    currentTheme: null,
    isDebugMode: false,
    showPerformance: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 创建主题数据管理实例
    this.themeManager = new ThemeManager();

    // 创建画布管理器实例
    this.canvasManager = new CanvasManager({
      page: this,
      themeManager: this.themeManager
    });

    // 初始化画布
    this.initializeCanvas();
  },

  /**
   * 初始化画布
   */
  initializeCanvas: function () {
    // 先加载主题数据
    this.themeManager.loadCurrentPlanTags(true)
      .then(themes =>
        // 创建对应类型的画布
        this.canvasManager.createCanvas(this.data.interfaceStyle, {
          canvasId: `${this.data.interfaceStyle}-canvas`,
          config: {
            animationSpeedMultiplier: 0.064,
            baseSpeed: 0.64
          }
        })
      )
      .then(canvas => {
        console.log(`${this.data.interfaceStyle}画布创建成功`);
        this.setData({ initialized: true });
      })
      .catch(err => {
        console.error('初始化画布失败:', err);
        this.setData({ loadingFailed: true });
      });
  },

  /**
   * 切换界面样式
   */
  switchInterfaceStyle: function () {
    const newStyle = this.data.interfaceStyle === 'bubble' ? 'star' : 'bubble';

    this.setData({
      interfaceStyle: newStyle,
      initialized: false
    });

    // 切换画布类型
    this.canvasManager.switchCanvas(newStyle, {
      canvasId: `${newStyle}-canvas`,
      config: {
        animationSpeedMultiplier: 0.064,
        baseSpeed: 0.64
      }
    }).then(success => {
      if (success) {
        console.log(`切换到${newStyle}画布成功`);
        this.setData({ initialized: true });
      } else {
        console.error(`切换到${newStyle}画布失败`);
        this.setData({ loadingFailed: true });
      }
    }).catch(err => {
      console.error(`切换到${newStyle}画布失败:`, err);
      this.setData({ loadingFailed: true });
    });
  },

  /**
   * 处理触摸开始事件
   */
  onTouchStart: function (e) {
    if (!this.canvasManager) return;

    const result = this.canvasManager.handleTouchStart(e);
    if (result) {
      this.setData({
        currentTheme: result.theme,
        showThemeModal: true
      });
    }
  },

  /**
   * 处理触摸移动事件
   */
  onTouchMove: function (e) {
    if (!this.canvasManager) return;

    const cursor = this.canvasManager.handleTouchMove(e);
    this.setData({
      bubbleFocused: cursor === 'pointer'
    });
  },

  /**
   * 处理触摸结束事件
   */
  onTouchEnd: function () {
    if (!this.canvasManager) return;

    const result = this.canvasManager.handleTouchEnd();
    if (result) {
      this.setData({
        currentTheme: result.theme,
        showThemeModal: true
      });
    }

    // 重置鼠标样式
    this.setData({
      bubbleFocused: false
    });
  },

  /**
   * 关闭主题详情弹窗
   */
  closeThemeModal: function () {
    this.setData({
      showThemeModal: false
    });

    // 重置点击状态
    if (this.canvasManager) {
      const canvas = this.canvasManager.getCurrentCanvas();
      if (canvas) {
        canvas.resetInteractionState();
      }
    }
  },

  /**
   * 重新加载
   */
  retryLoading: function () {
    this.setData({
      loadingFailed: false,
      initialized: false
    });

    // 重新初始化
    this.initializeCanvas();
  },

  /**
   * 切换调试模式
   */
  toggleDebugMode: function () {
    const newDebugMode = !this.data.isDebugMode;

    this.setData({
      isDebugMode: newDebugMode
    });

    if (newDebugMode) {
      // 启用调试模式
      debugTools.enableDebugMode({
        showFPS: true,
        showBoundingBoxes: true,
        showElementInfo: true
      });
    } else {
      // 禁用调试模式
      debugTools.disableDebugMode();
    }
  },

  /**
   * 切换性能监控
   */
  togglePerformanceMonitoring: function () {
    const newShowPerformance = !this.data.showPerformance;

    this.setData({
      showPerformance: newShowPerformance
    });

    // 重新初始化画布，启用或禁用性能监控
    if (this.canvasManager) {
      const canvas = this.canvasManager.getCurrentCanvas();
      if (canvas) {
        canvas.stopAnimation();
        canvas.startAnimation(newShowPerformance);
      }
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 恢复动画
    if (this.canvasManager) {
      this.canvasManager.resumeAnimation();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    // 暂停动画
    if (this.canvasManager) {
      this.canvasManager.pauseAnimation();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清理资源
    if (this.canvasManager) {
      this.canvasManager.cleanup();
      this.canvasManager = null;
    }

    if (this.themeManager) {
      this.themeManager = null;
    }
  }
});
