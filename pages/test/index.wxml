<!-- pages/test/index.wxml -->
<!-- 测试页面 -->
<view class="container {{bubbleFocused ? 'cursor-pointer' : ''}}">
  <!-- 泡泡画布区域 - 在使用泡泡样式时显示 -->
  <canvas
    wx:if="{{interfaceStyle === 'bubble'}}"
    type="2d"
    id="bubble-canvas"
    class="bubble-canvas"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
    disable-scroll="true">
  </canvas>

  <!-- 星星画布区域 - 在使用星星样式时显示 -->
  <canvas
    wx:if="{{interfaceStyle === 'star'}}"
    type="2d"
    id="star-canvas"
    class="star-canvas {{isDarkMode ? 'dark-mode' : ''}}"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
    disable-scroll="true">
  </canvas>

  <!-- 工具栏 -->
  <view class="toolbar">
    <!-- 切换按钮 -->
    <view class="tool-button" bindtap="switchInterfaceStyle">
      <text>切换到{{interfaceStyle === 'bubble' ? '星星' : '泡泡'}}模式</text>
    </view>

    <!-- 调试模式按钮 -->
    <view class="tool-button {{isDebugMode ? 'active' : ''}}" bindtap="toggleDebugMode">
      <text>调试模式</text>
    </view>

    <!-- 性能监控按钮 -->
    <view class="tool-button {{showPerformance ? 'active' : ''}}" bindtap="togglePerformanceMonitoring">
      <text>性能监控</text>
    </view>
  </view>

  <!-- 加载指示器 -->
  <view class="loading-container" wx:if="{{!initialized && !loadingFailed}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败提示 -->
  <view class="error-container" wx:if="{{loadingFailed}}">
    <icon type="warn" size="40"></icon>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="retryLoading">重试</button>
  </view>

  <!-- 主题详情弹窗 -->
  <view class="theme-modal {{showThemeModal ? 'visible' : ''}}" bindtap="closeThemeModal">
    <view class="theme-modal-content" catchtap>
      <view class="theme-modal-header">
        <text class="theme-modal-title">{{currentTheme.name}}</text>
        <text class="theme-modal-subtitle">{{currentTheme.englishName}}</text>
      </view>
      <view class="theme-modal-body">
        <text class="theme-modal-description">{{currentTheme.description}}</text>
      </view>
      <view class="theme-modal-footer">
        <button class="theme-modal-button" style="background-color: {{currentTheme.color}};">开始学习</button>
      </view>
    </view>
  </view>
</view>
