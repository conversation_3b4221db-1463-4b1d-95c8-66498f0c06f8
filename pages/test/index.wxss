/* pages/test/index.wxss */
/* 测试页面样式 */

/* 整体容器 */
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: transparent;
}

/* 鼠标指针样式 */
.cursor-pointer {
  cursor: pointer;
}

/* 气泡画布样式 */
.bubble-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: block;
  background-color: transparent;
}

/* 星星画布样式 */
.star-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: block;
  background-color: #121212;
}

/* 星星界面的深色模式 */
.star-canvas.dark-mode {
  background-color: #000000;
}

/* 工具栏 */
.toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 工具按钮 */
.tool-button {
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 10px 20px;
  border-radius: 20px;
  color: #fff;
  font-size: 14px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* 激活状态 */
.tool-button.active {
  background-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.4);
}

/* 加载指示器 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
  background-color: transparent;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.1);
  border-top-color: #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #fff;
}

/* 错误提示 */
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.7);
}

.error-text {
  font-size: 28rpx;
  color: #fff;
  margin: 20rpx 0;
}

.retry-button {
  background-color: #2563eb;
  color: white;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin-top: 20rpx;
}

/* 主题详情弹窗 */
.theme-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.theme-modal.visible {
  opacity: 1;
  visibility: visible;
}

.theme-modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
}

.theme-modal-header {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.theme-modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.theme-modal-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.theme-modal-body {
  padding: 30rpx;
  flex: 1;
}

.theme-modal-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.theme-modal-footer {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  justify-content: flex-end;
}

.theme-modal-button {
  background-color: #3B82F6;
  color: white;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  border: none;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
