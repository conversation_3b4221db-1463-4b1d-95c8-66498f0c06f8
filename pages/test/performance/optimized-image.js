// pages/test/performance/optimized-image.js
// 优化图片性能测试页面

// 性能监控工具
const PerformanceMonitor = require('../../../utils/performance-monitor');

// 示例图片URL
const sampleImageUrls = [
  'https://images.unsplash.com/photo-1506744038136-46273834b3fb',
  'https://images.unsplash.com/photo-1511300636408-a63a89df3482',
  'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05',
  'https://images.unsplash.com/photo-1501854140801-50d01698950b',
  'https://images.unsplash.com/photo-1441974231531-c6227db76b6e',
  'https://images.unsplash.com/photo-1518791841217-8f162f1e1131',
  'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
  'https://images.unsplash.com/photo-1519046904884-53103b34b206',
  'https://images.unsplash.com/photo-1520962922320-2038eebab146',
  'https://images.unsplash.com/photo-1476514525535-07fb3b4ae5f1'
];

Page({
  /**
   * 页面的初始数据
   */
  data: {
    imageType: 'normal', // 图片类型：normal-普通图片，optimized-优化图片
    imageCount: 30, // 图片数量
    images: [], // 图片数据
    loadTime: 0, // 加载时间
    memoryUsage: 0, // 内存使用
    loadedCount: 0, // 已加载数量
    errorCount: 0, // 加载失败数量
    normalLoadTime: 0, // 普通图片加载时间
    optimizedLoadTime: 0, // 优化图片加载时间
    performanceImprovement: 0, // 性能提升百分比
    cdnDomains: ['images.unsplash.com'] // CDN域名列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化性能监控
    this.normalMonitor = new PerformanceMonitor();
    this.optimizedMonitor = new PerformanceMonitor();

    // 启动性能监控
    this.normalMonitor.start();
    this.optimizedMonitor.start();

    // 加载初始图片
    this.loadImages();

    // 设置性能监控定时器
    this.performanceTimer = setInterval(() => {
      this.updatePerformanceMetrics();
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清除定时器
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = null;
    }

    // 停止性能监控
    if (this.normalMonitor) {
      this.normalMonitor.stop();
    }

    if (this.optimizedMonitor) {
      this.optimizedMonitor.stop();
    }
  },

  /**
   * 更新性能指标
   */
  updatePerformanceMetrics: function () {
    // 获取当前性能监控器
    const monitor = this.data.imageType === 'normal' ? this.normalMonitor : this.optimizedMonitor;

    // 获取性能指标
    const metrics = monitor.getMetrics();

    // 更新内存使用
    this.setData({
      memoryUsage: (metrics.memory.current / 1024 / 1024).toFixed(2)
    });
  },

  /**
   * 处理图片类型变更
   */
  handleImageTypeChange: function (e) {
    this.setData({
      imageType: e.detail.value,
      loadedCount: 0,
      errorCount: 0,
      loadTime: 0
    });

    // 重置当前类型的性能监控
    if (e.detail.value === 'normal') {
      this.normalMonitor.reset();
    } else {
      this.optimizedMonitor.reset();
    }
  },

  /**
   * 处理图片数量变更
   */
  handleImageCountChange: function (e) {
    this.setData({
      imageCount: e.detail.value
    });
  },

  /**
   * 加载图片
   */
  loadImages: function () {
    // 重置计数器
    this.setData({
      loadedCount: 0,
      errorCount: 0
    });

    // 重置性能监控
    this.normalMonitor.reset();
    this.optimizedMonitor.reset();

    // 记录开始时间
    const startTime = Date.now();

    // 生成图片数据
    const images = [];

    for (let i = 0; i < this.data.imageCount; i++) {
      // 随机选择一个图片URL
      const urlIndex = i % sampleImageUrls.length;
      const baseUrl = sampleImageUrls[urlIndex];

      // 添加随机参数避免缓存
      const url = `${baseUrl}?random=${Math.random()}`;

      images.push({
        id: i,
        url,
        title: `图片 ${i + 1}`,
        description: `这是第 ${i + 1} 张测试图片，用于测试图片加载性能。`
      });
    }

    this.setData({
      images,
      loadTime: 0
    });
  },

  /**
   * 处理普通图片加载完成
   */
  handleNormalImageLoad: function () {
    const loadedCount = this.data.loadedCount + 1;

    this.setData({
      loadedCount
    });

    // 记录性能数据
    this.normalMonitor.recordFrame();

    // 如果所有图片都已加载，记录加载时间
    if (loadedCount === this.data.imageCount) {
      const loadTime = Date.now() - this.normalMonitor.startTime;

      this.setData({
        loadTime,
        normalLoadTime: loadTime
      });

      // 计算性能提升
      this._calculatePerformanceImprovement();
    }
  },

  /**
   * 处理普通图片加载失败
   */
  handleNormalImageError: function () {
    this.setData({
      errorCount: this.data.errorCount + 1
    });
  },

  /**
   * 处理优化图片加载完成
   */
  handleOptimizedImageLoad: function () {
    const loadedCount = this.data.loadedCount + 1;

    this.setData({
      loadedCount
    });

    // 记录性能数据
    this.optimizedMonitor.recordFrame();

    // 如果所有图片都已加载，记录加载时间
    if (loadedCount === this.data.imageCount) {
      const loadTime = Date.now() - this.optimizedMonitor.startTime;

      this.setData({
        loadTime,
        optimizedLoadTime: loadTime
      });

      // 计算性能提升
      this._calculatePerformanceImprovement();
    }
  },

  /**
   * 处理优化图片加载失败
   */
  handleOptimizedImageError: function () {
    this.setData({
      errorCount: this.data.errorCount + 1
    });
  },

  /**
   * 处理滚动
   */
  handleScroll: function () {
    // 记录滚动性能数据
    if (this.data.imageType === 'normal') {
      this.normalMonitor.recordScroll();
    } else {
      this.optimizedMonitor.recordScroll();
    }
  },

  /**
   * 计算性能提升
   */
  _calculatePerformanceImprovement: function () {
    const { normalLoadTime, optimizedLoadTime } = this.data;

    if (normalLoadTime > 0 && optimizedLoadTime > 0) {
      const improvement = Math.round((normalLoadTime - optimizedLoadTime) / normalLoadTime * 100);

      this.setData({
        performanceImprovement: improvement
      });
    }
  },

  /**
   * 重置测试
   */
  resetTest: function () {
    // 重置性能监控
    this.normalMonitor.reset();
    this.optimizedMonitor.reset();

    this.setData({
      loadedCount: 0,
      errorCount: 0,
      loadTime: 0,
      memoryUsage: 0,
      normalLoadTime: 0,
      optimizedLoadTime: 0,
      performanceImprovement: 0
    });
  }
});
