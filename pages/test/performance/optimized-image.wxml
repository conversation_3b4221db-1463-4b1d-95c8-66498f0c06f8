<!-- 优化图片测试页面 -->
<view class="container">
  <view class="header">
    <view class="title">优化图片性能测试</view>
    <view class="controls">
      <view class="control-group">
        <text class="control-label">图片类型:</text>
        <radio-group class="radio-group" bindchange="handleImageTypeChange">
          <label class="radio-label">
            <radio value="normal" checked="{{imageType === 'normal'}}" />
            <text>普通图片</text>
          </label>
          <label class="radio-label">
            <radio value="optimized" checked="{{imageType === 'optimized'}}" />
            <text>优化图片</text>
          </label>
        </radio-group>
      </view>
      
      <view class="control-group">
        <text class="control-label">图片数量:</text>
        <slider 
          min="10" 
          max="100" 
          step="10" 
          value="{{imageCount}}" 
          show-value 
          bindchange="handleImageCountChange"
        ></slider>
      </view>
      
      <button class="btn-primary" bindtap="loadImages">加载图片</button>
    </view>
  </view>
  
  <view class="content">
    <view class="performance-info">
      <view class="info-item">
        <text class="info-label">加载时间:</text>
        <text class="info-value">{{loadTime}}ms</text>
      </view>
      <view class="info-item">
        <text class="info-label">内存使用:</text>
        <text class="info-value">{{memoryUsage}}MB</text>
      </view>
      <view class="info-item">
        <text class="info-label">已加载:</text>
        <text class="info-value">{{loadedCount}}/{{imageCount}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">失败:</text>
        <text class="info-value">{{errorCount}}</text>
      </view>
    </view>
    
    <scroll-view class="image-list" scroll-y bindscroll="handleScroll">
      <!-- 普通图片列表 -->
      <block wx:if="{{imageType === 'normal'}}">
        <view 
          wx:for="{{images}}" 
          wx:key="id"
          class="image-item"
        >
          <view class="image-container">
            <image 
              class="normal-image"
              src="{{item.url}}"
              mode="aspectFill"
              lazy-load="{{true}}"
              binderror="handleNormalImageError"
              bindload="handleNormalImageLoad"
            ></image>
          </view>
          <view class="image-info">
            <view class="image-title">{{item.title}}</view>
            <view class="image-desc">{{item.description}}</view>
          </view>
        </view>
      </block>
      
      <!-- 优化图片列表 -->
      <block wx:if="{{imageType === 'optimized'}}">
        <view 
          wx:for="{{images}}" 
          wx:key="id"
          class="image-item"
        >
          <view class="image-container">
            <optimized-image 
              src="{{item.url}}"
              width="200"
              height="200"
              mode="aspectFill"
              lazy-load="{{true}}"
              webp="{{true}}"
              use-cdn="{{true}}"
              cdn-domains="{{cdnDomains}}"
              show-placeholder="{{true}}"
              show-error="{{true}}"
              fade-in="{{true}}"
              binderror="handleOptimizedImageError"
              bindload="handleOptimizedImageLoad"
            ></optimized-image>
          </view>
          <view class="image-info">
            <view class="image-title">{{item.title}}</view>
            <view class="image-desc">{{item.description}}</view>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
  
  <view class="footer">
    <view class="comparison">
      <view class="comparison-title">性能对比</view>
      <view class="comparison-item">
        <text class="comparison-label">普通图片加载时间:</text>
        <text class="comparison-value">{{normalLoadTime}}ms</text>
      </view>
      <view class="comparison-item">
        <text class="comparison-label">优化图片加载时间:</text>
        <text class="comparison-value">{{optimizedLoadTime}}ms</text>
      </view>
      <view class="comparison-item">
        <text class="comparison-label">性能提升:</text>
        <text class="comparison-value">{{performanceImprovement}}%</text>
      </view>
    </view>
    
    <button class="btn-secondary" bindtap="resetTest">重置测试</button>
  </view>
</view>
