/* 优化图片测试页面样式 */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--nl-color-background);
}

.header {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid var(--nl-color-border);
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 20rpx;
}

.controls {
  margin-bottom: 20rpx;
}

.control-group {
  margin-bottom: 20rpx;
}

.control-label {
  font-size: 28rpx;
  color: var(--nl-color-text);
  margin-bottom: 10rpx;
  display: block;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
}

.radio-label {
  margin-right: 30rpx;
  font-size: 28rpx;
  color: var(--nl-color-text);
  display: flex;
  align-items: center;
}

.btn-primary {
  background-color: var(--nl-color-primary);
  color: #FFFFFF;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.performance-info {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid var(--nl-color-border);
  background-color: var(--nl-color-background-light);
}

.info-item {
  flex: 1;
  min-width: 50%;
  margin-bottom: 10rpx;
}

.info-label {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
  margin-right: 10rpx;
}

.info-value {
  font-size: 24rpx;
  font-weight: 500;
  color: var(--nl-color-text);
}

.image-list {
  flex: 1;
  padding: 20rpx;
}

.image-item {
  display: flex;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: var(--nl-color-background-light);
  border-radius: 12rpx;
}

.image-container {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  border-radius: 12rpx;
  overflow: hidden;
}

.normal-image {
  width: 100%;
  height: 100%;
}

.image-info {
  flex: 1;
}

.image-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 10rpx;
}

.image-desc {
  font-size: 28rpx;
  color: var(--nl-color-text-secondary);
}

.footer {
  padding: 20rpx 30rpx;
  border-top: 1px solid var(--nl-color-border);
}

.comparison {
  margin-bottom: 20rpx;
}

.comparison-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 10rpx;
}

.comparison-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.comparison-label {
  font-size: 28rpx;
  color: var(--nl-color-text);
}

.comparison-value {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--nl-color-primary);
}

.btn-secondary {
  background-color: var(--nl-color-background-light);
  color: var(--nl-color-text);
  font-size: 28rpx;
}
