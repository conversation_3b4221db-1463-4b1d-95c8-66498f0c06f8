// pages/test/performance/virtual-list.js
// 虚拟列表性能测试页面

// 性能监控工具
const PerformanceMonitor = require('../../../utils/performance-monitor');

// 随机颜色生成
const colors = [
  '#4CAF50', '#2196F3', '#9C27B0', '#FF9800', '#F44336',
  '#3F51B5', '#009688', '#FF5722', '#607D8B', '#E91E63'
];

// 随机标签
const tags = [
  '技术', '设计', '产品', '管理', '营销',
  '数据', '用户体验', '前端', '后端', '移动端',
  '人工智能', '机器学习', '区块链', '云计算', '大数据'
];

Page({
  /**
   * 页面的初始数据
   */
  data: {
    listType: 'fixed', // 列表类型：fixed-固定高度，dynamic-动态高度
    dataCount: 1000, // 数据量
    listData: [], // 列表数据
    visibleCount: 0, // 可见数量
    totalCount: 0, // 总数量
    fps: 0, // 帧率
    memory: 0, // 内存使用
    normalFps: 0, // 普通列表帧率
    normalMemory: 0, // 普通列表内存使用
    avgFps: 0, // 平均帧率
    avgNormalFps: 0, // 普通列表平均帧率
    performanceImprovement: 0, // 性能提升百分比
    loading: false, // 是否正在加载
    noMore: false, // 是否没有更多数据

    // 性能监控相关
    fpsHistory: [], // 帧率历史
    normalFpsHistory: [], // 普通列表帧率历史
    memoryHistory: [], // 内存使用历史
    normalMemoryHistory: [] // 普通列表内存使用历史
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化性能监控
    this.virtualListMonitor = new PerformanceMonitor();
    this.normalListMonitor = new PerformanceMonitor();

    // 启动性能监控
    this.virtualListMonitor.start();
    this.normalListMonitor.start();

    // 生成初始数据
    this.generateData();

    // 设置性能监控定时器
    this.performanceTimer = setInterval(() => {
      this.updatePerformanceMetrics();
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清除定时器
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = null;
    }

    // 停止性能监控
    if (this.virtualListMonitor) {
      this.virtualListMonitor.stop();
    }

    if (this.normalListMonitor) {
      this.normalListMonitor.stop();
    }
  },

  /**
   * 更新性能指标
   */
  updatePerformanceMetrics: function () {
    // 获取虚拟列表性能指标
    const virtualMetrics = this.virtualListMonitor.getMetrics();

    // 获取普通列表性能指标
    const normalMetrics = this.normalListMonitor.getMetrics();

    // 更新帧率历史
    const fpsHistory = [...this.data.fpsHistory];
    fpsHistory.push(virtualMetrics.fps.current);
    if (fpsHistory.length > 10) fpsHistory.shift();

    const normalFpsHistory = [...this.data.normalFpsHistory];
    normalFpsHistory.push(normalMetrics.fps.current);
    if (normalFpsHistory.length > 10) normalFpsHistory.shift();

    // 更新内存使用历史
    const memoryHistory = [...this.data.memoryHistory];
    memoryHistory.push(virtualMetrics.memory.current);
    if (memoryHistory.length > 10) memoryHistory.shift();

    const normalMemoryHistory = [...this.data.normalMemoryHistory];
    normalMemoryHistory.push(normalMetrics.memory.current);
    if (normalMemoryHistory.length > 10) normalMemoryHistory.shift();

    // 计算平均帧率
    const avgFps = fpsHistory.reduce((sum, fps) => sum + fps, 0) / fpsHistory.length;
    const avgNormalFps = normalFpsHistory.reduce((sum, fps) => sum + fps, 0) / normalFpsHistory.length;

    // 计算性能提升百分比
    const performanceImprovement = avgNormalFps > 0 ?
      Math.round((avgFps - avgNormalFps) / avgNormalFps * 100) : 0;

    // 更新数据
    this.setData({
      fps: Math.round(virtualMetrics.fps.current),
      memory: (virtualMetrics.memory.current / 1024 / 1024).toFixed(2),
      normalFps: Math.round(normalMetrics.fps.current),
      normalMemory: (normalMetrics.memory.current / 1024 / 1024).toFixed(2),
      fpsHistory,
      normalFpsHistory,
      memoryHistory,
      normalMemoryHistory,
      avgFps: Math.round(avgFps),
      avgNormalFps: Math.round(avgNormalFps),
      performanceImprovement
    });
  },

  /**
   * 处理列表类型变更
   */
  handleListTypeChange: function (e) {
    this.setData({
      listType: e.detail.value
    });
  },

  /**
   * 处理数据量变更
   */
  handleDataCountChange: function (e) {
    this.setData({
      dataCount: e.detail.value
    });
  },

  /**
   * 生成测试数据
   */
  generateData: function () {
    const { dataCount, listType } = this.data;

    // 重置性能监控
    this.virtualListMonitor.reset();
    this.normalListMonitor.reset();

    // 生成数据
    const listData = [];

    for (let i = 0; i < dataCount; i++) {
      // 随机颜色
      const colorIndex = Math.floor(Math.random() * colors.length);
      const color = colors[colorIndex];

      // 随机标签（仅动态高度模式使用）
      const itemTags = [];
      if (listType === 'dynamic') {
        const tagCount = Math.floor(Math.random() * 5); // 0-4个标签
        for (let j = 0; j < tagCount; j++) {
          const tagIndex = Math.floor(Math.random() * tags.length);
          itemTags.push(tags[tagIndex]);
        }
      }

      // 随机描述长度
      const descLength = listType === 'dynamic' ?
        Math.floor(Math.random() * 100) + 20 : 50; // 动态高度模式下描述长度不同

      listData.push({
        id: i,
        title: `Item ${i + 1}`,
        description: this._generateRandomText(descLength),
        color,
        tags: itemTags
      });
    }

    this.setData({
      listData,
      totalCount: listData.length,
      visibleCount: 0,
      loading: false,
      noMore: true,
      fpsHistory: [],
      normalFpsHistory: [],
      memoryHistory: [],
      normalMemoryHistory: [],
      avgFps: 0,
      avgNormalFps: 0,
      performanceImprovement: 0
    });
  },

  /**
   * 生成随机文本
   * @param {number} length - 文本长度
   * @returns {string} 随机文本
   */
  _generateRandomText: function (length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 ';
    let result = '';

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  },

  /**
   * 处理虚拟列表滚动
   */
  handleListScroll: function (e) {
    const { scrollTop, scrollHeight, scrollBottom } = e.detail;

    // 计算可见数量
    const visibleCount = this.selectComponent('.virtual-list').data.visibleItems.length;

    this.setData({
      visibleCount
    });

    // 记录性能数据
    this.virtualListMonitor.recordScroll();
  },

  /**
   * 处理普通列表滚动
   */
  handleNormalListScroll: function (e) {
    // 记录性能数据
    this.normalListMonitor.recordScroll();
  },

  /**
   * 处理虚拟列表项点击
   */
  handleItemTap: function (e) {
    const { index, item } = e.detail;

    wx.showToast({
      title: `点击了虚拟列表项 ${index + 1}`,
      icon: 'none'
    });
  },

  /**
   * 处理普通列表项点击
   */
  handleNormalItemTap: function (e) {
    const { index } = e.currentTarget.dataset;

    wx.showToast({
      title: `点击了普通列表项 ${index + 1}`,
      icon: 'none'
    });
  },

  /**
   * 处理虚拟列表加载更多
   */
  handleLoadMore: function () {
    // 模拟加载更多
    if (this.data.noMore) return;

    this.setData({
      loading: true
    });

    // 模拟网络请求延迟
    setTimeout(() => {
      this.setData({
        loading: false,
        noMore: true
      });
    }, 1000);
  },

  /**
   * 处理普通列表加载更多
   */
  handleNormalListLoadMore: function () {
    // 模拟加载更多
    if (this.data.noMore) return;

    this.setData({
      loading: true
    });

    // 模拟网络请求延迟
    setTimeout(() => {
      this.setData({
        loading: false,
        noMore: true
      });
    }, 1000);
  },

  /**
   * 重置测试
   */
  resetTest: function () {
    // 重置性能监控
    this.virtualListMonitor.reset();
    this.normalListMonitor.reset();

    this.setData({
      fpsHistory: [],
      normalFpsHistory: [],
      memoryHistory: [],
      normalMemoryHistory: [],
      avgFps: 0,
      avgNormalFps: 0,
      performanceImprovement: 0,
      fps: 0,
      memory: 0,
      normalFps: 0,
      normalMemory: 0
    });
  }
});
