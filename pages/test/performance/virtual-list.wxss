/* 虚拟列表测试页面样式 */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--nl-color-background);
}

.header {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid var(--nl-color-border);
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 20rpx;
}

.controls {
  margin-bottom: 20rpx;
}

.control-group {
  margin-bottom: 20rpx;
}

.control-label {
  font-size: 28rpx;
  color: var(--nl-color-text);
  margin-bottom: 10rpx;
  display: block;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
}

.radio-label {
  margin-right: 30rpx;
  font-size: 28rpx;
  color: var(--nl-color-text);
  display: flex;
  align-items: center;
}

.btn-primary {
  background-color: var(--nl-color-primary);
  color: #FFFFFF;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--nl-color-border);
  overflow: hidden;
}

.list-container:last-child {
  border-right: none;
}

.list-header {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid var(--nl-color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--nl-color-text);
}

.performance-info {
  display: flex;
  gap: 20rpx;
}

.performance-info text {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
}

.virtual-list {
  flex: 1;
  height: 0;
}

.normal-list {
  flex: 1;
  height: 0;
}

.list-item {
  display: flex;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid var(--nl-color-border);
  background-color: var(--nl-color-background-light);
}

.list-item.dynamic-height {
  min-height: 100rpx;
}

.item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--nl-color-text);
  margin-bottom: 10rpx;
}

.item-desc {
  font-size: 28rpx;
  color: var(--nl-color-text-secondary);
  margin-bottom: 10rpx;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-top: 10rpx;
}

.item-tag {
  padding: 4rpx 16rpx;
  background-color: var(--nl-color-primary-light);
  color: var(--nl-color-primary);
  font-size: 24rpx;
  border-radius: 30rpx;
}

.loading-more, .no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.loading-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 4rpx solid var(--nl-color-border);
  border-top-color: var(--nl-color-primary);
  animation: loading-spin 0.8s linear infinite;
  margin-right: 10rpx;
}

.loading-text, .no-more-text {
  font-size: 24rpx;
  color: var(--nl-color-text-secondary);
}

.footer {
  padding: 20rpx 30rpx;
  border-top: 1px solid var(--nl-color-border);
}

.performance-summary {
  margin-bottom: 20rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.summary-label {
  font-size: 28rpx;
  color: var(--nl-color-text);
}

.summary-value {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--nl-color-primary);
}

.btn-secondary {
  background-color: var(--nl-color-background-light);
  color: var(--nl-color-text);
  font-size: 28rpx;
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
