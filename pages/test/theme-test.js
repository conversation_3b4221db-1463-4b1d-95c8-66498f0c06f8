// pages/test/theme-test.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isDarkMode: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取当前主题模式
    const app = getApp();
    if (app && app.globalData && app.globalData.themeMode) {
      this.setData({
        isDarkMode: app.globalData.themeMode === 'dark'
      });
    }
  },

  /**
   * 切换主题
   */
  toggleTheme: function (e) {
    const isDarkMode = e.detail.value;
    this.setData({ isDarkMode });

    // 更新全局主题
    const app = getApp();
    if (app) {
      const newTheme = isDarkMode ? 'dark' : 'light';

      // 更新存储
      wx.setStorageSync('themeMode', newTheme);

      // 更新全局数据
      app.globalData.themeMode = newTheme;
      app.globalData.currentTheme = newTheme;

      // 应用主题样式
      if (app.applyThemeStyles) {
        app.applyThemeStyles(newTheme);
      }

      // 通知页面刷新
      if (app.themeModeChangeCallback) {
        app.themeModeChangeCallback(newTheme);
      }

      // 设置页面data-theme属性
      wx.createSelectorQuery().select('page').fields({
        node: true,
        properties: ['dataset']
      }, function (res) {
        if (res && res.node) {
          res.node.dataset.theme = newTheme;
        }
      }).exec();
    }
  }
});
