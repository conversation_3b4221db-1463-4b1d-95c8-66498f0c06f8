<view class="container">
  <view class="section">
    <view class="section-title">按钮组件测试</view>
    <view class="section-content">
      <view class="button-row">
        <nl-button type="default" text="默认按钮" />
        <nl-button type="primary" text="主要按钮" />
        <nl-button type="secondary" text="次要按钮" />
      </view>
      <view class="button-row">
        <nl-button type="success" text="成功按钮" />
        <nl-button type="warning" text="警告按钮" />
        <nl-button type="error" text="错误按钮" />
      </view>
      <view class="button-row">
        <nl-button type="primary" text="朴素按钮" plain="{{true}}" />
        <nl-button type="primary" text="圆形按钮" round="{{true}}" />
        <nl-button type="primary" text="禁用按钮" disabled="{{true}}" />
      </view>
      <view class="button-row">
        <nl-button type="primary" text="小按钮" size="small" />
        <nl-button type="primary" text="中按钮" size="medium" />
        <nl-button type="primary" text="大按钮" size="large" />
      </view>
      <view class="button-row">
        <nl-button type="primary" text="加载中" loading="{{true}}" />
        <nl-button type="text" text="文本按钮" />
        <nl-button type="link" text="链接按钮" />
      </view>
      <view class="button-row">
        <nl-button type="primary" text="块级按钮" block="{{true}}" />
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">颜色变量测试</view>
    <view class="section-content">
      <view class="color-block primary">主色调</view>
      <view class="color-block secondary">次要色调</view>
      <view class="color-block success">成功色</view>
      <view class="color-block warning">警告色</view>
      <view class="color-block error">错误色</view>
      <view class="color-block info">信息色</view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">文本样式测试</view>
    <view class="section-content">
      <nl-text type="h1">标题1 (H1) - 使用Text组件</nl-text>
      <nl-text type="h2">标题2 (H2) - 使用Text组件</nl-text>
      <nl-text type="h3">标题3 (H3) - 使用Text组件</nl-text>
      <nl-text type="body1">正文1 (Body1) - 这是一段示例文本，使用Text组件。</nl-text>
      <nl-text type="body2">正文2 (Body2) - 这是一段示例文本，使用Text组件。</nl-text>
      <nl-text type="caption">说明文本 (Caption) - 这是一段示例文本，使用Text组件。</nl-text>

      <view class="text-divider"></view>

      <nl-text type="body1" color="primary">主要颜色文本</nl-text>
      <nl-text type="body1" color="secondary">次要颜色文本</nl-text>
      <nl-text type="body1" color="disabled">禁用颜色文本</nl-text>
      <nl-text type="body1" color="accent">强调颜色文本</nl-text>
      <nl-text type="body1" color="success">成功颜色文本</nl-text>
      <nl-text type="body1" color="warning">警告颜色文本</nl-text>
      <nl-text type="body1" color="error">错误颜色文本</nl-text>

      <view class="text-divider"></view>

      <nl-text type="body1" align="left">左对齐文本</nl-text>
      <nl-text type="body1" align="center">居中对齐文本</nl-text>
      <nl-text type="body1" align="right">右对齐文本</nl-text>

      <view class="text-divider"></view>

      <nl-text type="body1" bold="{{true}}">加粗文本</nl-text>
      <nl-text type="body1" italic="{{true}}">斜体文本</nl-text>
      <nl-text type="body1" truncate="{{true}}">这是一段很长的文本，将会被截断，这是一段很长的文本，将会被截断，这是一段很长的文本，将会被截断。</nl-text>
    </view>
  </view>

  <view class="section">
    <view class="section-title">间距测试</view>
    <view class="section-content">
      <view class="spacing-block xs">超小间距 (XS)</view>
      <view class="spacing-block sm">小间距 (SM)</view>
      <view class="spacing-block md">中等间距 (MD)</view>
      <view class="spacing-block lg">大间距 (LG)</view>
      <view class="spacing-block xl">超大间距 (XL)</view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">圆角测试</view>
    <view class="section-content radius-content">
      <view class="radius-block none">无圆角</view>
      <view class="radius-block xs">超小圆角 (XS)</view>
      <view class="radius-block sm">小圆角 (SM)</view>
      <view class="radius-block md">中等圆角 (MD)</view>
      <view class="radius-block lg">大圆角 (LG)</view>
      <view class="radius-block xl">超大圆角 (XL)</view>
      <view class="radius-block full">完全圆角</view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">阴影测试</view>
    <view class="section-content shadow-content">
      <view class="shadow-block none">无阴影</view>
      <view class="shadow-block xs">超小阴影 (XS)</view>
      <view class="shadow-block sm">小阴影 (SM)</view>
      <view class="shadow-block md">中等阴影 (MD)</view>
      <view class="shadow-block lg">大阴影 (LG)</view>
      <view class="shadow-block xl">超大阴影 (XL)</view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">图标测试</view>
    <view class="section-content">
      <view class="icon-grid">
        <view class="icon-item">
          <nl-icon name="home" size="small"></nl-icon>
          <view class="icon-label">home (小)</view>
        </view>
        <view class="icon-item">
          <nl-icon name="home" size="medium"></nl-icon>
          <view class="icon-label">home (中)</view>
        </view>
        <view class="icon-item">
          <nl-icon name="home" size="large"></nl-icon>
          <view class="icon-label">home (大)</view>
        </view>
        <view class="icon-item">
          <nl-icon name="home" active="{{true}}"></nl-icon>
          <view class="icon-label">home (激活)</view>
        </view>
      </view>

      <view class="icon-grid">
        <view class="icon-item">
          <nl-icon name="search" color="primary"></nl-icon>
          <view class="icon-label">search (主色)</view>
        </view>
        <view class="icon-item">
          <nl-icon name="search" color="secondary"></nl-icon>
          <view class="icon-label">search (次色)</view>
        </view>
        <view class="icon-item">
          <nl-icon name="search" color="success"></nl-icon>
          <view class="icon-label">search (成功)</view>
        </view>
        <view class="icon-item">
          <nl-icon name="search" color="error"></nl-icon>
          <view class="icon-label">search (错误)</view>
        </view>
      </view>

      <view class="icon-grid">
        <view class="icon-item">
          <nl-icon name="user"></nl-icon>
          <view class="icon-label">user</view>
        </view>
        <view class="icon-item">
          <nl-icon name="book"></nl-icon>
          <view class="icon-label">book</view>
        </view>
        <view class="icon-item">
          <nl-icon name="grid"></nl-icon>
          <view class="icon-label">grid</view>
        </view>
        <view class="icon-item">
          <nl-icon name="list"></nl-icon>
          <view class="icon-label">list</view>
        </view>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">输入框测试</view>
    <view class="section-content">
      <nl-input label="基础输入框" placeholder="请输入内容"></nl-input>
      <nl-input label="带图标输入框" placeholder="请输入搜索内容" prefixIcon="search"></nl-input>
      <nl-input label="可清除输入框" placeholder="请输入内容" clearable="{{true}}"></nl-input>
      <nl-input label="禁用输入框" placeholder="禁用状态" disabled="{{true}}"></nl-input>
      <nl-input label="错误输入框" placeholder="请输入内容" error="{{true}}" errorMessage="输入内容有误"></nl-input>
      <nl-input label="密码输入框" placeholder="请输入密码" type="password"></nl-input>
      <nl-input label="带计数输入框" placeholder="请输入内容" maxlength="{{20}}" showCount="{{true}}"></nl-input>
    </view>
  </view>

  <view class="section">
    <view class="section-title">卡片测试</view>
    <view class="section-content">
      <nl-card title="基础卡片" subtitle="这是一个基础卡片">
        <view>这是卡片内容区域，可以放置任何内容。</view>
      </nl-card>

      <nl-card title="带阴影卡片" subtitle="这是一个带阴影的卡片" shadow="always">
        <view>这是卡片内容区域，可以放置任何内容。</view>
      </nl-card>

      <nl-card title="圆角卡片" subtitle="这是一个大圆角卡片" radius="large">
        <view>这是卡片内容区域，可以放置任何内容。</view>
      </nl-card>

      <nl-card title="可点击卡片" subtitle="点击有反馈效果" clickable="{{true}}">
        <view>点击此卡片会有反馈效果。</view>
      </nl-card>
    </view>
  </view>

  <view class="section">
    <view class="section-title">标签测试</view>
    <view class="section-content">
      <view class="tag-row">
        <nl-tag text="默认标签" type="default"></nl-tag>
        <nl-tag text="主要标签" type="primary"></nl-tag>
        <nl-tag text="成功标签" type="success"></nl-tag>
        <nl-tag text="警告标签" type="warning"></nl-tag>
        <nl-tag text="错误标签" type="error"></nl-tag>
      </view>

      <view class="tag-row">
        <nl-tag text="朴素标签" type="primary" plain="{{true}}"></nl-tag>
        <nl-tag text="圆角标签" type="success" round="{{true}}"></nl-tag>
        <nl-tag text="标记标签" type="warning" mark="{{true}}"></nl-tag>
        <nl-tag text="可关闭标签" type="error" closable="{{true}}"></nl-tag>
      </view>

      <view class="tag-row">
        <nl-tag text="小标签" size="small" type="primary"></nl-tag>
        <nl-tag text="中等标签" size="medium" type="primary"></nl-tag>
        <nl-tag text="大标签" size="large" type="primary"></nl-tag>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">徽章测试</view>
    <view class="section-content">
      <view class="badge-row">
        <nl-badge content="5">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge content="10" type="primary">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge content="99+" type="success">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge content="NEW" type="warning">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge content="HOT" type="error">
          <view class="badge-box"></view>
        </nl-badge>
      </view>

      <view class="badge-row">
        <nl-badge dot="{{true}}">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge dot="{{true}}" type="primary">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge dot="{{true}}" type="success">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge dot="{{true}}" type="warning">
          <view class="badge-box"></view>
        </nl-badge>

        <nl-badge dot="{{true}}" type="error">
          <view class="badge-box"></view>
        </nl-badge>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">分割线测试</view>
    <view class="section-content">
      <nl-text type="body1">默认分割线</nl-text>
      <nl-divider></nl-divider>

      <nl-text type="body1">虚线分割线</nl-text>
      <nl-divider type="dashed"></nl-divider>

      <nl-text type="body1">点线分割线</nl-text>
      <nl-divider type="dotted"></nl-divider>

      <nl-text type="body1">带文字分割线</nl-text>
      <nl-divider withText="{{true}}">文字内容</nl-divider>

      <nl-text type="body1">左侧文字分割线</nl-text>
      <nl-divider withText="{{true}}" textPosition="left">左侧文字</nl-divider>

      <nl-text type="body1">右侧文字分割线</nl-text>
      <nl-divider withText="{{true}}" textPosition="right">右侧文字</nl-divider>

      <nl-text type="body1">自定义颜色分割线</nl-text>
      <nl-divider color="var(--primary-color)"></nl-divider>

      <view class="vertical-divider-container">
        <nl-text type="body1">文本1</nl-text>
        <nl-divider direction="vertical"></nl-divider>
        <nl-text type="body1">文本2</nl-text>
        <nl-divider direction="vertical"></nl-divider>
        <nl-text type="body1">文本3</nl-text>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">开关测试</view>
    <view class="section-content">
      <view class="switch-row">
        <nl-switch label="默认开关"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="主要开关" type="primary" value="{{true}}"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="成功开关" type="success"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="警告开关" type="warning" value="{{true}}"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="错误开关" type="error"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="禁用开关" disabled="{{true}}"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="加载中开关" loading="{{true}}"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="右侧标签" labelPosition="right"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="小尺寸" size="small"></nl-switch>
      </view>

      <view class="switch-row">
        <nl-switch label="大尺寸" size="large" value="{{true}}"></nl-switch>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">主题切换</view>
    <view class="section-content">
      <view class="theme-switch">
        <view class="switch-label">深色模式</view>
        <nl-switch value="{{isDarkMode}}" bindchange="toggleTheme" type="primary"></nl-switch>
      </view>
    </view>
  </view>
</view>
