/* pages/test/theme-test.wxss */
.container {
  padding: var(--space-md);
  padding-bottom: var(--space-3xl);
}

.section {
  margin-bottom: var(--space-xl);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin-bottom: var(--space-sm);
  padding-bottom: var(--space-xs);
  border-bottom: 1px solid var(--divider-color);
}

.section-content {
  padding: var(--space-sm);
}

/* 文本样式测试 */
.text-divider {
  height: 1px;
  background-color: var(--divider-color);
  margin: var(--space-md) 0;
}

/* 图标测试样式 */
.icon-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: var(--space-md);
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 25%;
  padding: var(--space-sm);
  box-sizing: border-box;
}

.icon-label {
  margin-top: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  text-align: center;
}

/* 按钮测试样式 */
.button-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-sm);
}

/* 颜色块样式 */
.color-block {
  padding: var(--space-md);
  margin-bottom: var(--space-sm);
  border-radius: var(--radius-md);
  color: var(--white);
  font-weight: var(--font-weight-medium);
}

.color-block.primary {
  background-color: var(--primary-color);
}

.color-block.secondary {
  background-color: var(--secondary-color);
}

.color-block.success {
  background-color: var(--success-color);
}

.color-block.warning {
  background-color: var(--warning-color);
}

.color-block.error {
  background-color: var(--error-color);
}

.color-block.info {
  background-color: var(--info-color);
}

/* 文本样式 */
.text-block {
  margin-bottom: var(--space-md);
}

.text-block.h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-color-primary);
}

.text-block.h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-color-primary);
}

.text-block.h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
  color: var(--text-color-primary);
}

.text-block.body1 {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-color-primary);
}

.text-block.body2 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-color-primary);
}

.text-block.caption {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-color-secondary);
}

/* 间距测试 */
.spacing-block {
  background-color: var(--primary-color-light);
  color: var(--white);
  border-radius: var(--radius-sm);
  text-align: center;
  margin-bottom: var(--space-sm);
}

.spacing-block.xs {
  padding: var(--space-xs);
}

.spacing-block.sm {
  padding: var(--space-sm);
}

.spacing-block.md {
  padding: var(--space-md);
}

.spacing-block.lg {
  padding: var(--space-lg);
}

.spacing-block.xl {
  padding: var(--space-xl);
}

/* 圆角测试 */
.radius-content {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.radius-block {
  width: 100px;
  height: 100px;
  background-color: var(--secondary-color-light);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: var(--font-size-xs);
  padding: var(--space-xs);
}

.radius-block.none {
  border-radius: var(--radius-none);
}

.radius-block.xs {
  border-radius: var(--radius-xs);
}

.radius-block.sm {
  border-radius: var(--radius-sm);
}

.radius-block.md {
  border-radius: var(--radius-md);
}

.radius-block.lg {
  border-radius: var(--radius-lg);
}

.radius-block.xl {
  border-radius: var(--radius-xl);
}

.radius-block.full {
  border-radius: var(--radius-full);
}

/* 阴影测试 */
.shadow-content {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.shadow-block {
  width: 100px;
  height: 100px;
  background-color: var(--bg-color-card);
  color: var(--text-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: var(--font-size-xs);
  padding: var(--space-xs);
  border-radius: var(--radius-md);
}

.shadow-block.none {
  box-shadow: var(--shadow-none);
}

.shadow-block.xs {
  box-shadow: var(--shadow-xs);
}

.shadow-block.sm {
  box-shadow: var(--shadow-sm);
}

.shadow-block.md {
  box-shadow: var(--shadow-md);
}

.shadow-block.lg {
  box-shadow: var(--shadow-lg);
}

.shadow-block.xl {
  box-shadow: var(--shadow-xl);
}

/* 标签测试样式 */
.tag-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-sm);
}

/* 徽章测试样式 */
.badge-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
}

.badge-box {
  width: 40px;
  height: 40px;
  background-color: var(--grey-200);
  border-radius: var(--radius-sm);
}

/* 垂直分割线容器 */
.vertical-divider-container {
  display: flex;
  align-items: center;
  height: 24px;
  margin: var(--space-md) 0;
}

/* 开关测试样式 */
.switch-row {
  margin-bottom: var(--space-md);
}

/* 主题切换 */
.theme-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background-color: var(--bg-color-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.switch-label {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}
