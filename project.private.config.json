{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "AIBubble", "setting": {"compileHotReLoad": true, "ignoreDevUnusedFiles": false, "useStaticServer": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "bigPackageSizeSupport": false, "useIsolateContext": true}, "libVersion": "3.0.2", "condition": {}}