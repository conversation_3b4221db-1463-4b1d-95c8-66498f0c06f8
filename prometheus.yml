global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "api-server"
    metrics_path: /metrics
    static_configs:
      - targets: ["host.docker.internal:9094"]

  - job_name: "k6"
    static_configs:
      - targets: ["host.docker.internal:6565"]
