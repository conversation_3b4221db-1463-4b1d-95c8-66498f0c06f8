# Redis配置文件
# 性能优化设置

# 基础设置
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile "/data/redis.log"
databases 16

# 网络设置
bind 0.0.0.0
protected-mode yes
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 60

# 内存与缓存策略
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 10

# 持久化设置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite yes
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# RDB持久化
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 客户端设置
maxclients 10000

# 高级优化
activerehashing yes
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes
jemalloc-bg-thread yes

# 延迟监控
latency-monitor-threshold 0