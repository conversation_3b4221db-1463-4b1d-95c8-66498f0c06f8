<html><head><meta charset="utf-8"/><title>Test Report</title><style type="text/css">:root {
  --text-primary: #111;
  --text-secondary: #4f4f4f;
  --success: #006633;
  --success-bright: #80ffbf;
  --danger: #cc071e;
  --danger-bright: #fbdfe0;
  --warning: #995c00;
  --warning-bright: #ffeea8;
  --panel: #eee;
  --border: #949494;
  --disabled: #6b6b6b;
}

html,
body {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 16px;
  margin: 0;
  padding: 0;
  color: var(--text-primary);
}
body {
  padding: 2rem 1rem;
}
.jesthtml-content {
  margin: 0 auto;
  max-width: 70rem;
}
header {
  display: flex;
  align-items: center;
}
#title {
  margin: 0;
  flex-grow: 1;
}
#logo {
  height: 4rem;
}
#timestamp {
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

#metadata-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
}

.additional-information-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  color: var(--text-secondary);
}

/** SUMMARY */
#summary {
  color: var(--text-primary);
  display: flex;
  font-family: monospace;
  font-size: 1rem;
}
#summary > div {
  margin-right: 0.5rem;
  background: var(--panel);
  padding: 1rem;
  min-width: 15rem;
}
#summary > div:last-child {
  margin-right: 0;
}
@media only screen and (max-width: 720px) {
  #summary {
    flex-direction: column;
  }
  #summary > div {
    margin-right: 0;
    margin-top: 1rem;
  }
  #summary > div:first-child {
    margin-top: 0;
  }
}

.summary-total {
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.summary-passed {
  color: var(--success);
  border-left: 0.4rem solid var(--success);
  padding-left: 0.5rem;
  margin-bottom: 0.15rem;
}
.summary-failed,
.summary-obsolete-snapshots {
  color: var(--danger);
  border-left: 0.4rem solid var(--danger);
  padding-left: 0.5rem;
  margin-bottom: 0.15rem;
}
.summary-pending {
  color: var(--warning);
  border-left: 0.4rem solid var(--warning);
  padding-left: 0.5rem;
  margin-bottom: 0.15rem;
}
.summary-empty {
  color: var(--disabled);
  border-left: 0.4rem solid var(--disabled);
  margin-bottom: 0.15rem;
}

.test-result {
  padding: 1rem;
  margin-bottom: 0.25rem;
}
.test-result:last-child {
  border: 0;
}
.test-result.passed {
  background-color: var(--success-bright);
  color: var(--success);
}
.test-result.failed {
  background-color: var(--danger-bright);
  color: var(--danger);
}
.test-result.pending {
  background-color: var(--warning-bright);
  color: var(--warning);
}

.test-info {
  display: flex;
  justify-content: space-between;
}
.test-suitename {
  width: 20%;
  text-align: left;
  font-weight: bold;
  word-break: break-word;
}
.test-title {
  width: 40%;
  text-align: left;
  font-style: italic;
}
.test-status {
  width: 20%;
  text-align: right;
}
.test-duration {
  width: 10%;
  text-align: right;
  font-size: 0.85rem;
}

.failureMessages {
  padding: 0 1rem;
  margin-top: 1rem;
  border-top: 1px dashed var(--danger);
}
.failureMessages.suiteFailure {
  border-top: none;
}
.failureMsg {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

.suite-container {
  margin-bottom: 1rem;
}
.suite-info {
  padding: 1rem;
  background-color: var(--panel);
  color: var(--text-secondary);
  border: 0.15rem solid;
  border-color: var(--panel);
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}
.suite-info:hover {
  border-color: var(--border);
  cursor: pointer;
}
.suite-info .suite-path {
  word-break: break-all;
  flex-grow: 1;
  font-family: monospace;
  font-size: 1rem;
}
.suite-info .suite-time {
  margin-left: 1rem;
  padding: 0.2rem 0.3rem;
  font-size: 0.85rem;
}
.suite-info .suite-time.warn {
  background-color: var(--danger);
  color: #fff;
}
.suite-info:before {
  content: "\2303";
  display: inline-block;
  margin-right: 1rem;
  transform: rotate(180deg) translateY(0.15rem);
}
.suite-container[open] .suite-info:before {
  transform: rotate(0deg) translateY(0.15rem);
}

/* CONSOLE LOGS */
.suite-consolelog {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: var(--panel);
}
.suite-consolelog-header {
  font-weight: bold;
}
.suite-consolelog-item {
  padding: 0.5rem;
}
.suite-consolelog-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-consolelog-item-origin {
  color: var(--text-secondary);
  font-weight: bold;
}
.suite-consolelog-item-message {
  color: var(--text-primary);
  font-size: 1rem;
  padding: 0 0.5rem;
}

/* OBSOLETE SNAPSHOTS */
.suite-obsolete-snapshots {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: var(--danger-bright);
  color: var(--danger);
}
.suite-obsolete-snapshots-header {
  font-weight: bold;
}
.suite-obsolete-snapshots-item {
  padding: 0.5rem;
}
.suite-obsolete-snapshots-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-obsolete-snapshots-item-message {
  color: var(--text-primary);
  font-size: 1rem;
  padding: 0 0.5rem;
}
</style></head><body><main class="jesthtml-content"><header><h1 id="title">Test Report</h1></header><section id="metadata-container"><div id="timestamp">Started: 2025-05-07 11:30:28</div><div id="summary"><div id="suite-summary"><div class="summary-total">Suites (9)</div><div class="summary-passed  summary-empty">0 passed</div><div class="summary-failed ">9 failed</div><div class="summary-pending  summary-empty">0 pending</div></div><div id="test-summary"><div class="summary-total">Tests (12)</div><div class="summary-passed  summary-empty">0 passed</div><div class="summary-failed ">12 failed</div><div class="summary-pending  summary-empty">0 pending</div></div></div></section><details id="suite-1" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/api/content/learningPlan/learningPlan.e2e.test.ts</div><div class="suite-time">0s</div></summary><div class="suite-tests"/></details><details id="suite-2" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/learningTemplate.e2e.test.ts</div><div class="suite-time">0s</div></summary><div class="suite-tests"/></details><details id="suite-3" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/api/content/theme/theme.e2e.test.ts</div><div class="suite-time">0s</div></summary><div class="suite-tests"/></details><details id="suite-4" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/api/content/note/note.e2e.test.ts</div><div class="suite-time">0s</div></summary><div class="suite-tests"/></details><details id="suite-5" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/tag/tag.e2e.test.ts</div><div class="suite-time">0s</div></summary><div class="suite-tests"/></details><details id="suite-6" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/api/learning-plan/learning-plan.e2e.test.js</div><div class="suite-time">0.636s</div></summary><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">学习计划API</div><div class="test-title">应该成功创建学习计划</div><div class="test-status">failed</div><div class="test-duration"> </div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">学习计划API</div><div class="test-title">应该成功获取学习计划列表</div><div class="test-status">failed</div><div class="test-duration"> </div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">学习计划API</div><div class="test-title">应该成功获取学习计划详情</div><div class="test-status">failed</div><div class="test-duration"> </div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">学习计划API</div><div class="test-title">应该成功更新学习计划</div><div class="test-status">failed</div><div class="test-duration"> </div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">学习计划API</div><div class="test-title">应该成功删除学习计划</div><div class="test-status">failed</div><div class="test-duration"> </div></div></div></div></details><details id="suite-7" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/content/exercise.e2e.test.ts</div><div class="suite-time">0s</div></summary><div class="suite-tests"/></details><details id="suite-8" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/api/auth/auth.e2e.test.js</div><div class="suite-time">0.591s</div></summary><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">用户认证API</div><div class="test-title">应该成功注册新用户</div><div class="test-status">failed</div><div class="test-duration">0.029s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">用户认证API</div><div class="test-title">应该成功登录用户</div><div class="test-status">failed</div><div class="test-duration">0.003s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">用户认证API</div><div class="test-title">应该成功获取当前用户信息</div><div class="test-status">failed</div><div class="test-duration">0.005s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">用户认证API</div><div class="test-title">应该拒绝无效令牌</div><div class="test-status">failed</div><div class="test-duration">0.002s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">用户认证API</div><div class="test-title">应该成功刷新令牌</div><div class="test-status">failed</div><div class="test-duration">0.002s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">用户认证API</div><div class="test-title">应该成功登出用户</div><div class="test-status">failed</div><div class="test-duration">0.002s</div></div></div></div></details><details id="suite-9" class="suite-container" open=""><summary class="suite-info"><div class="suite-path">/Users/<USER>/5月3日 3/NebulaLearn/backend/tests/e2e/api/health.e2e.test.js</div><div class="suite-time">0.576s</div></summary><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">健康检查API</div><div class="test-title">应该返回健康状态</div><div class="test-status">failed</div><div class="test-duration">0.032s</div></div></div></div></details></main></body></html>