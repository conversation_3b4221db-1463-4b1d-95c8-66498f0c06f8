backend/routes/themeV2.routes.js:router.get('/', themeController.getThemes);
backend/routes/themeV2.routes.js:router.get('/:id', themeController.getThemeById);
backend/routes/themeV2.routes.js:router.delete('/:id/soft-delete', authenticate, authorizeAdmin, themeController.softDeleteTheme);
backend/routes/themeV2.routes.js:router.post('/:id/restore', authenticate, authorizeAdmin, themeController.restoreTheme);
backend/routes/themeV2.routes.js:router.get('/deleted', authenticate, authorizeAdmin, themeController.getDeletedThemes);
backend/routes/health.routes.js:router.get('/', async (req, res) => {
backend/routes/health.routes.js:router.get('/liveness', (req, res) => {
backend/routes/health.routes.js:router.get('/readiness', async (req, res) => {
backend/routes/health.routes.js:router.get('/database', healthController.databaseHealthCheck);
backend/routes/health.routes.js:router.get('/redis', healthController.redisHealthCheck);
backend/routes/health.routes.js:router.get('/system', healthController.systemHealthCheck);
backend/routes/health.routes.js:router.get('/full', healthController.fullHealthCheck);
backend/routes/squareV2.routes.js:router.get(
backend/routes/squareV2.routes.js:router.get(
backend/routes/squareV2.routes.js:router.get(
backend/routes/gamification/achievementV2.routes.ts:  router.get(
backend/routes/gamification/achievementV2.routes.ts:  router.get(
backend/routes/gamification/achievementV2.routes.ts:  router.get(
backend/routes/gamification/achievementV2.routes.ts:  router.post(
backend/routes/gamification/achievementV2.routes.ts:  router.put(
backend/routes/gamification/achievementV2.routes.ts:  router.delete(
backend/routes/gamification/achievementV2.routes.ts:  router.post(
backend/routes/gamification/achievementV2.routes.ts:  router.post(
backend/routes/gamification/levelV2.routes.ts:  router.get(
backend/routes/gamification/levelV2.routes.ts:  router.get(
backend/routes/gamification/levelV2.routes.ts:  router.get(
backend/routes/gamification/levelV2.routes.ts:  router.post(
backend/routes/gamification/levelV2.routes.ts:  router.put(
backend/routes/gamification/levelV2.routes.ts:  router.delete(
backend/routes/gamification/levelV2.routes.ts:  router.post(
backend/routes/gamification/levelV2.routes.ts:  router.post(
backend/routes/gamification/badgeV2.routes.ts:  router.get(
backend/routes/gamification/badgeV2.routes.ts:  router.get(
backend/routes/gamification/badgeV2.routes.ts:  router.get(
backend/routes/gamification/badgeV2.routes.ts:  router.post(
backend/routes/gamification/badgeV2.routes.ts:  router.put(
backend/routes/gamification/badgeV2.routes.ts:  router.delete(
backend/routes/gamification/badgeV2.routes.ts:  router.post(
backend/routes/gamification/badgeV2.routes.ts:  router.post(
backend/routes/gamification/badgeV2.routes.ts:  router.post(
backend/routes/gamification/badgeV2.routes.ts:  router.post(
backend/routes/deadLetterQueue.routes.js:router.get('/', deadLetterQueueController.getDeadLetterQueue);
backend/routes/deadLetterQueue.routes.js:router.get('/:id', deadLetterQueueController.getDeadLetterQueueItem);
backend/routes/deadLetterQueue.routes.js:router.post('/:id/retry', deadLetterQueueController.retryDeadLetterQueueItem);
backend/routes/deadLetterQueue.routes.js:router.post('/:id/resolve', deadLetterQueueController.resolveDeadLetterQueueItem);
backend/routes/noteV2.routes.js:router.get(
backend/routes/noteV2.routes.js:router.get(
backend/routes/noteV2.routes.js:router.get(
backend/routes/noteV2.routes.js:router.post(
backend/routes/noteV2.routes.js:router.put(
backend/routes/noteV2.routes.js:router.delete(
backend/routes/noteV2.routes.js:router.delete(
backend/routes/noteV2.routes.js:router.post(
backend/routes/noteV2.routes.js:router.get(
backend/routes/noteV2.routes.js:router.post(
backend/routes/noteV2.routes.js:router.post(
backend/routes/insightV2.routes.js:router.get(
backend/routes/insightV2.routes.js:router.get(
backend/routes/insightV2.routes.js:router.post(
backend/routes/insightV2.routes.js:router.put(
backend/routes/insightV2.routes.js:router.delete(
backend/routes/insightV2.routes.js:router.delete(
backend/routes/insightV2.routes.js:router.post(
backend/routes/insightV2.routes.js:router.get(
backend/routes/dailyContentV2.routes.js:router.get('/plan/:planId', authenticate, dailyContentController.getPlanContents);
backend/routes/dailyContentV2.routes.js:router.get('/:id', authenticate, dailyContentController.getContentById);
backend/routes/dailyContentV2.routes.js:router.put('/:id', authenticate, dailyContentController.updateContent);
backend/routes/dailyContentV2.routes.js:router.delete('/:id/soft-delete', authenticate, dailyContentController.softDeleteContent);
backend/routes/dailyContentV2.routes.js:router.post('/:id/restore', authenticate, dailyContentController.restoreContent);
backend/routes/dailyContentV2.routes.js:router.get('/plan/:planId/deleted', authenticate, dailyContentController.getDeletedContents);
backend/routes/exerciseV2.routes.js:router.get(
backend/routes/exerciseV2.routes.js:router.get(
backend/routes/exerciseV2.routes.js:router.post(
backend/routes/exerciseV2.routes.js:router.put(
backend/routes/exerciseV2.routes.js:router.delete(
backend/routes/exerciseV2.routes.js:router.delete(
backend/routes/exerciseV2.routes.js:router.post(
backend/routes/exerciseV2.routes.js:router.get(
backend/routes/errorMonitor.routes.js:router.get(
backend/routes/errorMonitor.routes.js:router.post(
backend/routes/errorMonitor.routes.js:router.get(
backend/routes/errorMonitor.routes.js:router.get(
backend/routes/v2/user.routes.ts:router.post(
backend/routes/v2/user.routes.ts:router.get(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.delete(
backend/routes/v2/user.routes.ts:router.post(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.post(
backend/routes/v2/user.routes.ts:router.delete(
backend/routes/v2/user.routes.ts:router.get(
backend/routes/v2/user.routes.ts:router.get(
backend/routes/v2/user.routes.ts:router.get(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.get(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/user.routes.ts:router.put(
backend/routes/v2/learningTemplate.routes.ts:router.post('/', authMiddleware, (req, res) => learningTemplateController.createTemplate(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.put('/:id', authMiddleware, (req, res) => learningTemplateController.updateTemplate(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.delete('/:id', authMiddleware, (req, res) => learningTemplateController.deleteTemplate(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.post('/:id/restore', authMiddleware, (req, res) => learningTemplateController.restoreTemplate(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.post('/:id/publish', authMiddleware, (req, res) => learningTemplateController.publishTemplate(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.post('/:id/archive', authMiddleware, (req, res) => learningTemplateController.archiveTemplate(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.post('/:id/rating', authMiddleware, (req, res) => learningTemplateController.addRating(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.get('/:id', (req, res) => learningTemplateController.getTemplate(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.get('/', (req, res) => learningTemplateController.getTemplates(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.get('/search', (req, res) => learningTemplateController.searchTemplates(req, res));
backend/routes/v2/learningTemplate.routes.ts:router.get('/recommended-tags', (req, res) => learningTemplateController.getRecommendedTags(req, res));
backend/routes/v2/password-reset.routes.ts:router.post(
backend/routes/v2/password-reset.routes.ts:router.post(
backend/routes/v2/password-reset.routes.ts:router.get(
backend/routes/v2/password-reset.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/v2/auth.routes.ts:router.post(
backend/routes/learningPlanV2.routes.js:router.get('/', authenticate, learningPlanController.getUserPlans);
backend/routes/learningPlanV2.routes.js:router.get('/:id', authenticate, learningPlanController.getPlanById);
backend/routes/learningPlanV2.routes.js:router.post('/', authenticate, sanitizeInput(['description']), learningPlanController.createPlan);
backend/routes/learningPlanV2.routes.js:router.put('/:id', authenticate, sanitizeInput(['description']), learningPlanController.updatePlan);
backend/routes/learningPlanV2.routes.js:router.delete('/:id/soft-delete', authenticate, learningPlanController.softDeletePlan);
backend/routes/learningPlanV2.routes.js:router.post('/:id/restore', authenticate, learningPlanController.restorePlan);
backend/routes/learningPlanV2.routes.js:router.get('/deleted', authenticate, learningPlanController.getDeletedPlans);
backend/routes/ai.routes.js:router.get(
backend/routes/ai.routes.js:router.post(
backend/routes/ai.routes.js:router.post(
backend/routes/ai.routes.js:router.post(
backend/routes/statisticsV2.routes.js:router.get(
backend/routes/statisticsV2.routes.js:router.get(
backend/routes/statisticsV2.routes.js:router.post(
backend/routes/statisticsV2.routes.js:router.get(
backend/routes/statisticsV2.routes.js:router.get(
backend/routes/statisticsV2.routes.js:router.get(
backend/routes/tagV2.routes.js:router.delete(
backend/routes/tagV2.routes.js:router.post(
backend/routes/tagV2.routes.js:router.get(
backend/routes/batchOperation.routes.js:router.post('/tags/soft-delete', authenticate, authorizeAdmin, batchOperationController.batchSoftDeleteTags);
backend/routes/batchOperation.routes.js:router.post('/tags/restore', authenticate, authorizeAdmin, batchOperationController.batchRestoreTags);
backend/routes/batchOperation.routes.js:router.post('/users/soft-delete', authenticate, authorizeAdmin, batchOperationController.batchSoftDeleteUsers);
backend/routes/batchOperation.routes.js:router.post('/users/restore', authenticate, authorizeAdmin, batchOperationController.batchRestoreUsers);
backend/routes/batchOperation.routes.js:router.post('/notes/soft-delete', authenticate, batchOperationController.batchSoftDeleteNotes);
backend/routes/batchOperation.routes.js:router.post('/notes/restore', authenticate, batchOperationController.batchRestoreNotes);
backend/routes/batchOperation.routes.js:router.post('/insights/soft-delete', authenticate, batchOperationController.batchSoftDeleteInsights);
backend/routes/batchOperation.routes.js:router.post('/insights/restore', authenticate, batchOperationController.batchRestoreInsights);
backend/routes/cleanup.routes.js:router.get('/config', authenticate, authorizeAdmin, cleanupController.getCleanupConfig);
backend/routes/cleanup.routes.js:router.put('/config', authenticate, authorizeAdmin, cleanupController.updateCleanupConfig);
backend/routes/cleanup.routes.js:router.post('/run', authenticate, authorizeAdmin, cleanupController.runCleanup);
backend/routes/cleanup.routes.js:router.post('/start', authenticate, authorizeAdmin, cleanupController.startAutoCleanup);
backend/routes/cleanup.routes.js:router.post('/stop', authenticate, authorizeAdmin, cleanupController.stopAutoCleanup);
backend/routes/statisticsMonitor.routes.js:router.get(
backend/routes/statisticsMonitor.routes.js:router.get(
backend/routes/statisticsMonitor.routes.js:router.get(
backend/routes/statisticsMonitor.routes.js:router.get(
backend/routes/statisticsMonitor.routes.js:router.post(
backend/routes/mockData.routes.js:router.post(
