# 
# 
PORT
NODE_ENV
# 
# 
DB_HOST
DB_PORT
DB_NAME
DB_USER
DB_PASSWORD
DB_ROOT_PASSWORD
# 
# 
REDIS_URL
REDIS_PASSWORD
# 
# 
JWT_SECRET
JWT_EXPIRES_IN
# 
# 
WECHAT_APP_ID
WECHAT_APP_SECRET
# 
# 
AI_PROVIDER
DASHSCOPE_API_KEY
ARK_API_KEY
HUNYUAN_SECRET_ID
HUNYUAN_SECRET_KEY
# 
# 
CORS_ORIGIN
RATE_LIMIT_MAX
# 
# 
LOG_LEVEL
LOG_FILE_PATH
# 
# 
ENABLE_API_DOCS
ENABLE_DETAILED_ERRORS
ENABLE_PERFORMANCE_MONITORING
# 
# 
INTEGRATION_TEST_MODE
MOCK_EXTERNAL_SERVICES
MOCK_PORT
# 
# 
PRODUCTION_DOMAIN
SSL_CERT_PATH
SSL_KEY_PATH
# 
# 
ENABLE_HEALTH_CHECK
HEALTH_CHECK_ENDPOINT
ERROR_REPORTING_SERVICE
ERROR_REPORTING_API_KEY
# 
# 
SMTP_HOST
SMTP_PORT
SMTP_USER
SMTP_PASSWORD
SMS_PROVIDER
SMS_API_KEY
FILE_STORAGE_PROVIDER
FILE_STORAGE_PATH
CLOUD_STORAGE_BUCKET
CLOUD_STORAGE_ACCESS_KEY
CLOUD_STORAGE_SECRET_KEY
# 
# 
NODE_MAX_OLD_SPACE_SIZE
DB_POOL_MAX
DB_POOL_MIN
REDIS_POOL_MAX
# 
# 
DEBUG_MODE
DEBUG_NAMESPACE
ENABLE_SQL_LOGGING
ENABLE_REQUEST_LOGGING
