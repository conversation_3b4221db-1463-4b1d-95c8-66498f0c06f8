backend/models/userContentProgress.model.js:    type: DataTypes.INTEGER,
backend/models/userContentProgress.model.js:    type: DataTypes.BIGINT,
backend/models/userContentProgress.model.js:    type: DataTypes.ENUM('exercise', 'insight', 'daily_content', 'note'),
backend/models/userContentProgress.model.js:    type: DataTypes.INTEGER,
backend/models/userContentProgress.model.js:    type: DataTypes.INTEGER,
backend/models/userContentProgress.model.js:    type: DataTypes.INTEGER,
backend/models/userContentProgress.model.js:    type: DataTypes.ENUM('not_started', 'in_progress', 'completed', 'skipped'),
backend/models/userContentProgress.model.js:    type: DataTypes.DATE,
backend/models/userContentProgress.model.js:    type: DataTypes.DATE,
backend/models/userContentProgress.model.js:    type: DataTypes.ENUM('not_started', 'beginner', 'familiar', 'proficient', 'expert'),
backend/models/learningTemplate.model.js:    type: DataTypes.INTEGER,
backend/models/learningTemplate.model.js:    type: DataTypes.INTEGER,
backend/models/learningTemplate.model.js:    type: DataTypes.STRING(100),
backend/models/learningTemplate.model.js:    type: DataTypes.TEXT,
backend/models/learningTemplate.model.js:    type: DataTypes.STRING(255),
backend/models/learningTemplate.model.js:    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
backend/models/learningTemplate.model.js:    type: DataTypes.INTEGER,
backend/models/learningTemplate.model.js:    type: DataTypes.INTEGER,
backend/models/learningTemplate.model.js:    type: DataTypes.BOOLEAN,
backend/models/learningTemplate.model.js:    type: DataTypes.BIGINT,
backend/models/learningTemplate.model.js:    type: DataTypes.INTEGER,
backend/models/learningTemplate.model.js:    type: DataTypes.DECIMAL(2, 1),
backend/models/learningTemplate.model.js:    type: DataTypes.INTEGER,
backend/models/learningTemplate.model.js:    type: DataTypes.DECIMAL(10, 2),
backend/models/learningTemplate.model.js:    type: DataTypes.ENUM('draft', 'published', 'archived'),
backend/models/userFeatureAccess.model.js:    type: DataTypes.INTEGER,
backend/models/userFeatureAccess.model.js:    type: DataTypes.BIGINT,
backend/models/userFeatureAccess.model.js:    type: DataTypes.INTEGER,
backend/models/userFeatureAccess.model.js:    type: DataTypes.BOOLEAN,
backend/models/userFeatureAccess.model.js:    type: DataTypes.BIGINT,
backend/models/userFeatureAccess.model.js:    type: DataTypes.DATE,
backend/models/userFeatureAccess.model.js:    defaultValue: DataTypes.NOW,
backend/models/userFeatureAccess.model.js:    type: DataTypes.DATE,
backend/models/userFeatureAccess.model.js:    type: DataTypes.STRING(255),
backend/models/noteComment.model.js:    type: DataTypes.INTEGER,
backend/models/noteComment.model.js:    type: DataTypes.INTEGER,
backend/models/noteComment.model.js:    type: DataTypes.BIGINT,
backend/models/noteComment.model.js:    type: DataTypes.TEXT,
backend/models/noteComment.model.js:    type: DataTypes.INTEGER,
backend/models/noteComment.model.js:    type: DataTypes.INTEGER,
backend/models/noteComment.model.js:    type: DataTypes.ENUM('active', 'hidden', 'deleted'),
backend/models/tagLike.model.js:    type: DataTypes.INTEGER,
backend/models/tagLike.model.js:    type: DataTypes.BIGINT,
backend/models/tagLike.model.js:    type: DataTypes.INTEGER,
backend/models/userNotificationSetting.model.js:    type: DataTypes.INTEGER,
backend/models/userNotificationSetting.model.js:    type: DataTypes.BIGINT,
backend/models/userNotificationSetting.model.js:    type: DataTypes.ENUM('like', 'comment', 'follow', 'achievement', 'system', 'reminder', 'template_update'),
backend/models/userNotificationSetting.model.js:    type: DataTypes.BOOLEAN,
backend/models/featureFlag.model.js:    type: DataTypes.INTEGER,
backend/models/featureFlag.model.js:    type: DataTypes.STRING(100),
backend/models/featureFlag.model.js:    type: DataTypes.STRING(100),
backend/models/featureFlag.model.js:    type: DataTypes.TEXT,
backend/models/featureFlag.model.js:    type: DataTypes.BOOLEAN,
backend/models/featureFlag.model.js:    type: DataTypes.ENUM('all', 'admin', 'beta', 'whitelist'),
backend/models/featureFlag.model.js:    type: DataTypes.INTEGER,
backend/models/featureFlag.model.js:    type: DataTypes.DATE,
backend/models/featureFlag.model.js:    type: DataTypes.DATE,
backend/models/featureFlag.model.js:    type: DataTypes.JSON,
backend/models/noteLike.model.js:    type: DataTypes.INTEGER,
backend/models/noteLike.model.js:    type: DataTypes.BIGINT,
backend/models/noteLike.model.js:    type: DataTypes.INTEGER,
backend/models/learningActivity.model.js:    type: DataTypes.BIGINT,
backend/models/learningActivity.model.js:    type: DataTypes.BIGINT,
backend/models/learningActivity.model.js:    type: DataTypes.INTEGER,
backend/models/learningActivity.model.js:    type: DataTypes.ENUM(
backend/models/learningActivity.model.js:    type: DataTypes.STRING(50),
backend/models/learningActivity.model.js:    type: DataTypes.ENUM('tag', 'exercise', 'insight', 'note', 'plan', 'achievement', 'badge', 'user'),
backend/models/learningActivity.model.js:    type: DataTypes.INTEGER,
backend/models/learningActivity.model.js:    type: DataTypes.INTEGER,
backend/models/learningActivity.model.js:    type: DataTypes.INTEGER,
backend/models/learningActivity.model.js:    type: DataTypes.JSON,
backend/models/learningActivity.model.js:    type: DataTypes.JSON,
backend/models/learningActivity.model.js:    type: DataTypes.STRING(50),
backend/models/bubbleContent.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleContent.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleContent.model.js:    type: DataTypes.ENUM('exercise', 'insight', 'note', 'quiz', 'media'),
backend/models/bubbleContent.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleContent.model.js:    type: DataTypes.STRING(100),
backend/models/bubbleContent.model.js:    type: DataTypes.STRING(255),
backend/models/bubbleContent.model.js:    type: DataTypes.JSON,
backend/models/bubbleContent.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleContent.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleContent.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleContent.model.js:    type: DataTypes.FLOAT,
backend/models/bubbleContent.model.js:    type: DataTypes.BOOLEAN,
backend/models/dailyContent.model.js:    type: DataTypes.INTEGER,
backend/models/dailyContent.model.js:    type: DataTypes.INTEGER,
backend/models/dailyContent.model.js:    type: DataTypes.INTEGER,
backend/models/dailyContent.model.js:    type: DataTypes.STRING(100),
backend/models/dailyContent.model.js:    type: DataTypes.TEXT,
backend/models/dailyContent.model.js:    type: DataTypes.TEXT,
backend/models/dailyContent.model.js:    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
backend/models/dailyContent.model.js:    type: DataTypes.INTEGER,
backend/models/dailyContent.model.js:    type: DataTypes.BOOLEAN,
backend/models/dailyContent.model.js:    type: DataTypes.DATE,
backend/models/dailyContent.model.js:    type: DataTypes.ENUM('pending', 'active', 'completed', 'skipped'),
backend/models/achievement.model.js:    type: DataTypes.INTEGER,
backend/models/achievement.model.js:    type: DataTypes.STRING(50),
backend/models/achievement.model.js:    type: DataTypes.TEXT,
backend/models/achievement.model.js:    type: DataTypes.STRING(50),
backend/models/achievement.model.js:    type: DataTypes.ENUM('learning', 'social', 'creation', 'special'),
backend/models/achievement.model.js:    type: DataTypes.ENUM('easy', 'medium', 'hard', 'expert'),
backend/models/achievement.model.js:    type: DataTypes.INTEGER,
backend/models/achievement.model.js:    type: DataTypes.STRING(50),
backend/models/achievement.model.js:    type: DataTypes.JSON,
backend/models/achievement.model.js:    type: DataTypes.BOOLEAN,
backend/models/achievement.model.js:    type: DataTypes.BOOLEAN,
backend/models/dailyContentRelation.model.js:    type: DataTypes.INTEGER,
backend/models/dailyContentRelation.model.js:    type: DataTypes.INTEGER,
backend/models/dailyContentRelation.model.js:    type: DataTypes.ENUM('exercise', 'insight', 'note'),
backend/models/dailyContentRelation.model.js:    type: DataTypes.INTEGER,
backend/models/dailyContentRelation.model.js:    type: DataTypes.INTEGER,
backend/models/userSetting.model.js:    type: DataTypes.INTEGER,
backend/models/userSetting.model.js:    type: DataTypes.BIGINT,
backend/models/userSetting.model.js:    type: DataTypes.STRING(20),
backend/models/userSetting.model.js:    type: DataTypes.JSON,
backend/models/userSetting.model.js:    type: DataTypes.JSON,
backend/models/note.model.js:    type: DataTypes.INTEGER,
backend/models/note.model.js:    type: DataTypes.INTEGER,
backend/models/note.model.js:    type: DataTypes.BIGINT,
backend/models/note.model.js:    type: DataTypes.STRING(100),
backend/models/note.model.js:    type: DataTypes.TEXT,
backend/models/note.model.js:    type: DataTypes.STRING(255),
backend/models/note.model.js:    type: DataTypes.INTEGER,
backend/models/note.model.js:    type: DataTypes.INTEGER,
backend/models/note.model.js:    type: DataTypes.INTEGER,
backend/models/note.model.js:    type: DataTypes.BOOLEAN,
backend/models/note.model.js:    type: DataTypes.ENUM('draft', 'published', 'hidden', 'archived'),
backend/models/note.model.js:    type: DataTypes.INTEGER,
backend/models/tagCategory.model.js:    type: DataTypes.INTEGER,
backend/models/tagCategory.model.js:    type: DataTypes.STRING(50),
backend/models/tagCategory.model.js:    type: DataTypes.TEXT,
backend/models/tagCategory.model.js:    type: DataTypes.INTEGER,
backend/models/tagCategory.model.js:    type: DataTypes.INTEGER,
backend/models/tagCategory.model.js:    type: DataTypes.INTEGER,
backend/models/tagCategory.model.js:    type: DataTypes.STRING(50),
backend/models/tagCategory.model.js:    type: DataTypes.STRING(20),
backend/models/tagCategory.model.js:    type: DataTypes.BOOLEAN,
backend/models/tagCategory.model.js:    type: DataTypes.INTEGER,
backend/models/commentLike.model.js:    type: DataTypes.INTEGER,
backend/models/commentLike.model.js:    type: DataTypes.BIGINT,
backend/models/commentLike.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.BIGINT,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.INTEGER,
backend/models/userLearningStats.model.js:    type: DataTypes.DATEONLY,
backend/models/userLearningStats.model.js:    type: DataTypes.JSON,
backend/models/userLearningStats.model.js:    type: DataTypes.JSON,
backend/models/systemConfig.model.js:    type: DataTypes.INTEGER,
backend/models/systemConfig.model.js:    type: DataTypes.STRING(100),
backend/models/systemConfig.model.js:    type: DataTypes.TEXT,
backend/models/systemConfig.model.js:    type: DataTypes.ENUM('string', 'number', 'boolean', 'json', 'array'),
backend/models/systemConfig.model.js:    type: DataTypes.TEXT,
backend/models/systemConfig.model.js:    type: DataTypes.BOOLEAN,
backend/models/systemConfig.model.js:    type: DataTypes.STRING(50),
backend/models/systemConfig.model.js:    type: DataTypes.INTEGER,
backend/models/level.model.js:    type: DataTypes.INTEGER,
backend/models/level.model.js:    type: DataTypes.INTEGER,
backend/models/level.model.js:    type: DataTypes.STRING(50),
backend/models/level.model.js:    type: DataTypes.INTEGER,
backend/models/level.model.js:    type: DataTypes.STRING(50),
backend/models/level.model.js:    type: DataTypes.JSON,
backend/models/level.model.js:    type: DataTypes.TEXT,
backend/models/bubbleInteraction.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleInteraction.model.js:    type: DataTypes.BIGINT,
backend/models/bubbleInteraction.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleInteraction.model.js:    type: DataTypes.STRING(50),
backend/models/bubbleInteraction.model.js:    type: DataTypes.ENUM('click', 'drag', 'view', 'hold', 'swipe', 'zoom'),
backend/models/bubbleInteraction.model.js:    type: DataTypes.INTEGER,
backend/models/bubbleInteraction.model.js:    type: DataTypes.FLOAT,
backend/models/bubbleInteraction.model.js:    type: DataTypes.FLOAT,
backend/models/bubbleInteraction.model.js:    type: DataTypes.JSON,
backend/models/bubbleInteraction.model.js:    type: DataTypes.JSON,
backend/models/bubbleInteraction.model.js:    type: DataTypes.STRING(50),
backend/models/bubbleInteraction.model.js:    type: DataTypes.STRING(20),
backend/models/bubbleInteraction.model.js:    type: DataTypes.STRING(100),
backend/models/bubbleInteraction.model.js:    type: DataTypes.STRING(20),
backend/models/bubbleInteraction.model.js:    type: DataTypes.STRING(100),
backend/models/bubbleInteraction.model.js:    type: DataTypes.INTEGER,
backend/models/userFollow.model.js:    type: DataTypes.INTEGER,
backend/models/userFollow.model.js:    type: DataTypes.BIGINT,
backend/models/userFollow.model.js:    type: DataTypes.BIGINT,
backend/models/userFollow.model.js:    type: DataTypes.ENUM('active', 'blocked'),
backend/models/notification.model.js:    type: DataTypes.INTEGER,
backend/models/notification.model.js:    type: DataTypes.BIGINT,
backend/models/notification.model.js:    type: DataTypes.BIGINT,
backend/models/notification.model.js:    type: DataTypes.ENUM('like', 'comment', 'follow', 'achievement', 'system', 'reminder', 'badge', 'level_up'),
backend/models/notification.model.js:    type: DataTypes.TEXT,
backend/models/notification.model.js:    type: DataTypes.STRING(50),
backend/models/notification.model.js:    type: DataTypes.STRING(50),
backend/models/notification.model.js:    type: DataTypes.BOOLEAN,
backend/models/notification.model.js:    type: DataTypes.DATE,
backend/models/user.model.js:    type: DataTypes.BIGINT,
backend/models/user.model.js:    type: DataTypes.STRING(64),
backend/models/user.model.js:    type: DataTypes.STRING(20),
backend/models/user.model.js:    type: DataTypes.STRING(50),
backend/models/user.model.js:    type: DataTypes.STRING(255),
backend/models/user.model.js:    type: DataTypes.TINYINT,
backend/models/user.model.js:    type: DataTypes.STRING(100),
backend/models/user.model.js:    type: DataTypes.ENUM('wechat', 'phone', 'github', 'apple'),
backend/models/user.model.js:    type: DataTypes.DATE,
backend/models/user.model.js:    type: DataTypes.INTEGER,
backend/models/user.model.js:    type: DataTypes.INTEGER,
backend/models/user.model.js:    type: DataTypes.INTEGER,
backend/models/user.model.js:    type: DataTypes.BOOLEAN,
backend/models/user.model.js:    type: DataTypes.ENUM('active', 'inactive', 'banned'),
backend/models/bContent.model.js:    type: DataTypes.INTEGER,
backend/models/bContent.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.BIGINT,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.STRING(100),
backend/models/learningPlan.model.js:    type: DataTypes.TEXT,
backend/models/learningPlan.model.js:    type: DataTypes.STRING(255),
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js:    type: DataTypes.ENUM('not_started', 'in_progress', 'completed', 'paused', 'abandoned'),
backend/models/learningPlan.model.js:    type: DataTypes.DATEONLY,
backend/models/learningPlan.model.js:    type: DataTypes.DATEONLY,
backend/models/learningPlan.model.js:    type: DataTypes.BOOLEAN,
backend/models/learningPlan.model.js:    type: DataTypes.BOOLEAN,
backend/models/learningPlan.model.js:    type: DataTypes.BOOLEAN,
backend/models/tagSynonym.model.js:    type: DataTypes.INTEGER,
backend/models/tagSynonym.model.js:    type: DataTypes.INTEGER,
backend/models/tagSynonym.model.js:    type: DataTypes.STRING(10),
backend/models/tagSynonym.model.js:    type: DataTypes.FLOAT,
backend/models/tagSynonym.model.js:    type: DataTypes.DATE,
backend/models/tagSynonym.model.js:    defaultValue: DataTypes.NOW
backend/models/user.model.js.new:    type: DataTypes.BIGINT,
backend/models/user.model.js.new:    type: DataTypes.STRING(64),
backend/models/user.model.js.new:    type: DataTypes.STRING(20),
backend/models/user.model.js.new:    type: DataTypes.STRING(50),
backend/models/user.model.js.new:    type: DataTypes.STRING(255),
backend/models/user.model.js.new:    type: DataTypes.TINYINT,
backend/models/user.model.js.new:    type: DataTypes.STRING(100),
backend/models/user.model.js.new:    type: DataTypes.ENUM('wechat', 'phone', 'github', 'apple'),
backend/models/user.model.js.new:    type: DataTypes.DATE,
backend/models/user.model.js.new:    type: DataTypes.INTEGER,
backend/models/user.model.js.new:    type: DataTypes.INTEGER,
backend/models/user.model.js.new:    type: DataTypes.INTEGER,
backend/models/user.model.js.new:    type: DataTypes.BOOLEAN,
backend/models/user.model.js.new:    type: DataTypes.ENUM('active', 'inactive', 'banned'),
backend/models/theme.model.js:    type: DataTypes.INTEGER,
backend/models/theme.model.js:    type: DataTypes.STRING(50),
backend/models/theme.model.js:    type: DataTypes.STRING(50),
backend/models/theme.model.js:    type: DataTypes.TEXT,
backend/models/theme.model.js:    type: DataTypes.STRING(50),
backend/models/theme.model.js:    type: DataTypes.STRING(20),
backend/models/theme.model.js:    type: DataTypes.STRING(255),
backend/models/theme.model.js:    type: DataTypes.INTEGER,
backend/models/theme.model.js:    type: DataTypes.BOOLEAN,
backend/models/theme.model.js:    type: DataTypes.INTEGER,
backend/models/passwordResetToken.model.js:      type: DataTypes.BIGINT,
backend/models/passwordResetToken.model.js:      type: DataTypes.BIGINT,
backend/models/passwordResetToken.model.js:      type: DataTypes.STRING(100),
backend/models/passwordResetToken.model.js:      type: DataTypes.DATE,
backend/models/passwordResetToken.model.js:      type: DataTypes.BOOLEAN,
backend/models/passwordResetToken.model.js:      type: DataTypes.STRING(50),
backend/models/passwordResetToken.model.js:      type: DataTypes.STRING(255),
backend/models/passwordResetToken.model.js:      type: DataTypes.DATE,
backend/models/passwordResetToken.model.js:      defaultValue: DataTypes.NOW,
backend/models/passwordResetToken.model.js:      type: DataTypes.DATE,
backend/models/passwordResetToken.model.js:      defaultValue: DataTypes.NOW,
backend/models/insight.model.js:    type: DataTypes.INTEGER,
backend/models/insight.model.js:    type: DataTypes.INTEGER,
backend/models/insight.model.js:    type: DataTypes.TEXT,
backend/models/insight.model.js:    type: DataTypes.STRING(100),
backend/models/insight.model.js:    type: DataTypes.TEXT,
backend/models/insight.model.js:    type: DataTypes.BIGINT,
backend/models/insight.model.js:    type: DataTypes.BOOLEAN,
backend/models/insight.model.js:    type: DataTypes.ENUM('draft', 'published', 'archived'),
backend/models/insight.model.js:    type: DataTypes.INTEGER,
backend/models/exercise.model.js:    type: DataTypes.INTEGER,
backend/models/exercise.model.js:    type: DataTypes.INTEGER,
backend/models/exercise.model.js:    type: DataTypes.STRING(100),
backend/models/exercise.model.js:    type: DataTypes.TEXT,
backend/models/exercise.model.js:    type: DataTypes.TEXT,
backend/models/exercise.model.js:    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
backend/models/exercise.model.js:    type: DataTypes.INTEGER,
backend/models/exercise.model.js:    type: DataTypes.BIGINT,
backend/models/exercise.model.js:    type: DataTypes.BOOLEAN,
backend/models/exercise.model.js:    type: DataTypes.ENUM('draft', 'published', 'archived'),
backend/models/dailyRecord.model.js:    type: DataTypes.INTEGER,
backend/models/dailyRecord.model.js:    type: DataTypes.BIGINT,
backend/models/dailyRecord.model.js:    type: DataTypes.DATEONLY,
backend/models/dailyRecord.model.js:    defaultValue: DataTypes.NOW
backend/models/dailyRecord.model.js:    type: DataTypes.INTEGER,
backend/models/dailyRecord.model.js:    type: DataTypes.INTEGER,
backend/models/dailyRecord.model.js:    type: DataTypes.INTEGER,
backend/models/dailyRecord.model.js:    type: DataTypes.INTEGER,
backend/models/dailyRecord.model.js:    type: DataTypes.INTEGER,
backend/models/dailyRecord.model.js:    type: DataTypes.INTEGER,
backend/models/dailyRecord.model.js:    type: DataTypes.BOOLEAN,
backend/models/dailyRecord.model.js:    type: DataTypes.BOOLEAN,
backend/models/dailyRecord.model.js:    type: DataTypes.ENUM('great', 'good', 'neutral', 'bad', 'terrible'),
backend/models/dailyRecord.model.js:    type: DataTypes.TEXT,
backend/models/tag.model.js:    type: DataTypes.INTEGER,
backend/models/tag.model.js:    type: DataTypes.STRING(10),
backend/models/tag.model.js:    type: DataTypes.INTEGER,
backend/models/tag.model.js:    type: DataTypes.BIGINT,
backend/models/tag.model.js:    type: DataTypes.TEXT,
backend/models/tag.model.js:    type: DataTypes.FLOAT,
backend/models/tag.model.js:    type: DataTypes.FLOAT,
backend/models/tag.model.js:    type: DataTypes.INTEGER,
backend/models/tag.model.js:    type: DataTypes.INTEGER,
backend/models/tag.model.js:    type: DataTypes.BOOLEAN,
backend/models/tag.model.js:    type: DataTypes.BOOLEAN,
backend/models/tag.model.js:    type: DataTypes.INTEGER,
backend/models/userBadge.model.js:    type: DataTypes.INTEGER,
backend/models/userBadge.model.js:    type: DataTypes.BIGINT,
backend/models/userBadge.model.js:    type: DataTypes.INTEGER,
backend/models/userBadge.model.js:    type: DataTypes.DATE,
backend/models/userBadge.model.js:    defaultValue: DataTypes.NOW,
backend/models/userBadge.model.js:    type: DataTypes.BOOLEAN,
backend/models/badge.model.js:    type: DataTypes.INTEGER,
backend/models/badge.model.js:    type: DataTypes.STRING(50),
backend/models/badge.model.js:    type: DataTypes.TEXT,
backend/models/badge.model.js:    type: DataTypes.STRING(50),
backend/models/badge.model.js:    type: DataTypes.ENUM('theme', 'skill', 'event', 'special'),
backend/models/badge.model.js:    type: DataTypes.ENUM('common', 'uncommon', 'rare', 'epic', 'legendary'),
backend/models/badge.model.js:    type: DataTypes.BOOLEAN,
backend/models/badge.model.js:    type: DataTypes.TEXT,
backend/models/userAchievement.model.js:    type: DataTypes.INTEGER,
backend/models/userAchievement.model.js:    type: DataTypes.BIGINT,
backend/models/userAchievement.model.js:    type: DataTypes.INTEGER,
backend/models/userAchievement.model.js:    type: DataTypes.DATE,
backend/models/userAchievement.model.js:    defaultValue: DataTypes.NOW,
backend/models/userAchievement.model.js:    type: DataTypes.INTEGER,
backend/models/userAchievement.model.js:    type: DataTypes.BOOLEAN,
backend/models/tagFeedback.model.js:    type: DataTypes.INTEGER,
backend/models/tagFeedback.model.js:    type: DataTypes.INTEGER,
backend/models/tagFeedback.model.js:    type: DataTypes.BIGINT,
backend/models/tagFeedback.model.js:    type: DataTypes.ENUM('irrelevant', 'duplicate', 'inappropriate', 'suggestion', 'other'),
backend/models/tagFeedback.model.js:    type: DataTypes.TEXT,
backend/models/tagFeedback.model.js:    type: DataTypes.ENUM('pending', 'reviewed', 'resolved', 'rejected'),
backend/models/tagFeedback.model.js:    type: DataTypes.TEXT,
backend/models/tagFeedback.model.js:    type: DataTypes.BIGINT,
backend/models/tagFeedback.model.js:    type: DataTypes.DATE,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.BIGINT,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.STRING(100),
backend/models/learningPlan.model.js.new:    type: DataTypes.TEXT,
backend/models/learningPlan.model.js.new:    type: DataTypes.STRING(255),
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.INTEGER,
backend/models/learningPlan.model.js.new:    type: DataTypes.ENUM('not_started', 'in_progress', 'completed', 'paused', 'abandoned'),
backend/models/learningPlan.model.js.new:    type: DataTypes.DATEONLY,
backend/models/learningPlan.model.js.new:    type: DataTypes.DATEONLY,
backend/models/learningPlan.model.js.new:    type: DataTypes.BOOLEAN,
backend/models/learningPlan.model.js.new:    type: DataTypes.BOOLEAN,
backend/models/learningPlan.model.js.new:    type: DataTypes.BOOLEAN,
backend/models/DeadLetterQueue.js:    type: DataTypes.INTEGER,
backend/models/DeadLetterQueue.js:    type: DataTypes.STRING(36),
backend/models/DeadLetterQueue.js:    type: DataTypes.STRING(100),
backend/models/DeadLetterQueue.js:    type: DataTypes.STRING(100),
backend/models/DeadLetterQueue.js:    type: DataTypes.STRING(36),
backend/models/DeadLetterQueue.js:    type: DataTypes.STRING(100),
backend/models/DeadLetterQueue.js:    type: DataTypes.JSON,
backend/models/DeadLetterQueue.js:    type: DataTypes.TEXT,
backend/models/DeadLetterQueue.js:    type: DataTypes.TEXT,
backend/models/DeadLetterQueue.js:    type: DataTypes.INTEGER,
backend/models/DeadLetterQueue.js:    type: DataTypes.INTEGER,
backend/models/DeadLetterQueue.js:    type: DataTypes.DATE,
backend/models/DeadLetterQueue.js:    type: DataTypes.ENUM('pending', 'retrying', 'failed', 'resolved'),
backend/models/DeadLetterQueue.js:    type: DataTypes.DATE,
backend/models/planTag.model.js:    type: DataTypes.INTEGER,
backend/models/planTag.model.js:    type: DataTypes.INTEGER,
backend/models/planTag.model.js:    type: DataTypes.INTEGER,
backend/models/planTag.model.js:    type: DataTypes.FLOAT,
backend/models/planTag.model.js:    type: DataTypes.FLOAT,
backend/models/planTag.model.js:    type: DataTypes.BOOLEAN,
backend/models/planTag.model.js:    type: DataTypes.INTEGER,
