/**
 * 标签分类V2路由
 * 使用DDD实现的标签分类领域
 */
const express = require('express');
const router = express.Router();
const { container, initializeContainer } = require('../backend/infrastructure/config/containerConfig');
const { TagCategoryController } = require('../backend/interfaces/api/controllers/tag/TagCategoryController');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validateRequest } = require('../backend/interfaces/api/middlewares/validateRequestMiddleware');
const { 
  createTagCategorySchema, 
  updateTagCategorySchema 
} = require('../backend/interfaces/api/validators/tag/tagCategoryValidators');

// 确保容器已初始化
if (typeof initializeContainer === 'function') {
  initializeContainer();
}

// 获取控制器实例
const tagCategoryController = new TagCategoryController(container.get('tagCategoryApplicationService'));

// 标签分类CRUD
router.post(
  '/',
  authMiddleware,
  validateRequest(createTagCategorySchema),
  tagCategoryController.createTagCategory.bind(tagCategoryController)
);

router.get(
  '/:id',
  tagCategoryController.getTagCategory.bind(tagCategoryController)
);

router.put(
  '/:id',
  authMiddleware,
  validateRequest(updateTagCategorySchema),
  tagCategoryController.updateTagCategory.bind(tagCategoryController)
);

router.delete(
  '/:id',
  authMiddleware,
  tagCategoryController.deleteTagCategory.bind(tagCategoryController)
);

router.post(
  '/:id/restore',
  authMiddleware,
  tagCategoryController.restoreTagCategory.bind(tagCategoryController)
);

router.get(
  '/',
  tagCategoryController.searchTagCategories.bind(tagCategoryController)
);

// 获取分类下的标签
router.get(
  '/:id/tags',
  tagCategoryController.getTagsByCategory.bind(tagCategoryController)
);

// 获取根分类
router.get(
  '/root/all',
  tagCategoryController.getRootCategories.bind(tagCategoryController)
);

// 获取分类及其子分类
router.get(
  '/:id/children',
  tagCategoryController.getCategoryWithChildren.bind(tagCategoryController)
);

module.exports = router;
