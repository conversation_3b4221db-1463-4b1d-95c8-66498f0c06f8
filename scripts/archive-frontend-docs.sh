#!/bin/bash
# 前端文档归档脚本
# 使用方法: ./scripts/archive-frontend-docs.sh <文档路径>

# 确保脚本以正确的方式运行
if [ $# -eq 0 ]; then
  echo "使用方法: $0 <文档路径>"
  echo "例如: $0 新的前端文档.md"
  exit 1
fi

# 创建归档目录（如果不存在）
if [ ! -d "归档文档/前端文档" ]; then
  mkdir -p "归档文档/前端文档"
  echo "创建归档目录: 归档文档/前端文档"
fi

# 检查文件是否存在
if [ ! -f "$1" ]; then
  echo "错误: 文件 '$1' 不存在"
  exit 1
fi

# 执行归档操作
echo "正在归档文档: $1"
mv "$1" "归档文档/前端文档/"

# 更新README中的文档清单
echo "提示: 请手动更新 归档文档/前端文档/README.md 中的文档清单"
echo "提示: 如果需要，请手动在前端系统升级综合规划.md中整合文档内容"

# 完成
echo "归档完成: $1 已移动到 归档文档/前端文档/"
echo "归档日期: $(date +%Y-%m-%d)" 