/**
 * 检查测试覆盖率
 * 用于生成覆盖率报告并检查是否达到目标
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  RED: '\x1b[31m',
  BLUE: '\x1b[34m'
};

// 打印带颜色的消息
function printColored(message, color) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

// 覆盖率目标
const COVERAGE_TARGETS = {
  statements: 70,
  branches: 60,
  functions: 70,
  lines: 70
};

// 获取命令行参数
const args = process.argv.slice(2);
const strict = args.includes('--strict');

// 运行测试并生成覆盖率报告
try {
  printColored('运行测试并生成覆盖率报告...', COLORS.BLUE);
  execSync('jest --coverage', { stdio: 'inherit' });
  printColored('覆盖率报告已生成!', COLORS.GREEN);
} catch (error) {
  printColored(`测试失败: ${error.message}`, COLORS.RED);
  process.exit(1);
}

// 读取覆盖率摘要
try {
  const coverageSummaryPath = path.join(__dirname, '../coverage/coverage-summary.json');
  if (!fs.existsSync(coverageSummaryPath)) {
    printColored('覆盖率摘要文件不存在!', COLORS.RED);
    process.exit(1);
  }

  const coverageSummary = JSON.parse(fs.readFileSync(coverageSummaryPath, 'utf8')).total;

  // 打印覆盖率摘要
  printColored('\n覆盖率摘要:', COLORS.BLUE);
  printColored(`语句覆盖率: ${coverageSummary.statements.pct}% (目标: ${COVERAGE_TARGETS.statements}%)`, 
    coverageSummary.statements.pct >= COVERAGE_TARGETS.statements ? COLORS.GREEN : COLORS.RED);
  printColored(`分支覆盖率: ${coverageSummary.branches.pct}% (目标: ${COVERAGE_TARGETS.branches}%)`, 
    coverageSummary.branches.pct >= COVERAGE_TARGETS.branches ? COLORS.GREEN : COLORS.RED);
  printColored(`函数覆盖率: ${coverageSummary.functions.pct}% (目标: ${COVERAGE_TARGETS.functions}%)`, 
    coverageSummary.functions.pct >= COVERAGE_TARGETS.functions ? COLORS.GREEN : COLORS.RED);
  printColored(`行覆盖率: ${coverageSummary.lines.pct}% (目标: ${COVERAGE_TARGETS.lines}%)`, 
    coverageSummary.lines.pct >= COVERAGE_TARGETS.lines ? COLORS.GREEN : COLORS.RED);

  // 检查是否达到目标
  const failures = [];
  if (coverageSummary.statements.pct < COVERAGE_TARGETS.statements) {
    failures.push(`语句覆盖率 ${coverageSummary.statements.pct}% 低于目标 ${COVERAGE_TARGETS.statements}%`);
  }
  if (coverageSummary.branches.pct < COVERAGE_TARGETS.branches) {
    failures.push(`分支覆盖率 ${coverageSummary.branches.pct}% 低于目标 ${COVERAGE_TARGETS.branches}%`);
  }
  if (coverageSummary.functions.pct < COVERAGE_TARGETS.functions) {
    failures.push(`函数覆盖率 ${coverageSummary.functions.pct}% 低于目标 ${COVERAGE_TARGETS.functions}%`);
  }
  if (coverageSummary.lines.pct < COVERAGE_TARGETS.lines) {
    failures.push(`行覆盖率 ${coverageSummary.lines.pct}% 低于目标 ${COVERAGE_TARGETS.lines}%`);
  }

  if (failures.length > 0) {
    printColored('\n覆盖率未达到目标:', COLORS.RED);
    failures.forEach(failure => printColored(`- ${failure}`, COLORS.RED));
    
    if (strict) {
      printColored('\n严格模式: 覆盖率未达到目标，退出状态码为1', COLORS.RED);
      process.exit(1);
    } else {
      printColored('\n警告模式: 覆盖率未达到目标，但不会导致失败', COLORS.YELLOW);
    }
  } else {
    printColored('\n所有覆盖率目标已达成!', COLORS.GREEN);
  }
} catch (error) {
  printColored(`解析覆盖率摘要失败: ${error.message}`, COLORS.RED);
  process.exit(1);
}
