/**
 * 检查数据库连接
 * 用于验证数据库连接是否正常
 */
const { sequelize } = require('../config/database');
const logger = require('../config/logger');

// 测试数据库连接
const testConnection = async () => {
  try {
    logger.info('正在测试数据库连接...');
    await sequelize.authenticate();
    logger.info('数据库连接成功！');
    return true;
  } catch (error) {
    logger.error(`数据库连接失败: ${error.message}`);
    logger.error(error.stack);
    return false;
  } finally {
    // 关闭连接
    await sequelize.close();
  }
};

// 执行测试
testConnection()
  .then(success => {
    if (success) {
      console.log('✅ 数据库连接成功');
      process.exit(0);
    } else {
      console.log('❌ 数据库连接失败');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`❌ 发生错误: ${error.message}`);
    process.exit(1);
  });
