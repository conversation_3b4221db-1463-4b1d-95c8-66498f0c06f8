#!/bin/bash

# 为指定用户复制系统默认学习计划
# 支持本地和Docker两种模式
# 用法: ./copy-plan-to-user.sh <用户ID> [local|docker]

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查参数
if [ -z "$1" ]; then
  echo -e "${RED}错误: 请提供用户ID作为参数${NC}"
  echo "用法: ./copy-plan-to-user.sh <用户ID> [local|docker]"
  exit 1
fi

USER_ID=$1
MODE="docker"
if [ "$2" == "local" ]; then
  MODE="local"
fi

echo -e "${GREEN}===== 为用户 $USER_ID 复制默认学习计划 =====${NC}"
echo "运行模式: $MODE"

# Docker模式运行
if [ "$MODE" == "docker" ]; then
  echo -e "${YELLOW}使用Docker模式运行脚本...${NC}"
  
  # 确保后端容器正在运行
  if ! docker ps | grep -q aibubb-backend; then
    echo -e "${RED}错误: 后端容器不在运行状态，请先启动容器${NC}"
    echo "可以使用命令: docker-compose up -d"
    exit 1
  fi
  
  # 在Docker容器中运行脚本
  docker exec -it aibubb-backend node /usr/src/app/scripts/copy-default-plan-to-user.js $USER_ID
  
  echo -e "${GREEN}脚本执行完成!${NC}"
else
  # 本地模式运行
  echo -e "${YELLOW}使用本地模式运行脚本...${NC}"
  
  # 切换到后端目录
  cd "$(dirname "$0")/../backend" || exit
  
  # 执行脚本
  node scripts/copy-default-plan-to-user.js $USER_ID
  
  echo -e "${GREEN}脚本执行完成!${NC}"
fi

exit 0 