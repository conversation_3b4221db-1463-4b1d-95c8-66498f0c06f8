<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AIBUBB 测试覆盖率报告</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      text-align: center;
      margin-bottom: 40px;
    }
    h1 {
      color: #2c3e50;
    }
    .summary {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      margin-bottom: 40px;
    }
    .metric {
      text-align: center;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      min-width: 200px;
      margin: 10px;
    }
    .metric h2 {
      margin-top: 0;
      font-size: 18px;
      color: #7f8c8d;
    }
    .metric .value {
      font-size: 48px;
      font-weight: bold;
    }
    .good {
      color: #27ae60;
    }
    .warning {
      color: #f39c12;
    }
    .bad {
      color: #e74c3c;
    }
    .files {
      margin-top: 40px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f8f9fa;
      font-weight: 600;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .progress-bar {
      height: 10px;
      background-color: #ecf0f1;
      border-radius: 5px;
      overflow: hidden;
      margin-top: 5px;
    }
    .progress {
      height: 100%;
      border-radius: 5px;
    }
    .file-link {
      color: #3498db;
      text-decoration: none;
    }
    .file-link:hover {
      text-decoration: underline;
    }
    .date {
      color: #7f8c8d;
      font-size: 14px;
    }
    .target-line {
      position: absolute;
      height: 100%;
      border-left: 2px dashed #e74c3c;
      z-index: 1;
    }
  </style>
</head>
<body>
  <header>
    <h1>AIBUBB 测试覆盖率报告</h1>
    <p class="date">生成时间: {{DATE}}</p>
  </header>

  <div class="summary">
    <div class="metric">
      <h2>语句覆盖率</h2>
      <div class="value {{STATEMENTS_CLASS}}">{{STATEMENTS_PCT}}%</div>
      <div class="progress-bar">
        <div class="progress" style="width: {{STATEMENTS_PCT}}%; background-color: {{STATEMENTS_COLOR}};"></div>
        <div class="target-line" style="left: {{STATEMENTS_TARGET}}%;"></div>
      </div>
      <div>目标: {{STATEMENTS_TARGET}}%</div>
    </div>
    <div class="metric">
      <h2>分支覆盖率</h2>
      <div class="value {{BRANCHES_CLASS}}">{{BRANCHES_PCT}}%</div>
      <div class="progress-bar">
        <div class="progress" style="width: {{BRANCHES_PCT}}%; background-color: {{BRANCHES_COLOR}};"></div>
        <div class="target-line" style="left: {{BRANCHES_TARGET}}%;"></div>
      </div>
      <div>目标: {{BRANCHES_TARGET}}%</div>
    </div>
    <div class="metric">
      <h2>函数覆盖率</h2>
      <div class="value {{FUNCTIONS_CLASS}}">{{FUNCTIONS_PCT}}%</div>
      <div class="progress-bar">
        <div class="progress" style="width: {{FUNCTIONS_PCT}}%; background-color: {{FUNCTIONS_COLOR}};"></div>
        <div class="target-line" style="left: {{FUNCTIONS_TARGET}}%;"></div>
      </div>
      <div>目标: {{FUNCTIONS_TARGET}}%</div>
    </div>
    <div class="metric">
      <h2>行覆盖率</h2>
      <div class="value {{LINES_CLASS}}">{{LINES_PCT}}%</div>
      <div class="progress-bar">
        <div class="progress" style="width: {{LINES_PCT}}%; background-color: {{LINES_COLOR}};"></div>
        <div class="target-line" style="left: {{LINES_TARGET}}%;"></div>
      </div>
      <div>目标: {{LINES_TARGET}}%</div>
    </div>
  </div>

  <div class="files">
    <h2>文件详情</h2>
    <table>
      <thead>
        <tr>
          <th>文件</th>
          <th>语句覆盖率</th>
          <th>分支覆盖率</th>
          <th>函数覆盖率</th>
          <th>行覆盖率</th>
        </tr>
      </thead>
      <tbody>
        {{FILE_ROWS}}
      </tbody>
    </table>
  </div>

  <script>
    // 可以添加一些交互功能，如排序、过滤等
  </script>
</body>
</html>
