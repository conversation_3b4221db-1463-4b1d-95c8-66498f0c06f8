#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}AIBUBB系统部署版本管理脚本${NC}"

# 进入项目目录
cd "$(dirname "$0")/.." || { echo -e "${RED}无法进入项目目录${NC}"; exit 1; }

# 创建部署历史目录
DEPLOYMENTS_DIR="./deployments"
mkdir -p "$DEPLOYMENTS_DIR"

# 获取版本信息
TIMESTAMP=$(date +%Y%m%d%H%M%S)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
VERSION="v-${TIMESTAMP}-${GIT_COMMIT}"

echo -e "${YELLOW}创建版本: $VERSION${NC}"

# 创建版本目录
VERSION_DIR="$DEPLOYMENTS_DIR/$VERSION"
mkdir -p "$VERSION_DIR"

# 复制当前文件到版本目录
echo -e "${YELLOW}复制文件到版本目录...${NC}"
rsync -av --exclude='node_modules' --exclude='logs' --exclude='data' --exclude='deployments' ./ "$VERSION_DIR/"

# 如果存在.env文件，也复制它
if [ -f .env ]; then
  echo -e "${YELLOW}复制环境变量文件...${NC}"
  cp .env "$VERSION_DIR/"
fi

# 更新当前版本符号链接
echo -e "${YELLOW}更新当前版本符号链接...${NC}"
ln -sfn "$VERSION_DIR" "$DEPLOYMENTS_DIR/current"

# 清理旧版本（保留最近5个版本）
echo -e "${YELLOW}清理旧版本...${NC}"
ls -t "$DEPLOYMENTS_DIR" | grep -v "current" | tail -n +6 | xargs -I {} rm -rf "$DEPLOYMENTS_DIR/{}"

echo -e "${GREEN}版本 $VERSION 创建成功!${NC}"
echo -e "${GREEN}可以使用 'scripts/rollback.sh $VERSION' 回滚到此版本${NC}"
