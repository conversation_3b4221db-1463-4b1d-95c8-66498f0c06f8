#!/bin/bash

# AIBUBB项目部署前自动化检查脚本
# 此脚本将自动检查项目的关键配置和状态

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置检查结果计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查结果记录函数
check_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ $test_name${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        if [ -n "$details" ]; then
            echo -e "  ${BLUE}$details${NC}"
        fi
    else
        echo -e "${RED}✗ $test_name${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        if [ -n "$details" ]; then
            echo -e "  ${YELLOW}$details${NC}"
        fi
    fi
}

echo -e "${BLUE}===========================================${NC}"
echo -e "${BLUE}    AIBUBB 项目部署前自动化检查${NC}"
echo -e "${BLUE}===========================================${NC}"
echo

# 1. 检查Git状态
echo -e "${YELLOW}1. 检查Git仓库状态...${NC}"
if git status --porcelain | grep -q .; then
    check_result "Git工作区状态" "FAIL" "存在未提交的更改"
else
    check_result "Git工作区状态" "PASS" "工作区干净"
fi

# 检查当前分支
current_branch=$(git branch --show-current)
check_result "当前Git分支" "PASS" "分支: $current_branch"

echo

# 2. 检查环境变量配置
echo -e "${YELLOW}2. 检查环境变量配置...${NC}"
if [ -f .env ]; then
    check_result ".env文件存在" "PASS"
    
    # 运行环境变量验证脚本
    if [ -f validate-env.sh ]; then
        if ./validate-env.sh > /dev/null 2>&1; then
            check_result "环境变量验证" "PASS" "所有必需变量已设置"
        else
            check_result "环境变量验证" "FAIL" "存在缺失或无效的环境变量"
        fi
    else
        check_result "环境变量验证脚本" "FAIL" "validate-env.sh不存在"
    fi
else
    check_result ".env文件存在" "FAIL" "请创建.env文件"
fi

echo

# 3. 检查Node.js依赖
echo -e "${YELLOW}3. 检查Node.js环境...${NC}"
if command -v node > /dev/null 2>&1; then
    node_version=$(node --version)
    check_result "Node.js安装" "PASS" "版本: $node_version"
else
    check_result "Node.js安装" "FAIL" "Node.js未安装"
fi

if command -v npm > /dev/null 2>&1; then
    npm_version=$(npm --version)
    check_result "npm安装" "PASS" "版本: $npm_version"
else
    check_result "npm安装" "FAIL" "npm未安装"
fi

# 检查后端依赖
if [ -f backend/package.json ]; then
    check_result "后端package.json存在" "PASS"
    
    if [ -d backend/node_modules ]; then
        check_result "后端依赖安装" "PASS"
    else
        check_result "后端依赖安装" "FAIL" "请运行: cd backend && npm install"
    fi
else
    check_result "后端package.json存在" "FAIL"
fi

echo

# 4. 检查Docker环境
echo -e "${YELLOW}4. 检查Docker环境...${NC}"
if command -v docker > /dev/null 2>&1; then
    docker_version=$(docker --version | cut -d ' ' -f 3 | cut -d ',' -f 1)
    check_result "Docker安装" "PASS" "版本: $docker_version"
    
    # 检查Docker服务状态
    if docker info > /dev/null 2>&1; then
        check_result "Docker服务运行" "PASS"
    else
        check_result "Docker服务运行" "FAIL" "Docker服务未运行"
    fi
else
    check_result "Docker安装" "FAIL" "Docker未安装"
fi

if command -v docker-compose > /dev/null 2>&1; then
    compose_version=$(docker-compose --version | cut -d ' ' -f 3 | cut -d ',' -f 1)
    check_result "Docker Compose安装" "PASS" "版本: $compose_version"
else
    check_result "Docker Compose安装" "FAIL" "Docker Compose未安装"
fi

echo

# 5. 检查Docker配置文件
echo -e "${YELLOW}5. 检查Docker配置文件...${NC}"
if [ -f docker-compose.yml ]; then
    check_result "docker-compose.yml存在" "PASS"
    
    # 验证docker-compose.yml语法
    if docker-compose config > /dev/null 2>&1; then
        check_result "docker-compose.yml语法" "PASS"
    else
        check_result "docker-compose.yml语法" "FAIL" "配置文件语法错误"
    fi
else
    check_result "docker-compose.yml存在" "FAIL"
fi

if [ -f backend/Dockerfile ]; then
    check_result "后端Dockerfile存在" "PASS"
else
    check_result "后端Dockerfile存在" "FAIL"
fi

echo

# 6. 检查关键脚本文件
echo -e "${YELLOW}6. 检查部署脚本...${NC}"
scripts_to_check=(
    "docker-start-optimized.sh"
    "docker-stop-optimized.sh"
    "docker-backup.sh"
    "docker-restore.sh"
    "validate-env.sh"
)

for script in "${scripts_to_check[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            check_result "$script" "PASS" "存在且可执行"
        else
            check_result "$script" "FAIL" "存在但不可执行，请运行: chmod +x $script"
        fi
    else
        check_result "$script" "FAIL" "文件不存在"
    fi
done

echo

# 7. 检查数据库相关文件
echo -e "${YELLOW}7. 检查数据库配置...${NC}"
if [ -d mysql-init ]; then
    check_result "MySQL初始化目录" "PASS"
else
    check_result "MySQL初始化目录" "FAIL" "mysql-init目录不存在"
fi

if [ -f backend/scripts/healthcheck.js ]; then
    check_result "健康检查脚本" "PASS"
else
    check_result "健康检查脚本" "FAIL" "healthcheck.js不存在"
fi

echo

# 8. 检查AI服务配置
echo -e "${YELLOW}8. 检查AI服务配置...${NC}"
if [ -f .env ]; then
    ai_provider=$(grep "^AI_PROVIDER=" .env | cut -d '=' -f 2-)
    if [ -n "$ai_provider" ]; then
        check_result "AI提供商配置" "PASS" "提供商: $ai_provider"
        
        # 检查对应的API密钥
        case "$ai_provider" in
            "bytedance")
                if grep -q "^ARK_API_KEY=" .env && [ -n "$(grep "^ARK_API_KEY=" .env | cut -d '=' -f 2-)" ]; then
                    check_result "字节大模型API密钥" "PASS"
                else
                    check_result "字节大模型API密钥" "FAIL" "ARK_API_KEY未设置"
                fi
                ;;
            "aliyun")
                if grep -q "^DASHSCOPE_API_KEY=" .env && [ -n "$(grep "^DASHSCOPE_API_KEY=" .env | cut -d '=' -f 2-)" ]; then
                    check_result "阿里云百炼API密钥" "PASS"
                else
                    check_result "阿里云百炼API密钥" "FAIL" "DASHSCOPE_API_KEY未设置"
                fi
                ;;
            "hunyuan")
                if grep -q "^HUNYUAN_API_KEY=" .env && [ -n "$(grep "^HUNYUAN_API_KEY=" .env | cut -d '=' -f 2-)" ]; then
                    check_result "腾讯混元API密钥" "PASS"
                else
                    check_result "腾讯混元API密钥" "FAIL" "HUNYUAN_API_KEY未设置"
                fi
                ;;
            *)
                check_result "AI提供商配置" "FAIL" "不支持的提供商: $ai_provider"
                ;;
        esac
    else
        check_result "AI提供商配置" "FAIL" "AI_PROVIDER未设置"
    fi
fi

echo

# 9. 检查安全配置
echo -e "${YELLOW}9. 检查安全配置...${NC}"
if [ -f .env ]; then
    # 检查JWT密钥
    jwt_secret=$(grep "^JWT_SECRET=" .env | cut -d '=' -f 2-)
    if [ -n "$jwt_secret" ] && [ ${#jwt_secret} -ge 32 ]; then
        check_result "JWT密钥强度" "PASS" "密钥长度: ${#jwt_secret}字符"
    else
        check_result "JWT密钥强度" "FAIL" "JWT密钥太短或未设置（建议至少32字符）"
    fi
    
    # 检查数据库密码
    db_password=$(grep "^DB_PASSWORD=" .env | cut -d '=' -f 2-)
    if [ -n "$db_password" ] && [ ${#db_password} -ge 8 ]; then
        check_result "数据库密码强度" "PASS"
    else
        check_result "数据库密码强度" "FAIL" "数据库密码太短或未设置（建议至少8字符）"
    fi
fi

echo

# 10. 检查文档完整性
echo -e "${YELLOW}10. 检查文档完整性...${NC}"
docs_to_check=(
    "README.md"
    "SERVER-DEPLOYMENT-GUIDE.md"
    "DEPLOYMENT-CHECKLIST.md"
)

for doc in "${docs_to_check[@]}"; do
    if [ -f "$doc" ]; then
        check_result "$doc" "PASS"
    else
        check_result "$doc" "FAIL" "文档不存在"
    fi
done

echo

# 输出检查结果汇总
echo -e "${BLUE}===========================================${NC}"
echo -e "${BLUE}           检查结果汇总${NC}"
echo -e "${BLUE}===========================================${NC}"
echo -e "${GREEN}通过检查: $PASSED_CHECKS${NC}"
echo -e "${RED}失败检查: $FAILED_CHECKS${NC}"
echo -e "${YELLOW}总计检查: $TOTAL_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 恭喜！所有检查都已通过，项目已准备好部署！${NC}"
    echo -e "${YELLOW}建议：在部署前请仔细阅读 DEPLOYMENT-CHECKLIST.md${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  发现 $FAILED_CHECKS 个问题需要解决${NC}"
    echo -e "${YELLOW}请修复上述问题后重新运行此脚本${NC}"
    echo -e "${YELLOW}详细的部署检查清单请参考 DEPLOYMENT-CHECKLIST.md${NC}"
    exit 1
fi