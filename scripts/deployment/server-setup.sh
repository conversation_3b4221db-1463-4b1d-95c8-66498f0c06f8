#!/bin/bash

# AIBUBB服务器配置脚本
# 此脚本用于自动化配置AIBUBB服务器环境
# 使用方法: ./server-setup.sh [--skip-docker] [--skip-nginx] [--skip-firewall]

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 解析命令行参数
SKIP_DOCKER=false
SKIP_NGINX=false
SKIP_FIREWALL=false

for arg in "$@"; do
  case $arg in
    --skip-docker)
      SKIP_DOCKER=true
      shift
      ;;
    --skip-nginx)
      SKIP_NGINX=true
      shift
      ;;
    --skip-firewall)
      SKIP_FIREWALL=true
      shift
      ;;
    *)
      # 未知参数
      echo -e "${RED}未知参数: $arg${NC}"
      echo "使用方法: ./server-setup.sh [--skip-docker] [--skip-nginx] [--skip-firewall]"
      exit 1
      ;;
  esac
done

# 显示欢迎信息
echo -e "${GREEN}=== AIBUBB服务器配置脚本 ===${NC}"
echo -e "${YELLOW}此脚本将自动配置AIBUBB服务器环境${NC}"
echo -e "${YELLOW}包括系统更新、Docker安装、Nginx配置和防火墙设置${NC}"
echo

# 检查是否以root用户运行
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}错误: 请以root用户运行此脚本${NC}"
  echo -e "${YELLOW}提示: 使用 sudo ./server-setup.sh${NC}"
  exit 1
fi

# 更新系统
echo -e "${GREEN}=== 更新系统 ===${NC}"
apt update
if [ $? -ne 0 ]; then
  echo -e "${RED}错误: 无法更新软件包列表${NC}"
  exit 1
fi

apt upgrade -y
if [ $? -ne 0 ]; then
  echo -e "${RED}错误: 无法升级软件包${NC}"
  exit 1
fi

# 安装基本工具
echo -e "${GREEN}=== 安装基本工具 ===${NC}"
apt install -y curl wget git vim htop net-tools
if [ $? -ne 0 ]; then
  echo -e "${RED}错误: 无法安装基本工具${NC}"
  exit 1
fi

# 安装Docker
if [ "$SKIP_DOCKER" = false ]; then
  echo -e "${GREEN}=== 安装Docker ===${NC}"
  
  # 检查Docker是否已安装
  if command -v docker &> /dev/null; then
    echo -e "${YELLOW}Docker已安装，跳过安装步骤${NC}"
  else
    # 安装依赖
    apt install -y apt-transport-https ca-certificates curl software-properties-common
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法添加Docker GPG密钥${NC}"
      exit 1
    fi
    
    # 添加Docker仓库
    add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法添加Docker仓库${NC}"
      exit 1
    fi
    
    # 更新软件包列表
    apt update
    
    # 安装Docker
    apt install -y docker-ce docker-ce-cli containerd.io
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法安装Docker${NC}"
      exit 1
    fi
    
    # 启动Docker服务
    systemctl start docker
    
    # 设置Docker开机自启
    systemctl enable docker
    
    echo -e "${GREEN}Docker安装成功!${NC}"
  fi
  
  # 安装Docker Compose
  if command -v docker-compose &> /dev/null; then
    echo -e "${YELLOW}Docker Compose已安装，跳过安装步骤${NC}"
  else
    echo -e "${GREEN}=== 安装Docker Compose ===${NC}"
    
    # 下载Docker Compose
    curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法下载Docker Compose${NC}"
      exit 1
    fi
    
    # 添加执行权限
    chmod +x /usr/local/bin/docker-compose
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法添加Docker Compose执行权限${NC}"
      exit 1
    fi
    
    echo -e "${GREEN}Docker Compose安装成功!${NC}"
  fi
  
  # 创建docker用户组
  if getent group docker > /dev/null; then
    echo -e "${YELLOW}docker用户组已存在，跳过创建步骤${NC}"
  else
    echo -e "${GREEN}=== 创建docker用户组 ===${NC}"
    
    groupadd docker
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法创建docker用户组${NC}"
      exit 1
    fi
    
    echo -e "${GREEN}docker用户组创建成功!${NC}"
  fi
  
  # 询问要添加到docker用户组的用户
  echo -e "${YELLOW}请输入要添加到docker用户组的用户名 (默认: $SUDO_USER): ${NC}"
  read -r username
  
  # 如果用户未输入，使用$SUDO_USER
  if [ -z "$username" ]; then
    username=$SUDO_USER
  fi
  
  # 检查用户是否存在
  if id "$username" &>/dev/null; then
    # 将用户添加到docker用户组
    usermod -aG docker "$username"
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法将用户 $username 添加到docker用户组${NC}"
      exit 1
    fi
    
    echo -e "${GREEN}用户 $username 已添加到docker用户组!${NC}"
    echo -e "${YELLOW}注意: 用户需要重新登录才能使用docker命令${NC}"
  else
    echo -e "${RED}错误: 用户 $username 不存在${NC}"
    exit 1
  fi
fi

# 安装Nginx
if [ "$SKIP_NGINX" = false ]; then
  echo -e "${GREEN}=== 安装Nginx ===${NC}"
  
  # 检查Nginx是否已安装
  if command -v nginx &> /dev/null; then
    echo -e "${YELLOW}Nginx已安装，跳过安装步骤${NC}"
  else
    # 安装Nginx
    apt install -y nginx
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法安装Nginx${NC}"
      exit 1
    fi
    
    # 启动Nginx服务
    systemctl start nginx
    
    # 设置Nginx开机自启
    systemctl enable nginx
    
    echo -e "${GREEN}Nginx安装成功!${NC}"
  fi
  
  # 安装Certbot
  echo -e "${GREEN}=== 安装Certbot ===${NC}"
  
  # 检查Certbot是否已安装
  if command -v certbot &> /dev/null; then
    echo -e "${YELLOW}Certbot已安装，跳过安装步骤${NC}"
  else
    # 安装Certbot
    apt install -y certbot python3-certbot-nginx
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法安装Certbot${NC}"
      exit 1
    fi
    
    echo -e "${GREEN}Certbot安装成功!${NC}"
  fi
  
  # 创建Nginx配置目录
  mkdir -p /etc/nginx/sites-available
  mkdir -p /etc/nginx/sites-enabled
  
  # 询问域名
  echo -e "${YELLOW}请输入API服务的域名 (例如: api.yourdomain.com): ${NC}"
  read -r domain
  
  if [ -n "$domain" ]; then
    # 创建Nginx配置文件
    cat > /etc/nginx/sites-available/aibubb << EOF
server {
    listen 80;
    server_name $domain;
    
    # 重定向HTTP到HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name $domain;
    
    # SSL配置将由Certbot自动添加
    
    # 反向代理配置
    location / {
        proxy_pass http://localhost:9090;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
    
    # 创建符号链接
    ln -sf /etc/nginx/sites-available/aibubb /etc/nginx/sites-enabled/
    
    # 测试Nginx配置
    nginx -t
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: Nginx配置测试失败${NC}"
      exit 1
    fi
    
    # 重新加载Nginx配置
    systemctl reload nginx
    
    echo -e "${GREEN}Nginx配置成功!${NC}"
    
    # 询问是否获取SSL证书
    echo -e "${YELLOW}是否现在获取SSL证书? [y/N]: ${NC}"
    read -r get_ssl
    
    if [[ "$get_ssl" =~ ^[Yy]$ ]]; then
      # 获取SSL证书
      certbot --nginx -d "$domain"
      if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 无法获取SSL证书${NC}"
        echo -e "${YELLOW}提示: 请确保域名 $domain 已正确解析到此服务器${NC}"
      else
        echo -e "${GREEN}SSL证书获取成功!${NC}"
      fi
    else
      echo -e "${YELLOW}跳过SSL证书获取，您可以稍后运行以下命令获取证书:${NC}"
      echo -e "${GREEN}sudo certbot --nginx -d $domain${NC}"
    fi
  else
    echo -e "${YELLOW}未提供域名，跳过Nginx配置${NC}"
  fi
fi

# 配置防火墙
if [ "$SKIP_FIREWALL" = false ]; then
  echo -e "${GREEN}=== 配置防火墙 ===${NC}"
  
  # 检查UFW是否已安装
  if ! command -v ufw &> /dev/null; then
    # 安装UFW
    apt install -y ufw
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法安装UFW${NC}"
      exit 1
    fi
  fi
  
  # 允许SSH连接
  ufw allow ssh
  
  # 允许HTTP和HTTPS连接
  ufw allow 80/tcp
  ufw allow 443/tcp
  
  # 允许API服务端口
  ufw allow 9090/tcp
  
  # 询问是否启用防火墙
  echo -e "${YELLOW}是否启用防火墙? [y/N]: ${NC}"
  read -r enable_ufw
  
  if [[ "$enable_ufw" =~ ^[Yy]$ ]]; then
    # 启用防火墙
    ufw --force enable
    if [ $? -ne 0 ]; then
      echo -e "${RED}错误: 无法启用防火墙${NC}"
      exit 1
    fi
    
    echo -e "${GREEN}防火墙已启用!${NC}"
  else
    echo -e "${YELLOW}跳过防火墙启用，您可以稍后运行以下命令启用防火墙:${NC}"
    echo -e "${GREEN}sudo ufw enable${NC}"
  fi
  
  # 检查防火墙状态
  ufw status
fi

# 创建应用目录
echo -e "${GREEN}=== 创建应用目录 ===${NC}"
mkdir -p /opt/aibubb
if [ $? -ne 0 ]; then
  echo -e "${RED}错误: 无法创建应用目录${NC}"
  exit 1
fi

# 设置目录权限
chown -R "$username:$username" /opt/aibubb
if [ $? -ne 0 ]; then
  echo -e "${RED}错误: 无法设置目录权限${NC}"
  exit 1
fi

echo -e "${GREEN}应用目录创建成功!${NC}"

# 完成
echo -e "${GREEN}=== 服务器配置完成! ===${NC}"
echo -e "${YELLOW}请按照以下步骤完成AIBUBB部署:${NC}"
echo -e "1. 切换到应用目录: ${GREEN}cd /opt/aibubb${NC}"
echo -e "2. 克隆代码仓库: ${GREEN}git clone https://github.com/mccic/AIBUBB.git .${NC}"
echo -e "3. 配置环境变量: ${GREEN}cp .env.production.example .env && vim .env${NC}"
echo -e "4. 启动服务: ${GREEN}./docker-start-optimized.sh${NC}"
echo -e "${YELLOW}详细说明请参考 SERVER-DEPLOYMENT-GUIDE.md 文档${NC}"
