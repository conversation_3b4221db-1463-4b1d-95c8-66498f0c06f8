#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}    AIBUBB V1 API废弃计划执行脚本    ${NC}"
echo -e "${BLUE}=========================================${NC}"

# 当前目录
CURRENT_DIR=$(pwd)

# 项目根目录
ROOT_DIR=$(git rev-parse --show-toplevel 2>/dev/null)
if [ -z "$ROOT_DIR" ]; then
  ROOT_DIR=$CURRENT_DIR
fi

# 后端目录
BACKEND_DIR="$ROOT_DIR/backend"

# 前端目录
FRONTEND_DIR="$ROOT_DIR/frontend"

# 文档目录
DOCS_DIR="$ROOT_DIR/docs"

# 检查目录是否存在
if [ ! -d "$BACKEND_DIR" ]; then
  echo -e "${RED}后端目录不存在: $BACKEND_DIR${NC}"
  exit 1
fi

if [ ! -d "$FRONTEND_DIR" ]; then
  echo -e "${RED}前端目录不存在: $FRONTEND_DIR${NC}"
  exit 1
fi

if [ ! -d "$DOCS_DIR" ]; then
  mkdir -p "$DOCS_DIR"
fi

# 第一阶段：准备工作
echo -e "${YELLOW}第一阶段：准备工作${NC}"

# 检查V1废弃警告中间件是否存在
if [ ! -f "$BACKEND_DIR/middlewares/v1-deprecation.middleware.js" ]; then
  echo -e "${RED}V1废弃警告中间件不存在${NC}"
  exit 1
fi

# 检查V1 API废弃文档是否存在
if [ ! -f "$BACKEND_DIR/docs/V1-API-DEPRECATION.md" ]; then
  echo -e "${RED}V1 API废弃文档不存在${NC}"
  exit 1
fi

# 检查废弃计划脚本是否存在
if [ ! -f "$BACKEND_DIR/scripts/remove-v1-api.js" ]; then
  echo -e "${RED}废弃计划脚本不存在${NC}"
  exit 1
fi

echo -e "${GREEN}准备工作完成${NC}"

# 第二阶段：更新前端代码
echo -e "${YELLOW}第二阶段：更新前端代码${NC}"

# 检查前端迁移指南是否存在
if [ ! -f "$FRONTEND_DIR/docs/V1-TO-V2-MIGRATION-GUIDE.md" ]; then
  echo -e "${RED}前端迁移指南不存在${NC}"
  exit 1
fi

# 检查前端API客户端是否存在
if [ ! -f "$FRONTEND_DIR/src/api/v2-client.js" ]; then
  echo -e "${RED}前端API客户端不存在${NC}"
  exit 1
fi

# 检查字段命名转换工具是否存在
if [ ! -f "$FRONTEND_DIR/src/utils/case-converter.js" ]; then
  echo -e "${RED}字段命名转换工具不存在${NC}"
  exit 1
fi

echo -e "${GREEN}前端代码更新完成${NC}"

# 第三阶段：执行废弃计划
echo -e "${YELLOW}第三阶段：执行废弃计划${NC}"

# 检查废弃通知邮件模板是否存在
if [ ! -f "$DOCS_DIR/V1-API-DEPRECATION-EMAIL.md" ]; then
  echo -e "${RED}废弃通知邮件模板不存在${NC}"
  exit 1
fi

# 应用V1废弃警告中间件
echo -e "${BLUE}应用V1废弃警告中间件...${NC}"
cd "$BACKEND_DIR"
git add middlewares/v1-deprecation.middleware.js
git add utils/register-version-routes.js

# 提交更改
echo -e "${BLUE}提交更改...${NC}"
git commit -m "chore: 添加V1 API废弃警告中间件"

# 设置定时任务，在7天后执行废弃计划脚本
echo -e "${BLUE}设置定时任务，在7天后执行废弃计划脚本...${NC}"
DEPRECATION_DATE=$(date -d "+7 days" +"%Y-%m-%d")
DEPRECATION_TIME="00:00:00"

# 创建定时任务
(crontab -l 2>/dev/null; echo "0 0 $DEPRECATION_DATE * * cd $BACKEND_DIR && node scripts/remove-v1-api.js >> logs/v1-api-deprecation.log 2>&1") | crontab -

echo -e "${GREEN}废弃计划执行完成${NC}"

# 发送废弃通知邮件
echo -e "${YELLOW}发送废弃通知邮件${NC}"
echo -e "${BLUE}请将以下文件中的内容发送给所有开发者：${NC}"
echo -e "${BLUE}$DOCS_DIR/V1-API-DEPRECATION-EMAIL.md${NC}"

echo -e "${BLUE}=========================================${NC}"
echo -e "${GREEN}V1 API废弃计划已成功启动！${NC}"
echo -e "${YELLOW}V1 API将在 $DEPRECATION_DATE $DEPRECATION_TIME 完全废弃。${NC}"
echo -e "${BLUE}=========================================${NC}"
