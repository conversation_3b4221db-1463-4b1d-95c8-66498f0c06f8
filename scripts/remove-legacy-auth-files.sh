#!/bin/bash

# 移除用户和认证相关的老架构遗留文件和代码
# 此脚本将删除已经被增强版实现替代的文件

echo "开始移除用户和认证相关的老架构遗留文件和代码..."

# 移除旧版控制器
echo "移除旧版控制器..."
rm -f backend/controllers/auth.controller.js
rm -f backend/controllers/auth.controller.compatibility.js
rm -f backend/controllers/authV2.controller.js
rm -f backend/controllers/user.controller.js
rm -f backend/controllers/user.controller.compatibility.js
rm -f backend/controllers/userV2.controller.js

# 移除旧版路由
echo "移除旧版路由..."
rm -f backend/routes/auth.routes.js
rm -f backend/routes/authV2.routes.js
rm -f backend/routes/user.routes.js
rm -f backend/routes/userV2.routes.js

# 移除旧版服务
echo "移除旧版服务..."
rm -f backend/services/auth.service.js
rm -f backend/services/user.service.js

# 移除旧版仓库
echo "移除旧版仓库..."
rm -f backend/infrastructure/persistence/repositories/user/SequelizeUserRepository.js
rm -f backend/infrastructure/persistence/repositories/user/SequelizeUserSettingRepository.js

# 移除旧版工具
echo "移除旧版工具..."
rm -f backend/utils/jwt.js
rm -f backend/utils/password.js

echo "移除完成！"
