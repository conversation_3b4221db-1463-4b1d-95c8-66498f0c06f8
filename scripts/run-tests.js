/**
 * 运行测试脚本
 * 用于运行单元测试和生成覆盖率报告
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 测试目录
const TEST_DIR = path.join(__dirname, '../tests');

// 测试类型
const TEST_TYPES = {
  UNIT: 'unit',
  ALL: 'all'
};

// 获取命令行参数
const args = process.argv.slice(2);
const testType = args[0] || TEST_TYPES.ALL;
const testPattern = args[1] || '';
const coverage = args.includes('--coverage');

// 颜色
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  RED: '\x1b[31m',
  BLUE: '\x1b[34m'
};

// 打印带颜色的消息
function printColored(message, color) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

// 创建测试结果目录
const RESULTS_DIR = path.join(__dirname, '../test-results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

// 构建测试命令
let testCommand = 'jest';

// 添加测试类型
if (testType === TEST_TYPES.UNIT) {
  testCommand += ' --testPathPattern=unit';
}

// 添加测试模式
if (testPattern) {
  testCommand += ` --testPathPattern=${testPattern}`;
}

// 添加覆盖率
if (coverage) {
  testCommand += ' --coverage';
}

// 添加输出格式
testCommand += ' --json --outputFile=test-results/test-results.json';

// 运行测试
try {
  printColored(`运行测试: ${testCommand}`, COLORS.BLUE);
  execSync(testCommand, { stdio: 'inherit' });
  
  // 如果需要覆盖率报告，生成HTML报告
  if (coverage) {
    printColored('生成HTML覆盖率报告...', COLORS.BLUE);
    execSync('jest --coverage --coverageReporters=html', { stdio: 'inherit' });
    printColored('HTML覆盖率报告已生成在 coverage/lcov-report 目录中', COLORS.GREEN);
  }
  
  printColored('测试完成!', COLORS.GREEN);
} catch (error) {
  printColored(`测试失败: ${error.message}`, COLORS.RED);
  process.exit(1);
}

// 解析测试结果
try {
  const testResults = JSON.parse(fs.readFileSync(path.join(RESULTS_DIR, 'test-results.json'), 'utf8'));
  
  // 打印测试摘要
  printColored('\n测试摘要:', COLORS.BLUE);
  printColored(`总测试数: ${testResults.numTotalTests}`, COLORS.YELLOW);
  printColored(`通过: ${testResults.numPassedTests}`, COLORS.GREEN);
  printColored(`失败: ${testResults.numFailedTests}`, COLORS.RED);
  printColored(`跳过: ${testResults.numPendingTests}`, COLORS.YELLOW);
  
  // 打印覆盖率摘要
  if (coverage && testResults.coverageMap) {
    printColored('\n覆盖率摘要:', COLORS.BLUE);
    const coverageSummary = testResults.coverageMap.getCoverageSummary();
    printColored(`语句覆盖率: ${coverageSummary.statements.pct}%`, COLORS.YELLOW);
    printColored(`分支覆盖率: ${coverageSummary.branches.pct}%`, COLORS.YELLOW);
    printColored(`函数覆盖率: ${coverageSummary.functions.pct}%`, COLORS.YELLOW);
    printColored(`行覆盖率: ${coverageSummary.lines.pct}%`, COLORS.YELLOW);
  }
} catch (error) {
  printColored(`解析测试结果失败: ${error.message}`, COLORS.RED);
}
