/**
 * API变更检测定时任务脚本
 *
 * 该脚本用于定时执行API变更检测和通知，包括：
 * 1. 每天凌晨2点执行API变更检测
 * 2. 如果检测到变更，发送通知
 * 3. 每周一凌晨3点生成周报
 *
 * 使用方法：
 * node scripts/schedule-api-change-detection.js [options]
 *
 * 选项：
 * --daily-time <time>: 每日检测时间，格式为HH:MM，默认为02:00
 * --weekly-time <time>: 每周检测时间，格式为HH:MM，默认为03:00
 * --weekly-day <day>: 每周检测日期，0-6表示周日到周六，默认为1（周一）
 * --no-daily: 禁用每日检测
 * --no-weekly: 禁用每周检测
 *
 * 示例：
 * node scripts/schedule-api-change-detection.js --daily-time 01:30 --weekly-time 04:00 --weekly-day 5
 */

const { program } = require('commander');
const cron = require('node-cron');
const { exec } = require('child_process');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 定义命令行选项
program
  .option('--daily-time <time>', '每日检测时间，格式为HH:MM', '02:00')
  .option('--weekly-time <time>', '每周检测时间，格式为HH:MM', '03:00')
  .option('--weekly-day <day>', '每周检测日期，0-6表示周日到周六', '1')
  .option('--no-daily', '禁用每日检测')
  .option('--no-weekly', '禁用每周检测')
  .parse(process.argv);

const options = program.opts();

// 解析时间
function parseTime(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return { hours, minutes };
}

// 每日检测时间
const dailyTime = parseTime(options.dailyTime);
// 每周检测时间
const weeklyTime = parseTime(options.weeklyTime);
// 每周检测日期
const weeklyDay = parseInt(options.weeklyDay, 10);

// 日志目录
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 日志文件
const logFile = path.join(logDir, 'api-change-detection.log');

/**
 * 写入日志
 * @param {string} message 日志消息
 */
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  // 输出到控制台
  console.log(logMessage);

  // 写入日志文件
  fs.appendFileSync(logFile, logMessage);
}

/**
 * 执行命令
 * @param {string} command 命令
 * @returns {Promise<string>} 命令输出
 */
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    log(`执行命令: ${command}`);

    exec(command, (error, stdout, stderr) => {
      if (error) {
        log(`命令执行错误: ${error.message}`);
        log(`stderr: ${stderr}`);
        reject(error);
        return;
      }

      log(`命令执行成功: ${stdout}`);
      resolve(stdout);
    });
  });
}

/**
 * 执行API变更检测
 * @param {Object} options 选项
 * @returns {Promise<boolean>} 是否检测到变更
 */
async function detectApiChanges(options = {}) {
  try {
    const command = `node ${path.join(__dirname, 'detect-api-changes.js')}`;
    const output = await executeCommand(command);

    // 检查是否检测到变更
    return output.includes('API变更');
  } catch (error) {
    log(`执行API变更检测时出错: ${error.message}`);
    return false;
  }
}

/**
 * 发送API变更通知
 * @param {Object} options 选项
 * @returns {Promise<void>}
 */
async function sendApiNotifications(options = {}) {
  try {
    const command = `node ${path.join(__dirname, 'send-api-notifications.js')}`;
    await executeCommand(command);
  } catch (error) {
    log(`发送API变更通知时出错: ${error.message}`);
  }
}

/**
 * 生成API变更周报
 * @param {Object} options 选项
 * @returns {Promise<void>}
 */
async function generateWeeklyReport(options = {}) {
  try {
    // 获取过去一周的变更报告
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);

    // 这里可以实现更复杂的逻辑，如合并多个变更报告
    // 简单起见，我们只是生成一个新的报告
    const command = `node ${path.join(__dirname, 'detect-api-changes.js')} --output docs/api-changes-weekly.json`;
    await executeCommand(command);

    // 发送周报通知
    const notifyCommand = `node ${path.join(__dirname, 'send-api-notifications.js')} --input docs/api-changes-weekly.json`;
    await executeCommand(notifyCommand);
  } catch (error) {
    log(`生成API变更周报时出错: ${error.message}`);
  }
}

// 启动定时任务
log('启动API变更检测定时任务');

// 每日检测
if (options.daily) {
  const cronExpression = `${dailyTime.minutes} ${dailyTime.hours} * * *`;
  log(`配置每日检测定时任务: ${cronExpression}`);

  cron.schedule(cronExpression, async () => {
    log('执行每日API变更检测');

    const hasChanges = await detectApiChanges();

    if (hasChanges) {
      log('检测到API变更，发送通知');
      await sendApiNotifications();
    } else {
      log('未检测到API变更，不发送通知');
    }
  });
}

// 每周检测
if (options.weekly) {
  const cronExpression = `${weeklyTime.minutes} ${weeklyTime.hours} * * ${weeklyDay}`;
  log(`配置每周检测定时任务: ${cronExpression}`);

  cron.schedule(cronExpression, async () => {
    log('执行每周API变更检测和周报生成');

    await generateWeeklyReport();
  });
}

// 保持进程运行
log('API变更检测定时任务已启动，按Ctrl+C停止');

// 处理进程退出
process.on('SIGINT', () => {
  log('接收到SIGINT信号，停止API变更检测定时任务');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('接收到SIGTERM信号，停止API变更检测定时任务');
  process.exit(0);
});

// 处理未捕获的异常
process.on('uncaughtException', error => {
  log(`未捕获的异常: ${error.message}`);
  log(error.stack);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  log(`未处理的Promise拒绝: ${reason}`);
});
