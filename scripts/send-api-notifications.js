/**
 * API变更通知脚本
 *
 * 该脚本用于发送API变更通知，包括：
 * 1. 邮件通知
 * 2. Slack通知
 * 3. 系统内通知
 *
 * 使用方法：
 * node scripts/send-api-notifications.js [options]
 *
 * 选项：
 * --input <file>: 指定变更报告文件路径
 * --email-only: 只发送邮件通知
 * --slack-only: 只发送Slack通知
 * --system-only: 只发送系统内通知
 * --urgent-only: 只发送紧急级别的通知
 * --dry-run: 不实际发送通知，只打印通知内容
 *
 * 示例：
 * node scripts/send-api-notifications.js --input changes.json --email-only
 */

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const nodemailer = require('nodemailer');
const { WebClient } = require('@slack/web-api');
const chalk = require('chalk');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 定义命令行选项
program
  .option('--input <file>', '指定变更报告文件路径', 'docs/api-changes.json')
  .option('--email-only', '只发送邮件通知')
  .option('--slack-only', '只发送Slack通知')
  .option('--system-only', '只发送系统内通知')
  .option('--urgent-only', '只发送紧急级别的通知')
  .option('--dry-run', '不实际发送通知，只打印通知内容')
  .parse(process.argv);

const options = program.opts();

// 邮件通知服务
class EmailNotificationService {
  constructor(config) {
    this.transporter = nodemailer.createTransport(config.smtp);
    this.from = config.from;
    this.recipients = config.recipients;
    this.dryRun = config.dryRun;
  }

  async sendNotification(notification) {
    const { subject, content, level } = notification;

    // 根据级别设置邮件优先级
    const priority = level === 'urgent' ? 'high' : 'normal';

    const mailOptions = {
      from: this.from,
      to: this.recipients.join(','),
      subject: `[API变更-${level.toUpperCase()}] ${subject}`,
      html: content,
      priority
    };

    if (this.dryRun) {
      console.log(chalk.blue('邮件通知内容:'));
      console.log(chalk.blue('发件人:'), this.from);
      console.log(chalk.blue('收件人:'), this.recipients.join(','));
      console.log(chalk.blue('主题:'), mailOptions.subject);
      console.log(chalk.blue('内容:'), mailOptions.html);
      return;
    }

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log(chalk.green(`邮件通知已发送: ${info.messageId}`));
    } catch (error) {
      console.error(chalk.red('发送邮件通知时出错:'), error);
      throw error;
    }
  }
}

// Slack通知服务
class SlackNotificationService {
  constructor(config) {
    this.client = new WebClient(config.token);
    this.channel = config.channel;
    this.dryRun = config.dryRun;
  }

  async sendNotification(notification) {
    const { subject, content, level } = notification;

    // 根据级别设置消息颜色
    const color = level === 'urgent' ? '#FF0000' : level === 'warning' ? '#FFA500' : '#00FF00';

    const message = {
      channel: this.channel,
      text: `[API变更-${level.toUpperCase()}] ${subject}`,
      attachments: [
        {
          color,
          text: content,
          footer: 'API变更通知系统'
        }
      ]
    };

    if (this.dryRun) {
      console.log(chalk.blue('Slack通知内容:'));
      console.log(chalk.blue('频道:'), this.channel);
      console.log(chalk.blue('文本:'), message.text);
      console.log(chalk.blue('附件:'), JSON.stringify(message.attachments, null, 2));
      return;
    }

    try {
      const result = await this.client.chat.postMessage(message);
      console.log(chalk.green(`Slack通知已发送: ${result.ts}`));
    } catch (error) {
      console.error(chalk.red('发送Slack通知时出错:'), error);
      throw error;
    }
  }
}

// 系统内通知服务
class SystemNotificationService {
  constructor(config) {
    this.apiUrl = config.apiUrl;
    this.apiKey = config.apiKey;
    this.dryRun = config.dryRun;
  }

  async sendNotification(notification) {
    const { subject, content, level, recipients } = notification;

    const notificationData = {
      title: `[API变更-${level.toUpperCase()}] ${subject}`,
      content,
      level,
      recipients,
      type: 'api-change',
      createdAt: new Date().toISOString()
    };

    if (this.dryRun) {
      console.log(chalk.blue('系统内通知内容:'));
      console.log(chalk.blue('数据:'), JSON.stringify(notificationData, null, 2));
      return;
    }

    try {
      // 这里使用简单的console.log模拟系统内通知
      // 实际项目中应该调用API或直接操作数据库
      console.log(chalk.green('系统内通知已发送:'), JSON.stringify(notificationData, null, 2));
    } catch (error) {
      console.error(chalk.red('发送系统内通知时出错:'), error);
      throw error;
    }
  }
}

// 通知管理器
class NotificationManager {
  constructor(config) {
    this.services = {};
    this.config = config;
  }

  registerService(name, service) {
    this.services[name] = service;
  }

  async sendNotification(notification) {
    const { level } = notification;

    // 如果只发送紧急级别的通知，则过滤掉非紧急通知
    if (this.config.urgentOnly && level !== 'urgent') {
      return;
    }

    // 根据级别和配置决定使用哪些通知服务
    let serviceNames = this.config.levelServices[level] || [];

    // 根据命令行选项过滤服务
    if (this.config.emailOnly) {
      serviceNames = serviceNames.filter(name => name === 'email');
    } else if (this.config.slackOnly) {
      serviceNames = serviceNames.filter(name => name === 'slack');
    } else if (this.config.systemOnly) {
      serviceNames = serviceNames.filter(name => name === 'system');
    }

    // 发送通知
    for (const name of serviceNames) {
      if (this.services[name]) {
        try {
          await this.services[name].sendNotification(notification);
        } catch (error) {
          console.error(chalk.red(`使用 ${name} 服务发送通知时出错:`), error);
        }
      }
    }
  }
}

/**
 * 生成通知内容
 * @param {Object} report 变更报告
 * @returns {Object} 通知内容
 */
function generateNotificationContent(report) {
  // 生成通知主题
  const subject = `API变更通知: ${report.summary.urgent} 紧急, ${report.summary.warning} 警告, ${report.summary.info} 信息`;

  // 生成HTML格式的通知内容
  let content = `<h1>API变更通知</h1>
<p>生成时间: ${new Date(report.generatedAt).toLocaleString()}</p>

<h2>变更摘要</h2>
<ul>
  <li>总变更数: ${report.summary.total}</li>
  <li>紧急变更: <span style="color: #d9534f;">${report.summary.urgent}</span></li>
  <li>警告变更: <span style="color: #f0ad4e;">${report.summary.warning}</span></li>
  <li>信息变更: <span style="color: #5bc0de;">${report.summary.info}</span></li>
</ul>`;

  // 添加紧急变更详情
  if (report.summary.urgent > 0) {
    content += '<h2 style="color: #d9534f;">紧急变更</h2>';
    for (const change of report.changes.urgent) {
      content += `<div style="margin: 10px 0; padding: 10px; border: 1px solid #d9534f; border-radius: 4px;">
        <h3>${change.path}</h3>
        <p>类型: ${change.type}</p>
        <p>消息: ${change.message}</p>`;
      if (change.oldValue && change.newValue) {
        content += `<p>旧值: <code>${JSON.stringify(change.oldValue)}</code></p>
        <p>新值: <code>${JSON.stringify(change.newValue)}</code></p>`;
      }
      content += '</div>';
    }
  }

  // 添加警告变更详情
  if (report.summary.warning > 0) {
    content += '<h2 style="color: #f0ad4e;">警告变更</h2>';
    for (const change of report.changes.warning) {
      content += `<div style="margin: 10px 0; padding: 10px; border: 1px solid #f0ad4e; border-radius: 4px;">
        <h3>${change.path}</h3>
        <p>类型: ${change.type}</p>
        <p>消息: ${change.message}</p>`;
      if (change.oldValue && change.newValue) {
        content += `<p>旧值: <code>${JSON.stringify(change.oldValue)}</code></p>
        <p>新值: <code>${JSON.stringify(change.newValue)}</code></p>`;
      }
      content += '</div>';
    }
  }

  // 添加信息变更详情
  if (report.summary.info > 0) {
    content += '<h2 style="color: #5bc0de;">信息变更</h2>';
    for (const change of report.changes.info) {
      content += `<div style="margin: 10px 0; padding: 10px; border: 1px solid #5bc0de; border-radius: 4px;">
        <h3>${change.path}</h3>
        <p>类型: ${change.type}</p>
        <p>消息: ${change.message}</p>`;
      if (change.oldValue && change.newValue) {
        content += `<p>旧值: <code>${JSON.stringify(change.oldValue)}</code></p>
        <p>新值: <code>${JSON.stringify(change.newValue)}</code></p>`;
      }
      content += '</div>';
    }
  }

  return { subject, content };
}

/**
 * 主函数
 */
async function main() {
  console.log(chalk.green('开始发送API变更通知...'));

  try {
    // 读取变更报告
    const reportPath = options.input;
    if (!fs.existsSync(reportPath)) {
      console.error(chalk.red(`变更报告文件不存在: ${reportPath}`));
      process.exit(1);
    }

    const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));

    // 如果没有变更，则不发送通知
    if (report.summary.total === 0) {
      console.log(chalk.yellow('没有API变更，不发送通知'));
      return;
    }

    // 创建通知服务
    const emailService = new EmailNotificationService({
      smtp: {
        host: process.env.SMTP_HOST || 'smtp.example.com',
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASS || 'password'
        }
      },
      from: process.env.EMAIL_FROM || '<EMAIL>',
      recipients: (process.env.EMAIL_RECIPIENTS || '<EMAIL>').split(','),
      dryRun: options.dryRun
    });

    const slackService = new SlackNotificationService({
      token: process.env.SLACK_TOKEN || 'xoxb-your-token',
      channel: process.env.SLACK_CHANNEL || '#api-changes',
      dryRun: options.dryRun
    });

    const systemService = new SystemNotificationService({
      apiUrl: process.env.SYSTEM_API_URL || 'http://localhost:3000/api/notifications',
      apiKey: process.env.SYSTEM_API_KEY || 'your-api-key',
      dryRun: options.dryRun
    });

    // 创建通知管理器
    const notificationManager = new NotificationManager({
      levelServices: {
        urgent: ['email', 'slack', 'system'],
        warning: ['email', 'slack'],
        info: ['system']
      },
      emailOnly: options.emailOnly,
      slackOnly: options.slackOnly,
      systemOnly: options.systemOnly,
      urgentOnly: options.urgentOnly
    });

    // 注册通知服务
    notificationManager.registerService('email', emailService);
    notificationManager.registerService('slack', slackService);
    notificationManager.registerService('system', systemService);

    // 生成通知内容
    const { subject, content } = generateNotificationContent(report);

    // 发送通知
    if (report.summary.urgent > 0) {
      await notificationManager.sendNotification({
        subject,
        content,
        level: 'urgent',
        recipients: ['all']
      });
    }

    if (report.summary.warning > 0 && !options.urgentOnly) {
      await notificationManager.sendNotification({
        subject,
        content,
        level: 'warning',
        recipients: ['all']
      });
    }

    if (report.summary.info > 0 && !options.urgentOnly) {
      await notificationManager.sendNotification({
        subject,
        content,
        level: 'info',
        recipients: ['all']
      });
    }

    console.log(chalk.green('API变更通知发送完成'));
  } catch (error) {
    console.error(chalk.red('发送API变更通知时出错:'), error);
    process.exit(1);
  }
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red('执行过程中出错:'), error);
  process.exit(1);
});
