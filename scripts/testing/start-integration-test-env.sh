#!/bin/bash

# 集成测试环境启动脚本
# 用于启动集成测试环境，包括数据库、Redis和API服务器

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_green() {
  echo -e "${GREEN}$1${NC}"
}

print_yellow() {
  echo -e "${YELLOW}$1${NC}"
}

print_red() {
  echo -e "${RED}$1${NC}"
}

print_blue() {
  echo -e "${BLUE}$1${NC}"
}

# 检查Docker是否安装
check_docker() {
  print_blue "检查Docker是否安装..."
  if ! command -v docker &> /dev/null; then
    print_red "✗ Docker未安装，请先安装Docker"
    exit 1
  fi
  print_green "✓ Docker已安装"
}

# 检查Docker Compose是否安装
check_docker_compose() {
  print_blue "检查Docker Compose是否安装..."
  if ! command -v docker-compose &> /dev/null; then
    print_yellow "⚠ Docker Compose未安装，尝试使用Docker Compose插件..."
    if ! docker compose version &> /dev/null; then
      print_red "✗ Docker Compose未安装，请先安装Docker Compose"
      exit 1
    fi
    DOCKER_COMPOSE="docker compose"
  else
    DOCKER_COMPOSE="docker-compose"
  fi
  print_green "✓ Docker Compose已安装"
}

# 启动Docker容器
start_containers() {
  print_blue "启动Docker容器..."
  $DOCKER_COMPOSE -f docker-compose.test.yml up -d
  if [ $? -ne 0 ]; then
    print_red "✗ 启动Docker容器失败"
    exit 1
  fi
  print_green "✓ Docker容器已启动"
}

# 等待容器健康检查通过
wait_for_containers() {
  print_blue "等待容器健康检查通过..."
  
  # 等待MySQL容器
  print_yellow "等待MySQL容器就绪..."
  while ! docker exec aibubb_test_mysql mysqladmin ping -h localhost -u root -proot --silent &> /dev/null; do
    echo -n "."
    sleep 1
  done
  print_green "✓ MySQL容器已就绪"
  
  # 等待Redis容器
  print_yellow "等待Redis容器就绪..."
  while ! docker exec aibubb_test_redis redis-cli ping &> /dev/null; do
    echo -n "."
    sleep 1
  done
  print_green "✓ Redis容器已就绪"
}

# 更新环境变量
update_env_vars() {
  print_blue "更新环境变量..."
  
  # 检查集成测试环境配置文件是否存在
  if [ ! -f "backend/config/integration-test.env" ]; then
    print_yellow "⚠ 集成测试环境配置文件不存在，将使用默认配置"
  else
    print_green "✓ 集成测试环境配置文件已存在"
  fi
}

# 运行数据库迁移
run_migrations() {
  print_blue "运行数据库迁移..."
  cd backend && node scripts/migrate.js
  if [ $? -ne 0 ]; then
    print_red "✗ 数据库迁移失败"
    exit 1
  fi
  print_green "✓ 数据库迁移成功"
}

# 运行数据库种子
run_seeds() {
  print_blue "运行数据库种子..."
  cd backend && node scripts/seed.js
  if [ $? -ne 0 ]; then
    print_red "✗ 数据库种子失败"
    exit 1
  fi
  print_green "✓ 数据库种子成功"
}

# 启动集成测试环境
start_integration_env() {
  print_blue "启动集成测试环境..."
  cd backend && node scripts/start-integration-env.js "$@"
}

# 主函数
main() {
  print_blue "=== 集成测试环境启动脚本 ==="
  
  # 解析命令行参数
  RESET_DB=false
  INTEGRATION_ENV_ARGS=""
  
  while [[ $# -gt 0 ]]; do
    case $1 in
      --reset-db)
        RESET_DB=true
        INTEGRATION_ENV_ARGS="$INTEGRATION_ENV_ARGS --reset-db"
        shift
        ;;
      *)
        INTEGRATION_ENV_ARGS="$INTEGRATION_ENV_ARGS $1"
        shift
        ;;
    esac
  done
  
  # 检查Docker和Docker Compose
  check_docker
  check_docker_compose
  
  # 启动Docker容器
  start_containers
  
  # 等待容器健康检查通过
  wait_for_containers
  
  # 更新环境变量
  update_env_vars
  
  # 如果需要重置数据库
  if [ "$RESET_DB" = true ]; then
    # 运行数据库迁移
    run_migrations
    
    # 运行数据库种子
    run_seeds
  fi
  
  # 启动集成测试环境
  start_integration_env $INTEGRATION_ENV_ARGS
}

# 执行主函数
main "$@"
