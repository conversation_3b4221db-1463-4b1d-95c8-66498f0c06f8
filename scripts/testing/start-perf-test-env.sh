#!/bin/bash

# 性能测试环境启动脚本
# 用于启动性能测试环境，包括数据库、Redis和监控服务

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_green() {
  echo -e "${GREEN}$1${NC}"
}

print_yellow() {
  echo -e "${YELLOW}$1${NC}"
}

print_red() {
  echo -e "${RED}$1${NC}"
}

print_blue() {
  echo -e "${BLUE}$1${NC}"
}

# 检查Docker是否安装
check_docker() {
  print_blue "检查Docker是否安装..."
  if ! command -v docker &> /dev/null; then
    print_red "✗ Docker未安装，请先安装Docker"
    exit 1
  fi
  print_green "✓ Docker已安装"
}

# 检查Docker Compose是否安装
check_docker_compose() {
  print_blue "检查Docker Compose是否安装..."
  if ! command -v docker-compose &> /dev/null; then
    print_yellow "⚠ Docker Compose未安装，尝试使用Docker Compose插件..."
    if ! docker compose version &> /dev/null; then
      print_red "✗ Docker Compose未安装，请先安装Docker Compose"
      exit 1
    fi
    DOCKER_COMPOSE="docker compose"
  else
    DOCKER_COMPOSE="docker-compose"
  fi
  print_green "✓ Docker Compose已安装"
}

# 停止现有容器
stop_containers() {
  print_blue "停止现有容器..."
  $DOCKER_COMPOSE -f docker-compose.perf-test.yml down
  print_green "✓ 现有容器已停止"
}

# 启动Docker容器
start_containers() {
  print_blue "启动Docker容器..."
  $DOCKER_COMPOSE -f docker-compose.perf-test.yml up -d
  if [ $? -ne 0 ]; then
    print_red "✗ 启动Docker容器失败"
    exit 1
  fi
  print_green "✓ Docker容器已启动"
}

# 等待容器健康检查通过
wait_for_containers() {
  print_blue "等待容器健康检查通过..."

  # 等待MySQL容器
  print_yellow "等待MySQL容器就绪..."
  while ! docker exec aibubb-perf-test-mysql mysqladmin ping -h localhost -u root -proot --silent &> /dev/null; do
    echo -n "."
    sleep 1
  done
  print_green "✓ MySQL容器已就绪"

  # 等待Redis容器
  print_yellow "等待Redis容器就绪..."
  while ! docker exec aibubb-perf-test-redis redis-cli ping &> /dev/null; do
    echo -n "."
    sleep 1
  done
  print_green "✓ Redis容器已就绪"

  # 等待InfluxDB容器
  print_yellow "等待InfluxDB容器就绪..."
  while ! docker exec aibubb-perf-test-influxdb influx -execute 'SHOW DATABASES' &> /dev/null; do
    echo -n "."
    sleep 1
  done
  print_green "✓ InfluxDB容器已就绪"

  # 等待Grafana容器
  print_yellow "等待Grafana容器就绪..."
  while ! curl -s http://localhost:13000/api/health &> /dev/null; do
    echo -n "."
    sleep 1
  done
  print_green "✓ Grafana容器已就绪"

  # 等待Prometheus容器
  print_yellow "等待Prometheus容器就绪..."
  while ! curl -s http://localhost:19090/-/healthy &> /dev/null; do
    echo -n "."
    sleep 1
  done
  print_green "✓ Prometheus容器已就绪"
}

# 主函数
main() {
  print_blue "=== 性能测试环境启动脚本 ==="

  # 检查Docker和Docker Compose
  check_docker
  check_docker_compose

  # 停止现有容器
  stop_containers

  # 启动Docker容器
  start_containers

  # 等待容器健康检查通过
  wait_for_containers

  print_green "\n✓ 性能测试环境已启动"
  print_blue "Grafana: http://localhost:13000 (admin/admin)"
  print_blue "Prometheus: http://localhost:19090"
  print_blue "InfluxDB: http://localhost:18086 (admin/admin)"
  print_blue "MySQL: localhost:13306 (root/root)"
  print_blue "Redis: localhost:16379"

  print_yellow "\n按 Ctrl+C 停止服务"
}

# 执行主函数
main
