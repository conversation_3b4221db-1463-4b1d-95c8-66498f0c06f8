#!/bin/bash

# 移除已归档的文档，避免重复
# 此脚本将删除已经归档到归档文档/后端文档/目录下的原始文档

echo "开始移除已归档的文档..."

# API设计与优化相关文档
echo "移除API设计与优化相关文档..."
rm -f "API设计规范.md"
rm -f "API设计工具选型评估.md"
rm -f "API设计优化方案.md"
rm -f "API文档自动生成实施方案.md"
rm -f "docs/API设计规范.md"

# API测试与监控相关文档
echo "移除API测试与监控相关文档..."
rm -f "docs/API契约测试实施方案.md"
rm -f "docs/API自动化测试实施方案.md"
rm -f "docs/API性能优化方案.md"
rm -f "docs/API监控系统实施方案.md"

# 领域驱动设计相关文档
echo "移除领域驱动设计相关文档..."
rm -f "领域驱动设计-架构调整方案.md"
rm -f "AIBUBB领域驱动设计实施指南.md"
rm -f "docs/用户领域DDD实施方案.md"
rm -f "领域驱动设计-领域知识梳理.md"
rm -f "领域驱动设计-学习内容领域分析.md"
rm -f "领域驱动设计-基础架构实现.md"
rm -f "领域驱动设计-标签领域实现.md"
rm -f "领域驱动设计-架构问题分析.md"
rm -f "领域驱动设计-领域模型设计.md"
rm -f "领域驱动设计-学习内容领域实现.md"
rm -f "docs/领域事件机制实施方案.md"
rm -f "docs/AIBUBB领域驱动设计实施指南.md"

# 评估与分析文档
echo "移除评估与分析文档..."
rm -f "api-documentation-architecture-analysis.md"
rm -f "归档文档/API设计一致性评估.md"
rm -f "归档文档/API文档与实现一致性审计.md"
rm -f "归档文档/API-First设计当前状况评估总结.md"

# 其他文档
echo "移除其他文档..."
rm -f "AIBUBB后端升级计划.md"

echo "已归档文档移除完成！"
echo "所有文档已归档到归档文档/后端文档/目录下，可以在那里查阅。"
echo "后端团队应以《后端系统升级综合规划.md》作为唯一参考文档。"
