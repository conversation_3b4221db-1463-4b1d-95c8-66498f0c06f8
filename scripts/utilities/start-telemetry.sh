#!/bin/bash

# 启动遥测服务
echo "启动遥测服务..."
docker-compose -f docker-compose.telemetry.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 显示服务状态
echo "服务状态:"
docker-compose -f docker-compose.telemetry.yml ps

# 显示访问信息
echo ""
echo "遥测服务已启动，可通过以下地址访问:"
echo "- Jaeger UI: http://localhost:16686"
echo "- Prometheus: http://localhost:9090"
echo "- Grafana: http://localhost:3000 (用户名: admin, 密码: admin)"
echo ""
echo "要启用OpenTelemetry追踪，请设置以下环境变量:"
echo "export OTEL_ENABLED=true"
echo "export OTEL_EXPORTER_TYPE=otlp"
echo "export OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318/v1/traces"
echo ""
echo "或者在.env文件中添加以上环境变量"
echo ""
