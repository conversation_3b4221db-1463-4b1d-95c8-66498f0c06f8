#!/bin/bash

# AIBUBB 项目文档验证执行脚本
# 用于自动化验证文档与实际代码的一致性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
REPORT_DIR="documentation-verification-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$REPORT_DIR/verification_report_$TIMESTAMP.md"

# 创建报告目录
mkdir -p "$REPORT_DIR"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 初始化报告文件
init_report() {
    cat > "$REPORT_FILE" << EOF
# AIBUBB 项目文档验证报告

**验证时间**: $(date)
**验证脚本版本**: 1.0
**项目路径**: $(pwd)

## 验证概要

EOF
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."

    local issues=0

    # 检查关键目录
    local required_dirs=("backend" "backend/models" "backend/routes" "backend/controllers" "backend/docs")

    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_error "缺少关键目录: $dir"
            ((issues++))
        else
            log_success "目录存在: $dir"
        fi
    done

    # 检查关键文件
    local required_files=("package.json" "docker-compose.yml" "README.md")

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少关键文件: $file"
            ((issues++))
        else
            log_success "文件存在: $file"
        fi
    done

    echo "### 项目结构检查" >> "$REPORT_FILE"
    echo "- 检查项目: $issues 个问题" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    return $issues
}

# 验证技术栈一致性
verify_tech_stack() {
    log_info "验证技术栈一致性..."

    local issues=0

    # 检查 package.json 依赖
    if [ -f "package.json" ]; then
        log_info "分析根目录 package.json..."
        if command -v jq >/dev/null 2>&1; then
            jq '.dependencies' package.json > "$REPORT_DIR/root_dependencies.json" 2>/dev/null || true
        fi
    fi

    if [ -f "backend/package.json" ]; then
        log_info "分析后端 package.json..."
        if command -v jq >/dev/null 2>&1; then
            jq '.dependencies' backend/package.json > "$REPORT_DIR/backend_dependencies.json" 2>/dev/null || true
        fi
    fi

    # 检查 Docker 配置
    if [ -f "docker-compose.yml" ]; then
        log_info "分析 Docker 配置..."
        grep -E "image:|build:" docker-compose.yml > "$REPORT_DIR/docker_images.txt" 2>/dev/null || true
    fi

    echo "### 技术栈验证" >> "$REPORT_FILE"
    echo "- 依赖分析完成，详见附件文件" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    return $issues
}

# 验证数据库模型
verify_database_models() {
    log_info "验证数据库模型..."

    local issues=0

    if [ -d "backend/models" ]; then
        # 统计模型文件
        local model_count=$(find backend/models -name "*.js" | wc -l)
        log_info "发现 $model_count 个模型文件"

        # 提取模型名称
        find backend/models -name "*.js" -exec basename {} .js \; | sort > "$REPORT_DIR/model_files.txt"

        # 检查模型关系
        grep -r "belongsTo\|hasMany\|hasOne" backend/models/ > "$REPORT_DIR/model_relationships.txt" 2>/dev/null || true

        # 检查数据类型
        grep -r "DataTypes\." backend/models/ > "$REPORT_DIR/model_datatypes.txt" 2>/dev/null || true

        log_success "模型分析完成"
    else
        log_error "backend/models 目录不存在"
        ((issues++))
    fi

    echo "### 数据库模型验证" >> "$REPORT_FILE"
    echo "- 模型文件数量: $model_count" >> "$REPORT_FILE"
    echo "- 详细分析见附件文件" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    return $issues
}

# 验证 API 端点
verify_api_endpoints() {
    log_info "验证 API 端点..."

    local issues=0

    if [ -d "backend/routes" ]; then
        # 提取路由定义
        find backend/routes -name "*.js" -exec grep -l "router\." {} \; > "$REPORT_DIR/route_files.txt"

        # 提取 API 端点
        grep -r "router\." backend/routes/ | grep -E "(get|post|put|delete)" > "$REPORT_DIR/api_endpoints.txt" 2>/dev/null || true

        # 统计端点数量
        local endpoint_count=$(wc -l < "$REPORT_DIR/api_endpoints.txt" 2>/dev/null || echo "0")
        log_info "发现 $endpoint_count 个 API 端点"

        log_success "API 端点分析完成"
    else
        log_error "backend/routes 目录不存在"
        ((issues++))
    fi

    # 检查 API 规范文件
    if [ -f "backend/docs/api-spec.json" ]; then
        log_info "发现 API 规范文件"
        if command -v jq >/dev/null 2>&1; then
            jq '.paths | keys' backend/docs/api-spec.json > "$REPORT_DIR/api_spec_paths.txt" 2>/dev/null || true
        fi
    else
        log_warning "未发现 API 规范文件"
    fi

    echo "### API 端点验证" >> "$REPORT_FILE"
    echo "- API 端点数量: $endpoint_count" >> "$REPORT_FILE"
    echo "- API 规范文件: $([ -f "backend/docs/api-spec.json" ] && echo "存在" || echo "不存在")" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    return $issues
}

# 验证部署配置
verify_deployment_config() {
    log_info "验证部署配置..."

    local issues=0

    # 检查 Docker 文件
    local docker_files=("docker-compose.yml" "Dockerfile" "backend/Dockerfile")

    for file in "${docker_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "Docker 文件存在: $file"
        else
            log_warning "Docker 文件不存在: $file"
        fi
    done

    # 检查环境配置
    if [ -f ".env.example" ]; then
        log_success "环境配置示例存在"
        grep "=" .env.example | cut -d'=' -f1 > "$REPORT_DIR/env_variables.txt"
    else
        log_warning "环境配置示例不存在"
    fi

    # 检查部署脚本
    local deploy_scripts=("docker-start.sh" "docker-stop.sh")

    for script in "${deploy_scripts[@]}"; do
        if [ -f "$script" ]; then
            log_success "部署脚本存在: $script"
            if [ -x "$script" ]; then
                log_success "脚本可执行: $script"
            else
                log_warning "脚本不可执行: $script"
            fi
        else
            log_warning "部署脚本不存在: $script"
        fi
    done

    echo "### 部署配置验证" >> "$REPORT_FILE"
    echo "- Docker 配置文件检查完成" >> "$REPORT_FILE"
    echo "- 部署脚本检查完成" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    return $issues
}

# 验证文档存在性
verify_documentation_existence() {
    log_info "验证文档存在性..."

    local issues=0
    local total_docs=0
    local existing_docs=0

    # 从 DOCUMENTATION-INDEX.md 提取文档列表
    if [ -f "DOCUMENTATION-INDEX.md" ]; then
        # 提取 Markdown 链接中的文档路径
        grep -o '\[.*\]([^)]*\.md)' DOCUMENTATION-INDEX.md | sed 's/.*(\([^)]*\)).*/\1/' > "$REPORT_DIR/documented_files.txt"

        while IFS= read -r doc_path; do
            ((total_docs++))
            # 处理相对路径
            clean_path=$(echo "$doc_path" | sed 's|^\./||')

            if [ -f "$clean_path" ]; then
                ((existing_docs++))
                log_success "文档存在: $clean_path"
            else
                log_error "文档缺失: $clean_path"
                ((issues++))
            fi
        done < "$REPORT_DIR/documented_files.txt"
    else
        log_error "DOCUMENTATION-INDEX.md 不存在"
        ((issues++))
    fi

    echo "### 文档存在性验证" >> "$REPORT_FILE"
    echo "- 总文档数: $total_docs" >> "$REPORT_FILE"
    echo "- 存在文档数: $existing_docs" >> "$REPORT_FILE"
    echo "- 缺失文档数: $((total_docs - existing_docs))" >> "$REPORT_FILE"
    echo "- 存在率: $(( existing_docs * 100 / total_docs ))%" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    return $issues
}

# 生成验证摘要
generate_summary() {
    local total_issues=$1

    echo "## 验证摘要" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "- **总问题数**: $total_issues" >> "$REPORT_FILE"
    echo "- **验证状态**: $([ $total_issues -eq 0 ] && echo "通过" || echo "需要修复")" >> "$REPORT_FILE"
    echo "- **报告生成时间**: $(date)" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    echo "## 附件文件" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "以下文件包含详细的分析结果：" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    for file in "$REPORT_DIR"/*.txt "$REPORT_DIR"/*.json; do
        if [ -f "$file" ]; then
            echo "- $(basename "$file")" >> "$REPORT_FILE"
        fi
    done

    echo "" >> "$REPORT_FILE"
    echo "## 下一步建议" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    if [ $total_issues -eq 0 ]; then
        echo "✅ 自动验证通过，建议进行人工深度验证" >> "$REPORT_FILE"
    else
        echo "⚠️ 发现 $total_issues 个问题，建议优先修复" >> "$REPORT_FILE"
    fi

    echo "" >> "$REPORT_FILE"
    echo "---" >> "$REPORT_FILE"
    echo "*此报告由自动化脚本生成，建议结合人工验证*" >> "$REPORT_FILE"
}

# 主函数
main() {
    log_info "开始 AIBUBB 项目文档验证..."

    init_report

    local total_issues=0

    # 执行各项验证
    check_project_structure
    total_issues=$((total_issues + $?))

    verify_tech_stack
    total_issues=$((total_issues + $?))

    verify_database_models
    total_issues=$((total_issues + $?))

    verify_api_endpoints
    total_issues=$((total_issues + $?))

    verify_deployment_config
    total_issues=$((total_issues + $?))

    verify_documentation_existence
    total_issues=$((total_issues + $?))

    # 生成摘要
    generate_summary $total_issues

    log_info "验证完成！"
    log_info "报告文件: $REPORT_FILE"
    log_info "附件目录: $REPORT_DIR"

    if [ $total_issues -eq 0 ]; then
        log_success "自动验证通过！"
        exit 0
    else
        log_warning "发现 $total_issues 个问题，请查看报告"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
AIBUBB 项目文档验证脚本

用法: $0 [选项]

选项:
  -h, --help     显示此帮助信息
  -v, --verbose  详细输出模式

此脚本将自动验证项目文档与实际代码的一致性，包括：
- 项目结构检查
- 技术栈一致性验证
- 数据库模型验证
- API 端点验证
- 部署配置验证
- 文档存在性验证

验证结果将保存在 $REPORT_DIR 目录中。

EOF
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -v|--verbose)
        set -x
        main
        ;;
    "")
        main
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
