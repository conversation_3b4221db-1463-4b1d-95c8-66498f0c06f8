/**
 * NebulaLearn 设计系统
 * 定义全局设计标准和规范
 */

// 色彩系统
export const colors = {
  // 主色调
  primary: {
    light: '#4dabf5',
    main: '#2196f3',
    dark: '#1976d2',
    contrastText: '#ffffff'
  },

  // 次要色调
  secondary: {
    light: '#ff94c2',
    main: '#f06292',
    dark: '#ba2d65',
    contrastText: '#ffffff'
  },

  // 成功状态
  success: {
    light: '#81c784',
    main: '#4caf50',
    dark: '#388e3c',
    contrastText: '#ffffff'
  },

  // 信息状态
  info: {
    light: '#64b5f6',
    main: '#2196f3',
    dark: '#1976d2',
    contrastText: '#ffffff'
  },

  // 警告状态
  warning: {
    light: '#ffb74d',
    main: '#ff9800',
    dark: '#f57c00',
    contrastText: '#000000'
  },

  // 错误状态
  error: {
    light: '#e57373',
    main: '#f44336',
    dark: '#d32f2f',
    contrastText: '#ffffff'
  },

  // 中性色
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121'
  },

  // 基础色
  common: {
    black: '#000000',
    white: '#ffffff',
    transparent: 'transparent'
  },

  // 背景色
  background: {
    default: '#f5f5f5',
    paper: '#ffffff',
    card: '#ffffff'
  },

  // 文本色
  text: {
    primary: 'rgba(0, 0, 0, 0.87)',
    secondary: 'rgba(0, 0, 0, 0.6)',
    disabled: 'rgba(0, 0, 0, 0.38)',
    hint: 'rgba(0, 0, 0, 0.38)'
  },

  // 分割线
  divider: 'rgba(0, 0, 0, 0.12)',

  // 阴影
  shadow: 'rgba(0, 0, 0, 0.2)'
};

// 排版规范
export const typography = {
  // 字体家族
  fontFamily: {
    base: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
    code: 'source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace'
  },

  // 字体大小
  fontSize: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px',
    '4xl': '36px',
    '5xl': '48px',
    '6xl': '60px'
  },

  // 字体粗细
  fontWeight: {
    thin: 100,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900
  },

  // 行高
  lineHeight: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2
  },

  // 字母间距
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em'
  },

  // 标题样式
  h1: {
    fontSize: '30px',
    fontWeight: 700,
    lineHeight: 1.2
  },
  h2: {
    fontSize: '24px',
    fontWeight: 700,
    lineHeight: 1.3
  },
  h3: {
    fontSize: '20px',
    fontWeight: 600,
    lineHeight: 1.4
  },
  h4: {
    fontSize: '18px',
    fontWeight: 600,
    lineHeight: 1.4
  },
  h5: {
    fontSize: '16px',
    fontWeight: 600,
    lineHeight: 1.5
  },
  h6: {
    fontSize: '14px',
    fontWeight: 600,
    lineHeight: 1.5
  },

  // 正文样式
  body1: {
    fontSize: '16px',
    fontWeight: 400,
    lineHeight: 1.5
  },
  body2: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: 1.5
  },

  // 其他文本样式
  caption: {
    fontSize: '12px',
    fontWeight: 400,
    lineHeight: 1.5
  },
  button: {
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: 1.75,
    textTransform: 'uppercase'
  }
};

// 间距规范
export const spacing = {
  // 基础间距单位 (4px)
  unit: 4,

  // 预设间距
  xs: '4px', // 超小间距 (1 * unit)
  sm: '8px', // 小间距 (2 * unit)
  md: '16px', // 中等间距 (4 * unit)
  lg: '24px', // 大间距 (6 * unit)
  xl: '32px', // 超大间距 (8 * unit)
  '2xl': '48px', // 2倍超大间距 (12 * unit)
  '3xl': '64px', // 3倍超大间距 (16 * unit)

  // 辅助函数
  getValue: multiplier => `${multiplier * 4}px`
};

// 圆角规范
export const borderRadius = {
  none: '0',
  xs: '2px',
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  '2xl': '24px',
  full: '9999px'
};

// 阴影规范
export const shadows = {
  none: 'none',
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'
};

// 动画与过渡规范
export const animation = {
  // 过渡时间
  duration: {
    fastest: '100ms',
    fast: '200ms',
    normal: '300ms',
    slow: '400ms',
    slowest: '500ms'
  },

  // 过渡曲线
  easing: {
    // 标准过渡曲线
    standard: 'cubic-bezier(0.4, 0, 0.2, 1)',
    // 减速过渡曲线（结束时减速）
    deceleration: 'cubic-bezier(0.0, 0, 0.2, 1)',
    // 加速过渡曲线（开始时加速）
    acceleration: 'cubic-bezier(0.4, 0, 1, 1)',
    // 急速过渡曲线（加速然后减速）
    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    // 弹性过渡曲线
    spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
  }
};

// 响应式断点
export const breakpoints = {
  xs: '0px', // 超小屏幕（手机竖屏）
  sm: '600px', // 小屏幕（手机横屏）
  md: '960px', // 中等屏幕（平板）
  lg: '1280px', // 大屏幕（桌面）
  xl: '1920px' // 超大屏幕
};

// z-index层级规范
export const zIndex = {
  mobileStepper: 1000,
  appBar: 1100,
  drawer: 1200,
  modal: 1300,
  snackbar: 1400,
  tooltip: 1500,
  fab: 1050,
  speedDial: 1050
};

// 导出设计系统
export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animation,
  breakpoints,
  zIndex
};
