/**
 * Nebula<PERSON>ear<PERSON> 全局样式
 */

/* 重置样式 */
page, view, text, image, button, input, textarea, scroll-view {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 全局字体设置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.87);
  background-color: #f5f5f5;
}

/* 文本样式 */
.text-primary {
  color: rgba(0, 0, 0, 0.87);
}

.text-secondary {
  color: rgba(0, 0, 0, 0.6);
}

.text-disabled {
  color: rgba(0, 0, 0, 0.38);
}

.text-hint {
  color: rgba(0, 0, 0, 0.38);
}

/* 标题样式 */
.h1 {
  font-size: 30px;
  font-weight: 700;
  line-height: 1.2;
}

.h2 {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.3;
}

.h3 {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
}

.h4 {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.h5 {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
}

.h6 {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.5;
}

/* 正文样式 */
.body1 {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}

.body2 {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
}

.caption {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
}

/* 文本对齐 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 文本变形 */
.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

/* 文本截断 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本截断 */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-xs { margin: 4px; }
.m-sm { margin: 8px; }
.m-md { margin: 16px; }
.m-lg { margin: 24px; }
.m-xl { margin: 32px; }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: 4px; }
.mt-sm { margin-top: 8px; }
.mt-md { margin-top: 16px; }
.mt-lg { margin-top: 24px; }
.mt-xl { margin-top: 32px; }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: 4px; }
.mr-sm { margin-right: 8px; }
.mr-md { margin-right: 16px; }
.mr-lg { margin-right: 24px; }
.mr-xl { margin-right: 32px; }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: 4px; }
.mb-sm { margin-bottom: 8px; }
.mb-md { margin-bottom: 16px; }
.mb-lg { margin-bottom: 24px; }
.mb-xl { margin-bottom: 32px; }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: 4px; }
.ml-sm { margin-left: 8px; }
.ml-md { margin-left: 16px; }
.ml-lg { margin-left: 24px; }
.ml-xl { margin-left: 32px; }

.p-0 { padding: 0; }
.p-xs { padding: 4px; }
.p-sm { padding: 8px; }
.p-md { padding: 16px; }
.p-lg { padding: 24px; }
.p-xl { padding: 32px; }

.pt-0 { padding-top: 0; }
.pt-xs { padding-top: 4px; }
.pt-sm { padding-top: 8px; }
.pt-md { padding-top: 16px; }
.pt-lg { padding-top: 24px; }
.pt-xl { padding-top: 32px; }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: 4px; }
.pr-sm { padding-right: 8px; }
.pr-md { padding-right: 16px; }
.pr-lg { padding-right: 24px; }
.pr-xl { padding-right: 32px; }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: 4px; }
.pb-sm { padding-bottom: 8px; }
.pb-md { padding-bottom: 16px; }
.pb-lg { padding-bottom: 24px; }
.pb-xl { padding-bottom: 32px; }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: 4px; }
.pl-sm { padding-left: 8px; }
.pl-md { padding-left: 16px; }
.pl-lg { padding-left: 24px; }
.pl-xl { padding-left: 32px; }

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

/* 显示与可见性 */
.hidden {
  display: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

/* 定位 */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* 圆角 */
.rounded-none {
  border-radius: 0;
}

.rounded-xs {
  border-radius: 2px;
}

.rounded-sm {
  border-radius: 4px;
}

.rounded-md {
  border-radius: 8px;
}

.rounded-lg {
  border-radius: 12px;
}

.rounded-xl {
  border-radius: 16px;
}

.rounded-full {
  border-radius: 9999px;
}

/* 阴影 */
.shadow-none {
  box-shadow: none;
}

.shadow-xs {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow-sm {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 颜色 */
.bg-primary {
  background-color: #2196f3;
}

.bg-secondary {
  background-color: #f06292;
}

.bg-success {
  background-color: #4caf50;
}

.bg-info {
  background-color: #2196f3;
}

.bg-warning {
  background-color: #ff9800;
}

.bg-error {
  background-color: #f44336;
}

.bg-white {
  background-color: #ffffff;
}

.bg-black {
  background-color: #000000;
}

.bg-transparent {
  background-color: transparent;
}

.text-primary-color {
  color: #2196f3;
}

.text-secondary-color {
  color: #f06292;
}

.text-success-color {
  color: #4caf50;
}

.text-info-color {
  color: #2196f3;
}

.text-warning-color {
  color: #ff9800;
}

.text-error-color {
  color: #f44336;
}

.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

/* 边框 */
.border {
  border-width: 1px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.12);
}

.border-none {
  border: none;
}

.border-t {
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: rgba(0, 0, 0, 0.12);
}

.border-r {
  border-right-width: 1px;
  border-right-style: solid;
  border-right-color: rgba(0, 0, 0, 0.12);
}

.border-b {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

.border-l {
  border-left-width: 1px;
  border-left-style: solid;
  border-left-color: rgba(0, 0, 0, 0.12);
}

/* 宽度和高度 */
.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.h-full {
  height: 100%;
}

.h-auto {
  height: auto;
}

/* 溢出处理 */
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-visible {
  overflow: visible;
}

/* 透明度 */
.opacity-0 {
  opacity: 0;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-100 {
  opacity: 1;
}

/* 动画 */
.transition {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.transition-fast {
  transition-duration: 200ms;
}

.transition-slow {
  transition-duration: 400ms;
}

/* 常用状态样式 */
.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.clickable {
  cursor: pointer;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.12);
  width: 100%;
}

.divider-vertical {
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.12);
}

/* 容器 */
.container {
  padding-left: 16px;
  padding-right: 16px;
  width: 100%;
}

/* 安全区域适配 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

/* 状态颜色 */
.status-not-started {
  background-color: #3498db;
  color: #ffffff;
}

.status-in-progress {
  background-color: #27ae60;
  color: #ffffff;
}

.status-completed {
  background-color: #f39c12;
  color: #ffffff;
}

.status-paused {
  background-color: #7f8c8d;
  color: #ffffff;
}

.status-abandoned {
  background-color: #e74c3c;
  color: #ffffff;
}
