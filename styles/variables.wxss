/**
 * NebulaLearn 设计系统变量
 * 基于AIBUBB视觉设计文档V2.0
 */

/* 色彩系统 */
page {
  /* 主色调 */
  --primary-color-light: #4dabf5;
  --primary-color: #2196f3;
  --primary-color-dark: #1976d2;
  
  /* 次要色调 */
  --secondary-color-light: #ff94c2;
  --secondary-color: #f06292;
  --secondary-color-dark: #ba2d65;
  
  /* 成功状态 */
  --success-color-light: #81c784;
  --success-color: #4caf50;
  --success-color-dark: #388e3c;
  
  /* 信息状态 */
  --info-color-light: #64b5f6;
  --info-color: #2196f3;
  --info-color-dark: #1976d2;
  
  /* 警告状态 */
  --warning-color-light: #ffb74d;
  --warning-color: #ff9800;
  --warning-color-dark: #f57c00;
  
  /* 错误状态 */
  --error-color-light: #e57373;
  --error-color: #f44336;
  --error-color-dark: #d32f2f;
  
  /* 中性色 */
  --grey-50: #fafafa;
  --grey-100: #f5f5f5;
  --grey-200: #eeeeee;
  --grey-300: #e0e0e0;
  --grey-400: #bdbdbd;
  --grey-500: #9e9e9e;
  --grey-600: #757575;
  --grey-700: #616161;
  --grey-800: #424242;
  --grey-900: #212121;
  
  /* 基础色 */
  --black: #000000;
  --white: #ffffff;
  --transparent: transparent;
  
  /* 文本色 - 亮色模式 */
  --text-color-primary: rgba(0, 0, 0, 0.87);
  --text-color-secondary: rgba(0, 0, 0, 0.6);
  --text-color-disabled: rgba(0, 0, 0, 0.38);
  --text-color-hint: rgba(0, 0, 0, 0.38);
  
  /* 背景色 - 亮色模式 */
  --bg-color-default: #f5f5f5;
  --bg-color-paper: #ffffff;
  --bg-color-card: #ffffff;
  
  /* 分割线 - 亮色模式 */
  --divider-color: rgba(0, 0, 0, 0.12);
  
  /* 阴影 - 亮色模式 */
  --shadow-color: rgba(0, 0, 0, 0.2);
  
  /* 渐变色 - 亮色模式 */
  --bg-gradient-start: #e0f2ff;
  --bg-gradient-end: #f0e6ff;
  --button-gradient-start: #3B82F6;
  --button-gradient-end: #8B5CF6;
  --button-shadow: rgba(59, 130, 246, 0.2);
  
  /* 卡片样式 - 亮色模式 */
  --card-bg: rgba(255, 255, 255, 0.25);
  --card-border: rgba(255, 255, 255, 0.18);
  --card-shadow: rgba(31, 38, 135, 0.1);
}

/* 深色模式样式 */
page[data-theme="dark"] {
  /* 文本色 - 深色模式 */
  --text-color-primary: #e0e0e0;
  --text-color-secondary: #b0b0b0;
  --text-color-disabled: #909090;
  --text-color-hint: #909090;
  
  /* 背景色 - 深色模式 */
  --bg-color-default: #121212;
  --bg-color-paper: #1e1e1e;
  --bg-color-card: #1e1e1e;
  
  /* 分割线 - 深色模式 */
  --divider-color: rgba(255, 255, 255, 0.12);
  
  /* 阴影 - 深色模式 */
  --shadow-color: rgba(0, 0, 0, 0.4);
  
  /* 渐变色 - 深色模式 */
  --bg-gradient-start: var(--star-bg-color);
  --bg-gradient-end: var(--star-bg-gradient-middle);
  --button-gradient-start: #4f46e5;
  --button-gradient-end: #7e22ce;
  --button-shadow: rgba(79, 70, 229, 0.3);
  
  /* 卡片样式 - 深色模式 */
  --card-bg: rgba(30, 30, 40, 0.6);
  --card-border: rgba(60, 60, 80, 0.3);
  --card-shadow: rgba(0, 0, 0, 0.3);
  
  /* 石墨色效果 - 用于导航栏和tabBar */
  --graphite-bg: #2C2C2E;
  --graphite-border: #3C3C3E;
  --graphite-shadow: rgba(0, 0, 0, 0.3);
}

/* 字体排版 */
page {
  /* 字体大小 */
  --font-size-xs: 24rpx; /* 12px */
  --font-size-sm: 28rpx; /* 14px */
  --font-size-md: 32rpx; /* 16px */
  --font-size-lg: 36rpx; /* 18px */
  --font-size-xl: 40rpx; /* 20px */
  --font-size-2xl: 48rpx; /* 24px */
  --font-size-3xl: 60rpx; /* 30px */
  
  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* 字体层级 */
  --font-h1: var(--font-size-3xl)/1.2 var(--font-weight-bold);
  --font-h2: var(--font-size-2xl)/1.3 var(--font-weight-bold);
  --font-h3: var(--font-size-xl)/1.4 var(--font-weight-semibold);
  --font-h4: var(--font-size-lg)/1.4 var(--font-weight-semibold);
  --font-h5: var(--font-size-md)/1.5 var(--font-weight-semibold);
  --font-h6: var(--font-size-sm)/1.5 var(--font-weight-semibold);
  --font-body1: var(--font-size-md)/1.5 var(--font-weight-normal);
  --font-body2: var(--font-size-sm)/1.5 var(--font-weight-normal);
  --font-caption: var(--font-size-xs)/1.5 var(--font-weight-normal);
}

/* 间距 */
page {
  /* 基础间距 (基于4px/8px倍数) */
  --space-xs: 8rpx;  /* 4px */
  --space-sm: 16rpx; /* 8px */
  --space-md: 32rpx; /* 16px */
  --space-lg: 48rpx; /* 24px */
  --space-xl: 64rpx; /* 32px */
  --space-2xl: 96rpx; /* 48px */
  --space-3xl: 128rpx; /* 64px */
}

/* 圆角 */
page {
  --radius-none: 0;
  --radius-xs: 4rpx;   /* 2px */
  --radius-sm: 8rpx;   /* 4px */
  --radius-md: 16rpx;  /* 8px */
  --radius-lg: 24rpx;  /* 12px */
  --radius-xl: 32rpx;  /* 16px */
  --radius-2xl: 48rpx; /* 24px */
  --radius-full: 9999rpx;
}

/* 阴影 */
page {
  --shadow-none: none;
  --shadow-xs: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2rpx 6rpx 0 rgba(0, 0, 0, 0.1), 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1), 0 4rpx 8rpx -2rpx rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 20rpx 30rpx -6rpx rgba(0, 0, 0, 0.1), 0 8rpx 12rpx -4rpx rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 40rpx 50rpx -10rpx rgba(0, 0, 0, 0.1), 0 20rpx 20rpx -10rpx rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 50rpx 100rpx -24rpx rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 4rpx 8rpx 0 rgba(0, 0, 0, 0.06);
}

/* 动画与过渡 */
page {
  /* 过渡时间 */
  --transition-fastest: 100ms;
  --transition-fast: 200ms;
  --transition-normal: 300ms;
  --transition-slow: 400ms;
  --transition-slowest: 500ms;
  
  /* 过渡曲线 */
  --easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-deceleration: cubic-bezier(0.0, 0, 0.2, 1);
  --easing-acceleration: cubic-bezier(0.4, 0, 1, 1);
  --easing-sharp: cubic-bezier(0.4, 0, 0.6, 1);
  --easing-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* z-index层级 */
page {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1100;
  --z-index-fixed: 1200;
  --z-index-modal-backdrop: 1300;
  --z-index-modal: 1400;
  --z-index-popover: 1500;
  --z-index-tooltip: 1600;
  --z-index-toast: 1700;
  --z-index-fab: 1050;
}

/* 星星背景色变量 */
page {
  --star-bg-color: #0f0921;
  --star-bg-gradient-start: #0f0921;
  --star-bg-gradient-middle: #1a1035;
  --star-bg-gradient-end: #26164d;
}
