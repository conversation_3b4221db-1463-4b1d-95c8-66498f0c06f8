/**
 * 统一授权中间件测试
 */
const httpMocks = require('node-mocks-http');
const { authorize, loadUserInfo } = require('../../../middlewares/unified-auth.middleware');
const apiResponse = require('../../../utils/apiResponse');
const logger = require('../../../config/logger');
const { User, Role, Permission } = require('../../../models');
const enhancedCache = require('../../../services/enhanced-cache.service');
const authorizationConfig = require('../../../config/authorization-config');

// 模拟依赖
jest.mock('../../../utils/apiResponse');
jest.mock('../../../models');
jest.mock('../../../services/enhanced-cache.service');

describe('UnifiedAuthMiddleware', () => {
  // 测试数据
  const testUserId = 123;
  const testResourceId = 456;
  
  // 每个测试前重置模拟
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 模拟apiResponse方法
    apiResponse.forbidden = jest.fn().mockReturnValue('forbidden-response');
    apiResponse.serverError = jest.fn().mockReturnValue('server-error-response');
    
    // 模拟enhancedCache方法
    enhancedCache.get = jest.fn().mockResolvedValue(null);
    enhancedCache.set = jest.fn().mockResolvedValue(true);
  });
  
  describe('authorize', () => {
    it('应该允许管理员访问需要管理员权限的资源', async () => {
      // 创建模拟请求和响应
      const req = httpMocks.createRequest({
        user: {
          userId: testUserId,
          isAdmin: true
        },
        params: {
          id: testResourceId
        }
      });
      const res = httpMocks.createResponse();
      const next = jest.fn();
      
      // 模拟授权规则
      const mockRule = {
        adminOnly: true
      };
      
      // 模拟authorizationRules
      jest.spyOn(authorizationConfig, 'resources', 'get').mockReturnValue({
        testResource: {
          testAction: mockRule
        }
      });
      
      // 调用方法
      await authorize('testResource', 'testAction')(req, res, next);
      
      // 验证结果
      expect(next).toHaveBeenCalled();
      expect(apiResponse.forbidden).not.toHaveBeenCalled();
    });
    
    it('应该拒绝非管理员访问需要管理员权限的资源', async () => {
      // 创建模拟请求和响应
      const req = httpMocks.createRequest({
        user: {
          userId: testUserId,
          isAdmin: false
        },
        params: {
          id: testResourceId
        }
      });
      const res = httpMocks.createResponse();
      const next = jest.fn();
      
      // 模拟授权规则
      const mockRule = {
        adminOnly: true
      };
      
      // 模拟authorizationRules
      jest.spyOn(authorizationConfig, 'resources', 'get').mockReturnValue({
        testResource: {
          testAction: mockRule
        }
      });
      
      // 调用方法
      await authorize('testResource', 'testAction')(req, res, next);
      
      // 验证结果
      expect(next).not.toHaveBeenCalled();
      expect(apiResponse.forbidden).toHaveBeenCalledWith(res, '需要管理员权限');
    });
    
    it('应该允许资源所有者访问自己的资源', async () => {
      // 创建模拟请求和响应
      const req = httpMocks.createRequest({
        user: {
          userId: testUserId,
          isAdmin: false
        },
        params: {
          id: testResourceId
        },
        app: {
          get: jest.fn().mockReturnValue({
            Note: {
              findByPk: jest.fn().mockResolvedValue({
                user_id: testUserId
              })
            }
          })
        }
      });
      const res = httpMocks.createResponse();
      const next = jest.fn();
      
      // 模拟授权规则
      const mockRule = {
        ownerCheck: true
      };
      
      // 模拟authorizationRules
      jest.spyOn(authorizationConfig, 'resources', 'get').mockReturnValue({
        note: {
          testAction: mockRule
        }
      });
      
      // 调用方法
      await authorize('note', 'testAction')(req, res, next);
      
      // 验证结果
      expect(next).toHaveBeenCalled();
      expect(apiResponse.forbidden).not.toHaveBeenCalled();
    });
    
    it('应该拒绝非资源所有者访问他人的资源', async () => {
      // 创建模拟请求和响应
      const req = httpMocks.createRequest({
        user: {
          userId: testUserId,
          isAdmin: false
        },
        params: {
          id: testResourceId
        },
        app: {
          get: jest.fn().mockReturnValue({
            Note: {
              findByPk: jest.fn().mockResolvedValue({
                user_id: 999 // 不同的用户ID
              })
            }
          })
        }
      });
      const res = httpMocks.createResponse();
      const next = jest.fn();
      
      // 模拟授权规则
      const mockRule = {
        ownerCheck: true
      };
      
      // 模拟authorizationRules
      jest.spyOn(authorizationConfig, 'resources', 'get').mockReturnValue({
        note: {
          testAction: mockRule
        }
      });
      
      // 调用方法
      await authorize('note', 'testAction')(req, res, next);
      
      // 验证结果
      expect(next).not.toHaveBeenCalled();
      expect(apiResponse.forbidden).toHaveBeenCalledWith(res, '没有足够的权限执行此操作');
    });
    
    it('应该允许访问公开资源', async () => {
      // 创建模拟请求和响应
      const req = httpMocks.createRequest({
        user: {
          userId: testUserId,
          isAdmin: false
        },
        params: {
          id: testResourceId
        },
        app: {
          get: jest.fn().mockReturnValue({
            Note: {
              findByPk: jest.fn().mockResolvedValue({
                user_id: 999, // 不同的用户ID
                status: 'published' // 公开状态
              })
            }
          })
        }
      });
      const res = httpMocks.createResponse();
      const next = jest.fn();
      
      // 模拟授权规则
      const mockRule = {
        publicCheck: true
      };
      
      // 模拟authorizationRules
      jest.spyOn(authorizationConfig, 'resources', 'get').mockReturnValue({
        note: {
          testAction: mockRule
        }
      });
      
      // 调用方法
      await authorize('note', 'testAction')(req, res, next);
      
      // 验证结果
      expect(next).toHaveBeenCalled();
      expect(apiResponse.forbidden).not.toHaveBeenCalled();
    });
    
    it('应该处理授权检查过程中的错误', async () => {
      // 创建模拟请求和响应
      const req = httpMocks.createRequest({
        user: {
          userId: testUserId,
          isAdmin: false
        },
        params: {
          id: testResourceId
        }
      });
      const res = httpMocks.createResponse();
      const next = jest.fn();
      
      // 模拟授权规则
      const mockRule = {
        ownerCheck: true
      };
      
      // 模拟authorizationRules
      jest.spyOn(authorizationConfig, 'resources', 'get').mockReturnValue({
        note: {
          testAction: mockRule
        }
      });
      
      // 模拟错误
      req.app = {
        get: jest.fn().mockImplementation(() => {
          throw new Error('测试错误');
        })
      };
      
      // 调用方法
      await authorize('note', 'testAction')(req, res, next);
      
      // 验证结果
      expect(next).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalled();
      expect(apiResponse.serverError).toHaveBeenCalledWith(res, '授权检查过程中发生错误');
    });
  });
  
  describe('loadUserInfo', () => {
    it('应该从缓存加载用户信息', async () => {
      // 创建模拟请求
      const req = {
        user: {
          id: testUserId
        }
      };
      
      // 模拟缓存数据
      const cachedUserInfo = {
        id: testUserId,
        username: 'testuser',
        email: '<EMAIL>',
        isAdmin: true,
        roles: []
      };
      
      // 模拟缓存命中
      enhancedCache.get.mockResolvedValue(cachedUserInfo);
      
      // 调用方法
      await loadUserInfo(req);
      
      // 验证结果
      expect(req.userInfo).toEqual(cachedUserInfo);
      expect(enhancedCache.get).toHaveBeenCalledWith(`user:${testUserId}:info`);
      expect(User.findByPk).not.toHaveBeenCalled();
    });
    
    it('应该在缓存未命中时从数据库加载用户信息', async () => {
      // 创建模拟请求
      const req = {
        user: {
          id: testUserId
        }
      };
      
      // 模拟数据库用户数据
      const dbUser = {
        id: testUserId,
        username: 'testuser',
        email: '<EMAIL>',
        is_admin: true,
        status: 'active',
        level_id: 1,
        roles: [
          {
            id: 1,
            name: 'admin',
            description: 'Administrator',
            permissions: [
              {
                id: 1,
                name: 'manage_users',
                resource: 'user',
                action: 'manage'
              }
            ]
          }
        ]
      };
      
      // 模拟缓存未命中
      enhancedCache.get.mockResolvedValue(null);
      
      // 模拟数据库查询
      User.findByPk.mockResolvedValue(dbUser);
      
      // 调用方法
      await loadUserInfo(req);
      
      // 验证结果
      expect(req.userInfo).toBeDefined();
      expect(req.userInfo.id).toBe(testUserId);
      expect(req.userInfo.isAdmin).toBe(true);
      expect(req.userInfo.roles).toHaveLength(1);
      expect(req.userInfo.roles[0].permissions).toHaveLength(1);
      
      expect(enhancedCache.get).toHaveBeenCalledWith(`user:${testUserId}:info`);
      expect(User.findByPk).toHaveBeenCalledWith(testUserId, expect.any(Object));
      expect(enhancedCache.set).toHaveBeenCalled();
    });
    
    it('应该处理加载用户信息过程中的错误', async () => {
      // 创建模拟请求
      const req = {
        user: {
          id: testUserId
        }
      };
      
      // 模拟缓存未命中
      enhancedCache.get.mockResolvedValue(null);
      
      // 模拟数据库查询错误
      User.findByPk.mockRejectedValue(new Error('数据库错误'));
      
      // 调用方法
      await loadUserInfo(req);
      
      // 验证结果
      expect(req.userInfo).toBeUndefined();
      expect(logger.error).toHaveBeenCalled();
    });
  });
});
