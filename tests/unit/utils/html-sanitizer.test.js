/**
 * HTML净化工具测试
 */
const { sanitizeHtml, sanitizeObject, sanitizeArray } = require('../../../utils/html-sanitizer');
const logger = require('../../../config/logger');

// 模拟依赖
jest.mock('../../../config/logger');

describe('HtmlSanitizer', () => {
  // 每个测试前重置模拟
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('sanitizeHtml', () => {
    it('应该移除HTML标签', () => {
      // 测试数据
      const html = '<script>alert("XSS")</script><p>Hello <b>World</b></p>';
      
      // 调用方法
      const result = sanitizeHtml(html);
      
      // 验证结果
      expect(result).toBe('Hello World');
    });
    
    it('应该保留允许的HTML标签', () => {
      // 测试数据
      const html = '<script>alert("XSS")</script><p>Hello <b>World</b></p>';
      
      // 调用方法
      const result = sanitizeHtml(html, true);
      
      // 验证结果
      expect(result).toBe('<p>Hello <b>World</b></p>');
    });
    
    it('应该处理null和undefined输入', () => {
      // 调用方法
      const result1 = sanitizeHtml(null);
      const result2 = sanitizeHtml(undefined);
      
      // 验证结果
      expect(result1).toBeNull();
      expect(result2).toBeUndefined();
    });
    
    it('应该处理净化过程中的错误', () => {
      // 模拟DOMPurify.sanitize抛出错误
      const originalSanitize = require('dompurify').sanitize;
      require('dompurify').sanitize = jest.fn().mockImplementation(() => {
        throw new Error('净化错误');
      });
      
      // 测试数据
      const html = '<script>alert("XSS")</script><p>Hello <b>World</b></p>';
      
      // 调用方法
      const result = sanitizeHtml(html);
      
      // 验证结果
      expect(result).toBe('alertXSSHello World');
      expect(logger.error).toHaveBeenCalled();
      
      // 恢复原始方法
      require('dompurify').sanitize = originalSanitize;
    });
  });
  
  describe('sanitizeObject', () => {
    it('应该净化对象中的所有字符串属性', () => {
      // 测试数据
      const obj = {
        name: '<script>alert("XSS")</script>John',
        age: 30,
        description: '<p>This is a <b>description</b></p>',
        nested: {
          title: '<h1>Title</h1>',
          content: '<script>evil()</script><p>Content</p>'
        }
      };
      
      // 调用方法
      const result = sanitizeObject(obj);
      
      // 验证结果
      expect(result).toEqual({
        name: 'John',
        age: 30,
        description: 'This is a description',
        nested: {
          title: 'Title',
          content: 'Content'
        }
      });
    });
    
    it('应该保留指定字段中的允许HTML标签', () => {
      // 测试数据
      const obj = {
        name: '<script>alert("XSS")</script>John',
        description: '<p>This is a <b>description</b></p>',
        content: '<script>evil()</script><p>Content</p>'
      };
      
      // 调用方法
      const result = sanitizeObject(obj, ['description', 'content']);
      
      // 验证结果
      expect(result).toEqual({
        name: 'John',
        description: '<p>This is a <b>description</b></p>',
        content: '<p>Content</p>'
      });
    });
    
    it('应该处理null和undefined输入', () => {
      // 调用方法
      const result1 = sanitizeObject(null);
      const result2 = sanitizeObject(undefined);
      
      // 验证结果
      expect(result1).toBeNull();
      expect(result2).toBeUndefined();
    });
    
    it('应该处理非对象输入', () => {
      // 调用方法
      const result = sanitizeObject('not an object');
      
      // 验证结果
      expect(result).toBe('not an object');
    });
  });
  
  describe('sanitizeArray', () => {
    it('应该净化数组中的所有字符串元素', () => {
      // 测试数据
      const arr = [
        '<script>alert("XSS")</script>John',
        30,
        '<p>This is a <b>description</b></p>',
        {
          title: '<h1>Title</h1>',
          content: '<script>evil()</script><p>Content</p>'
        }
      ];
      
      // 调用方法
      const result = sanitizeArray(arr);
      
      // 验证结果
      expect(result).toEqual([
        'John',
        30,
        'This is a description',
        {
          title: 'Title',
          content: 'Content'
        }
      ]);
    });
    
    it('应该保留指定字段中的允许HTML标签', () => {
      // 测试数据
      const arr = [
        '<script>alert("XSS")</script>John',
        {
          title: '<h1>Title</h1>',
          description: '<p>This is a <b>description</b></p>',
          content: '<script>evil()</script><p>Content</p>'
        }
      ];
      
      // 调用方法
      const result = sanitizeArray(arr, ['description', 'content']);
      
      // 验证结果
      expect(result).toEqual([
        'John',
        {
          title: 'Title',
          description: '<p>This is a <b>description</b></p>',
          content: '<p>Content</p>'
        }
      ]);
    });
    
    it('应该处理null和undefined输入', () => {
      // 调用方法
      const result1 = sanitizeArray(null);
      const result2 = sanitizeArray(undefined);
      
      // 验证结果
      expect(result1).toBeNull();
      expect(result2).toBeUndefined();
    });
    
    it('应该处理非数组输入', () => {
      // 调用方法
      const result = sanitizeArray('not an array');
      
      // 验证结果
      expect(result).toBe('not an array');
    });
  });
});
