/**
 * API客户端单元测试
 */

// 导入API客户端
import { ApiClient } from '../../utils/api-client/index';
import { CacheManager } from '../../utils/api-client/cache-manager';
import { DataTransformer } from '../../utils/api-client/data-transformer';
import { RequestController } from '../../utils/api-client/request-controller';

// 模拟wx对象
global.wx = {
  request: jest.fn(),
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn()
};

// 模拟AbortController
global.AbortController = class AbortController {
  constructor() {
    this.signal = { aborted: false };
    this.abort = jest.fn(() => {
      this.signal.aborted = true;
    });
  }
};

describe('ApiClient', () => {
  let apiClient;

  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();

    // 创建API客户端实例
    apiClient = new ApiClient({
      baseUrl: 'https://api.example.com/v1',
      timeout: 5000,
      maxRetries: 2
    });
  });

  describe('构造函数', () => {
    test('应该使用默认配置创建实例', () => {
      const client = new ApiClient();
      expect(client).toBeInstanceOf(ApiClient);
      expect(client.config).toBeDefined();
      expect(client.config.baseUrl).toBeDefined();
      expect(client.requestInterceptors).toBeInstanceOf(Array);
      expect(client.responseInterceptors).toBeInstanceOf(Array);
    });

    test('应该使用自定义配置创建实例', () => {
      const config = {
        baseUrl: 'https://custom-api.example.com/v1',
        timeout: 10000,
        maxRetries: 3
      };
      const client = new ApiClient(config);
      expect(client.config.baseUrl).toBe(config.baseUrl);
      expect(client.config.timeout).toBe(config.timeout);
      expect(client.config.maxRetries).toBe(config.maxRetries);
    });
  });

  describe('请求方法', () => {
    beforeEach(() => {
      // 模拟wx.request成功响应
      wx.request.mockImplementation(options => {
        setTimeout(() => {
          options.success({
            statusCode: 200,
            data: {
              success: true,
              data: { id: 1, name: 'Test' },
              message: 'Success'
            }
          });
        }, 10);
        return { abort: jest.fn() };
      });
    });

    test('get方法应该发送GET请求', async () => {
      const result = await apiClient.get('/test', { id: 1 });
      
      expect(wx.request).toHaveBeenCalledTimes(1);
      expect(wx.request.mock.calls[0][0].method).toBe('GET');
      expect(wx.request.mock.calls[0][0].url).toContain('/test');
      expect(result).toEqual({
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success'
      });
    });

    test('post方法应该发送POST请求', async () => {
      const data = { name: 'New Test' };
      const result = await apiClient.post('/test', data);
      
      expect(wx.request).toHaveBeenCalledTimes(1);
      expect(wx.request.mock.calls[0][0].method).toBe('POST');
      expect(wx.request.mock.calls[0][0].url).toContain('/test');
      expect(wx.request.mock.calls[0][0].data).toEqual(data);
      expect(result).toEqual({
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success'
      });
    });

    test('put方法应该发送PUT请求', async () => {
      const data = { id: 1, name: 'Updated Test' };
      const result = await apiClient.put('/test/1', data);
      
      expect(wx.request).toHaveBeenCalledTimes(1);
      expect(wx.request.mock.calls[0][0].method).toBe('PUT');
      expect(wx.request.mock.calls[0][0].url).toContain('/test/1');
      expect(wx.request.mock.calls[0][0].data).toEqual(data);
      expect(result).toEqual({
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success'
      });
    });

    test('delete方法应该发送DELETE请求', async () => {
      const result = await apiClient.delete('/test/1');
      
      expect(wx.request).toHaveBeenCalledTimes(1);
      expect(wx.request.mock.calls[0][0].method).toBe('DELETE');
      expect(wx.request.mock.calls[0][0].url).toContain('/test/1');
      expect(result).toEqual({
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success'
      });
    });

    test('patch方法应该发送PATCH请求', async () => {
      const data = { name: 'Patched Test' };
      const result = await apiClient.patch('/test/1', data);
      
      expect(wx.request).toHaveBeenCalledTimes(1);
      expect(wx.request.mock.calls[0][0].method).toBe('PATCH');
      expect(wx.request.mock.calls[0][0].url).toContain('/test/1');
      expect(wx.request.mock.calls[0][0].data).toEqual(data);
      expect(result).toEqual({
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success'
      });
    });
  });

  describe('错误处理', () => {
    test('应该处理HTTP错误', async () => {
      // 模拟wx.request返回HTTP错误
      wx.request.mockImplementation(options => {
        setTimeout(() => {
          options.fail({
            errMsg: 'request:fail timeout'
          });
        }, 10);
        return { abort: jest.fn() };
      });

      await expect(apiClient.get('/test')).rejects.toThrow();
    });

    test('应该处理业务错误', async () => {
      // 模拟wx.request返回业务错误
      wx.request.mockImplementation(options => {
        setTimeout(() => {
          options.success({
            statusCode: 200,
            data: {
              success: false,
              message: 'Business error',
              code: 'BUSINESS_ERROR'
            }
          });
        }, 10);
        return { abort: jest.fn() };
      });

      await expect(apiClient.get('/test')).rejects.toThrow('Business error');
    });

    test('应该处理HTTP状态码错误', async () => {
      // 模拟wx.request返回HTTP状态码错误
      wx.request.mockImplementation(options => {
        setTimeout(() => {
          options.success({
            statusCode: 404,
            data: {
              message: 'Not Found'
            }
          });
        }, 10);
        return { abort: jest.fn() };
      });

      await expect(apiClient.get('/test')).rejects.toThrow('Not Found');
    });
  });

  describe('请求拦截器', () => {
    test('应该应用请求拦截器', async () => {
      // 添加请求拦截器
      const interceptor = jest.fn(config => {
        config.headers = config.headers || {};
        config.headers['X-Custom-Header'] = 'test-value';
        return config;
      });
      apiClient.requestInterceptors.push(interceptor);

      // 发送请求
      await apiClient.get('/test');

      // 验证拦截器被调用
      expect(interceptor).toHaveBeenCalled();
      // 验证请求头被修改
      expect(wx.request.mock.calls[0][0].header['X-Custom-Header']).toBe('test-value');
    });

    test('应该处理请求拦截器中的错误', async () => {
      // 添加抛出错误的请求拦截器
      const interceptor = jest.fn(() => {
        throw new Error('Interceptor error');
      });
      apiClient.requestInterceptors.push(interceptor);

      // 发送请求，应该抛出错误
      await expect(apiClient.get('/test')).rejects.toThrow('Interceptor error');
    });
  });

  describe('响应拦截器', () => {
    test('应该应用响应拦截器', async () => {
      // 添加响应拦截器
      const interceptor = jest.fn(response => {
        response.intercepted = true;
        return response;
      });
      apiClient.responseInterceptors.push(interceptor);

      // 发送请求
      const result = await apiClient.get('/test');

      // 验证拦截器被调用
      expect(interceptor).toHaveBeenCalled();
      // 验证响应被修改
      expect(result.intercepted).toBe(true);
    });

    test('应该处理响应拦截器中的错误', async () => {
      // 添加抛出错误的响应拦截器
      const interceptor = jest.fn(() => {
        throw new Error('Interceptor error');
      });
      apiClient.responseInterceptors.push(interceptor);

      // 发送请求，应该抛出错误
      await expect(apiClient.get('/test')).rejects.toThrow('Interceptor error');
    });
  });

  describe('缓存机制', () => {
    beforeEach(() => {
      // 模拟缓存管理器
      jest.spyOn(CacheManager.prototype, 'get').mockImplementation(() => null);
      jest.spyOn(CacheManager.prototype, 'set').mockImplementation(() => {});
      jest.spyOn(CacheManager.prototype, 'generateKey').mockImplementation((url, params) => `${url}:${JSON.stringify(params)}`);
    });

    test('应该使用缓存响应GET请求', async () => {
      // 模拟缓存命中
      CacheManager.prototype.get.mockImplementation(() => ({
        success: true,
        data: { id: 1, name: 'Cached Test' },
        message: 'Success (Cached)'
      }));

      // 发送GET请求
      const result = await apiClient.get('/test', { id: 1 }, { useCache: true });

      // 验证缓存被使用
      expect(CacheManager.prototype.get).toHaveBeenCalled();
      expect(wx.request).not.toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        data: { id: 1, name: 'Cached Test' },
        message: 'Success (Cached)'
      });
    });

    test('应该缓存GET请求的响应', async () => {
      // 发送GET请求
      await apiClient.get('/test', { id: 1 }, { useCache: true });

      // 验证响应被缓存
      expect(CacheManager.prototype.set).toHaveBeenCalled();
    });

    test('应该不缓存非GET请求', async () => {
      // 发送POST请求
      await apiClient.post('/test', { name: 'Test' });

      // 验证响应未被缓存
      expect(CacheManager.prototype.set).not.toHaveBeenCalled();
    });

    test('应该清除缓存', () => {
      // 模拟缓存清除
      jest.spyOn(CacheManager.prototype, 'clear').mockImplementation(() => {});

      // 清除缓存
      apiClient.clearCache();

      // 验证缓存被清除
      expect(CacheManager.prototype.clear).toHaveBeenCalled();
    });
  });

  describe('请求取消', () => {
    test('应该支持取消请求', async () => {
      // 创建请求控制器
      const controller = new RequestController();
      
      // 模拟wx.request返回可取消的请求
      const abortMock = jest.fn();
      wx.request.mockReturnValue({ abort: abortMock });

      // 发送请求
      const requestPromise = apiClient.get('/test', {}, { controller });
      
      // 取消请求
      controller.abort();

      // 验证请求被取消
      expect(abortMock).toHaveBeenCalled();
      
      // 请求应该被拒绝
      await expect(requestPromise).rejects.toThrow('Request aborted');
    });
  });

  describe('超时控制', () => {
    test('应该处理请求超时', async () => {
      // 模拟wx.request超时
      jest.useFakeTimers();
      wx.request.mockImplementation(() => ({ abort: jest.fn() }));

      // 发送请求
      const requestPromise = apiClient.get('/test', {}, { timeout: 1000 });
      
      // 前进时间超过超时时间
      jest.advanceTimersByTime(1500);
      
      // 请求应该被拒绝
      await expect(requestPromise).rejects.toThrow('Request timeout');
      
      jest.useRealTimers();
    });
  });

  describe('重试机制', () => {
    test('应该在失败时重试请求', async () => {
      // 模拟wx.request第一次失败，然后成功
      let callCount = 0;
      wx.request.mockImplementation(options => {
        callCount++;
        if (callCount === 1) {
          // 第一次调用失败
          setTimeout(() => {
            options.fail({
              errMsg: 'request:fail timeout'
            });
          }, 10);
        } else {
          // 第二次调用成功
          setTimeout(() => {
            options.success({
              statusCode: 200,
              data: {
                success: true,
                data: { id: 1, name: 'Retry Success' },
                message: 'Success after retry'
              }
            });
          }, 10);
        }
        return { abort: jest.fn() };
      });

      // 发送请求
      const result = await apiClient.get('/test', {}, { maxRetries: 1 });

      // 验证请求被重试
      expect(wx.request).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        success: true,
        data: { id: 1, name: 'Retry Success' },
        message: 'Success after retry'
      });
    });

    test('应该在达到最大重试次数后失败', async () => {
      // 模拟wx.request总是失败
      wx.request.mockImplementation(options => {
        setTimeout(() => {
          options.fail({
            errMsg: 'request:fail timeout'
          });
        }, 10);
        return { abort: jest.fn() };
      });

      // 发送请求
      await expect(apiClient.get('/test', {}, { maxRetries: 2 })).rejects.toThrow();

      // 验证请求被重试指定次数
      expect(wx.request).toHaveBeenCalledTimes(3); // 原始请求 + 2次重试
    });
  });
});

describe('CacheManager', () => {
  let cacheManager;

  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();

    // 创建缓存管理器实例
    cacheManager = new CacheManager({
      defaultTTL: 5000,
      useLocalStorage: true,
      localStoragePrefix: 'test_'
    });
  });

  test('应该生成缓存键', () => {
    const url = '/test';
    const params = { id: 1 };
    const key = cacheManager.generateKey(url, params);
    expect(key).toBe('/test:{"id":1}');
  });

  test('应该设置和获取缓存', () => {
    const key = 'test-key';
    const data = { id: 1, name: 'Test' };
    
    // 设置缓存
    cacheManager.set(key, data);
    
    // 获取缓存
    const cachedData = cacheManager.get(key);
    
    expect(cachedData).toEqual(data);
  });

  test('应该处理过期缓存', () => {
    const key = 'test-key';
    const data = { id: 1, name: 'Test' };
    
    // 设置缓存，TTL为0（立即过期）
    cacheManager.set(key, data, 0);
    
    // 获取缓存
    const cachedData = cacheManager.get(key);
    
    // 缓存应该已过期
    expect(cachedData).toBeNull();
  });

  test('应该清除缓存', () => {
    const key1 = 'test-key-1';
    const key2 = 'test-key-2';
    const data = { id: 1, name: 'Test' };
    
    // 设置缓存
    cacheManager.set(key1, data);
    cacheManager.set(key2, data);
    
    // 清除缓存
    cacheManager.clear();
    
    // 缓存应该已被清除
    expect(cacheManager.get(key1)).toBeNull();
    expect(cacheManager.get(key2)).toBeNull();
  });
});

describe('DataTransformer', () => {
  let dataTransformer;

  beforeEach(() => {
    // 创建数据转换器实例
    dataTransformer = new DataTransformer();
  });

  test('应该将snake_case转换为camelCase', () => {
    const snakeData = {
      user_id: 1,
      first_name: 'John',
      last_name: 'Doe',
      address: {
        street_name: 'Main St',
        zip_code: '12345'
      },
      phone_numbers: [
        { phone_type: 'home', phone_number: '************' },
        { phone_type: 'work', phone_number: '************' }
      ]
    };

    const camelData = dataTransformer.snakeToCamel(snakeData);

    expect(camelData).toEqual({
      userId: 1,
      firstName: 'John',
      lastName: 'Doe',
      address: {
        streetName: 'Main St',
        zipCode: '12345'
      },
      phoneNumbers: [
        { phoneType: 'home', phoneNumber: '************' },
        { phoneType: 'work', phoneNumber: '************' }
      ]
    });
  });

  test('应该将camelCase转换为snake_case', () => {
    const camelData = {
      userId: 1,
      firstName: 'John',
      lastName: 'Doe',
      address: {
        streetName: 'Main St',
        zipCode: '12345'
      },
      phoneNumbers: [
        { phoneType: 'home', phoneNumber: '************' },
        { phoneType: 'work', phoneNumber: '************' }
      ]
    };

    const snakeData = dataTransformer.camelToSnake(camelData);

    expect(snakeData).toEqual({
      user_id: 1,
      first_name: 'John',
      last_name: 'Doe',
      address: {
        street_name: 'Main St',
        zip_code: '12345'
      },
      phone_numbers: [
        { phone_type: 'home', phone_number: '************' },
        { phone_type: 'work', phone_number: '************' }
      ]
    });
  });

  test('应该处理null和undefined值', () => {
    expect(dataTransformer.snakeToCamel(null)).toBeNull();
    expect(dataTransformer.snakeToCamel(undefined)).toBeUndefined();
    expect(dataTransformer.camelToSnake(null)).toBeNull();
    expect(dataTransformer.camelToSnake(undefined)).toBeUndefined();
  });

  test('应该处理非对象值', () => {
    expect(dataTransformer.snakeToCamel('test_string')).toBe('test_string');
    expect(dataTransformer.snakeToCamel(123)).toBe(123);
    expect(dataTransformer.camelToSnake('testString')).toBe('testString');
    expect(dataTransformer.camelToSnake(123)).toBe(123);
  });
});

describe('RequestController', () => {
  let controller;

  beforeEach(() => {
    // 创建请求控制器实例
    controller = new RequestController();
  });

  test('应该创建带有信号的实例', () => {
    expect(controller).toBeInstanceOf(RequestController);
    expect(controller.signal).toBeDefined();
    expect(controller.signal.aborted).toBe(false);
  });

  test('应该能够中止请求', () => {
    controller.abort();
    expect(controller.signal.aborted).toBe(true);
  });

  test('应该能够创建超时控制器', () => {
    jest.useFakeTimers();
    
    const timeoutController = RequestController.timeout(1000);
    expect(timeoutController).toBeInstanceOf(RequestController);
    expect(timeoutController.signal.aborted).toBe(false);
    
    // 前进时间超过超时时间
    jest.advanceTimersByTime(1500);
    
    expect(timeoutController.signal.aborted).toBe(true);
    
    jest.useRealTimers();
  });
});
