/**
 * 认证服务单元测试
 */
/* eslint-env jest */

const authService = require('../../utils/auth-service');
const tokenManager = require('../../utils/token-manager');

// 模拟wx对象
global.wx = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  login: jest.fn(),
  getUserProfile: jest.fn()
};

// 模拟API客户端
jest.mock('../../utils/api-client/index', () => ({
  default: {
    auth: {
      login: jest.fn(),
      loginWithPhone: jest.fn(),
      registerWithPhone: jest.fn(),
      logout: jest.fn(),
      getCurrentUser: jest.fn()
    }
  }
}));

// 模拟令牌管理器
jest.mock('../../utils/token-manager', () => ({
  saveTokens: jest.fn(),
  getToken: jest.fn(),
  clearTokens: jest.fn(),
  isLoggedIn: jest.fn()
}));

describe('AuthService', () => {
  beforeEach(() => {
    // 清除所有模拟函数的调用记录
    jest.clearAllMocks();
  });

  describe('loginWithWechat', () => {
    it('当微信登录成功时应该返回成功结果', async () => {
      // 模拟wx.login成功
      wx.login.mockImplementation(({ success }) => {
        success({ code: 'test-code' });
      });

      // 模拟wx.getUserProfile成功
      wx.getUserProfile.mockImplementation(({ success }) => {
        success({ userInfo: { nickName: 'Test User' } });
      });

      // 模拟API调用成功
      const api = require('../../utils/api-client/index').default;
      api.auth.login.mockResolvedValue({
        success: true,
        data: {
          token: 'test-token',
          refreshToken: 'test-refresh-token',
          expiresIn: 3600,
          userId: 'test-user-id',
          isNewUser: false
        }
      });

      // 模拟getCurrentUser成功
      jest.spyOn(authService, 'getCurrentUser').mockResolvedValue({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });

      const result = await authService.loginWithWechat({ getUserInfo: true });

      expect(result.success).toBe(true);
      expect(result.isNewUser).toBe(false);
      expect(result.userInfo).toEqual({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });
      expect(tokenManager.saveTokens).toHaveBeenCalledWith({
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 3600,
        userId: 'test-user-id'
      });
    });

    it('当微信登录失败时应该返回失败结果', async () => {
      // 模拟wx.login失败
      wx.login.mockImplementation(({ fail }) => {
        fail(new Error('Login failed'));
      });

      const result = await authService.loginWithWechat();

      expect(result.success).toBe(false);
      expect(result.error).toBeTruthy();
      expect(tokenManager.saveTokens).not.toHaveBeenCalled();
    });
  });

  describe('loginWithPhone', () => {
    it('当手机号登录成功时应该返回成功结果', async () => {
      // 模拟API调用成功
      const api = require('../../utils/api-client/index').default;
      api.auth.loginWithPhone.mockResolvedValue({
        success: true,
        data: {
          token: 'test-token',
          refreshToken: 'test-refresh-token',
          expiresIn: 3600,
          userId: 'test-user-id'
        }
      });

      // 模拟getCurrentUser成功
      jest.spyOn(authService, 'getCurrentUser').mockResolvedValue({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });

      const result = await authService.loginWithPhone('13800138000', 'password');

      expect(result.success).toBe(true);
      expect(result.userInfo).toEqual({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });
      expect(tokenManager.saveTokens).toHaveBeenCalledWith({
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 3600,
        userId: 'test-user-id'
      });
    });

    it('当手机号或密码为空时应该返回失败结果', async () => {
      const result = await authService.loginWithPhone('', '');

      expect(result.success).toBe(false);
      expect(result.error).toBeTruthy();
      expect(tokenManager.saveTokens).not.toHaveBeenCalled();
    });
  });

  describe('registerWithPhone', () => {
    it('当手机号注册成功时应该返回成功结果', async () => {
      // 模拟API调用成功
      const api = require('../../utils/api-client/index').default;
      api.auth.registerWithPhone.mockResolvedValue({
        success: true,
        data: {
          token: 'test-token',
          refreshToken: 'test-refresh-token',
          expiresIn: 3600,
          userId: 'test-user-id'
        }
      });

      // 模拟getCurrentUser成功
      jest.spyOn(authService, 'getCurrentUser').mockResolvedValue({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });

      const result = await authService.registerWithPhone('13800138000', 'password', 'password', 'Test User');

      expect(result.success).toBe(true);
      expect(result.userInfo).toEqual({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });
      expect(tokenManager.saveTokens).toHaveBeenCalledWith({
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 3600,
        userId: 'test-user-id'
      });
    });

    it('当两次输入的密码不一致时应该返回失败结果', async () => {
      const result = await authService.registerWithPhone('13800138000', 'password1', 'password2');

      expect(result.success).toBe(false);
      expect(result.error).toBe('两次输入的密码不一致');
      expect(tokenManager.saveTokens).not.toHaveBeenCalled();
    });
  });

  describe('logout', () => {
    it('当登出成功时应该清除令牌和用户信息', async () => {
      // 模拟API调用成功
      const api = require('../../utils/api-client/index').default;
      api.auth.logout.mockResolvedValue({ success: true });

      const result = await authService.logout();

      expect(result).toBe(true);
      expect(tokenManager.clearTokens).toHaveBeenCalled();
      expect(wx.removeStorageSync).toHaveBeenCalledWith('userInfo');
    });

    it('当API调用失败时也应该清除本地令牌', async () => {
      // 模拟API调用失败
      const api = require('../../utils/api-client/index').default;
      api.auth.logout.mockRejectedValue(new Error('Logout failed'));

      const result = await authService.logout();

      expect(result).toBe(false);
      expect(tokenManager.clearTokens).toHaveBeenCalled();
      expect(wx.removeStorageSync).toHaveBeenCalledWith('userInfo');
    });
  });

  describe('getCurrentUser', () => {
    it('当用户已登录且有缓存时应该返回缓存的用户信息', async () => {
      // 模拟已登录
      tokenManager.isLoggedIn.mockResolvedValue(true);

      // 模拟有缓存的用户信息
      wx.getStorageSync.mockReturnValue({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });

      const userInfo = await authService.getCurrentUser(false);

      expect(userInfo).toEqual({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });
      expect(wx.getStorageSync).toHaveBeenCalledWith('userInfo');
    });

    it('当强制刷新时应该调用API获取用户信息', async () => {
      // 模拟已登录
      tokenManager.isLoggedIn.mockResolvedValue(true);

      // 模拟API调用成功
      const api = require('../../utils/api-client/index').default;
      api.auth.getCurrentUser.mockResolvedValue({
        data: {
          nickName: 'Test User',
          avatarUrl: 'test-avatar-url'
        }
      });

      const userInfo = await authService.getCurrentUser(true);

      expect(userInfo).toEqual({
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });
      expect(api.auth.getCurrentUser).toHaveBeenCalled();
      expect(wx.setStorageSync).toHaveBeenCalledWith('userInfo', {
        nickName: 'Test User',
        avatarUrl: 'test-avatar-url'
      });
    });

    it('当用户未登录时应该返回null', async () => {
      // 模拟未登录
      tokenManager.isLoggedIn.mockResolvedValue(false);

      const userInfo = await authService.getCurrentUser();

      expect(userInfo).toBeNull();
    });
  });
});
