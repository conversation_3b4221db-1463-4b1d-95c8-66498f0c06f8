/**
 * 权限服务单元测试
 */
/* eslint-env jest */

const permissionService = require('../../utils/permission-service');
const authService = require('../../utils/auth-service');

// 模拟认证服务
jest.mock('../../utils/auth-service', () => ({
  isLoggedIn: jest.fn(),
  getCurrentUser: jest.fn()
}));

describe('PermissionService', () => {
  beforeEach(() => {
    // 清除所有模拟函数的调用记录
    jest.clearAllMocks();
    // 清除权限缓存
    permissionService.clearCache();
  });

  describe('getUserRoles', () => {
    it('当用户未登录时应该返回访客角色', async () => {
      // 模拟未登录
      authService.isLoggedIn.mockResolvedValue(false);

      const roles = await permissionService.getUserRoles();

      expect(roles).toEqual([permissionService.ROLES.GUEST]);
      expect(authService.isLoggedIn).toHaveBeenCalled();
      expect(authService.getCurrentUser).not.toHaveBeenCalled();
    });

    it('当用户已登录但无法获取用户信息时应该返回访客角色', async () => {
      // 模拟已登录但无法获取用户信息
      authService.isLoggedIn.mockResolvedValue(true);
      authService.getCurrentUser.mockResolvedValue(null);

      const roles = await permissionService.getUserRoles();

      expect(roles).toEqual([permissionService.ROLES.GUEST]);
      expect(authService.isLoggedIn).toHaveBeenCalled();
      expect(authService.getCurrentUser).toHaveBeenCalled();
    });

    it('当用户已登录且有角色信息时应该返回用户角色', async () => {
      // 模拟已登录且有角色信息
      authService.isLoggedIn.mockResolvedValue(true);
      authService.getCurrentUser.mockResolvedValue({
        roles: [permissionService.ROLES.USER, permissionService.ROLES.PREMIUM_USER]
      });

      const roles = await permissionService.getUserRoles();

      expect(roles).toEqual([permissionService.ROLES.USER, permissionService.ROLES.PREMIUM_USER]);
      expect(authService.isLoggedIn).toHaveBeenCalled();
      expect(authService.getCurrentUser).toHaveBeenCalled();
    });

    it('当用户已登录但没有角色信息时应该返回默认用户角色', async () => {
      // 模拟已登录但没有角色信息
      authService.isLoggedIn.mockResolvedValue(true);
      authService.getCurrentUser.mockResolvedValue({});

      const roles = await permissionService.getUserRoles();

      expect(roles).toEqual([permissionService.ROLES.USER]);
      expect(authService.isLoggedIn).toHaveBeenCalled();
      expect(authService.getCurrentUser).toHaveBeenCalled();
    });
  });

  describe('getUserPermissions', () => {
    it('当用户是访客时应该返回访客权限', async () => {
      // 模拟用户是访客
      jest.spyOn(permissionService, 'getUserRoles').mockResolvedValue([permissionService.ROLES.GUEST]);

      const permissions = await permissionService.getUserPermissions();

      expect(permissions).toContain(permissionService.PERMISSIONS.CONTENT.VIEW);
      expect(permissions.length).toBe(1);
      expect(permissionService.getUserRoles).toHaveBeenCalled();
    });

    it('当用户有多个角色时应该返回所有角色的权限', async () => {
      // 模拟用户有多个角色
      jest.spyOn(permissionService, 'getUserRoles').mockResolvedValue([
        permissionService.ROLES.USER,
        permissionService.ROLES.PREMIUM_USER
      ]);

      const permissions = await permissionService.getUserPermissions();

      // 检查是否包含USER角色的权限
      expect(permissions).toContain(permissionService.PERMISSIONS.CONTENT.VIEW);
      expect(permissions).toContain(permissionService.PERMISSIONS.USER.VIEW_PROFILE);

      // 检查是否包含PREMIUM_USER角色的权限
      expect(permissions).toContain(permissionService.PERMISSIONS.LEARNING_PLAN.SHARE);

      // 确保没有重复权限
      const uniquePermissions = new Set(permissions);
      expect(permissions.length).toBe(uniquePermissions.size);

      expect(permissionService.getUserRoles).toHaveBeenCalled();
    });
  });

  describe('hasPermission', () => {
    it('当用户有指定权限时应该返回true', async () => {
      // 模拟用户有指定权限
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue([
        permissionService.PERMISSIONS.CONTENT.VIEW,
        permissionService.PERMISSIONS.CONTENT.SHARE
      ]);

      const result = await permissionService.hasPermission(permissionService.PERMISSIONS.CONTENT.VIEW);

      expect(result).toBe(true);
      expect(permissionService.getUserPermissions).toHaveBeenCalled();
    });

    it('当用户没有指定权限时应该返回false', async () => {
      // 模拟用户没有指定权限
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue([
        permissionService.PERMISSIONS.CONTENT.VIEW
      ]);

      const result = await permissionService.hasPermission(permissionService.PERMISSIONS.CONTENT.EDIT);

      expect(result).toBe(false);
      expect(permissionService.getUserPermissions).toHaveBeenCalled();
    });

    it('当检查多个权限且requireAll为true时，用户必须拥有所有权限', async () => {
      // 模拟用户有部分权限
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue([
        permissionService.PERMISSIONS.CONTENT.VIEW,
        permissionService.PERMISSIONS.CONTENT.SHARE
      ]);

      const result = await permissionService.hasPermission([
        permissionService.PERMISSIONS.CONTENT.VIEW,
        permissionService.PERMISSIONS.CONTENT.EDIT
      ], true);

      expect(result).toBe(false);
      expect(permissionService.getUserPermissions).toHaveBeenCalled();
    });

    it('当检查多个权限且requireAll为false时，用户只需拥有一个权限', async () => {
      // 模拟用户有部分权限
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue([
        permissionService.PERMISSIONS.CONTENT.VIEW,
        permissionService.PERMISSIONS.CONTENT.SHARE
      ]);

      const result = await permissionService.hasPermission([
        permissionService.PERMISSIONS.CONTENT.VIEW,
        permissionService.PERMISSIONS.CONTENT.EDIT
      ], false);

      expect(result).toBe(true);
      expect(permissionService.getUserPermissions).toHaveBeenCalled();
    });
  });

  describe('hasRole', () => {
    it('当用户有指定角色时应该返回true', async () => {
      // 模拟用户有指定角色
      jest.spyOn(permissionService, 'getUserRoles').mockResolvedValue([
        permissionService.ROLES.USER,
        permissionService.ROLES.PREMIUM_USER
      ]);

      const result = await permissionService.hasRole(permissionService.ROLES.USER);

      expect(result).toBe(true);
      expect(permissionService.getUserRoles).toHaveBeenCalled();
    });

    it('当用户没有指定角色时应该返回false', async () => {
      // 模拟用户没有指定角色
      jest.spyOn(permissionService, 'getUserRoles').mockResolvedValue([
        permissionService.ROLES.USER
      ]);

      const result = await permissionService.hasRole(permissionService.ROLES.ADMIN);

      expect(result).toBe(false);
      expect(permissionService.getUserRoles).toHaveBeenCalled();
    });

    it('当检查多个角色且requireAll为true时，用户必须拥有所有角色', async () => {
      // 模拟用户有部分角色
      jest.spyOn(permissionService, 'getUserRoles').mockResolvedValue([
        permissionService.ROLES.USER,
        permissionService.ROLES.PREMIUM_USER
      ]);

      const result = await permissionService.hasRole([
        permissionService.ROLES.USER,
        permissionService.ROLES.ADMIN
      ], true);

      expect(result).toBe(false);
      expect(permissionService.getUserRoles).toHaveBeenCalled();
    });

    it('当检查多个角色且requireAll为false时，用户只需拥有一个角色', async () => {
      // 模拟用户有部分角色
      jest.spyOn(permissionService, 'getUserRoles').mockResolvedValue([
        permissionService.ROLES.USER,
        permissionService.ROLES.PREMIUM_USER
      ]);

      const result = await permissionService.hasRole([
        permissionService.ROLES.USER,
        permissionService.ROLES.ADMIN
      ], false);

      expect(result).toBe(true);
      expect(permissionService.getUserRoles).toHaveBeenCalled();
    });
  });
});
