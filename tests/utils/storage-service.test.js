/**
 * 本地存储服务测试
 */

// 导入存储服务
import { StorageService } from '../../utils/storage-service';

// 模拟 wx API
const mockWx = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  clearStorageSync: jest.fn(),
  getStorageInfoSync: jest.fn()
};

// 保存原始 wx 对象
const originalWx = global.wx;

describe('StorageService', () => {
  let storage;

  beforeEach(() => {
    // 替换全局 wx 对象为模拟对象
    global.wx = mockWx;

    // 重置所有模拟函数
    jest.clearAllMocks();

    // 创建存储服务实例
    storage = new StorageService({
      prefix: 'test',
      defaultExpiry: 1000, // 1秒
      enableLogging: false
    });
  });

  afterEach(() => {
    // 恢复原始 wx 对象
    global.wx = originalWx;
  });

  describe('set', () => {
    it('应该正确设置存储项', () => {
      // 安排
      const key = 'testKey';
      const data = { foo: 'bar' };
      const now = Date.now();
      Date.now = jest.fn(() => now);

      // 执行
      const result = storage.set(key, data);

      // 断言
      expect(result).toBe(true);
      expect(mockWx.setStorageSync).toHaveBeenCalledWith(
        'test_testKey',
        JSON.stringify({
          data,
          expireAt: now + 1000
        })
      );
    });

    it('当键名无效时应该返回false', () => {
      // 执行
      const result = storage.set('', { foo: 'bar' });

      // 断言
      expect(result).toBe(false);
      expect(mockWx.setStorageSync).not.toHaveBeenCalled();
    });

    it('当设置存储抛出异常时应该返回false', () => {
      // 安排
      mockWx.setStorageSync.mockImplementation(() => {
        throw new Error('模拟错误');
      });

      // 执行
      const result = storage.set('testKey', { foo: 'bar' });

      // 断言
      expect(result).toBe(false);
    });
  });

  describe('get', () => {
    it('应该正确获取存储项', () => {
      // 安排
      const key = 'testKey';
      const data = { foo: 'bar' };
      const storageItem = {
        data,
        expireAt: Date.now() + 1000
      };
      mockWx.getStorageSync.mockReturnValue(JSON.stringify(storageItem));

      // 执行
      const result = storage.get(key);

      // 断言
      expect(result).toEqual(data);
      expect(mockWx.getStorageSync).toHaveBeenCalledWith('test_testKey');
    });

    it('当存储项不存在时应该返回默认值', () => {
      // 安排
      mockWx.getStorageSync.mockReturnValue(null);

      // 执行
      const result = storage.get('testKey', 'defaultValue');

      // 断言
      expect(result).toBe('defaultValue');
    });

    it('当存储项已过期时应该返回默认值并移除该项', () => {
      // 安排
      const key = 'testKey';
      const storageItem = {
        data: { foo: 'bar' },
        expireAt: Date.now() - 1000 // 已过期
      };
      mockWx.getStorageSync.mockReturnValue(JSON.stringify(storageItem));

      // 执行
      const result = storage.get(key, 'defaultValue');

      // 断言
      expect(result).toBe('defaultValue');
      expect(mockWx.removeStorageSync).toHaveBeenCalledWith('test_testKey');
    });
  });

  describe('remove', () => {
    it('应该正确移除存储项', () => {
      // 安排
      const key = 'testKey';

      // 执行
      const result = storage.remove(key);

      // 断言
      expect(result).toBe(true);
      expect(mockWx.removeStorageSync).toHaveBeenCalledWith('test_testKey');
    });
  });

  describe('clear', () => {
    it('应该只清除带前缀的存储项', () => {
      // 安排
      mockWx.getStorageInfoSync.mockReturnValue({
        keys: ['test_key1', 'test_key2', 'other_key']
      });

      // 执行
      const result = storage.clear(true);

      // 断言
      expect(result).toBe(true);
      expect(mockWx.removeStorageSync).toHaveBeenCalledTimes(2);
      expect(mockWx.removeStorageSync).toHaveBeenCalledWith('test_key1');
      expect(mockWx.removeStorageSync).toHaveBeenCalledWith('test_key2');
    });

    it('应该清除所有存储项', () => {
      // 执行
      const result = storage.clear(false);

      // 断言
      expect(result).toBe(true);
      expect(mockWx.clearStorageSync).toHaveBeenCalled();
    });
  });

  describe('cleanExpired', () => {
    it('应该清理过期的存储项', () => {
      // 安排
      mockWx.getStorageInfoSync.mockReturnValue({
        keys: ['test_key1', 'test_key2', 'other_key']
      });

      // 模拟第一个项已过期，第二个项未过期
      mockWx.getStorageSync.mockImplementation((key) => {
        if (key === 'test_key1') {
          return JSON.stringify({
            data: 'value1',
            expireAt: Date.now() - 1000 // 已过期
          });
        } else if (key === 'test_key2') {
          return JSON.stringify({
            data: 'value2',
            expireAt: Date.now() + 1000 // 未过期
          });
        }
        return null;
      });

      // 执行
      const result = storage.cleanExpired();

      // 断言
      expect(result).toBe(1); // 清理了1个项
      expect(mockWx.removeStorageSync).toHaveBeenCalledTimes(1);
      expect(mockWx.removeStorageSync).toHaveBeenCalledWith('test_key1');
    });
  });
});
