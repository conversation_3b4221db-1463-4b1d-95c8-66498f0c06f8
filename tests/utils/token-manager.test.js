/**
 * 令牌管理器单元测试
 */
/* eslint-env jest */

const tokenManager = require('../../utils/token-manager');

// 模拟wx对象
global.wx = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn()
};

// 模拟API客户端
jest.mock('../../utils/api-client/index', () => ({
  auth: {
    refreshToken: jest.fn()
  }
}));

describe('TokenManager', () => {
  beforeEach(() => {
    // 清除所有模拟函数的调用记录
    jest.clearAllMocks();
  });

  describe('saveTokens', () => {
    it('应该正确保存令牌信息', () => {
      const tokenData = {
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 3600,
        userId: 'test-user-id'
      };

      const result = tokenManager.saveTokens(tokenData);

      expect(result).toBe(true);
      expect(wx.setStorageSync).toHaveBeenCalledTimes(4);
      expect(wx.setStorageSync).toHaveBeenCalledWith('token', 'test-token');
      expect(wx.setStorageSync).toHaveBeenCalledWith('refreshToken', 'test-refresh-token');
      expect(wx.setStorageSync).toHaveBeenCalledWith('userId', 'test-user-id');
      // 不检查过期时间，因为它是基于当前时间计算的
    });

    it('当令牌数据不完整时应该返回false', () => {
      const result = tokenManager.saveTokens({});
      expect(result).toBe(false);
      expect(wx.setStorageSync).not.toHaveBeenCalled();
    });
  });

  describe('getToken', () => {
    it('当令牌不存在时应该返回null', async () => {
      wx.getStorageSync.mockReturnValue(null);

      const token = await tokenManager.getToken();

      expect(token).toBeNull();
      expect(wx.getStorageSync).toHaveBeenCalledWith('token');
    });

    it('当令牌存在且未过期时应该返回令牌', async () => {
      const now = Date.now();
      const future = now + 1000000; // 未来的时间

      wx.getStorageSync.mockImplementation(key => {
        if (key === 'token') return 'test-token';
        if (key === 'tokenExpiry') return future;
        return null;
      });

      const token = await tokenManager.getToken();

      expect(token).toBe('test-token');
      expect(wx.getStorageSync).toHaveBeenCalledWith('token');
      expect(wx.getStorageSync).toHaveBeenCalledWith('tokenExpiry');
    });

    it('当令牌已过期时应该返回null', async () => {
      const now = Date.now();
      const past = now - 1000000; // 过去的时间

      wx.getStorageSync.mockImplementation(key => {
        if (key === 'token') return 'test-token';
        if (key === 'tokenExpiry') return past;
        return null;
      });

      const token = await tokenManager.getToken(false); // 不自动刷新

      expect(token).toBeNull();
      expect(wx.getStorageSync).toHaveBeenCalledWith('token');
      expect(wx.getStorageSync).toHaveBeenCalledWith('tokenExpiry');
    });
  });

  describe('clearTokens', () => {
    it('应该清除所有令牌信息', () => {
      tokenManager.clearTokens();

      expect(wx.removeStorageSync).toHaveBeenCalledTimes(4);
      expect(wx.removeStorageSync).toHaveBeenCalledWith('token');
      expect(wx.removeStorageSync).toHaveBeenCalledWith('tokenExpiry');
      expect(wx.removeStorageSync).toHaveBeenCalledWith('refreshToken');
      expect(wx.removeStorageSync).toHaveBeenCalledWith('userId');
    });
  });

  describe('isLoggedIn', () => {
    it('当令牌存在且有效时应该返回true', async () => {
      // 模拟getToken返回有效令牌
      jest.spyOn(tokenManager, 'getToken').mockResolvedValue('test-token');

      const isLoggedIn = await tokenManager.isLoggedIn();

      expect(isLoggedIn).toBe(true);
      expect(tokenManager.getToken).toHaveBeenCalled();
    });

    it('当令牌不存在或无效时应该返回false', async () => {
      // 模拟getToken返回null
      jest.spyOn(tokenManager, 'getToken').mockResolvedValue(null);

      const isLoggedIn = await tokenManager.isLoggedIn();

      expect(isLoggedIn).toBe(false);
      expect(tokenManager.getToken).toHaveBeenCalled();
    });
  });
});
