#!/usr/bin/env node

/**
 * AIBUBB文档治理工具包
 * 实现基于价值和版本的文档管理和自动化流程
 */

const fs = require("fs");
const path = require("path");
const yaml = require("js-yaml");

/**
 * 文档版本管理器
 */
class DocumentVersionManager {
  constructor() {
    this.versionCache = new Map();
  }

  // 解析版本号
  parseVersion(versionString) {
    if (this.versionCache.has(versionString)) {
      return this.versionCache.get(versionString);
    }

    const regex =
      /^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9.-]+))?(?:\+([a-zA-Z0-9.-]+))?$/;
    const match = versionString.match(regex);

    if (!match) {
      throw new Error(`Invalid version format: ${versionString}`);
    }

    const parsed = {
      major: parseInt(match[1]),
      minor: parseInt(match[2]),
      patch: parseInt(match[3]),
      prerelease: match[4] || null,
      build: match[5] || null,
      raw: versionString,
    };

    this.versionCache.set(versionString, parsed);
    return parsed;
  }

  // 比较版本号
  compareVersions(version1, version2) {
    const v1 = this.parseVersion(version1);
    const v2 = this.parseVersion(version2);

    // 比较主版本号
    if (v1.major !== v2.major) {
      return v1.major - v2.major;
    }

    // 比较次版本号
    if (v1.minor !== v2.minor) {
      return v1.minor - v2.minor;
    }

    // 比较修订版本号
    if (v1.patch !== v2.patch) {
      return v1.patch - v2.patch;
    }

    // 比较预发布版本
    return this.comparePrerelease(v1.prerelease, v2.prerelease);
  }

  comparePrerelease(pre1, pre2) {
    // 正式版本 > 预发布版本
    if (!pre1 && pre2) return 1;
    if (pre1 && !pre2) return -1;
    if (!pre1 && !pre2) return 0;

    // 比较预发布标识
    const order = { dev: 1, alpha: 2, beta: 3, rc: 4 };
    const p1 = pre1.split(".")[0];
    const p2 = pre2.split(".")[0];

    if (order[p1] && order[p2]) {
      return order[p1] - order[p2];
    }

    return pre1.localeCompare(pre2);
  }

  // 检查版本兼容性
  isCompatible(currentVersion, requiredVersionRange) {
    try {
      const current = this.parseVersion(currentVersion);

      // 简单的版本范围匹配
      if (requiredVersionRange.includes(">=")) {
        const minVersion = requiredVersionRange.replace(">=", "").trim();
        return this.compareVersions(currentVersion, minVersion) >= 0;
      }

      if (requiredVersionRange.includes("<=")) {
        const maxVersion = requiredVersionRange.replace("<=", "").trim();
        return this.compareVersions(currentVersion, maxVersion) <= 0;
      }

      if (requiredVersionRange.includes("^")) {
        const baseVersion = requiredVersionRange.replace("^", "").trim();
        const base = this.parseVersion(baseVersion);
        return (
          current.major === base.major &&
          this.compareVersions(currentVersion, baseVersion) >= 0
        );
      }

      if (requiredVersionRange.includes("~")) {
        const baseVersion = requiredVersionRange.replace("~", "").trim();
        const base = this.parseVersion(baseVersion);
        return (
          current.major === base.major &&
          current.minor === base.minor &&
          this.compareVersions(currentVersion, baseVersion) >= 0
        );
      }

      if (requiredVersionRange.includes(".x")) {
        const pattern = requiredVersionRange.replace(".x", "");
        return currentVersion.startsWith(pattern);
      }

      // 精确匹配
      return currentVersion === requiredVersionRange;
    } catch (error) {
      return false;
    }
  }

  // 查找最新版本
  findLatestVersion(versions) {
    const validVersions = versions.filter((v) => {
      try {
        const parsed = this.parseVersion(v);
        return !parsed.prerelease; // 排除预发布版本
      } catch {
        return false;
      }
    });

    if (validVersions.length === 0) return null;

    return validVersions.sort((a, b) => this.compareVersions(b, a))[0];
  }

  // 提取文档版本元数据
  extractVersionMetadata(content) {
    try {
      // 查找YAML front matter
      const yamlMatch = content.match(/^---\n([\s\S]*?)\n---/);
      if (yamlMatch) {
        const metadata = yaml.load(yamlMatch[1]);
        return {
          document_version: metadata.document_version || "0.0.0",
          document_status: metadata.document_status || "unknown",
          created_date: metadata.created_date,
          last_updated: metadata.last_updated,
          author: metadata.author,
          maintainer: metadata.maintainer,
          replaces: metadata.replaces || [],
          depends_on: metadata.depends_on || [],
          compatible_with: metadata.compatible_with || [],
        };
      }

      // 查找文档底部的版本信息
      const bottomMatch = content.match(/\*\*文档版本\*\*:\s*([^\n]+)/);
      if (bottomMatch) {
        return {
          document_version: bottomMatch[1].trim(),
          document_status: "unknown",
          replaces: [],
          depends_on: [],
          compatible_with: [],
        };
      }

      return {
        document_version: "0.0.0",
        document_status: "unknown",
        replaces: [],
        depends_on: [],
        compatible_with: [],
      };
    } catch (error) {
      return {
        document_version: "0.0.0",
        document_status: "unknown",
        replaces: [],
        depends_on: [],
        compatible_with: [],
      };
    }
  }

  // 检测版本冲突
  detectVersionConflicts(documents) {
    const conflicts = [];
    const versionMap = new Map();

    for (const doc of documents) {
      const content = fs.readFileSync(doc.path, "utf8");
      const metadata = this.extractVersionMetadata(content);

      // 检查版本号格式
      try {
        this.parseVersion(metadata.document_version);
      } catch (error) {
        conflicts.push({
          document: doc.path,
          type: "invalid_version_format",
          version: metadata.document_version,
          error: error.message,
        });
        continue;
      }

      // 检查版本冲突
      const versionKey = `${path.basename(doc.path, ".md")}-${
        metadata.document_version
      }`;
      if (versionMap.has(versionKey)) {
        conflicts.push({
          document: doc.path,
          type: "version_collision",
          version: metadata.document_version,
          conflictsWith: versionMap.get(versionKey),
        });
      } else {
        versionMap.set(versionKey, doc.path);
      }

      // 检查依赖版本是否存在
      for (const dep of metadata.depends_on) {
        const depExists = documents.some((d) => {
          const depContent = fs.readFileSync(d.path, "utf8");
          const depMetadata = this.extractVersionMetadata(depContent);
          return this.isCompatible(depMetadata.document_version, dep);
        });

        if (!depExists) {
          conflicts.push({
            document: doc.path,
            type: "missing_dependency",
            missing: dep,
            version: metadata.document_version,
          });
        }
      }
    }

    return conflicts;
  }

  // 查找文档的所有版本
  findDocumentVersions(documents, documentName) {
    const versions = [];

    for (const doc of documents) {
      const baseName = path.basename(doc.path, ".md");
      if (baseName.startsWith(documentName) || baseName === documentName) {
        const content = fs.readFileSync(doc.path, "utf8");
        const metadata = this.extractVersionMetadata(content);

        versions.push({
          path: doc.path,
          version: metadata.document_version,
          status: metadata.document_status,
          metadata: metadata,
        });
      }
    }

    return versions.sort((a, b) => this.compareVersions(b.version, a.version));
  }
}

/**
 * 版本关系分析器
 */
class DocumentVersionRelationshipAnalyzer {
  constructor(versionManager) {
    this.versionManager = versionManager;
  }

  // 构建版本依赖图
  buildDependencyGraph(documents) {
    const graph = new Map();

    for (const doc of documents) {
      const content = fs.readFileSync(doc.path, "utf8");
      const metadata = this.versionManager.extractVersionMetadata(content);
      const nodeId = `${path.basename(doc.path, ".md")}-${
        metadata.document_version
      }`;

      graph.set(nodeId, {
        document: doc,
        metadata: metadata,
        dependencies: metadata.depends_on || [],
        dependents: [],
        replaces: metadata.replaces || [],
        compatibleWith: metadata.compatible_with || [],
      });
    }

    // 建立依赖关系
    for (const [nodeId, node] of graph) {
      for (const dep of node.dependencies) {
        const depNodes = this.findCompatibleNodes(graph, dep);
        for (const depNode of depNodes) {
          depNode.dependents.push(nodeId);
        }
      }
    }

    return graph;
  }

  findCompatibleNodes(graph, versionRange) {
    const compatibleNodes = [];

    for (const [nodeId, node] of graph) {
      if (
        this.versionManager.isCompatible(
          node.metadata.document_version,
          versionRange
        )
      ) {
        compatibleNodes.push(node);
      }
    }

    return compatibleNodes;
  }

  // 检测循环依赖
  detectCircularDependencies(dependencyGraph) {
    const visited = new Set();
    const recursionStack = new Set();
    const cycles = [];

    for (const nodeId of dependencyGraph.keys()) {
      if (!visited.has(nodeId)) {
        this.dfsDetectCycle(
          dependencyGraph,
          nodeId,
          visited,
          recursionStack,
          cycles,
          []
        );
      }
    }

    return cycles;
  }

  dfsDetectCycle(graph, nodeId, visited, recursionStack, cycles, path) {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    path.push(nodeId);

    const node = graph.get(nodeId);
    if (node) {
      for (const dep of node.dependencies) {
        const depNodes = this.findCompatibleNodes(graph, dep);

        for (const depNode of depNodes) {
          const depNodeId = `${path.basename(depNode.document.path, ".md")}-${
            depNode.metadata.document_version
          }`;

          if (!visited.has(depNodeId)) {
            this.dfsDetectCycle(
              graph,
              depNodeId,
              visited,
              recursionStack,
              cycles,
              [...path]
            );
          } else if (recursionStack.has(depNodeId)) {
            const cycleStart = path.indexOf(depNodeId);
            cycles.push([...path.slice(cycleStart), depNodeId]);
          }
        }
      }
    }

    recursionStack.delete(nodeId);
  }

  // 查找孤立版本
  findOrphanedVersions(dependencyGraph) {
    const orphaned = [];

    for (const [nodeId, node] of dependencyGraph) {
      if (node.dependencies.length === 0 && node.dependents.length === 0) {
        orphaned.push({
          nodeId,
          document: node.document.path,
          version: node.metadata.document_version,
        });
      }
    }

    return orphaned;
  }

  // 版本影响分析
  analyzeVersionImpact(dependencyGraph, targetNodeId) {
    const impacted = new Set();
    const queue = [targetNodeId];

    while (queue.length > 0) {
      const current = queue.shift();
      const node = dependencyGraph.get(current);

      if (node) {
        for (const dependent of node.dependents) {
          if (!impacted.has(dependent)) {
            impacted.add(dependent);
            queue.push(dependent);
          }
        }
      }
    }

    return Array.from(impacted);
  }

  // 查找可以安全删除的版本
  findSafeToDeleteVersions(dependencyGraph) {
    const safeToDelete = [];

    for (const [nodeId, node] of dependencyGraph) {
      // 如果没有依赖者，且状态为deprecated，则可以安全删除
      if (
        node.dependents.length === 0 &&
        node.metadata.document_status === "deprecated"
      ) {
        safeToDelete.push({
          nodeId,
          document: node.document.path,
          version: node.metadata.document_version,
          reason: "No dependents and deprecated",
        });
      }

      // 如果有替代版本，且没有依赖者
      if (node.dependents.length === 0 && node.replaces.length > 0) {
        safeToDelete.push({
          nodeId,
          document: node.document.path,
          version: node.metadata.document_version,
          reason: "Replaced by newer version and no dependents",
        });
      }
    }

    return safeToDelete;
  }
}

class DocumentGovernanceToolkit {
  constructor() {
    this.config = this.loadConfig();
    this.metadata = new Map();
    this.versionManager = new DocumentVersionManager();
    this.relationshipAnalyzer = new DocumentVersionRelationshipAnalyzer(
      this.versionManager
    );
  }

  loadConfig() {
    const configPath = path.join(__dirname, "document-governance-config.yaml");
    if (fs.existsSync(configPath)) {
      return yaml.load(fs.readFileSync(configPath, "utf8"));
    }
    return this.getDefaultConfig();
  }

  getDefaultConfig() {
    return {
      valueAssessment: {
        permanent: {
          keywords: ["架构决策", "技术决策", "安全审计", "升级记录", "ADR"],
          patterns: ["**/decisions/**", "**/architecture/**", "**/security/**"],
          minScore: 90,
        },
        longTerm: {
          keywords: ["工作总结", "阶段报告", "实施计划", "评估报告"],
          patterns: ["**/reports/**", "**/summaries/**", "**/plans/**"],
          minScore: 70,
        },
        mediumTerm: {
          keywords: ["开发指南", "部署指南", "最佳实践", "使用说明"],
          patterns: ["**/guides/**", "**/docs/**", "**/howto/**"],
          minScore: 50,
        },
        shortTerm: {
          keywords: ["会议记录", "临时方案", "草稿", "实验"],
          patterns: ["**/temp/**", "**/draft/**", "**/meeting/**"],
          minScore: 30,
        },
      },
      classification: {
        coreDesign: [
          "架构设计",
          "数据库设计",
          "API设计",
          "安全设计",
          "升级记录",
        ],
        decisions: ["技术决策", "架构决策", "业务决策", "工具选型"],
        workRecords: ["升级实施", "问题解决", "阶段总结", "调研报告"],
        guides: ["开发指南", "部署指南", "运维指南", "使用说明"],
        reports: ["安全分析", "性能分析", "系统评估", "技术调研"],
        history: ["已完成项目", "演进历史", "废弃功能", "版本记录"],
      },
    };
  }

  /**
   * 文档价值评估器
   */
  assessDocumentValue(filePath, content) {
    const assessment = {
      filePath,
      valueScore: 0,
      valueLevel: "unknown",
      reasons: [],
      recommendations: [],
    };

    // 基于关键词评估
    const keywordScore = this.assessByKeywords(content);
    assessment.valueScore += keywordScore.score;
    assessment.reasons.push(...keywordScore.reasons);

    // 基于文件路径评估
    const pathScore = this.assessByPath(filePath);
    assessment.valueScore += pathScore.score;
    assessment.reasons.push(...pathScore.reasons);

    // 基于文档结构评估
    const structureScore = this.assessByStructure(content);
    assessment.valueScore += structureScore.score;
    assessment.reasons.push(...structureScore.reasons);

    // 确定价值等级
    assessment.valueLevel = this.determineValueLevel(assessment.valueScore);
    assessment.recommendations = this.generateRecommendations(assessment);

    return assessment;
  }

  assessByKeywords(content) {
    const result = { score: 0, reasons: [] };
    const config = this.config.valueAssessment;

    for (const [level, criteria] of Object.entries(config)) {
      for (const keyword of criteria.keywords) {
        if (content.toLowerCase().includes(keyword.toLowerCase())) {
          const score = criteria.minScore * 0.1;
          result.score += score;
          result.reasons.push(`包含${level}关键词: ${keyword} (+${score})`);
        }
      }
    }

    return result;
  }

  assessByPath(filePath) {
    const result = { score: 0, reasons: [] };
    const config = this.config.valueAssessment;

    for (const [level, criteria] of Object.entries(config)) {
      for (const pattern of criteria.patterns) {
        if (this.matchPattern(filePath, pattern)) {
          const score = criteria.minScore * 0.2;
          result.score += score;
          result.reasons.push(`路径匹配${level}模式: ${pattern} (+${score})`);
        }
      }
    }

    return result;
  }

  assessByStructure(content) {
    const result = { score: 0, reasons: [] };

    // 检查文档结构完整性
    if (content.includes("## ") || content.includes("# ")) {
      result.score += 10;
      result.reasons.push("具有良好的文档结构 (+10)");
    }

    // 检查是否包含代码示例
    if (content.includes("```")) {
      result.score += 5;
      result.reasons.push("包含代码示例 (+5)");
    }

    // 检查是否包含决策记录格式
    if (
      content.includes("决策") &&
      content.includes("背景") &&
      content.includes("结果")
    ) {
      result.score += 20;
      result.reasons.push("符合决策记录格式 (+20)");
    }

    // 检查文档长度（内容丰富度）
    if (content.length > 5000) {
      result.score += 10;
      result.reasons.push("内容丰富，文档详细 (+10)");
    }

    return result;
  }

  determineValueLevel(score) {
    if (score >= 90) return "permanent";
    if (score >= 70) return "longTerm";
    if (score >= 50) return "mediumTerm";
    if (score >= 30) return "shortTerm";
    return "archivable";
  }

  generateRecommendations(assessment) {
    const recommendations = [];

    switch (assessment.valueLevel) {
      case "permanent":
        recommendations.push("建议永久保留，移至核心设计文档目录");
        recommendations.push("添加详细的元数据和标签");
        recommendations.push("建立定期审查机制");
        break;
      case "longTerm":
        recommendations.push("建议长期保留，移至相应的报告目录");
        recommendations.push("设置3年后重新评估");
        break;
      case "mediumTerm":
        recommendations.push("建议中期保留，移至指南文档目录");
        recommendations.push("设置1年后重新评估");
        break;
      case "shortTerm":
        recommendations.push("建议短期保留，定期清理");
        recommendations.push("设置6个月后重新评估");
        break;
      case "archivable":
        recommendations.push("可考虑归档，但需人工确认");
        recommendations.push("检查是否有替代文档");
        break;
    }

    return recommendations;
  }

  /**
   * 文档分类器
   */
  classifyDocument(filePath, content) {
    const classification = {
      filePath,
      suggestedCategory: "unknown",
      confidence: 0,
      reasons: [],
    };

    const config = this.config.classification;
    let maxScore = 0;
    let bestCategory = "unknown";

    for (const [category, keywords] of Object.entries(config)) {
      let score = 0;
      const reasons = [];

      for (const keyword of keywords) {
        if (content.toLowerCase().includes(keyword.toLowerCase())) {
          score += 10;
          reasons.push(`包含关键词: ${keyword}`);
        }
      }

      // 基于文件名评估
      const fileName = path.basename(filePath).toLowerCase();
      for (const keyword of keywords) {
        if (fileName.includes(keyword.toLowerCase())) {
          score += 15;
          reasons.push(`文件名包含: ${keyword}`);
        }
      }

      if (score > maxScore) {
        maxScore = score;
        bestCategory = category;
        classification.reasons = reasons;
      }
    }

    classification.suggestedCategory = bestCategory;
    classification.confidence = Math.min((maxScore / 50) * 100, 100);

    return classification;
  }

  /**
   * 文档关系分析器
   */
  analyzeDocumentRelationships(documents) {
    const relationships = new Map();

    for (const doc of documents) {
      const relations = {
        references: [],
        referencedBy: [],
        similar: [],
        duplicates: [],
      };

      // 分析引用关系
      const content = fs.readFileSync(doc.path, "utf8");
      const references = this.extractReferences(content);
      relations.references = references;

      // 检测相似文档
      const similar = this.findSimilarDocuments(doc, documents);
      relations.similar = similar;

      // 检测重复文档
      const duplicates = this.findDuplicateDocuments(doc, documents);
      relations.duplicates = duplicates;

      relationships.set(doc.path, relations);
    }

    return relationships;
  }

  extractReferences(content) {
    const references = [];

    // 提取Markdown链接
    const markdownLinks = content.match(/\[.*?\]\((.*?)\)/g) || [];
    for (const link of markdownLinks) {
      const match = link.match(/\[.*?\]\((.*?)\)/);
      if (match && match[1].endsWith(".md")) {
        references.push(match[1]);
      }
    }

    // 提取文件路径引用
    const pathReferences = content.match(/[a-zA-Z0-9_-]+\.md/g) || [];
    references.push(...pathReferences);

    return [...new Set(references)];
  }

  findSimilarDocuments(targetDoc, allDocs) {
    const similar = [];
    const targetContent = fs.readFileSync(targetDoc.path, "utf8");
    const targetWords = this.extractKeywords(targetContent);

    for (const doc of allDocs) {
      if (doc.path === targetDoc.path) continue;

      const docContent = fs.readFileSync(doc.path, "utf8");
      const docWords = this.extractKeywords(docContent);

      const similarity = this.calculateSimilarity(targetWords, docWords);
      if (similarity > 0.3) {
        similar.push({
          path: doc.path,
          similarity: similarity,
        });
      }
    }

    return similar.sort((a, b) => b.similarity - a.similarity);
  }

  findDuplicateDocuments(targetDoc, allDocs) {
    const duplicates = [];
    const targetContent = fs.readFileSync(targetDoc.path, "utf8");
    const targetHash = this.calculateContentHash(targetContent);

    for (const doc of allDocs) {
      if (doc.path === targetDoc.path) continue;

      const docContent = fs.readFileSync(doc.path, "utf8");
      const docHash = this.calculateContentHash(docContent);

      if (targetHash === docHash) {
        duplicates.push(doc.path);
      }
    }

    return duplicates;
  }

  /**
   * 自动化重组工具
   */
  async reorganizeDocuments(sourceDir, targetDir) {
    const documents = this.scanDocuments(sourceDir);
    const reorganizationPlan = [];

    for (const doc of documents) {
      const content = fs.readFileSync(doc.path, "utf8");

      // 评估文档价值
      const valueAssessment = this.assessDocumentValue(doc.path, content);

      // 分类文档
      const classification = this.classifyDocument(doc.path, content);

      // 生成重组计划
      const targetPath = this.generateTargetPath(
        targetDir,
        classification.suggestedCategory,
        valueAssessment.valueLevel,
        doc.name
      );

      reorganizationPlan.push({
        source: doc.path,
        target: targetPath,
        valueAssessment,
        classification,
        action: this.determineAction(valueAssessment, classification),
      });
    }

    return reorganizationPlan;
  }

  generateTargetPath(baseDir, category, valueLevel, fileName) {
    const categoryMap = {
      coreDesign: "核心设计文档",
      decisions: "决策记录",
      workRecords: "工作记录",
      guides: "指南文档",
      reports: "分析报告",
      history: "历史文档",
    };

    const categoryDir = categoryMap[category] || "未分类文档";

    if (valueLevel === "permanent") {
      return path.join(baseDir, categoryDir, "永久保留", fileName);
    } else if (valueLevel === "longTerm") {
      return path.join(baseDir, categoryDir, "长期保留", fileName);
    } else {
      return path.join(baseDir, categoryDir, fileName);
    }
  }

  determineAction(valueAssessment, classification) {
    if (valueAssessment.valueLevel === "permanent") {
      return "move_to_core";
    } else if (valueAssessment.valueLevel === "longTerm") {
      return "move_to_reports";
    } else if (
      valueAssessment.valueLevel === "archivable" &&
      classification.confidence < 50
    ) {
      return "review_required";
    } else {
      return "move_to_category";
    }
  }

  /**
   * 报告生成器
   */
  generateGovernanceReport(documents, relationships) {
    const report = {
      summary: {
        totalDocuments: documents.length,
        byValueLevel: {},
        byCategory: {},
        issuesFound: 0,
      },
      details: {
        highValueDocuments: [],
        duplicateDocuments: [],
        orphanedDocuments: [],
        misclassifiedDocuments: [],
      },
      recommendations: [],
    };

    // 统计分析
    for (const doc of documents) {
      const content = fs.readFileSync(doc.path, "utf8");
      const valueAssessment = this.assessDocumentValue(doc.path, content);
      const classification = this.classifyDocument(doc.path, content);

      // 按价值等级统计
      report.summary.byValueLevel[valueAssessment.valueLevel] =
        (report.summary.byValueLevel[valueAssessment.valueLevel] || 0) + 1;

      // 按分类统计
      report.summary.byCategory[classification.suggestedCategory] =
        (report.summary.byCategory[classification.suggestedCategory] || 0) + 1;

      // 识别高价值文档
      if (valueAssessment.valueLevel === "permanent") {
        report.details.highValueDocuments.push({
          path: doc.path,
          score: valueAssessment.valueScore,
          reasons: valueAssessment.reasons,
        });
      }

      // 识别分类置信度低的文档
      if (classification.confidence < 50) {
        report.details.misclassifiedDocuments.push({
          path: doc.path,
          confidence: classification.confidence,
          suggestedCategory: classification.suggestedCategory,
        });
        report.summary.issuesFound++;
      }
    }

    // 分析文档关系
    for (const [docPath, relations] of relationships) {
      if (relations.duplicates.length > 0) {
        report.details.duplicateDocuments.push({
          path: docPath,
          duplicates: relations.duplicates,
        });
        report.summary.issuesFound++;
      }

      if (
        relations.references.length === 0 &&
        relations.referencedBy.length === 0
      ) {
        report.details.orphanedDocuments.push(docPath);
        report.summary.issuesFound++;
      }
    }

    // 生成建议
    report.recommendations = this.generateGovernanceRecommendations(report);

    return report;
  }

  generateGovernanceRecommendations(report) {
    const recommendations = [];

    if (report.details.highValueDocuments.length > 0) {
      recommendations.push({
        priority: "high",
        type: "preservation",
        message: `发现${report.details.highValueDocuments.length}个高价值文档，建议立即移至核心文档目录并加强保护`,
      });
    }

    if (report.details.duplicateDocuments.length > 0) {
      recommendations.push({
        priority: "medium",
        type: "deduplication",
        message: `发现${report.details.duplicateDocuments.length}组重复文档，建议进行去重处理`,
      });
    }

    if (report.details.orphanedDocuments.length > 0) {
      recommendations.push({
        priority: "low",
        type: "relationship",
        message: `发现${report.details.orphanedDocuments.length}个孤立文档，建议检查其价值和关联性`,
      });
    }

    if (report.details.misclassifiedDocuments.length > 0) {
      recommendations.push({
        priority: "medium",
        type: "classification",
        message: `发现${report.details.misclassifiedDocuments.length}个分类不确定的文档，建议人工审查`,
      });
    }

    return recommendations;
  }

  // 工具方法
  scanDocuments(dir) {
    const documents = [];

    function scanRecursive(currentDir) {
      const items = fs.readdirSync(currentDir);

      for (const item of items) {
        const itemPath = path.join(currentDir, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory()) {
          scanRecursive(itemPath);
        } else if (item.endsWith(".md")) {
          documents.push({
            path: itemPath,
            name: item,
            size: stat.size,
            modified: stat.mtime,
          });
        }
      }
    }

    scanRecursive(dir);
    return documents;
  }

  matchPattern(filePath, pattern) {
    // 简单的glob模式匹配
    const regex = pattern
      .replace(/\*\*/g, ".*")
      .replace(/\*/g, "[^/]*")
      .replace(/\?/g, ".");

    return new RegExp(regex).test(filePath);
  }

  extractKeywords(content) {
    // 提取关键词（简化版）
    const words = content
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, " ")
      .split(/\s+/)
      .filter((word) => word.length > 2);

    const wordCount = {};
    for (const word of words) {
      wordCount[word] = (wordCount[word] || 0) + 1;
    }

    return Object.keys(wordCount)
      .sort((a, b) => wordCount[b] - wordCount[a])
      .slice(0, 20);
  }

  calculateSimilarity(words1, words2) {
    const set1 = new Set(words1);
    const set2 = new Set(words2);
    const intersection = new Set([...set1].filter((x) => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  calculateContentHash(content) {
    // 简单的内容哈希
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 版本控制相关方法
   */

  // 评估文档版本状态
  assessDocumentVersionStatus(filePath, content) {
    const metadata = this.versionManager.extractVersionMetadata(content);
    const assessment = {
      filePath,
      version: metadata.document_version,
      status: metadata.document_status,
      issues: [],
      recommendations: [],
    };

    // 检查版本号格式
    try {
      this.versionManager.parseVersion(metadata.document_version);
    } catch (error) {
      assessment.issues.push({
        type: "invalid_version_format",
        message: `版本号格式错误: ${error.message}`,
      });
    }

    // 检查版本状态
    if (
      ![
        "dev",
        "alpha",
        "beta",
        "rc",
        "release",
        "maintenance",
        "deprecated",
      ].includes(metadata.document_status)
    ) {
      assessment.issues.push({
        type: "invalid_status",
        message: `无效的文档状态: ${metadata.document_status}`,
      });
    }

    // 生成建议
    if (metadata.document_status === "deprecated") {
      assessment.recommendations.push("考虑归档此文档，如果没有其他文档依赖它");
    }

    if (metadata.replaces.length > 0) {
      assessment.recommendations.push(
        `此版本替代了 ${metadata.replaces.join(
          ", "
        )}，确保旧版本已正确标记为deprecated`
      );
    }

    return assessment;
  }

  // 生成版本控制报告
  generateVersionControlReport(documents) {
    const report = {
      summary: {
        totalDocuments: documents.length,
        byStatus: {},
        versionIssues: 0,
        dependencyIssues: 0,
      },
      versionConflicts: [],
      dependencyAnalysis: {},
      recommendations: [],
    };

    // 检测版本冲突
    report.versionConflicts =
      this.versionManager.detectVersionConflicts(documents);
    report.summary.versionIssues = report.versionConflicts.length;

    // 构建依赖图
    const dependencyGraph =
      this.relationshipAnalyzer.buildDependencyGraph(documents);

    // 分析依赖关系
    report.dependencyAnalysis = {
      circularDependencies:
        this.relationshipAnalyzer.detectCircularDependencies(dependencyGraph),
      orphanedVersions:
        this.relationshipAnalyzer.findOrphanedVersions(dependencyGraph),
      safeToDelete:
        this.relationshipAnalyzer.findSafeToDeleteVersions(dependencyGraph),
    };

    // 统计文档状态
    for (const doc of documents) {
      const content = fs.readFileSync(doc.path, "utf8");
      const metadata = this.versionManager.extractVersionMetadata(content);

      report.summary.byStatus[metadata.document_status] =
        (report.summary.byStatus[metadata.document_status] || 0) + 1;
    }

    // 生成建议
    if (report.versionConflicts.length > 0) {
      report.recommendations.push({
        priority: "high",
        type: "version_conflicts",
        message: `发现 ${report.versionConflicts.length} 个版本冲突，需要立即解决`,
      });
    }

    if (report.dependencyAnalysis.circularDependencies.length > 0) {
      report.recommendations.push({
        priority: "high",
        type: "circular_dependencies",
        message: `发现 ${report.dependencyAnalysis.circularDependencies.length} 个循环依赖，需要重构依赖关系`,
      });
    }

    if (report.dependencyAnalysis.safeToDelete.length > 0) {
      report.recommendations.push({
        priority: "low",
        type: "cleanup",
        message: `发现 ${report.dependencyAnalysis.safeToDelete.length} 个可以安全删除的版本`,
      });
    }

    return report;
  }

  // 建议版本号升级
  suggestVersionUpgrade(filePath, content, changeDescription) {
    const metadata = this.versionManager.extractVersionMetadata(content);
    const currentVersion = this.versionManager.parseVersion(
      metadata.document_version
    );

    let suggestedVersion;

    // 基于变更描述判断版本升级类型
    const majorKeywords = ["重大变更", "不兼容", "架构重构", "完全重写"];
    const minorKeywords = ["新功能", "新增", "扩展", "重要更新"];
    const patchKeywords = ["修复", "错误", "格式", "小幅"];

    const description = changeDescription.toLowerCase();

    if (majorKeywords.some((keyword) => description.includes(keyword))) {
      suggestedVersion = `${currentVersion.major + 1}.0.0`;
    } else if (minorKeywords.some((keyword) => description.includes(keyword))) {
      suggestedVersion = `${currentVersion.major}.${
        currentVersion.minor + 1
      }.0`;
    } else if (patchKeywords.some((keyword) => description.includes(keyword))) {
      suggestedVersion = `${currentVersion.major}.${currentVersion.minor}.${
        currentVersion.patch + 1
      }`;
    } else {
      // 默认为patch升级
      suggestedVersion = `${currentVersion.major}.${currentVersion.minor}.${
        currentVersion.patch + 1
      }`;
    }

    return {
      currentVersion: metadata.document_version,
      suggestedVersion,
      reasoning: this.getVersionUpgradeReasoning(changeDescription),
    };
  }

  getVersionUpgradeReasoning(changeDescription) {
    const description = changeDescription.toLowerCase();

    if (description.includes("重大变更") || description.includes("不兼容")) {
      return "检测到重大变更或不兼容更新，建议升级主版本号";
    } else if (description.includes("新功能") || description.includes("新增")) {
      return "检测到新功能添加，建议升级次版本号";
    } else if (description.includes("修复") || description.includes("错误")) {
      return "检测到错误修复，建议升级修订版本号";
    } else {
      return "基于变更内容，建议升级修订版本号";
    }
  }
}

// CLI接口扩展
if (require.main === module) {
  const toolkit = new DocumentGovernanceToolkit();
  const command = process.argv[2];
  const args = process.argv.slice(3);

  switch (command) {
    case "version-check":
      if (args.length < 1) {
        console.log(
          "用法: node document-governance-toolkit.js version-check <文档路径>"
        );
        process.exit(1);
      }
      const versionCheckPath = args[0];
      const versionCheckContent = fs.readFileSync(versionCheckPath, "utf8");
      const versionStatus = toolkit.assessDocumentVersionStatus(
        versionCheckPath,
        versionCheckContent
      );
      console.log(JSON.stringify(versionStatus, null, 2));
      break;

    case "version-report":
      if (args.length < 1) {
        console.log(
          "用法: node document-governance-toolkit.js version-report <目录路径>"
        );
        process.exit(1);
      }
      const versionReportDir = args[0];
      const versionReportDocs = toolkit.scanDocuments(versionReportDir);
      const versionReport =
        toolkit.generateVersionControlReport(versionReportDocs);
      console.log(JSON.stringify(versionReport, null, 2));
      break;

    case "suggest-version":
      if (args.length < 2) {
        console.log(
          "用法: node document-governance-toolkit.js suggest-version <文档路径> <变更描述>"
        );
        process.exit(1);
      }
      const docPath = args[0];
      const changeDesc = args[1];
      const docContent = fs.readFileSync(docPath, "utf8");
      const suggestion = toolkit.suggestVersionUpgrade(
        docPath,
        docContent,
        changeDesc
      );
      console.log(JSON.stringify(suggestion, null, 2));
      break;

    case "assess":
      if (args.length < 1) {
        console.log(
          "用法: node document-governance-toolkit.js assess <文档路径>"
        );
        process.exit(1);
      }
      const assessFilePath = args[0];
      const assessContent = fs.readFileSync(assessFilePath, "utf8");
      const assessment = toolkit.assessDocumentValue(
        assessFilePath,
        assessContent
      );
      console.log(JSON.stringify(assessment, null, 2));
      break;

    case "classify":
      if (args.length < 1) {
        console.log(
          "用法: node document-governance-toolkit.js classify <文档路径>"
        );
        process.exit(1);
      }
      const classifyPath = args[0];
      const classifyContent = fs.readFileSync(classifyPath, "utf8");
      const classification = toolkit.classifyDocument(
        classifyPath,
        classifyContent
      );
      console.log(JSON.stringify(classification, null, 2));
      break;

    case "scan":
      if (args.length < 1) {
        console.log(
          "用法: node document-governance-toolkit.js scan <目录路径>"
        );
        process.exit(1);
      }
      const scanDir = args[0];
      const documents = toolkit.scanDocuments(scanDir);
      console.log(`扫描到 ${documents.length} 个文档`);
      console.log(JSON.stringify(documents, null, 2));
      break;

    case "report":
      if (args.length < 1) {
        console.log(
          "用法: node document-governance-toolkit.js report <目录路径>"
        );
        process.exit(1);
      }
      const governanceReportDir = args[0];
      const governanceReportDocs = toolkit.scanDocuments(governanceReportDir);
      const relationships =
        toolkit.analyzeDocumentRelationships(governanceReportDocs);
      const report = toolkit.generateGovernanceReport(
        governanceReportDocs,
        relationships
      );
      console.log(JSON.stringify(report, null, 2));
      break;

    default:
      console.log("AIBUBB文档治理工具包 - 版本控制增强版");
      console.log("");
      console.log("可用命令:");
      console.log("  assess <文档路径>           - 评估文档价值");
      console.log("  classify <文档路径>         - 分类文档");
      console.log("  scan <目录路径>             - 扫描目录中的文档");
      console.log("  report <目录路径>           - 生成治理报告");
      console.log("  version-check <文档路径>    - 检查文档版本状态");
      console.log("  version-report <目录路径>   - 生成版本控制报告");
      console.log("  suggest-version <文档路径> <变更描述> - 建议版本号升级");
      break;
  }
}

module.exports = {
  DocumentGovernanceToolkit,
  DocumentVersionManager,
  DocumentVersionRelationshipAnalyzer,
};
