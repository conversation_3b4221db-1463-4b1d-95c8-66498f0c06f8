# 第3章：核心技术栈

## 📋 文档概述

**文档名称**: AIBUBB后端系统全貌培训文档 - 第3章 核心技术栈  
**文档版本**: 2.2.0  
**创建日期**: 2024-11-15  
**最后修改**: 2025-01-27  
**作者**: 后端技术团队  
**审核者**: 技术架构师  
**文档状态**: ✅ 最新  
**价值等级**: 🔴 核心关键  
**依赖文档**: [第2章-系统架构](./AIBUBB后端系统全貌培训文档-第2章-系统架构.md)  
**被依赖文档**: [第4章-领域模型](./AIBUBB后端系统全貌培训文档-第4章-领域模型.md), [第5章-API设计与实现](./AIBUBB后端系统全貌培训文档-第5章-API设计与实现.md)  

## 📝 变更日志

### v2.2.0 (2025-01-27)
- 添加版本控制和价值评估元数据
- 更新所有技术栈组件的实现示例
- 完善Docker容器化配置
- 新增Swagger/OpenAPI文档配置
- 更新JWT认证和授权机制

### v2.1.0 (2024-12-20)
- 完善领域事件机制实现
- 新增Redis缓存策略
- 更新Sequelize ORM配置
- 添加TypeScript集成指南

### v2.0.0 (2024-12-01)
- 大幅更新技术栈内容
- 新增领域事件机制章节
- 完善认证授权机制
- 添加容器化部署指南

### v1.0.0 (2024-11-15)
- 初始版本创建
- 定义核心技术栈框架
- 建立技术组件介绍

## 3.1 后端框架：Node.js + Express.js

### Node.js 概述
在AIBUBB系统中，我们使用Node.js v18.x LTS版本 (基于 `node:18-alpine` Docker镜像)，这个版本提供了良好的稳定性和性能，同时支持现代JavaScript特性。我们要求所有开发环境和生产环境使用相同的Node.js版本，以确保一致性。

Node.js是AIBUBB后端系统的基础运行环境，它是一个基于Chrome V8引擎的JavaScript运行时，具有以下特点：
- **事件驱动**：基于事件循环的非阻塞I/O模型，适合处理高并发请求
- **单线程**：主线程单线程执行，通过事件循环和工作线程池处理并发
- **跨平台**：可在Windows、macOS和Linux等多种操作系统上运行
- **生态系统**：拥有丰富的npm包生态系统，可以快速集成各种功能

### Express.js 框架
Express.js是AIBUBB系统的Web应用框架，我们使用Express.js v5.1.0版本。选择5.x版本的原因：
- 提供了更好的性能和现代化特性
- 支持更好的异步处理和错误处理
- 与Node.js 18.x有更好的兼容性
- 提供了更强的类型安全支持

Express.js是一个轻量级、灵活的Node.js Web应用框架，提供了一系列强大的功能：
- **路由系统**：支持HTTP方法和URL路径的灵活匹配
- **中间件机制**：通过中间件链处理请求和响应
- **视图渲染**：支持多种模板引擎（虽然在AIBUBB中主要用于API服务）
- **错误处理**：提供集中式的错误处理机制
- **扩展性**：易于与其他库和中间件集成

在AIBUBB系统中，Express.js的使用方式如下：

#### 应用初始化
Express.js的初始化主要在 `backend/app.ts` 文件中完成。基础中间件如 `cors`, `helmet`, `compression`, `express.json`, `express.urlencoded` 被直接使用。请求日志和全局错误处理中间件以内联函数的形式集成在 `app.ts` 中。API路由通过依赖注入和路由注册机制进行配置。

```typescript
// backend/app.ts
/**
 * 应用入口文件
 * 使用DDD架构和依赖注入容器
 */
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { ContainerProvider } from './infrastructure/di/ContainerProvider';
import { Logger } from './infrastructure/logging/Logger';

const app = express();

// 初始化容器
const containerProvider = ContainerProvider.getInstance();
containerProvider.initialize();
const container = containerProvider.getContainer();

// 获取日志记录器
const logger = container.get<Logger>('logger');


// 配置中间件
app.use(cors());
app.use(helmet());
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.url}`, {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.headers['user-agent']
  });
  next();
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', version: process.env.npm_package_version });
});

// 导入路由 (实际项目中可能更复杂，并包含版本控制)
// const apiRouterV2 = require('./routes/v2'); // 假设v2路由
// app.use('/api/v2', apiRouterV2);
// 实际代码显示的是:
const apiRouter = require('./routes/api'); // 需要确认 './routes/api' 的具体内容和结构
app.use('/api', apiRouter);


// 错误处理中间件
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error(`请求处理错误: ${err.message}`, {
    error: err.stack,
    method: req.method,
    url: req.url
  });

  const statusCode = err.statusCode || 500;
  const message = err.message || '服务器内部错误';

  res.status(statusCode).json({
    error: {
      code: err.code || 'INTERNAL_SERVER_ERROR',
      message
    }
  });
});

export default app;
```

#### 路由定义
路由定义在 `backend/interfaces/api/routes/` 目录下，按领域进行分类，如 `content/exerciseRoutes.ts`、`tag/tagRoutes.ts` 等。使用 `express.Router()` 创建路由实例，并通过专门的路由配置函数将路由与控制器方法绑定。请求验证通过验证器和验证中间件实现。

```typescript
// backend/interfaces/api/routes/content/exerciseRoutes.ts
import { Router } from 'express';
import { ExerciseController } from '../../controllers/content/ExerciseController';
import { authMiddleware } from '../../middlewares/authMiddleware';
import { validateRequest } from '../../middlewares/validateRequestMiddleware';
import {
  createExerciseSchema,
  updateExerciseSchema,
  addTagSchema
} from '../../validators/content/exerciseValidators';

/**
 * 配置练习路由
 * @param router Express路由器
 * @param exerciseController 练习控制器
 */
export const exerciseRoutes = (router: Router, exerciseController: ExerciseController): void => {
  // 练习CRUD
  router.post(
    '/exercises',
    authMiddleware,
    validateRequest(createExerciseSchema),
    exerciseController.createExercise.bind(exerciseController)
  );

router.get('/exercises/:id', // 路径调整为更RESTful
  authMiddleware,
  exerciseController.getExerciseById
);

router.post('/exercises',
  authMiddleware,
  [
    body('tagId').isInt().withMessage('标签ID必须是整数'), // 示例，实际字段根据业务调整
    body('title').notEmpty().withMessage('练习标题不能为空')
      .isLength({ max: 100 }).withMessage('练习标题最多100个字符'),
    body('description').notEmpty().withMessage('练习描述不能为空'),
    // ... 其他验证规则 ...
    validate
  ],
  exerciseController.createExercise
);

router.put('/exercises/:id',
  authMiddleware,
  [
    // ... PUT请求的验证规则 ...
    validate
  ],
  exerciseController.updateExercise
);

router.delete('/exercises/:id',
  authMiddleware,
  exerciseController.deleteExercise // 实际可能是 softDeleteExercise 或其他
);

module.exports = router;
```

#### 控制器实现
控制器位于 `backend/interfaces/api/controllers/` 目录下，按领域进行分类，如 `content/ExerciseController.ts`、`tag/TagController.ts` 等。控制器是TypeScript类，通过依赖注入获取应用服务，并将HTTP请求转换为命令或查询对象。

```typescript
// backend/interfaces/api/controllers/content/ExerciseController.ts
import { Request, Response, NextFunction } from 'express';
import { ExerciseApplicationService } from '../../../../application/services/content/exercise/ExerciseApplicationService';
import { CreateExerciseCommand } from '../../../../application/commands/content/exercise/CreateExerciseCommand';
import { GetExerciseQuery } from '../../../../application/queries/content/exercise/GetExerciseQuery';
import { SearchExercisesQuery } from '../../../../application/queries/content/exercise/SearchExercisesQuery';

/**
 * ExerciseController
 * 练习控制器，处理练习相关的HTTP请求
 */
export class ExerciseController {
  /**
   * 构造函数
   * @param exerciseApplicationService 练习应用服务
   */
  constructor(private readonly exerciseApplicationService: ExerciseApplicationService) {}

  /**
   * 创建练习
   * @param req Express请求对象
   * @param res Express响应对象
   * @param next Express下一个中间件函数
   */
  async createExercise(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: CreateExerciseCommand = {
        title: req.body.title,
        description: req.body.description,
        expectedResult: req.body.expectedResult,
        difficulty: req.body.difficulty,
        timeEstimateMinutes: req.body.timeEstimateMinutes,
        creatorId: req.user!.id,
        visibility: req.body.visibility,
        isOfficial: req.body.isOfficial,
        tags: req.body.tags
      };

      const result = await this.exerciseApplicationService.createExercise(command);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }
};

// 其他控制器方法 (getExerciseById, updateExercise, deleteExercise) 类似结构

```

#### 应用服务实现
应用服务位于 `backend/application/services/` 目录下，按领域进行分类，如 `content/exercise/ExerciseApplicationService.ts`。应用服务协调领域对象完成用例，并将领域对象转换为DTO。

```typescript
// backend/application/services/content/exercise/ExerciseApplicationService.ts
import { Exercise } from '../../../../domain/models/content/exercise/Exercise';
import { ExerciseRepository } from '../../../../domain/repositories/content/exercise/ExerciseRepository';
import { UnitOfWork } from '../../../../infrastructure/persistence/UnitOfWork';
import { CreateExerciseCommand } from '../../../commands/content/exercise/CreateExerciseCommand';
import { ExerciseDto } from '../../../dtos/content/exercise/ExerciseDto';

/**
 * ExerciseApplicationService
 * 练习应用服务，协调领域对象完成用例
 */
export class ExerciseApplicationService {
  /**
   * 构造函数
   * @param exerciseRepository 练习仓库
   * @param unitOfWork 工作单元
   */
  constructor(
    private readonly exerciseRepository: ExerciseRepository,
    private readonly unitOfWork: UnitOfWork
  ) {}

  /**
   * 创建练习
   * @param command 创建练习命令
   * @returns 创建的练习DTO
   */
  async createExercise(command: CreateExerciseCommand): Promise<ExerciseDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const exercise = Exercise.create(
        command.title,
        command.description,
        command.expectedResult || null,
        command.difficulty,
        command.timeEstimateMinutes,
        command.creatorId,
        command.visibility,
        command.isOfficial
      );

      // 添加标签
      if (command.tags && command.tags.length > 0) {
        command.tags.forEach(tag => exercise.addTag(tag));
      }

      const savedExercise = await this.exerciseRepository.save(exercise);

      return this.toExerciseDto(savedExercise);
    });
  }
```

### TypeScript 集成
AIBUBB系统使用TypeScript作为主要开发语言，特别是在领域驱动设计的实现中。TypeScript的配置文件 `tsconfig.json` 位于项目根目录。

它是JavaScript的超集，添加了静态类型系统和其他特性：
- **类型安全**：通过静态类型检查减少运行时错误
- **代码可读性**：类型注解提高了代码的可读性和自文档性
- **工具支持**：提供更好的IDE支持，如代码补全和重构
- **面向对象特性**：支持类、接口、泛型等面向对象编程特性

在AIBUBB系统中，TypeScript的配置如下：
```json
// ./tsconfig.json (根目录)
{
  "compilerOptions": {
    "target": "es2018",
    "module": "commonjs",
    "lib": ["es2018", "dom"],
    "allowJs": true,
    "checkJs": false,
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": "./",
    "strict": false,
    "noImplicitAny": false,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": false,
    "resolveJsonModule": true
  },
  "include": [
    "**/*.ts",
    "**/*.js"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.spec.ts",
    "**/*.test.ts"
  ]
}
```

## 3.2 数据库：MySQL + Sequelize ORM

### MySQL 数据库
MySQL是AIBUBB系统的主要关系型数据库。数据库连接配置和 Sequelize 实例的初始化主要在 `backend/config/database.js` 文件中处理。该文件会从一个更通用的配置文件 (如 `backend/config/config.js`) 读取基础连接参数，并可能根据运行环境（如 Docker 内外）进行调整。它直接导出配置好的 `sequelize` 实例供应用其他部分使用。

MySQL用于存储所有结构化数据：
- **可靠性**：成熟稳定的关系型数据库，具有良好的数据一致性和可靠性
- **性能**：优秀的查询性能和优化能力，支持复杂查询和事务
- **扩展性**：支持主从复制、分区等扩展方案
- **生态系统**：丰富的工具和社区支持

在AIBUBB系统中，MySQL的主要配置如下：
```javascript
// backend/config/database.js
const { Sequelize } = require('sequelize');
const config = require('./config'); // 假设主配置文件是 './config.js'

// 示例：从主配置中获取数据库参数
let dbHost = config.database.host;
let dbPort = config.database.port;
let dbUser = config.database.user;
let dbPassword = config.database.password;
let dbName = config.database.name;

// 示例：针对 Docker 环境的调整逻辑
if (config.database.host === 'mysql' && process.env.NODE_ENV !== 'production') {
  console.log('检测到Docker环境的数据库配置，可能修正为本地测试参数');
  // dbHost = 'localhost'; // 实际调整逻辑
}

const sequelize = new Sequelize(
  dbName,
  dbUser,
  dbPassword,
  {
    host: dbHost,
    port: dbPort,
    dialect: config.database.dialect || 'mysql',
    logging: config.database.logging === true ? console.log : false, // 简化处理
    pool: config.database.pool || { max: 5, min: 0, acquire: 30000, idle: 10000 },
    define: { // 实际的 define 配置
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      // underscored: true, // 根据实际情况添加
      // timestamps: true,
      // paranoid: true
    },
    // ... 其他实际 Sequelize 配置 ...
  }
);

const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功。');
    return true;
  } catch (error) {
    console.error('无法连接到数据库:', error);
    return false;
  }
};

module.exports = {
  sequelize,
  testConnection
};
```

### Sequelize ORM
AIBUBB系统使用Sequelize ORM操作数据库。数据库连接的初始化和管理是一个复杂过程，由 `backend/infrastructure/persistence/DatabaseConnectionManager.ts` 负责。该管理器支持更高级的功能，如主从数据库连接的读写分离和数据分片（如果启用）。它从 `backend/config/database-scalability.ts` 获取详细配置来创建和管理不同的 Sequelize 连接实例。简单的、单一的 `sequelize` 实例创建如标准示例中所示可能不完全适用，而是通过该管理器获取特定用途的连接。

Sequelize是AIBUBB系统使用的ORM（对象关系映射）框架，它提供了一种优雅的方式来操作数据库：
- **模型定义**：通过JavaScript/TypeScript类定义数据库模型
- **查询构建**：提供流畅的API来构建SQL查询
- **关联管理**：支持一对一、一对多、多对多等关联关系
- **事务支持**：提供事务管理功能，确保数据一致性
- **迁移支持**：支持数据库结构的版本控制和迁移

在AIBUBB系统中，Sequelize的使用方式如下：

#### 数据库连接
*(这部分描述已在上面的 "Sequelize ORM" 概述中提及，指出了 DatabaseConnectionManager.ts 的作用。原文档的 sequelize.ts 示例不再适用，因此此处不提供旧的 sequelize.ts 代码块)*

#### 模型定义
Sequelize 模型定义在 `backend/models/` 目录下的 JavaScript 文件中 (例如 `exercise.model.js`)。模型通过 `sequelize.define('ModelName', attributes, options)` 方法进行定义。字段属性（如类型、是否允许为空、默认值）和表选项（如表名、时间戳、软删除）在此处指定。模型间的关联关系（如 `belongsTo`, `hasMany`, `belongsToMany`）通常在所有模型定义加载完毕后，在一个集中的地方（例如 `backend/models/index.js` 或专门的关联配置文件）进行设置，而不是在每个模型文件内部通过 `associate`静态方法定义。

```javascript
// backend/models/exercise.model.js
const { DataTypes } = require('sequelize');
// sequelize 实例通常从统一配置中引入，例如:
const { sequelize } = require('../config/database'); // 路径根据实际调整

const Exercise = sequelize.define('Exercise', { // 模型名首字母大写
  id: {
    type: DataTypes.INTEGER, // 实际类型
    primaryKey: true,
    autoIncrement: true,
    comment: '练习ID'
  },
  tag_id: { // 实际字段名
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联的标签ID'
  },
  title: {
    type: DataTypes.STRING(100), // 实际长度
    allowNull: false,
    comment: '练习标题'
  },
  description: { // 实际字段名 (替代文档中的 content)
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '练习描述'
  },
  expected_result: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '预期结果'
  },
  difficulty: { // 实际字段名
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'), // 实际枚举值
    defaultValue: 'beginner',
    comment: '难度级别'
  },
  // ... 其他实际字段定义 ...
  creator_id: {
    type: DataTypes.BIGINT, // 实际类型
    allowNull: true, // 注意：实际代码中为true，与文档中false不同
    // references: { model: 'users', key: 'id' } // 外键引用通常在关联设置时处理
  },
  status: {
    type: DataTypes.ENUM('draft', 'published', 'archived'), // 实际枚举值
    defaultValue: 'published', // 实际默认值
    comment: '状态'
  }
}, {
  tableName: 'exercise', // 实际表名 (单数)
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  deletedAt: 'deleted_at',
  paranoid: true, // 启用软删除
  // underscored: true, // 根据实际情况确认是否使用
  indexes: [ // 实际索引定义
    { name: 'idx_tag_id', fields: ['tag_id'] },
    // ... 其他索引
  ]
});

// 关联通常不在此文件中定义
// Example of how associations might be defined elsewhere (e.g., models/index.js):
// Exercise.belongsTo(models.User, { foreignKey: 'creator_id', as: 'creator' });
// Exercise.belongsToMany(models.Tag, { through: 'exercise_tags', foreignKey: 'exercise_id', otherKey: 'tag_id', as: 'tags' });

module.exports = Exercise;
```

#### 仓库实现
仓库的具体实现位于 `backend/infrastructure/persistence/repositories/` 下的相应领域子目录中，例如 `content/exercise/SequelizeExerciseRepository.ts`。这些实现类通常继承自一个通用的基类 (如 `RepositoryBase`) 并实现领域层定义的仓库接口。它们通过构造函数接收 Sequelize 模型实例和其他依赖（如工作单元、事件发布器）。领域对象与持久化模型之间的映射通过内部方法 (如 `toDomainModel`) 或专门的 Mapper 类完成。

```typescript
// backend/infrastructure/persistence/repositories/content/exercise/SequelizeExerciseRepository.ts
import { Exercise } from '../../../../../domain/models/content/exercise/Exercise';
import { ExerciseRepository } from '../../../../../domain/repositories/content/exercise/ExerciseRepository';
import { RepositoryBase } from '../../RepositoryBase'; // 实际基类路径
import { UnitOfWork } from '../../../UnitOfWork'; // 实际UnitOfWork路径
// ...其他导入

export class SequelizeExerciseRepository extends RepositoryBase<Exercise, number> implements ExerciseRepository {
  // sequelize, exerciseModel 等通过构造函数注入
  constructor(
    unitOfWork: UnitOfWork,
    // eventPublisher: EventPublisher, // 根据实际依赖添加
    private readonly sequelize: Sequelize, // 假设直接注入或通过ConnectionManager获取
    private readonly exerciseModel: any, // Sequelize模型，类型可能为 typeof YourSequelizeModel
    private readonly exerciseTagModel: any // 关联模型
  ) {
    super(unitOfWork /*, eventPublisher */);
  }

  async findById(id: number): Promise<Exercise | null> {
    const exerciseData = await this.exerciseModel.findByPk(id, {
      include: [ /* ... 关联加载逻辑 ... */ ]
    });
    if (!exerciseData) return null;
    return this.toDomainModel(exerciseData); // 假设的映射方法
  }

  // save 方法通常在基类中实现，或在此处覆盖
  // protected async doSave(exercise: Exercise): Promise<Exercise> { ... }

  private toDomainModel(exerciseData: any): Exercise {
    // 实际的映射逻辑，将 Sequelize 模型数据转换为领域模型
    // 例如: new Exercise({ id: exerciseData.id, title: exerciseData.title, ...tagsData })
    // 这部分与文档中的 ExerciseMapper 概念类似，但实现方式不同
    return new Exercise(
        exerciseData.id,
        exerciseData.title,
        exerciseData.description,
        exerciseData.expected_result,
        this.mapDbValueToDifficulty(exerciseData.difficulty), // 假设有此转换
        exerciseData.time_estimate_minutes,
        exerciseData.creator_id,
        this.mapDbValueToContentStatus(exerciseData.status), // 假设有此转换
        // ... 其他属性和关联数据映射
    );
  }
  // 其他映射辅助方法...
}
```

## 3.3 缓存：Redis

### Redis 概述
Redis是AIBUBB系统中使用的主要缓存解决方案，它是一个开源的内存数据结构存储系统，可用作数据库、缓存和消息中间件。Redis具有以下特点：

- **高性能**：基于内存操作，提供极高的读写速度
- **数据结构丰富**：支持字符串、哈希、列表、集合、有序集合等多种数据结构
- **持久化**：支持RDB和AOF两种持久化方式，确保数据不会丢失
- **原子操作**：所有操作都是原子性的，支持事务
- **分布式特性**：支持主从复制、哨兵模式和集群模式

### Redis在AIBUBB中的应用

#### 数据缓存
AIBUBB系统使用Redis缓存频繁访问的数据，减轻数据库负担：

- **API响应缓存**：缓存常用API的响应结果
- **查询结果缓存**：缓存复杂查询的结果集
- **对象缓存**：缓存用户、内容、标签等领域对象
- **计算结果缓存**：缓存统计数据和计算密集型操作的结果

#### 会话存储
Redis用于存储用户会话和认证信息：

- **JWT令牌存储**：存储已发放的JWT令牌，支持令牌撤销
- **刷新令牌管理**：管理用于刷新访问令牌的刷新令牌
- **用户会话数据**：存储用户的会话状态和临时数据

#### 分布式锁
Redis实现分布式环境下的锁机制，确保并发操作的安全：

- **资源锁定**：在修改共享资源时锁定资源，防止并发修改
- **操作去重**：防止重复提交和重复处理
- **定时任务协调**：确保定时任务在分布式环境中只执行一次

#### 计数器和限流
Redis用于实现各种计数器和限流机制：

- **API限流**：限制用户API调用频率，防止滥用
- **内容计数**：记录点赞、评论、浏览等计数
- **用户行为统计**：统计用户活跃度、完成任务数等

#### 实时排行榜
Redis的有序集合(Sorted Set)用于实现各种实时排行榜：

- **热门内容排行**：根据点赞、评论、浏览等指标排序
- **用户活跃度排行**：根据用户活动频率和贡献排序
- **学习进度排行**：根据学习完成度和速度排序

### 实现方式

#### Redis客户端配置
Redis 客户端的配置和初始化在 `backend/config/redis.js` 中完成。该文件使用 `redis` 包的 `createClient` 方法创建客户端实例，并包含连接事件处理、自定义重试策略以及连接和关闭辅助函数。配置参数（如URL、密码）从通用配置文件中读取。

```javascript
// backend/config/redis.js
const { createClient } = require('redis');
const config = require('./config'); // 假设主配置文件
const logger = require('./logger'); // 假设日志模块

const redisClient = createClient({
  url: config.redis.url,
  password: config.redis.password || undefined,
  socket: {
    reconnectStrategy: (retries) => {
      // ... 实际的重试逻辑 ...
      if (retries >= 3) return new Error('...');
      return Math.min(retries * 100, 1000);
    }
  }
});

redisClient.on('connect', () => logger.info('Redis ...'));
// ... 其他事件监听 ...

const connectRedis = async () => { /* ... 实际连接逻辑 ... */ };
const closeRedis = async () => { /* ... 实际关闭逻辑 ... */ };

module.exports = { redisClient, connectRedis, closeRedis };
```

#### 缓存服务封装
AIBUBB系统中Redis缓存的集成方式可能不完全依赖于一个独立的、通用的 `CacheService` 类。虽然存在Redis客户端配置 (`backend/config/redis.js`)，但缓存的具体应用逻辑（如缓存读取、写入、失效）可能更多地通过装饰器模式 (例如 `backend/infrastructure/persistence/cache/CachedRepositoryDecorator.ts`) 应用于数据仓库层，或者通过特定的缓存策略 (`MultiLevelCacheStrategy.ts`) 实现。这意味着缓存操作对应用服务层可能是透明的，或者通过仓库接口间接进行。

*(由于实际代码中可能不存在独立的 CacheService 或形式完全不同，此处不提供 CacheService 的代码示例，上述描述已指出差异)*

#### 缓存策略实现
在应用服务层 (如 `ExerciseApplicationService`) 中，可能不会直接看到对通用 `CacheService` 的显式调用。如果缓存是通过仓库装饰器或类似的机制实现的，那么应用服务在调用仓库方法时，缓存的读取和写入会自动发生，对应用服务本身是透明的。

*(由于缓存策略与 CacheService 紧密相关，且其具体实现依赖于实际架构，此处不提供 ExerciseApplicationService 中缓存策略的示例代码)*

## 3.4 消息与事件：领域事件机制

### 领域事件概述
领域事件是AIBUBB系统中实现模块间松耦合通信的核心机制，它表示在领域中发生的事件。领域事件具有以下特点：

- **事实记录**：领域事件记录了系统中发生的重要业务事实
- **松耦合通信**：通过事件实现不同领域模块之间的通信，降低模块间的直接依赖
- **异步处理**：支持异步处理模式，提高系统响应性和弹性
- **可扩展性**：新功能可以通过订阅现有事件来实现，而不需要修改现有代码

在AIBUBB系统中，领域事件用于以下场景：

- **内容创建通知**：当新的练习、笔记或学习计划被创建时发送通知
- **成就触发**：当用户完成特定条件时触发成就解锁
- **统计数据更新**：当用户行为发生时更新统计数据
- **实时通知**：通过WebSocket向前端发送实时通知

### 事件总线实现

#### 事件定义
领域事件在AIBUBB系统中通过类继承的方式定义。具体事件类（如 `ExerciseCreatedEvent`）继承自领域事件基类。在实际实现中，这些事件的构造函数通常接收聚合根的ID和与事件相关的关键属性，而不是直接传递整个聚合根对象，以促进更好的解耦。

```typescript
// backend/domain/events/domain-event.ts (或 domain-event-base.ts)
export abstract class DomainEvent { // 或 DomainEventBase
  public readonly occurredOn: Date;
  public readonly eventId: string;

  constructor(
    public readonly aggregateId: string,
    public readonly eventType: string
  ) {
    this.occurredOn = new Date();
    this.eventId = this.generateId(); // 假设有此方法或在构造中生成
  }

  private generateId(): string { // 示例性ID生成
    return `${this.eventType}-${this.aggregateId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

具体事件类继承自基类：

```typescript
// backend/domain/events/content/exercise/ExerciseCreatedEvent.ts (假设路径和命名)
// import { DomainEvent } from '../DomainEvent'; // 或 '../DomainEventBase'
// import { Exercise } from '../../models/content/exercise/Exercise'; // 实际领域模型路径

// export class ExerciseCreatedEvent extends DomainEvent {
//   public readonly title: string;
//   // ... other relevant properties from the exercise
//   public readonly creatorId: string;
//   public readonly difficulty: string; // Or a Difficulty enum/type
//   public readonly isPublic: boolean;


//   constructor(
//     aggregateId: string, // Exercise ID
//     title: string,
//     difficulty: string,
//     creatorId: string,
//     isPublic: boolean
//     // ... other necessary data for the event
//   ) {
//     super(aggregateId, 'ExerciseCreated');
//     this.title = title;
//     this.difficulty = difficulty;
//     this.creatorId = creatorId;
//     this.isPublic = isPublic;
//   }
// }
```
*(注: 上述 `ExerciseCreatedEvent` 代码是基于观察到的 `ExercisePublishedEvent` 和文档的 `ExerciseCreatedEvent` 结合推断的修正。实际的 `ExerciseCreatedEvent` 需要具体查看其定义。)*


#### 事件发布
事件发布通过事件总线接口 (`EventBus`) 和其实现 (`EventBusImpl` 或 `EnhancedEventBusImpl`) 完成。其功能描述（发布、订阅、取消订阅）与实际一致。

```typescript
// backend/infrastructure/events/event-bus.interface.ts
import { DomainEvent } from '../../domain/events/domain-event'; // 调整路径

export interface EventBus {
  publish(event: DomainEvent): Promise<void>;
  subscribe(eventType: string, callback: (event: DomainEvent) => Promise<void>): void;
  unsubscribe(eventType: string, callback: (event: DomainEvent) => Promise<void>): void;
}
```

```typescript
// backend/infrastructure/events/event-bus.impl.ts (或 EnhancedEventBusImpl.ts)
import { injectable, inject } from 'inversify'; // 假设使用InversifyJS
import { EventBus } from './event-bus.interface';
import { DomainEvent } from '../../domain/events/domain-event'; // 调整路径
import { Logger } from '../../utils/logger'; // 假设Logger路径

@injectable()
export class EventBusImpl implements EventBus {
  private subscribers: Map<string, Array<(event: DomainEvent) => Promise<void>>> = new Map();

  constructor(@inject('Logger') private logger: Logger) {} // 假设Logger注入

  async publish(event: DomainEvent): Promise<void> {
    const eventType = event.eventType;
    this.logger.info(`Publishing event: ${eventType}`, { eventId: event.eventId, aggregateId: event.aggregateId });

    const callbacks = this.subscribers.get(eventType) || [];

    const publishPromises = callbacks.map(async (callback) => {
      try {
        await callback(event);
      } catch (error) {
        this.logger.error(`Error handling event ${eventType}`, { error, eventId: event.eventId });
        // 在生产环境中，这里可能需要实现重试机制或死信队列
      }
    });

    await Promise.all(publishPromises);
    this.logger.info(`Event published: ${eventType}`, { eventId: event.eventId, subscribersCount: callbacks.length });
  }

  subscribe(eventType: string, callback: (event: DomainEvent) => Promise<void>): void {
    if (!this.subscribers.has(eventType)) {
      this.subscribers.set(eventType, []);
    }
    // Ensure callback is not already subscribed to prevent duplicates
    if (!this.subscribers.get(eventType)!.includes(callback)) {
        this.subscribers.get(eventType)!.push(callback);
    }
    this.logger.info(`Subscribed to event: ${eventType}`);
  }

  unsubscribe(eventType: string, callback: (event: DomainEvent) => Promise<void>): void {
    if (!this.subscribers.has(eventType)) {
      return;
    }

    const callbacks = this.subscribers.get(eventType)!;
    const index = callbacks.indexOf(callback);

    if (index !== -1) {
      callbacks.splice(index, 1);
      this.logger.info(`Unsubscribed from event: ${eventType}`);
    }
  }
}
```

#### 事件发布者
事件发布者 (`EventPublisher` 接口和 `EventPublisherImpl` 或 `EnhancedEventPublisherImpl` 实现) 负责将领域事件持久化，然后通过事件总线发布。

```typescript
// backend/infrastructure/events/event-publisher.interface.ts
import { DomainEvent } from '../../domain/events/domain-event'; // 调整路径

export interface EventPublisher {
  publishEvent(event: DomainEvent): Promise<void>;
  publishEvents(events: DomainEvent[]): Promise<void>;
}
```

```typescript
// backend/infrastructure/events/event-publisher.impl.ts (或 EnhancedEventPublisherImpl.ts)
import { injectable, inject } from 'inversify'; // 假设使用InversifyJS
import { EventPublisher } from './event-publisher.interface';
import { EventBus } from './event-bus.interface';
import { DomainEvent } from '../../domain/events/domain-event'; // 调整路径
import { DatabaseEventStore } from './DatabaseEventStore'; // 调整路径，假设 DatabaseEventStore 在同级
import { Logger } from '../../utils/logger'; // 假设Logger路径

@injectable()
export class EventPublisherImpl implements EventPublisher {
  constructor(
    @inject('EventBus') private eventBus: EventBus, // 假设注入
    @inject('DatabaseEventStore') private eventStore: DatabaseEventStore, // 假设注入
    @inject('Logger') private logger: Logger // 假设注入
  ) {}

  async publishEvent(event: DomainEvent): Promise<void> {
    try {
      // 先持久化事件，确保事件不会丢失
      await this.eventStore.storeEvent(event);

      // 然后发布事件
      await this.eventBus.publish(event);
    } catch (error) {
      this.logger.error(`Failed to publish event: ${event.eventType}`, { error, eventId: event.eventId });
      // Consider re-throwing or specific error handling
      throw error;
    }
  }

  async publishEvents(events: DomainEvent[]): Promise<void> {
    if (!events || events.length === 0) return;

    try {
      // 先持久化所有事件
      // Note: Storing events might be done in a transaction with domain changes
      // depending on the Unit of Work pattern implementation.
      await this.eventStore.storeEvents(events);

      // 然后发布所有事件
      // Publishing can be done in parallel or sequentially based on requirements
      await Promise.all(events.map(event => this.eventBus.publish(event)));

    } catch (error) {
      this.logger.error(`Failed to publish events`, { error, eventsCount: events.length });
      // Consider re-throwing or specific error handling
      throw error;
    }
  }
}
```

#### 事件持久化
事件持久化通过 `backend/infrastructure/events/DatabaseEventStore.ts` 实现。此类负责将领域事件存储到数据库中（通常是一个名为 `domain_events` 的表）。其内部定义并使用了一个 Sequelize 模型（在代码中为 `EventModel`，原文档为 `DomainEventModel`）来与数据库交互。

```typescript
// backend/infrastructure/events/DatabaseEventStore.ts
import { injectable } from 'inversify'; // 假设使用InversifyJS
import { DomainEvent } from '../../domain/events/domain-event'; // 调整路径
import { sequelize } from '../database/sequelize'; // 假设 Sequelize 实例路径
// import { DomainEventModel } from '../database/models/domain-event.model'; // 原文档模型，下面用 EventModel
import { DataTypes, Model, Optional } from 'sequelize'; // Sequelize 类型

// 定义 EventModel 接口 (属性)
interface EventModelAttributes {
  id?: number; // Auto-incremented primary key
  event_id: string;
  aggregate_id: string;
  event_type: string;
  event_data: string; // JSON string
  occurred_on: Date;
  processed_at?: Date | null; // Optional: for tracking processing status
}

// 定义创建时的可选属性
interface EventModelCreationAttributes extends Optional<EventModelAttributes, 'id' | 'processed_at'> {}

// 定义 Sequelize 模型类
class EventModel extends Model<EventModelAttributes, EventModelCreationAttributes> implements EventModelAttributes {
  public id!: number;
  public event_id!: string;
  public aggregate_id!: string;
  public event_type!: string;
  public event_data!: string;
  public occurred_on!: Date;
  public processed_at?: Date | null;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// 初始化模型
EventModel.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    event_id: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true, // Event ID should be unique
    },
    aggregate_id: {
      type: DataTypes.STRING, // Or specific ID type like BIGINT if mapping to numeric IDs
      allowNull: false,
    },
    event_type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    event_data: {
      type: DataTypes.TEXT, // TEXT for potentially large JSON strings
      allowNull: false,
    },
    occurred_on: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    processed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize, // Sequelize instance
    tableName: 'domain_events', // Table name
    timestamps: true, // Enable Sequelize timestamps (createdAt, updatedAt)
    underscored: true, // Use snake_case for column names
    indexes: [
        { fields: ['aggregate_id'] },
        { fields: ['event_type'] },
        { fields: ['occurred_on'] }
    ]
  }
);


@injectable()
export class DatabaseEventStore {
  async storeEvent(event: DomainEvent): Promise<void> {
    await EventModel.create({ // 使用 EventModel
      event_id: event.eventId,
      aggregate_id: event.aggregateId,
      event_type: event.eventType,
      occurred_on: event.occurredOn,
      event_data: JSON.stringify(event) // Serialize the whole event object
    });
  }

  async storeEvents(events: DomainEvent[]): Promise<void> {
    if (!events || events.length === 0) return;
    const transaction = await sequelize.transaction(); // Sequelize instance

    try {
      const eventData = events.map(event => ({
        event_id: event.eventId,
        aggregate_id: event.aggregateId,
        event_type: event.eventType,
        occurred_on: event.occurredOn,
        event_data: JSON.stringify(event)
      }));

      await EventModel.bulkCreate(eventData, { transaction }); // 使用 EventModel
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      // Log error or handle as needed
      throw error;
    }
  }

  async getEventsByAggregateId(aggregateId: string): Promise<DomainEvent[]> {
    const eventModels = await EventModel.findAll({ // 使用 EventModel
      where: { aggregate_id: aggregateId },
      order: [['occurred_on', 'ASC']]
    });
    // Deserialize event_data back to DomainEvent objects
    return eventModels.map(model => JSON.parse(model.event_data) as DomainEvent);
  }

  async getEventsByType(eventType: string, limit?: number, offset?: number): Promise<DomainEvent[]> {
    const options: any = {
      where: { event_type: eventType },
      order: [['occurred_on', 'ASC']]
    };

    if (limit) options.limit = limit;
    if (offset) options.offset = offset;

    const eventModels = await EventModel.findAll(options); // 使用 EventModel
    return eventModels.map(model => JSON.parse(model.event_data) as DomainEvent);
  }

  // Optional: Method to mark events as processed
  async markEventAsProcessed(eventId: string): Promise<void> {
    await EventModel.update({ processed_at: new Date() }, { where: { event_id: eventId }});
  }

  // Optional: Method to retrieve unprocessed events
  async getUnprocessedEvents(limit: number = 100): Promise<DomainEvent[]> {
      const eventModels = await EventModel.findAll({
          where: { processed_at: null },
          order: [['occurred_on', 'ASC']],
          limit: limit
      });
      return eventModels.map(model => JSON.parse(model.event_data) as DomainEvent);
  }
}
```

### 事件处理器

#### 事件处理器基类
事件处理器基类 (`EventHandlerBase.ts`) 提供了处理领域事件的通用功能。

```typescript
// backend/infrastructure/events/handlers/event-handler.base.ts
import { injectable, inject } from 'inversify'; // 假设使用InversifyJS
import { DomainEvent } from '../../../domain/events/domain-event'; // 调整路径
import { Logger } from '../../../utils/logger'; // 假设Logger路径
import { WebSocketService } from '../../websocket/websocket.service'; // 假设WebSocketService路径

@injectable()
export abstract class EventHandlerBase<T extends DomainEvent> {
  constructor(
    @inject('Logger') protected logger: Logger, // 假设注入
    @inject('WebSocketService') protected webSocketService: WebSocketService // 假设注入
  ) {}

  // 处理事件的主要方法
  async handle(event: T): Promise<void> {
    try {
      this.logger.info(`Handling event: ${event.eventType}`, { eventId: event.eventId, aggregateId: event.aggregateId });

      // 处理事件的业务逻辑
      await this.processEvent(event);

      // 发送通知（如果需要）
      await this.sendNotification(event);

      this.logger.info(`Event handled successfully: ${event.eventType}`, { eventId: event.eventId, aggregateId: event.aggregateId });
    } catch (error) {
      this.logger.error(`Error handling event: ${event.eventType}`, { error, eventId: event.eventId, aggregateId: event.aggregateId });
      // Decide on error handling strategy: rethrow, log, move to dead-letter queue, etc.
      throw error; // Or handle more gracefully
    }
  }

  // 子类必须实现的方法，处理事件的业务逻辑
  protected abstract processEvent(event: T): Promise<void>;

  // 子类可以重写的方法，发送通知
  protected async sendNotification(event: T): Promise<void> {
    // 默认实现为空，子类可以重写
    this.logger.debug(`No specific notification to send for event: ${event.eventType}`, { eventId: event.eventId });
  }

  // 帮助方法，发送用户通知
  protected async sendUserNotification(userId: string, notificationType: string, data: any): Promise<void> {
    try {
      // Ensure WebSocketService is available and connected if necessary
      await this.webSocketService.publishToUser(userId, {
        type: notificationType,
        payload: data, // Consistent payload structure
        timestamp: new Date().toISOString()
      });

      this.logger.info(`Notification sent to user: ${userId}`, { notificationType, userId });
    } catch (error) {
      this.logger.error(`Failed to send notification to user: ${userId}`, { error, notificationType, userId });
    }
  }

  // 帮助方法，发送广播通知
  protected async sendBroadcastNotification(notificationType: string, data: any): Promise<void> {
    try {
      // Ensure WebSocketService is available and connected if necessary
      await this.webSocketService.publishToAll({
        type: notificationType,
        payload: data, // Consistent payload structure
        timestamp: new Date().toISOString()
      });

      this.logger.info(`Broadcast notification sent`, { notificationType });
    } catch (error)
      this.logger.error(`Failed to send broadcast notification`, { error, notificationType });
    }
}
```

#### 具体事件处理器实现
具体事件处理器（例如 `ExerciseCreatedEventHandler`）继承自 `EventHandlerBase.ts`，并实现特定事件的处理逻辑。这些处理器通过依赖注入容器进行管理，并在 `EventHandlerRegistry.ts` 中注册到相应的事件类型。虽然文档中可能将处理器示例放在 `handlers/` 子目录下，实际项目中它们可能根据领域或功能模块组织在不同的路径下，但最终都会被DI容器发现并由注册表进行订阅。

```typescript
// backend/infrastructure/events/handlers/exercise-created.handler.ts (或类似路径)
import { injectable, inject } from 'inversify'; // 假设使用InversifyJS
import { EventHandlerBase } from './event-handler.base';
import { ExerciseCreatedEvent } from '../../../domain/events/content/exercise/ExerciseCreatedEvent'; // 修正路径和事件名
import { StatisticsService } from '../../services/statistics.service'; // 假设服务路径
import { AchievementService } from '../../services/achievement.service'; // 假设服务路径
import { Logger } from '../../../utils/logger'; // 假设Logger路径
import { WebSocketService } from '../../websocket/websocket.service'; // 假设WebSocketService路径


@injectable()
export class ExerciseCreatedEventHandler extends EventHandlerBase<ExerciseCreatedEvent> {
  constructor(
    @inject('Logger') logger: Logger, // 确保类型正确
    @inject('WebSocketService') webSocketService: WebSocketService, // 确保类型正确
    @inject('StatisticsService') private statisticsService: StatisticsService,
    @inject('AchievementService') private achievementService: AchievementService
  ) {
    super(logger, webSocketService);
  }

  // 实现事件处理逻辑
  protected async processEvent(event: ExerciseCreatedEvent): Promise<void> {
    this.logger.info(`Processing ExerciseCreatedEvent for exercise ID: ${event.aggregateId}`);

    // 1. 更新统计数据
    if (event.creatorId) {
      await this.statisticsService.incrementUserStat(event.creatorId, 'exercisesCreated');
      this.logger.info(`Incremented exercisesCreated stat for user: ${event.creatorId}`);
    }

    // 2. 更新内容统计
    await this.statisticsService.incrementContentStat('exercises', 'total');
    this.logger.info(`Incremented total exercises stat`);


    // 3. 检查成就条件
    if (event.creatorId) {
      await this.achievementService.checkAndUnlockAchievement(
        event.creatorId,
        'CONTENT_CREATOR_EXERCISE', // More specific achievement type
        { contentType: 'exercise', contentId: event.aggregateId, title: event.title }
      );
      this.logger.info(`Checked achievements for user: ${event.creatorId} for new exercise`);
    }
  }

  // 实现通知发送
  protected async sendNotification(event: ExerciseCreatedEvent): Promise<void> {
    // 1. 如果是公开练习，发送广播通知
    if (event.isPublic) { // Assuming isPublic is a property of ExerciseCreatedEvent
      await this.sendBroadcastNotification('NEW_PUBLIC_EXERCISE', {
        exerciseId: event.aggregateId,
        title: event.title,
        difficulty: event.difficulty // Assuming difficulty is a property
      });
      this.logger.info(`Sent broadcast notification for new public exercise: ${event.aggregateId}`);
    }

    // 2. 如果有创建者，发送个人通知
    if (event.creatorId) {
      await this.sendUserNotification(event.creatorId, 'EXERCISE_CREATED_SUCCESS', { // More specific notification type
        exerciseId: event.aggregateId,
        title: event.title
      });
      this.logger.info(`Sent personal notification to creator: ${event.creatorId} for exercise: ${event.aggregateId}`);
    }
  }
}
```

#### 事件处理器注册
事件处理器注册类 (`EventHandlerRegistry.ts`) 负责将所有事件处理器注册到事件总线。

```typescript
// backend/infrastructure/events/handlers/event-handler-registry.ts
import { injectable, inject, multiInject } from 'inversify'; // 假设使用InversifyJS
import { EventBus } from '../event-bus.interface';
import { EventHandlerBase } from './event-handler.base';
import { ExerciseCreatedEventHandler } from './exercise-created.handler'; // 假设路径
// Import other handlers as they are created
// import { NoteCreatedEventHandler } from './note-created.handler';
// import { LearningPlanCreatedEventHandler } from './learning-plan-created.handler';
import { Logger } from '../../../utils/logger'; // 假设Logger路径
import { DomainEvent } from '../../../domain/events/domain-event'; // 引入 DomainEvent

// Define a type for event handlers for clarity
type AnyEventHandler = EventHandlerBase<any>;

@injectable()
export class EventHandlerRegistry {
  private handlers: Map<string, AnyEventHandler[]> = new Map(); // Store arrays of handlers

  constructor(
    @inject('EventBus') private eventBus: EventBus,
    @inject('Logger') private logger: Logger,
    // Use multiInject if handlers are bound with a common identifier,
    // or inject them individually if bound with unique identifiers.
    // For this example, let's assume individual injection for clarity,
    // though multiInject might be more scalable.
    @inject('ExerciseCreatedEventHandler') private exerciseCreatedHandler: ExerciseCreatedEventHandler
    // @inject('NoteCreatedEventHandler') private noteCreatedHandler: NoteCreatedEventHandler,
    // @inject('LearningPlanCreatedEventHandler') private learningPlanCreatedHandler: LearningPlanCreatedEventHandler
    // ... other event handlers
  ) {}

  // 注册所有事件处理器
  public registerHandlers(): void {
    this.logger.info('Registering event handlers...');

    // 注册各种事件处理器
    // Example: Registering ExerciseCreatedEventHandler for "ExerciseCreated" event type
    // The eventType string should match the eventType property of the DomainEvent
    this.registerHandler('ExerciseCreated', this.exerciseCreatedHandler);
    // this.registerHandler('NoteCreated', this.noteCreatedHandler);
    // this.registerHandler('LearningPlanCreated', this.learningPlanCreatedHandler);
    // ... Register other handlers for their respective event types

    this.logger.info(`Finished registering event handlers. Total event types with handlers: ${this.handlers.size}`);
  }

  // 注册单个事件处理器到特定事件类型
  private registerHandler(eventType: string, handler: AnyEventHandler): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    this.handlers.get(eventType)!.push(handler);

    // 将处理器订阅到事件总线
    // This subscription logic might be slightly different depending on the EventBusImpl
    // It's common for the EventBus to manage subscribers internally based on event type.
    // The registry informs the bus about handlers for specific event types.
    // The provided EventBusImpl example subscribes a callback.
    // Here, we ensure that when an event of 'eventType' is published,
    // the 'handle' method of the registered handler is called.

    this.eventBus.subscribe(eventType, async (event: DomainEvent) => {
        // It's important that the event passed to handler.handle is correctly typed.
        // The EventBus might pass a generic DomainEvent.
        // The handler's `handle` method expects a specific event type (T extends DomainEvent).
        // This simple subscription assumes the event object from the bus is compatible.
        // More robust solutions might involve type checking or specific event dispatching logic.
        await handler.handle(event); // handler.handle will be called with the specific event type
    });

    this.logger.info(`Registered handler ${handler.constructor.name} for event type: ${eventType}`);
  }

  // 获取特定事件类型的所有处理器 ( illustrative, might not be directly used by EventBus )
  public getHandlersForEventType(eventType: string): AnyEventHandler[] | undefined {
    return this.handlers.get(eventType);
  }

  // 获取所有已注册的事件类型
  public getRegisteredEventTypes(): string[] {
    return Array.from(this.handlers.keys());
  }
}
```

在应用启动时注册事件处理器：

```typescript
// backend/index.ts (或应用的入口文件如 app.ts, server.ts)
// import { container } from './infrastructure/di'; // 假设DI容器入口
// import { EventHandlerRegistry } from './infrastructure/events/handlers/event-handler-registry'; // 调整路径

// async function startApplication() {
//   // ... 其他应用初始化代码 (数据库连接, Express app setup etc.)

//   // 获取DI容器 (如果使用)
//   // const appContainer = container; // Or however you access your DI container

//   // 注册事件处理器
//   // const eventHandlerRegistry = appContainer.get<EventHandlerRegistry>('EventHandlerRegistry'); // Or by Type
//   // eventHandlerRegistry.registerHandlers();

//   // ... 启动服务器等
//   // app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
// }

// startApplication().catch(error => {
//   console.error("Failed to start application:", error);
//   process.exit(1);
// });
```

## 3.5 认证与授权：JWT

### JWT认证机制
JSON Web Token (JWT) 是AIBUBB系统中实现用户认证和会话管理的主要机制。JWT是一种紧凑的、自包含的方式，用于在各方之间以JSON对象安全地传输信息。

JWT由三部分组成，用点分隔：
1.  **头部(Header)**：包含令牌类型和使用的签名算法
2.  **负载(Payload)**：包含声明（用户数据和元数据）
3.  **签名(Signature)**：用于验证令牌的真实性

在AIBUBB系统中，JWT的主要优势包括：
- **无状态**：服务器不需要存储会话状态，提高了可扩展性
- **跨域**：可以在不同的域之间使用
- **性能**：减少了数据库查询，提高了性能
- **解耦**：前端和后端可以完全解耦

### 认证流程

#### 用户登录
用户认证相关的HTTP接口处理逻辑位于认证控制器中，例如 `backend/controllers/user/AuthController.ts` (或 `EnhancedAuthController.ts`)。该控制器负责处理用户登录、注册（如果适用）、令牌刷新等请求。

```typescript
// backend/controllers/user/AuthController.ts (或 EnhancedAuthController.ts)
import { Request, Response, NextFunction } from 'express';
import { injectable, inject } from 'inversify'; // 假设使用InversifyJS
import { AuthService } from '../../application/services/auth/AuthService'; // 修正路径
import { LoginCommand } from '../../application/commands/auth/LoginCommand'; // 修正路径
import { RefreshTokenCommand } from '../../application/commands/auth/RefreshTokenCommand'; // 假设的刷新命令
import { apiResponse } from '../../common/utils/apiResponse'; // 修正路径

@injectable()
export class AuthController { // 或 EnhancedAuthController
  constructor(
    @inject('AuthService') private authService: AuthService // 假设注入名称
  ) {}

  async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email, password } = req.body;

      const command = new LoginCommand({
        email,
        password,
        ipAddress: req.ip, // Express req.ip
        userAgent: req.headers['user-agent'] || ''
      });

      const result = await this.authService.login(command);

      // 在实际代码中，AuthService.login 可能直接抛出错误，或者返回包含错误信息的对象
      if (!result.success || !result.data) { // 假设 result 结构
        // 根据实际错误处理调整
        return apiResponse.unauthorized(res, { message: result.message || 'Login failed' });
      }

      return apiResponse.success(res, {
        accessToken: result.data.accessToken,
        refreshToken: result.data.refreshToken,
        expiresIn: result.data.expiresIn,
        user: result.data.user // 假设返回用户信息
      });
    } catch (error) {
      next(error); // 交给全局错误处理器
    }
  }

  async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return apiResponse.badRequest(res, { message: 'Refresh token is required' });
      }

      const command = new RefreshTokenCommand({ // 假设的命令
          refreshToken,
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'] || ''
      });

      const result = await this.authService.refreshToken(command); // 假设 refreshToken 方法

      if (!result.success || !result.data) {
        return apiResponse.unauthorized(res, { message: result.message || 'Failed to refresh token' });
      }

      return apiResponse.success(res, {
        accessToken: result.data.accessToken,
        refreshToken: result.data.newRefreshToken, // 可能返回新的刷新令牌
        expiresIn: result.data.expiresIn
      });
    } catch (error) {
      next(error);
    }
  }
  // 其他认证相关方法...
}
```

#### 令牌生成
JWT的生成和验证逻辑封装在JWT服务中，例如 `backend/infrastructure/services/JwtService.ts` (或其子目录 `security/` 下)。该服务负责创建访问令牌和刷新令牌，并可能与缓存服务交互以支持令牌撤销。

```typescript
// backend/infrastructure/services/JwtService.ts (或 services/security/JwtService.ts)
import { injectable, inject } from 'inversify'; // 假设使用InversifyJS
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { CacheService } from '../cache/CacheService'; // 假设 CacheService 路径
import { User } from '../../domain/models/user/User'; // 假设 User 领域模型路径
import { jwtConfig } from '../../config/auth-config'; // 假设 jwt 配置路径 (auth-config.ts 或 jwt.ts)
import { Logger } from '../../common/logging/Logger'; // 假设 Logger 路径

@injectable()
export class JwtService {
  constructor(
    @inject('CacheService') private cacheService: CacheService, // 假设注入
    @inject('Logger') private logger: Logger // 假设注入
  ) {}

  async generateTokens(user: User): Promise<{ accessToken: string; refreshToken: string; expiresIn: number; tokenId: string }> {
    // 生成唯一的令牌标识符 (JTI - JWT ID)
    const tokenId = uuidv4();

    // 创建访问令牌负载
    const accessTokenPayload = {
      sub: user.id.value, // 使用领域模型的ID (假设是值对象)
      email: user.email.value, // 使用领域模型的Email (假设是值对象)
      roles: user.roles.map(role => role.name), // 假设 User 有 roles 属性
      jti: tokenId, // JWT ID, 用于撤销等操作
      // aud: jwtConfig.audience, // Audience
      // iss: jwtConfig.issuer    // Issuer
    };

    // 生成访问令牌
    const accessToken = jwt.sign(accessTokenPayload, jwtConfig.secret, { // 使用 jwtConfig
      expiresIn: jwtConfig.accessTokenExpiresInSeconds, // 使用 jwtConfig
      // issuer: jwtConfig.issuer, // 已经在 payload 中指定或此处指定
      // audience: jwtConfig.audience // 已经在 payload 中指定或此处指定
    });

    // 创建刷新令牌负载
    const refreshTokenPayload = {
      sub: user.id.value,
      jti: tokenId, // 关联到同一个 JTI，或使用独立的 JTI for refresh token
      // iss: jwtConfig.issuer,
      // aud: jwtConfig.audience
    };

    // 生成刷新令牌
    const refreshToken = jwt.sign(
      refreshTokenPayload,
      jwtConfig.refreshSecret, // 使用不同的密钥
      {
        expiresIn: jwtConfig.refreshTokenExpiresInSeconds // 使用 jwtConfig
      }
    );

    // 示例：将令牌信息存储在Redis中，支持令牌撤销 (可选，根据实际实现)
    // key 可以是 `token:${tokenId}` 或 `user:${user.id.value}:token:${tokenId}`
    // value 可以是 { valid: true, userId: user.id.value }
    // TTL 应与令牌的有效期一致
    const accessTokenKey = `jwt:access_token:${tokenId}`;
    await this.cacheService.set(
      accessTokenKey,
      JSON.stringify({ userId: user.id.value, valid: true }), // Store as JSON string
      jwtConfig.accessTokenExpiresInSeconds
    );
    this.logger.info(`Access token stored in cache for JTI: ${tokenId}`);

    // 刷新令牌也可以有类似的缓存策略
    const refreshTokenKey = `jwt:refresh_token:${tokenId}`; // 或使用刷新令牌自身的JTI
     await this.cacheService.set(
      refreshTokenKey,
      JSON.stringify({ userId: user.id.value, valid: true }),
      jwtConfig.refreshTokenExpiresInSeconds
    );
    this.logger.info(`Refresh token stored in cache for JTI related to: ${tokenId}`);


    return {
      accessToken,
      refreshToken,
      expiresIn: jwtConfig.accessTokenExpiresInSeconds,
      tokenId // 返回tokenId，客户端可能需要它来进行某些操作（如注销特定会话）
    };
  }

  async verifyAccessToken(token: string): Promise<any | null> { // 返回类型根据实际payload调整
    try {
      const decoded = jwt.verify(token, jwtConfig.secret, {
        // issuer: jwtConfig.issuer, // Enable if issuer check is needed
        // audience: jwtConfig.audience, // Enable if audience check is needed
      });
      return decoded as any; // 类型断言，或使用更具体的类型
    } catch (error) {
      this.logger.error('Invalid access token', { error: error.message, token });
      return null;
    }
  }

  async verifyRefreshToken(token: string): Promise<any | null> {
      try {
          const decoded = jwt.verify(token, jwtConfig.refreshSecret, {
              // issuer: jwtConfig.issuer,
              // audience: jwtConfig.audience,
          });
          return decoded as any;
      } catch (error) {
          this.logger.error('Invalid refresh token', { error: error.message, token });
          return null;
      }
  }


  // 示例: 撤销令牌 (需要 tokenId 或 jti)
  async revokeToken(tokenId: string): Promise<void> {
    const accessTokenKey = `jwt:access_token:${tokenId}`;
    const refreshTokenKey = `jwt:refresh_token:${tokenId}`; // 假设JTI相同或关联

    // 从缓存中删除或标记为无效
    await this.cacheService.delete(accessTokenKey);
    await this.cacheService.delete(refreshTokenKey); // 或更新为 { valid: false }
    this.logger.info(`Token (and related refresh token) revoked for JTI: ${tokenId}`);
  }
}
```

#### 令牌验证
用于保护路由、验证JWT令牌的认证中间件主要在 `backend/middlewares/enhanced-auth.middleware.js` 文件中定义和导出 (例如 `authMiddleware`)。它负责从请求中提取令牌，使用JWT服务进行验证，检查令牌是否有效（例如未被撤销），并将用户信息附加到请求对象上供后续处理程序使用。

```typescript
// backend/middlewares/enhanced-auth.middleware.js (实际是JS文件, 但这里用TS示意)
import { Request, Response, NextFunction } from 'express';
// import jwt from 'jsonwebtoken'; // JwtService 内部会使用
import { container } from '../infrastructure/di/container'; // 假设DI容器路径
import { JwtService } from '../infrastructure/services/JwtService'; // 假设JwtService路径
import { CacheService } from '../infrastructure/cache/CacheService'; // 假设CacheService路径
// import { UserRepository } from '../domain/repositories/user/UserRepository'; // 根据实际用户加载方式
import { Logger } from '../common/logging/Logger'; // 假设Logger路径
// import { jwtConfig } from '../config/auth-config'; // JwtService 内部会使用其配置

// 扩展Express的Request类型以包含user属性
declare global {
  namespace Express {
    interface Request {
      user?: { // 根据实际附加到req.user的信息调整
        id: string; // 或 number
        email: string;
        roles: string[];
        jti?: string; // JWT ID
      };
    }
  }
}

export const authMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const logger = container.get<Logger>('Logger'); // 获取Logger实例
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logger.warn('Authentication attempt without token or invalid format');
      res.status(401).json({ message: 'Unauthorized: No token provided or invalid format' });
      return;
    }

    const token = authHeader.split(' ')[1];

    const jwtService = container.get<JwtService>('JwtService'); // 获取JwtService实例
    const decodedPayload = await jwtService.verifyAccessToken(token);

    if (!decodedPayload || !decodedPayload.sub || !decodedPayload.jti) { // 检查关键字段
      logger.warn('Invalid access token or missing required payload fields', { token });
      res.status(401).json({ message: 'Unauthorized: Invalid token' });
      return;
    }

    // 检查令牌是否已被撤销 (通过JTI)
    const cacheService = container.get<CacheService>('CacheService');
    const cacheKey = `jwt:access_token:${decodedPayload.jti}`;
    const cachedTokenInfo = await cacheService.get(cacheKey);

    // 如果缓存中不存在，或标记为无效，则认为令牌已撤销或过期
    // (注意：如果缓存中没有，也可能是因为缓存策略或TTL导致，需结合具体实现判断)
    // 一个更严格的检查是确保 cachedTokenInfo 存在且 valid 字段为 true
    if (!cachedTokenInfo) { // 简化示例：假设不存在即无效
        logger.warn(`Token JTI ${decodedPayload.jti} not found in cache or marked invalid.`);
        res.status(401).json({ message: 'Unauthorized: Token has been revoked or expired' });
        return;
    }
    // const tokenState = JSON.parse(cachedTokenInfo); // 如果存的是JSON字符串
    // if (!tokenState || !tokenState.valid) {
    //    ...
    // }


    // 附加用户信息到请求对象
    // 实际项目中，可能需要根据 decodedPayload.sub (userId) 从数据库或用户服务获取完整的用户信息
    // const userRepository = container.get<UserRepository>('UserRepository');
    // const userEntity = await userRepository.findById(new UserId(decodedPayload.sub));
    // if (!userEntity) { ... }
    req.user = {
      id: decodedPayload.sub, // 用户ID
      email: decodedPayload.email, // 从payload获取
      roles: decodedPayload.roles || [], // 从payload获取
      jti: decodedPayload.jti // 令牌ID
    };

    logger.info(`User ${req.user.id} authenticated via JWT (JTI: ${req.user.jti})`);
    next();
  } catch (error) { // 这个catch主要处理 verifyAccessToken 内部未捕获的 jwt 错误或DI错误
    logger.error('Authentication middleware error', { error: error.message, stack: error.stack });
    // jwt.verify 会抛出如 TokenExpiredError, JsonWebTokenError
    if (error.name === 'TokenExpiredError') {
      res.status(401).json({ message: 'Unauthorized: Token expired' });
    } else if (error.name === 'JsonWebTokenError') {
      res.status(401).json({ message: 'Unauthorized: Invalid token signature or structure' });
    } else {
      res.status(500).json({ message: 'Internal Server Error during authentication' });
    }
  }
};

// 可选的认证中间件 (如果令牌存在则验证，否则继续)
export const optionalAuthMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        // 如果提供了令牌，则执行完整的认证逻辑
        return authMiddleware(req, res, next);
    }
    //如果没有提供令牌，则直接进入下一个中间件，req.user 将为 undefined
    next();
};
```

#### 令牌刷新
*(令牌刷新的控制器和服务方法示例已在上面的 “用户登录” 和 “令牌生成” 部分的 AuthController 和 AuthService 中体现。)*

### 授权实现

#### 角色和权限定义
角色和权限定义在领域模型中，例如 `backend/domain/models/user/Role.ts` 和 `backend/domain/models/user/Permission.ts`。这些模型与文档描述的概念高度一致。

```typescript
// backend/domain/models/user/Role.ts
import { Permission } from './Permission'; // 假设 Permission 在同一目录或可访问

export class Role {
  constructor(
    public readonly id: string, // 或者 number, 根据实际ID类型
    public readonly name: string, // e.g., "ADMIN", "EDITOR", "VIEWER"
    public readonly description: string,
    public readonly permissions: Permission[] // 一组权限对象
  ) {}

  hasPermission(permissionName: string): boolean {
    return this.permissions.some(permission => permission.name === permissionName);
  }

  // 可以添加静态工厂方法或从持久化数据创建Role实例的方法
  // static create(id: string, name: string, description: string, permissions: Permission[]): Role {
  //   return new Role(id, name, description, permissions);
  // }
}
```
```typescript
// backend/domain/models/user/Permission.ts
export class Permission {
  constructor(
    public readonly id: string, // 或者 number
    public readonly name: string, // e.g., "exercise:create", "exercise:delete", "user:manage"
    public readonly description: string
  ) {}

  // static create(id: string, name: string, description: string): Permission {
  //   return new Permission(id, name, description);
  // }
}
```

#### 权限检查中间件
授权相关的中间件用于检查用户是否拥有执行特定操作或访问特定资源的权限。例如，`hasPermission` 逻辑（可能在 `backend/middlewares/enhanced-auth.middleware.js` 中）用于检查具体权限点，而 `adminMiddleware` (可能在 `backend/middlewares/admin.middleware.js` 中) 则用于限制只有管理员角色的用户才能访问。这些中间件通常在认证中间件之后执行。

```typescript
// backend/middlewares/authorization.middleware.ts (或 enhanced-auth.middleware.js 的一部分)
import { Request, Response, NextFunction } from 'express';
import { container } from '../infrastructure/di/container'; // 假设DI容器
import { Logger } from '../common/logging/Logger'; // 假设Logger

// 检查用户是否拥有特定权限的中间件工厂函数
export const requirePermission = (requiredPermission: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const logger = container.get<Logger>('Logger');
    if (!req.user) { // 确保用户已通过认证中间件
      logger.warn(`Permission check (${requiredPermission}) failed: User not authenticated.`);
      res.status(401).json({ message: 'Unauthorized: Authentication required.' });
      return;
    }

    // 假设 req.user.roles 包含角色名，并且我们需要从角色中查找权限
    // 实际应用中，req.user 可能直接包含权限列表，或者需要查询用户的角色和权限
    // const userPermissions = req.user.permissions; // 如果直接有权限列表

    // 此处简化：假设 req.user.roles 是角色名数组，我们需要一个机制来获取这些角色的权限
    // 更真实的场景是 User domain object has a method like user.hasPermission(requiredPermission)
    // 或者 authMiddleware 将用户的权限列表直接附加到 req.user.permissions

    // 示例：如果 req.user.roles 包含一个名为 "ADMIN" 的角色，则认为拥有所有权限
    const hasRequiredPermission = req.user.roles.includes('ADMIN') ||
                                 (req.user.roles.includes('EDITOR') && requiredPermission.startsWith('exercise:')); // 示例逻辑

    if (!hasRequiredPermission) {
      logger.warn(`Permission check failed for user ${req.user.id}. Required: ${requiredPermission}, User roles: ${req.user.roles.join(', ')}.`);
      res.status(403).json({ message: `Forbidden: Insufficient permissions. Requires: ${requiredPermission}` });
      return;
    }

    logger.info(`Permission check passed for user ${req.user.id}. Required: ${requiredPermission}.`);
    next();
  };
};

// 检查用户是否为管理员的中间件
export const requireAdminRole = (req: Request, res: Response, next: NextFunction): void => {
  const logger = container.get<Logger>('Logger');
  if (!req.user) {
    logger.warn('Admin role check failed: User not authenticated.');
    res.status(401).json({ message: 'Unauthorized: Authentication required.' });
    return;
  }

  const isAdmin = req.user.roles && req.user.roles.includes('ADMIN'); // 直接检查角色

  if (!isAdmin) {
    logger.warn(`Admin role check failed for user ${req.user.id}. User roles: ${req.user.roles.join(', ')}.`);
    res.status(403).json({ message: 'Forbidden: Administrator access required.' });
    return;
  }

  logger.info(`Admin role check passed for user ${req.user.id}.`);
  next();
};
```

#### 资源访问控制
除了基于角色和权限的中间件进行粗粒度授权外，细粒度的资源访问控制逻辑（例如，用户只能修改自己创建的内容）通常在应用服务层 (如 `ExerciseApplicationService`) 的具体业务方法中实现。服务会检查当前认证用户的ID与所操作资源的属性（如 `creatorId`）是否匹配，或根据其他业务规则判断是否有权操作。

```typescript
// backend/application/services/content/exercise/ExerciseApplicationService.ts (示例片段)
// import { ExerciseRepository } from '../../../../domain/repositories/content/exercise/ExerciseRepository';
// import { Exercise }from '../../../../domain/models/content/exercise/Exercise';
// import { UserId }from '../../../../domain/models/user/UserId'; // 假设UserId领域对象
// import { AppError }from '../../../../common/errors/AppError'; // 假设自定义错误类
// import { ExerciseMapper } from './ExerciseMapper'; // 假设的 DTO 映射器

// @injectable()
// export class ExerciseApplicationService {
//   constructor(
//     @inject('ExerciseRepository') private exerciseRepository: ExerciseRepository,
//     @inject('Logger') private logger: Logger
//   ) {}

//   async getExerciseById(exerciseIdValue: string, currentUserIdValue?: string): Promise<ExerciseDTO | null> {
//     const exerciseId = new ExerciseId(exerciseIdValue); // 假设领域ID对象
//     const exercise = await this.exerciseRepository.findById(exerciseId); // 假设仓库方法

//     if (!exercise) {
//       this.logger.warn(`Exercise not found: ${exerciseIdValue}`);
//       return null; // 或者抛出 NotFoundError
//     }

//     // 资源访问控制逻辑:
//     // 1. 公开的练习可以被任何人访问
//     if (exercise.isPublic()) { // 假设领域模型有 isPublic 方法
//       return ExerciseMapper.toDTO(exercise);
//     }

//     // 2. 非公开的练习只能被创建者或管理员访问
//     if (currentUserIdValue) {
//       const currentUserId = new UserId(currentUserIdValue);
//       // 假设 User 领域对象有 isAdmin() 方法，或者从 currentUserIdValue 关联的角色中判断
//       // const currentUser = await this.userRepository.findById(currentUserId);
//       // if (currentUser && (currentUser.isAdmin() || exercise.isOwnedBy(currentUserId))) {
//       if (exercise.isOwnedBy(currentUserId) /* || userIsAdminLogic */) { // 假设领域模型有 isOwnedBy 方法
//         return ExerciseMapper.toDTO(exercise);
//       }
//     }

//     this.logger.warn(`Access denied for exercise ${exerciseIdValue} by user ${currentUserIdValue || 'anonymous'}`);
//     throw new AppError('Forbidden', 'You do not have permission to access this exercise.', 403);
//   }

//   async updateExercise(command: UpdateExerciseCommand, currentUserIdValue: string): Promise<void> {
//       const exerciseId = new ExerciseId(command.exerciseId);
//       const exercise = await this.exerciseRepository.findById(exerciseId);
//       const currentUserId = new UserId(currentUserIdValue);

//       if (!exercise) {
//           throw new AppError('NotFound', 'Exercise not found', 404);
//       }

//       // 检查用户是否有权更新此练习 (例如，创建者或管理员)
//       // if (!exercise.canBeModifiedBy(currentUserId) /* && !userIsAdminLogic */ ) {
//       if (!exercise.isOwnedBy(currentUserId) /* && !userIsAdminLogic */) {
//           this.logger.warn(`User ${currentUserIdValue} attempted to update exercise ${command.exerciseId} without permission.`);
//           throw new AppError('Forbidden', 'You do not have permission to update this exercise.', 403);
//       }

//       // ... 更新逻辑 ...
//       // exercise.updateDetails(command.title, command.description, ...);
//       // await this.exerciseRepository.save(exercise);
//       this.logger.info(`Exercise ${command.exerciseId} updated by user ${currentUserIdValue}`);
//   }
// }
```

在路由中使用权限中间件：

```javascript
// backend/routes/v2/exercise.routes.js (示例，实际路由结构可能不同)
const express = require('express');
const router = express.Router();
// 假设 exerciseController 已经通过DI或直接require获取
// const { exerciseV2Controller } = require('../../controllers/v2/exercise.controller');
const { authMiddleware, optionalAuthMiddleware } = require('../../middlewares/auth.middleware'); // 实际的认证中间件路径
const { requirePermission, requireAdminRole } = require('../../middlewares/authorization.middleware'); // 修正路径

// 公开路由 (使用 optionalAuthMiddleware 如果需要知道用户是否登录，但不强制)
router.get('/',
    optionalAuthMiddleware, // 示例：如果需要根据用户登录状态返回不同内容
    exerciseV2Controller.getAllPublicExercises // 假设控制器方法
);
router.get('/:id',
    optionalAuthMiddleware,
    exerciseV2Controller.getExerciseDetails // 假设控制器方法
);

// 需要认证的路由
router.post('/',
  authMiddleware, // 先认证
  requirePermission('exercise:create'), // 再授权
  exerciseV2Controller.createExercise
);

router.put('/:id',
  authMiddleware,
  requirePermission('exercise:update'), // 授权检查特定权限
  // 细粒度控制在服务层处理（例如，用户只能修改自己的练习）
  exerciseV2Controller.updateExercise
);

// 需要特定角色（如管理员）的路由
router.delete('/:id',
  authMiddleware,
  requireAdminRole, // 要求管理员角色
  // requirePermission('exercise:delete'), // 也可以同时检查具体权限
  exerciseV2Controller.deleteExercise
);

// 仅管理员可以访问的特定端点
router.get('/admin/all',
    authMiddleware,
    requireAdminRole,
    exerciseV2Controller.getAllExercisesForAdmin
);

module.exports = router;
```

## 3.6 API文档：Swagger/OpenAPI

### Swagger/OpenAPI概述
AIBUBB系统使用Swagger/OpenAPI规范来描述和文档化其API。这有助于开发者理解API功能、自动生成客户端代码以及进行交互式API测试。我们主要使用 `swagger-jsdoc` 从代码注释中提取API信息，并结合 `swagger-ui-express` 来提供可交互的API文档界面。

在AIBUBB系统中，Swagger/OpenAPI的主要优势包括：
- **文档与代码同步**：API文档信息直接来源于代码注释，减少文档过期的风险。
- **交互式探索**：开发者可以通过Swagger UI直接在浏览器中测试API端点。
- **标准化**：遵循OpenAPI规范，易于被各种工具理解和集成。

### API文档实现

#### Swagger注释
API的元数据通过JSDoc风格的注释直接写在控制器（Controller）的路由处理函数上方。`swagger-jsdoc` 会解析这些注释来构建OpenAPI规范。

```javascript
// backend/controllers/exerciseV2.controller.js (示例，实际路径和内容可能调整)
/**
 * @swagger
 * /api/v2/exercises/{id}/soft-delete:
 *   delete:
 *     summary: 软删除练习
 *     description: 软删除指定练习（练习仍然存在，但在大多数查询中不可见）。
 *     tags: [Exercises] # 假设的标签
 *     security:
 *       - bearerAuth: [] # 表明此接口需要JWT认证
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string # 实际可能是integer或根据ID类型调整
 *         description: 练习ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse' # 引用预定义的响应结构
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
// const softDeleteExercise = async (req, res) => { ... }
```


#### API文档生成与配置
API的OpenAPI规范定义和 `swagger-jsdoc` 的配置主要在 `backend/config/swagger.js` 文件中。

```javascript
// backend/config/swagger.js
const swaggerJsdoc = require('swagger-jsdoc');
// const swaggerUi = require('swagger-ui-express'); // swagger-ui-express 可能在其他地方用于服务UI
const config = require('./config'); // 假设的基础配置文件

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'AIBUBB API', // 实际项目名称
    version: '2.0.0', // 或从 package.json 读取
    description: 'AIBUBB后端API文档', // 实际描述
    // ... 其他 contact, license 信息 ...
  },
  servers: [
    {
      // url: `http://localhost:${config.server.port}/api/v2`, // 根据实际API前缀和版本调整
      url: `/api/v2`, // 推荐使用相对路径，方便部署
      description: 'V2 API'
    },
    // 可以定义多个服务器，如生产环境、测试环境等
  ],
  components: {
    securitySchemes: {
      bearerAuth: { // JWT认证定义
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
    schemas: { // 定义可重用的数据模型 (DTOs, Entities等)
      // Error: { ... }, // 如同实际代码中定义的错误响应结构
      // User: { ... }, // 用户模型定义
      // Exercise: { ... }, // 练习模型定义
      SuccessResponse: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'Operation successful' },
          data: { type: 'object', nullable: true }
        }
      },
      // ... 其他通用响应或请求体定义
    },
    responses: { // 定义可重用的响应
        UnauthorizedError: {
            description: '认证失败或未提供令牌',
            content: { 'application/json': { schema: { $ref: '#/components/schemas/Error' } } }
        },
        NotFoundError: {
            description: '资源未找到',
            content: { 'application/json': { schema: { $ref: '#/components/schemas/Error' } } }
        },
        ServerError: {
            description: '服务器内部错误',
            content: { 'application/json': { schema: { $ref: '#/components/schemas/Error' } } }
        }
    }
  },
  security: [ // 全局安全定义，可被单个操作覆盖
    {
      bearerAuth: []
    }
  ]
};

const options = {
  swaggerDefinition,
  // Path to the API docs (typically JSDoc comments in controllers/routes)
  apis: [
    './backend/controllers/**/*.js', // 扫描所有JS控制器文件
    './backend/routes/**/*.js',   // 扫描所有JS路由文件
    // 如果有TypeScript控制器且包含JSDoc，也应加入：'./backend/controllers/**/*.ts'
  ],
};

const swaggerSpec = swaggerJsdoc(options);

module.exports = { swaggerSpec /*, swaggerUi */ }; // swaggerUi 可能不在此导出
```

#### API文档UI服务
API文档通常通过 `swagger-ui-express` 中间件在特定的路由（例如 `/api-docs`）上提供服务。`package.json` 中也可能包含如 `redoc-cli` 用于生成静态ReDoc文档的脚本。

```typescript
// backend/app.ts 或相关路由配置文件中 (示例性，实际集成方式可能不同)
// import swaggerUi from 'swagger-ui-express';
// import { swaggerSpec } from './config/swagger'; // 或从 swagger.js 导出的 swaggerSpec

// // ... Express app 初始化 ...

// if (process.env.NODE_ENV !== 'production') { // 通常只在非生产环境暴露
//   const swaggerUi = require('swagger-ui-express');
//   const { swaggerSpec } = require('./config/swagger'); // 假设 swagger.js 导出 swaggerSpec
//   app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

//   // 可选: 提供原始JSON规范的端点
//   app.get('/api-docs.json', (req, res) => {
//     res.setHeader('Content-Type', 'application/json');
//     res.send(swaggerSpec);
//   });
// }
```
实际项目中，API文档的生成和部署也可能通过 `package.json` 中定义的脚本命令（如 `generate-api-docs`, `api-docs:deploy`）来完成，特别是对于生成静态HTML文档或推送到专门的文档服务器。

#### API文档版本管理
如果系统存在多个API版本（例如 v1, v2），每个版本的API文档可以独立生成和提供。这通常通过为每个版本定义不同的 `swaggerJsdoc` 配置（特别是 `info.version` 和 `apis` 路径）来实现，并在不同的路由上提供各自的Swagger UI。
```javascript
// 示例思路:
// const swaggerSpecV1 = swaggerJsdoc(optionsV1);
// const swaggerSpecV2 = swaggerJsdoc(optionsV2);
// app.use('/api-docs/v1', swaggerUi.serve, swaggerUi.setup(swaggerSpecV1));
// app.use('/api-docs/v2', swaggerUi.serve, swaggerUi.setup(swaggerSpecV2));
```
---

## 3.7 容器化：Docker + Docker Compose
AIBUBB系统采用Docker进行容器化，以确保开发、测试和生产环境的一致性，并利用Docker Compose来编排多容器应用环境，尤其是在本地开发和测试设置中。

### Docker容器化

#### Dockerfile
后端应用的Docker镜像通过 `backend/Dockerfile` 构建。该Dockerfile采用了多阶段构建（multi-stage builds）策略，以优化最终镜像的大小并增强安全性。

```dockerfile
# backend/Dockerfile
# 构建阶段 (Builder Stage)
FROM node:18-alpine AS builder # 使用 Node 18 LTS Alpine 版本作为基础

WORKDIR /usr/src/app

# 复制 package.json 和 package-lock.json (或 yarn.lock)
COPY package*.json ./
# 安装所有依赖，包括开发依赖，用于可能的构建步骤或测试
RUN npm ci # 或 yarn install

# 复制所有源代码到工作目录
COPY .. .

# 如果有构建步骤（例如 TypeScript 编译），在此执行
# RUN npm run build # 假设有构建脚本

# 运行阶段 (Runtime Stage)
FROM node:18-alpine # 同样使用 Node 18 LTS Alpine

# 创建非root用户以增强安全性
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -u 1001 -G nodejs nodeuser

WORKDIR /usr/src/app

# 从构建阶段复制 package.json 和 package-lock.json
COPY --from=builder /usr/src/app/package*.json ./

# 只安装生产环境依赖
RUN npm ci --only=production && \
    npm cache clean --force # 清理缓存以减小镜像大小

# 从构建阶段复制应用代码 (如果是编译型语言，这里应复制编译后的产物)
# COPY --from=builder /usr/src/app/dist ./dist # 如果有构建产物在dist目录
COPY --from=builder /usr/src/app/ . ./ # 如果直接运行源码或构建产物在根目录

# 创建日志目录并设置权限 (如果应用向本地文件系统写日志)
RUN mkdir -p logs && \
    chown -R nodeuser:nodejs logs

# 设置必要的环境变量
ENV NODE_ENV=production
ENV PORT=9090 # 与应用监听端口一致

# 暴露应用监听的端口
EXPOSE 9090

# 切换到非root用户运行应用
USER nodeuser

# 健康检查 (根据实际健康检查端点调整)
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD node ./scripts/healthcheck.js || exit 1 # 假设有健康检查脚本

# 启动应用的命令
CMD ["node", "server.js"] # 假设入口文件是 server.js
```
此Dockerfile展示了构建生产环境适用镜像的常见实践，包括使用Alpine基础镜像、多阶段构建、非root用户运行以及健康检查。

### Docker Compose
Docker Compose用于在开发和测试环境中定义和运行由多个容器（服务）组成的AIBUBB应用。项目包含多个特定用途的 `docker-compose.*.yml` 文件，例如 `docker-compose.dev.yml` 用于本地开发，可能还包括 `docker-compose.test.yml` 或 `docker-compose.perf-test.yml` 等。

一个典型的 `docker-compose.dev.yml` 可能如下所示，主要关注核心依赖服务：

```yaml
# backend/docker-compose.dev.yml (或项目根目录)
version: '3.8'

services:
  mysql_db: # 服务名可以自定义，例如 aibubb_mysql_dev
    image: mysql:8.0
    container_name: aibubb_mysql_dev # 容器名
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: your_root_password # 从环境变量或.env文件读取更安全
      MYSQL_DATABASE: aibubb_db_dev
      MYSQL_USER: aibubb_user_dev
      MYSQL_PASSWORD: your_user_password
    ports:
      - "3306:3306" # 将容器的3306端口映射到主机的3306端口
    volumes:
      - mysql_dev_data:/var/lib/mysql # 持久化数据库数据
      # - ./path/to/init-scripts:/docker-entrypoint-initdb.d # 可选：用于数据库初始化脚本
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - aibubb_dev_network # 定义网络

  redis_cache: # 服务名自定义
    image: redis:6.2-alpine # 使用Alpine版本以减小大小
    container_name: aibubb_redis_dev
    restart: always
    ports:
      - "6379:6379" # 将容器的6379端口映射到主机的6379端口
    volumes:
      - redis_dev_data:/data # 持久化Redis数据
    networks:
      - aibubb_dev_network

  # 后端API服务 (如果不在本地直接运行，而是通过Docker Compose启动开发实例)
  # api_dev:
  #   build:
  #     context: ./backend # Dockerfile 所在目录
  #     dockerfile: Dockerfile # 或 Dockerfile.dev
  #   container_name: aibubb_api_dev
  #   ports:
  #     - "9090:9090" # 假设应用在容器内监听9090
  #   environment:
  #     NODE_ENV: development
  #     DB_HOST: mysql_db # 服务发现，使用service名
  #     DB_PORT: 3306
  #     DB_USER: aibubb_user_dev
  #     # ...其他环境变量...
  #     REDIS_URL: redis://redis_cache:6379
  #   volumes:
  #     - ./backend:/usr/src/app # 将本地代码映射到容器，实现热重载
  #     - /usr/src/app/node_modules # 避免本地node_modules覆盖容器内的
  #   depends_on:
  #     - mysql_db
  #     - redis_cache
  #   networks:
  #     - aibubb_dev_network
  #   command: npm run dev # 或其他开发启动命令

volumes: # 定义命名的卷
  mysql_dev_data:
  redis_dev_data:

networks: # 定义网络
  aibubb_dev_network:
    driver: bridge
```
**注意**:
- 上述 `docker-compose.dev.yml` 中的 `api_dev` 服务部分是示例性的，实际开发中后端服务可能直接在本地主机上通过 `npm run dev` 启动，并通过 `localhost` 连接到由Docker Compose管理的数据库和Redis服务。
- 更复杂的编排，如包含监控（Prometheus, Grafana）或Swagger UI服务，可能会在其他的 `docker-compose.*.yml` 文件中定义，或者在生产环境中使用如Kubernetes等更高级的编排工具。
- 数据卷 (`volumes`) 用于持久化数据库和Redis的数据，确保容器重启后数据不丢失。
- 网络 (`networks`) 用于使容器之间能够通过服务名称相互通信。

通过这种方式，AIBUBB系统利用Docker和Docker Compose实现了开发环境的标准化和依赖管理的简化。