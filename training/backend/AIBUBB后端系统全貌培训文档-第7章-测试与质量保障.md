# 第 7 章：测试与质量保障

## 📋 文档概述

**文档名称**: AIBUBB后端系统全貌培训文档 - 第7章 测试与质量保障  
**文档版本**: 2.0.0  
**创建日期**: 2024-11-15  
**最后修改**: 2025-01-27  
**作者**: 后端技术团队  
**审核者**: 质量保障工程师  
**文档状态**: ✅ 最新  
**价值等级**: 🔴 核心关键  
**依赖文档**: [第5章-API设计与实现](./AIBUBB后端系统全貌培训文档-第5章-API设计与实现.md), [第6章-事件驱动架构](./AIBUBB后端系统全貌培训文档-第6章-事件驱动架构.md)  
**被依赖文档**: [第8章-部署与运维](./AIBUBB后端系统全貌培训文档-第8章-部署与运维.md)  

## 📝 变更日志

### v2.0.0 (2025-01-27)
- 添加版本控制和价值评估元数据
- 完善测试策略和实现
- 新增性能测试和安全测试
- 更新测试自动化和CI/CD集成

### v1.5.0 (2024-12-20)
- 新增集成测试和端到端测试
- 完善单元测试策略
- 更新代码质量检查工具
- 添加测试覆盖率要求

### v1.0.0 (2024-11-15)
- 初始版本创建
- 定义测试策略框架
- 建立质量保障标准

## 7.1 测试体系概述

AIBUBB 后端系统建立了完整的测试体系，包括：

- 单元测试（Unit Tests）
- 集成测试（Integration Tests）
- API 测试（API Tests）
- 端到端测试（E2E Tests）
- 性能测试（Performance Tests）
- 安全测试（Security Tests）

### 测试金字塔

```
        ┌─────────────────┐
        │   E2E Tests     │  少量，高价值
        │   (Cypress)     │
        └─────────────────┘
       ┌───────────────────┐
       │ Integration Tests │  适量，关键路径
       │    (Jest)         │
       └───────────────────┘
      ┌─────────────────────┐
      │   Unit Tests        │  大量，快速反馈
      │   (Jest)            │
      └─────────────────────┘
```

## 7.2 单元测试

### 测试配置

```typescript
// jest.config.js
module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  roots: ["<rootDir>/src", "<rootDir>/tests"],
  testMatch: ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"],
  transform: {
    "^.+.ts$": "ts-jest",
  },
  collectCoverageFrom: [
    "src/**/*.ts",
    "!src/**/*.d.ts",
    "!src/index.ts",
    "!src/app.ts",
  ],
  coverageDirectory: "coverage",
  coverageReporters: ["text", "lcov", "html"],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  setupFilesAfterEnv: ["<rootDir>/tests/setup.ts"],
  testTimeout: 10000,
};
```

### 测试工具配置

```typescript
// tests/setup.ts
import { Container } from "../src/infrastructure/di/container";
import { DatabaseService } from "../src/infrastructure/database/database.service";
import { CacheService } from "../src/infrastructure/cache/cache.service";

// 全局测试设置
beforeAll(async () => {
  // 初始化测试数据库
  const dbService = Container.get(DatabaseService);
  await dbService.connect();

  // 清理缓存
  const cacheService = Container.get(CacheService);
  await cacheService.flushAll();
});

afterAll(async () => {
  // 清理测试数据
  const dbService = Container.get(DatabaseService);
  await dbService.disconnect();
});

// 每个测试前清理
beforeEach(async () => {
  // 重置数据库状态
  await resetDatabase();
});

async function resetDatabase() {
  // 实现数据库重置逻辑
  const dbService = Container.get(DatabaseService);
  await dbService.query("SET FOREIGN_KEY_CHECKS = 0");

  const tables = ["users", "notes", "exercises", "insights"];
  for (const table of tables) {
    await dbService.query(`TRUNCATE TABLE ${table}`);
  }

  await dbService.query("SET FOREIGN_KEY_CHECKS = 1");
}
```

### 服务层测试示例

```typescript
// tests/services/user.service.test.ts
import { UserService } from "../../src/services/user.service";
import { UserRepository } from "../../src/repositories/user.repository";
import { CacheService } from "../../src/infrastructure/cache/cache.service";
import { Container } from "../../src/infrastructure/di/container";

describe("UserService", () => {
  let userService: UserService;
  let userRepository: UserRepository;
  let cacheService: CacheService;

  beforeEach(() => {
    userService = Container.get(UserService);
    userRepository = Container.get(UserRepository);
    cacheService = Container.get(CacheService);
  });

  describe("createUser", () => {
    it("should create a new user successfully", async () => {
      // Arrange
      const userData = {
        username: "testuser",
        nickname: "Test User",
        email: "<EMAIL>",
      };

      // Act
      const result = await userService.createUser(userData);

      // Assert
      expect(result).toBeDefined();
      expect(result.username).toBe(userData.username);
      expect(result.nickname).toBe(userData.nickname);
      expect(result.email).toBe(userData.email);
      expect(result.id).toBeDefined();
    });

    it("should throw error when username already exists", async () => {
      // Arrange
      const userData = {
        username: "existinguser",
        nickname: "Existing User",
        email: "<EMAIL>",
      };

      await userService.createUser(userData);

      // Act & Assert
      await expect(userService.createUser(userData)).rejects.toThrow(
        "用户名已存在"
      );
    });

    it("should validate email format", async () => {
      // Arrange
      const userData = {
        username: "testuser2",
        nickname: "Test User 2",
        email: "invalid-email",
      };

      // Act & Assert
      await expect(userService.createUser(userData)).rejects.toThrow(
        "邮箱格式不正确"
      );
    });
  });

  describe("getUserById", () => {
    it("should return user when found", async () => {
      // Arrange
      const userData = {
        username: "testuser",
        nickname: "Test User",
        email: "<EMAIL>",
      };
      const createdUser = await userService.createUser(userData);

      // Act
      const result = await userService.getUserById(createdUser.id);

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(createdUser.id);
      expect(result.username).toBe(userData.username);
    });

    it("should return null when user not found", async () => {
      // Act
      const result = await userService.getUserById(999999);

      // Assert
      expect(result).toBeNull();
    });

    it("should use cache when available", async () => {
      // Arrange
      const userData = {
        username: "testuser",
        nickname: "Test User",
        email: "<EMAIL>",
      };
      const createdUser = await userService.createUser(userData);

      // 第一次调用，应该从数据库获取
      await userService.getUserById(createdUser.id);

      // Mock repository to verify cache is used
      const repositorySpy = jest.spyOn(userRepository, "findById");

      // Act - 第二次调用，应该从缓存获取
      const result = await userService.getUserById(createdUser.id);

      // Assert
      expect(result).toBeDefined();
      expect(repositorySpy).not.toHaveBeenCalled();
    });
  });
});
```

## 7.3 集成测试

### 数据库集成测试

```typescript
// tests/integration/database.integration.test.ts
import { DatabaseService } from "../../src/infrastructure/database/database.service";
import { UserRepository } from "../../src/repositories/user.repository";
import { Container } from "../../src/infrastructure/di/container";

describe("Database Integration", () => {
  let databaseService: DatabaseService;
  let userRepository: UserRepository;

  beforeAll(async () => {
    databaseService = Container.get(DatabaseService);
    userRepository = Container.get(UserRepository);
    await databaseService.connect();
  });

  afterAll(async () => {
    await databaseService.disconnect();
  });

  describe("User Repository", () => {
    it("should perform CRUD operations correctly", async () => {
      // Create
      const userData = {
        username: "integrationtest",
        nickname: "Integration Test",
        email: "<EMAIL>",
      };

      const createdUser = await userRepository.create(userData);
      expect(createdUser.id).toBeDefined();
      expect(createdUser.username).toBe(userData.username);

      // Read
      const foundUser = await userRepository.findById(createdUser.id);
      expect(foundUser).toBeDefined();
      expect(foundUser.username).toBe(userData.username);

      // Update
      const updateData = { nickname: "Updated Nickname" };
      const updatedUser = await userRepository.update(
        createdUser.id,
        updateData
      );
      expect(updatedUser.nickname).toBe(updateData.nickname);

      // Delete
      await userRepository.delete(createdUser.id);
      const deletedUser = await userRepository.findById(createdUser.id);
      expect(deletedUser).toBeNull();
    });

    it("should handle database constraints correctly", async () => {
      // 测试唯一约束
      const userData = {
        username: "uniquetest",
        nickname: "Unique Test",
        email: "<EMAIL>",
      };

      await userRepository.create(userData);

      // 尝试创建相同用户名的用户
      await expect(userRepository.create(userData)).rejects.toThrow();
    });

    it("should handle transactions correctly", async () => {
      const transaction = await databaseService.beginTransaction();

      try {
        const userData1 = {
          username: "transaction1",
          nickname: "Transaction Test 1",
          email: "<EMAIL>",
        };

        const userData2 = {
          username: "transaction2",
          nickname: "Transaction Test 2",
          email: "<EMAIL>",
        };

        await userRepository.create(userData1, { transaction });
        await userRepository.create(userData2, { transaction });

        await transaction.commit();

        // 验证数据已提交
        const user1 = await userRepository.findByUsername("transaction1");
        const user2 = await userRepository.findByUsername("transaction2");

        expect(user1).toBeDefined();
        expect(user2).toBeDefined();
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    });
  });
});
```

## 7.4 API 测试

### API 端点测试

```typescript
// tests/api/auth.api.test.ts
import request from "supertest";
import { app } from "../../src/app";
import { UserService } from "../../src/services/user.service";
import { Container } from "../../src/infrastructure/di/container";

describe("Auth API", () => {
  let userService: UserService;

  beforeEach(() => {
    userService = Container.get(UserService);
  });

  describe("POST /api/v1/auth/register", () => {
    it("should register user successfully", async () => {
      const userData = {
        username: "newuser",
        nickname: "New User",
        email: "<EMAIL>",
        password: "SecurePassword123!",
      };

      const response = await request(app)
        .post("/api/v1/auth/register")
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe(userData.username);
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.user.password).toBeUndefined(); // 密码不应返回
    });

    it("should validate password strength", async () => {
      const userData = {
        username: "weakpassuser",
        nickname: "Weak Pass User",
        email: "<EMAIL>",
        password: "123", // 弱密码
      };

      const response = await request(app)
        .post("/api/v1/auth/register")
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("WEAK_PASSWORD");
    });
  });

  describe("POST /api/v1/auth/login", () => {
    beforeEach(async () => {
      // 创建测试用户
      await request(app).post("/api/v1/auth/register").send({
        username: "logintest",
        nickname: "Login Test",
        email: "<EMAIL>",
        password: "TestPassword123!",
      });
    });

    it("should login successfully with correct credentials", async () => {
      const loginData = {
        username: "logintest",
        password: "TestPassword123!",
      };

      const response = await request(app)
        .post("/api/v1/auth/login")
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.user.username).toBe(loginData.username);
    });

    it("should reject invalid credentials", async () => {
      const loginData = {
        username: "logintest",
        password: "WrongPassword",
      };

      const response = await request(app)
        .post("/api/v1/auth/login")
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("INVALID_CREDENTIALS");
    });

    it("should handle rate limiting", async () => {
      const loginData = {
        username: "logintest",
        password: "WrongPassword",
      };

      // 多次尝试错误登录
      for (let i = 0; i < 10; i++) {
        await request(app).post("/api/v1/auth/login").send(loginData);
      }

      // 第11次应该被限制
      const response = await request(app)
        .post("/api/v1/auth/login")
        .send(loginData)
        .expect(429);

      expect(response.body.error.code).toBe("TOO_MANY_LOGIN_ATTEMPTS");
    });
  });

  describe("GET /api/v1/auth/profile", () => {
    let authToken: string;

    beforeEach(async () => {
      // 注册并登录获取token
      await request(app).post("/api/v1/auth/register").send({
        username: "profiletest",
        nickname: "Profile Test",
        email: "<EMAIL>",
        password: "TestPassword123!",
      });

      const loginResponse = await request(app).post("/api/v1/auth/login").send({
        username: "profiletest",
        password: "TestPassword123!",
      });

      authToken = loginResponse.body.data.token;
    });

    it("should return user profile with valid token", async () => {
      const response = await request(app)
        .get("/api/v1/auth/profile")
        .set("Authorization", `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.username).toBe("profiletest");
      expect(response.body.data.password).toBeUndefined();
    });

    it("should reject request without token", async () => {
      const response = await request(app)
        .get("/api/v1/auth/profile")
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("UNAUTHORIZED");
    });

    it("should reject request with invalid token", async () => {
      const response = await request(app)
        .get("/api/v1/auth/profile")
        .set("Authorization", "Bearer invalid-token")
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("INVALID_TOKEN");
    });
  });
});
```

## 7.5 性能测试

### 负载测试配置

```typescript
// tests/performance/load.test.ts
import { performance } from "perf_hooks";
import request from "supertest";
import { app } from "../../src/app";

describe("Performance Tests", () => {
  describe("API Response Time", () => {
    it("should respond to health check within 100ms", async () => {
      const start = performance.now();

      await request(app).get("/health").expect(200);

      const end = performance.now();
      const responseTime = end - start;

      expect(responseTime).toBeLessThan(100);
    });

    it("should handle concurrent requests efficiently", async () => {
      const concurrentRequests = 50;
      const promises = [];

      const start = performance.now();

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(request(app).get("/api/v1/notes").expect(200));
      }

      await Promise.all(promises);

      const end = performance.now();
      const totalTime = end - start;
      const averageTime = totalTime / concurrentRequests;

      expect(averageTime).toBeLessThan(500); // 平均响应时间小于500ms
    });
  });

  describe("Database Performance", () => {
    it("should handle large dataset queries efficiently", async () => {
      // 创建大量测试数据
      const testData = [];
      for (let i = 0; i < 1000; i++) {
        testData.push({
          title: `Test Note ${i}`,
          content: `This is test content for note ${i}`,
          creator_id: 1,
        });
      }

      // 批量插入
      const start = performance.now();

      await request(app)
        .post("/api/v1/notes/batch")
        .send({ notes: testData })
        .expect(201);

      const end = performance.now();
      const insertTime = end - start;

      expect(insertTime).toBeLessThan(5000); // 批量插入应在5秒内完成

      // 测试查询性能
      const queryStart = performance.now();

      await request(app).get("/api/v1/notes?limit=100&offset=0").expect(200);

      const queryEnd = performance.now();
      const queryTime = queryEnd - queryStart;

      expect(queryTime).toBeLessThan(1000); // 查询应在1秒内完成
    });
  });

  describe("Memory Usage", () => {
    it("should not have memory leaks during stress test", async () => {
      const initialMemory = process.memoryUsage();

      // 执行大量请求
      for (let i = 0; i < 100; i++) {
        await request(app).get("/api/v1/notes").expect(200);
      }

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      // 内存增长应该在合理范围内（小于50MB）
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });
});
```

## 7.6 安全测试

### 安全漏洞测试

```typescript
// tests/security/security.test.ts
import request from "supertest";
import { app } from "../../src/app";

describe("Security Tests", () => {
  describe("SQL Injection Protection", () => {
    it("should prevent SQL injection in login", async () => {
      const maliciousPayload = {
        username: "admin'; DROP TABLE users; --",
        password: "password",
      };

      const response = await request(app)
        .post("/api/v1/auth/login")
        .send(maliciousPayload);

      // 应该返回认证失败，而不是服务器错误
      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe("INVALID_CREDENTIALS");
    });

    it("should prevent SQL injection in search", async () => {
      const maliciousQuery = "'; DROP TABLE notes; --";

      const response = await request(app).get(
        `/api/v1/notes/search?q=${encodeURIComponent(maliciousQuery)}`
      );

      // 应该正常处理，返回空结果或错误，而不是服务器错误
      expect([200, 400]).toContain(response.status);
    });
  });

  describe("XSS Protection", () => {
    it("should sanitize user input", async () => {
      const xssPayload = {
        title: "<script>alert('XSS')</script>",
        content: "<img src=x onerror=alert('XSS')>",
      };

      // 先登录获取token
      const loginResponse = await request(app).post("/api/v1/auth/login").send({
        username: "testuser",
        password: "TestPassword123!",
      });

      const token = loginResponse.body.data.token;

      const response = await request(app)
        .post("/api/v1/notes")
        .set("Authorization", `Bearer ${token}`)
        .send(xssPayload)
        .expect(201);

      // 检查返回的数据是否已被清理
      expect(response.body.data.title).not.toContain("<script>");
      expect(response.body.data.content).not.toContain("onerror");
    });
  });

  describe("Authentication Security", () => {
    it("should reject weak passwords", async () => {
      const weakPasswords = [
        "123456",
        "password",
        "qwerty",
        "12345678",
        "abc123",
      ];

      for (const password of weakPasswords) {
        const response = await request(app)
          .post("/api/v1/auth/register")
          .send({
            username: `user_${Date.now()}`,
            nickname: "Test User",
            email: `test_${Date.now()}@example.com`,
            password,
          });

        expect(response.status).toBe(400);
        expect(response.body.error.code).toBe("WEAK_PASSWORD");
      }
    });

    it("should enforce rate limiting on login attempts", async () => {
      const loginData = {
        username: "nonexistentuser",
        password: "wrongpassword",
      };

      // 快速连续尝试登录
      const promises = [];
      for (let i = 0; i < 15; i++) {
        promises.push(request(app).post("/api/v1/auth/login").send(loginData));
      }

      const responses = await Promise.all(promises);

      // 应该有一些请求被限制
      const rateLimitedResponses = responses.filter((r) => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it("should invalidate tokens on logout", async () => {
      // 登录获取token
      const loginResponse = await request(app).post("/api/v1/auth/login").send({
        username: "testuser",
        password: "TestPassword123!",
      });

      const token = loginResponse.body.data.token;

      // 验证token有效
      await request(app)
        .get("/api/v1/auth/profile")
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      // 登出
      await request(app)
        .post("/api/v1/auth/logout")
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      // 验证token已失效
      await request(app)
        .get("/api/v1/auth/profile")
        .set("Authorization", `Bearer ${token}`)
        .expect(401);
    });
  });
});
```

## 7.7 测试自动化与 CI/CD 集成

### GitHub Actions 测试工作流

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: aibubb_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:6-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Setup test environment
        run: |
          cp .env.test.example .env.test
          npm run db:migrate:test
          npm run db:seed:test

      - name: Run unit tests
        run: npm run test:unit
        env:
          NODE_ENV: test
          DB_HOST: localhost
          DB_PORT: 3306
          DB_USER: root
          DB_PASSWORD: testpassword
          DB_NAME: aibubb_test
          REDIS_URL: redis://localhost:6379

      - name: Run integration tests
        run: npm run test:integration
        env:
          NODE_ENV: test
          DB_HOST: localhost
          DB_PORT: 3306
          DB_USER: root
          DB_PASSWORD: testpassword
          DB_NAME: aibubb_test
          REDIS_URL: redis://localhost:6379

      - name: Run API tests
        run: npm run test:api
        env:
          NODE_ENV: test
          DB_HOST: localhost
          DB_PORT: 3306
          DB_USER: root
          DB_PASSWORD: testpassword
          DB_NAME: aibubb_test
          REDIS_URL: redis://localhost:6379

      - name: Run security tests
        run: npm run test:security
        env:
          NODE_ENV: test

      - name: Generate coverage report
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

      - name: Run performance tests
        run: npm run test:performance
        env:
          NODE_ENV: test

      - name: Archive test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: |
            coverage/
            test-results/
            performance-results/
```

### 测试脚本配置

```json
// package.json (测试相关脚本)
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest --testPathPattern=tests/unit",
    "test:integration": "jest --testPathPattern=tests/integration",
    "test:api": "jest --testPathPattern=tests/api",
    "test:security": "jest --testPathPattern=tests/security",
    "test:performance": "jest --testPathPattern=tests/performance",
    "test:coverage": "jest --coverage",
    "test:watch": "jest --watch",
    "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand",
    "test:load": "artillery run tests/performance/load-test.yml",
    "db:migrate:test": "NODE_ENV=test npx sequelize-cli db:migrate",
    "db:seed:test": "NODE_ENV=test npx sequelize-cli db:seed:all"
  }
}
```

## 7.8 测试数据管理

### 测试数据工厂

```typescript
// tests/factories/user.factory.ts
import { faker } from "@faker-js/faker";
import { UserService } from "../../src/services/user.service";
import { Container } from "../../src/infrastructure/di/container";

export class UserFactory {
  private static userService = Container.get(UserService);

  static async create(overrides: Partial<any> = {}) {
    const userData = {
      username: faker.internet.userName(),
      nickname: faker.person.fullName(),
      email: faker.internet.email(),
      password: "TestPassword123!",
      ...overrides,
    };

    return await this.userService.createUser(userData);
  }

  static async createMany(count: number, overrides: Partial<any> = {}) {
    const users = [];
    for (let i = 0; i < count; i++) {
      users.push(await this.create(overrides));
    }
    return users;
  }

  static generateUserData(overrides: Partial<any> = {}) {
    return {
      username: faker.internet.userName(),
      nickname: faker.person.fullName(),
      email: faker.internet.email(),
      password: "TestPassword123!",
      ...overrides,
    };
  }
}
```

```typescript
// tests/factories/note.factory.ts
import { faker } from "@faker-js/faker";
import { NoteService } from "../../src/services/note.service";
import { Container } from "../../src/infrastructure/di/container";
import { UserFactory } from "./user.factory";

export class NoteFactory {
  private static noteService = Container.get(NoteService);

  static async create(overrides: Partial<any> = {}) {
    let creatorId = overrides.creator_id;

    if (!creatorId) {
      const user = await UserFactory.create();
      creatorId = user.id;
    }

    const noteData = {
      title: faker.lorem.sentence(),
      content: faker.lorem.paragraphs(3),
      creator_id: creatorId,
      status: "PUBLISHED",
      ...overrides,
    };

    return await this.noteService.createNote(noteData);
  }

  static async createMany(count: number, overrides: Partial<any> = {}) {
    const notes = [];
    for (let i = 0; i < count; i++) {
      notes.push(await this.create(overrides));
    }
    return notes;
  }
}
```

### 测试数据清理

```typescript
// tests/helpers/database-cleaner.ts
import { DatabaseService } from "../../src/infrastructure/database/database.service";
import { Container } from "../../src/infrastructure/di/container";

export class DatabaseCleaner {
  private static databaseService = Container.get(DatabaseService);

  static async cleanAll() {
    const tables = [
      "user_achievements",
      "user_badges",
      "note_tags",
      "tags",
      "notes",
      "exercises",
      "insights",
      "users",
    ];

    await this.databaseService.query("SET FOREIGN_KEY_CHECKS = 0");

    for (const table of tables) {
      await this.databaseService.query(`TRUNCATE TABLE ${table}`);
    }

    await this.databaseService.query("SET FOREIGN_KEY_CHECKS = 1");
  }

  static async cleanTable(tableName: string) {
    await this.databaseService.query(`TRUNCATE TABLE ${tableName}`);
  }

  static async resetAutoIncrement(tableName: string) {
    await this.databaseService.query(
      `ALTER TABLE ${tableName} AUTO_INCREMENT = 1`
    );
  }
}
```

## 7.9 测试报告与质量指标

### 覆盖率报告配置

```typescript
// jest.config.js (覆盖率配置)
module.exports = {
  // ... 其他配置
  collectCoverageFrom: [
    "src/**/*.ts",
    "!src/**/*.d.ts",
    "!src/index.ts",
    "!src/app.ts",
    "!src/infrastructure/database/migrations/**",
    "!src/infrastructure/database/seeders/**",
  ],
  coverageDirectory: "coverage",
  coverageReporters: ["text", "text-summary", "lcov", "html", "json"],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    "./src/services/": {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    "./src/controllers/": {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },
};
```

### 质量门禁

```bash
#!/bin/bash
# scripts/quality-gate.sh

echo "🚀 运行质量门禁检查..."

# 运行测试并生成覆盖率报告
echo "📊 运行测试套件..."
npm run test:coverage

if [ $? -ne 0 ]; then
    echo "❌ 测试失败"
    exit 1
fi

# 检查覆盖率
echo "📈 检查代码覆盖率..."
coverage_line=$(grep -o '"lines":{"total":[0-9]*,"covered":[0-9]*,"skipped":[0-9]*,"pct":[0-9.]*}' coverage/coverage-summary.json | grep -o '"pct":[0-9.]*' | cut -d':' -f2)
coverage_functions=$(grep -o '"functions":{"total":[0-9]*,"covered":[0-9]*,"skipped":[0-9]*,"pct":[0-9.]*}' coverage/coverage-summary.json | grep -o '"pct":[0-9.]*' | cut -d':' -f2)
coverage_branches=$(grep -o '"branches":{"total":[0-9]*,"covered":[0-9]*,"skipped":[0-9]*,"pct":[0-9.]*}' coverage/coverage-summary.json | grep -o '"pct":[0-9.]*' | cut -d':' -f2)

echo "代码覆盖率:"
echo "  行覆盖率: ${coverage_line}%"
echo "  函数覆盖率: ${coverage_functions}%"
echo "  分支覆盖率: ${coverage_branches}%"

# 检查是否达到最低要求
if (( $(echo "$coverage_line < 80" | bc -l) )); then
    echo "❌ 行覆盖率低于80%"
    exit 1
fi

if (( $(echo "$coverage_functions < 80" | bc -l) )); then
    echo "❌ 函数覆盖率低于80%"
    exit 1
fi

if (( $(echo "$coverage_branches < 80" | bc -l) )); then
    echo "❌ 分支覆盖率低于80%"
    exit 1
fi

# 运行代码质量检查
echo "🔍 运行代码质量检查..."
npm run lint

if [ $? -ne 0 ]; then
    echo "❌ 代码质量检查失败"
    exit 1
fi

# 运行安全扫描
echo "🔒 运行安全扫描..."
npm audit --audit-level=moderate

if [ $? -ne 0 ]; then
    echo "❌ 发现安全漏洞"
    exit 1
fi

echo "✅ 所有质量门禁检查通过！"
exit 0
```

## 7.10 测试最佳实践

### 测试组织原则

1. **AAA 模式**：Arrange（准备）、Act（执行）、Assert（断言）
2. **单一职责**：每个测试只验证一个功能点
3. **独立性**：测试之间不应相互依赖
4. **可重复性**：测试结果应该是确定的
5. **快速反馈**：单元测试应该快速执行

### 测试命名规范

```typescript
// 好的测试命名示例
describe("UserService", () => {
  describe("createUser", () => {
    it("should create user successfully with valid data", () => {});
    it("should throw error when username already exists", () => {});
    it("should throw error when email format is invalid", () => {});
    it("should hash password before saving", () => {});
  });

  describe("getUserById", () => {
    it("should return user when found", () => {});
    it("should return null when user not found", () => {});
    it("should use cache when available", () => {});
  });
});
```

### Mock 和 Stub 使用指南

```typescript
// 外部依赖Mock示例
jest.mock("../../src/infrastructure/cache/cache.service");
jest.mock("../../src/infrastructure/email/email.service");

const mockCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  delete: jest.fn(),
};

const mockEmailService = {
  sendEmail: jest.fn(),
};

// 在测试中使用
beforeEach(() => {
  jest.clearAllMocks();
  Container.set(CacheService, mockCacheService);
  Container.set(EmailService, mockEmailService);
});
```

### 测试数据管理

```typescript
// 使用工厂模式创建测试数据
const user = await UserFactory.create({
  username: "specificuser",
  email: "<EMAIL>",
});

// 使用Builder模式构建复杂对象
const note = new NoteBuilder()
  .withTitle("Test Note")
  .withContent("Test Content")
  .withCreator(user.id)
  .withTags(["test", "example"])
  .build();
```

通过这个全面的测试体系，AIBUBB 后端系统确保了代码质量、功能正确性和系统稳定性。测试覆盖了从单元测试到端到端测试的各个层面，并集成了自动化 CI/CD 流程，为系统的持续交付提供了坚实的质量保障。
