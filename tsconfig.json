{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020", "DOM"], "allowJs": true, "checkJs": false, "jsx": "preserve", "declaration": false, "declarationMap": false, "sourceMap": true, "outDir": "./dist", "rootDir": ".", "removeComments": false, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "baseUrl": ".", "paths": {"*": ["*"]}, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}