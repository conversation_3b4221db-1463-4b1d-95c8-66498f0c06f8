/**
 * 日期工具函数测试
 */
const { formatDate, isToday, getDaysDifference } = require('../date-utils');

describe('日期工具函数', () => {
  describe('formatDate', () => {
    test('应该正确格式化日期', () => {
      const date = new Date('2025-05-15T12:30:45');
      expect(formatDate(date, 'YYYY-MM-DD')).toBe('2025-05-15');
      expect(formatDate(date, 'YYYY/MM/DD')).toBe('2025/05/15');
      expect(formatDate(date, 'YYYY年MM月DD日')).toBe('2025年05月15日');
      expect(formatDate(date, 'HH:mm:ss')).toBe('12:30:45');
      expect(formatDate(date, 'YYYY-MM-DD HH:mm')).toBe('2025-05-15 12:30');
    });

    test('应该处理无效日期', () => {
      expect(formatDate(null, 'YYYY-MM-DD')).toBe('');
      expect(formatDate(undefined, 'YYYY-MM-DD')).toBe('');
      expect(formatDate('invalid date', 'YYYY-MM-DD')).toBe('');
    });
  });

  describe('isToday', () => {
    test('应该正确判断是否为今天', () => {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      expect(isToday(today)).toBe(true);
      expect(isToday(yesterday)).toBe(false);
      expect(isToday(tomorrow)).toBe(false);
    });

    test('应该处理无效日期', () => {
      expect(isToday(null)).toBe(false);
      expect(isToday(undefined)).toBe(false);
      expect(isToday('invalid date')).toBe(false);
    });
  });

  describe('getDaysDifference', () => {
    test('应该正确计算两个日期之间的天数差', () => {
      const date1 = new Date('2025-05-15');
      const date2 = new Date('2025-05-20');

      expect(getDaysDifference(date1, date2)).toBe(5);
      expect(getDaysDifference(date2, date1)).toBe(-5);
    });

    test('应该处理相同日期', () => {
      const date = new Date('2025-05-15');
      expect(getDaysDifference(date, date)).toBe(0);
    });

    test('应该处理无效日期', () => {
      const date = new Date('2025-05-15');
      expect(getDaysDifference(null, date)).toBe(0);
      expect(getDaysDifference(date, null)).toBe(0);
      expect(getDaysDifference(undefined, date)).toBe(0);
      expect(getDaysDifference('invalid date', date)).toBe(0);
    });
  });
});
