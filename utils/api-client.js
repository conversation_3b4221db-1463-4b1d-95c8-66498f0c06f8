/**
 * API客户端
 * 提供统一的API请求处理、错误处理和数据转换
 */

// 配置
const API_CONFIG = {
  baseUrl: 'http://localhost:3010/api/v1', // 开发环境使用模拟API服务
  // baseUrl: 'https://api.nebulalearn.com/api/v1', // 生产环境使用实际API
  timeout: 30000, // 请求超时时间（毫秒）
  withCredentials: true // 跨域请求是否携带凭证
};

// 请求拦截器
const requestInterceptors = [
  config => {
    // 添加认证信息
    const token = localStorage.getItem('token');
    if (token) {
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // 添加时间戳，避免缓存
    if (config.method === 'get') {
      config.params = config.params || {};
      config.params._t = Date.now();
    }

    return config;
  }
];

// 响应拦截器
const responseInterceptors = [
  response => {
    // 处理响应数据
    const { data } = response;

    // 检查API响应格式
    if (data && typeof data === 'object') {
      if (data.success === true) {
        // 成功响应
        return {
          data: data.data,
          message: data.message,
          meta: {
            total: data.total,
            page: data.page,
            pageSize: data.pageSize
          }
        };
      } else {
        // 业务错误
        const error = new Error(data.error?.message || '未知错误');
        error.code = data.error?.code || 'UNKNOWN_ERROR';
        error.response = response;
        throw error;
      }
    }

    // 非标准响应
    return { data };
  },
  error => {
    // 处理错误响应
    if (error.response) {
      // 服务器返回错误
      const { status, data } = error.response;

      // 处理特定状态码
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token');
          // 跳转到登录页的逻辑
          break;
        case 403:
          // 禁止访问
          console.error('禁止访问:', data);
          break;
        case 404:
          // 资源不存在
          console.error('资源不存在:', data);
          break;
        case 500:
          // 服务器错误
          console.error('服务器错误:', data);
          break;
        default:
          console.error(`HTTP错误 ${status}:`, data);
      }

      // 格式化错误信息
      const errorMessage = data?.error?.message || '服务器响应错误';
      const errorCode = data?.error?.code || `HTTP_ERROR_${status}`;

      const formattedError = new Error(errorMessage);
      formattedError.code = errorCode;
      formattedError.status = status;
      formattedError.response = error.response;

      throw formattedError;
    } else if (error.request) {
      // 请求发送但没有收到响应
      const networkError = new Error('网络错误，请检查您的网络连接');
      networkError.code = 'NETWORK_ERROR';
      networkError.request = error.request;

      throw networkError;
    } else {
      // 请求配置错误
      const configError = new Error('请求配置错误');
      configError.code = 'REQUEST_CONFIG_ERROR';

      throw configError;
    }
  }
];

/**
 * 数据转换工具
 */
const dataTransformer = {
  /**
   * 将snake_case转换为camelCase
   * @param {Object} data - 要转换的数据
   * @returns {Object} - 转换后的数据
   */
  snakeToCamel(data) {
    if (data === null || data === undefined || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.snakeToCamel(item));
    }

    return Object.keys(data).reduce((result, key) => {
      // 转换键名
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

      // 递归转换值
      result[camelKey] = this.snakeToCamel(data[key]);

      return result;
    }, {});
  },

  /**
   * 将camelCase转换为snake_case
   * @param {Object} data - 要转换的数据
   * @returns {Object} - 转换后的数据
   */
  camelToSnake(data) {
    if (data === null || data === undefined || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.camelToSnake(item));
    }

    return Object.keys(data).reduce((result, key) => {
      // 转换键名
      const snakeKey = key.replace(/([A-Z])/g, letter => `_${letter.toLowerCase()}`);

      // 递归转换值
      result[snakeKey] = this.camelToSnake(data[key]);

      return result;
    }, {});
  }
};

/**
 * 缓存管理器
 */
class CacheManager {
  constructor() {
    this.cache = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 默认缓存时间（5分钟）
  }

  /**
   * 生成缓存键
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @returns {string} - 缓存键
   */
  generateKey(url, params = {}) {
    return `${url}:${JSON.stringify(params)}`;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} data - 缓存数据
   * @param {number} ttl - 缓存时间（毫秒）
   */
  set(key, data, ttl = this.defaultTTL) {
    const expireAt = Date.now() + ttl;
    this.cache.set(key, { data, expireAt });

    // 设置过期清理
    setTimeout(() => {
      this.cache.delete(key);
    }, ttl);
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {*} - 缓存数据，如果不存在或已过期则返回null
   */
  get(key) {
    const cached = this.cache.get(key);

    if (!cached) {
      return null;
    }

    // 检查是否过期
    if (cached.expireAt < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * 清除缓存
   * @param {string} keyPattern - 缓存键模式，用于部分清除
   */
  clear(keyPattern) {
    if (!keyPattern) {
      // 清除所有缓存
      this.cache.clear();
      return;
    }

    // 清除匹配的缓存
    for (const key of this.cache.keys()) {
      if (key.includes(keyPattern)) {
        this.cache.delete(key);
      }
    }
  }
}

// 创建缓存管理器实例
const cacheManager = new CacheManager();

/**
 * API客户端
 */
class ApiClient {
  constructor(config = {}) {
    this.config = { ...API_CONFIG, ...config };
    this.requestInterceptors = [...requestInterceptors];
    this.responseInterceptors = [...responseInterceptors];
  }

  /**
   * 发送请求
   * @param {Object} options - 请求选项
   * @returns {Promise} - 请求Promise
   */
  async request(options) {
    // 合并配置
    const config = {
      ...this.config,
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
        ...options.headers
      }
    };

    // 应用请求拦截器
    let requestConfig = { ...config };
    for (const interceptor of this.requestInterceptors) {
      requestConfig = await interceptor(requestConfig);
    }

    // 构建完整URL
    const url = new URL(
      requestConfig.url.startsWith('http')
        ? requestConfig.url
        : `${this.config.baseUrl}${requestConfig.url}`
    );

    // 添加查询参数
    if (requestConfig.params) {
      Object.entries(requestConfig.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value);
        }
      });
    }

    // 检查缓存
    if (requestConfig.method === 'get' && requestConfig.useCache !== false) {
      const cacheKey = cacheManager.generateKey(url.toString(), requestConfig.data);
      const cachedData = cacheManager.get(cacheKey);

      if (cachedData) {
        return cachedData;
      }
    }

    // 准备请求选项
    const fetchOptions = {
      method: requestConfig.method,
      headers: requestConfig.headers,
      credentials: requestConfig.withCredentials ? 'include' : 'same-origin',
      mode: 'cors'
    };

    // 添加请求体
    if (requestConfig.data && ['post', 'put', 'patch'].includes(requestConfig.method.toLowerCase())) {
      // 转换请求数据为snake_case
      const snakeCaseData = dataTransformer.camelToSnake(requestConfig.data);
      fetchOptions.body = JSON.stringify(snakeCaseData);
    }

    try {
      // 发送请求
      const response = await fetch(url.toString(), fetchOptions);

      // 解析响应
      const responseData = await response.json();

      // 构建响应对象
      const responseObject = {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        config: requestConfig,
        request: null
      };

      // 应用响应拦截器
      let result = responseObject;
      for (const interceptor of this.responseInterceptors) {
        result = await interceptor(result);
      }

      // 转换响应数据为camelCase
      if (result.data) {
        result.data = dataTransformer.snakeToCamel(result.data);
      }

      // 缓存结果
      if (requestConfig.method === 'get' && requestConfig.useCache !== false) {
        const cacheKey = cacheManager.generateKey(url.toString(), requestConfig.data);
        const ttl = requestConfig.cacheTTL || this.config.cacheTTL;
        cacheManager.set(cacheKey, result, ttl);
      }

      return result;
    } catch (error) {
      // 应用错误拦截器
      for (const interceptor of this.responseInterceptors) {
        if (typeof interceptor === 'function' && interceptor.length === 1) {
          try {
            return await interceptor(error);
          } catch (e) {
            error = e;
          }
        }
      }

      throw error;
    }
  }

  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  get(url, params = {}, config = {}) {
    return this.request({
      method: 'get',
      url,
      params,
      ...config
    });
  }

  /**
   * POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  post(url, data = {}, config = {}) {
    return this.request({
      method: 'post',
      url,
      data,
      ...config
    });
  }

  /**
   * PUT请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  put(url, data = {}, config = {}) {
    return this.request({
      method: 'put',
      url,
      data,
      ...config
    });
  }

  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  delete(url, params = {}, config = {}) {
    return this.request({
      method: 'delete',
      url,
      params,
      ...config
    });
  }

  /**
   * PATCH请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  patch(url, data = {}, config = {}) {
    return this.request({
      method: 'patch',
      url,
      data,
      ...config
    });
  }

  /**
   * 清除API缓存
   * @param {string} urlPattern - URL模式，用于部分清除
   */
  clearCache(urlPattern) {
    cacheManager.clear(urlPattern);
  }
}

// 创建默认API客户端实例
const apiClient = new ApiClient();

export default apiClient;
export { ApiClient, dataTransformer, cacheManager };
