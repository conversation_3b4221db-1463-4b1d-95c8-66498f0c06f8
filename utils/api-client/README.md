# 增强版API客户端

这是NebulaLearn项目的增强版API客户端，提供统一的API请求处理、错误处理、数据转换、请求取消、超时控制和重试机制。

## 特性

- **请求取消**：支持通过AbortController取消正在进行的请求，避免竞态条件
- **请求超时**：自动处理请求超时，可配置超时时间
- **请求重试**：支持失败请求自动重试，可配置重试次数和策略
- **多级缓存**：支持内存缓存和本地存储缓存，提高性能和离线访问能力
- **数据转换**：自动处理snake_case和camelCase转换，支持数据验证和规范化
- **拦截器机制**：灵活的请求/响应拦截器，支持全局和实例级别的拦截器
- **批量操作**：支持批量删除、恢复等操作，提高效率

## 使用方法

### 基本用法

```javascript
import api from 'utils/api-client/api-modules';

// 获取主题列表
api.theme.getThemes({ page: 1, pageSize: 10 })
  .then(result => {
    console.log('主题列表:', result.data);
    console.log('总数:', result.meta.total);
  })
  .catch(error => {
    console.error('获取主题列表失败:', error.message);
  });

// 创建主题
api.theme.createTheme({
  name: '新主题',
  description: '这是一个新主题',
  isActive: true
})
  .then(result => {
    console.log('主题创建成功:', result.data);
  })
  .catch(error => {
    console.error('创建主题失败:', error.message);
  });
```

### 请求取消

```javascript
import api from 'utils/api-client/api-modules';

// 创建AbortController
const abortController = new AbortController();

// 发送请求
api.theme.getThemes(
  { page: 1, pageSize: 10 },
  { signal: abortController.signal }
)
  .then(result => {
    console.log('主题列表:', result.data);
  })
  .catch(error => {
    if (error.name === 'AbortError') {
      console.log('请求已取消');
    } else {
      console.error('获取主题列表失败:', error.message);
    }
  });

// 取消请求
abortController.abort();
```

### 缓存控制

```javascript
import api from 'utils/api-client/api-modules';

// 使用缓存
api.theme.getTheme(1, {
  useCache: true,
  cacheTTL: 10 * 60 * 1000 // 10分钟缓存
})
  .then(result => {
    console.log('主题详情:', result.data);
  });

// 强制刷新（不使用缓存）
api.theme.getTheme(1, { useCache: false })
  .then(result => {
    console.log('主题详情（刷新）:', result.data);
  });

// 清除特定主题的缓存
api.theme.clearCache();

// 清除所有缓存
api.clearCache();
```

### 请求重试

```javascript
import api from 'utils/api-client/api-modules';

// 配置重试
api.theme.createTheme(
  {
    name: '新主题',
    description: '这是一个新主题',
    isActive: true
  },
  {
    maxRetries: 3, // 最多重试3次
    retryDelay: 1000 // 初始重试延迟1秒
  }
)
  .then(result => {
    console.log('主题创建成功:', result.data);
  })
  .catch(error => {
    console.error('创建主题失败:', error.message);
  });
```

### 批量操作

```javascript
import api from 'utils/api-client/api-modules';

// 批量删除主题
api.theme.batchDeleteThemes([1, 2, 3])
  .then(result => {
    console.log('批量删除成功:', result.message);
  })
  .catch(error => {
    console.error('批量删除失败:', error.message);
  });

// 批量恢复主题
api.theme.batchRestoreThemes([1, 2, 3])
  .then(result => {
    console.log('批量恢复成功:', result.message);
  })
  .catch(error => {
    console.error('批量恢复失败:', error.message);
  });
```

## 高级用法

### 自定义API客户端实例

```javascript
import { ApiClient } from 'utils/api-client';

// 创建自定义API客户端实例
const customApiClient = new ApiClient({
  baseUrl: 'https://custom-api.example.com/api/v1',
  timeout: 60000, // 60秒超时
  maxRetries: 5, // 最多重试5次
  withCredentials: false // 不携带凭证
});

// 添加自定义请求拦截器
customApiClient.addRequestInterceptor((config) => {
  console.log('请求配置:', config);
  return config;
});

// 添加自定义响应拦截器
customApiClient.addResponseInterceptor((response) => {
  console.log('响应数据:', response);
  return response;
});

// 使用自定义API客户端
customApiClient.get('/resources')
  .then(result => {
    console.log('资源列表:', result.data);
  });
```

### 数据验证和规范化

```javascript
import { DataTransformer } from 'utils/api-client/data-transformer';

// 创建数据转换器实例
const dataTransformer = new DataTransformer();

// 定义数据模式
const userSchema = {
  name: { type: 'string', required: true },
  age: { type: 'number', required: true },
  email: { 
    type: 'string', 
    required: true,
    validator: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
  },
  role: { 
    type: 'string', 
    default: 'user',
    transform: (value) => value.toLowerCase()
  }
};

// 验证数据
const userData = {
  name: 'John',
  age: 30,
  email: '<EMAIL>',
  role: 'ADMIN'
};

if (dataTransformer.validate(userData, userSchema)) {
  console.log('数据有效');
  
  // 规范化数据
  const normalizedData = dataTransformer.normalize(userData, userSchema);
  console.log('规范化数据:', normalizedData); // role将被转换为小写
} else {
  console.error('数据无效');
}
```

## 配置选项

API客户端支持以下配置选项：

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| baseUrl | string | 'http://localhost:3010/api/v1' | API基础URL |
| useMock | boolean | true (开发环境) | 是否使用模拟数据 |
| mockBaseUrl | string | 'http://localhost:3010/mock-api/v1' | 模拟数据基础URL |
| timeout | number | 30000 | 请求超时时间（毫秒） |
| withCredentials | boolean | true | 跨域请求是否携带凭证 |
| cacheTTL | number | 300000 | 默认缓存时间（毫秒） |
| maxRetries | number | 3 | 最大重试次数 |
| maxRetryDelay | number | 30000 | 最大重试延迟（毫秒） |
| autoHandleError | boolean | true | 是否自动处理错误 |
| autoTransform | boolean | true | 是否自动转换数据格式 |
| enableCache | boolean | true | 是否启用缓存 |
| useLocalStorageCache | boolean | true | 是否使用本地存储缓存 |

## 错误处理

API客户端会自动处理以下类型的错误：

- **网络错误**：请求发送但没有收到响应
- **超时错误**：请求超过指定时间未完成
- **取消错误**：请求被手动取消
- **服务器错误**：服务器返回5xx错误
- **客户端错误**：服务器返回4xx错误
- **业务错误**：服务器返回成功状态码但业务逻辑错误

每种错误都会包含以下信息：

- **message**：错误消息
- **code**：错误代码
- **status**：HTTP状态码（如果有）
- **response**：原始响应对象（如果有）

## 单元测试

API客户端包含完整的单元测试，可以通过以下命令运行：

```bash
npm test -- --testPathPattern=api-client
```
