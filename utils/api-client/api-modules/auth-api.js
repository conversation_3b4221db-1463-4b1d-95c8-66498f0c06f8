/**
 * 认证API模块
 */

/**
 * 创建认证API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 认证API对象
 */
const authApi = apiClient => ({
  /**
     * 登录
     * @param {Object} data - 登录数据
     * @param {string} data.code - 微信登录code
     * @param {Object} data.userInfo - 用户信息
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  login(data, config = {}) {
    return apiClient.post('/auth/login', data, {
      maxRetries: 2, // 最多重试2次
      useCache: false, // 不缓存登录请求
      ...config
    });
  },

  /**
     * 登出
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  logout(config = {}) {
    return apiClient.post('/auth/logout', {}, {
      maxRetries: 1, // 最多重试1次
      useCache: false, // 不缓存登出请求
      ...config
    });
  },

  /**
     * 刷新令牌
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  refreshToken(config = {}) {
    return apiClient.post('/auth/refresh-token', {}, {
      maxRetries: 3, // 最多重试3次
      useCache: false, // 不缓存刷新令牌请求
      ...config
    });
  },

  /**
     * 获取当前用户信息
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getCurrentUser(config = {}) {
    return apiClient.get('/auth/me', {}, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 更新用户信息
     * @param {Object} data - 用户信息
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateUserInfo(data, config = {}) {
    return apiClient.put('/auth/me', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 绑定手机号
     * @param {Object} data - 绑定数据
     * @param {string} data.phone - 手机号
     * @param {string} data.code - 验证码
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  bindPhone(data, config = {}) {
    return apiClient.post('/auth/bind-phone', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 发送验证码
     * @param {Object} data - 发送数据
     * @param {string} data.phone - 手机号
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  sendVerificationCode(data, config = {}) {
    return apiClient.post('/auth/send-verification-code', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 验证验证码
     * @param {Object} data - 验证数据
     * @param {string} data.phone - 手机号
     * @param {string} data.code - 验证码
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  verifyCode(data, config = {}) {
    return apiClient.post('/auth/verify-code', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 手机号登录
     * @param {Object} data - 登录数据
     * @param {string} data.phone - 手机号
     * @param {string} data.password - 密码
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  loginWithPhone(data, config = {}) {
    return apiClient.post('/auth/login/phone', data, {
      maxRetries: 2, // 最多重试2次
      useCache: false, // 不缓存登录请求
      ...config
    });
  },

  /**
     * 手机号注册
     * @param {Object} data - 注册数据
     * @param {string} data.phone - 手机号
     * @param {string} data.password - 密码
     * @param {string} data.nickname - 昵称
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  registerWithPhone(data, config = {}) {
    return apiClient.post('/auth/register/phone', data, {
      maxRetries: 2, // 最多重试2次
      useCache: false, // 不缓存注册请求
      ...config
    });
  },

  /**
     * 重置密码
     * @param {Object} data - 重置数据
     * @param {string} data.phone - 手机号
     * @param {string} data.code - 验证码
     * @param {string} data.newPassword - 新密码
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  resetPassword(data, config = {}) {
    return apiClient.post('/auth/reset-password', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 修改密码
     * @param {Object} data - 修改数据
     * @param {string} data.oldPassword - 旧密码
     * @param {string} data.newPassword - 新密码
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  changePassword(data, config = {}) {
    return apiClient.post('/auth/change-password', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 清除认证缓存
     */
  clearCache() {
    apiClient.clearCache('/auth/me');
  }
});

export default authApi;
