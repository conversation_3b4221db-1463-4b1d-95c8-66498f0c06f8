/**
 * 清理API模块
 * 处理软删除数据的清理操作
 */

/**
 * 创建清理API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 清理API对象
 */
const cleanupApi = apiClient => ({
  /**
     * 获取清理配置
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getConfig(config = {}) {
    return apiClient.get('/cleanup/config', {}, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 更新清理配置
     * @param {Object} data - 清理配置
     * @param {number} data.retentionDays - 保留已删除数据的天数
     * @param {number} data.batchSize - 每次批处理的记录数
     * @param {boolean} data.autoCleanupEnabled - 是否启用自动清理
     * @param {number} data.cleanupInterval - 自动清理的时间间隔（毫秒）
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateConfig(data, config = {}) {
    return apiClient.put('/cleanup/config', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 手动执行清理
     * @param {Object} params - 清理参数
     * @param {string} params.modelName - 要清理的模型名称，不指定则清理所有模型
     * @param {number} params.retentionDays - 保留已删除数据的天数，不指定则使用配置值
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  runCleanup(params = {}, config = {}) {
    return apiClient.post('/cleanup/run', params, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 永久删除指定类型的单个记录
     * @param {string} type - 记录类型，如'note', 'tag', 'theme'等
     * @param {number|string} id - 记录ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  permanentDelete(type, id, config = {}) {
    // 使用cleanup服务的runCleanup方法，指定模型名称和ID
    const modelNameMap = {
      note: 'Note',
      insight: 'Insight',
      exercise: 'Exercise',
      tag: 'Tag',
      theme: 'Theme',
      dailyContent: 'DailyContent',
      learningPlan: 'LearningPlan'
    };

    const modelName = modelNameMap[type];
    if (!modelName) {
      return Promise.reject(new Error(`不支持的内容类型: ${type}`));
    }

    return apiClient.post('/cleanup/run', {
      modelName,
      specificIds: [id],
      forceDelete: true
    }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 批量永久删除指定类型的记录
     * @param {string} type - 记录类型，如'note', 'tag', 'theme'等
     * @param {Array<number|string>} ids - 记录ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchPermanentDelete(type, ids, config = {}) {
    // 使用cleanup服务的runCleanup方法，指定模型名称和ID数组
    const modelNameMap = {
      note: 'Note',
      insight: 'Insight',
      exercise: 'Exercise',
      tag: 'Tag',
      theme: 'Theme',
      dailyContent: 'DailyContent',
      learningPlan: 'LearningPlan'
    };

    const modelName = modelNameMap[type];
    if (!modelName) {
      return Promise.reject(new Error(`不支持的内容类型: ${type}`));
    }

    return apiClient.post('/cleanup/run', {
      modelName,
      specificIds: ids,
      forceDelete: true
    }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 启动自动清理
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  startAutoCleanup(config = {}) {
    return apiClient.post('/cleanup/start', {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 停止自动清理
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  stopAutoCleanup(config = {}) {
    return apiClient.post('/cleanup/stop', {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 清除清理缓存
     */
  clearCache() {
    apiClient.clearCache('/cleanup');
  }
});

export default cleanupApi;
