/**
 * 每日内容API模块
 */

/**
 * 创建每日内容API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 每日内容API对象
 */
const dailyContentApi = apiClient => ({
  /**
     * 获取学习计划的每日内容
     * @param {number|string} planId - 学习计划ID
     * @param {Object} params - 查询参数
     * @param {string} params.date - 日期，格式为YYYY-MM-DD
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getDailyContent(planId, params = {}, config = {}) {
    const apiParams = {
      date: params.date
    };

    return apiClient.get(`/learning-plans/${planId}/daily-content`, apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取特定日期的每日内容
     * @param {number|string} planId - 学习计划ID
     * @param {string} date - 日期，格式为YYYY-MM-DD
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getDailyContentByDate(planId, date, config = {}) {
    return apiClient.get(`/learning-plans/${planId}/daily-content`, { date }, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取特定天数的每日内容
     * @param {number|string} planId - 学习计划ID
     * @param {number} day - 天数
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getDailyContentByDay(planId, day, config = {}) {
    return apiClient.get(`/learning-plans/${planId}/daily-content/day/${day}`, {}, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 标记每日内容完成
     * @param {number|string} id - 每日内容ID
     * @param {Object} data - 完成数据
     * @param {number} data.rating - 评分
     * @param {string} data.notes - 笔记
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  completeDailyContent(id, data = {}, config = {}) {
    return apiClient.post(`/daily-contents/${id}/complete`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 取消标记每日内容完成
     * @param {number|string} id - 每日内容ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  uncompleteDailyContent(id, config = {}) {
    return apiClient.delete(`/daily-contents/${id}/complete`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取用户的每日进度
     * @param {Object} params - 查询参数
     * @param {string} params.date - 日期，格式为YYYY-MM-DD
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserDailyProgress(params = {}, config = {}) {
    const apiParams = {
      date: params.date
    };

    return apiClient.get('/user/daily-progress', apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取用户的连续学习天数
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserStreak(config = {}) {
    return apiClient.get('/user/streak', {}, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取用户的学习统计
     * @param {Object} params - 查询参数
     * @param {string} params.startDate - 开始日期，格式为YYYY-MM-DD
     * @param {string} params.endDate - 结束日期，格式为YYYY-MM-DD
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserStats(params = {}, config = {}) {
    const apiParams = {
      start_date: params.startDate,
      end_date: params.endDate
    };

    return apiClient.get('/user/stats', apiParams, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取学习计划的每日内容列表（V2）
     * @param {number|string} planId - 学习计划ID
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getPlanContents(planId, params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get(`/daily-contents/plan/${planId}`, apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取内容详情（V2）
     * @param {number|string} contentId - 内容ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getContentById(contentId, config = {}) {
    return apiClient.get(`/daily-contents/${contentId}`, {}, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 更新内容（V2）
     * @param {number|string} contentId - 内容ID
     * @param {Object} data - 更新数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateContent(contentId, data, config = {}) {
    return apiClient.put(`/daily-contents/${contentId}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 软删除内容（V2）
     * @param {number|string} contentId - 内容ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  softDelete(contentId, config = {}) {
    return apiClient.delete(`/daily-contents/${contentId}/soft-delete`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 恢复内容（V2）
     * @param {number|string} contentId - 内容ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  restore(contentId, config = {}) {
    return apiClient.post(`/daily-contents/${contentId}/restore`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取已删除的内容列表（V2）
     * @param {number|string} planId - 学习计划ID
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getDeletedContents(planId, params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get(`/daily-contents/plan/${planId}/deleted`, apiParams, {
      useCache: false, // 不缓存已删除列表
      ...config
    });
  },

  /**
     * 批量软删除内容（V2）
     * @param {Array<number|string>} ids - 内容ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchSoftDelete(ids, config = {}) {
    return apiClient.post('/daily-contents/batch-soft-delete', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 批量恢复内容（V2）
     * @param {Array<number|string>} ids - 内容ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchRestore(ids, config = {}) {
    return apiClient.post('/daily-contents/batch-restore', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 清除每日内容缓存
     */
  clearCache() {
    apiClient.clearCache('/daily-contents');
    apiClient.clearCache('/learning-plans');
    apiClient.clearCache('/user/daily-progress');
    apiClient.clearCache('/user/streak');
    apiClient.clearCache('/user/stats');
  }
});

export default dailyContentApi;
