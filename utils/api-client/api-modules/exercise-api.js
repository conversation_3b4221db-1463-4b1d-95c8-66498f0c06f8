/**
 * 练习API模块
 */

/**
 * 创建练习API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 练习API对象
 */
const exerciseApi = apiClient => ({
  /**
     * 获取练习列表
     * @param {Object} params - 查询参数
     * @param {string} params.difficulty - 难度
     * @param {boolean} params.isVerified - 是否已验证
     * @param {boolean} params.isOfficial - 是否官方
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getExercises(params = {}, config = {}) {
    // 转换参数名称
    const apiParams = {
      difficulty: params.difficulty,
      is_verified: params.isVerified,
      is_official: params.isOfficial,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/exercises', apiParams, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 获取练习详情
     * @param {number|string} id - 练习ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getExercise(id, config = {}) {
    return apiClient.get(`/exercises/${id}`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 创建练习
     * @param {Object} data - 练习数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createExercise(data, config = {}) {
    return apiClient.post('/exercises', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新练习
     * @param {number|string} id - 练习ID
     * @param {Object} data - 练习数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateExercise(id, data, config = {}) {
    return apiClient.put(`/exercises/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除练习
     * @param {number|string} id - 练习ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteExercise(id, config = {}) {
    return apiClient.delete(`/exercises/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 恢复已删除的练习
     * @param {number|string} id - 练习ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  restoreExercise(id, config = {}) {
    return apiClient.put(`/exercises/${id}/restore`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 标记练习完成
     * @param {number|string} id - 练习ID
     * @param {Object} data - 完成数据
     * @param {number} data.rating - 评分
     * @param {string} data.notes - 笔记
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  completeExercise(id, data = {}, config = {}) {
    return apiClient.post(`/exercises/${id}/complete`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 获取练习标签
     * @param {number|string} id - 练习ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getExerciseTags(id, config = {}) {
    return apiClient.get(`/exercises/${id}/tags`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 添加练习标签
     * @param {number|string} id - 练习ID
     * @param {number|string} tagId - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  addExerciseTag(id, tagId, config = {}) {
    return apiClient.post(`/exercises/${id}/tags`, { tagId }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 删除练习标签
     * @param {number|string} id - 练习ID
     * @param {number|string} tagId - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  removeExerciseTag(id, tagId, config = {}) {
    return apiClient.delete(`/exercises/${id}/tags/${tagId}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取已删除的练习列表
     * @param {Object} params - 查询参数
     * @param {string} params.userId - 用户ID
     * @param {string} params.keyword - 搜索关键词
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getDeletedExercises(params = {}, config = {}) {
    const apiParams = {
      user_id: params.userId,
      keyword: params.keyword,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/exercises/deleted', apiParams, {
      useCache: false, // 不缓存已删除列表
      ...config
    });
  },

  /**
     * 批量删除练习
     * @param {Array<number|string>} ids - 练习ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchDeleteExercises(ids, config = {}) {
    return apiClient.post('/exercises/batch-delete', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 批量恢复练习
     * @param {Array<number|string>} ids - 练习ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchRestoreExercises(ids, config = {}) {
    return apiClient.post('/exercises/batch-restore', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取相似练习
     * @param {number|string} id - 练习ID
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 返回数量限制
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getSimilarExercises(id, params = {}, config = {}) {
    const apiParams = {
      limit: params.limit || 3
    };

    return apiClient.get(`/exercises/${id}/similar`, apiParams, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 清除练习缓存
     */
  clearCache() {
    apiClient.clearCache('/exercises');
  }
});

export default exerciseApi;
