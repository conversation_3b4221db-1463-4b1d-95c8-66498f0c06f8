/**
 * API模块集合
 * 整合所有API模块
 */

import apiClient from '../index';
import themeApi from './theme-api';
import tagApi from './tag-api';
import learningPlanApi from './learning-plan-api';
import exerciseApi from './exercise-api';
import insightApi from './insight-api';
import noteApi from './note-api';
import dailyContentApi from './daily-content-api';
import authApi from './auth-api';
import userApi from './user-api';
import squareApi from './square-api';
import cleanupApi from './cleanup-api';

// 创建API对象
const api = {
  theme: themeApi(apiClient),
  tag: tagApi(apiClient),
  learningPlan: learningPlanApi(apiClient),
  exercise: exerciseApi(apiClient),
  insight: insightApi(apiClient),
  note: noteApi(apiClient),
  dailyContent: dailyContentApi(apiClient),
  auth: authApi(apiClient),
  user: user<PERSON><PERSON>(apiClient),
  square: squareApi(apiClient),
  cleanup: cleanup<PERSON>pi(apiClient),

  // 清除所有缓存
  clearCache() {
    apiClient.clearCache();
  },

  // 清除特定实体的缓存
  clearEntityCache(entity) {
    apiClient.clearCache(`/${entity}`);
  }
};

export default api;
