/**
 * 观点API模块
 */

/**
 * 创建观点API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 观点API对象
 */
const insightApi = apiClient => ({
  /**
     * 获取观点列表
     * @param {Object} params - 查询参数
     * @param {boolean} params.isVerified - 是否已验证
     * @param {boolean} params.isOfficial - 是否官方
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getInsights(params = {}, config = {}) {
    // 转换参数名称
    const apiParams = {
      is_verified: params.isVerified,
      is_official: params.isOfficial,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/insights', apiParams, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 获取观点详情
     * @param {number|string} id - 观点ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getInsight(id, config = {}) {
    return apiClient.get(`/insights/${id}`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 创建观点
     * @param {Object} data - 观点数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createInsight(data, config = {}) {
    return apiClient.post('/insights', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新观点
     * @param {number|string} id - 观点ID
     * @param {Object} data - 观点数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateInsight(id, data, config = {}) {
    return apiClient.put(`/insights/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除观点
     * @param {number|string} id - 观点ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteInsight(id, config = {}) {
    return apiClient.delete(`/insights/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 恢复已删除的观点
     * @param {number|string} id - 观点ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  restoreInsight(id, config = {}) {
    return apiClient.put(`/insights/${id}/restore`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 标记观点已读
     * @param {number|string} id - 观点ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  readInsight(id, config = {}) {
    return apiClient.post(`/insights/${id}/read`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 点赞观点
     * @param {number|string} id - 观点ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  likeInsight(id, config = {}) {
    return apiClient.post(`/insights/${id}/like`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 取消点赞观点
     * @param {number|string} id - 观点ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  unlikeInsight(id, config = {}) {
    return apiClient.delete(`/insights/${id}/like`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取观点标签
     * @param {number|string} id - 观点ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getInsightTags(id, config = {}) {
    return apiClient.get(`/insights/${id}/tags`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 添加观点标签
     * @param {number|string} id - 观点ID
     * @param {number|string} tagId - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  addInsightTag(id, tagId, config = {}) {
    return apiClient.post(`/insights/${id}/tags`, { tagId }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 删除观点标签
     * @param {number|string} id - 观点ID
     * @param {number|string} tagId - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  removeInsightTag(id, tagId, config = {}) {
    return apiClient.delete(`/insights/${id}/tags/${tagId}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取已删除的观点列表
     * @param {Object} params - 查询参数
     * @param {string} params.userId - 用户ID
     * @param {string} params.keyword - 搜索关键词
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getDeletedInsights(params = {}, config = {}) {
    const apiParams = {
      user_id: params.userId,
      keyword: params.keyword,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/insights/deleted', apiParams, {
      useCache: false, // 不缓存已删除列表
      ...config
    });
  },

  /**
     * 批量删除观点
     * @param {Array<number|string>} ids - 观点ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchDeleteInsights(ids, config = {}) {
    return apiClient.post('/insights/batch-delete', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 批量恢复观点
     * @param {Array<number|string>} ids - 观点ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchRestoreInsights(ids, config = {}) {
    return apiClient.post('/insights/batch-restore', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取相似观点
     * @param {number|string} id - 观点ID
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 返回数量限制
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getSimilarInsights(id, params = {}, config = {}) {
    const apiParams = {
      limit: params.limit || 3
    };

    return apiClient.get(`/insights/${id}/similar`, apiParams, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 清除观点缓存
     */
  clearCache() {
    apiClient.clearCache('/insights');
  }
});

export default insightApi;
