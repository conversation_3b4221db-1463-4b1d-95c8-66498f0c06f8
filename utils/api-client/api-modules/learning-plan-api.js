/**
 * 学习计划API模块
 */

/**
 * 创建学习计划API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 学习计划API对象
 */
const learningPlanApi = apiClient => ({
  /**
     * 获取学习计划列表
     * @param {Object} params - 查询参数
     * @param {number} params.userId - 用户ID
     * @param {boolean} params.isCurrent - 是否当前计划
     * @param {string} params.status - 状态
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getLearningPlans(params = {}, config = {}) {
    // 转换参数名称
    const apiParams = {
      user_id: params.userId,
      is_current: params.isCurrent,
      status: params.status,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/learning-plans', apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取学习计划详情
     * @param {number|string} id - 学习计划ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getLearningPlan(id, config = {}) {
    return apiClient.get(`/learning-plans/${id}`, {}, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 创建学习计划
     * @param {Object} data - 学习计划数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createLearningPlan(data, config = {}) {
    return apiClient.post('/learning-plans', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新学习计划
     * @param {number|string} id - 学习计划ID
     * @param {Object} data - 学习计划数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateLearningPlan(id, data, config = {}) {
    return apiClient.put(`/learning-plans/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除学习计划
     * @param {number|string} id - 学习计划ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteLearningPlan(id, config = {}) {
    return apiClient.delete(`/learning-plans/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 恢复已删除的学习计划
     * @param {number|string} id - 学习计划ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  restoreLearningPlan(id, config = {}) {
    return apiClient.put(`/learning-plans/${id}/restore`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 激活学习计划
     * @param {number|string} id - 学习计划ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  activateLearningPlan(id, config = {}) {
    return apiClient.put(`/learning-plans/${id}/activate`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取系统默认学习计划
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getSystemDefaultPlan(config = {}) {
    return apiClient.get('/learning-plans/system/default', {}, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 获取学习模板列表
     * @param {Object} params - 查询参数
     * @param {number} params.themeId - 主题ID
     * @param {string} params.difficulty - 难度
     * @param {boolean} params.isOfficial - 是否官方
     * @param {boolean} params.isPublic - 是否公开
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getLearningTemplates(params = {}, config = {}) {
    // 转换参数名称
    const apiParams = {
      theme_id: params.themeId,
      difficulty: params.difficulty,
      is_official: params.isOfficial,
      is_public: params.isPublic,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/learning-templates', apiParams, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 获取学习模板详情
     * @param {number|string} id - 学习模板ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getLearningTemplate(id, config = {}) {
    return apiClient.get(`/learning-templates/${id}`, {}, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 创建学习模板
     * @param {Object} data - 学习模板数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createLearningTemplate(data, config = {}) {
    return apiClient.post('/learning-templates', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新学习模板
     * @param {number|string} id - 学习模板ID
     * @param {Object} data - 学习模板数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateLearningTemplate(id, data, config = {}) {
    return apiClient.put(`/learning-templates/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除学习模板
     * @param {number|string} id - 学习模板ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteLearningTemplate(id, config = {}) {
    return apiClient.delete(`/learning-templates/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 从学习模板创建学习计划
     * @param {number|string} templateId - 学习模板ID
     * @param {Object} data - 额外数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createPlanFromTemplate(templateId, data = {}, config = {}) {
    return apiClient.post(`/learning-templates/${templateId}/create-plan`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 清除学习计划缓存
     */
  clearCache() {
    apiClient.clearCache('/learning-plans');
    apiClient.clearCache('/learning-templates');
  }
});

export default learningPlanApi;
