/**
 * 笔记API模块
 */

/**
 * 创建笔记API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 笔记API对象
 */
const noteApi = apiClient => ({
  /**
     * 获取笔记列表
     * @param {Object} params - 查询参数
     * @param {number} params.userId - 用户ID
     * @param {boolean} params.isPublic - 是否公开
     * @param {string} params.keyword - 搜索关键词
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getNotes(params = {}, config = {}) {
    // 转换参数名称
    const apiParams = {
      user_id: params.userId,
      is_public: params.isPublic,
      keyword: params.keyword,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/notes', apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取笔记详情
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getNote(id, config = {}) {
    return apiClient.get(`/notes/${id}`, {}, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 创建笔记
     * @param {Object} data - 笔记数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createNote(data, config = {}) {
    return apiClient.post('/notes', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} data - 笔记数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateNote(id, data, config = {}) {
    return apiClient.put(`/notes/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteNote(id, config = {}) {
    return apiClient.delete(`/notes/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 软删除笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  softDeleteNote(id, config = {}) {
    return apiClient.delete(`/notes/${id}/soft-delete`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 恢复已删除的笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  restoreNote(id, config = {}) {
    return apiClient.put(`/notes/${id}/restore`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 点赞笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  likeNote(id, config = {}) {
    return apiClient.post(`/notes/${id}/like`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 取消点赞笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  unlikeNote(id, config = {}) {
    return apiClient.delete(`/notes/${id}/like`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取笔记评论
     * @param {number|string} id - 笔记ID
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getNoteComments(id, params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get(`/notes/${id}/comments`, apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 添加笔记评论
     * @param {number|string} id - 笔记ID
     * @param {Object} data - 评论数据
     * @param {string} data.content - 评论内容
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  addNoteComment(id, data, config = {}) {
    return apiClient.post(`/notes/${id}/comments`, data, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 删除笔记评论
     * @param {number|string} id - 笔记ID
     * @param {number|string} commentId - 评论ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteNoteComment(id, commentId, config = {}) {
    return apiClient.delete(`/notes/${id}/comments/${commentId}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取笔记标签
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getNoteTags(id, config = {}) {
    return apiClient.get(`/notes/${id}/tags`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 添加笔记标签
     * @param {number|string} id - 笔记ID
     * @param {number|string} tagId - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  addNoteTag(id, tagId, config = {}) {
    return apiClient.post(`/notes/${id}/tags`, { tagId }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 删除笔记标签
     * @param {number|string} id - 笔记ID
     * @param {number|string} tagId - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  removeNoteTag(id, tagId, config = {}) {
    return apiClient.delete(`/notes/${id}/tags/${tagId}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取已删除的笔记列表
     * @param {Object} params - 查询参数
     * @param {string} params.userId - 用户ID
     * @param {string} params.keyword - 搜索关键词
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getDeletedNotes(params = {}, config = {}) {
    const apiParams = {
      user_id: params.userId,
      keyword: params.keyword,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/notes/deleted', apiParams, {
      useCache: false, // 不缓存已删除列表
      ...config
    });
  },

  /**
     * 批量软删除笔记
     * @param {Array<number|string>} ids - 笔记ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchSoftDeleteNotes(ids, config = {}) {
    return apiClient.post('/notes/batch-soft-delete', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 批量恢复笔记
     * @param {Array<number|string>} ids - 笔记ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchRestoreNotes(ids, config = {}) {
    return apiClient.post('/notes/batch-restore', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 清空回收站
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  emptyTrash(config = {}) {
    return apiClient.delete('/notes/trash', {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 发布笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  publishNote(id, config = {}) {
    return apiClient.post(`/notes/${id}/publish`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 取消发布笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  unpublishNote(id, config = {}) {
    return apiClient.post(`/notes/${id}/unpublish`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取相似笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 返回数量限制
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getSimilarNotes(id, params = {}, config = {}) {
    const apiParams = {
      limit: params.limit || 3
    };

    return apiClient.get(`/notes/${id}/similar`, apiParams, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 清除笔记缓存
     */
  clearCache() {
    apiClient.clearCache('/notes');
  }
});

export default noteApi;
