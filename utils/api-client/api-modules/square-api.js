/**
 * 广场API模块
 */

/**
 * 创建广场API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 广场API对象
 */
const squareApi = apiClient => ({
  /**
     * 获取广场笔记列表
     * @param {Object} params - 查询参数
     * @param {number} params.tagId - 标签ID
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getNotes(params = {}, config = {}) {
    const apiParams = {
      tag_id: params.tagId,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/square/notes', apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取热门标签
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getTags(config = {}) {
    return apiClient.get('/square/tags', {}, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 获取推荐用户
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getRecommendedUsers(params = {}, config = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/recommended-users', apiParams, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取热门学习计划
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getHotLearningPlans(params = {}, config = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/hot-learning-plans', apiParams, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取热门练习
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getHotExercises(params = {}, config = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/hot-exercises', apiParams, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取热门观点
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getHotInsights(params = {}, config = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/hot-insights', apiParams, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 搜索广场内容
     * @param {Object} params - 查询参数
     * @param {string} params.keyword - 关键词
     * @param {string} params.type - 内容类型，可选值：note, learning_plan, exercise, insight
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  search(params = {}, config = {}) {
    const apiParams = {
      keyword: params.keyword,
      type: params.type,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/square/search', apiParams, {
      useCache: false, // 不缓存搜索结果，确保实时性
      ...config
    });
  },

  /**
     * 获取推荐笔记
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getRecommendedNotes(params = {}, config = {}) {
    const apiParams = {
      limit: params.limit || 5
    };

    return apiClient.get('/square/recommended', apiParams, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取广场动态
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getActivities(params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/square/activities', apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取关注用户的动态
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getFollowingActivities(params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/square/following-activities', apiParams, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 获取用户的广场内容
     * @param {number|string} userId - 用户ID
     * @param {Object} params - 查询参数
     * @param {string} params.type - 内容类型，可选值：note, learning_plan, exercise, insight
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserContents(userId, params = {}, config = {}) {
    const apiParams = {
      type: params.type,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get(`/square/users/${userId}/contents`, apiParams, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 获取广场统计数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getStats(config = {}) {
    return apiClient.get('/square/stats', {}, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 清除广场缓存
     */
  clearCache() {
    apiClient.clearCache('/square');
  }
});

export default squareApi;
