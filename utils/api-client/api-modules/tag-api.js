/**
 * 标签API模块
 */

/**
 * 创建标签API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 标签API对象
 */
const tagApi = apiClient => ({
  /**
     * 获取标签列表
     * @param {Object} params - 查询参数
     * @param {number} params.categoryId - 分类ID
     * @param {boolean} params.isVerified - 是否已验证
     * @param {boolean} params.isOfficial - 是否官方
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getTags(params = {}, config = {}) {
    // 转换参数名称
    const apiParams = {
      category_id: params.categoryId,
      is_verified: params.isVerified,
      is_official: params.isOfficial,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/tags', apiParams, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 获取标签详情
     * @param {number|string} id - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getTag(id, config = {}) {
    return apiClient.get(`/tags/${id}`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 创建标签
     * @param {Object} data - 标签数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createTag(data, config = {}) {
    return apiClient.post('/tags', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新标签
     * @param {number|string} id - 标签ID
     * @param {Object} data - 标签数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateTag(id, data, config = {}) {
    return apiClient.put(`/tags/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除标签
     * @param {number|string} id - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteTag(id, config = {}) {
    return apiClient.delete(`/tags/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 恢复已删除的标签
     * @param {number|string} id - 标签ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  restoreTag(id, config = {}) {
    return apiClient.put(`/tags/${id}/restore`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取标签分类列表
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getTagCategories(config = {}) {
    return apiClient.get('/tag-categories', {}, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 获取标签分类详情
     * @param {number|string} id - 标签分类ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getTagCategory(id, config = {}) {
    return apiClient.get(`/tag-categories/${id}`, {}, {
      useCache: true,
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时缓存
      ...config
    });
  },

  /**
     * 创建标签分类
     * @param {Object} data - 标签分类数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createTagCategory(data, config = {}) {
    return apiClient.post('/tag-categories', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新标签分类
     * @param {number|string} id - 标签分类ID
     * @param {Object} data - 标签分类数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateTagCategory(id, data, config = {}) {
    return apiClient.put(`/tag-categories/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除标签分类
     * @param {number|string} id - 标签分类ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteTagCategory(id, config = {}) {
    return apiClient.delete(`/tag-categories/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取学习计划的标签
     * @param {number|string} planId - 学习计划ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getTagsByPlanId(planId, config = {}) {
    return apiClient.get(`/tags/learning-plans/${planId}/tags`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 清除标签缓存
     */
  clearCache() {
    apiClient.clearCache('/tags');
    apiClient.clearCache('/tag-categories');
  }
});

export default tagApi;
