/**
 * 主题API模块
 */

/**
 * 创建主题API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 主题API对象
 */
const themeApi = apiClient => ({
  /**
     * 获取主题列表
     * @param {Object} params - 查询参数
     * @param {boolean} params.isActive - 是否激活
     * @param {string} params.sortBy - 排序字段
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getThemes(params = {}, config = {}) {
    return apiClient.get('/themes', params, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 获取主题详情
     * @param {number|string} id - 主题ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getTheme(id, config = {}) {
    return apiClient.get(`/themes/${id}`, {}, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 创建主题
     * @param {Object} data - 主题数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  createTheme(data, config = {}) {
    return apiClient.post('/themes', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 更新主题
     * @param {number|string} id - 主题ID
     * @param {Object} data - 主题数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateTheme(id, data, config = {}) {
    return apiClient.put(`/themes/${id}`, data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 删除主题
     * @param {number|string} id - 主题ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  deleteTheme(id, config = {}) {
    return apiClient.delete(`/themes/${id}`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 恢复已删除的主题
     * @param {number|string} id - 主题ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  restoreTheme(id, config = {}) {
    return apiClient.put(`/themes/${id}/restore`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 批量删除主题
     * @param {Array<number|string>} ids - 主题ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchDeleteThemes(ids, config = {}) {
    return apiClient.post('/themes/batch-delete', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 批量恢复主题
     * @param {Array<number|string>} ids - 主题ID数组
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  batchRestoreThemes(ids, config = {}) {
    return apiClient.post('/themes/batch-restore', { ids }, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 清除主题缓存
     */
  clearCache() {
    apiClient.clearCache('/themes');
  }
});

export default themeApi;
