/**
 * 用户API模块
 */

/**
 * 创建用户API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 用户API对象
 */
const userApi = apiClient => ({
  /**
     * 获取当前用户信息
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getCurrentUser(config = {}) {
    return apiClient.get('/users/me', {}, {
      useCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟缓存
      ...config
    });
  },

  /**
     * 更新当前用户信息
     * @param {Object} data - 用户信息
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateCurrentUser(data, config = {}) {
    return apiClient.put('/users/me', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 获取用户设置
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserSettings(config = {}) {
    return apiClient.get('/users/me/settings', {}, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 更新用户设置
     * @param {Object} data - 设置数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateUserSettings(data, config = {}) {
    return apiClient.put('/users/me/settings', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 获取用户通知设置
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getNotificationSettings(config = {}) {
    return apiClient.get('/users/me/notification-settings', {}, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 更新用户通知设置
     * @param {Object} data - 通知设置数据
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  updateNotificationSettings(data, config = {}) {
    return apiClient.put('/users/me/notification-settings', data, {
      maxRetries: 2, // 最多重试2次
      ...config
    });
  },

  /**
     * 获取用户通知列表
     * @param {Object} params - 查询参数
     * @param {boolean} params.isRead - 是否已读
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getNotifications(params = {}, config = {}) {
    const apiParams = {
      is_read: params.isRead,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/users/me/notifications', apiParams, {
      useCache: false, // 不缓存通知列表，确保实时性
      ...config
    });
  },

  /**
     * 标记通知为已读
     * @param {number|string} id - 通知ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  markNotificationAsRead(id, config = {}) {
    return apiClient.put(`/users/me/notifications/${id}/read`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 标记所有通知为已读
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  markAllNotificationsAsRead(config = {}) {
    return apiClient.put('/users/me/notifications/read-all', {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取用户成就列表
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserAchievements(config = {}) {
    return apiClient.get('/users/me/achievements', {}, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取用户徽章列表
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserBadges(config = {}) {
    return apiClient.get('/users/me/badges', {}, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取用户等级信息
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserLevel(config = {}) {
    return apiClient.get('/users/me/level', {}, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 获取用户积分历史
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getPointsHistory(params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/users/me/points-history', apiParams, {
      useCache: false, // 不缓存积分历史，确保实时性
      ...config
    });
  },

  /**
     * 获取用户学习统计
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getUserStats(config = {}) {
    return apiClient.get('/users/me/stats', {}, {
      useCache: true,
      cacheTTL: 30 * 60 * 1000, // 30分钟缓存
      ...config
    });
  },

  /**
     * 获取用户学习记录
     * @param {Object} params - 查询参数
     * @param {string} params.startDate - 开始日期，格式为YYYY-MM-DD
     * @param {string} params.endDate - 结束日期，格式为YYYY-MM-DD
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getLearningRecords(params = {}, config = {}) {
    const apiParams = {
      start_date: params.startDate,
      end_date: params.endDate,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/users/me/learning-records', apiParams, {
      useCache: true,
      cacheTTL: 30 * 60 * 1000, // 30分钟缓存
      ...config
    });
  },

  /**
     * 获取用户学习趋势
     * @param {Object} params - 查询参数
     * @param {number} params.days - 天数
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getLearningTrend(params = {}, config = {}) {
    const apiParams = {
      days: params.days || 30
    };

    return apiClient.get('/users/me/learning-trend', apiParams, {
      useCache: true,
      cacheTTL: 60 * 60 * 1000, // 1小时缓存
      ...config
    });
  },

  /**
     * 上传用户头像
     * @param {Object} data - 上传数据
     * @param {File|Blob} data.file - 头像文件
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  uploadAvatar(data, config = {}) {
    return apiClient.post('/users/me/avatar', data, {
      maxRetries: 2, // 最多重试2次
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    });
  },

  /**
     * 关注用户
     * @param {number|string} userId - 用户ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  followUser(userId, config = {}) {
    return apiClient.post(`/users/${userId}/follow`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 取消关注用户
     * @param {number|string} userId - 用户ID
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  unfollowUser(userId, config = {}) {
    return apiClient.delete(`/users/${userId}/follow`, {}, {
      maxRetries: 1, // 最多重试1次
      ...config
    });
  },

  /**
     * 获取关注列表
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getFollowing(params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/users/me/following', apiParams, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 获取粉丝列表
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {Object} config - 请求配置
     * @returns {Promise} - 请求Promise
     */
  getFollowers(params = {}, config = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/users/me/followers', apiParams, {
      useCache: true,
      cacheTTL: 10 * 60 * 1000, // 10分钟缓存
      ...config
    });
  },

  /**
     * 清除用户缓存
     */
  clearCache() {
    apiClient.clearCache('/users/me');
    apiClient.clearCache('/users/me/settings');
    apiClient.clearCache('/users/me/notification-settings');
    apiClient.clearCache('/users/me/achievements');
    apiClient.clearCache('/users/me/badges');
    apiClient.clearCache('/users/me/level');
    apiClient.clearCache('/users/me/stats');
    apiClient.clearCache('/users/me/learning-trend');
    apiClient.clearCache('/users/me/following');
    apiClient.clearCache('/users/me/followers');
  }
});

export default userApi;
