/**
 * 认证拦截器
 * 提供令牌管理和自动刷新功能
 */

// 导入令牌管理器
const tokenManager = require('../token-manager');

// 是否正在刷新令牌
let isRefreshing = false;
// 等待令牌刷新的请求队列
const refreshQueue = [];

/**
 * 处理等待队列中的请求
 * @param {string|null} token - 新的访问令牌
 * @param {Error|null} error - 刷新过程中的错误
 */
const processQueue = (token, error = null) => {
  refreshQueue.forEach(promise => {
    if (error) {
      promise.reject(error);
    } else {
      promise.resolve(token);
    }
  });

  // 清空队列
  refreshQueue.length = 0;
};

/**
 * 创建认证请求拦截器
 * @returns {Function} 请求拦截器函数
 */
const createAuthRequestInterceptor = () => async config => {
  // 如果请求不需要认证，直接返回配置
  if (config.noAuth) {
    return config;
  }

  try {
    // 获取访问令牌（如果即将过期，会自动刷新）
    const token = await tokenManager.getToken();

    if (token) {
      // 添加认证头
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  } catch (error) {
    console.error('添加认证信息失败:', error);
    return config;
  }
};

/**
 * 创建认证响应拦截器
 * @param {Object} apiClient - API客户端实例
 * @returns {Function} 响应拦截器函数
 */
const createAuthResponseInterceptor = apiClient => async error => {
  // 如果不是响应错误或不是401错误，直接抛出
  if (!error.response || error.response.status !== 401) {
    throw error;
  }

  // 获取原始请求配置
  const originalRequest = error.config;

  // 如果已经尝试过刷新令牌，避免无限循环
  if (originalRequest._retry) {
    throw error;
  }

  // 标记请求已尝试刷新令牌
  originalRequest._retry = true;

  // 如果正在刷新令牌，将请求加入队列
  if (isRefreshing) {
    try {
      // 等待令牌刷新完成
      const token = await new Promise((resolve, reject) => {
        refreshQueue.push({ resolve, reject });
      });

      // 更新请求头
      originalRequest.headers['Authorization'] = `Bearer ${token}`;

      // 重试请求
      return apiClient.request(originalRequest);
    } catch (refreshError) {
      throw error; // 如果刷新失败，抛出原始错误
    }
  }

  // 开始刷新令牌
  isRefreshing = true;

  try {
    // 刷新令牌
    const token = await tokenManager.refreshToken();

    if (!token) {
      // 刷新失败，处理队列中的请求
      processQueue(null, new Error('刷新令牌失败'));
      throw error;
    }

    // 刷新成功，处理队列中的请求
    processQueue(token);

    // 更新请求头
    originalRequest.headers['Authorization'] = `Bearer ${token}`;

    // 重试请求
    return apiClient.request(originalRequest);
  } catch (refreshError) {
    // 刷新失败，处理队列中的请求
    processQueue(null, refreshError);
    throw error;
  } finally {
    // 重置刷新状态
    isRefreshing = false;
  }
};

module.exports = {
  createAuthRequestInterceptor,
  createAuthResponseInterceptor
};
