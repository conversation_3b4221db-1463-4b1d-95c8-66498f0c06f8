/**
 * 缓存管理器
 * 提供多级缓存机制，支持内存缓存和本地存储缓存
 */

export class CacheManager {
  constructor(options = {}) {
    this.options = {
      defaultTTL: 5 * 60 * 1000, // 默认缓存时间（5分钟）
      useLocalStorage: true, // 是否使用本地存储
      localStoragePrefix: 'api_cache_', // 本地存储前缀
      ...options
    };

    // 内存缓存
    this.memoryCache = new Map();

    // 初始化时清理过期的本地存储缓存
    if (this.options.useLocalStorage) {
      this.cleanExpiredLocalStorage();
    }
  }

  /**
   * 生成缓存键
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @returns {string} - 缓存键
   */
  generateKey(url, params = {}) {
    return `${url}:${JSON.stringify(params)}`;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} data - 缓存数据
   * @param {number} ttl - 缓存时间（毫秒）
   */
  set(key, data, ttl = this.options.defaultTTL) {
    const expireAt = Date.now() + ttl;
    const cacheItem = { data, expireAt };

    // 设置内存缓存
    this.memoryCache.set(key, cacheItem);

    // 设置本地存储缓存
    if (this.options.useLocalStorage) {
      try {
        localStorage.setItem(
          `${this.options.localStoragePrefix}${key}`,
          JSON.stringify(cacheItem)
        );
      } catch (error) {
        console.warn('无法写入本地存储缓存:', error);
      }
    }

    // 设置过期清理
    setTimeout(() => {
      this.memoryCache.delete(key);
    }, ttl);
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {*} - 缓存数据，如果不存在或已过期则返回null
   */
  get(key) {
    // 先尝试从内存缓存获取
    const memoryItem = this.memoryCache.get(key);

    if (memoryItem) {
      // 检查是否过期
      if (memoryItem.expireAt < Date.now()) {
        this.memoryCache.delete(key);
        return null;
      }

      return memoryItem.data;
    }

    // 如果内存缓存没有，尝试从本地存储获取
    if (this.options.useLocalStorage) {
      try {
        const localStorageKey = `${this.options.localStoragePrefix}${key}`;
        const localStorageItem = localStorage.getItem(localStorageKey);

        if (localStorageItem) {
          const parsedItem = JSON.parse(localStorageItem);

          // 检查是否过期
          if (parsedItem.expireAt < Date.now()) {
            localStorage.removeItem(localStorageKey);
            return null;
          }

          // 恢复到内存缓存
          this.memoryCache.set(key, parsedItem);

          return parsedItem.data;
        }
      } catch (error) {
        console.warn('无法读取本地存储缓存:', error);
      }
    }

    return null;
  }

  /**
   * 清除缓存
   * @param {string} keyPattern - 缓存键模式，用于部分清除
   */
  clear(keyPattern) {
    // 清除内存缓存
    if (!keyPattern) {
      // 清除所有缓存
      this.memoryCache.clear();
    } else {
      // 清除匹配的缓存
      for (const key of this.memoryCache.keys()) {
        if (key.includes(keyPattern)) {
          this.memoryCache.delete(key);
        }
      }
    }

    // 清除本地存储缓存
    if (this.options.useLocalStorage) {
      try {
        if (!keyPattern) {
          // 清除所有缓存
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith(this.options.localStoragePrefix)) {
              localStorage.removeItem(key);
            }
          }
        } else {
          // 清除匹配的缓存
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith(this.options.localStoragePrefix) &&
                key.includes(keyPattern)) {
              localStorage.removeItem(key);
            }
          }
        }
      } catch (error) {
        console.warn('无法清除本地存储缓存:', error);
      }
    }
  }

  /**
   * 清理过期的本地存储缓存
   */
  cleanExpiredLocalStorage() {
    if (!this.options.useLocalStorage) {
      return;
    }

    try {
      const now = Date.now();
      const keysToRemove = [];

      // 找出所有过期的缓存
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);

        if (key.startsWith(this.options.localStoragePrefix)) {
          const item = localStorage.getItem(key);

          try {
            const parsedItem = JSON.parse(item);

            if (parsedItem.expireAt < now) {
              keysToRemove.push(key);
            }
          } catch (e) {
            // 无效的JSON，移除
            keysToRemove.push(key);
          }
        }
      }

      // 移除过期缓存
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.warn('清理过期本地存储缓存失败:', error);
    }
  }
}
