/**
 * API客户端配置
 */

// 导入环境配置
const { getEnvConfig } = require('../env-config');

// 获取当前环境配置
const envConfig = getEnvConfig();

// 根据环境版本映射到API客户端配置
const getApiClientConfig = () => {
  // 环境映射
  const ENV_MAPPING = {
    develop: {
      baseUrl: envConfig.apiBaseUrl,
      useMock: envConfig.isTestMode
    },
    trial: {
      baseUrl: envConfig.apiBaseUrl,
      useMock: envConfig.isTestMode
    },
    release: {
      baseUrl: envConfig.apiBaseUrl,
      useMock: false // 生产环境始终关闭模拟数据
    }
  };

  // 获取当前环境的配置
  const currentEnvConfig = ENV_MAPPING[envConfig.envVersion] || ENV_MAPPING.develop;

  console.log(`API客户端配置 - 环境: ${envConfig.envVersion}, API: ${currentEnvConfig.baseUrl}, 模拟数据: ${currentEnvConfig.useMock}`);

  return currentEnvConfig;
};

// 获取当前配置
const currentConfig = getApiClientConfig();

// API配置
export const API_CONFIG = {
  // 基础URL
  baseUrl: currentConfig.baseUrl,

  // 是否使用模拟数据
  useMock: currentConfig.useMock,

  // 模拟数据基础URL
  mockBaseUrl: 'http://localhost:3010/mock-api/v1',

  // 请求超时时间（毫秒）
  timeout: 30000,

  // 跨域请求是否携带凭证
  withCredentials: true,

  // 默认缓存时间（毫秒）
  cacheTTL: 5 * 60 * 1000,

  // 最大重试次数
  maxRetries: 3,

  // 最大重试延迟（毫秒）
  maxRetryDelay: 30000,

  // 是否自动处理错误
  autoHandleError: true,

  // 是否自动转换数据格式
  autoTransform: true,

  // 是否启用缓存
  enableCache: true,

  // 是否使用本地存储缓存
  useLocalStorageCache: true
};
