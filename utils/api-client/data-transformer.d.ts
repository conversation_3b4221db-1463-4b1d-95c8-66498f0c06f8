/**
 * 数据转换器类型定义
 */

/**
 * 数据转换器类
 */
export class DataTransformer {
  /**
   * 构造函数
   */
  constructor();
  
  /**
   * 将对象的键从camelCase转换为snake_case
   * @param obj - 要转换的对象
   * @returns 转换后的对象
   */
  camelToSnake<T = any>(obj: any): T;
  
  /**
   * 将对象的键从snake_case转换为camelCase
   * @param obj - 要转换的对象
   * @returns 转换后的对象
   */
  snakeToCamel<T = any>(obj: any): T;
  
  /**
   * 转换单个键
   * @param key - 要转换的键
   * @param type - 转换类型，'camelToSnake'或'snakeToCamel'
   * @returns 转换后的键
   */
  transformKey(key: string, type: 'camelToSnake' | 'snakeToCamel'): string;
}
