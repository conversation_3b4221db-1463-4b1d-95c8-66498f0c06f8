/**
 * 数据转换器
 * 提供数据格式转换功能，如snake_case和camelCase之间的转换
 */

export class DataTransformer {
  constructor() {
    // 可以在这里添加配置选项
  }

  /**
   * 将snake_case转换为camelCase
   * @param {Object} data - 要转换的数据
   * @returns {Object} - 转换后的数据
   */
  snakeToCamel(data) {
    if (data === null || data === undefined || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.snakeToCamel(item));
    }

    return Object.keys(data).reduce((result, key) => {
      // 转换键名
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

      // 递归转换值
      result[camelKey] = this.snakeToCamel(data[key]);

      return result;
    }, {});
  }

  /**
   * 将camelCase转换为snake_case
   * @param {Object} data - 要转换的数据
   * @returns {Object} - 转换后的数据
   */
  camelToSnake(data) {
    if (data === null || data === undefined || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.camelToSnake(item));
    }

    return Object.keys(data).reduce((result, key) => {
      // 转换键名
      const snakeKey = key.replace(/([A-Z])/g, letter => `_${letter.toLowerCase()}`);

      // 递归转换值
      result[snakeKey] = this.camelToSnake(data[key]);

      return result;
    }, {});
  }

  /**
   * 验证数据是否符合指定的模式
   * @param {Object} data - 要验证的数据
   * @param {Object} schema - 数据模式
   * @returns {boolean} - 是否符合模式
   */
  validate(data, schema) {
    // 简单的数据验证实现
    if (!data || !schema) {
      return false;
    }

    for (const key in schema) {
      if (schema[key].required && (data[key] === undefined || data[key] === null)) {
        return false;
      }

      if (data[key] !== undefined && data[key] !== null) {
        // 类型检查
        if (schema[key].type && typeof data[key] !== schema[key].type) {
          return false;
        }

        // 数组检查
        if (schema[key].isArray && !Array.isArray(data[key])) {
          return false;
        }

        // 自定义验证函数
        if (schema[key].validator && typeof schema[key].validator === 'function') {
          if (!schema[key].validator(data[key])) {
            return false;
          }
        }
      }
    }

    return true;
  }

  /**
   * 规范化数据，确保数据符合指定的模式
   * @param {Object} data - 要规范化的数据
   * @param {Object} schema - 数据模式
   * @returns {Object} - 规范化后的数据
   */
  normalize(data, schema) {
    if (!data || !schema) {
      return data;
    }

    const result = { ...data };

    for (const key in schema) {
      // 设置默认值
      if (result[key] === undefined && schema[key].default !== undefined) {
        result[key] = schema[key].default;
      }

      // 类型转换
      if (result[key] !== undefined && result[key] !== null) {
        if (schema[key].type === 'number' && typeof result[key] !== 'number') {
          result[key] = Number(result[key]);
        } else if (schema[key].type === 'string' && typeof result[key] !== 'string') {
          result[key] = String(result[key]);
        } else if (schema[key].type === 'boolean' && typeof result[key] !== 'boolean') {
          result[key] = Boolean(result[key]);
        }
      }

      // 应用转换函数
      if (result[key] !== undefined && result[key] !== null && schema[key].transform) {
        result[key] = schema[key].transform(result[key]);
      }
    }

    return result;
  }
}
