/**
 * API客户端类型定义
 */

import { CacheManager } from './cache-manager';
import { DataTransformer } from './data-transformer';

/**
 * 请求方法类型
 */
export type RequestMethod = 'get' | 'post' | 'put' | 'delete' | 'patch';

/**
 * API配置接口
 */
export interface ApiConfig {
  /**
   * 基础URL
   */
  baseUrl: string;
  
  /**
   * 是否使用模拟数据
   */
  useMock?: boolean;
  
  /**
   * 模拟数据基础URL
   */
  mockBaseUrl?: string;
  
  /**
   * 请求超时时间（毫秒）
   */
  timeout?: number;
  
  /**
   * 跨域请求是否携带凭证
   */
  withCredentials?: boolean;
  
  /**
   * 默认缓存时间（毫秒）
   */
  cacheTTL?: number;
  
  /**
   * 最大重试次数
   */
  maxRetries?: number;
  
  /**
   * 最大重试延迟（毫秒）
   */
  maxRetryDelay?: number;
  
  /**
   * 是否自动处理错误
   */
  autoHandleError?: boolean;
  
  /**
   * 是否自动转换数据格式
   */
  autoTransform?: boolean;
  
  /**
   * 是否启用缓存
   */
  enableCache?: boolean;
  
  /**
   * 是否使用本地存储缓存
   */
  useLocalStorageCache?: boolean;
  
  /**
   * 请求头
   */
  headers?: Record<string, string>;
}

/**
 * 请求配置接口
 */
export interface RequestConfig extends ApiConfig {
  /**
   * 请求方法
   */
  method: RequestMethod;
  
  /**
   * 请求URL
   */
  url: string;
  
  /**
   * 请求参数
   */
  params?: Record<string, any>;
  
  /**
   * 请求数据
   */
  data?: any;
  
  /**
   * 是否使用缓存
   */
  useCache?: boolean;
  
  /**
   * 缓存时间（毫秒）
   */
  cacheTTL?: number;
  
  /**
   * 请求信号
   */
  signal?: AbortSignal;
}

/**
 * 响应接口
 */
export interface ApiResponse<T = any> {
  /**
   * 响应数据
   */
  data: T;
  
  /**
   * 响应消息
   */
  message?: string;
  
  /**
   * 元数据
   */
  meta?: {
    /**
     * 总数
     */
    total?: number;
    
    /**
     * 页码
     */
    page?: number;
    
    /**
     * 每页数量
     */
    pageSize?: number;
    
    /**
     * 其他元数据
     */
    [key: string]: any;
  };
  
  /**
   * 响应状态码
   */
  status?: number;
  
  /**
   * 响应状态文本
   */
  statusText?: string;
  
  /**
   * 响应头
   */
  headers?: Headers;
  
  /**
   * 请求配置
   */
  config?: RequestConfig;
  
  /**
   * 请求对象
   */
  request?: any;
}

/**
 * 请求拦截器类型
 */
export type RequestInterceptor = (config: RequestConfig) => Promise<RequestConfig> | RequestConfig;

/**
 * 响应拦截器类型
 */
export type ResponseInterceptor = (response: ApiResponse) => Promise<ApiResponse> | ApiResponse;

/**
 * 错误拦截器类型
 */
export type ErrorInterceptor = (error: any) => Promise<ApiResponse> | never;

/**
 * API客户端类
 */
export class ApiClient {
  /**
   * 配置
   */
  config: ApiConfig;
  
  /**
   * 请求拦截器
   */
  requestInterceptors: (RequestInterceptor | null)[];
  
  /**
   * 响应拦截器
   */
  responseInterceptors: (ResponseInterceptor | null)[];
  
  /**
   * 错误拦截器
   */
  errorInterceptors: (ErrorInterceptor | null)[];
  
  /**
   * 构造函数
   * @param config - API配置
   */
  constructor(config?: Partial<ApiConfig>);
  
  /**
   * 添加默认拦截器
   */
  addDefaultInterceptors(): void;
  
  /**
   * 添加请求拦截器
   * @param interceptor - 拦截器函数
   * @returns 拦截器ID
   */
  addRequestInterceptor(interceptor: RequestInterceptor): number;
  
  /**
   * 添加响应拦截器
   * @param interceptor - 拦截器函数
   * @returns 拦截器ID
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): number;
  
  /**
   * 添加错误拦截器
   * @param interceptor - 拦截器函数
   * @returns 拦截器ID
   */
  addErrorInterceptor(interceptor: ErrorInterceptor): number;
  
  /**
   * 移除请求拦截器
   * @param id - 拦截器ID
   */
  removeRequestInterceptor(id: number): void;
  
  /**
   * 移除响应拦截器
   * @param id - 拦截器ID
   */
  removeResponseInterceptor(id: number): void;
  
  /**
   * 移除错误拦截器
   * @param id - 拦截器ID
   */
  removeErrorInterceptor(id: number): void;
  
  /**
   * 发送请求
   * @param options - 请求选项
   * @returns 请求Promise
   */
  request<T = any>(options: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  
  /**
   * GET请求
   * @param url - 请求URL
   * @param params - 请求参数
   * @param config - 其他配置
   * @returns 请求Promise
   */
  get<T = any>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  
  /**
   * POST请求
   * @param url - 请求URL
   * @param data - 请求数据
   * @param config - 其他配置
   * @returns 请求Promise
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  
  /**
   * PUT请求
   * @param url - 请求URL
   * @param data - 请求数据
   * @param config - 其他配置
   * @returns 请求Promise
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  
  /**
   * DELETE请求
   * @param url - 请求URL
   * @param params - 请求参数
   * @param config - 其他配置
   * @returns 请求Promise
   */
  delete<T = any>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  
  /**
   * PATCH请求
   * @param url - 请求URL
   * @param data - 请求数据
   * @param config - 其他配置
   * @returns 请求Promise
   */
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  
  /**
   * 清除API缓存
   * @param urlPattern - URL模式，用于部分清除
   */
  clearCache(urlPattern?: string): void;
}

/**
 * 默认API客户端实例
 */
declare const apiClient: ApiClient;

export default apiClient;

/**
 * 数据转换器
 */
export declare const dataTransformer: DataTransformer;

/**
 * 缓存管理器
 */
export declare const cacheManager: CacheManager;
