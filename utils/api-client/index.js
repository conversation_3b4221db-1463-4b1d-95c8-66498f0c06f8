/**
 * 增强版API客户端
 * 提供统一的API请求处理、错误处理、数据转换、请求取消、超时控制和重试机制
 */

import { CacheManager } from './cache-manager';
import { DataTransformer } from './data-transformer';
import { RequestController } from './request-controller';
import { API_CONFIG } from './config';

// 导入认证拦截器
const { createAuthRequestInterceptor, createAuthResponseInterceptor } = require('./auth-interceptor');

// 创建缓存管理器实例
const cacheManager = new CacheManager();

// 创建数据转换器实例
const dataTransformer = new DataTransformer();

/**
 * API客户端
 */
class ApiClient {
  constructor(config = {}) {
    this.config = { ...API_CONFIG, ...config };
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    this.errorInterceptors = [];

    // 添加默认拦截器
    this.addDefaultInterceptors();
  }

  /**
   * 添加默认拦截器
   */
  addDefaultInterceptors() {
    // 添加认证请求拦截器
    this.addRequestInterceptor(createAuthRequestInterceptor());

    // 添加时间戳请求拦截器，避免缓存
    this.addRequestInterceptor(config => {
      if (config.method === 'get') {
        config.params = config.params || {};
        config.params._t = Date.now();
      }
      return config;
    });

    // 响应拦截器 - 处理响应数据
    this.addResponseInterceptor(response => {
      // 处理响应数据
      const { data } = response;

      // 检查API响应格式
      if (data && typeof data === 'object') {
        if (data.success === true) {
          // 成功响应
          return {
            data: data.data,
            message: data.message,
            meta: {
              total: data.total,
              page: data.page,
              pageSize: data.pageSize
            }
          };
        } else {
          // 业务错误
          const error = new Error(data.error?.message || '未知错误');
          error.code = data.error?.code || 'UNKNOWN_ERROR';
          error.response = response;
          throw error;
        }
      }

      // 非标准响应
      return { data };
    });

    // 添加认证错误拦截器（处理令牌刷新）
    this.addErrorInterceptor(createAuthResponseInterceptor(this));

    // 错误拦截器 - 处理HTTP错误
    this.addErrorInterceptor(error => {
      // 如果错误已经被处理，直接抛出
      if (error._handled) {
        throw error;
      }

      if (error.response) {
        // 服务器返回错误
        const { status, data } = error.response;

        // 处理特定状态码（除了401，已由认证拦截器处理）
        switch (status) {
          case 403:
            // 禁止访问
            console.error('禁止访问:', data);
            break;
          case 404:
            // 资源不存在
            console.error('资源不存在:', data);
            break;
          case 500:
            // 服务器错误
            console.error('服务器错误:', data);
            break;
          default:
            console.error(`HTTP错误 ${status}:`, data);
        }

        // 格式化错误信息
        const errorMessage = data?.error?.message || '服务器响应错误';
        const errorCode = data?.error?.code || `HTTP_ERROR_${status}`;

        const formattedError = new Error(errorMessage);
        formattedError.code = errorCode;
        formattedError.status = status;
        formattedError.response = error.response;
        formattedError._handled = true;

        throw formattedError;
      } else if (error.request) {
        // 请求发送但没有收到响应
        const networkError = new Error('网络错误，请检查您的网络连接');
        networkError.code = 'NETWORK_ERROR';
        networkError.request = error.request;
        networkError._handled = true;

        throw networkError;
      } else {
        // 请求配置错误
        const configError = new Error('请求配置错误');
        configError.code = 'REQUEST_CONFIG_ERROR';
        configError._handled = true;

        throw configError;
      }
    });
  }

  /**
   * 添加请求拦截器
   * @param {Function} interceptor - 拦截器函数
   * @returns {number} - 拦截器ID
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
    return this.requestInterceptors.length - 1;
  }

  /**
   * 添加响应拦截器
   * @param {Function} interceptor - 拦截器函数
   * @returns {number} - 拦截器ID
   */
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
    return this.responseInterceptors.length - 1;
  }

  /**
   * 添加错误拦截器
   * @param {Function} interceptor - 拦截器函数
   * @returns {number} - 拦截器ID
   */
  addErrorInterceptor(interceptor) {
    this.errorInterceptors.push(interceptor);
    return this.errorInterceptors.length - 1;
  }

  /**
   * 移除请求拦截器
   * @param {number} id - 拦截器ID
   */
  removeRequestInterceptor(id) {
    this.requestInterceptors[id] = null;
  }

  /**
   * 移除响应拦截器
   * @param {number} id - 拦截器ID
   */
  removeResponseInterceptor(id) {
    this.responseInterceptors[id] = null;
  }

  /**
   * 移除错误拦截器
   * @param {number} id - 拦截器ID
   */
  removeErrorInterceptor(id) {
    this.errorInterceptors[id] = null;
  }

  /**
   * 发送请求
   * @param {Object} options - 请求选项
   * @returns {Promise} - 请求Promise
   */
  async request(options) {
    // 创建请求控制器
    const controller = new RequestController(options);

    // 合并配置
    const config = {
      ...this.config,
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
        ...options.headers
      }
    };

    // 应用请求拦截器
    let requestConfig = { ...config };
    for (const interceptor of this.requestInterceptors) {
      if (interceptor) {
        requestConfig = await interceptor(requestConfig);
      }
    }

    // 构建完整URL
    const url = new URL(
      requestConfig.url.startsWith('http')
        ? requestConfig.url
        : `${this.config.baseUrl}${requestConfig.url}`
    );

    // 添加查询参数
    if (requestConfig.params) {
      Object.entries(requestConfig.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value);
        }
      });
    }

    // 检查缓存
    if (requestConfig.method === 'get' && requestConfig.useCache !== false) {
      const cacheKey = cacheManager.generateKey(url.toString(), requestConfig.data);
      const cachedData = cacheManager.get(cacheKey);

      if (cachedData) {
        return cachedData;
      }
    }

    // 准备请求选项
    const fetchOptions = {
      method: requestConfig.method,
      headers: requestConfig.headers,
      credentials: requestConfig.withCredentials ? 'include' : 'same-origin',
      mode: 'cors',
      signal: controller.signal // 添加AbortSignal用于取消请求
    };

    // 添加请求体
    if (requestConfig.data && ['post', 'put', 'patch'].includes(requestConfig.method.toLowerCase())) {
      // 转换请求数据为snake_case
      const snakeCaseData = dataTransformer.camelToSnake(requestConfig.data);
      fetchOptions.body = JSON.stringify(snakeCaseData);
    }

    // 设置超时
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, requestConfig.timeout || this.config.timeout);

    // 重试机制
    const maxRetries = requestConfig.maxRetries || this.config.maxRetries || 0;
    let retryCount = 0;
    let lastError = null;

    while (retryCount <= maxRetries) {
      try {
        // 发送请求
        const response = await fetch(url.toString(), fetchOptions);

        // 清除超时
        clearTimeout(timeoutId);

        // 解析响应
        const responseData = await response.json();

        // 构建响应对象
        const responseObject = {
          data: responseData,
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          config: requestConfig,
          request: null
        };

        // 应用响应拦截器
        let result = responseObject;
        for (const interceptor of this.responseInterceptors) {
          if (interceptor) {
            result = await interceptor(result);
          }
        }

        // 转换响应数据为camelCase
        if (result.data) {
          result.data = dataTransformer.snakeToCamel(result.data);
        }

        // 缓存结果
        if (requestConfig.method === 'get' && requestConfig.useCache !== false) {
          const cacheKey = cacheManager.generateKey(url.toString(), requestConfig.data);
          const ttl = requestConfig.cacheTTL || this.config.cacheTTL;
          cacheManager.set(cacheKey, result, ttl);
        }

        return result;
      } catch (error) {
        // 清除超时
        clearTimeout(timeoutId);

        // 保存最后一次错误
        lastError = error;

        // 判断是否需要重试
        const shouldRetry =
          retryCount < maxRetries &&
          (!error.status || error.status >= 500) &&
          error.code !== 'ABORT_ERROR';

        if (shouldRetry) {
          // 计算重试延迟（指数退避策略）
          const delay = Math.min(
            1000 * Math.pow(2, retryCount),
            this.config.maxRetryDelay || 30000
          );

          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, delay));

          retryCount++;
        } else {
          break;
        }
      }
    }

    // 应用错误拦截器
    for (const interceptor of this.errorInterceptors) {
      if (interceptor) {
        try {
          return await interceptor(lastError);
        } catch (e) {
          lastError = e;
        }
      }
    }

    throw lastError;
  }

  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  get(url, params = {}, config = {}) {
    return this.request({
      method: 'get',
      url,
      params,
      ...config
    });
  }

  /**
   * POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  post(url, data = {}, config = {}) {
    return this.request({
      method: 'post',
      url,
      data,
      ...config
    });
  }

  /**
   * PUT请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  put(url, data = {}, config = {}) {
    return this.request({
      method: 'put',
      url,
      data,
      ...config
    });
  }

  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  delete(url, params = {}, config = {}) {
    return this.request({
      method: 'delete',
      url,
      params,
      ...config
    });
  }

  /**
   * PATCH请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 其他配置
   * @returns {Promise} - 请求Promise
   */
  patch(url, data = {}, config = {}) {
    return this.request({
      method: 'patch',
      url,
      data,
      ...config
    });
  }

  /**
   * 清除API缓存
   * @param {string} urlPattern - URL模式，用于部分清除
   */
  clearCache(urlPattern) {
    cacheManager.clear(urlPattern);
  }
}

// 创建默认API客户端实例
const apiClient = new ApiClient();

export default apiClient;
export { ApiClient, dataTransformer, cacheManager };
