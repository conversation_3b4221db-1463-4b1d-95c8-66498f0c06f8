/**
 * 请求控制器类型定义
 */

import { RequestConfig } from './index';

/**
 * 请求控制器类
 */
export class RequestController {
  /**
   * 中止控制器
   */
  abortController: AbortController;
  
  /**
   * 中止信号
   */
  signal: AbortSignal;
  
  /**
   * 请求配置
   */
  config: RequestConfig;
  
  /**
   * 构造函数
   * @param config - 请求配置
   */
  constructor(config: RequestConfig);
  
  /**
   * 中止请求
   * @param reason - 中止原因
   */
  abort(reason?: string): void;
}
