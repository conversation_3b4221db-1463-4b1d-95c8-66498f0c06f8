/**
 * 请求控制器
 * 用于控制请求的取消、超时等功能
 */

export class RequestController {
  constructor(options = {}) {
    // 创建AbortController实例
    this.abortController = new AbortController();
    this.options = options;
    this.aborted = false;
  }

  /**
   * 获取AbortSignal
   * @returns {AbortSignal} - AbortSignal实例
   */
  get signal() {
    return this.abortController.signal;
  }

  /**
   * 取消请求
   * @param {string} reason - 取消原因
   */
  abort(reason = 'Request aborted') {
    if (!this.aborted) {
      this.aborted = true;
      this.abortController.abort(reason);
    }
  }

  /**
   * 创建超时处理
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {number} - 超时定时器ID
   */
  createTimeout(timeout) {
    return setTimeout(() => {
      this.abort('Request timeout');
    }, timeout);
  }
}
