/**
 * API客户端V2
 * 使用新版统计API端点
 */

// 导入基础API工具
const {
  requestWithRetry,
  bubbleAPI,
  squareAPI,
  learningPlanAPI,
  themeAPI,
  tagAPI,
  userAPI,
  authAPI,
  getBaseUrl
} = require('./api');

// 学习统计相关API V2
const statisticsAPIV2 = {
  // 获取学习统计数据
  getLearningStatistics: () => requestWithRetry('/statistics/learning', 'GET'),

  // 获取每日学习记录
  getDailyRecords: (params = {}) => {
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return requestWithRetry(`/statistics/daily${query ? '?' + query : ''}`, 'GET');
  },

  // 记录学习活动
  recordLearningActivity: data => requestWithRetry('/statistics/activities', 'POST', data),

  // 获取学习活动列表
  getActivities: (params = {}) => {
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return requestWithRetry(`/statistics/activities${query ? '?' + query : ''}`, 'GET');
  },

  // 获取学习概览
  getLearningOverview: () => requestWithRetry('/statistics/overview', 'GET'),

  // 获取学习趋势
  getLearningTrend: (days = 30) => requestWithRetry(`/statistics/trend?days=${days}`, 'GET')
};

// 导出API
module.exports = {
  bubbleAPI,
  squareAPI,
  statisticsAPIV2,
  learningPlanAPI,
  themeAPI,
  tagAPI,
  userAPI,
  authAPI,
  getBaseUrl
};
