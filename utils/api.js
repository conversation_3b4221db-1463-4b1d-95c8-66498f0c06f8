// API调用工具

// 基础URL
// 根据环境选择不同的API地址
const BASE_URL = wx.getAccountInfoSync().miniProgram.envVersion === 'release'
  ? 'https://api.aibubb.com/api/v1' // 生产环境
  : wx.getAccountInfoSync().miniProgram.envVersion === 'trial'
    ? 'https://api-test.aibubb.com/api/v1' // 体验版环境
    : 'http://localhost:9090/api/v1'; // 开发环境使用Docker容器API

// 移除备用API地址，确保所有请求都使用主API地址

// 获取基础URL
const getBaseUrl = () => {
  return BASE_URL;
};

// 获取存储的token
const getToken = () => {
  const token = wx.getStorageSync('token');
  const tokenExpiry = wx.getStorageSync('tokenExpiry');

  // 如果没有token或过期时间，返回null
  if (!token || !tokenExpiry) {
    return null;
  }

  // 检查token是否过期（提前5分钟视为过期，给刷新留出时间）
  const now = Date.now();
  const expiryTime = parseInt(tokenExpiry);
  const bufferTime = 5 * 60 * 1000; // 5分钟缓冲时间

  if (now > expiryTime - bufferTime) {
    console.log('Token已过期或即将过期，需要刷新');
    // 清除过期token
    wx.removeStorageSync('token');
    wx.removeStorageSync('tokenExpiry');
    return null;
  }

  return token;
};

// 缓存配置
const CACHE_CONFIG = {
  // 缓存过期时间（毫秒）
  DEFAULT_EXPIRY: 10 * 60 * 1000, // 10分钟 (从5分钟提高到10分钟)
  // 不同API的缓存配置
  ENDPOINTS: {
    '/auth/user': { expiry: 60 * 60 * 1000 }, // 用户信息缓存60分钟 (从30分钟提高到60分钟)
    '/themes': { expiry: 24 * 60 * 60 * 1000 }, // 主题缓存24小时
    '/learning-plans': { expiry: 20 * 60 * 1000 }, // 学习计划缓存20分钟 (从10分钟提高到20分钟)
    '/statistics/learning': { expiry: 60 * 60 * 1000 }, // 学习统计缓存60分钟 (从30分钟提高到60分钟)
    '/tags/current-plan/tags': { expiry: 60 * 60 * 1000 }, // 当前计划标签缓存60分钟 (从30分钟提高到60分钟)
    '/tags/system/default/tags': { expiry: 24 * 60 * 60 * 1000 }, // 系统默认标签缓存24小时 (新增)
    '/square/tags': { expiry: 24 * 60 * 60 * 1000 }, // 广场标签缓存24小时 (新增)
    '/users/me/stats': { expiry: 60 * 60 * 1000 } // 用户统计数据缓存60分钟 (从30分钟提高到60分钟)
  }
};

// 获取缓存
const getCache = (cacheKey) => {
  try {
    const cachedData = wx.getStorageSync(cacheKey);
    if (!cachedData) return null;

    const { data, expiry } = JSON.parse(cachedData);
    if (Date.now() > expiry) {
      // 缓存已过期
      wx.removeStorageSync(cacheKey);
      return null;
    }

    return data;
  } catch (err) {
    console.error('读取缓存失败:', err);
    return null;
  }
};

// 设置缓存
const setCache = (cacheKey, data, expiryMs) => {
  try {
    const cacheData = {
      data,
      expiry: Date.now() + expiryMs
    };
    wx.setStorageSync(cacheKey, JSON.stringify(cacheData));
  } catch (err) {
    console.error('设置缓存失败:', err);
  }
};

// 生成缓存键
const generateCacheKey = (url, method, data) => {
  // 对于GET请求，将URL和查询参数作为缓存键
  if (method === 'GET') {
    return `api_cache_${url}`;
  }

  // 对于其他请求，将URL、方法和请求数据作为缓存键
  return `api_cache_${method}_${url}_${JSON.stringify(data)}`;
};

// 判断是否为广场相关API
const isSquareRelatedAPI = (url) => {
  return url.includes('/square') ||
         url.includes('/tags/system/default/tags') ||
         url.includes('/tags/current-plan/tags');
};

// 基础请求函数
const request = (url, method, data = {}, needAuth = true, useCache = true, isPublicSquareRequest = false) => {
  return new Promise((resolve, reject) => {
    // 生成缓存键
    const cacheKey = generateCacheKey(url, method, data);

    // 对于GET请求，尝试从缓存获取数据
    if (method === 'GET' && useCache) {
      const cachedData = getCache(cacheKey);
      if (cachedData) {
        console.log(`从缓存获取数据: ${method} ${url}`);
        return resolve(cachedData);
      }
    }

    const header = {
      'Content-Type': 'application/json'
    };

    // 如果需要认证，添加token
    if (needAuth) {
      const token = getToken();
      if (token) {
        header['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn(`API请求需要认证但未找到token: ${method} ${url}`);
        // 临时解决方案：不强制跳转到登录页，而是返回错误
        return reject(new Error('未登录'));
      }
    }

    // 使用主API地址
    const apiBaseUrl = BASE_URL;
    console.log(`API请求: ${method} ${apiBaseUrl}${url}`);

    // 请求超时时间
    const timeout = 5000; // 5秒，进一步减少超时时间以提高响应速度

    // 发起请求
    const requestTask = wx.request({
      url: `${apiBaseUrl}${url}`,
      method,
      data,
      header: {
        ...header,
        'Accept-Charset': 'utf-8',
        'Content-Type': 'application/json; charset=utf-8'
      },
      timeout: timeout,
      encoding: 'utf-8', // 确保使用UTF-8编码
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`API请求成功: ${method} ${url}`, res.statusCode);

          // 缓存GET请求的结果
          if (method === 'GET' && useCache) {
            // 获取该API的缓存配置
            const endpointConfig = CACHE_CONFIG.ENDPOINTS[url.split('?')[0]] || {};
            const expiryMs = endpointConfig.expiry || CACHE_CONFIG.DEFAULT_EXPIRY;

            // 设置缓存
            setCache(cacheKey, res.data, expiryMs);
          }

          resolve(res.data);
        } else if (res.statusCode === 401) {
          console.error(`API认证失败: ${method} ${url}`, res.statusCode);
          // 认证失败，清除token和过期时间
          wx.removeStorageSync('token');
          wx.removeStorageSync('tokenExpiry');

          // 检查是否是广场相关API或公开广场请求
          const isSquareAPI = isSquareRelatedAPI(url) || isPublicSquareRequest;

          if (isSquareAPI) {
            // 广场相关API，不跳转到登录页，而是返回特定错误
            console.log('广场相关API认证失败，不跳转登录页');
            reject(new Error('需要登录但不强制跳转'));
          } else {
            // 非广场API，提示并跳转到登录页
            wx.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none',
              duration: 2000
            });

            // 获取当前页面路径
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const currentPath = currentPage ? currentPage.route : '';

            // 如果当前不在广场页面，才跳转到登录页
            if (!currentPath.includes('square')) {
              setTimeout(() => {
                wx.navigateTo({
                  url: '/pages/login/phone'
                });
              }, 1000);
            }

            reject(new Error('认证失败，请重新登录'));
          }
        } else {
          const errorMsg = res.data.error?.message || '请求失败';
          console.error(`API请求失败: ${method} ${url}`, res.statusCode, errorMsg);
          reject(new Error(errorMsg));
        }
      },
      fail: (err) => {
        console.error(`API请求网络错误: ${method} ${url}`, err);

        // 不使用备用API，直接返回错误
        // 移除备用API逻辑，确保API错误能够正确传递给上层处理

        // 提供更具体的错误信息
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            reject(new Error('网络请求超时，请检查网络连接'));
          } else if (err.errMsg.includes('fail')) {
            reject(new Error('网络连接失败，请检查网络设置'));
          } else {
            reject(new Error(`请求失败: ${err.errMsg}`));
          }
        } else {
          reject(err);
        }
      },
      complete: () => {
        // 可以在这里添加通用的完成处理
      }
    });

    // 设置请求超时处理
    setTimeout(() => {
      try {
        requestTask.abort(); // 尝试中断请求
      } catch (e) {
        console.error('中断请求失败:', e);
      }
    }, timeout + 1000); // 比请求超时多1秒，确保wx.request的超时先触发
  });
};

// 带重试的请求函数
const requestWithRetry = (url, method, data = {}, needAuth = true, useCache = true, maxRetries = 3, isPublicSquareRequest = false) => {
  return new Promise(async (resolve, reject) => {
    let retries = 0;

    while (retries <= maxRetries) {
      try {
        console.log(`尝试请求(${retries}/${maxRetries}): ${method} ${url}`);
        const result = await request(url, method, data, needAuth, useCache, isPublicSquareRequest);
        return resolve(result);
      } catch (err) {
        // 如果是参数错误，不重试
        if (err.message.includes('参数错误')) {
          return reject(err);
        }

        // 如果是认证错误，不在这里处理，让上层的401处理逻辑接管
        if (err.message === '未登录' || err.message.includes('认证失败')) {
          return reject(err);
        }

        // 最后一次重试失败，返回错误
        if (retries === maxRetries) {
          console.error(`请求失败，已达到最大重试次数(${maxRetries}): ${method} ${url}`, err);
          return reject(err);
        }

        // 重试前等待时间，使用指数退避策略
        const waitTime = Math.pow(2, retries) * 1000;
        console.log(`请求失败，${waitTime}ms后重试(${retries + 1}/${maxRetries}): ${method} ${url}`, err.message);

        // 等待一段时间后重试
        await new Promise(r => setTimeout(r, waitTime));

        retries++;
      }
    }
  });
};

// 泡泡互动相关API
const bubbleAPI = {
  // 记录泡泡互动
  recordInteraction: (data) => {
    return requestWithRetry('/bubble/interactions', 'POST', data);
  },

  // 获取泡泡内容
  getBubbleContent: (tagId) => {
    return requestWithRetry(`/bubble/content?tagId=${tagId}`, 'GET');
  },

  // 获取泡泡互动统计
  getInteractionStats: (params = {}) => {
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return requestWithRetry(`/bubble/interactions/stats${query ? '?' + query : ''}`, 'GET');
  }
};

// 广场相关API
const squareAPI = {
  // 获取广场笔记列表
  getNotes: (params = {}) => {
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');

    // 广场笔记不需要认证，使用optionalAuthJWT中间件
    const needAuth = false;

    // 使用专门的请求方法处理广场请求
    return request(`/square/notes${query ? '?' + query : ''}`, 'GET', {}, needAuth, true, true);
  },

  // 获取广场标签列表
  getTags: () => {
    // 广场标签不需要认证，使用optionalAuthJWT中间件
    return requestWithRetry('/square/tags', 'GET', {}, false);
  },

  // 获取推荐笔记
  getRecommendedNotes: (limit = 5) => {
    // 推荐笔记不需要认证，使用optionalAuthJWT中间件
    return requestWithRetry(`/square/recommended?limit=${limit}`, 'GET', {}, false);
  }
};

// 学习计划相关API
const learningPlanAPI = {
  // 获取学习计划列表
  getPlans: (params = {}) => {
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return requestWithRetry(`/learning-plans${query ? '?' + query : ''}`, 'GET');
  },

  // 获取学习计划详情
  getPlanById: (id, isSystemDefault = false) => {
    // 如果是系统默认计划，不需要认证
    return requestWithRetry(`/learning-plans/${id}`, 'GET', {}, !isSystemDefault);
  },

  // 创建学习计划
  createPlan: (data) => {
    return requestWithRetry('/learning-plans', 'POST', data);
  },

  // 更新学习计划
  updatePlan: (id, data) => {
    return requestWithRetry(`/learning-plans/${id}`, 'PUT', data);
  },

  // 删除学习计划
  deletePlan: (id) => {
    return requestWithRetry(`/learning-plans/${id}`, 'DELETE');
  },

  // 激活学习计划（设为当前计划）
  activatePlan: (id) => {
    return requestWithRetry(`/learning-plans/${id}/activate`, 'PUT');
  },

  // 获取系统默认学习计划（所有用户可见）
  getSystemDefaultPlan: () => {
    return requestWithRetry('/learning-plans/system/default', 'GET', {}, false);
  }
};



// 主题相关API
const themeAPI = {
  // 获取所有可用主题
  getThemes: () => {
    return requestWithRetry('/themes', 'GET');
  }
};

// 标签相关API
const tagAPI = {
  // 获取学习计划的标签
  getTagsByPlanId: (planId) => {
    return requestWithRetry(`/tags/learning-plans/${planId}/tags`, 'GET');
  },

  // 获取当前学习计划的标签
  getCurrentPlanTags: (forceRefresh = false) => {
    // 如果强制刷新，则不使用缓存
    return requestWithRetry('/tags/current-plan/tags', 'GET', {}, true, !forceRefresh);
  },

  // 获取系统默认学习计划的标签（所有用户可见）
  getSystemDefaultPlanTags: () => {
    return requestWithRetry('/tags/system/default/tags', 'GET', {}, false);
  }
};

// 学习统计相关API
const statisticsAPI = {
  // 获取学习统计数据
  getLearningStatistics: () => {
    return requestWithRetry('/statistics/learning', 'GET');
  },

  // 获取每日学习记录
  getDailyRecords: (params = {}) => {
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return requestWithRetry(`/statistics/daily${query ? '?' + query : ''}`, 'GET');
  },

  // 记录学习活动 - 暂时禁用，返回模拟成功响应
  recordActivity: (data) => {
    console.log('学习活动记录功能已禁用，返回模拟成功响应');
    // 返回模拟成功响应
    return Promise.resolve({
      success: true,
      data: {
        activityId: Date.now(),
        activityType: data.activityType,
        createdAt: new Date().toISOString()
      },
      message: '学习活动已记录'
    });
    // 原始实现
    // return requestWithRetry('/statistics/activities', 'POST', data);
  },

  // 获取学习活动列表
  getActivities: (params = {}) => {
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return requestWithRetry(`/statistics/activities${query ? '?' + query : ''}`, 'GET');
  }
};

// 用户相关API
const userAPI = {
  // 获取当前用户信息
  getUserInfo: () => {
    return requestWithRetry('/auth/user', 'GET');
  },

  // 更新用户信息
  updateUserInfo: (data) => {
    return requestWithRetry('/users/me', 'PUT', data);
  },

  // 获取用户学习统计
  getUserStats: () => {
    return requestWithRetry('/users/me/stats', 'GET');
  },

  // 绑定手机号
  bindPhone: (userId, phone, password) => {
    return requestWithRetry('/users/bind-phone', 'POST', {
      userId,
      phone,
      password
    });
  }
};

// 认证相关API
const authAPI = {
  // 微信登录
  login: (code, userInfo) => {
    const data = { code };
    if (userInfo) {
      data.userInfo = userInfo;
    }
    return requestWithRetry('/auth/login', 'POST', data, false);
  },

  // 手机号注册
  registerWithPhone: (phone, password, nickname) => {
    const data = { phone, password };
    if (nickname) {
      data.nickname = nickname;
    }
    return requestWithRetry('/auth/register/phone', 'POST', data, false);
  },

  // 手机号登录
  loginWithPhone: (phone, password) => {
    return requestWithRetry('/auth/login/phone', 'POST', { phone, password }, false);
  }
};

// 导出API
module.exports = {
  bubbleAPI,
  squareAPI,
  statisticsAPI,
  learningPlanAPI,

  themeAPI,
  tagAPI,
  userAPI,
  authAPI,
  getBaseUrl
};
