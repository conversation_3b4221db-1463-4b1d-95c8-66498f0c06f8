# NebulaLearn API客户端

本目录包含NebulaLearn项目的API客户端，用于前端与后端API的交互。

## 目录结构

```
utils/
  ├── api-client.js         # API客户端基础类
  └── api/                  # API模块
      ├── index.js          # API模块入口
      ├── theme-api.js      # 主题API
      ├── tag-api.js        # 标签API
      ├── learning-plan-api.js  # 学习计划API
      ├── exercise-api.js   # 练习API
      ├── insight-api.js    # 观点API
      ├── note-api.js       # 笔记API
      ├── daily-content-api.js  # 每日内容API
      ├── auth-api.js       # 认证API
      ├── user-api.js       # 用户API
      └── square-api.js     # 广场API
```

## 使用方法

### 导入API模块

```javascript
import api from 'utils/api';
```

### 调用API方法

```javascript
// 获取主题列表
api.theme.getThemes({ page: 1, pageSize: 10 })
  .then(result => {
    console.log('主题列表:', result.data);
    console.log('总数:', result.meta.total);
  })
  .catch(error => {
    console.error('获取主题列表失败:', error.message);
  });

// 获取学习计划详情
api.learningPlan.getLearningPlan(1)
  .then(result => {
    console.log('学习计划详情:', result.data);
  })
  .catch(error => {
    console.error('获取学习计划详情失败:', error.message);
  });

// 创建笔记
api.note.createNote({
  title: '我的学习笔记',
  content: '# 学习笔记\n\n这是我的学习笔记内容...',
  isPublic: true
})
  .then(result => {
    console.log('笔记创建成功:', result.data);
  })
  .catch(error => {
    console.error('创建笔记失败:', error.message);
  });
```

### 使用async/await

```javascript
async function fetchUserData() {
  try {
    // 获取当前用户信息
    const userResult = await api.user.getCurrentUser();
    console.log('当前用户:', userResult.data);
    
    // 获取用户的学习计划
    const plansResult = await api.learningPlan.getLearningPlans({
      userId: userResult.data.id,
      page: 1,
      pageSize: 10
    });
    console.log('用户学习计划:', plansResult.data);
    
    return {
      user: userResult.data,
      plans: plansResult.data
    };
  } catch (error) {
    console.error('获取用户数据失败:', error.message);
    throw error;
  }
}
```

## 特性

### 自动数据转换

API客户端会自动处理数据格式转换：

- 请求数据：前端使用camelCase，发送请求时自动转换为snake_case
- 响应数据：后端返回snake_case，接收响应时自动转换为camelCase

```javascript
// 前端代码使用camelCase
api.learningPlan.createLearningPlan({
  templateId: 1,
  title: '我的学习计划',
  isPublic: true
});

// 实际发送到服务器的数据是snake_case
// {
//   "template_id": 1,
//   "title": "我的学习计划",
//   "is_public": true
// }

// 服务器返回的数据是snake_case
// {
//   "id": 1,
//   "template_id": 1,
//   "title": "我的学习计划",
//   "is_public": true,
//   "created_at": "2023-05-06T10:30:00Z"
// }

// 前端收到的数据是camelCase
// {
//   "id": 1,
//   "templateId": 1,
//   "title": "我的学习计划",
//   "isPublic": true,
//   "createdAt": "2023-05-06T10:30:00Z"
// }
```

### 缓存机制

API客户端内置了缓存机制，可以减少重复请求：

```javascript
// 第一次请求，从服务器获取数据
api.theme.getThemes()
  .then(result => console.log('主题列表:', result.data));

// 短时间内再次请求，直接从缓存获取数据
api.theme.getThemes()
  .then(result => console.log('主题列表(缓存):', result.data));

// 清除特定实体的缓存
api.clearEntityCache('themes');

// 清除所有缓存
api.clearCache();
```

### 错误处理

API客户端统一处理错误，并提供友好的错误信息：

```javascript
api.note.getNote(999)
  .then(result => {
    console.log('笔记详情:', result.data);
  })
  .catch(error => {
    console.error('错误代码:', error.code);
    console.error('错误消息:', error.message);
    console.error('HTTP状态码:', error.status);
    
    // 根据错误代码处理不同情况
    switch (error.code) {
      case 'NOTE_NOT_FOUND':
        console.error('笔记不存在');
        break;
      case 'UNAUTHORIZED':
        console.error('未授权访问');
        break;
      default:
        console.error('未知错误');
    }
  });
```

## 配置

API客户端的配置在 `api-client.js` 文件中：

```javascript
const API_CONFIG = {
  baseUrl: 'http://localhost:3010/api/v1', // 开发环境使用模拟API服务
  // baseUrl: 'https://api.nebulalearn.com/api/v1', // 生产环境使用实际API
  timeout: 30000, // 请求超时时间（毫秒）
  withCredentials: true, // 跨域请求是否携带凭证
};
```

可以根据不同的环境修改配置。
