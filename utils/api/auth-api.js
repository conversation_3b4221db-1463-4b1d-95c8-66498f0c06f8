/**
 * 认证API
 */

/**
 * 创建认证API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 认证API对象
 */
const authApi = apiClient => ({
  /**
     * 登录
     * @param {Object} data - 登录数据
     * @param {string} data.code - 微信登录code
     * @param {Object} data.userInfo - 用户信息
     * @returns {Promise} - 请求Promise
     */
  login(data) {
    return apiClient.post('/auth/login', data);
  },

  /**
     * 登出
     * @returns {Promise} - 请求Promise
     */
  logout() {
    return apiClient.post('/auth/logout');
  },

  /**
     * 刷新令牌
     * @returns {Promise} - 请求Promise
     */
  refreshToken() {
    return apiClient.post('/auth/refresh-token');
  },

  /**
     * 获取当前用户信息
     * @returns {Promise} - 请求Promise
     */
  getCurrentUser() {
    return apiClient.get('/auth/me');
  },

  /**
     * 更新用户信息
     * @param {Object} data - 用户信息
     * @returns {Promise} - 请求Promise
     */
  updateUserInfo(data) {
    return apiClient.put('/auth/me', data);
  },

  /**
     * 绑定手机号
     * @param {Object} data - 绑定数据
     * @param {string} data.phone - 手机号
     * @param {string} data.code - 验证码
     * @returns {Promise} - 请求Promise
     */
  bindPhone(data) {
    return apiClient.post('/auth/bind-phone', data);
  },

  /**
     * 发送验证码
     * @param {Object} data - 发送数据
     * @param {string} data.phone - 手机号
     * @returns {Promise} - 请求Promise
     */
  sendVerificationCode(data) {
    return apiClient.post('/auth/send-verification-code', data);
  },

  /**
     * 验证验证码
     * @param {Object} data - 验证数据
     * @param {string} data.phone - 手机号
     * @param {string} data.code - 验证码
     * @returns {Promise} - 请求Promise
     */
  verifyCode(data) {
    return apiClient.post('/auth/verify-code', data);
  }
});

export default authApi;
