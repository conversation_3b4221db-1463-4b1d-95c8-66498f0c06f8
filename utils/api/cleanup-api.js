/**
 * 清理API
 * 处理软删除数据的清理操作
 */

/**
 * 创建清理API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 清理API对象
 */
const cleanupApi = apiClient => ({
  /**
     * 获取清理配置
     * @returns {Promise} - 请求Promise
     */
  getConfig() {
    return apiClient.get('/cleanup/config');
  },

  /**
     * 更新清理配置
     * @param {Object} config - 清理配置
     * @param {number} config.retentionDays - 保留已删除数据的天数
     * @param {number} config.batchSize - 每次批处理的记录数
     * @param {boolean} config.autoCleanupEnabled - 是否启用自动清理
     * @param {number} config.cleanupInterval - 自动清理的时间间隔（毫秒）
     * @returns {Promise} - 请求Promise
     */
  updateConfig(config) {
    return apiClient.put('/cleanup/config', config);
  },

  /**
     * 手动执行清理
     * @param {Object} params - 清理参数
     * @param {string} params.modelName - 要清理的模型名称，不指定则清理所有模型
     * @param {number} params.retentionDays - 保留已删除数据的天数，不指定则使用配置值
     * @returns {Promise} - 请求Promise
     */
  runCleanup(params = {}) {
    return apiClient.post('/cleanup/run', params);
  },

  /**
     * 永久删除指定类型的单个记录
     * @param {string} type - 记录类型，如'note', 'tag', 'theme'等
     * @param {number|string} id - 记录ID
     * @returns {Promise} - 请求Promise
     */
  permanentDelete(type, id) {
    // 使用cleanup服务的runCleanup方法，指定模型名称和ID
    const modelNameMap = {
      note: 'Note',
      insight: 'Insight',
      exercise: 'Exercise',
      tag: 'Tag',
      theme: 'Theme',
      dailyContent: 'DailyContent',
      learningPlan: 'LearningPlan'
    };

    const modelName = modelNameMap[type];
    if (!modelName) {
      return Promise.reject(new Error(`不支持的内容类型: ${type}`));
    }

    return apiClient.post('/cleanup/run', {
      modelName,
      specificIds: [id],
      forceDelete: true
    });
  },

  /**
     * 批量永久删除指定类型的记录
     * @param {string} type - 记录类型，如'note', 'tag', 'theme'等
     * @param {Array<number|string>} ids - 记录ID数组
     * @returns {Promise} - 请求Promise
     */
  batchPermanentDelete(type, ids) {
    // 使用cleanup服务的runCleanup方法，指定模型名称和ID数组
    const modelNameMap = {
      note: 'Note',
      insight: 'Insight',
      exercise: 'Exercise',
      tag: 'Tag',
      theme: 'Theme',
      dailyContent: 'DailyContent',
      learningPlan: 'LearningPlan'
    };

    const modelName = modelNameMap[type];
    if (!modelName) {
      return Promise.reject(new Error(`不支持的内容类型: ${type}`));
    }

    return apiClient.post('/cleanup/run', {
      modelName,
      specificIds: ids,
      forceDelete: true
    });
  },

  /**
     * 启动自动清理
     * @returns {Promise} - 请求Promise
     */
  startAutoCleanup() {
    return apiClient.post('/cleanup/start');
  },

  /**
     * 停止自动清理
     * @returns {Promise} - 请求Promise
     */
  stopAutoCleanup() {
    return apiClient.post('/cleanup/stop');
  }
});

export default cleanupApi;
