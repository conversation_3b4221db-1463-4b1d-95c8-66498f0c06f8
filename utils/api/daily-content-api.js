/**
 * 每日内容API
 */

/**
 * 创建每日内容API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 每日内容API对象
 */
const dailyContentApi = apiClient => ({
  /**
     * 获取学习计划的每日内容
     * @param {number|string} planId - 学习计划ID
     * @param {Object} params - 查询参数
     * @param {string} params.date - 日期，格式为YYYY-MM-DD
     * @returns {Promise} - 请求Promise
     */
  getDailyContent(planId, params = {}) {
    const apiParams = {
      date: params.date
    };

    return apiClient.get(`/learning-plans/${planId}/daily-content`, apiParams);
  },

  /**
     * 获取特定日期的每日内容
     * @param {number|string} planId - 学习计划ID
     * @param {string} date - 日期，格式为YYYY-MM-DD
     * @returns {Promise} - 请求Promise
     */
  getDailyContentByDate(planId, date) {
    return apiClient.get(`/learning-plans/${planId}/daily-content`, { date });
  },

  /**
     * 获取特定天数的每日内容
     * @param {number|string} planId - 学习计划ID
     * @param {number} day - 天数
     * @returns {Promise} - 请求Promise
     */
  getDailyContentByDay(planId, day) {
    return apiClient.get(`/learning-plans/${planId}/daily-content/day/${day}`);
  },

  /**
     * 标记每日内容完成
     * @param {number|string} id - 每日内容ID
     * @param {Object} data - 完成数据
     * @param {number} data.rating - 评分
     * @param {string} data.notes - 笔记
     * @returns {Promise} - 请求Promise
     */
  completeDailyContent(id, data = {}) {
    return apiClient.post(`/daily-contents/${id}/complete`, data);
  },

  /**
     * 取消标记每日内容完成
     * @param {number|string} id - 每日内容ID
     * @returns {Promise} - 请求Promise
     */
  uncompleteDailyContent(id) {
    return apiClient.delete(`/daily-contents/${id}/complete`);
  },

  /**
     * 获取用户的每日进度
     * @param {Object} params - 查询参数
     * @param {string} params.date - 日期，格式为YYYY-MM-DD
     * @returns {Promise} - 请求Promise
     */
  getUserDailyProgress(params = {}) {
    const apiParams = {
      date: params.date
    };

    return apiClient.get('/user/daily-progress', apiParams);
  },

  /**
     * 获取用户的连续学习天数
     * @returns {Promise} - 请求Promise
     */
  getUserStreak() {
    return apiClient.get('/user/streak');
  },

  /**
     * 获取用户的学习统计
     * @param {Object} params - 查询参数
     * @param {string} params.startDate - 开始日期，格式为YYYY-MM-DD
     * @param {string} params.endDate - 结束日期，格式为YYYY-MM-DD
     * @returns {Promise} - 请求Promise
     */
  getUserStats(params = {}) {
    const apiParams = {
      start_date: params.startDate,
      end_date: params.endDate
    };

    return apiClient.get('/user/stats', apiParams);
  }
});

export default dailyContentApi;
