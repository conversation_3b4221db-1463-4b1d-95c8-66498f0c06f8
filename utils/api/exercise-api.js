/**
 * 练习API
 */

/**
 * 创建练习API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 练习API对象
 */
const exerciseApi = apiClient => ({
  /**
     * 获取练习列表
     * @param {Object} params - 查询参数
     * @param {string} params.difficulty - 难度
     * @param {boolean} params.isVerified - 是否已验证
     * @param {boolean} params.isOfficial - 是否官方
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getExercises(params = {}) {
    // 转换参数名称
    const apiParams = {
      difficulty: params.difficulty,
      is_verified: params.isVerified,
      is_official: params.isOfficial,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/exercises', apiParams);
  },

  /**
     * 获取练习详情
     * @param {number|string} id - 练习ID
     * @returns {Promise} - 请求Promise
     */
  getExercise(id) {
    return apiClient.get(`/exercises/${id}`);
  },

  /**
     * 创建练习
     * @param {Object} data - 练习数据
     * @returns {Promise} - 请求Promise
     */
  createExercise(data) {
    return apiClient.post('/exercises', data);
  },

  /**
     * 更新练习
     * @param {number|string} id - 练习ID
     * @param {Object} data - 练习数据
     * @returns {Promise} - 请求Promise
     */
  updateExercise(id, data) {
    return apiClient.put(`/exercises/${id}`, data);
  },

  /**
     * 删除练习
     * @param {number|string} id - 练习ID
     * @returns {Promise} - 请求Promise
     */
  deleteExercise(id) {
    return apiClient.delete(`/exercises/${id}`);
  },

  /**
     * 恢复已删除的练习
     * @param {number|string} id - 练习ID
     * @returns {Promise} - 请求Promise
     */
  restoreExercise(id) {
    return apiClient.put(`/exercises/${id}/restore`);
  },

  /**
     * 标记练习完成
     * @param {number|string} id - 练习ID
     * @param {Object} data - 完成数据
     * @param {number} data.rating - 评分
     * @param {string} data.notes - 笔记
     * @returns {Promise} - 请求Promise
     */
  completeExercise(id, data = {}) {
    return apiClient.post(`/exercises/${id}/complete`, data);
  },

  /**
     * 获取练习标签
     * @param {number|string} id - 练习ID
     * @returns {Promise} - 请求Promise
     */
  getExerciseTags(id) {
    return apiClient.get(`/exercises/${id}/tags`);
  },

  /**
     * 添加练习标签
     * @param {number|string} id - 练习ID
     * @param {number|string} tagId - 标签ID
     * @returns {Promise} - 请求Promise
     */
  addExerciseTag(id, tagId) {
    return apiClient.post(`/exercises/${id}/tags`, { tagId });
  },

  /**
     * 删除练习标签
     * @param {number|string} id - 练习ID
     * @param {number|string} tagId - 标签ID
     * @returns {Promise} - 请求Promise
     */
  removeExerciseTag(id, tagId) {
    return apiClient.delete(`/exercises/${id}/tags/${tagId}`);
  }
});

export default exerciseApi;
