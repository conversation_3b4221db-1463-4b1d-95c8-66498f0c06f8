/**
 * 观点API
 */

/**
 * 创建观点API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 观点API对象
 */
const insightApi = apiClient => ({
  /**
     * 获取观点列表
     * @param {Object} params - 查询参数
     * @param {boolean} params.isVerified - 是否已验证
     * @param {boolean} params.isOfficial - 是否官方
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getInsights(params = {}) {
    // 转换参数名称
    const apiParams = {
      is_verified: params.isVerified,
      is_official: params.isOfficial,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/insights', apiParams);
  },

  /**
     * 获取观点详情
     * @param {number|string} id - 观点ID
     * @returns {Promise} - 请求Promise
     */
  getInsight(id) {
    return apiClient.get(`/insights/${id}`);
  },

  /**
     * 创建观点
     * @param {Object} data - 观点数据
     * @returns {Promise} - 请求Promise
     */
  createInsight(data) {
    return apiClient.post('/insights', data);
  },

  /**
     * 更新观点
     * @param {number|string} id - 观点ID
     * @param {Object} data - 观点数据
     * @returns {Promise} - 请求Promise
     */
  updateInsight(id, data) {
    return apiClient.put(`/insights/${id}`, data);
  },

  /**
     * 删除观点
     * @param {number|string} id - 观点ID
     * @returns {Promise} - 请求Promise
     */
  deleteInsight(id) {
    return apiClient.delete(`/insights/${id}`);
  },

  /**
     * 恢复已删除的观点
     * @param {number|string} id - 观点ID
     * @returns {Promise} - 请求Promise
     */
  restoreInsight(id) {
    return apiClient.put(`/insights/${id}/restore`);
  },

  /**
     * 标记观点已读
     * @param {number|string} id - 观点ID
     * @returns {Promise} - 请求Promise
     */
  readInsight(id) {
    return apiClient.post(`/insights/${id}/read`);
  },

  /**
     * 点赞观点
     * @param {number|string} id - 观点ID
     * @returns {Promise} - 请求Promise
     */
  likeInsight(id) {
    return apiClient.post(`/insights/${id}/like`);
  },

  /**
     * 取消点赞观点
     * @param {number|string} id - 观点ID
     * @returns {Promise} - 请求Promise
     */
  unlikeInsight(id) {
    return apiClient.delete(`/insights/${id}/like`);
  },

  /**
     * 获取观点标签
     * @param {number|string} id - 观点ID
     * @returns {Promise} - 请求Promise
     */
  getInsightTags(id) {
    return apiClient.get(`/insights/${id}/tags`);
  },

  /**
     * 添加观点标签
     * @param {number|string} id - 观点ID
     * @param {number|string} tagId - 标签ID
     * @returns {Promise} - 请求Promise
     */
  addInsightTag(id, tagId) {
    return apiClient.post(`/insights/${id}/tags`, { tagId });
  },

  /**
     * 删除观点标签
     * @param {number|string} id - 观点ID
     * @param {number|string} tagId - 标签ID
     * @returns {Promise} - 请求Promise
     */
  removeInsightTag(id, tagId) {
    return apiClient.delete(`/insights/${id}/tags/${tagId}`);
  }
});

export default insightApi;
