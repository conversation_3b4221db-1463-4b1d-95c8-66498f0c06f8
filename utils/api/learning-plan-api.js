/**
 * 学习计划API
 */

/**
 * 创建学习计划API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 学习计划API对象
 */
const learningPlanApi = apiClient => ({
  /**
     * 获取学习计划列表
     * @param {Object} params - 查询参数
     * @param {number} params.userId - 用户ID
     * @param {boolean} params.isCurrent - 是否当前计划
     * @param {string} params.status - 状态
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getLearningPlans(params = {}) {
    // 转换参数名称
    const apiParams = {
      user_id: params.userId,
      is_current: params.isCurrent,
      status: params.status,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/learning-plans', apiParams);
  },

  /**
     * 获取学习计划详情
     * @param {number|string} id - 学习计划ID
     * @returns {Promise} - 请求Promise
     */
  getLearningPlan(id) {
    return apiClient.get(`/learning-plans/${id}`);
  },

  /**
     * 创建学习计划
     * @param {Object} data - 学习计划数据
     * @returns {Promise} - 请求Promise
     */
  createLearningPlan(data) {
    return apiClient.post('/learning-plans', data);
  },

  /**
     * 更新学习计划
     * @param {number|string} id - 学习计划ID
     * @param {Object} data - 学习计划数据
     * @returns {Promise} - 请求Promise
     */
  updateLearningPlan(id, data) {
    return apiClient.put(`/learning-plans/${id}`, data);
  },

  /**
     * 删除学习计划
     * @param {number|string} id - 学习计划ID
     * @returns {Promise} - 请求Promise
     */
  deleteLearningPlan(id) {
    return apiClient.delete(`/learning-plans/${id}`);
  },

  /**
     * 恢复已删除的学习计划
     * @param {number|string} id - 学习计划ID
     * @returns {Promise} - 请求Promise
     */
  restoreLearningPlan(id) {
    return apiClient.put(`/learning-plans/${id}/restore`);
  },

  /**
     * 激活学习计划
     * @param {number|string} id - 学习计划ID
     * @returns {Promise} - 请求Promise
     */
  activateLearningPlan(id) {
    return apiClient.put(`/learning-plans/${id}/activate`);
  },

  /**
     * 获取系统默认学习计划
     * @returns {Promise} - 请求Promise
     */
  getSystemDefaultPlan() {
    return apiClient.get('/learning-plans/system/default');
  },

  /**
     * 获取学习模板列表
     * @param {Object} params - 查询参数
     * @param {number} params.themeId - 主题ID
     * @param {string} params.difficulty - 难度
     * @param {boolean} params.isOfficial - 是否官方
     * @param {boolean} params.isPublic - 是否公开
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getLearningTemplates(params = {}) {
    // 转换参数名称
    const apiParams = {
      theme_id: params.themeId,
      difficulty: params.difficulty,
      is_official: params.isOfficial,
      is_public: params.isPublic,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/learning-templates', apiParams);
  },

  /**
     * 获取学习模板详情
     * @param {number|string} id - 学习模板ID
     * @returns {Promise} - 请求Promise
     */
  getLearningTemplate(id) {
    return apiClient.get(`/learning-templates/${id}`);
  },

  /**
     * 创建学习模板
     * @param {Object} data - 学习模板数据
     * @returns {Promise} - 请求Promise
     */
  createLearningTemplate(data) {
    return apiClient.post('/learning-templates', data);
  },

  /**
     * 更新学习模板
     * @param {number|string} id - 学习模板ID
     * @param {Object} data - 学习模板数据
     * @returns {Promise} - 请求Promise
     */
  updateLearningTemplate(id, data) {
    return apiClient.put(`/learning-templates/${id}`, data);
  },

  /**
     * 删除学习模板
     * @param {number|string} id - 学习模板ID
     * @returns {Promise} - 请求Promise
     */
  deleteLearningTemplate(id) {
    return apiClient.delete(`/learning-templates/${id}`);
  },

  /**
     * 从学习模板创建学习计划
     * @param {number|string} templateId - 学习模板ID
     * @param {Object} data - 额外数据
     * @returns {Promise} - 请求Promise
     */
  createPlanFromTemplate(templateId, data = {}) {
    return apiClient.post(`/learning-templates/${templateId}/create-plan`, data);
  }
});

export default learningPlanApi;
