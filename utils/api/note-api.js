/**
 * 笔记API
 */

/**
 * 创建笔记API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 笔记API对象
 */
const noteApi = apiClient => ({
  /**
     * 获取笔记列表
     * @param {Object} params - 查询参数
     * @param {number} params.userId - 用户ID
     * @param {boolean} params.isPublic - 是否公开
     * @param {string} params.keyword - 搜索关键词
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getNotes(params = {}) {
    // 转换参数名称
    const apiParams = {
      user_id: params.userId,
      is_public: params.isPublic,
      keyword: params.keyword,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/notes', apiParams);
  },

  /**
     * 获取笔记详情
     * @param {number|string} id - 笔记ID
     * @returns {Promise} - 请求Promise
     */
  getNote(id) {
    return apiClient.get(`/notes/${id}`);
  },

  /**
     * 创建笔记
     * @param {Object} data - 笔记数据
     * @returns {Promise} - 请求Promise
     */
  createNote(data) {
    return apiClient.post('/notes', data);
  },

  /**
     * 更新笔记
     * @param {number|string} id - 笔记ID
     * @param {Object} data - 笔记数据
     * @returns {Promise} - 请求Promise
     */
  updateNote(id, data) {
    return apiClient.put(`/notes/${id}`, data);
  },

  /**
     * 删除笔记
     * @param {number|string} id - 笔记ID
     * @returns {Promise} - 请求Promise
     */
  deleteNote(id) {
    return apiClient.delete(`/notes/${id}`);
  },

  /**
     * 软删除笔记
     * @param {number|string} id - 笔记ID
     * @returns {Promise} - 请求Promise
     */
  softDeleteNote(id) {
    return apiClient.delete(`/notes/${id}/soft-delete`);
  },

  /**
     * 恢复已删除的笔记
     * @param {number|string} id - 笔记ID
     * @returns {Promise} - 请求Promise
     */
  restoreNote(id) {
    return apiClient.put(`/notes/${id}/restore`);
  },

  /**
     * 点赞笔记
     * @param {number|string} id - 笔记ID
     * @returns {Promise} - 请求Promise
     */
  likeNote(id) {
    return apiClient.post(`/notes/${id}/like`);
  },

  /**
     * 取消点赞笔记
     * @param {number|string} id - 笔记ID
     * @returns {Promise} - 请求Promise
     */
  unlikeNote(id) {
    return apiClient.delete(`/notes/${id}/like`);
  },

  /**
     * 获取笔记评论
     * @param {number|string} id - 笔记ID
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getNoteComments(id, params = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get(`/notes/${id}/comments`, apiParams);
  },

  /**
     * 添加笔记评论
     * @param {number|string} id - 笔记ID
     * @param {Object} data - 评论数据
     * @param {string} data.content - 评论内容
     * @returns {Promise} - 请求Promise
     */
  addNoteComment(id, data) {
    return apiClient.post(`/notes/${id}/comments`, data);
  },

  /**
     * 删除笔记评论
     * @param {number|string} id - 笔记ID
     * @param {number|string} commentId - 评论ID
     * @returns {Promise} - 请求Promise
     */
  deleteNoteComment(id, commentId) {
    return apiClient.delete(`/notes/${id}/comments/${commentId}`);
  },

  /**
     * 获取笔记标签
     * @param {number|string} id - 笔记ID
     * @returns {Promise} - 请求Promise
     */
  getNoteTags(id) {
    return apiClient.get(`/notes/${id}/tags`);
  },

  /**
     * 添加笔记标签
     * @param {number|string} id - 笔记ID
     * @param {number|string} tagId - 标签ID
     * @returns {Promise} - 请求Promise
     */
  addNoteTag(id, tagId) {
    return apiClient.post(`/notes/${id}/tags`, { tagId });
  },

  /**
     * 删除笔记标签
     * @param {number|string} id - 笔记ID
     * @param {number|string} tagId - 标签ID
     * @returns {Promise} - 请求Promise
     */
  removeNoteTag(id, tagId) {
    return apiClient.delete(`/notes/${id}/tags/${tagId}`);
  },

  /**
     * 获取已删除的笔记列表
     * @param {Object} params - 查询参数
     * @param {string} params.userId - 用户ID
     * @param {string} params.keyword - 搜索关键词
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getDeletedNotes(params = {}) {
    const apiParams = {
      user_id: params.userId,
      keyword: params.keyword,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/notes/deleted', apiParams);
  },

  /**
     * 批量软删除笔记
     * @param {Array<number|string>} ids - 笔记ID数组
     * @returns {Promise} - 请求Promise
     */
  batchSoftDeleteNotes(ids) {
    return apiClient.post('/batch/notes/soft-delete', { ids });
  },

  /**
     * 批量恢复笔记
     * @param {Array<number|string>} ids - 笔记ID数组
     * @returns {Promise} - 请求Promise
     */
  batchRestoreNotes(ids) {
    return apiClient.post('/batch/notes/restore', { ids });
  },

  /**
     * 清空回收站
     * @returns {Promise} - 请求Promise
     */
  emptyTrash() {
    return apiClient.delete('/notes/trash');
  }
});

export default noteApi;
