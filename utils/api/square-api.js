/**
 * 广场API
 */

/**
 * 创建广场API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 广场API对象
 */
const squareApi = apiClient => ({
  /**
     * 获取广场笔记列表
     * @param {Object} params - 查询参数
     * @param {number} params.tagId - 标签ID
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getNotes(params = {}) {
    const apiParams = {
      tag_id: params.tagId,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/square/notes', apiParams);
  },

  /**
     * 获取热门标签
     * @returns {Promise} - 请求Promise
     */
  getTags() {
    return apiClient.get('/square/tags');
  },

  /**
     * 获取推荐用户
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @returns {Promise} - 请求Promise
     */
  getRecommendedUsers(params = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/recommended-users', apiParams);
  },

  /**
     * 获取热门学习计划
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @returns {Promise} - 请求Promise
     */
  getHotLearningPlans(params = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/hot-learning-plans', apiParams);
  },

  /**
     * 获取热门练习
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @returns {Promise} - 请求Promise
     */
  getHotExercises(params = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/hot-exercises', apiParams);
  },

  /**
     * 获取热门观点
     * @param {Object} params - 查询参数
     * @param {number} params.limit - 限制数量
     * @returns {Promise} - 请求Promise
     */
  getHotInsights(params = {}) {
    const apiParams = {
      limit: params.limit
    };

    return apiClient.get('/square/hot-insights', apiParams);
  },

  /**
     * 搜索广场内容
     * @param {Object} params - 查询参数
     * @param {string} params.keyword - 关键词
     * @param {string} params.type - 内容类型，可选值：note, learning_plan, exercise, insight
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  search(params = {}) {
    const apiParams = {
      keyword: params.keyword,
      type: params.type,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/square/search', apiParams);
  }
});

export default squareApi;
