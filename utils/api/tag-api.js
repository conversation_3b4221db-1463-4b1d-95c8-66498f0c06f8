/**
 * 标签API
 */

/**
 * 创建标签API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 标签API对象
 */
const tagApi = apiClient => ({
  /**
     * 获取标签列表
     * @param {Object} params - 查询参数
     * @param {number} params.categoryId - 分类ID
     * @param {boolean} params.isVerified - 是否已验证
     * @param {boolean} params.isOfficial - 是否官方
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getTags(params = {}) {
    // 转换参数名称
    const apiParams = {
      category_id: params.categoryId,
      is_verified: params.isVerified,
      is_official: params.isOfficial,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/tags', apiParams);
  },

  /**
     * 获取标签详情
     * @param {number|string} id - 标签ID
     * @returns {Promise} - 请求Promise
     */
  getTag(id) {
    return apiClient.get(`/tags/${id}`);
  },

  /**
     * 创建标签
     * @param {Object} data - 标签数据
     * @returns {Promise} - 请求Promise
     */
  createTag(data) {
    return apiClient.post('/tags', data);
  },

  /**
     * 更新标签
     * @param {number|string} id - 标签ID
     * @param {Object} data - 标签数据
     * @returns {Promise} - 请求Promise
     */
  updateTag(id, data) {
    return apiClient.put(`/tags/${id}`, data);
  },

  /**
     * 删除标签
     * @param {number|string} id - 标签ID
     * @returns {Promise} - 请求Promise
     */
  deleteTag(id) {
    return apiClient.delete(`/tags/${id}`);
  },

  /**
     * 恢复已删除的标签
     * @param {number|string} id - 标签ID
     * @returns {Promise} - 请求Promise
     */
  restoreTag(id) {
    return apiClient.put(`/tags/${id}/restore`);
  },

  /**
     * 获取标签分类列表
     * @returns {Promise} - 请求Promise
     */
  getTagCategories() {
    return apiClient.get('/tag-categories');
  },

  /**
     * 获取标签分类详情
     * @param {number|string} id - 标签分类ID
     * @returns {Promise} - 请求Promise
     */
  getTagCategory(id) {
    return apiClient.get(`/tag-categories/${id}`);
  },

  /**
     * 创建标签分类
     * @param {Object} data - 标签分类数据
     * @returns {Promise} - 请求Promise
     */
  createTagCategory(data) {
    return apiClient.post('/tag-categories', data);
  },

  /**
     * 更新标签分类
     * @param {number|string} id - 标签分类ID
     * @param {Object} data - 标签分类数据
     * @returns {Promise} - 请求Promise
     */
  updateTagCategory(id, data) {
    return apiClient.put(`/tag-categories/${id}`, data);
  },

  /**
     * 删除标签分类
     * @param {number|string} id - 标签分类ID
     * @returns {Promise} - 请求Promise
     */
  deleteTagCategory(id) {
    return apiClient.delete(`/tag-categories/${id}`);
  }
});

export default tagApi;
