/**
 * 主题API
 */

/**
 * 创建主题API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 主题API对象
 */
const themeApi = apiClient => ({
  /**
     * 获取主题列表
     * @param {Object} params - 查询参数
     * @param {boolean} params.isActive - 是否激活
     * @param {string} params.sortBy - 排序字段
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getThemes(params = {}) {
    // 转换参数名称
    const apiParams = {
      is_active: params.isActive,
      sort_by: params.sortBy,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/themes', apiParams);
  },

  /**
     * 获取主题详情
     * @param {number|string} id - 主题ID
     * @returns {Promise} - 请求Promise
     */
  getTheme(id) {
    return apiClient.get(`/themes/${id}`);
  },

  /**
     * 创建主题
     * @param {Object} data - 主题数据
     * @returns {Promise} - 请求Promise
     */
  createTheme(data) {
    return apiClient.post('/themes', data);
  },

  /**
     * 更新主题
     * @param {number|string} id - 主题ID
     * @param {Object} data - 主题数据
     * @returns {Promise} - 请求Promise
     */
  updateTheme(id, data) {
    return apiClient.put(`/themes/${id}`, data);
  },

  /**
     * 删除主题
     * @param {number|string} id - 主题ID
     * @returns {Promise} - 请求Promise
     */
  deleteTheme(id) {
    return apiClient.delete(`/themes/${id}`);
  },

  /**
     * 恢复已删除的主题
     * @param {number|string} id - 主题ID
     * @returns {Promise} - 请求Promise
     */
  restoreTheme(id) {
    return apiClient.put(`/themes/${id}/restore`);
  }
});

export default themeApi;
