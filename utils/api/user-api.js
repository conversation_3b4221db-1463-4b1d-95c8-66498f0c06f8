/**
 * 用户API
 */

/**
 * 创建用户API
 * @param {Object} apiClient - API客户端实例
 * @returns {Object} - 用户API对象
 */
const userApi = apiClient => ({
  /**
     * 获取当前用户信息
     * @returns {Promise} - 请求Promise
     */
  getCurrentUser() {
    return apiClient.get('/users/me');
  },

  /**
     * 更新当前用户信息
     * @param {Object} data - 用户信息
     * @returns {Promise} - 请求Promise
     */
  updateCurrentUser(data) {
    return apiClient.put('/users/me', data);
  },

  /**
     * 获取用户设置
     * @returns {Promise} - 请求Promise
     */
  getUserSettings() {
    return apiClient.get('/users/me/settings');
  },

  /**
     * 更新用户设置
     * @param {Object} data - 设置数据
     * @returns {Promise} - 请求Promise
     */
  updateUserSettings(data) {
    return apiClient.put('/users/me/settings', data);
  },

  /**
     * 获取用户通知设置
     * @returns {Promise} - 请求Promise
     */
  getNotificationSettings() {
    return apiClient.get('/users/me/notification-settings');
  },

  /**
     * 更新用户通知设置
     * @param {Object} data - 通知设置数据
     * @returns {Promise} - 请求Promise
     */
  updateNotificationSettings(data) {
    return apiClient.put('/users/me/notification-settings', data);
  },

  /**
     * 获取用户通知列表
     * @param {Object} params - 查询参数
     * @param {boolean} params.isRead - 是否已读
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getNotifications(params = {}) {
    const apiParams = {
      is_read: params.isRead,
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/users/me/notifications', apiParams);
  },

  /**
     * 标记通知为已读
     * @param {number|string} id - 通知ID
     * @returns {Promise} - 请求Promise
     */
  markNotificationAsRead(id) {
    return apiClient.put(`/users/me/notifications/${id}/read`);
  },

  /**
     * 标记所有通知为已读
     * @returns {Promise} - 请求Promise
     */
  markAllNotificationsAsRead() {
    return apiClient.put('/users/me/notifications/read-all');
  },

  /**
     * 获取用户成就列表
     * @returns {Promise} - 请求Promise
     */
  getUserAchievements() {
    return apiClient.get('/users/me/achievements');
  },

  /**
     * 获取用户徽章列表
     * @returns {Promise} - 请求Promise
     */
  getUserBadges() {
    return apiClient.get('/users/me/badges');
  },

  /**
     * 获取用户等级信息
     * @returns {Promise} - 请求Promise
     */
  getUserLevel() {
    return apiClient.get('/users/me/level');
  },

  /**
     * 获取用户积分历史
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise} - 请求Promise
     */
  getPointsHistory(params = {}) {
    const apiParams = {
      page: params.page,
      pageSize: params.pageSize
    };

    return apiClient.get('/users/me/points-history', apiParams);
  }
});

export default userApi;
