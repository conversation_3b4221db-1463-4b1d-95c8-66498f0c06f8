/**
 * 认证服务
 * 提供统一的登录、登出和用户信息管理功能
 */

// 导入API客户端
const api = require('./api-client/index').default;
// 导入令牌管理器
const tokenManager = require('./token-manager');

/**
 * 认证服务
 */
class AuthService {
  /**
   * 微信登录
   * @param {Object} options - 登录选项
   * @param {boolean} options.getUserInfo - 是否获取用户信息
   * @returns {Promise<Object>} 登录结果
   */
  async loginWithWechat(options = {}) {
    try {
      // 获取微信登录凭证
      const loginResult = await this._getWechatCode();

      if (!loginResult.code) {
        throw new Error('获取微信登录凭证失败');
      }

      // 准备登录数据
      const loginData = { code: loginResult.code };

      // 如果需要获取用户信息，添加到登录数据
      if (options.getUserInfo) {
        const userInfoResult = await this._getWechatUserInfo();
        if (userInfoResult) {
          loginData.userInfo = userInfoResult.userInfo;
        }
      }

      // 调用登录API
      const response = await api.auth.login(loginData);

      // 保存令牌信息
      tokenManager.saveTokens({
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        expiresIn: response.data.expiresIn,
        userId: response.data.userId
      });

      // 获取用户信息
      const userInfo = await this.getCurrentUser();

      return {
        success: true,
        isNewUser: response.data.isNewUser,
        userInfo
      };
    } catch (error) {
      console.error('微信登录失败:', error);
      return {
        success: false,
        error: error.message || '登录失败，请稍后再试'
      };
    }
  }

  /**
   * 手机号登录
   * @param {string} phone - 手机号
   * @param {string} password - 密码
   * @returns {Promise<Object>} 登录结果
   */
  async loginWithPhone(phone, password) {
    try {
      if (!phone || !password) {
        throw new Error('手机号和密码不能为空');
      }

      // 调用登录API
      const response = await api.auth.loginWithPhone({ phone, password });

      // 保存令牌信息
      tokenManager.saveTokens({
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        expiresIn: response.data.expiresIn,
        userId: response.data.userId
      });

      // 获取用户信息
      const userInfo = await this.getCurrentUser();

      return {
        success: true,
        userInfo
      };
    } catch (error) {
      console.error('手机号登录失败:', error);
      return {
        success: false,
        error: error.message || '登录失败，请稍后再试'
      };
    }
  }

  /**
   * 手机号注册
   * @param {string} phone - 手机号
   * @param {string} password - 密码
   * @param {string} confirmPassword - 确认密码
   * @param {string} nickname - 昵称（可选）
   * @returns {Promise<Object>} 注册结果
   */
  async registerWithPhone(phone, password, confirmPassword, nickname = '') {
    try {
      // 验证参数
      if (!phone || !password || !confirmPassword) {
        throw new Error('手机号和密码不能为空');
      }

      if (password !== confirmPassword) {
        throw new Error('两次输入的密码不一致');
      }

      // 调用注册API
      const response = await api.auth.registerWithPhone({
        phone,
        password,
        nickname: nickname || undefined
      });

      // 保存令牌信息
      tokenManager.saveTokens({
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        expiresIn: response.data.expiresIn,
        userId: response.data.userId
      });

      // 获取用户信息
      const userInfo = await this.getCurrentUser();

      return {
        success: true,
        userInfo
      };
    } catch (error) {
      console.error('手机号注册失败:', error);
      return {
        success: false,
        error: error.message || '注册失败，请稍后再试'
      };
    }
  }

  /**
   * 登出
   * @returns {Promise<boolean>} 是否成功
   */
  async logout() {
    try {
      // 调用登出API
      await api.auth.logout();

      // 清除令牌信息
      tokenManager.clearTokens();

      // 清除用户信息
      wx.removeStorageSync('userInfo');

      return true;
    } catch (error) {
      console.error('登出失败:', error);

      // 即使API调用失败，也清除本地令牌
      tokenManager.clearTokens();
      wx.removeStorageSync('userInfo');

      return false;
    }
  }

  /**
   * 获取当前用户信息
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Object|null>} 用户信息
   */
  async getCurrentUser(forceRefresh = false) {
    try {
      // 检查是否已登录
      const isLoggedIn = await tokenManager.isLoggedIn();

      if (!isLoggedIn) {
        return null;
      }

      // 如果不强制刷新，尝试从本地存储获取
      if (!forceRefresh) {
        const cachedUserInfo = wx.getStorageSync('userInfo');
        if (cachedUserInfo) {
          return cachedUserInfo;
        }
      }

      // 调用API获取用户信息
      const response = await api.auth.getCurrentUser();

      if (response.data) {
        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', response.data);
        return response.data;
      }

      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 更新用户信息
   * @param {Object} userInfo - 用户信息
   * @returns {Promise<Object>} 更新结果
   */
  async updateUserInfo(userInfo) {
    try {
      // 调用API更新用户信息
      const response = await api.auth.updateUserInfo(userInfo);

      if (response.data) {
        // 更新本地存储的用户信息
        const currentUserInfo = wx.getStorageSync('userInfo') || {};
        const updatedUserInfo = { ...currentUserInfo, ...response.data };
        wx.setStorageSync('userInfo', updatedUserInfo);

        return {
          success: true,
          userInfo: updatedUserInfo
        };
      }

      return {
        success: false,
        error: '更新用户信息失败'
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return {
        success: false,
        error: error.message || '更新用户信息失败，请稍后再试'
      };
    }
  }

  /**
   * 检查是否已登录
   * @returns {Promise<boolean>} 是否已登录
   */
  async isLoggedIn() {
    return tokenManager.isLoggedIn();
  }

  /**
   * 获取微信登录凭证
   * @returns {Promise<Object>} 登录凭证
   * @private
   */
  _getWechatCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: res => resolve(res),
        fail: err => reject(err)
      });
    });
  }

  /**
   * 获取微信用户信息
   * @returns {Promise<Object>} 用户信息
   * @private
   */
  _getWechatUserInfo() {
    return new Promise(resolve => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: res => resolve(res),
        fail: () => resolve(null) // 用户拒绝授权不算失败
      });
    });
  }
}

// 导出单例实例
module.exports = new AuthService();
