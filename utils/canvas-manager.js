// utils/canvas-manager.js
// 画布管理器

// 导入事件总线
const eventBus = require('./event-bus');

/**
 * 画布管理器类
 * 负责创建和管理不同类型的画布组件
 */
class CanvasManager {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   * @param {Object} options.page - 页面实例
   * @param {Object} options.themeManager - 主题数据管理实例
   */
  constructor(options = {}) {
    // 画布实例
    this.canvasInstances = {
      bubble: null,
      star: null
    };

    // 当前活动的画布类型
    this.currentType = null;

    // 引用
    this.page = options.page;
    this.themeManager = options.themeManager;
    this.eventBus = eventBus;

    // 状态
    this.isInitializing = false;
    this.retryAttempts = 0;
    this.maxRetryAttempts = 3;
  }

  /**
   * 创建画布组件
   * @param {string} type - 画布类型（bubble 或 star）
   * @param {Object} options - 初始化选项
   * @param {Function} options.onProgress - 加载进度回调函数，参数为0-100的进度值
   * @returns {Promise<Object>} 画布组件实例
   */
  createCanvas(type, options = {}) {
    return new Promise((resolve, reject) => {
      if (this.isInitializing) {
        reject(new Error('正在初始化另一个画布，请稍后再试'));
        return;
      }

      this.isInitializing = true;

      // 进度回调函数
      const onProgress = options.onProgress || (() => {});

      // 报告初始进度
      onProgress(5);

      // 检查类型是否有效
      if (type !== 'bubble' && type !== 'star') {
        this.isInitializing = false;
        reject(new Error(`无效的画布类型: ${type}`));
        return;
      }

      // 如果已经存在该类型的画布实例，直接返回
      if (this.canvasInstances[type]) {
        this.currentType = type;
        this.isInitializing = false;
        onProgress(100);
        resolve(this.canvasInstances[type]);
        return;
      }

      // 报告组件加载进度
      onProgress(10);

      // 加载对应的画布组件
      this._loadCanvasComponent(type)
        .then(CanvasComponent => {
          // 报告组件创建进度
          onProgress(30);

          // 创建画布实例
          const canvas = new CanvasComponent();

          // 初始化画布
          const initOptions = {
            page: this.page,
            canvasId: options.canvasId || `${type}-canvas`,
            getThemes: () => this.themeManager.getCurrentThemes(),
            config: options.config || {}
          };

          // 报告初始化开始进度
          onProgress(50);

          return canvas.init(initOptions)
            .then(success => {
              if (!success) {
                throw new Error(`初始化${type}画布失败`);
              }

              // 报告初始化完成进度
              onProgress(80);

              // 保存画布实例
              this.canvasInstances[type] = canvas;
              this.currentType = type;

              // 重置重试计数
              this.retryAttempts = 0;

              return canvas;
            });
        })
        .then(canvas => {
          this.isInitializing = false;

          // 报告完成进度
          onProgress(100);

          // 触发画布创建完成事件
          this.eventBus.emit('canvas:created', { type, canvas });

          resolve(canvas);
        })
        .catch(err => {
          console.error(`创建${type}画布失败:`, err);

          // 增加重试计数
          this.retryAttempts++;

          // 如果重试次数未超过最大值，尝试重试
          if (this.retryAttempts < this.maxRetryAttempts) {
            console.log(`尝试重新创建${type}画布 (${this.retryAttempts}/${this.maxRetryAttempts})`);

            // 报告重试进度
            onProgress(15 * this.retryAttempts);

            // 延迟后重试
            setTimeout(() => {
              this.isInitializing = false;
              this.createCanvas(type, options)
                .then(resolve)
                .catch(reject);
            }, 1000);
          } else {
            this.isInitializing = false;

            // 触发画布创建失败事件
            this.eventBus.emit('canvas:error', { type, error: err });

            reject(err);
          }
        });
    });
  }

  /**
   * 加载画布组件
   * @param {string} type - 画布类型（bubble 或 star）
   * @returns {Promise<Object>} 画布组件类
   * @private
   */
  _loadCanvasComponent(type) {
    return new Promise((resolve, reject) => {
      try {
        let CanvasComponent;

        if (type === 'bubble') {
          try {
            // 尝试使用不同的路径加载泡泡画布组件
            try {
              CanvasComponent = require('../components/bubble-canvas/index');
              console.log('成功加载泡泡画布组件: ../components/bubble-canvas/index');
            } catch (e1) {
              try {
                CanvasComponent = require('./components/bubble-canvas/index');
                console.log('成功加载泡泡画布组件: ./components/bubble-canvas/index');
              } catch (e2) {
                try {
                  CanvasComponent = require('components/bubble-canvas/index');
                  console.log('成功加载泡泡画布组件: components/bubble-canvas/index');
                } catch (e3) {
                  try {
                    CanvasComponent = require('/components/bubble-canvas/index');
                    console.log('成功加载泡泡画布组件: /components/bubble-canvas/index');
                  } catch (e4) {
                    throw new Error('无法加载泡泡画布组件，尝试了多种路径但都失败了');
                  }
                }
              }
            }
          } catch (err) {
            console.error('加载泡泡画布组件失败:', err);
            throw err;
          }
        } else if (type === 'star') {
          // 加载星星画布组件
          try {
            try {
              CanvasComponent = require('../components/star-canvas/index');
              console.log('成功加载星星画布组件: ../components/star-canvas/index');
            } catch (e1) {
              try {
                CanvasComponent = require('./components/star-canvas/index');
                console.log('成功加载星星画布组件: ./components/star-canvas/index');
              } catch (e2) {
                try {
                  CanvasComponent = require('components/star-canvas/index');
                  console.log('成功加载星星画布组件: components/star-canvas/index');
                } catch (e3) {
                  try {
                    CanvasComponent = require('/components/star-canvas/index');
                    console.log('成功加载星星画布组件: /components/star-canvas/index');
                  } catch (e4) {
                    throw new Error('无法加载星星画布组件，尝试了多种路径但都失败了');
                  }
                }
              }
            }
          } catch (err) {
            console.error('加载星星画布组件失败:', err);
            throw err;
          }
        } else {
          reject(new Error(`无效的画布类型: ${type}`));
          return;
        }

        resolve(CanvasComponent);
      } catch (err) {
        console.error(`加载${type}画布组件失败:`, err);
        reject(err);
      }
    });
  }

  /**
   * 切换画布类型
   * @param {string} type - 画布类型（bubble 或 star）
   * @param {Object} options - 初始化选项
   * @returns {Promise<boolean>} 是否切换成功
   */
  switchCanvas(type, options = {}) {
    return new Promise((resolve, reject) => {
      // 如果当前已经是该类型，直接返回成功
      if (this.currentType === type && this.canvasInstances[type]) {
        resolve(true);
        return;
      }

      // 触发画布切换开始事件
      this.eventBus.emit('canvas:switching', { fromType: this.currentType, toType: type });

      // 暂停当前画布动画
      this.pauseAnimation();

      // 创建新的画布
      this.createCanvas(type, options)
        .then(canvas => {
          // 切换成功
          this.currentType = type;

          // 启动新画布的动画
          canvas.startAnimation();

          // 触发画布切换完成事件
          this.eventBus.emit('canvas:switched', { type });

          resolve(true);
        })
        .catch(err => {
          console.error(`切换到${type}画布失败:`, err);

          // 如果切换失败，尝试恢复原来的画布
          if (this.currentType && this.canvasInstances[this.currentType]) {
            this.canvasInstances[this.currentType].startAnimation();
          }

          // 触发画布切换失败事件
          this.eventBus.emit('canvas:switchFailed', { fromType: this.currentType, toType: type, error: err });

          reject(err);
        });
    });
  }

  /**
   * 获取当前画布实例
   * @returns {Object} 画布组件实例
   */
  getCurrentCanvas() {
    if (!this.currentType) {
      return null;
    }

    return this.canvasInstances[this.currentType];
  }

  /**
   * 获取指定类型的画布实例
   * @param {string} type - 画布类型（bubble 或 star）
   * @returns {Object} 画布组件实例
   */
  getCanvas(type) {
    return this.canvasInstances[type] || null;
  }

  /**
   * 处理触摸开始事件
   * @param {Object} e - 触摸事件对象
   * @returns {Object|null} 交互结果
   */
  handleTouchStart(e) {
    const canvas = this.getCurrentCanvas();
    if (!canvas) {
      return null;
    }

    const result = canvas.handleTouchStart(e);

    // 触发触摸开始事件
    if (result) {
      this.eventBus.emit('canvas:touchStart', { type: this.currentType, result });
    }

    return result;
  }

  /**
   * 处理触摸移动事件
   * @param {Object} e - 触摸事件对象
   * @returns {string} 鼠标样式
   */
  handleTouchMove(e) {
    const canvas = this.getCurrentCanvas();
    if (!canvas) {
      return 'default';
    }

    const cursor = canvas.handleTouchMove(e);

    // 触发触摸移动事件
    this.eventBus.emit('canvas:touchMove', { type: this.currentType, cursor });

    return cursor;
  }

  /**
   * 处理触摸结束事件
   * @returns {Object|null} 交互结果
   */
  handleTouchEnd() {
    const canvas = this.getCurrentCanvas();
    if (!canvas) {
      return null;
    }

    const result = canvas.handleTouchEnd();

    // 触发触摸结束事件
    if (result) {
      this.eventBus.emit('canvas:touchEnd', { type: this.currentType, result });
    }

    return result;
  }

  /**
   * 更新主题数据
   * @param {Array} themes - 主题数据
   * @returns {boolean} 是否更新成功
   */
  updateThemes(themes) {
    // 更新主题管理器
    if (this.themeManager) {
      this.themeManager.updateThemes(themes);
    }

    // 更新所有画布实例
    let success = true;

    Object.values(this.canvasInstances).forEach(canvas => {
      if (canvas) {
        const result = canvas.updateThemes(themes);
        if (!result) {
          success = false;
        }
      }
    });

    // 触发主题更新事件
    this.eventBus.emit('canvas:themesUpdated', { themes, success });

    return success;
  }

  /**
   * 暂停当前画布动画
   */
  pauseAnimation() {
    const instance = this.getCurrentCanvas();
    if (instance && typeof instance.pauseAnimation === 'function') {
      console.log('CanvasManager: Calling pauseAnimation on', this.currentType);
      instance.pauseAnimation();
    } else {
      console.warn('CanvasManager: Cannot pause animation, instance or method not found for', this.currentType);
    }
  }

  /**
   * 恢复当前画布动画
   */
  resumeAnimation() {
    const instance = this.getCurrentCanvas();
    if (instance && instance.ctx && typeof instance.resumeAnimation === 'function') {
      console.log('CanvasManager: Calling resumeAnimation on', this.currentType);
      instance.resumeAnimation();
    } else {
      console.warn('CanvasManager: Cannot resume animation. Instance, context, or method not ready/found for', this.currentType, 'Instance:', instance, 'Context:', instance ? instance.ctx : 'N/A');
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 触发清理开始事件
    this.eventBus.emit('canvas:cleanupStarted');

    // 停止所有画布动画
    Object.values(this.canvasInstances).forEach(canvas => {
      if (canvas) {
        canvas.destroy();
      }
    });

    // 清空画布实例
    this.canvasInstances = {
      bubble: null,
      star: null
    };

    // 重置状态
    this.currentType = null;
    this.isInitializing = false;
    this.retryAttempts = 0;

    // 触发清理完成事件
    this.eventBus.emit('canvas:cleanupCompleted');

    console.log('画布管理器已清理');
  }
}

module.exports = CanvasManager;
