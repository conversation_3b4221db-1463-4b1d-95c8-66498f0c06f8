/**
 * 组件工具函数
 * 提供组件开发相关的工具函数
 */

/**
 * 生成组件样式类名
 * @param {string} baseClass - 基础类名
 * @param {Object} options - 选项对象，键为修饰符，值为布尔值
 * @param {string[]} extraClasses - 额外的类名数组
 * @returns {string} - 组合后的类名字符串
 */
export function classNames(baseClass, options = {}, extraClasses = []) {
  const classes = [baseClass];

  // 添加修饰符类名
  Object.entries(options).forEach(([modifier, isActive]) => {
    if (isActive) {
      classes.push(`${baseClass}--${modifier}`);
    }
  });

  // 添加额外类名
  if (extraClasses.length > 0) {
    classes.push(...extraClasses);
  }

  return classes.filter(Boolean).join(' ');
}

/**
 * 生成组件样式对象
 * @param {Object} baseStyle - 基础样式对象
 * @param {Object} conditionalStyles - 条件样式对象，键为条件，值为样式对象
 * @param {Object} extraStyle - 额外的样式对象
 * @returns {Object} - 组合后的样式对象
 */
export function styleObject(baseStyle = {}, conditionalStyles = {}, extraStyle = {}) {
  let result = { ...baseStyle };

  // 添加条件样式
  Object.entries(conditionalStyles).forEach(([condition, style]) => {
    if (condition === 'true' || condition === true) {
      result = { ...result, ...style };
    }
  });

  // 添加额外样式
  result = { ...result, ...extraStyle };

  return result;
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} - 防抖后的函数
 */
export function debounce(func, wait = 300) {
  let timeout;

  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} - 节流后的函数
 */
export function throttle(func, limit = 300) {
  let inThrottle;

  return function executedFunction(...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}

/**
 * 获取元素尺寸和位置信息
 * @param {string} selector - 元素选择器
 * @returns {Promise<Object>} - 元素的尺寸和位置信息
 */
export function getElementRect(selector) {
  return new Promise((resolve, reject) => {
    const query = wx.createSelectorQuery();
    query.select(selector).boundingClientRect();
    query.exec(res => {
      if (res && res[0]) {
        resolve(res[0]);
      } else {
        reject(new Error(`Element not found: ${selector}`));
      }
    });
  });
}

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式化模板，例如 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} - 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  const d = new Date(date);

  if (isNaN(d.getTime())) {
    return '';
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 生成唯一ID
 * @param {string} prefix - ID前缀
 * @returns {string} - 唯一ID
 */
export function uniqueId(prefix = 'id') {
  return `${prefix}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 深度合并对象
 * @param {Object} target - 目标对象
 * @param {Object} source - 源对象
 * @returns {Object} - 合并后的对象
 */
export function deepMerge(target, source) {
  const result = { ...target };

  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          result[key] = source[key];
        } else {
          result[key] = deepMerge(target[key], source[key]);
        }
      } else {
        result[key] = source[key];
      }
    });
  }

  return result;
}

/**
 * 检查值是否为对象
 * @param {*} item - 要检查的值
 * @returns {boolean} - 是否为对象
 */
function isObject(item) {
  return (item && typeof item === 'object' && !Array.isArray(item));
}

/**
 * 获取状态颜色
 * @param {string} status - 状态值
 * @returns {Object} - 状态颜色对象，包含背景色和文本色
 */
export function getStatusColor(status) {
  switch (status) {
    case 'notStarted':
      return { background: '#3498db', text: '#ffffff' };
    case 'inProgress':
      return { background: '#27ae60', text: '#ffffff' };
    case 'completed':
      return { background: '#f39c12', text: '#ffffff' };
    case 'paused':
      return { background: '#7f8c8d', text: '#ffffff' };
    case 'abandoned':
      return { background: '#e74c3c', text: '#ffffff' };
    default:
      return { background: '#95a5a6', text: '#ffffff' };
  }
}

/**
 * 获取状态文本
 * @param {string} status - 状态值
 * @returns {string} - 状态文本
 */
export function getStatusText(status) {
  switch (status) {
    case 'notStarted':
      return '未开始';
    case 'inProgress':
      return '进行中';
    case 'completed':
      return '已完成';
    case 'paused':
      return '已暂停';
    case 'abandoned':
      return '已放弃';
    default:
      return '未知';
  }
}

/**
 * 导出组件工具函数
 */
export default {
  classNames,
  styleObject,
  debounce,
  throttle,
  getElementRect,
  formatDate,
  uniqueId,
  deepMerge,
  getStatusColor,
  getStatusText
};
