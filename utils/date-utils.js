/**
 * 日期工具函数
 * 提供日期格式化、比较和计算功能
 */

/**
 * 格式化日期
 * @param {Date} date - 要格式化的日期
 * @param {string} format - 格式化模板，如'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format) {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return '';
  }

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  return format
    .replace(/YYYY/g, year)
    .replace(/MM/g, month < 10 ? `0${month}` : month)
    .replace(/DD/g, day < 10 ? `0${day}` : day)
    .replace(/HH/g, hours < 10 ? `0${hours}` : hours)
    .replace(/mm/g, minutes < 10 ? `0${minutes}` : minutes)
    .replace(/ss/g, seconds < 10 ? `0${seconds}` : seconds);
}

/**
 * 判断日期是否为今天
 * @param {Date} date - 要判断的日期
 * @returns {boolean} 是否为今天
 */
function isToday(date) {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return false;
  }

  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
}

/**
 * 计算两个日期之间的天数差
 * @param {Date} date1 - 第一个日期
 * @param {Date} date2 - 第二个日期
 * @returns {number} 天数差
 */
function getDaysDifference(date1, date2) {
  if (
    !date1 ||
    !date2 ||
    !(date1 instanceof Date) ||
    !(date2 instanceof Date) ||
    isNaN(date1.getTime()) ||
    isNaN(date2.getTime())
  ) {
    return 0;
  }

  // 将日期设置为当天的0点，忽略时分秒
  const d1 = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate());
  const d2 = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate());

  // 计算毫秒差，然后转换为天数
  const diffTime = d2.getTime() - d1.getTime();
  const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
}

/**
 * 获取指定日期所在月份的第一天
 * @param {Date} date - 指定日期
 * @returns {Date} 所在月份的第一天
 */
function getFirstDayOfMonth(date) {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return new Date();
  }

  return new Date(date.getFullYear(), date.getMonth(), 1);
}

/**
 * 获取指定日期所在月份的最后一天
 * @param {Date} date - 指定日期
 * @returns {Date} 所在月份的最后一天
 */
function getLastDayOfMonth(date) {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return new Date();
  }

  return new Date(date.getFullYear(), date.getMonth() + 1, 0);
}

/**
 * 获取指定日期所在周的第一天（周日）
 * @param {Date} date - 指定日期
 * @returns {Date} 所在周的第一天
 */
function getFirstDayOfWeek(date) {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return new Date();
  }

  const day = date.getDay();
  return new Date(date.getFullYear(), date.getMonth(), date.getDate() - day);
}

/**
 * 获取指定日期所在周的最后一天（周六）
 * @param {Date} date - 指定日期
 * @returns {Date} 所在周的最后一天
 */
function getLastDayOfWeek(date) {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return new Date();
  }

  const day = date.getDay();
  return new Date(date.getFullYear(), date.getMonth(), date.getDate() + (6 - day));
}

/**
 * 获取指定日期的相对日期
 * @param {Date} date - 指定日期
 * @param {number} days - 相对天数，正数为之后，负数为之前
 * @returns {Date} 相对日期
 */
function getRelativeDate(date, days) {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return new Date();
  }

  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

module.exports = {
  formatDate,
  isToday,
  getDaysDifference,
  getFirstDayOfMonth,
  getLastDayOfMonth,
  getFirstDayOfWeek,
  getLastDayOfWeek,
  getRelativeDate
};
