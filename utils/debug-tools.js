// utils/debug-tools.js
// 调试工具

/**
 * 调试工具类
 * 提供调试功能，用于在开发过程中调试组件
 */
class DebugTools {
  /**
   * 构造函数
   */
  constructor() {
    // 调试状态
    this.isDebugMode = false;
    this.debugOptions = {
      showFPS: false,
      showBoundingBoxes: false,
      showElementInfo: false,
      slowMotion: false,
      pauseOnError: true
    };

    // 日志级别
    this.logLevel = 'info'; // 'debug', 'info', 'warn', 'error'
    
    // 错误计数
    this.errorCount = 0;
    this.maxErrorsBeforeWarning = 10;
    
    // 性能标记
    this.performanceMarks = {};
  }

  /**
   * 启用调试模式
   * @param {Object} options - 调试选项
   */
  enableDebugMode(options = {}) {
    this.isDebugMode = true;
    this.debugOptions = { ...this.debugOptions, ...options };
    console.log('调试模式已启用', this.debugOptions);
  }

  /**
   * 禁用调试模式
   */
  disableDebugMode() {
    this.isDebugMode = false;
    console.log('调试模式已禁用');
  }

  /**
   * 设置日志级别
   * @param {string} level - 日志级别 ('debug', 'info', 'warn', 'error')
   */
  setLogLevel(level) {
    if (['debug', 'info', 'warn', 'error'].includes(level)) {
      this.logLevel = level;
      console.log(`日志级别已设置为: ${level}`);
    } else {
      console.error(`无效的日志级别: ${level}`);
    }
  }

  /**
   * 记录日志
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {*} data - 日志数据
   */
  log(level, message, data = null) {
    const levels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };

    if (levels[level] >= levels[this.logLevel]) {
      const timestamp = new Date().toISOString();
      const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

      switch (level) {
        case 'debug':
          console.debug(`${prefix} ${message}`, data);
          break;
        case 'info':
          console.info(`${prefix} ${message}`, data);
          break;
        case 'warn':
          console.warn(`${prefix} ${message}`, data);
          break;
        case 'error':
          console.error(`${prefix} ${message}`, data);
          this.errorCount++;
          
          // 如果错误过多，发出警告
          if (this.errorCount === this.maxErrorsBeforeWarning) {
            console.warn(`已记录${this.errorCount}个错误，可能存在严重问题`);
          }
          break;
      }
    }
  }

  /**
   * 记录性能标记
   * @param {string} name - 标记名称
   */
  markPerformance(name) {
    this.performanceMarks[name] = Date.now();
  }

  /**
   * 测量性能
   * @param {string} startMark - 开始标记名称
   * @param {string} endMark - 结束标记名称
   * @param {string} label - 标签
   * @returns {number} 耗时(毫秒)
   */
  measurePerformance(startMark, endMark, label = '') {
    if (!this.performanceMarks[startMark] || !this.performanceMarks[endMark]) {
      console.warn(`性能标记不存在: ${startMark} 或 ${endMark}`);
      return -1;
    }

    const duration = this.performanceMarks[endMark] - this.performanceMarks[startMark];
    
    if (label) {
      console.log(`${label}: ${duration}ms`);
    }
    
    return duration;
  }

  /**
   * 绘制调试信息
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {Object} canvas - Canvas对象
   * @param {Array} elements - 元素数组
   * @param {Object} metrics - 性能指标
   */
  drawDebugInfo(ctx, canvas, elements, metrics = null) {
    if (!this.isDebugMode || !ctx) return;

    // 保存上下文状态
    ctx.save();

    // 绘制边界框
    if (this.debugOptions.showBoundingBoxes && elements) {
      ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
      ctx.lineWidth = 1;

      elements.forEach(element => {
        if (element.radius) {
          // 圆形元素
          ctx.beginPath();
          ctx.arc(element.x, element.y, element.radius, 0, Math.PI * 2);
          ctx.stroke();
        } else if (element.width && element.height) {
          // 矩形元素
          ctx.strokeRect(element.x, element.y, element.width, element.height);
        }
      });
    }

    // 绘制元素信息
    if (this.debugOptions.showElementInfo && elements) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';

      elements.forEach(element => {
        const info = `ID: ${element.id || 'unknown'}`;
        ctx.fillText(info, element.x, element.y - (element.radius || 0) - 5);
      });
    }

    // 绘制FPS信息
    if (this.debugOptions.showFPS) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(10, 10, 150, metrics ? 70 : 30);
      ctx.fillStyle = 'white';
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';

      const fps = metrics ? metrics.fps.current : 'N/A';
      ctx.fillText(`FPS: ${fps}`, 15, 15);

      if (metrics) {
        ctx.fillText(`Avg FPS: ${metrics.fps.avg.toFixed(1)}`, 15, 30);
        ctx.fillText(`Frame Time: ${metrics.frameTime.current.toFixed(2)}ms`, 15, 45);
        ctx.fillText(`Elements: ${elements ? elements.length : 0}`, 15, 60);
      }
    }

    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 获取调试状态
   * @returns {Object} 调试状态
   */
  getDebugState() {
    return {
      isDebugMode: this.isDebugMode,
      debugOptions: { ...this.debugOptions },
      logLevel: this.logLevel,
      errorCount: this.errorCount
    };
  }

  /**
   * 重置调试状态
   */
  resetDebugState() {
    this.errorCount = 0;
    this.performanceMarks = {};
    console.log('调试状态已重置');
  }
}

// 创建全局调试工具实例
const debugTools = new DebugTools();

module.exports = debugTools;
