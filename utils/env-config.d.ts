/**
 * 环境配置类型定义
 */

/**
 * 环境版本类型
 */
export type EnvVersion = 'develop' | 'trial' | 'release';

/**
 * 日志级别类型
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * 环境配置接口
 */
export interface EnvConfig {
  /**
   * API基础URL
   */
  apiBaseUrl: string;
  
  /**
   * 是否为测试模式
   */
  isTestMode: boolean;
  
  /**
   * 日志级别
   */
  logLevel: LogLevel;
  
  /**
   * 环境版本
   */
  envVersion: EnvVersion;
  
  /**
   * 是否为生产环境
   */
  isProduction: boolean;
}

/**
 * 获取环境配置
 * @returns 当前环境配置
 */
export function getEnvConfig(): EnvConfig;

/**
 * 获取环境版本
 * @returns 当前环境版本
 */
export function getEnvVersion(): EnvVersion;
