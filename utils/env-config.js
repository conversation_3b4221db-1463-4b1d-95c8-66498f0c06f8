/**
 * 环境配置管理
 * 根据小程序环境自动选择对应的配置
 */

// 获取小程序环境版本
const getEnvVersion = () => {
  try {
    return wx.getAccountInfoSync().miniProgram.envVersion || 'develop';
  } catch (error) {
    console.error('获取小程序环境版本失败:', error);
    return 'develop'; // 默认为开发环境
  }
};

// 环境配置映射
const ENV_CONFIG = {
  // 开发环境 (工具内预览)
  develop: {
    apiBaseUrl: 'http://localhost:3010/mock-api/v1',
    isTestMode: true,
    logLevel: 'debug'
  },
  
  // 体验版环境
  trial: {
    apiBaseUrl: 'https://api-test.aibubb.com/api/v1',
    isTestMode: true,
    logLevel: 'info'
  },
  
  // 正式环境
  release: {
    apiBaseUrl: 'https://api.aibubb.com/api/v1',
    isTestMode: false,
    logLevel: 'error'
  }
};

// 获取当前环境配置
const getCurrentEnvConfig = () => {
  const envVersion = getEnvVersion();
  console.log(`当前小程序环境: ${envVersion}`);
  
  // 获取对应环境的配置，如果没有则使用开发环境配置
  const config = ENV_CONFIG[envVersion] || ENV_CONFIG.develop;
  
  return {
    ...config,
    // 添加环境标识
    envVersion,
    // 是否为生产环境
    isProduction: envVersion === 'release'
  };
};

module.exports = {
  getEnvConfig: getCurrentEnvConfig,
  getEnvVersion
};
