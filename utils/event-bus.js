// utils/event-bus.js
// 事件总线，用于组件间通信

/**
 * 事件总线类
 * 提供发布-订阅模式的事件机制，用于组件间通信
 */
class EventBus {
  /**
   * 构造函数
   */
  constructor() {
    this.events = {};
    this.nextId = 1;
  }

  /**
   * 订阅事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   * @returns {string} 订阅ID，用于取消订阅
   */
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = {};
    }

    const id = `${event}_${this.nextId++}`;
    this.events[event][id] = callback;

    return id;
  }

  /**
   * 取消订阅事件
   * @param {string} id - 订阅ID
   */
  off(id) {
    if (!id) return;

    const [event] = id.split('_');
    if (this.events[event] && this.events[event][id]) {
      delete this.events[event][id];
    }
  }

  /**
   * 发布事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    if (!this.events[event]) return;

    Object.values(this.events[event]).forEach(callback => {
      try {
        callback(data);
      } catch (err) {
        console.error(`执行事件回调时出错: ${event}`, err);
      }
    });
  }

  /**
   * 清理所有事件
   */
  clear() {
    this.events = {};
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus();

module.exports = eventBus;
