/**
 * 学习计划管理服务
 * 提供统一的学习计划创建、查询、更新和删除等功能
 */

// 导入API客户端
const api = require('./api-client/index').default;
// 导入认证服务
const authService = require('./auth-service');

/**
 * 学习计划管理服务
 */
class LearningPlanService {
  /**
   * 获取用户的学习计划列表
   * @param {Object} params - 查询参数
   * @param {boolean} params.isCurrent - 是否当前计划
   * @param {string} params.status - 状态
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.sortBy - 排序字段
   * @param {string} params.sortOrder - 排序方式
   * @returns {Promise<Object>} 学习计划列表
   */
  async getUserPlans(params = {}) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API获取学习计划列表
      const response = await api.learningPlan.getLearningPlans(params);

      return response;
    } catch (error) {
      console.error('获取学习计划列表失败:', error);
      return {
        success: false,
        error: error.message || '获取学习计划列表失败，请稍后再试'
      };
    }
  }

  /**
   * 获取学习计划详情
   * @param {number|string} id - 学习计划ID
   * @param {boolean} isSystemDefault - 是否系统默认计划
   * @returns {Promise<Object>} 学习计划详情
   */
  async getPlanById(id, isSystemDefault = false) {
    try {
      // 如果不是系统默认计划，检查用户是否已登录
      if (!isSystemDefault) {
        const isLoggedIn = await authService.isLoggedIn();

        if (!isLoggedIn) {
          return {
            success: false,
            error: '用户未登录'
          };
        }
      }

      // 调用API获取学习计划详情
      const response = await api.learningPlan.getLearningPlan(id);

      return response;
    } catch (error) {
      console.error('获取学习计划详情失败:', error);
      return {
        success: false,
        error: error.message || '获取学习计划详情失败，请稍后再试'
      };
    }
  }

  /**
   * 创建学习计划
   * @param {Object} data - 学习计划数据
   * @returns {Promise<Object>} 创建结果
   */
  async createPlan(data) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API创建学习计划
      const response = await api.learningPlan.createLearningPlan(data);

      return response;
    } catch (error) {
      console.error('创建学习计划失败:', error);
      return {
        success: false,
        error: error.message || '创建学习计划失败，请稍后再试'
      };
    }
  }

  /**
   * 从模板创建学习计划
   * @param {number|string} templateId - 模板ID
   * @param {Object} data - 额外数据
   * @returns {Promise<Object>} 创建结果
   */
  async createPlanFromTemplate(templateId, data = {}) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API从模板创建学习计划
      const response = await api.learningPlan.createPlanFromTemplate(templateId, data);

      return response;
    } catch (error) {
      console.error('从模板创建学习计划失败:', error);
      return {
        success: false,
        error: error.message || '从模板创建学习计划失败，请稍后再试'
      };
    }
  }

  /**
   * 使用AI生成学习计划
   * @param {Object} data - 生成参数
   * @param {string} data.title - 计划标题
   * @param {string} data.trouble - 困扰问题
   * @param {string} data.intensity - 学习强度
   * @param {number} data.duration - 学习时长
   * @param {number} data.themeId - 主题ID
   * @returns {Promise<Object>} 生成结果
   */
  async generatePlan(data) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API生成学习计划
      const response = await api.learningPlan.generatePlan(data);

      return response;
    } catch (error) {
      console.error('生成学习计划失败:', error);
      return {
        success: false,
        error: error.message || '生成学习计划失败，请稍后再试'
      };
    }
  }

  /**
   * 更新学习计划
   * @param {number|string} id - 学习计划ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updatePlan(id, data) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API更新学习计划
      const response = await api.learningPlan.updateLearningPlan(id, data);

      return response;
    } catch (error) {
      console.error('更新学习计划失败:', error);
      return {
        success: false,
        error: error.message || '更新学习计划失败，请稍后再试'
      };
    }
  }

  /**
   * 删除学习计划
   * @param {number|string} id - 学习计划ID
   * @returns {Promise<Object>} 删除结果
   */
  async deletePlan(id) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API删除学习计划
      const response = await api.learningPlan.deleteLearningPlan(id);

      return response;
    } catch (error) {
      console.error('删除学习计划失败:', error);
      return {
        success: false,
        error: error.message || '删除学习计划失败，请稍后再试'
      };
    }
  }

  /**
   * 设置当前学习计划
   * @param {number|string} id - 学习计划ID
   * @returns {Promise<Object>} 设置结果
   */
  async setCurrentPlan(id) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API设置当前学习计划
      const response = await api.learningPlan.setCurrentPlan(id);

      return response;
    } catch (error) {
      console.error('设置当前学习计划失败:', error);
      return {
        success: false,
        error: error.message || '设置当前学习计划失败，请稍后再试'
      };
    }
  }

  /**
   * 获取学习计划统计数据
   * @param {number|string} id - 学习计划ID
   * @returns {Promise<Object>} 统计数据
   */
  async getPlanStatistics(id) {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        return {
          success: false,
          error: '用户未登录'
        };
      }

      // 调用API获取学习计划统计数据
      const response = await api.learningPlan.getPlanStatistics(id);

      return response;
    } catch (error) {
      console.error('获取学习计划统计数据失败:', error);
      return {
        success: false,
        error: error.message || '获取学习计划统计数据失败，请稍后再试'
      };
    }
  }

  /**
   * 获取学习计划模板列表
   * @param {Object} params - 查询参数
   * @param {number} params.themeId - 主题ID
   * @param {string} params.difficulty - 难度
   * @param {boolean} params.isOfficial - 是否官方
   * @param {boolean} params.isPublic - 是否公开
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @returns {Promise<Object>} 模板列表
   */
  async getTemplates(params = {}) {
    try {
      // 调用API获取学习计划模板列表
      const response = await api.learningPlan.getLearningTemplates(params);

      return response;
    } catch (error) {
      console.error('获取学习计划模板列表失败:', error);
      return {
        success: false,
        error: error.message || '获取学习计划模板列表失败，请稍后再试'
      };
    }
  }

  /**
   * 获取学习计划模板详情
   * @param {number|string} id - 模板ID
   * @returns {Promise<Object>} 模板详情
   */
  async getTemplateById(id) {
    try {
      // 调用API获取学习计划模板详情
      const response = await api.learningPlan.getLearningTemplate(id);

      return response;
    } catch (error) {
      console.error('获取学习计划模板详情失败:', error);
      return {
        success: false,
        error: error.message || '获取学习计划模板详情失败，请稍后再试'
      };
    }
  }
}

// 导出单例实例
module.exports = new LearningPlanService();
