# NebulaLearn 数据模型层

这是NebulaLearn项目的数据模型层，提供统一的数据模型定义、数据转换、数据验证和模型服务功能。

## 特性

- **统一数据模型**：基于后端定义的数据结构，实现前端类型定义
- **数据转换**：自动处理snake_case到camelCase的转换，支持API响应到前端模型的转换
- **数据验证**：提供数据验证功能，确保数据符合业务规则
- **模型服务**：整合转换器和验证器，提供完整的模型服务

## 目录结构

```
utils/models/
├── interfaces/        # 数据模型接口定义
│   ├── base.ts        # 基础接口
│   ├── theme.ts       # 主题接口
│   ├── learning-plan.ts # 学习计划接口
│   └── ...
├── transformers/      # 数据转换器
│   ├── base-transformer.ts # 基础转换器
│   ├── theme-transformer.ts # 主题转换器
│   └── ...
├── validators/        # 数据验证器
│   ├── base-validator.ts # 基础验证器
│   ├── theme-validator.ts # 主题验证器
│   └── ...
├── services/          # 模型服务
│   ├── base-model-service.ts # 基础模型服务
│   ├── theme-model-service.ts # 主题模型服务
│   └── ...
└── index.ts           # 模型索引
```

## 使用方法

### 基本用法

```javascript
import { ThemeModelService } from 'utils/models/services/theme-model-service';
import api from 'utils/api-client/api-modules';

// 创建模型服务实例
const themeService = new ThemeModelService();

// 获取主题列表
async function fetchThemes() {
  try {
    // 使用API客户端获取数据
    const result = await api.theme.getThemes({ page: 1, pageSize: 10 });
    
    // 使用模型服务转换数据
    const themeModels = themeService.fromApiPaginatedResponse(result);
    
    // 使用转换后的模型
    console.log('主题列表:', themeModels.data);
    console.log('总数:', themeModels.meta.total);
  } catch (error) {
    console.error('获取主题列表失败:', error.message);
  }
}
```

### 数据验证

```javascript
import { ThemeModelService } from 'utils/models/services/theme-model-service';

// 创建模型服务实例
const themeService = new ThemeModelService();

// 验证创建主题请求
function validateTheme(themeData) {
  const validationResult = themeService.validateCreateRequest(themeData);
  
  if (!validationResult.isValid) {
    console.error('验证错误:', validationResult.errors);
    return false;
  }
  
  return true;
}

// 创建主题
async function createTheme(themeData) {
  // 验证数据
  if (!validateTheme(themeData)) {
    return;
  }
  
  try {
    // 转换数据为API请求格式
    const apiData = themeService.createRequestToApiRequest(themeData);
    
    // 发送API请求
    const result = await api.theme.createTheme(apiData);
    
    // 转换响应为模型
    const createdTheme = themeService.fromApiResponse(result);
    
    console.log('主题创建成功:', createdTheme);
  } catch (error) {
    console.error('创建主题失败:', error.message);
  }
}
```

### 查询参数转换

```javascript
import { ThemeModelService } from 'utils/models/services/theme-model-service';
import api from 'utils/api-client/api-modules';

// 创建模型服务实例
const themeService = new ThemeModelService();

// 使用查询参数获取主题列表
async function searchThemes(params) {
  try {
    // 转换查询参数为API格式
    const apiParams = themeService.themeQueryParamsToApiParams(params);
    
    // 发送API请求
    const result = await api.theme.getThemes(apiParams);
    
    // 转换响应为模型
    const themeModels = themeService.fromApiPaginatedResponse(result);
    
    return themeModels;
  } catch (error) {
    console.error('搜索主题失败:', error.message);
    throw error;
  }
}

// 示例调用
searchThemes({
  page: 1,
  pageSize: 20,
  sortBy: 'createdAt',
  sortOrder: 'desc',
  isActive: true,
  search: '编程'
});
```

## 扩展

### 添加新的模型

1. 在 `interfaces/` 目录下创建新的接口定义文件
2. 在 `transformers/` 目录下创建新的转换器
3. 在 `validators/` 目录下创建新的验证器
4. 在 `services/` 目录下创建新的模型服务
5. 在相应的索引文件中导出新添加的内容

### 自定义转换逻辑

可以通过扩展基础转换器来自定义转换逻辑：

```javascript
import { BaseTransformer } from 'utils/models/transformers/base-transformer';

class CustomTransformer extends BaseTransformer {
  fromApi(data) {
    // 自定义转换逻辑
    const model = super.fromApi(data);
    
    // 添加额外处理
    model.customField = this.processCustomField(data.custom_field);
    
    return model;
  }
  
  processCustomField(value) {
    // 自定义处理逻辑
    return value ? value.toUpperCase() : '';
  }
}
```

### 自定义验证规则

可以通过扩展基础验证器来自定义验证规则：

```javascript
import { BaseValidator } from 'utils/models/validators/base-validator';

class CustomValidator extends BaseValidator {
  validate(data) {
    const errors = [];
    
    // 基本验证
    const nameError = this.validateRequired(data, 'name', '名称');
    if (nameError) errors.push(nameError);
    
    // 自定义验证规则
    if (data.customField && !this.validateCustomField(data.customField)) {
      errors.push({
        field: 'customField',
        message: '自定义字段格式无效'
      });
    }
    
    return this.createResult(errors.length === 0, errors);
  }
  
  validateCustomField(value) {
    // 自定义验证逻辑
    return /^[A-Z]{3}-\d{4}$/.test(value);
  }
}
```

## 单元测试

数据模型层包含完整的单元测试，可以通过以下命令运行：

```bash
npm test -- --testPathPattern=models
```
