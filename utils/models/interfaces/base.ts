/**
 * 基础模型接口
 * 定义所有模型共有的属性
 */

export interface BaseModel {
  id: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

/**
 * 软删除模型接口
 * 定义支持软删除的模型共有的属性和方法
 */
export interface SoftDeleteModel extends BaseModel {
  isDeleted: boolean;
}

/**
 * 分页响应接口
 * 定义分页响应的结构
 */
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

/**
 * API响应接口
 * 定义API响应的通用结构
 */
export interface ApiResponse<T> {
  data: T;
  message?: string;
  meta?: {
    [key: string]: any;
  };
}

/**
 * 查询参数接口
 * 定义查询参数的通用结构
 */
export interface QueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

/**
 * 过滤参数接口
 * 定义过滤参数的通用结构
 */
export interface FilterParams {
  [key: string]: any;
}
