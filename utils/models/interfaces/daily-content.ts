/**
 * 每日内容模型接口
 * 定义每日内容模型的属性和方法
 */

import { BaseModel } from './base';
import { Exercise } from './exercise';
import { Insight } from './insight';

/**
 * 内容类型枚举
 */
export enum ContentType {
  Exercise = 'exercise',
  Insight = 'insight'
}

/**
 * 每日内容接口
 */
export interface DailyContent extends BaseModel {
  userId: number;
  planId: number;
  date: string;
  contentType: ContentType;
  contentId: number;
  isCompleted: boolean;
  completedAt?: string;

  // 关联数据
  exercise?: Exercise;
  insight?: Insight;
}

/**
 * 创建每日内容请求接口
 */
export interface CreateDailyContentRequest {
  planId: number;
  date: string;
  contentType: ContentType;
  contentId: number;
}

/**
 * 更新每日内容请求接口
 */
export interface UpdateDailyContentRequest {
  isCompleted?: boolean;
}

/**
 * 每日内容查询参数接口
 */
export interface DailyContentQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  userId?: number;
  planId?: number;
  date?: string;
  contentType?: ContentType;
  isCompleted?: boolean;
  includeContent?: boolean;
}
