/**
 * 练习模型接口
 * 定义练习模型的属性和方法
 */

import { BaseModel } from './base';
import { Tag } from './tag';

/**
 * 练习难度枚举
 */
export enum ExerciseDifficulty {
  Easy = 'easy',
  Medium = 'medium',
  Hard = 'hard'
}

/**
 * 练习接口
 */
export interface Exercise extends BaseModel {
  title: string;
  description: string;
  content: string;
  difficulty: ExerciseDifficulty;
  estimatedMinutes: number;
  tagId: number;
  creatorId?: number;
  isOfficial: boolean;
  completionCount: number;
  likeCount: number;

  // 关联数据
  tag?: Tag;
}

/**
 * 创建练习请求接口
 */
export interface CreateExerciseRequest {
  title: string;
  description: string;
  content: string;
  difficulty?: ExerciseDifficulty;
  estimatedMinutes?: number;
  tagId: number;
  isOfficial?: boolean;
}

/**
 * 更新练习请求接口
 */
export interface UpdateExerciseRequest {
  title?: string;
  description?: string;
  content?: string;
  difficulty?: ExerciseDifficulty;
  estimatedMinutes?: number;
  tagId?: number;
  isOfficial?: boolean;
}

/**
 * 练习查询参数接口
 */
export interface ExerciseQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  tagId?: number;
  difficulty?: ExerciseDifficulty;
  creatorId?: number;
  isOfficial?: boolean;
  search?: string;
  includeTag?: boolean;
}
