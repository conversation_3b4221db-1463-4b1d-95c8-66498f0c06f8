/**
 * 观点模型接口
 * 定义观点模型的属性和方法
 */

import { BaseModel } from './base';
import { Tag } from './tag';

/**
 * 观点类型枚举
 */
export enum InsightType {
  Quote = 'quote',
  Concept = 'concept',
  Principle = 'principle',
  Fact = 'fact',
  Question = 'question'
}

/**
 * 观点接口
 */
export interface Insight extends BaseModel {
  title: string;
  content: string;
  source?: string;
  type: InsightType;
  tagId: number;
  creatorId?: number;
  isOfficial: boolean;
  viewCount: number;
  likeCount: number;

  // 关联数据
  tag?: Tag;
}

/**
 * 创建观点请求接口
 */
export interface CreateInsightRequest {
  title: string;
  content: string;
  source?: string;
  type?: InsightType;
  tagId: number;
  isOfficial?: boolean;
}

/**
 * 更新观点请求接口
 */
export interface UpdateInsightRequest {
  title?: string;
  content?: string;
  source?: string;
  type?: InsightType;
  tagId?: number;
  isOfficial?: boolean;
}

/**
 * 观点查询参数接口
 */
export interface InsightQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  tagId?: number;
  type?: InsightType;
  creatorId?: number;
  isOfficial?: boolean;
  search?: string;
  includeTag?: boolean;
}
