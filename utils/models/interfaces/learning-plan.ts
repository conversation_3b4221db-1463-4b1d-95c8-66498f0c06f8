/**
 * 学习计划模型接口
 * 定义学习计划模型的属性和方法
 */

import { BaseModel } from './base';
import { Theme } from './theme';
import { Tag } from './tag';

/**
 * 学习计划状态枚举
 */
export enum LearningPlanStatus {
  NotStarted = 'notStarted',
  InProgress = 'inProgress',
  Completed = 'completed',
  Paused = 'paused',
  Abandoned = 'abandoned'
}

/**
 * 学习计划接口
 */
export interface LearningPlan extends BaseModel {
  userId: number;
  templateId?: number;
  themeId?: number;
  title: string;
  description?: string;
  coverImageUrl?: string;
  targetDays: number;
  completedDays: number;
  progress: number;
  dailyGoalExercises: number;
  dailyGoalInsights: number;
  dailyGoalMinutes: number;
  status: LearningPlanStatus;
  startDate?: string;
  endDate?: string;
  isCurrent: boolean;
  isSystemDefault: boolean;
  isPublic: boolean;

  // 关联数据
  theme?: Theme;
  tags?: Tag[];
}

/**
 * 创建学习计划请求接口
 */
export interface CreateLearningPlanRequest {
  templateId?: number;
  themeId?: number;
  title: string;
  description?: string;
  coverImageUrl?: string;
  targetDays?: number;
  dailyGoalExercises?: number;
  dailyGoalInsights?: number;
  dailyGoalMinutes?: number;
  startDate?: string;
  isPublic?: boolean;
  tagIds?: number[];
}

/**
 * 更新学习计划请求接口
 */
export interface UpdateLearningPlanRequest {
  title?: string;
  description?: string;
  coverImageUrl?: string;
  targetDays?: number;
  dailyGoalExercises?: number;
  dailyGoalInsights?: number;
  dailyGoalMinutes?: number;
  status?: LearningPlanStatus;
  startDate?: string;
  endDate?: string;
  isCurrent?: boolean;
  isPublic?: boolean;
  tagIds?: number[];
}

/**
 * 学习计划查询参数接口
 */
export interface LearningPlanQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  userId?: number;
  themeId?: number;
  templateId?: number;
  status?: LearningPlanStatus;
  isCurrent?: boolean;
  isPublic?: boolean;
  search?: string;
  includeTags?: boolean;
  includeTheme?: boolean;
}
