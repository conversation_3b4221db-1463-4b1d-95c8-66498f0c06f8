/**
 * 笔记模型接口
 * 定义笔记模型的属性和方法
 */

import { BaseModel } from './base';
import { Tag } from './tag';
import { User } from './user';

/**
 * 笔记接口
 */
export interface Note extends BaseModel {
  title: string;
  content: string;
  tagId: number;
  userId: number;
  isPublic: boolean;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  isAiGenerated?: boolean; // 是否由AI生成

  // 关联数据
  tag?: Tag;
  user?: User;
}

/**
 * 创建笔记请求接口
 */
export interface CreateNoteRequest {
  title: string;
  content: string;
  tagId: number;
  isPublic?: boolean;
}

/**
 * 更新笔记请求接口
 */
export interface UpdateNoteRequest {
  title?: string;
  content?: string;
  tagId?: number;
  isPublic?: boolean;
}

/**
 * 笔记查询参数接口
 */
export interface NoteQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  tagId?: number;
  userId?: number;
  isPublic?: boolean;
  search?: string;
  includeTag?: boolean;
  includeUser?: boolean;
}
