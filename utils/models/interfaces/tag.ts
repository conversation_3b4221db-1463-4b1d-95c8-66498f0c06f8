/**
 * 标签模型接口
 * 定义标签模型的属性和方法
 */

import { BaseModel } from './base';

/**
 * 标签接口
 */
export interface Tag extends BaseModel {
  name: string;
  categoryId?: number;
  creatorId?: number;
  description?: string;
  relevanceScore: number;
  weight: number;
  usageCount: number;
  likeCount: number;
  isVerified: boolean;
  isOfficial: boolean;
  sortOrder: number;

  // 关联数据
  synonyms?: string[];
}

/**
 * 创建标签请求接口
 */
export interface CreateTagRequest {
  name: string;
  categoryId?: number;
  description?: string;
  isOfficial?: boolean;
  synonyms?: string[];
}

/**
 * 更新标签请求接口
 */
export interface UpdateTagRequest {
  name?: string;
  categoryId?: number;
  description?: string;
  relevanceScore?: number;
  weight?: number;
  isVerified?: boolean;
  isOfficial?: boolean;
  sortOrder?: number;
  synonyms?: string[];
}

/**
 * 标签查询参数接口
 */
export interface TagQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  categoryId?: number;
  creatorId?: number;
  isVerified?: boolean;
  isOfficial?: boolean;
  search?: string;
  includeSynonyms?: boolean;
}
