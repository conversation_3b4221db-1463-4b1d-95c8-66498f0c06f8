/**
 * 主题模型接口
 * 定义主题模型的属性和方法
 */

import { BaseModel } from './base';

/**
 * 主题接口
 */
export interface Theme extends BaseModel {
  name: string;
  englishName?: string;
  description?: string;
  icon?: string;
  color?: string;
  coverImageUrl?: string;
  sortOrder: number;
  isActive: boolean;
  parentId?: number;
}

/**
 * 创建主题请求接口
 */
export interface CreateThemeRequest {
  name: string;
  englishName?: string;
  description?: string;
  icon?: string;
  color?: string;
  coverImageUrl?: string;
  sortOrder?: number;
  isActive?: boolean;
  parentId?: number;
}

/**
 * 更新主题请求接口
 */
export interface UpdateThemeRequest {
  name?: string;
  englishName?: string;
  description?: string;
  icon?: string;
  color?: string;
  coverImageUrl?: string;
  sortOrder?: number;
  isActive?: boolean;
  parentId?: number;
}

/**
 * 主题查询参数接口
 */
export interface ThemeQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  isActive?: boolean;
  parentId?: number;
  search?: string;
}
