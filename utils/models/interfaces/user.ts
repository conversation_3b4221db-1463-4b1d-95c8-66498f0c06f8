/**
 * 用户模型接口
 * 定义用户模型的属性和方法
 */

import { BaseModel } from './base';

/**
 * 用户接口
 */
export interface User extends BaseModel {
  openid?: string;
  nickname: string;
  avatarUrl?: string;
  levelId?: number;
  bio?: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
  language?: string;
  followingCount: number;
  followerCount: number;
  noteCount: number;
  likeCount: number;
}

/**
 * 更新用户请求接口
 */
export interface UpdateUserRequest {
  nickname?: string;
  avatarUrl?: string;
  bio?: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
  language?: string;
}

/**
 * 用户查询参数接口
 */
export interface UserQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  levelId?: number;
  search?: string;
}
