/**
 * 基础模型服务
 * 提供模型服务的基础功能，整合转换器和验证器
 */

import { BaseTransformer } from '../transformers/base-transformer';
import { BaseValidator, ValidationResult } from '../validators/base-validator';
import { ApiResponse, PaginatedResponse, QueryParams } from '../interfaces/base';

/**
 * 基础模型服务类
 */
export abstract class BaseModelService<T, R = any> {
  protected transformer: BaseTransformer<T, R>;
  protected validator?: BaseValidator<T>;

  constructor(transformer: BaseTransformer<T, R>, validator?: BaseValidator<T>) {
    this.transformer = transformer;
    this.validator = validator;
  }

  /**
   * 验证数据
   * @param data 要验证的数据
   * @returns 验证结果
   */
  validate(data: Partial<T>): ValidationResult {
    if (!this.validator) {
      return { isValid: true, errors: [] };
    }

    return this.validator.validate(data);
  }

  /**
   * 转换API响应为模型
   * @param response API响应
   * @returns 模型
   */
  fromApiResponse(response: ApiResponse<R>): T {
    return this.transformer.fromApi(response.data);
  }

  /**
   * 转换API分页响应为模型数组
   * @param response API分页响应
   * @returns 模型数组和分页信息
   */
  fromApiPaginatedResponse(response: ApiResponse<R[]>): PaginatedResponse<T> {
    const data = this.transformer.fromApiArray(response.data);
    const responseMeta = response.meta as any; // Treat as any to access properties flexibly

    return {
      data,
      meta: {
        total: responseMeta?.total !== undefined ? Number(responseMeta.total) : data.length,
        page: responseMeta?.page !== undefined ? Number(responseMeta.page) : 1,
        pageSize: responseMeta?.pageSize !== undefined ? Number(responseMeta.pageSize) : data.length,
        totalPages: responseMeta?.totalPages !== undefined ? Number(responseMeta.totalPages) : 1
      }
    };
  }

  /**
   * 转换模型为API请求数据
   * @param model 模型
   * @returns API请求数据
   */
  toApiRequest(model: Partial<T>): R {
    return this.transformer.toApi(model);
  }

  /**
   * 转换查询参数为API查询参数
   * @param params 查询参数
   * @returns API查询参数
   */
  toApiQueryParams(params: QueryParams): any {
    // 默认实现，子类可以覆盖
    const apiParams: any = {};

    if (params.page !== undefined) apiParams.page = params.page;
    if (params.pageSize !== undefined) apiParams.page_size = params.pageSize;
    if (params.sortBy !== undefined) apiParams.sort_by = this.camelToSnake(params.sortBy);
    if (params.sortOrder !== undefined) apiParams.sort_order = params.sortOrder;

    // 处理其他参数
    for (const key in params) {
      if (
        Object.prototype.hasOwnProperty.call(params, key) &&
        !['page', 'pageSize', 'sortBy', 'sortOrder'].includes(key)
      ) {
        apiParams[this.camelToSnake(key)] = params[key];
      }
    }

    return apiParams;
  }

  /**
   * 将camelCase转换为snake_case
   * @param str camelCase字符串
   * @returns snake_case字符串
   */
  protected camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
