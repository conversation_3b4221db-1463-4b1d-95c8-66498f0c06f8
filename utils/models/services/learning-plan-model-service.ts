/**
 * 学习计划模型服务
 * 提供学习计划模型的服务功能
 */

import { BaseModelService } from './base-model-service';
import {
  LearningPlan,
  LearningPlanQueryParams,
  CreateLearningPlanRequest,
  UpdateLearningPlanRequest,
  LearningPlanStatus
} from '../interfaces/learning-plan';
import { LearningPlanTransformer } from '../transformers/learning-plan-transformer';
import { LearningPlanValidator } from '../validators/learning-plan-validator';
import { ValidationResult } from '../validators/base-validator';

/**
 * 学习计划模型服务类
 */
export class LearningPlanModelService extends BaseModelService<LearningPlan> {
  private learningPlanValidator: LearningPlanValidator;

  constructor() {
    const transformer = new LearningPlanTransformer();
    const validator = new LearningPlanValidator();
    super(transformer, validator);
    this.learningPlanValidator = validator;
  }

  /**
   * 验证创建学习计划请求
   * @param request 创建学习计划请求
   * @returns 验证结果
   */
  validateCreateRequest(request: CreateLearningPlanRequest): ValidationResult {
    return this.learningPlanValidator.validateCreateRequest(request);
  }

  /**
   * 验证更新学习计划请求
   * @param request 更新学习计划请求
   * @returns 验证结果
   */
  validateUpdateRequest(request: UpdateLearningPlanRequest): ValidationResult {
    return this.learningPlanValidator.validateUpdateRequest(request);
  }

  /**
   * 转换创建学习计划请求为API请求数据
   * @param request 创建学习计划请求
   * @returns API请求数据
   */
  createRequestToApiRequest(request: CreateLearningPlanRequest): any {
    return (this.transformer as LearningPlanTransformer).createRequestToApi(request);
  }

  /**
   * 转换更新学习计划请求为API请求数据
   * @param request 更新学习计划请求
   * @returns API请求数据
   */
  updateRequestToApiRequest(request: UpdateLearningPlanRequest): any {
    return (this.transformer as LearningPlanTransformer).updateRequestToApi(request);
  }

  /**
   * 转换学习计划查询参数为API查询参数
   * @param params 学习计划查询参数
   * @returns API查询参数
   */
  learningPlanQueryParamsToApiParams(params: LearningPlanQueryParams): any {
    const apiParams = this.toApiQueryParams(params);

    // 处理特定参数
    if (params.userId !== undefined) apiParams.user_id = params.userId;
    if (params.themeId !== undefined) apiParams.theme_id = params.themeId;
    if (params.templateId !== undefined) apiParams.template_id = params.templateId;
    if (params.status !== undefined) {
      apiParams.status = this.mapStatusToApi(params.status);
    }
    if (params.isCurrent !== undefined) apiParams.is_current = params.isCurrent;
    if (params.isPublic !== undefined) apiParams.is_public = params.isPublic;
    if (params.search !== undefined) apiParams.search = params.search;
    if (params.includeTags !== undefined) apiParams.include_tags = params.includeTags;
    if (params.includeTheme !== undefined) apiParams.include_theme = params.includeTheme;

    return apiParams;
  }

  /**
   * 将前端状态枚举映射为API状态值
   * @param status 前端状态枚举
   * @returns API状态值
   */
  private mapStatusToApi(status: LearningPlanStatus): string {
    switch (status) {
      case LearningPlanStatus.NotStarted:
        return 'not_started';
      case LearningPlanStatus.InProgress:
        return 'in_progress';
      case LearningPlanStatus.Completed:
        return 'completed';
      case LearningPlanStatus.Paused:
        return 'paused';
      case LearningPlanStatus.Abandoned:
        return 'abandoned';
      default:
        return 'not_started';
    }
  }
}
