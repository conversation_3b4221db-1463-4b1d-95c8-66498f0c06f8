/**
 * 主题模型服务
 * 提供主题模型的服务功能
 */

import { BaseModelService } from './base-model-service';
import { Theme, ThemeQueryParams, CreateThemeRequest, UpdateThemeRequest } from '../interfaces/theme';
import { ThemeTransformer } from '../transformers/theme-transformer';
import { ThemeValidator } from '../validators/theme-validator';
import { ValidationResult } from '../validators/base-validator';

/**
 * 主题模型服务类
 */
export class ThemeModelService extends BaseModelService<Theme> {
  private themeValidator: ThemeValidator;

  constructor() {
    const transformer = new ThemeTransformer();
    const validator = new ThemeValidator();
    super(transformer, validator);
    this.themeValidator = validator;
  }

  /**
   * 验证创建主题请求
   * @param request 创建主题请求
   * @returns 验证结果
   */
  validateCreateRequest(request: CreateThemeRequest): ValidationResult {
    return this.themeValidator.validateCreateRequest(request);
  }

  /**
   * 验证更新主题请求
   * @param request 更新主题请求
   * @returns 验证结果
   */
  validateUpdateRequest(request: UpdateThemeRequest): ValidationResult {
    return this.themeValidator.validateUpdateRequest(request);
  }

  /**
   * 转换创建主题请求为API请求数据
   * @param request 创建主题请求
   * @returns API请求数据
   */
  createRequestToApiRequest(request: CreateThemeRequest): any {
    return (this.transformer as ThemeTransformer).createRequestToApi(request);
  }

  /**
   * 转换更新主题请求为API请求数据
   * @param request 更新主题请求
   * @returns API请求数据
   */
  updateRequestToApiRequest(request: UpdateThemeRequest): any {
    return (this.transformer as ThemeTransformer).updateRequestToApi(request);
  }

  /**
   * 转换主题查询参数为API查询参数
   * @param params 主题查询参数
   * @returns API查询参数
   */
  themeQueryParamsToApiParams(params: ThemeQueryParams): any {
    const apiParams = this.toApiQueryParams(params);

    // 处理特定参数
    if (params.isActive !== undefined) apiParams.is_active = params.isActive;
    if (params.parentId !== undefined) apiParams.parent_id = params.parentId;
    if (params.search !== undefined) apiParams.search = params.search;

    return apiParams;
  }
}
