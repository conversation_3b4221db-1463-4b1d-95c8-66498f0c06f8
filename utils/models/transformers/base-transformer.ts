/**
 * 基础数据转换器
 * 提供数据转换的基础功能
 */

import { DataTransformer } from '../../api-client/data-transformer';

/**
 * 基础数据转换器类
 */
export abstract class BaseTransformer<T, R = any> {
  protected dataTransformer: DataTransformer;

  constructor() {
    this.dataTransformer = new DataTransformer();
  }

  /**
   * 将API响应数据转换为前端模型
   * @param data API响应数据
   * @returns 前端模型
   */
  abstract fromApi(data: R): T;

  /**
   * 将前端模型转换为API请求数据
   * @param model 前端模型
   * @returns API请求数据
   */
  abstract toApi(model: Partial<T>): R;

  /**
   * 将多个API响应数据转换为前端模型数组
   * @param dataArray API响应数据数组
   * @returns 前端模型数组
   */
  fromApiArray(dataArray: R[]): T[] {
    return dataArray.map(data => this.fromApi(data));
  }

  /**
   * 将日期字符串转换为ISO格式
   * @param date 日期字符串
   * @returns ISO格式日期字符串
   */
  protected formatDate(date: string | null | undefined): string | undefined {
    if (!date) return undefined;

    try {
      return new Date(date).toISOString();
    } catch (error) {
      console.error('日期格式转换错误:', error);
      return undefined;
    }
  }

  /**
   * 将ISO格式日期字符串转换为YYYY-MM-DD格式
   * @param isoDate ISO格式日期字符串
   * @returns YYYY-MM-DD格式日期字符串
   */
  protected formatDateToYYYYMMDD(isoDate: string | null | undefined): string | undefined {
    if (!isoDate) return undefined;

    try {
      const date = new Date(isoDate);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('日期格式转换错误:', error);
      return undefined;
    }
  }
}
