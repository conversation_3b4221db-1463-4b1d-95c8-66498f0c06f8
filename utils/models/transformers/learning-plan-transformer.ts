/**
 * 学习计划数据转换器
 * 提供学习计划数据的转换功能
 */

import { BaseTransformer } from './base-transformer';
import {
  LearningPlan,
  LearningPlanStatus,
  CreateLearningPlanRequest,
  UpdateLearningPlanRequest
} from '../interfaces/learning-plan';
import { ThemeTransformer } from './theme-transformer';
import { TagTransformer } from './tag-transformer';

/**
 * 学习计划数据转换器类
 */
export class LearningPlanTransformer extends BaseTransformer<LearningPlan, any> {
  private themeTransformer: ThemeTransformer;
  private tagTransformer: TagTransformer;

  constructor() {
    super();
    this.themeTransformer = new ThemeTransformer();
    this.tagTransformer = new TagTransformer();
  }

  /**
   * 将API响应数据转换为前端学习计划模型
   * @param data API响应数据
   * @returns 前端学习计划模型
   */
  fromApi(data: any): LearningPlan {
    const learningPlan: LearningPlan = {
      id: data.id,
      userId: data.user_id,
      templateId: data.template_id,
      themeId: data.theme_id,
      title: data.title,
      description: data.description,
      coverImageUrl: data.cover_image_url,
      targetDays: data.target_days || 7,
      completedDays: data.completed_days || 0,
      progress: data.progress || 0,
      dailyGoalExercises: data.daily_goal_exercises || 3,
      dailyGoalInsights: data.daily_goal_insights || 5,
      dailyGoalMinutes: data.daily_goal_minutes || 15,
      status: this.mapStatusFromApi(data.status),
      startDate: data.start_date,
      endDate: data.end_date,
      isCurrent: data.is_current !== undefined ? data.is_current : false,
      isSystemDefault: data.is_system_default !== undefined ? data.is_system_default : false,
      isPublic: data.is_public !== undefined ? data.is_public : false,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      deletedAt: data.deleted_at
    };

    // 处理关联数据
    if (data.theme) {
      learningPlan.theme = this.themeTransformer.fromApi(data.theme);
    }

    if (data.tags && Array.isArray(data.tags)) {
      learningPlan.tags = this.tagTransformer.fromApiArray(data.tags);
    }

    return learningPlan;
  }

  /**
   * 将前端学习计划模型转换为API请求数据
   * @param model 前端学习计划模型
   * @returns API请求数据
   */
  toApi(model: Partial<LearningPlan>): any {
    const apiData: any = {};

    if (model.userId !== undefined) apiData.user_id = model.userId;
    if (model.templateId !== undefined) apiData.template_id = model.templateId;
    if (model.themeId !== undefined) apiData.theme_id = model.themeId;
    if (model.title !== undefined) apiData.title = model.title;
    if (model.description !== undefined) apiData.description = model.description;
    if (model.coverImageUrl !== undefined) apiData.cover_image_url = model.coverImageUrl;
    if (model.targetDays !== undefined) apiData.target_days = model.targetDays;
    if (model.completedDays !== undefined) apiData.completed_days = model.completedDays;
    if (model.progress !== undefined) apiData.progress = model.progress;
    if (model.dailyGoalExercises !== undefined) apiData.daily_goal_exercises = model.dailyGoalExercises;
    if (model.dailyGoalInsights !== undefined) apiData.daily_goal_insights = model.dailyGoalInsights;
    if (model.dailyGoalMinutes !== undefined) apiData.daily_goal_minutes = model.dailyGoalMinutes;
    if (model.status !== undefined) apiData.status = this.mapStatusToApi(model.status);
    if (model.startDate !== undefined) apiData.start_date = model.startDate;
    if (model.endDate !== undefined) apiData.end_date = model.endDate;
    if (model.isCurrent !== undefined) apiData.is_current = model.isCurrent;
    if (model.isSystemDefault !== undefined) apiData.is_system_default = model.isSystemDefault;
    if (model.isPublic !== undefined) apiData.is_public = model.isPublic;

    return apiData;
  }

  /**
   * 将创建学习计划请求转换为API请求数据
   * @param request 创建学习计划请求
   * @returns API请求数据
   */
  createRequestToApi(request: CreateLearningPlanRequest): any {
    const apiData: any = {
      title: request.title,
      template_id: request.templateId,
      theme_id: request.themeId,
      description: request.description,
      cover_image_url: request.coverImageUrl,
      target_days: request.targetDays || 7,
      daily_goal_exercises: request.dailyGoalExercises || 3,
      daily_goal_insights: request.dailyGoalInsights || 5,
      daily_goal_minutes: request.dailyGoalMinutes || 15,
      start_date: request.startDate,
      is_public: request.isPublic !== undefined ? request.isPublic : false
    };

    if (request.tagIds && request.tagIds.length > 0) {
      apiData.tag_ids = request.tagIds;
    }

    return apiData;
  }

  /**
   * 将更新学习计划请求转换为API请求数据
   * @param request 更新学习计划请求
   * @returns API请求数据
   */
  updateRequestToApi(request: UpdateLearningPlanRequest): any {
    const apiData: any = {};

    if (request.title !== undefined) apiData.title = request.title;
    if (request.description !== undefined) apiData.description = request.description;
    if (request.coverImageUrl !== undefined) apiData.cover_image_url = request.coverImageUrl;
    if (request.targetDays !== undefined) apiData.target_days = request.targetDays;
    if (request.dailyGoalExercises !== undefined) apiData.daily_goal_exercises = request.dailyGoalExercises;
    if (request.dailyGoalInsights !== undefined) apiData.daily_goal_insights = request.dailyGoalInsights;
    if (request.dailyGoalMinutes !== undefined) apiData.daily_goal_minutes = request.dailyGoalMinutes;
    if (request.status !== undefined) apiData.status = this.mapStatusToApi(request.status);
    if (request.startDate !== undefined) apiData.start_date = request.startDate;
    if (request.endDate !== undefined) apiData.end_date = request.endDate;
    if (request.isCurrent !== undefined) apiData.is_current = request.isCurrent;
    if (request.isPublic !== undefined) apiData.is_public = request.isPublic;

    if (request.tagIds !== undefined) {
      apiData.tag_ids = request.tagIds;
    }

    return apiData;
  }

  /**
   * 将API状态值映射为前端状态枚举
   * @param status API状态值
   * @returns 前端状态枚举
   */
  private mapStatusFromApi(status: string): LearningPlanStatus {
    switch (status) {
      case 'not_started':
        return LearningPlanStatus.NotStarted;
      case 'in_progress':
        return LearningPlanStatus.InProgress;
      case 'completed':
        return LearningPlanStatus.Completed;
      case 'paused':
        return LearningPlanStatus.Paused;
      case 'abandoned':
        return LearningPlanStatus.Abandoned;
      default:
        return LearningPlanStatus.NotStarted;
    }
  }

  /**
   * 将前端状态枚举映射为API状态值
   * @param status 前端状态枚举
   * @returns API状态值
   */
  private mapStatusToApi(status: LearningPlanStatus): string {
    switch (status) {
      case LearningPlanStatus.NotStarted:
        return 'not_started';
      case LearningPlanStatus.InProgress:
        return 'in_progress';
      case LearningPlanStatus.Completed:
        return 'completed';
      case LearningPlanStatus.Paused:
        return 'paused';
      case LearningPlanStatus.Abandoned:
        return 'abandoned';
      default:
        return 'not_started';
    }
  }
}
