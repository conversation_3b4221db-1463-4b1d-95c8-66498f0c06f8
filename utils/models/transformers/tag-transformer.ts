/**
 * 标签数据转换器
 * 提供标签数据的转换功能
 */

import { BaseTransformer } from './base-transformer';
import { Tag, CreateTagRequest, UpdateTagRequest } from '../interfaces/tag';

/**
 * 标签数据转换器类
 */
export class TagTransformer extends BaseTransformer<Tag, any> {
  /**
   * 将API响应数据转换为前端标签模型
   * @param data API响应数据
   * @returns 前端标签模型
   */
  fromApi(data: any): Tag {
    const tag: Tag = {
      id: data.id,
      name: data.name,
      categoryId: data.category_id,
      creatorId: data.creator_id,
      description: data.description,
      relevanceScore: data.relevance_score !== undefined ? data.relevance_score : 1.0,
      weight: data.weight !== undefined ? data.weight : 1.0,
      usageCount: data.usage_count || 0,
      likeCount: data.like_count || 0,
      isVerified: data.is_verified !== undefined ? data.is_verified : false,
      isOfficial: data.is_official !== undefined ? data.is_official : true,
      sortOrder: data.sort_order || 0,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      deletedAt: data.deleted_at
    };

    // 处理同义词
    if (data.synonyms && Array.isArray(data.synonyms)) {
      tag.synonyms = data.synonyms.map((synonym: any) => synonym.name || synonym);
    }

    return tag;
  }

  /**
   * 将前端标签模型转换为API请求数据
   * @param model 前端标签模型
   * @returns API请求数据
   */
  toApi(model: Partial<Tag>): any {
    const apiData: any = {};

    if (model.name !== undefined) apiData.name = model.name;
    if (model.categoryId !== undefined) apiData.category_id = model.categoryId;
    if (model.description !== undefined) apiData.description = model.description;
    if (model.relevanceScore !== undefined) apiData.relevance_score = model.relevanceScore;
    if (model.weight !== undefined) apiData.weight = model.weight;
    if (model.isVerified !== undefined) apiData.is_verified = model.isVerified;
    if (model.isOfficial !== undefined) apiData.is_official = model.isOfficial;
    if (model.sortOrder !== undefined) apiData.sort_order = model.sortOrder;

    if (model.synonyms !== undefined) {
      apiData.synonyms = model.synonyms;
    }

    return apiData;
  }

  /**
   * 将创建标签请求转换为API请求数据
   * @param request 创建标签请求
   * @returns API请求数据
   */
  createRequestToApi(request: CreateTagRequest): any {
    return {
      name: request.name,
      category_id: request.categoryId,
      description: request.description,
      is_official: request.isOfficial !== undefined ? request.isOfficial : true,
      synonyms: request.synonyms
    };
  }

  /**
   * 将更新标签请求转换为API请求数据
   * @param request 更新标签请求
   * @returns API请求数据
   */
  updateRequestToApi(request: UpdateTagRequest): any {
    const apiData: any = {};

    if (request.name !== undefined) apiData.name = request.name;
    if (request.categoryId !== undefined) apiData.category_id = request.categoryId;
    if (request.description !== undefined) apiData.description = request.description;
    if (request.relevanceScore !== undefined) apiData.relevance_score = request.relevanceScore;
    if (request.weight !== undefined) apiData.weight = request.weight;
    if (request.isVerified !== undefined) apiData.is_verified = request.isVerified;
    if (request.isOfficial !== undefined) apiData.is_official = request.isOfficial;
    if (request.sortOrder !== undefined) apiData.sort_order = request.sortOrder;

    if (request.synonyms !== undefined) {
      apiData.synonyms = request.synonyms;
    }

    return apiData;
  }
}
