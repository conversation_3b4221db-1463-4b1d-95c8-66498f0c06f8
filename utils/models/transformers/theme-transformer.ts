/**
 * 主题数据转换器
 * 提供主题数据的转换功能
 */

import { BaseTransformer } from './base-transformer';
import { Theme, CreateThemeRequest, UpdateThemeRequest } from '../interfaces/theme';

/**
 * 主题数据转换器类
 */
export class ThemeTransformer extends BaseTransformer<Theme, any> {
  /**
   * 将API响应数据转换为前端主题模型
   * @param data API响应数据
   * @returns 前端主题模型
   */
  fromApi(data: any): Theme {
    return {
      id: data.id,
      name: data.name,
      englishName: data.english_name,
      description: data.description,
      icon: data.icon,
      color: data.color,
      coverImageUrl: data.cover_image_url,
      sortOrder: data.sort_order || 0,
      isActive: data.is_active !== undefined ? data.is_active : true,
      parentId: data.parent_id,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      deletedAt: data.deleted_at
    };
  }

  /**
   * 将前端主题模型转换为API请求数据
   * @param model 前端主题模型
   * @returns API请求数据
   */
  toApi(model: Partial<Theme>): any {
    return {
      name: model.name,
      english_name: model.englishName,
      description: model.description,
      icon: model.icon,
      color: model.color,
      cover_image_url: model.coverImageUrl,
      sort_order: model.sortOrder,
      is_active: model.isActive,
      parent_id: model.parentId
    };
  }

  /**
   * 将创建主题请求转换为API请求数据
   * @param request 创建主题请求
   * @returns API请求数据
   */
  createRequestToApi(request: CreateThemeRequest): any {
    return {
      name: request.name,
      english_name: request.englishName,
      description: request.description,
      icon: request.icon,
      color: request.color,
      cover_image_url: request.coverImageUrl,
      sort_order: request.sortOrder || 0,
      is_active: request.isActive !== undefined ? request.isActive : true,
      parent_id: request.parentId
    };
  }

  /**
   * 将更新主题请求转换为API请求数据
   * @param request 更新主题请求
   * @returns API请求数据
   */
  updateRequestToApi(request: UpdateThemeRequest): any {
    const apiData: any = {};

    if (request.name !== undefined) apiData.name = request.name;
    if (request.englishName !== undefined) apiData.english_name = request.englishName;
    if (request.description !== undefined) apiData.description = request.description;
    if (request.icon !== undefined) apiData.icon = request.icon;
    if (request.color !== undefined) apiData.color = request.color;
    if (request.coverImageUrl !== undefined) apiData.cover_image_url = request.coverImageUrl;
    if (request.sortOrder !== undefined) apiData.sort_order = request.sortOrder;
    if (request.isActive !== undefined) apiData.is_active = request.isActive;
    if (request.parentId !== undefined) apiData.parent_id = request.parentId;

    return apiData;
  }
}
