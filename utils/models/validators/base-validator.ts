/**
 * 基础数据验证器
 * 提供数据验证的基础功能
 */

/**
 * 验证错误接口
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * 基础数据验证器类
 */
export abstract class BaseValidator<T> {
  /**
   * 验证数据
   * @param data 要验证的数据
   * @returns 验证结果
   */
  abstract validate(data: Partial<T>): ValidationResult;

  /**
   * 创建验证结果
   * @param isValid 是否有效
   * @param errors 错误列表
   * @returns 验证结果
   */
  protected createResult(isValid: boolean, errors: ValidationError[] = []): ValidationResult {
    return {
      isValid,
      errors
    };
  }

  /**
   * 验证必填字段
   * @param data 数据对象
   * @param field 字段名
   * @param fieldLabel 字段标签
   * @returns 错误信息，如果没有错误则返回null
   */
  protected validateRequired(data: any, field: string, fieldLabel: string): ValidationError | null {
    if (data[field] === undefined || data[field] === null || data[field] === '') {
      return {
        field,
        message: `${fieldLabel}不能为空`
      };
    }
    return null;
  }

  /**
   * 验证字符串长度
   * @param data 数据对象
   * @param field 字段名
   * @param fieldLabel 字段标签
   * @param min 最小长度
   * @param max 最大长度
   * @returns 错误信息，如果没有错误则返回null
   */
  protected validateLength(
    data: any,
    field: string,
    fieldLabel: string,
    min: number,
    max: number
  ): ValidationError | null {
    if (data[field] !== undefined && data[field] !== null) {
      const value = String(data[field]);
      if (value.length < min || value.length > max) {
        return {
          field,
          message: `${fieldLabel}长度应在${min}到${max}个字符之间`
        };
      }
    }
    return null;
  }

  /**
   * 验证数字范围
   * @param data 数据对象
   * @param field 字段名
   * @param fieldLabel 字段标签
   * @param min 最小值
   * @param max 最大值
   * @returns 错误信息，如果没有错误则返回null
   */
  protected validateRange(
    data: any,
    field: string,
    fieldLabel: string,
    min: number,
    max: number
  ): ValidationError | null {
    if (data[field] !== undefined && data[field] !== null) {
      const value = Number(data[field]);
      if (isNaN(value) || value < min || value > max) {
        return {
          field,
          message: `${fieldLabel}应在${min}到${max}之间`
        };
      }
    }
    return null;
  }

  /**
   * 验证枚举值
   * @param data 数据对象
   * @param field 字段名
   * @param fieldLabel 字段标签
   * @param allowedValues 允许的值
   * @returns 错误信息，如果没有错误则返回null
   */
  protected validateEnum(
    data: any,
    field: string,
    fieldLabel: string,
    allowedValues: any[]
  ): ValidationError | null {
    if (data[field] !== undefined && data[field] !== null) {
      if (!allowedValues.includes(data[field])) {
        return {
          field,
          message: `${fieldLabel}的值无效，允许的值为: ${allowedValues.join(', ')}`
        };
      }
    }
    return null;
  }

  /**
   * 验证日期格式
   * @param data 数据对象
   * @param field 字段名
   * @param fieldLabel 字段标签
   * @returns 错误信息，如果没有错误则返回null
   */
  protected validateDate(data: any, field: string, fieldLabel: string): ValidationError | null {
    if (data[field] !== undefined && data[field] !== null) {
      const date = new Date(data[field]);
      if (isNaN(date.getTime())) {
        return {
          field,
          message: `${fieldLabel}的日期格式无效`
        };
      }
    }
    return null;
  }

  /**
   * 验证正则表达式
   * @param data 数据对象
   * @param field 字段名
   * @param fieldLabel 字段标签
   * @param pattern 正则表达式
   * @param message 错误消息
   * @returns 错误信息，如果没有错误则返回null
   */
  protected validatePattern(
    data: any,
    field: string,
    fieldLabel: string,
    pattern: RegExp,
    message?: string
  ): ValidationError | null {
    if (data[field] !== undefined && data[field] !== null) {
      const value = String(data[field]);
      if (!pattern.test(value)) {
        return {
          field,
          message: message || `${fieldLabel}格式无效`
        };
      }
    }
    return null;
  }
}
