/**
 * 学习计划数据验证器
 * 提供学习计划数据的验证功能
 */

import { BaseValidator, ValidationError, ValidationResult } from './base-validator';
import {
  LearningPlan,
  LearningPlanStatus,
  CreateLearningPlanRequest,
  UpdateLearningPlanRequest
} from '../interfaces/learning-plan';

/**
 * 学习计划数据验证器类
 */
export class LearningPlanValidator extends BaseValidator<LearningPlan> {
  /**
   * 验证学习计划数据
   * @param data 要验证的学习计划数据
   * @returns 验证结果
   */
  validate(data: Partial<LearningPlan>): ValidationResult {
    const errors: ValidationError[] = [];

    // 验证标题
    if (data.title !== undefined) {
      const titleError = this.validateLength(data, 'title', '计划标题', 1, 100);
      if (titleError) errors.push(titleError);
    }

    // 验证目标天数
    if (data.targetDays !== undefined) {
      const targetDaysError = this.validateRange(data, 'targetDays', '目标天数', 1, 365);
      if (targetDaysError) errors.push(targetDaysError);
    }

    // 验证每日目标
    if (data.dailyGoalExercises !== undefined) {
      const dailyGoalExercisesError = this.validateRange(data, 'dailyGoalExercises', '每日练习目标', 0, 20);
      if (dailyGoalExercisesError) errors.push(dailyGoalExercisesError);
    }

    if (data.dailyGoalInsights !== undefined) {
      const dailyGoalInsightsError = this.validateRange(data, 'dailyGoalInsights', '每日观点目标', 0, 30);
      if (dailyGoalInsightsError) errors.push(dailyGoalInsightsError);
    }

    if (data.dailyGoalMinutes !== undefined) {
      const dailyGoalMinutesError = this.validateRange(data, 'dailyGoalMinutes', '每日学习时间目标', 0, 240);
      if (dailyGoalMinutesError) errors.push(dailyGoalMinutesError);
    }

    // 验证状态
    if (data.status !== undefined) {
      const statusError = this.validateEnum(
        data,
        'status',
        '计划状态',
        Object.values(LearningPlanStatus)
      );
      if (statusError) errors.push(statusError);
    }

    // 验证日期
    if (data.startDate !== undefined) {
      const startDateError = this.validateDate(data, 'startDate', '开始日期');
      if (startDateError) errors.push(startDateError);
    }

    if (data.endDate !== undefined) {
      const endDateError = this.validateDate(data, 'endDate', '结束日期');
      if (endDateError) errors.push(endDateError);
    }

    // 验证开始日期和结束日期的关系
    if (data.startDate && data.endDate) {
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);

      if (startDate > endDate) {
        errors.push({
          field: 'endDate',
          message: '结束日期不能早于开始日期'
        });
      }
    }

    return this.createResult(errors.length === 0, errors);
  }

  /**
   * 验证创建学习计划请求
   * @param request 创建学习计划请求
   * @returns 验证结果
   */
  validateCreateRequest(request: CreateLearningPlanRequest): ValidationResult {
    const errors: ValidationError[] = [];

    // 验证必填字段
    const titleRequiredError = this.validateRequired(request, 'title', '计划标题');
    if (titleRequiredError) errors.push(titleRequiredError);

    // 验证其他字段
    const planData: Partial<LearningPlan> = {
      title: request.title,
      description: request.description,
      coverImageUrl: request.coverImageUrl,
      targetDays: request.targetDays,
      dailyGoalExercises: request.dailyGoalExercises,
      dailyGoalInsights: request.dailyGoalInsights,
      dailyGoalMinutes: request.dailyGoalMinutes,
      startDate: request.startDate
    };

    const planValidationResult = this.validate(planData);
    errors.push(...planValidationResult.errors);

    return this.createResult(errors.length === 0, errors);
  }

  /**
   * 验证更新学习计划请求
   * @param request 更新学习计划请求
   * @returns 验证结果
   */
  validateUpdateRequest(request: UpdateLearningPlanRequest): ValidationResult {
    const planData: Partial<LearningPlan> = {
      title: request.title,
      description: request.description,
      coverImageUrl: request.coverImageUrl,
      targetDays: request.targetDays,
      dailyGoalExercises: request.dailyGoalExercises,
      dailyGoalInsights: request.dailyGoalInsights,
      dailyGoalMinutes: request.dailyGoalMinutes,
      status: request.status,
      startDate: request.startDate,
      endDate: request.endDate
    };

    return this.validate(planData);
  }
}
