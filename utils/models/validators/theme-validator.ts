/**
 * 主题数据验证器
 * 提供主题数据的验证功能
 */

import { BaseValidator, ValidationError, ValidationResult } from './base-validator';
import { Theme, CreateThemeRequest, UpdateThemeRequest } from '../interfaces/theme';

/**
 * 主题数据验证器类
 */
export class ThemeValidator extends BaseValidator<Theme> {
  /**
   * 验证主题数据
   * @param data 要验证的主题数据
   * @returns 验证结果
   */
  validate(data: Partial<Theme>): ValidationResult {
    const errors: ValidationError[] = [];

    // 验证名称
    if (data.name !== undefined) {
      const nameError = this.validateLength(data, 'name', '主题名称', 1, 50);
      if (nameError) errors.push(nameError);
    }

    // 验证英文名称
    if (data.englishName !== undefined) {
      const englishNameError = this.validateLength(data, 'englishName', '主题英文名称', 0, 50);
      if (englishNameError) errors.push(englishNameError);
    }

    // 验证颜色
    if (data.color !== undefined && data.color !== null) {
      const colorError = this.validatePattern(
        data,
        'color',
        '主题颜色',
        /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
        '主题颜色应为有效的十六进制颜色代码'
      );
      if (colorError) errors.push(colorError);
    }

    // 验证排序顺序
    if (data.sortOrder !== undefined) {
      const sortOrderError = this.validateRange(data, 'sortOrder', '排序顺序', 0, 1000);
      if (sortOrderError) errors.push(sortOrderError);
    }

    return this.createResult(errors.length === 0, errors);
  }

  /**
   * 验证创建主题请求
   * @param request 创建主题请求
   * @returns 验证结果
   */
  validateCreateRequest(request: CreateThemeRequest): ValidationResult {
    const errors: ValidationError[] = [];

    // 验证必填字段
    const nameRequiredError = this.validateRequired(request, 'name', '主题名称');
    if (nameRequiredError) errors.push(nameRequiredError);

    // 验证其他字段
    const themeData: Partial<Theme> = {
      name: request.name,
      englishName: request.englishName,
      description: request.description,
      icon: request.icon,
      color: request.color,
      coverImageUrl: request.coverImageUrl,
      sortOrder: request.sortOrder,
      isActive: request.isActive,
      parentId: request.parentId
    };

    const themeValidationResult = this.validate(themeData);
    errors.push(...themeValidationResult.errors);

    return this.createResult(errors.length === 0, errors);
  }

  /**
   * 验证更新主题请求
   * @param request 更新主题请求
   * @returns 验证结果
   */
  validateUpdateRequest(request: UpdateThemeRequest): ValidationResult {
    const themeData: Partial<Theme> = {
      name: request.name,
      englishName: request.englishName,
      description: request.description,
      icon: request.icon,
      color: request.color,
      coverImageUrl: request.coverImageUrl,
      sortOrder: request.sortOrder,
      isActive: request.isActive,
      parentId: request.parentId
    };

    return this.validate(themeData);
  }
}
