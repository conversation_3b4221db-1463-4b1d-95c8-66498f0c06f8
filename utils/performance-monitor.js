// utils/performance-monitor.js
// 性能监控工具

/**
 * 性能监控类
 * 用于监控组件和页面的性能
 */
class PerformanceMonitor {
  /**
   * 构造函数
   */
  constructor() {
    // 性能数据
    this.metrics = {
      fps: {
        current: 0,
        min: Infinity,
        max: 0,
        avg: 0,
        samples: []
      },
      frameTime: {
        current: 0,
        min: Infinity,
        max: 0,
        avg: 0,
        samples: []
      },
      memory: {
        current: 0,
        min: Infinity,
        max: 0,
        avg: 0,
        samples: []
      },
      scroll: {
        count: 0,
        fpsAvg: 0,
        fpsSamples: []
      }
    };

    // 帧计数器
    this.frameCount = 0;
    this.lastFrameTime = 0;
    this.frameTimeSum = 0;

    // 采样配置
    this.maxSamples = 60; // 保存最近60帧的数据
    this.sampleInterval = 1000; // 每秒采样一次

    // 状态
    this.isMonitoring = false;
    this.monitorTimer = null;
    this.lastSampleTime = 0;
    this.startTime = 0;
  }

  /**
   * 开始监控
   */
  start() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.lastFrameTime = Date.now();
    this.lastSampleTime = this.lastFrameTime;
    this.startTime = this.lastFrameTime;
    this.frameCount = 0;
    this.frameTimeSum = 0;

    // 定期采样
    this.monitorTimer = setInterval(() => {
      this._sampleMetrics();
    }, this.sampleInterval);

    console.log('性能监控已启动');
  }

  /**
   * 停止监控
   */
  stop() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.monitorTimer = null;
    }

    console.log('性能监控已停止');
  }

  /**
   * 记录帧
   */
  recordFrame() {
    if (!this.isMonitoring) return;

    const now = Date.now();
    const frameTime = now - this.lastFrameTime;
    this.lastFrameTime = now;

    // 更新帧时间
    this.metrics.frameTime.current = frameTime;
    this.metrics.frameTime.min = Math.min(this.metrics.frameTime.min, frameTime);
    this.metrics.frameTime.max = Math.max(this.metrics.frameTime.max, frameTime);

    // 累计帧时间和帧数
    this.frameTimeSum += frameTime;
    this.frameCount++;
  }

  /**
   * 采样性能指标
   * @private
   */
  _sampleMetrics() {
    if (!this.isMonitoring || this.frameCount === 0) return;

    const now = Date.now();
    const elapsed = now - this.lastSampleTime;
    this.lastSampleTime = now;

    // 计算FPS
    const fps = Math.round((this.frameCount * 1000) / elapsed);
    this.metrics.fps.current = fps;
    this.metrics.fps.min = Math.min(this.metrics.fps.min, fps);
    this.metrics.fps.max = Math.max(this.metrics.fps.max, fps);

    // 计算平均帧时间
    const avgFrameTime = this.frameTimeSum / this.frameCount;
    this.metrics.frameTime.avg = avgFrameTime;

    // 尝试获取内存使用情况（如果可用）
    try {
      if (wx && wx.getPerformance) {
        const performance = wx.getPerformance();
        const memory = performance.memory;
        if (memory) {
          const memoryUsage = memory.jsHeapSizeLimit ? (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100 : 0;
          this.metrics.memory.current = memoryUsage;
          this.metrics.memory.min = Math.min(this.metrics.memory.min, memoryUsage);
          this.metrics.memory.max = Math.max(this.metrics.memory.max, memoryUsage);

          // 更新内存使用样本
          this._updateSamples(this.metrics.memory.samples, memoryUsage);
          this.metrics.memory.avg = this._calculateAverage(this.metrics.memory.samples);
        }
      }
    } catch (err) {
      // 内存信息不可用，忽略错误
    }

    // 更新FPS样本
    this._updateSamples(this.metrics.fps.samples, fps);
    this.metrics.fps.avg = this._calculateAverage(this.metrics.fps.samples);

    // 更新帧时间样本
    this._updateSamples(this.metrics.frameTime.samples, avgFrameTime);
    this.metrics.frameTime.avg = this._calculateAverage(this.metrics.frameTime.samples);

    // 重置计数器
    this.frameCount = 0;
    this.frameTimeSum = 0;
  }

  /**
   * 更新样本数组
   * @param {Array} samples - 样本数组
   * @param {number} value - 新样本值
   * @private
   */
  _updateSamples(samples, value) {
    samples.push(value);
    if (samples.length > this.maxSamples) {
      samples.shift();
    }
  }

  /**
   * 计算平均值
   * @param {Array} samples - 样本数组
   * @returns {number} 平均值
   * @private
   */
  _calculateAverage(samples) {
    if (samples.length === 0) return 0;
    const sum = samples.reduce((a, b) => a + b, 0);
    return sum / samples.length;
  }

  /**
   * 记录滚动性能
   */
  recordScroll() {
    if (!this.isMonitoring) return;

    // 增加滚动计数
    this.metrics.scroll.count++;

    // 记录滚动时的帧率
    this.metrics.scroll.fpsSamples.push(this.metrics.fps.current);
    if (this.metrics.scroll.fpsSamples.length > this.maxSamples) {
      this.metrics.scroll.fpsSamples.shift();
    }

    // 计算滚动时的平均帧率
    if (this.metrics.scroll.fpsSamples.length > 0) {
      this.metrics.scroll.fpsAvg = this._calculateAverage(this.metrics.scroll.fpsSamples);
    }
  }

  /**
   * 获取性能指标
   * @returns {Object} 性能指标
   */
  getMetrics() {
    return {
      fps: {
        current: this.metrics.fps.current,
        min: this.metrics.fps.min === Infinity ? 0 : this.metrics.fps.min,
        max: this.metrics.fps.max,
        avg: this.metrics.fps.avg
      },
      frameTime: {
        current: this.metrics.frameTime.current,
        min: this.metrics.frameTime.min === Infinity ? 0 : this.metrics.frameTime.min,
        max: this.metrics.frameTime.max,
        avg: this.metrics.frameTime.avg
      },
      memory: {
        current: this.metrics.memory.current,
        min: this.metrics.memory.min === Infinity ? 0 : this.metrics.memory.min,
        max: this.metrics.memory.max,
        avg: this.metrics.memory.avg
      },
      scroll: {
        count: this.metrics.scroll.count,
        fpsAvg: this.metrics.scroll.fpsAvg
      },
      runtime: Date.now() - this.startTime
    };
  }

  /**
   * 重置性能指标
   */
  reset() {
    this.metrics = {
      fps: {
        current: 0,
        min: Infinity,
        max: 0,
        avg: 0,
        samples: []
      },
      frameTime: {
        current: 0,
        min: Infinity,
        max: 0,
        avg: 0,
        samples: []
      },
      memory: {
        current: 0,
        min: Infinity,
        max: 0,
        avg: 0,
        samples: []
      },
      scroll: {
        count: 0,
        fpsAvg: 0,
        fpsSamples: []
      }
    };

    this.frameCount = 0;
    this.frameTimeSum = 0;
    this.lastFrameTime = Date.now();
    this.lastSampleTime = this.lastFrameTime;
    this.startTime = this.lastFrameTime;

    console.log('性能指标已重置');
  }

  /**
   * 重置性能指标（兼容旧API）
   * @deprecated 使用 reset() 代替
   */
  resetMetrics() {
    this.reset();
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
