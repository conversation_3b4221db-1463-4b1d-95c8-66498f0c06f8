/**
 * 性能监控工具类
 * 提供FPS监控、内存监控、渲染性能监控等功能
 */

class PerformanceMonitor {
  constructor() {
    this.isMonitoring = false;
    this.metrics = {
      fps: {
        current: 0,
        average: 0,
        min: Infinity,
        max: 0,
        samples: []
      },
      memory: {
        used: 0,
        total: 0,
        peak: 0
      },
      render: {
        setDataCount: 0,
        setDataTime: 0,
        averageSetDataTime: 0
      },
      network: {
        requestCount: 0,
        totalTime: 0,
        averageTime: 0,
        errorCount: 0
      }
    };

    this.frameCount = 0;
    this.lastFrameTime = 0;
    this.startTime = 0;
    this.sampleSize = 60; // 保留最近60帧的数据

    // 性能阈值
    this.thresholds = {
      fps: {
        good: 55,
        warning: 45,
        poor: 30
      },
      memory: {
        warning: 100 * 1024 * 1024, // 100MB
        critical: 200 * 1024 * 1024 // 200MB
      },
      setData: {
        warning: 16, // 16ms
        critical: 33 // 33ms
      }
    };

    // 监听器
    this.listeners = {
      fps: [],
      memory: [],
      render: [],
      network: []
    };
  }

  /**
   * 开始监控
   */
  start() {
    if (this.isMonitoring) {
      console.warn('性能监控已经在运行中');
      return;
    }

    this.isMonitoring = true;
    this.startTime = Date.now();
    this.lastFrameTime = performance.now();

    // 重置指标
    this._resetMetrics();

    // 开始FPS监控
    this._startFPSMonitoring();

    // 开始内存监控
    this._startMemoryMonitoring();

    console.log('性能监控已启动');
  }

  /**
   * 停止监控
   */
  stop() {
    if (!this.isMonitoring) {
      console.warn('性能监控未在运行');
      return;
    }

    this.isMonitoring = false;

    // 停止定时器
    if (this.fpsTimer) {
      clearInterval(this.fpsTimer);
      this.fpsTimer = null;
    }

    if (this.memoryTimer) {
      clearInterval(this.memoryTimer);
      this.memoryTimer = null;
    }

    console.log('性能监控已停止');
  }

  /**
   * 重置指标
   */
  _resetMetrics() {
    this.metrics.fps = {
      current: 0,
      average: 0,
      min: Infinity,
      max: 0,
      samples: []
    };

    this.metrics.memory = {
      used: 0,
      total: 0,
      peak: 0
    };

    this.metrics.render = {
      setDataCount: 0,
      setDataTime: 0,
      averageSetDataTime: 0
    };

    this.metrics.network = {
      requestCount: 0,
      totalTime: 0,
      averageTime: 0,
      errorCount: 0
    };

    this.frameCount = 0;
  }

  /**
   * 开始FPS监控
   */
  _startFPSMonitoring() {
    this.fpsTimer = setInterval(() => {
      if (!this.isMonitoring) return;

      const currentTime = performance.now();
      const deltaTime = currentTime - this.lastFrameTime;

      if (deltaTime > 0) {
        const fps = 1000 / deltaTime;
        this._updateFPSMetrics(fps);
      }

      this.lastFrameTime = currentTime;
      this.frameCount++;
    }, 16); // 约60FPS的间隔
  }

  /**
   * 更新FPS指标
   */
  _updateFPSMetrics(fps) {
    const { metrics } = this;

    metrics.fps.current = Math.round(fps);
    metrics.fps.samples.push(fps);

    // 保持样本数量
    if (metrics.fps.samples.length > this.sampleSize) {
      metrics.fps.samples.shift();
    }

    // 计算统计值
    metrics.fps.min = Math.min(metrics.fps.min, fps);
    metrics.fps.max = Math.max(metrics.fps.max, fps);
    metrics.fps.average = Math.round(
      metrics.fps.samples.reduce((sum, val) => sum + val, 0) / metrics.fps.samples.length
    );

    // 触发监听器
    this._notifyListeners('fps', metrics.fps);
  }

  /**
   * 开始内存监控
   */
  _startMemoryMonitoring() {
    this.memoryTimer = setInterval(() => {
      if (!this.isMonitoring) return;

      try {
        // 尝试获取内存信息
        if (wx.getPerformance && wx.getPerformance().memory) {
          const memory = wx.getPerformance().memory;
          this._updateMemoryMetrics(memory);
        } else {
          // 降级方案：估算内存使用
          this._estimateMemoryUsage();
        }
      } catch (err) {
        console.error('获取内存信息失败:', err);
      }
    }, 1000); // 每秒检查一次
  }

  /**
   * 更新内存指标
   */
  _updateMemoryMetrics(memory) {
    const { metrics } = this;

    metrics.memory.used = memory.usedJSHeapSize || 0;
    metrics.memory.total = memory.totalJSHeapSize || 0;
    metrics.memory.peak = Math.max(metrics.memory.peak, metrics.memory.used);

    // 触发监听器
    this._notifyListeners('memory', metrics.memory);

    // 检查内存警告
    this._checkMemoryWarnings();
  }

  /**
   * 估算内存使用（降级方案）
   */
  _estimateMemoryUsage() {
    // 简单的内存使用估算
    const estimatedUsage = this.frameCount * 1024; // 粗略估算

    this._updateMemoryMetrics({
      usedJSHeapSize: estimatedUsage,
      totalJSHeapSize: estimatedUsage * 2
    });
  }

  /**
   * 检查内存警告
   */
  _checkMemoryWarnings() {
    const { used } = this.metrics.memory;
    const { warning, critical } = this.thresholds.memory;

    if (used > critical) {
      console.error(`内存使用过高: ${(used / 1024 / 1024).toFixed(2)}MB`);
    } else if (used > warning) {
      console.warn(`内存使用警告: ${(used / 1024 / 1024).toFixed(2)}MB`);
    }
  }

  /**
   * 记录setData性能
   */
  recordSetData(startTime, endTime) {
    if (!this.isMonitoring) return;

    const duration = endTime - startTime;
    const { metrics } = this;

    metrics.render.setDataCount++;
    metrics.render.setDataTime += duration;
    metrics.render.averageSetDataTime = metrics.render.setDataTime / metrics.render.setDataCount;

    // 检查setData性能警告
    this._checkSetDataWarnings(duration);

    // 触发监听器
    this._notifyListeners('render', metrics.render);
  }

  /**
   * 检查setData性能警告
   */
  _checkSetDataWarnings(duration) {
    const { warning, critical } = this.thresholds.setData;

    if (duration > critical) {
      console.error(`setData耗时过长: ${duration.toFixed(2)}ms`);
    } else if (duration > warning) {
      console.warn(`setData耗时警告: ${duration.toFixed(2)}ms`);
    }
  }

  /**
   * 记录网络请求性能
   */
  recordNetworkRequest(duration, success = true) {
    if (!this.isMonitoring) return;

    const { metrics } = this;

    metrics.network.requestCount++;

    if (success) {
      metrics.network.totalTime += duration;
      metrics.network.averageTime = metrics.network.totalTime / metrics.network.requestCount;
    } else {
      metrics.network.errorCount++;
    }

    // 触发监听器
    this._notifyListeners('network', metrics.network);
  }

  /**
   * 添加监听器
   */
  addListener(type, callback) {
    if (this.listeners[type]) {
      this.listeners[type].push(callback);
    }
  }

  /**
   * 移除监听器
   */
  removeListener(type, callback) {
    if (this.listeners[type]) {
      const index = this.listeners[type].indexOf(callback);
      if (index > -1) {
        this.listeners[type].splice(index, 1);
      }
    }
  }

  /**
   * 通知监听器
   */
  _notifyListeners(type, data) {
    if (this.listeners[type]) {
      this.listeners[type].forEach(callback => {
        try {
          callback(data);
        } catch (err) {
          console.error('监听器执行失败:', err);
        }
      });
    }
  }

  /**
   * 获取当前指标
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * 获取性能评分
   */
  getPerformanceScore() {
    const { fps, memory, render } = this.metrics;
    const { thresholds } = this;

    let score = 100;

    // FPS评分 (40%)
    if (fps.average < thresholds.fps.poor) {
      score -= 40;
    } else if (fps.average < thresholds.fps.warning) {
      score -= 20;
    } else if (fps.average < thresholds.fps.good) {
      score -= 10;
    }

    // 内存评分 (30%)
    if (memory.used > thresholds.memory.critical) {
      score -= 30;
    } else if (memory.used > thresholds.memory.warning) {
      score -= 15;
    }

    // 渲染性能评分 (30%)
    if (render.averageSetDataTime > thresholds.setData.critical) {
      score -= 30;
    } else if (render.averageSetDataTime > thresholds.setData.warning) {
      score -= 15;
    }

    return Math.max(0, Math.round(score));
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const metrics = this.getMetrics();
    const score = this.getPerformanceScore();
    const duration = Date.now() - this.startTime;

    return {
      score,
      duration,
      metrics,
      timestamp: new Date().toISOString(),
      recommendations: this._generateRecommendations()
    };
  }

  /**
   * 生成优化建议
   */
  _generateRecommendations() {
    const recommendations = [];
    const { fps, memory, render } = this.metrics;
    const { thresholds } = this;

    if (fps.average < thresholds.fps.warning) {
      recommendations.push('FPS过低，建议优化动画和渲染逻辑');
    }

    if (memory.used > thresholds.memory.warning) {
      recommendations.push('内存使用过高，建议检查内存泄漏');
    }

    if (render.averageSetDataTime > thresholds.setData.warning) {
      recommendations.push('setData耗时过长，建议减少数据更新频率');
    }

    return recommendations;
  }
}

// 创建单例实例
const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;
