/**
 * 权限检查中间件
 * 用于在页面加载时检查权限
 */

// 导入权限服务
const permissionService = require('./permission-service');

/**
 * 创建页面权限检查中间件
 * @param {Object} options - 配置选项
 * @param {string|Array<string>} options.permission - 需要的权限
 * @param {string|Array<string>} options.role - 需要的角色
 * @param {boolean} options.requireAll - 是否需要满足所有权限/角色
 * @param {string} options.redirectUrl - 无权限时重定向的页面
 * @param {Function} options.onDenied - 无权限时的回调函数
 * @returns {Function} 页面中间件函数
 */
function createPermissionMiddleware(options = {}) {
  return function (pageOptions) {
    // 保存原始的onLoad函数
    const originalOnLoad = pageOptions.onLoad;

    // 重写onLoad函数
    pageOptions.onLoad = async function (query) {
      // 设置页面加载中状态
      if (typeof this.setData === 'function') {
        this.setData({ isCheckingPermission: true });
      }

      try {
        let hasPermission = false;

        // 如果指定了权限，检查权限
        if (options.permission) {
          hasPermission = await permissionService.hasPermission(
            options.permission,
            options.requireAll
          );
        } else if (options.role) { // 如果指定了角色，检查角色
          hasPermission = await permissionService.hasRole(
            options.role,
            options.requireAll
          );
        } else { // 如果既没有指定权限也没有指定角色，默认为有权限
          hasPermission = true;
        }

        // 更新页面状态
        if (typeof this.setData === 'function') {
          this.setData({ isCheckingPermission: false, hasPermission });
        }

        // 如果有权限，调用原始的onLoad函数
        if (hasPermission) {
          if (typeof originalOnLoad === 'function') {
            originalOnLoad.call(this, query);
          }
        } else { // 如果没有权限，执行相应的操作
          // 如果指定了无权限回调，调用回调
          if (typeof options.onDenied === 'function') {
            options.onDenied.call(this, query);
          } else if (options.redirectUrl) { // 如果指定了重定向URL，进行重定向
            wx.redirectTo({
              url: options.redirectUrl,
              fail: () => {
                // 如果重定向失败，尝试切换到tabBar页面
                wx.switchTab({
                  url: options.redirectUrl,
                  fail: err => {
                    console.error('权限重定向失败:', err);
                    // 显示无权限提示
                    wx.showToast({
                      title: '您没有权限访问此页面',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                });
              }
            });
          } else { // 默认显示无权限提示
            wx.showToast({
              title: '您没有权限访问此页面',
              icon: 'none',
              duration: 2000
            });
            // 返回上一页
            setTimeout(() => {
              wx.navigateBack({
                fail: () => {
                  // 如果无法返回上一页，跳转到首页
                  wx.switchTab({
                    url: '/pages/index/index'
                  });
                }
              });
            }, 1500);
          }
        }
      } catch (error) {
        console.error('权限检查失败:', error);

        // 更新页面状态
        if (typeof this.setData === 'function') {
          this.setData({ isCheckingPermission: false, hasPermission: false });
        }

        // 出错时默认调用原始的onLoad函数
        if (typeof originalOnLoad === 'function') {
          originalOnLoad.call(this, query);
        }
      }
    };

    return pageOptions;
  };
}

module.exports = {
  createPermissionMiddleware
};
