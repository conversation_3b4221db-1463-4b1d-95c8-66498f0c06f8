/**
 * 权限控制服务
 * 提供基于角色的权限控制功能
 */

// 令牌管理器不直接使用，通过认证服务间接使用
// 导入认证服务
const authService = require('./auth-service');

// 权限常量
const PERMISSIONS = {
  // 内容权限
  CONTENT: {
    VIEW: 'content:view',
    CREATE: 'content:create',
    EDIT: 'content:edit',
    DELETE: 'content:delete',
    SHARE: 'content:share'
  },
  // 用户权限
  USER: {
    VIEW_PROFILE: 'user:view_profile',
    EDIT_PROFILE: 'user:edit_profile',
    MANAGE_USERS: 'user:manage_users'
  },
  // 学习计划权限
  LEARNING_PLAN: {
    VIEW: 'learning_plan:view',
    CREATE: 'learning_plan:create',
    EDIT: 'learning_plan:edit',
    DELETE: 'learning_plan:delete',
    SHARE: 'learning_plan:share'
  },
  // 管理员权限
  ADMIN: {
    ACCESS_ADMIN: 'admin:access',
    MANAGE_CONTENT: 'admin:manage_content',
    MANAGE_SETTINGS: 'admin:manage_settings'
  }
};

// 角色常量
const ROLES = {
  GUEST: 'guest',
  USER: 'user',
  PREMIUM_USER: 'premium_user',
  CONTENT_CREATOR: 'content_creator',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin'
};

// 角色权限映射
const ROLE_PERMISSIONS = {
  [ROLES.GUEST]: [
    PERMISSIONS.CONTENT.VIEW
  ],
  [ROLES.USER]: [
    PERMISSIONS.CONTENT.VIEW,
    PERMISSIONS.CONTENT.SHARE,
    PERMISSIONS.USER.VIEW_PROFILE,
    PERMISSIONS.USER.EDIT_PROFILE,
    PERMISSIONS.LEARNING_PLAN.VIEW,
    PERMISSIONS.LEARNING_PLAN.CREATE,
    PERMISSIONS.LEARNING_PLAN.EDIT,
    PERMISSIONS.LEARNING_PLAN.DELETE
  ],
  [ROLES.PREMIUM_USER]: [
    PERMISSIONS.CONTENT.VIEW,
    PERMISSIONS.CONTENT.SHARE,
    PERMISSIONS.USER.VIEW_PROFILE,
    PERMISSIONS.USER.EDIT_PROFILE,
    PERMISSIONS.LEARNING_PLAN.VIEW,
    PERMISSIONS.LEARNING_PLAN.CREATE,
    PERMISSIONS.LEARNING_PLAN.EDIT,
    PERMISSIONS.LEARNING_PLAN.DELETE,
    PERMISSIONS.LEARNING_PLAN.SHARE
  ],
  [ROLES.CONTENT_CREATOR]: [
    PERMISSIONS.CONTENT.VIEW,
    PERMISSIONS.CONTENT.CREATE,
    PERMISSIONS.CONTENT.EDIT,
    PERMISSIONS.CONTENT.DELETE,
    PERMISSIONS.CONTENT.SHARE,
    PERMISSIONS.USER.VIEW_PROFILE,
    PERMISSIONS.USER.EDIT_PROFILE,
    PERMISSIONS.LEARNING_PLAN.VIEW,
    PERMISSIONS.LEARNING_PLAN.CREATE,
    PERMISSIONS.LEARNING_PLAN.EDIT,
    PERMISSIONS.LEARNING_PLAN.DELETE,
    PERMISSIONS.LEARNING_PLAN.SHARE
  ],
  [ROLES.ADMIN]: [
    PERMISSIONS.CONTENT.VIEW,
    PERMISSIONS.CONTENT.CREATE,
    PERMISSIONS.CONTENT.EDIT,
    PERMISSIONS.CONTENT.DELETE,
    PERMISSIONS.CONTENT.SHARE,
    PERMISSIONS.USER.VIEW_PROFILE,
    PERMISSIONS.USER.EDIT_PROFILE,
    PERMISSIONS.USER.MANAGE_USERS,
    PERMISSIONS.LEARNING_PLAN.VIEW,
    PERMISSIONS.LEARNING_PLAN.CREATE,
    PERMISSIONS.LEARNING_PLAN.EDIT,
    PERMISSIONS.LEARNING_PLAN.DELETE,
    PERMISSIONS.LEARNING_PLAN.SHARE,
    PERMISSIONS.ADMIN.ACCESS_ADMIN,
    PERMISSIONS.ADMIN.MANAGE_CONTENT
  ],
  [ROLES.SUPER_ADMIN]: [
    PERMISSIONS.CONTENT.VIEW,
    PERMISSIONS.CONTENT.CREATE,
    PERMISSIONS.CONTENT.EDIT,
    PERMISSIONS.CONTENT.DELETE,
    PERMISSIONS.CONTENT.SHARE,
    PERMISSIONS.USER.VIEW_PROFILE,
    PERMISSIONS.USER.EDIT_PROFILE,
    PERMISSIONS.USER.MANAGE_USERS,
    PERMISSIONS.LEARNING_PLAN.VIEW,
    PERMISSIONS.LEARNING_PLAN.CREATE,
    PERMISSIONS.LEARNING_PLAN.EDIT,
    PERMISSIONS.LEARNING_PLAN.DELETE,
    PERMISSIONS.LEARNING_PLAN.SHARE,
    PERMISSIONS.ADMIN.ACCESS_ADMIN,
    PERMISSIONS.ADMIN.MANAGE_CONTENT,
    PERMISSIONS.ADMIN.MANAGE_SETTINGS
  ]
};

/**
 * 权限控制服务
 */
class PermissionService {
  constructor() {
    // 用户角色缓存
    this._userRoles = null;
    // 用户权限缓存
    this._userPermissions = null;
  }

  /**
   * 获取当前用户的角色
   * @returns {Promise<Array<string>>} 角色数组
   */
  async getUserRoles() {
    // 如果已有缓存，直接返回
    if (this._userRoles) {
      return this._userRoles;
    }

    try {
      // 检查是否已登录
      const isLoggedIn = await authService.isLoggedIn();

      if (!isLoggedIn) {
        // 未登录用户为访客角色
        this._userRoles = [ROLES.GUEST];
        return this._userRoles;
      }

      // 获取用户信息
      const userInfo = await authService.getCurrentUser(true);

      if (!userInfo) {
        // 无法获取用户信息，视为访客
        this._userRoles = [ROLES.GUEST];
        return this._userRoles;
      }

      // 从用户信息中获取角色
      // 注意：这里假设用户信息中有roles字段，实际情况可能需要调整
      const roles = userInfo.roles || [ROLES.USER];

      // 缓存角色
      this._userRoles = roles;

      return roles;
    } catch (error) {
      console.error('获取用户角色失败:', error);
      // 出错时返回访客角色
      return [ROLES.GUEST];
    }
  }

  /**
   * 获取当前用户的权限
   * @returns {Promise<Array<string>>} 权限数组
   */
  async getUserPermissions() {
    // 如果已有缓存，直接返回
    if (this._userPermissions) {
      return this._userPermissions;
    }

    try {
      // 获取用户角色
      const roles = await this.getUserRoles();

      // 根据角色获取权限
      const permissions = new Set();

      // 合并所有角色的权限
      roles.forEach(role => {
        const rolePermissions = ROLE_PERMISSIONS[role] || [];
        rolePermissions.forEach(permission => permissions.add(permission));
      });

      // 缓存权限
      this._userPermissions = Array.from(permissions);

      return this._userPermissions;
    } catch (error) {
      console.error('获取用户权限失败:', error);
      // 出错时返回空权限
      return [];
    }
  }

  /**
   * 检查当前用户是否有指定权限
   * @param {string|Array<string>} permissions - 要检查的权限
   * @param {boolean} requireAll - 是否要求拥有所有权限，默认为false（只需要有一个权限即可）
   * @returns {Promise<boolean>} 是否有权限
   */
  async hasPermission(permissions, requireAll = false) {
    try {
      // 获取用户权限
      const userPermissions = await this.getUserPermissions();

      // 转换为数组
      const permissionsToCheck = Array.isArray(permissions) ? permissions : [permissions];

      if (requireAll) {
        // 要求拥有所有权限
        return permissionsToCheck.every(permission => userPermissions.includes(permission));
      } else {
        // 只需要有一个权限
        return permissionsToCheck.some(permission => userPermissions.includes(permission));
      }
    } catch (error) {
      console.error('检查权限失败:', error);
      return false;
    }
  }

  /**
   * 检查当前用户是否有指定角色
   * @param {string|Array<string>} roles - 要检查的角色
   * @param {boolean} requireAll - 是否要求拥有所有角色，默认为false（只需要有一个角色即可）
   * @returns {Promise<boolean>} 是否有角色
   */
  async hasRole(roles, requireAll = false) {
    try {
      // 获取用户角色
      const userRoles = await this.getUserRoles();

      // 转换为数组
      const rolesToCheck = Array.isArray(roles) ? roles : [roles];

      if (requireAll) {
        // 要求拥有所有角色
        return rolesToCheck.every(role => userRoles.includes(role));
      } else {
        // 只需要有一个角色
        return rolesToCheck.some(role => userRoles.includes(role));
      }
    } catch (error) {
      console.error('检查角色失败:', error);
      return false;
    }
  }

  /**
   * 清除权限缓存
   */
  clearCache() {
    this._userRoles = null;
    this._userPermissions = null;
  }
}

// 导出单例实例
const permissionService = new PermissionService();

// 导出权限常量和角色常量
permissionService.PERMISSIONS = PERMISSIONS;
permissionService.ROLES = ROLES;

module.exports = permissionService;
