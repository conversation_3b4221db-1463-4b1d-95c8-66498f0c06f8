// utils/square-utils.js
// 广场页面工具函数

/**
 * 计算标签滚动位置
 * @param {Array} categories - 所有分类标签
 * @param {string} currentCategory - 当前选中的分类
 * @param {number} windowWidth - 窗口宽度
 * @returns {number} 滚动位置
 */
function calculateTagScrollPosition(categories, currentCategory, windowWidth) {
  // 找到当前分类的索引
  const currentIndex = categories.findIndex(item => item.id === currentCategory);
  if (currentIndex === -1) return 0;

  // 计算每个标签的平均宽度（估算值）
  const avgTagWidth = 80; // 假设平均宽度为80px

  // 计算滚动位置，使当前标签居中
  const scrollPosition = (currentIndex * avgTagWidth) - (windowWidth / 2) + (avgTagWidth / 2);

  // 确保滚动位置不小于0
  return Math.max(0, scrollPosition);
}

/**
 * 格式化帖子数据
 * @param {Object} note - API返回的笔记数据
 * @returns {Object} 格式化后的帖子数据
 */
function formatPostData(note) {
  return {
    id: note.id,
    title: note.title,
    content: note.content,
    imageUrl: note.imageUrl || 'https://picsum.photos/id/10/600/800', // 默认图片
    userAvatar: note.userAvatar || 'https://picsum.photos/id/100/200/200', // 默认头像
    userName: note.userName || '匿名用户',
    likes: note.likes || 0,
    comments: note.comments || 0,
    isLiked: note.isLiked || false,
    category: note.tagName || '未分类',
    tagId: note.tagId || 'unknown'
  };
}

/**
 * 计算瀑布流布局
 * @param {Array} posts - 所有帖子
 * @returns {Object} 包含左列和右列的对象
 */
function calculateWaterfallLayout(posts) {
  const leftPosts = [];
  const rightPosts = [];

  posts.forEach((post, index) => {
    if (index % 2 === 0) {
      leftPosts.push(post);
    } else {
      rightPosts.push(post);
    }
  });

  return { leftPosts, rightPosts };
}

/**
 * 处理API返回的标签数据
 * @param {Array} tags - API返回的标签数据
 * @returns {Array} 处理后的标签数据
 */
function processTagsData(tags) {
  if (!tags || !Array.isArray(tags)) {
    return getDefaultTags();
  }

  // 将API返回的标签转换为页面需要的格式
  const formattedTags = tags.map(tag => ({
    id: tag.id,
    name: tag.name
  }));

  // 确保"推荐"标签在中间位置
  const recommendIndex = formattedTags.findIndex(tag => tag.id === 'all');
  if (recommendIndex !== -1) {
    // 将"推荐"标签移到数组中间
    const recommendTag = formattedTags.splice(recommendIndex, 1)[0];
    const middleIndex = Math.floor(formattedTags.length / 2);
    formattedTags.splice(middleIndex, 0, recommendTag);
  } else {
    // 如果没有推荐标签，添加一个
    const recommendTag = { id: 'all', name: '推荐' };
    const middleIndex = Math.floor(formattedTags.length / 2);
    formattedTags.splice(middleIndex, 0, recommendTag);
  }

  return formattedTags;
}

/**
 * 获取默认标签
 * @returns {Array} 默认标签数组
 */
function getDefaultTags() {
  return [
    { id: 'listening', name: '倾听' },
    { id: 'empathy', name: '同理心' },
    { id: 'expression', name: '表达' },
    { id: 'appreciation', name: '赞美' },
    { id: 'feedback', name: '反馈' },
    { id: 'communication', name: '沟通技巧' },
    { id: 'relationship', name: '人际关系' },
    { id: 'all', name: '推荐' },
    { id: 'conflict', name: '冲突处理' },
    { id: 'teamwork', name: '团队协作' },
    { id: 'leadership', name: '领导力' },
    { id: 'negotiation', name: '谈判技巧' },
    { id: 'presentation', name: '演讲表达' },
    { id: 'networking', name: '社交能力' }
  ];
}

module.exports = {
  calculateTagScrollPosition,
  formatPostData,
  calculateWaterfallLayout,
  processTagsData,
  getDefaultTags
};
