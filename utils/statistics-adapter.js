/**
 * 统计模块适配器
 * 用于处理新旧API之间的差异
 */

// 导入API客户端
const { statisticsAPI, requestWithRetry } = require('./api');
const { statisticsAPIV2 } = require('./api-v2');
const app = getApp(); // 获取 App 实例

/**
 * 统计适配器
 * 提供统一的接口，内部使用新版API，同时处理错误回退到旧版API
 */
const statisticsAdapter = {
  /**
   * 获取学习统计数据
   * @returns {Promise<Object>} 学习统计数据
   */
  getLearningStatistics: async () => {
    // 在测试模式下，始终使用旧版API（它不需要登录且不会报错）
    if (app && app.globalData.isTestMode) {
      console.log('[Test Mode] Using statisticsAPI (v1) for getLearningStatistics.');
      return statisticsAPI.getLearningStatistics();
    }
    try {
      // 尝试使用新版API
      const result = await statisticsAPIV2.getLearningStatistics();
      return result;
    } catch (error) {
      console.warn('新版统计API调用失败，回退到旧版API', error);
      // 回退到旧版API
      return statisticsAPI.getLearningStatistics();
    }
  },

  /**
   * 获取每日学习记录
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 每日记录和分页信息
   */
  getDailyRecords: async (params = {}) => {
    // 在测试模式下，始终使用旧版API（它不需要登录且不会报错）
    if (app && app.globalData.isTestMode) {
      console.log('[Test Mode] Using statisticsAPI (v1) for getDailyRecords.');
      return statisticsAPI.getDailyRecords(params);
    }
    try {
      // 尝试使用新版API
      const result = await statisticsAPIV2.getDailyRecords(params);
      return result;
    } catch (error) {
      console.warn('新版统计API调用失败，回退到旧版API', error);
      // 回退到旧版API
      return statisticsAPI.getDailyRecords(params);
    }
  },

  /**
   * 记录学习活动
   * @param {Object} data - 活动数据
   * @returns {Promise<Object>} 活动记录结果
   */
  recordLearningActivity: async data => {
    // 旧版API recordActivity 已被注释掉并返回模拟数据，可以直接用
    if (app && app.globalData.isTestMode) {
      console.log('[Test Mode] Using statisticsAPI (v1) for recordLearningActivity.');
      return statisticsAPI.recordActivity(data); // 这个会返回模拟成功
    }
    try {
      // 尝试使用新版API
      const result = await statisticsAPIV2.recordLearningActivity(data);
      return result;
    } catch (error) {
      console.warn('新版统计API调用失败，回退到旧版API', error);
      // 回退到旧版API
      return statisticsAPI.recordActivity(data);
    }
  },

  /**
   * 获取学习活动列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 活动列表和分页信息
   */
  getActivities: async (params = {}) => {
    // 在测试模式下，始终使用旧版API（它不需要登录且不会报错）
    if (app && app.globalData.isTestMode) {
      console.log('[Test Mode] Using statisticsAPI (v1) for getActivities.');
      return statisticsAPI.getActivities(params);
    }
    try {
      // 尝试使用新版API
      const result = await statisticsAPIV2.getActivities(params);
      return result;
    } catch (error) {
      console.warn('新版统计API调用失败，回退到旧版API', error);
      // 回退到旧版API
      return statisticsAPI.getActivities(params);
    }
  },

  /**
   * 获取学习概览
   * @returns {Promise<Object>} 学习概览数据
   */
  getLearningOverview: async () => {
    // 在测试模式下，始终使用旧版API（它不需要登录且不会报错）
    if (app && app.globalData.isTestMode) {
      console.log('[Test Mode] Using combined statisticsAPI (v1) for getLearningOverview.');
      // 直接使用适配器中旧版API的组合逻辑
      const statistics = await statisticsAPI.getLearningStatistics();
      return {
        success: statistics.success,
        data: {
          statistics: statistics.data,
          recentTrend: [] // 旧版API没有趋势数据
        }
      };
    }
    try {
      // 尝试使用新版API
      const result = await statisticsAPIV2.getLearningOverview();
      return result;
    } catch (error) {
      console.warn('新版统计API调用失败，回退到旧版API', error);
      // 回退到旧版API - 组合多个旧版API调用
      const statistics = await statisticsAPI.getLearningStatistics();
      // 构造与新版API相同的响应格式
      return {
        success: statistics.success,
        data: {
          statistics: statistics.data,
          recentTrend: [] // 旧版API没有趋势数据
        }
      };
    }
  },

  /**
   * 获取学习趋势
   * @param {number} days - 天数
   * @returns {Promise<Object>} 学习趋势数据
   */
  getLearningTrend: async (days = 30) => {
    // 在测试模式下，始终使用旧版API（它不需要登录且不会报错）
    if (app && app.globalData.isTestMode) {
      console.log('[Test Mode] Using combined statisticsAPI (v1) for getLearningTrend.');
      // 直接使用适配器中旧版API的组合逻辑
      const today = new Date();
      const startDate = new Date();
      startDate.setDate(today.getDate() - days);
      const params = {
        startDate: formatDate(startDate),
        endDate: formatDate(today)
      };
      const dailyRecords = await statisticsAPI.getDailyRecords(params);
      return {
        success: dailyRecords.success,
        data: {
          trend: dailyRecords.data.records || []
        }
      };
    }
    try {
      // 尝试使用新版API
      const result = await statisticsAPIV2.getLearningTrend(days);
      return result;
    } catch (error) {
      console.warn('新版统计API调用失败，回退到旧版API', error);
      // 回退到旧版API - 使用每日记录API模拟趋势数据
      const today = new Date();
      const startDate = new Date();
      startDate.setDate(today.getDate() - days);
      const params = {
        startDate: formatDate(startDate),
        endDate: formatDate(today)
      };
      const dailyRecords = await statisticsAPI.getDailyRecords(params);
      // 构造与新版API相同的响应格式
      return {
        success: dailyRecords.success,
        data: {
          trend: dailyRecords.data.records || []
        }
      };
    }
  }
};

/**
 * 格式化日期为YYYY-MM-DD
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

module.exports = statisticsAdapter;
