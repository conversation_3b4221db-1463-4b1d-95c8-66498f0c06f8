/**
 * 本地存储服务
 * 提供统一的存储接口，支持键名前缀管理和过期时间管理
 */

/**
 * 存储服务类
 */
class StorageService {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   * @param {string} options.prefix - 键名前缀
   * @param {number} options.defaultExpiry - 默认过期时间（毫秒）
   * @param {boolean} options.enableLogging - 是否启用日志
   */
  constructor(options = {}) {
    this.options = {
      prefix: '',
      defaultExpiry: 24 * 60 * 60 * 1000, // 默认24小时
      enableLogging: false,
      ...options
    };

    // 初始化时清理过期数据
    this.cleanExpired();
  }

  /**
   * 生成带前缀的键名
   * @param {string} key - 原始键名
   * @returns {string} - 带前缀的键名
   * @private
   */
  _getKeyWithPrefix(key) {
    return this.options.prefix ? `${this.options.prefix}_${key}` : key;
  }

  /**
   * 记录日志
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   * @private
   */
  _log(level, message, data = null) {
    if (!this.options.enableLogging) return;

    const logMethod = level === 'error' ? console.error : console.log;
    if (data) {
      logMethod(`[StorageService] ${message}`, data);
    } else {
      logMethod(`[StorageService] ${message}`);
    }
  }

  /**
   * 设置存储项
   * @param {string} key - 键名
   * @param {*} data - 要存储的数据
   * @param {Object} options - 选项
   * @param {number} options.expiry - 过期时间（毫秒）
   * @returns {boolean} - 是否成功
   */
  set(key, data, options = {}) {
    if (!key) {
      this._log('error', '设置存储失败：键名无效');
      return false;
    }

    try {
      const prefixedKey = this._getKeyWithPrefix(key);
      const expiry = options.expiry || this.options.defaultExpiry;
      const storageItem = {
        data,
        expireAt: expiry ? Date.now() + expiry : null
      };

      wx.setStorageSync(prefixedKey, JSON.stringify(storageItem));
      this._log('info', `设置存储成功: ${key}`);
      return true;
    } catch (error) {
      this._log('error', `设置存储失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 获取存储项
   * @param {string} key - 键名
   * @param {*} defaultValue - 默认值（如果不存在或已过期）
   * @returns {*} - 存储的数据或默认值
   */
  get(key, defaultValue = null) {
    if (!key) {
      this._log('error', '获取存储失败：键名无效');
      return defaultValue;
    }

    try {
      const prefixedKey = this._getKeyWithPrefix(key);
      const storageData = wx.getStorageSync(prefixedKey);

      if (!storageData) {
        return defaultValue;
      }

      const storageItem = JSON.parse(storageData);

      // 检查是否过期
      if (storageItem.expireAt && storageItem.expireAt < Date.now()) {
        this.remove(key);
        this._log('info', `存储项已过期: ${key}`);
        return defaultValue;
      }

      this._log('info', `获取存储成功: ${key}`);
      return storageItem.data;
    } catch (error) {
      this._log('error', `获取存储失败: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 移除存储项
   * @param {string} key - 键名
   * @returns {boolean} - 是否成功
   */
  remove(key) {
    if (!key) {
      this._log('error', '移除存储失败：键名无效');
      return false;
    }

    try {
      const prefixedKey = this._getKeyWithPrefix(key);
      wx.removeStorageSync(prefixedKey);
      this._log('info', `移除存储成功: ${key}`);
      return true;
    } catch (error) {
      this._log('error', `移除存储失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 清除所有存储项
   * @param {boolean} onlyWithPrefix - 是否只清除带前缀的存储项
   * @returns {boolean} - 是否成功
   */
  clear(onlyWithPrefix = true) {
    try {
      if (onlyWithPrefix && this.options.prefix) {
        // 只清除带前缀的存储项
        const keys = wx.getStorageInfoSync().keys;
        const prefixLength = this.options.prefix.length;

        keys.forEach(key => {
          if (key.startsWith(this.options.prefix + '_')) {
            wx.removeStorageSync(key);
          }
        });

        this._log('info', `清除前缀为 ${this.options.prefix} 的存储项成功`);
      } else {
        // 清除所有存储项
        wx.clearStorageSync();
        this._log('info', '清除所有存储项成功');
      }
      return true;
    } catch (error) {
      this._log('error', '清除存储项失败', error);
      return false;
    }
  }

  /**
   * 清理过期的存储项
   * @returns {number} - 清理的项数
   */
  cleanExpired() {
    try {
      const keys = wx.getStorageInfoSync().keys;
      let cleanedCount = 0;

      keys.forEach(key => {
        // 如果有前缀，只处理带前缀的键
        if (this.options.prefix && !key.startsWith(this.options.prefix + '_')) {
          return;
        }

        try {
          const storageData = wx.getStorageSync(key);
          if (!storageData) return;

          const storageItem = JSON.parse(storageData);

          // 检查是否过期
          if (storageItem.expireAt && storageItem.expireAt < Date.now()) {
            wx.removeStorageSync(key);
            cleanedCount++;
          }
        } catch (e) {
          // 忽略单个项的错误，继续处理其他项
        }
      });

      if (cleanedCount > 0) {
        this._log('info', `清理了 ${cleanedCount} 个过期存储项`);
      }

      return cleanedCount;
    } catch (error) {
      this._log('error', '清理过期存储项失败', error);
      return 0;
    }
  }

  /**
   * 获取存储信息
   * @returns {Object|null} - 存储信息
   */
  getInfo() {
    try {
      const info = wx.getStorageInfoSync();
      return info;
    } catch (error) {
      this._log('error', '获取存储信息失败', error);
      return null;
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key - 键名
   * @returns {boolean} - 是否存在且未过期
   */
  has(key) {
    return this.get(key, undefined) !== undefined;
  }
}

// 创建默认实例
const defaultStorage = new StorageService({
  prefix: 'app',
  enableLogging: true
});

// 导出
export { StorageService, defaultStorage };
