/**
 * 本地存储工具
 * 提供统一的存储接口，支持键名前缀管理和过期时间管理
 */

import { StorageService } from './storage-service';

// 创建折叠状态专用存储服务实例
const collapseStorage = new StorageService({
  prefix: 'collapse',
  defaultExpiry: 30 * 24 * 60 * 60 * 1000, // 30天
  enableLogging: false
});

/**
 * 保存折叠状态到本地存储
 * @param {string} key - 存储的键名
 * @param {object} status - 要保存的状态对象
 */
export function saveCollapseStatus(key, status) {
  if (!key || !status) {
    console.error('保存折叠状态失败：参数无效', { key, status });
    return;
  }

  collapseStorage.set(key, status);
}

/**
 * 从本地存储加载折叠状态
 * @param {string} key - 存储的键名
 * @returns {object|null} - 状态对象，如果不存在则返回null
 */
export function loadCollapseStatus(key) {
  if (!key) {
    console.error('加载折叠状态失败：键名无效');
    return null;
  }

  return collapseStorage.get(key, null);
}

/**
 * 清除保存的折叠状态
 * @param {string} key - 存储的键名
 */
export function clearCollapseStatus(key) {
  if (!key) {
    console.error('清除折叠状态失败：键名无效');
    return;
  }

  collapseStorage.remove(key);
}

// 导出默认存储服务实例，以便其他模块使用
export { defaultStorage as storage } from './storage-service';