// utils/theme-manager.js
// 主题数据管理模块

/**
 * 主题数据管理类
 * 负责主题数据的加载、缓存和更新
 */
class ThemeManager {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   * @param {Object} options.api - API工具对象
   * @param {number} options.cacheExpiry - 缓存过期时间(毫秒)
   */
  constructor(options = {}) {
    // 主题数据
    this.themes = [];

    // 缓存相关
    this.cacheKey = 'theme_manager_cache';
    this.cacheExpiry = options.cacheExpiry || 30 * 60 * 1000; // 默认30分钟
    this.lastUpdateTime = 0;

    // API引用
    this.api = options.api || {};

    // 颜色相关
    this.colorMap = {
      blue: '#3B82F6',
      indigo: '#6366F1',
      purple: '#8B5CF6',
      pink: '#EC4899',
      red: '#EF4444',
      orange: '#F97316',
      amber: '#F59E0B',
      yellow: '#EAB308',
      lime: '#84CC16',
      green: '#22C55E',
      emerald: '#10B981',
      teal: '#14B8A6',
      cyan: '#06B6D4',
      sky: '#0EA5E9',
      violet: '#8B5CF6'
    };

    // 回调管理
    this.updateCallbacks = {};
    this.nextCallbackId = 1;

    // 初始化
    this._init();
  }

  /**
   * 初始化
   * @private
   */
  _init() {
    // 尝试从缓存加载主题数据
    try {
      const cachedData = wx.getStorageSync(this.cacheKey);
      if (cachedData) {
        const { themes, timestamp } = JSON.parse(cachedData);
        const now = Date.now();

        // 检查缓存是否过期
        if (now - timestamp < this.cacheExpiry) {
          this.themes = themes;
          this.lastUpdateTime = timestamp;
          console.log('从缓存加载主题数据成功', themes.length);
        } else {
          console.log('缓存已过期，需要重新加载');
        }
      }
    } catch (err) {
      console.error('从缓存加载主题数据失败', err);
    }
  }

  /**
   * 加载当前学习计划标签
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Array>} 主题数据
   */
  loadCurrentPlanTags(forceRefresh = false) {
    // 如果没有强制刷新且已有主题数据，直接返回
    if (!forceRefresh && this.themes.length > 0) {
      return Promise.resolve(this.themes);
    }

    // 如果没有API或API不可用，使用后备主题数据
    if (!this.api || !this.api.tagAPI || !this.api.tagAPI.getCurrentPlanTags) {
      console.warn('API不可用，使用后备主题数据');
      this.themes = this.getFallbackThemes();
      return Promise.resolve(this.themes);
    }

    // 获取洗牌后的颜色数组
    const shuffledColors = this.getShuffledColors();

    // 从API获取主题数据
    return this.api.tagAPI.getCurrentPlanTags(forceRefresh)
      .then(res => {
        if (res && res.data && res.data.length > 0) {
          console.log('成功加载标签数据:', res.data);
          this.themes = res.data.map((item, index) => ({
            id: item.id,
            name: item.name,
            englishName: item.englishName || '',
            description: item.description || '暂无描述',
            color: item.color || shuffledColors[index % shuffledColors.length]
          }));
          console.log('处理后的主题数据:', this.themes);

          // 更新缓存
          this._updateCache();

          // 触发更新回调
          this._triggerUpdateCallbacks();

          return this.themes;
        } else {
          console.log('API返回的标签数据为空，尝试加载系统默认标签');
          // 如果当前计划没有标签，尝试获取系统默认标签
          return this.loadSystemDefaultTags();
        }
      })
      .catch(err => {
        console.error('加载标签失败', err);
        // 失败时尝试获取系统默认标签
        return this.loadSystemDefaultTags();
      });
  }

  /**
   * 加载系统默认标签
   * @returns {Promise<Array>} 主题数据
   */
  loadSystemDefaultTags() {
    // 如果没有API或API不可用，使用后备主题数据
    if (!this.api || !this.api.tagAPI || !this.api.tagAPI.getSystemDefaultPlanTags) {
      console.warn('API不可用，使用后备主题数据');
      this.themes = this.getFallbackThemes();
      return Promise.resolve(this.themes);
    }

    // 获取洗牌后的颜色数组
    const shuffledColors = this.getShuffledColors();

    // 从API获取系统默认标签
    return this.api.tagAPI.getSystemDefaultPlanTags()
      .then(res => {
        if (res && res.success && res.data && res.data.tags && res.data.tags.length > 0) {
          console.log('成功从API获取系统默认标签:', res.data.tags);
          this.themes = res.data.tags.map((item, index) => ({
            id: item.id,
            name: item.name,
            englishName: item.englishName || '',
            description: item.description || '暂无描述',
            color: item.color || shuffledColors[index % shuffledColors.length]
          }));
          console.log('使用系统默认标签更新主题数据:', this.themes);

          // 更新缓存
          this._updateCache();

          // 触发更新回调
          this._triggerUpdateCallbacks();

          return this.themes;
        } else {
          // 如果没有系统默认标签数据，使用后备主题数据
          console.warn('系统默认标签数据为空，使用后备主题数据');
          this.themes = this.getFallbackThemes();
          return this.themes;
        }
      })
      .catch(err => {
        console.error('获取系统默认标签失败:', err);
        // 使用后备主题数据
        this.themes = this.getFallbackThemes();
        return this.themes;
      });
  }

  /**
   * 获取后备主题数据
   * @returns {Array} 主题数据
   */
  getFallbackThemes() {
    console.warn('使用后备主题数据');

    // 使用预定义的颜色
    return [
      {
        id: '101',
        name: '平台介绍',
        englishName: 'Intro',
        description: '了解AIBUBB平台的核心功能和使用技巧',
        color: this.colorMap.blue // "#3B82F6"
      },
      {
        id: '102',
        name: '泡泡功能',
        englishName: 'Bubbles',
        description: '探索交互式泡泡的使用方法',
        color: this.colorMap.lime // "#84CC16"
      },
      {
        id: '103',
        name: '广场探索',
        englishName: 'Square',
        description: '发现社区中的精彩内容和讨论',
        color: this.colorMap.sky // "#0EA5E9"
      },
      {
        id: '104',
        name: '学习计划',
        englishName: 'Plans',
        description: '制定和管理个性化学习计划',
        color: this.colorMap.amber // "#F59E0B"
      },
      {
        id: '105',
        name: '笔记技巧',
        englishName: 'Notes',
        description: '掌握高效记录和组织学习笔记的方法',
        color: this.colorMap.purple // "#8B5CF6"
      }
    ];
  }

  /**
   * 获取当前主题数据
   * @returns {Array} 主题数据
   */
  getCurrentThemes() {
    // 如果没有主题数据，返回后备主题数据
    if (!this.themes || this.themes.length === 0) {
      return this.getFallbackThemes();
    }

    return this.themes;
  }

  /**
   * 更新主题数据
   * @param {Array} themes - 主题数据
   */
  updateThemes(themes) {
    if (!themes || themes.length === 0) {
      console.warn('尝试更新空的主题数据');
      return;
    }

    this.themes = themes;

    // 更新缓存
    this._updateCache();

    // 触发更新回调
    this._triggerUpdateCallbacks();
  }

  /**
   * 更新缓存
   * @private
   */
  _updateCache() {
    try {
      const now = Date.now();
      const cacheData = JSON.stringify({
        themes: this.themes,
        timestamp: now
      });

      wx.setStorageSync(this.cacheKey, cacheData);
      this.lastUpdateTime = now;

      // 更新全局主题更新时间
      wx.setStorageSync('themeUpdateTime', now);

      console.log('主题数据缓存已更新');
    } catch (err) {
      console.error('更新主题数据缓存失败', err);
    }
  }

  /**
   * 触发更新回调
   * @private
   */
  _triggerUpdateCallbacks() {
    Object.values(this.updateCallbacks).forEach(callback => {
      try {
        callback(this.themes);
      } catch (err) {
        console.error('执行主题更新回调失败', err);
      }
    });
  }

  /**
   * 获取随机颜色
   * @returns {string} 颜色值
   */
  getRandomColor() {
    const colors = Object.values(this.colorMap);
    return colors[Math.floor(Math.random() * colors.length)];
  }

  /**
   * 获取洗牌后的颜色数组
   * @returns {Array<string>} 颜色数组
   */
  getShuffledColors() {
    const colors = Object.values(this.colorMap);

    // Fisher-Yates 洗牌算法
    for (let i = colors.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [colors[i], colors[j]] = [colors[j], colors[i]];
    }

    return colors;
  }

  /**
   * 注册主题更新回调
   * @param {Function} callback - 回调函数
   * @returns {string} 回调ID
   */
  onThemeUpdate(callback) {
    if (typeof callback !== 'function') {
      console.error('回调必须是函数');
      return '';
    }

    const callbackId = `callback_${this.nextCallbackId++}`;
    this.updateCallbacks[callbackId] = callback;

    return callbackId;
  }

  /**
   * 移除主题更新回调
   * @param {string} callbackId - 回调ID
   */
  offThemeUpdate(callbackId) {
    if (this.updateCallbacks[callbackId]) {
      delete this.updateCallbacks[callbackId];
    }
  }
}

module.exports = ThemeManager;
