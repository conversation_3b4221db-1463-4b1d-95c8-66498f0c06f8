/**
 * 主题工具函数
 * 用于在页面中方便地处理主题相关操作
 */

/**
 * 初始化页面主题
 * 在页面的onLoad或onShow中调用
 * @param {Object} page - 页面实例
 */
const initPageTheme = (page) => {
  if (!page) return;
  
  const app = getApp();
  if (!app) return;
  
  // 获取当前主题
  const currentTheme = app.getTheme ? app.getTheme() : 'light';
  
  // 设置页面主题数据
  page.setData({ theme: currentTheme });
  
  // 尝试设置页面根节点data-theme属性
  wx.createSelectorQuery().select('page').fields({
    node: true,
    properties: ['dataset']
  }, function(res) {
    if (res && res.node) {
      res.node.dataset.theme = currentTheme;
    }
  }).exec();
  
  // 注册主题变化回调
  app.themeModeChangeCallback = (theme) => {
    page.setData({ theme });
  };
  
  return currentTheme;
};

/**
 * 切换主题
 * @param {String} mode - 主题模式：'light', 'dark' 或 'system'
 */
const switchTheme = (mode) => {
  const app = getApp();
  if (!app || !app.updateThemeMode) return;
  
  app.updateThemeMode(mode);
};

/**
 * 获取当前主题
 * @returns {String} - 当前主题：'light' 或 'dark'
 */
const getCurrentTheme = () => {
  const app = getApp();
  if (!app || !app.getTheme) return 'light';
  
  return app.getTheme();
};

/**
 * 主题混入
 * 用于在页面中快速添加主题支持
 * 在页面定义里使用: const themeMixin = require('../../utils/theme-utils').themeMixin;
 * Page(themeMixin({...页面定义}))
 */
const themeMixin = (pageConfig) => {
  // 保存原始的生命周期函数
  const originalOnLoad = pageConfig.onLoad;
  const originalOnShow = pageConfig.onShow;
  
  // 混入新的生命周期函数
  pageConfig.onLoad = function(options) {
    // 初始化主题
    initPageTheme(this);
    
    // 调用原始的onLoad
    if (originalOnLoad) {
      originalOnLoad.call(this, options);
    }
  };
  
  pageConfig.onShow = function() {
    // 刷新主题
    const app = getApp();
    if (app && app.getTheme) {
      this.setData({ theme: app.getTheme() });
    }
    
    // 调用原始的onShow
    if (originalOnShow) {
      originalOnShow.call(this);
    }
  };
  
  return pageConfig;
};

module.exports = {
  initPageTheme,
  switchTheme,
  getCurrentTheme,
  themeMixin
}; 