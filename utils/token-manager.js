/**
 * 令牌管理服务
 * 提供统一的令牌存储、获取、刷新和清除功能
 */

// 令牌相关的存储键
const TOKEN_KEY = 'token';
const TOKEN_EXPIRY_KEY = 'tokenExpiry';
const REFRESH_TOKEN_KEY = 'refreshToken';
const USER_ID_KEY = 'userId';

// 导入API客户端
const api = require('./api-client/index');

/**
 * 令牌管理器
 */
class TokenManager {
  /**
   * 存储令牌信息
   * @param {Object} tokenData - 令牌数据
   * @param {string} tokenData.token - 访问令牌
   * @param {string} tokenData.refreshToken - 刷新令牌（可选）
   * @param {number} tokenData.expiresIn - 过期时间（秒）
   * @param {string|number} tokenData.userId - 用户ID
   */
  saveTokens(tokenData) {
    if (!tokenData || !tokenData.token || !tokenData.expiresIn) {
      console.error('令牌数据不完整，无法保存');
      return false;
    }

    try {
      // 计算过期时间（当前时间 + expiresIn秒）
      const expiryTime = Date.now() + (tokenData.expiresIn * 1000);

      // 存储令牌信息
      wx.setStorageSync(TOKEN_KEY, tokenData.token);
      wx.setStorageSync(TOKEN_EXPIRY_KEY, expiryTime);

      // 如果有刷新令牌，也存储它
      if (tokenData.refreshToken) {
        wx.setStorageSync(REFRESH_TOKEN_KEY, tokenData.refreshToken);
      }

      // 存储用户ID
      if (tokenData.userId) {
        wx.setStorageSync(USER_ID_KEY, tokenData.userId);
      }

      console.log('令牌信息已保存');
      return true;
    } catch (error) {
      console.error('保存令牌信息失败:', error);
      return false;
    }
  }

  /**
   * 获取访问令牌
   * @param {boolean} autoRefresh - 如果令牌即将过期，是否自动刷新
   * @returns {Promise<string|null>} 访问令牌或null
   */
  async getToken(autoRefresh = true) {
    try {
      const token = wx.getStorageSync(TOKEN_KEY);
      const tokenExpiry = wx.getStorageSync(TOKEN_EXPIRY_KEY);

      // 如果没有令牌或过期时间，返回null
      if (!token || !tokenExpiry) {
        return null;
      }

      const now = Date.now();
      const expiryTime = parseInt(tokenExpiry);

      // 如果令牌已过期，返回null
      if (now >= expiryTime) {
        console.log('令牌已过期');

        // 如果设置了自动刷新，尝试刷新令牌
        if (autoRefresh) {
          const newToken = await this.refreshToken();
          return newToken;
        }

        return null;
      }

      // 如果令牌即将过期（小于10分钟），尝试刷新
      const refreshThreshold = 10 * 60 * 1000; // 10分钟
      if (autoRefresh && (expiryTime - now < refreshThreshold)) {
        console.log('令牌即将过期，尝试刷新');
        const newToken = await this.refreshToken();
        return newToken || token; // 如果刷新失败，返回原令牌
      }

      return token;
    } catch (error) {
      console.error('获取令牌失败:', error);
      return null;
    }
  }

  /**
   * 刷新访问令牌
   * @returns {Promise<string|null>} 新的访问令牌或null
   */
  async refreshToken() {
    try {
      const refreshToken = wx.getStorageSync(REFRESH_TOKEN_KEY);

      if (!refreshToken) {
        console.log('没有刷新令牌，无法刷新访问令牌');
        return null;
      }

      // 调用刷新令牌API
      const response = await api.auth.refreshToken();

      if (response.success && response.data) {
        // 保存新的令牌信息
        this.saveTokens({
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          expiresIn: response.data.expiresIn,
          userId: response.data.userId
        });

        return response.data.token;
      } else {
        console.error('刷新令牌失败:', response.message || '未知错误');
        // 清除无效的令牌
        this.clearTokens();
        return null;
      }
    } catch (error) {
      console.error('刷新令牌过程中发生错误:', error);
      return null;
    }
  }

  /**
   * 清除所有令牌信息
   */
  clearTokens() {
    try {
      wx.removeStorageSync(TOKEN_KEY);
      wx.removeStorageSync(TOKEN_EXPIRY_KEY);
      wx.removeStorageSync(REFRESH_TOKEN_KEY);
      wx.removeStorageSync(USER_ID_KEY);
      console.log('令牌信息已清除');
    } catch (error) {
      console.error('清除令牌信息失败:', error);
    }
  }

  /**
   * 检查用户是否已登录
   * @param {boolean} autoRefresh - 如果令牌即将过期，是否自动刷新
   * @returns {Promise<boolean>} 是否已登录
   */
  async isLoggedIn(autoRefresh = true) {
    const token = await this.getToken(autoRefresh);
    return !!token;
  }

  /**
   * 获取用户ID
   * @returns {string|number|null} 用户ID或null
   */
  getUserId() {
    try {
      return wx.getStorageSync(USER_ID_KEY);
    } catch (error) {
      console.error('获取用户ID失败:', error);
      return null;
    }
  }
}

// 导出单例实例
module.exports = new TokenManager();
