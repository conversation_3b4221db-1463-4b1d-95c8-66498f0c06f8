# AIBUBB前端系统升级2.0阶段指导文档 (优化版)

## 文档说明

| 文档属性 | 值 |
|---------|-----|
| 状态 | ⭐ **活跃 (第二阶段唯一指导)** |
| 版本 | **2.0 (优化版)** |
| 修订日期 | 2025-05-22 (基于 V1.0 及综合规划 V3.6, 视觉设计 V2.0) |
| 作者 | AIBUBB前端团队 & AI Assistant |
| **修订说明** | 本版本基于 V1.0，并根据《前端系统升级综合规划 V3.6》的最新进度和《AIBUBB视觉设计文档 V2.0》的详细规范进行了全面优化。**本文档是前端团队在系统升级 2.0 阶段需要遵从的唯一指导文档。** |
| 文档定位 | 前端团队2.0阶段唯一遵从和更新进度的指导文档 |
| 归档声明 | 本文档基于"前端系统升级综合规划.md"和"AIBUBB视觉设计文档.md"，作为2.0阶段的唯一指导文档 |

## 一、2.0阶段背景与目标

### 1.1 背景概述

AIBUBB（计划改名NebulaLearn）前端系统升级已完成1.0阶段的基础架构建设和部分功能模块实现。根据《前端系统升级综合规划 V3.6》第16章"已完成工作检查报告"确认，目前已完成：

- ✅ API客户端架构设计和实现 (`utils/api-client/`)
- ✅ 数据模型和转换层实现 (`utils/models/`, `utils/models/transformers/`)
- ✅ 基础UI组件库实现 (`components/base/`)
- ✅ 用户认证模块重构（包括登录、令牌管理、权限控制）
- ✅ 回收站与软删除功能实现
- 🔄 学习计划管理模块部分实现（约50%）

2.0阶段将在这些已有成果的基础上，继续完成剩余核心功能模块的开发，全面落实视觉设计规范，优化性能与体验，并为后续的前后端融合做好准备。

### 1.2 2.0阶段核心目标

1.  **完成核心功能模块开发**:
    *   完成学习计划管理模块剩余功能（创建、模板管理、可视化）。
    *   实现内容交互模块（练习、观点、笔记的统一展示与交互）。
    *   实现并优化泡泡交互系统（首页核心体验）。
    *   根据设计文档优化用户中心（`profile`）和广场（`square`）模块。
2.  **全面落地视觉设计系统**: 严格按照《AIBUBB视觉设计文档 V2.0》的规范，统一界面风格，提升视觉效果和交互体验。
3.  **关键性能优化**: 重点优化加载速度、交互响应时间，特别是泡泡系统的动画性能。
4.  **提升测试覆盖率**: 扩展单元测试、集成测试和端到端测试，确保核心功能稳定。目标单元测试覆盖率 ≥ 80%。
5.  **准备前后端融合**: 完善API契约测试，准备必要的数据处理工具。

### 1.3 核心原则

-   **⚠️ 页面升级保留原则 (最高优先级)**：所有前端页面的升级，都**必须在现有页面实现的基础上进行优化**，尊重并保留原有的核心视觉布局和交互逻辑，不得完全推翻或破坏之前的成果。重点是体验提升和规范统一。
-   **设计规范驱动**: 严格遵循《AIBUBB视觉设计文档 V2.0》进行开发。
-   **性能优先**: 所有设计和实现，尤其是动效和复杂布局，必须优先考虑性能，确保流畅运行。
-   **渐进式改进**: 采用增量更新策略，确保系统稳定。
-   **用户体验至上**: 技术决策以提升用户体验为最终目标。

## 二、视觉设计系统落地 (遵循《AIBUBB视觉设计文档 V2.0》)

本阶段需全面应用已定义的视觉规范，确保所有新增和修改的界面符合设计要求。

### 2.1 设计哲学与原则落地

确保所有开发工作体现以下原则：用户中心、趣味探索、清晰一致、轻盈现代、**保留优化**、**性能优先**。

### 2.2 色彩系统应用

1.  **强制使用色彩变量**:
    *   在全局样式文件（如 `styles/variables.wxss` 或类似文件）中定义并使用 V2.0 文档规定的色彩 Token/变量（如 `--primary-color-light`, `--text-color-primary-dark` 等）。
    *   确保所有组件和页面均通过变量引用颜色。
2.  **实现并测试双主题**:
    *   完善亮色/暗色模式的切换逻辑 (`profile/index.wxml` 已有基础)。
    *   **严格测试**两种模式下的色彩应用和对比度，确保符合 WCAG AA 级标准。
3.  **规范渐变色使用**: 遵循 V2.0 文档关于渐变色的使用指南。

### 2.3 字体排版规范应用

1.  **使用排版变量与层级**:
    *   在全局样式文件中定义并使用 V2.0 文档推荐的字体层级变量（如 `--font-size-h1`, `--line-height-body`）。
    *   推荐使用 `rpx` 单位。
2.  **统一字体应用**: 审查并确保所有页面的文本元素符合定义的字体、字号、字重、行高和颜色规范。

### 2.4 图标系统规范应用

1.  **统一图标库**:
    *   明确并**强制使用 V2.0 文档确定的统一图标来源**（Remix Icon, Feather Icons 或内部规范库），禁止混用。
    *   维护 `/assets/icons/new/` 路径下的图标，确保风格统一。
2.  **规范图标使用**:
    *   遵循定义的尺寸规范（16px, 20px, 24px）及其应用场景。
    *   确保图标颜色符合不同状态（默认、激活、禁用）的规范。

### 2.5 布局与间距规范应用

1.  **强制使用间距变量**:
    *   在全局样式文件中定义并使用基于 4px/8px 倍数的间距变量（如 `--space-xs: 4px`, `--space-md: 16px`）。
    *   所有 `margin`, `padding` 必须使用间距变量。
2.  **统一布局模式**:
    *   遵循卡片布局、列表布局、瀑布流、全屏画布等模式的设计规范。
    *   注意页面元素的对齐和呼吸感。
3.  **毛玻璃效果**: 适度使用 `glass-card` 效果，并关注其性能影响。

### 2.6 交互元素与动效规范应用

1.  **核心元素**:
    *   **按钮**: 严格区分主/次按钮样式，并实现所有状态（Default, Hover/Pressed, Disabled, Loading）的视觉样式。
    *   **输入框**: 实现简洁样式和清晰的聚焦反馈。
    *   **选择器 (`tag-scroll`)**: 优化选中状态和滚动交互。
2.  **加载状态**:
    *   **强制使用统一加载模式**: 列表/卡片使用**骨架屏 (Skeleton Screen)**，页面级/独立操作使用指定的**加载指示器 (Spinner)**。
    *   确保加载反馈及时且过渡平滑。
3.  **动效**:
    *   **页面切换**: 保持 subtle 效果。
    *   **泡泡/星星**: **核心关注点**，必须流畅、自然、物理感，并进行**严格的性能优化**（利用硬件加速，控制资源消耗）。
    *   **微交互**: 提供即时 (<100ms) 且轻量的视觉反馈。

## 三、核心功能模块升级 (遵循《AIBUBB视觉设计文档 V2.0》第8节)

**重要提示**: 所有模块的升级必须在现有代码基础上进行，遵循"页面升级保留原则"。

### 3.1 学习计划管理模块 (完成剩余 50%)

*   **负责人**: 开发团队A
*   **现状**: 已完成服务层、列表页、详情页重构（约50%）。
*   **目标**: 完成模块全部功能，并符合 `learn` 页面的 V2.0 设计规范。
*   **待完成任务**:
    1.  **学习计划创建流程**:
        *   实现 `create-plan` 或 `plan-generating` 页面/流程。
        *   整合 AI 辅助创建功能（引导、预览、调整、确认）。
        *   **特别关注**: AI 创建流程的 UX 设计，需独立且精心设计。
    2.  **模版资源发现 (模板市场基础)**:
        *   在 `learn` 页面的"模版资源发现"视图中，实现模板列表展示 (`template-list`)。
        *   实现"使用模板"创建计划的入口。
        *   **设计预留**: 为未来的搜索、筛选（主题、难度等）、排序、创作者信息、评分预留 UI 空间。
    3.  **学习进度可视化**: 在计划详情页或其他合适位置，开发学习进度可视化图表组件。
    4.  **首次使用体验 (Onboarding)**:
        *   实现首次进入或无计划时的引导流程。
        *   调用 AI 快速生成 3 日体验计划。
    5.  **视觉与交互优化**:
        *   确保视图切换 (`view-toggle`) 平滑。
        *   卡片设计、空状态、加载状态符合规范。
    6.  **测试**: 编写相关单元测试和端到端测试。

### 3.2 内容交互模块

*   **负责人**: 开发团队B
*   **目标**: 实现三种核心学习内容（练习、观点、笔记）在首页泡泡触发的模态弹窗内的统一展示和交互逻辑，符合 `index` 页面的 V2.0 设计规范。
*   **实施任务**:
    1.  **开发统一内容展示模态弹窗**:
        *   设计弹窗的基本布局和样式。
        *   能根据传入的内容类型（Exercise, Insight, Note）动态渲染相应内容和交互控件。
    2.  **实现各类型内容的交互**:
        *   **练习 (Exercise)**: 展示题目、交互控件（输入/选择），处理提交与反馈。
        *   **观点 (Insight)**: 展示内容，关闭即完成。
        *   **笔记 (Note)**: 展示富文本/图片，支持滚动。实现 V2.0 确定的完成逻辑（**方案 N1: 滚动到底** 或 **方案 N2: 确认按钮**，需最终确认）。
    3.  **数据对接**: 与 `daily_content` 及 `daily_content_relation` 关联，获取正确的任务内容。
    4.  **性能与体验**: 确保弹窗加载迅速，交互流畅，内容展示清晰。
    5.  **测试**: 编写单元测试和交互测试。

### 3.3 泡泡交互系统 (首页 `index`)

*   **负责人**: 开发团队C
*   **目标**: 实现并优化首页的核心泡泡/星星交互体验，严格符合 `index` 页面的 V2.0 设计规范，并**高度关注性能**。
*   **实施任务**:
    1.  **Canvas 动画优化 (性能关键)**:
        *   重构或优化泡泡/星星的生成、运动（漂浮、碰撞）、合并、消失动画算法。
        *   确保动画流畅、自然、物理感强。
        *   **进行性能剖析和针对性优化**: 利用硬件加速 (CSS Transform/Opacity)，降低 CPU/GPU 消耗，在不同设备上测试。
        *   实现泡泡/星星与内容（颜色/标签关联）的视觉联系。
    2.  **实现核心交互循环**:
        *   **内容呈现**: 画布动态展示少量（如6个）代表当天核心任务的泡泡/星星。
        *   **任务触发**: 点击泡泡/星星，能正确识别并传递任务信息。
        *   **任务执行**: 调用 **3.2 内容交互模块** 实现的模态弹窗。
        *   **完成反馈**: 任务完成后，对应泡泡/星星播放精致的消失动画。
        *   **即时奖励**: 调用后端接口，更新 `user_content_progress`, `user.exp_points`, `learning_plan.progress`。
        *   **阶段性奖励**: 实现"清屏"奖励视觉效果（画布上当前批次全部消除后触发）。
    3.  **实现底部悬浮按钮 (FAB)**:
        *   实现 **V2.0 决策 (方案 A)**: "加载下一组任务"功能。
        *   按钮**仅在当前画布泡泡/星星全部完成后激活**。
        *   设计合适的图标（如"下一组"）。
    4.  **UI 元素**: 实现新手引导层、加载/错误状态提示，风格统一。
    5.  **测试**: 重点进行性能测试、交互测试、边界条件测试。

### 3.4 用户中心模块优化 (`profile`)

*   **负责人**: 开发团队A
*   **目标**: 根据 `profile` 页面的 V2.0 设计规范，优化用户信息展示、菜单结构和整体视觉。
*   **实施任务**:
    1.  **优化顶部用户信息区 (`profile-header`)**:
        *   保留 `glass-card` 效果、主题切换开关、头像昵称。
        *   **显著展示**: 用户等级名称 (`level.name`) 和醒目的经验值进度条 (`user.exp_points` / `level.required_exp`)，增强成长感。
        *   (可选) 补充核心统计数据。
    2.  **实现可折叠菜单列表区 (`menu-section`)**:
        *   使用可折叠区块组织菜单项，确保交互流畅并考虑状态记忆。
        *   **实现所有 V2.0 定义的区块和菜单项**:
            *   学习与成就 (学习计划、学习记录、学习统计、**我的徽章**、**成就墙**)
            *   内容与创作 (笔记管理、我的收藏、我的发布)
            *   社交与分享 (邀请好友、学习排行、**分享主页/成就**)
            *   设置与帮助 (账号设置、通知设置、**隐私设置**、主题偏好、关于、客服、手机绑定、退出)
    3.  **子页面基础**:
        *   创建或准备**徽章墙** (`user_badge`) 和**成就墙** (`user_achievement`) 页面，需注重视觉设计。
        *   准备**学习统计**页面，需使用简洁图表。
        *   优化**学习记录**页面信息展示。
    4.  **视觉与交互**: 整体保持 `glass-card` 和清晰信息层级，图标统一，菜单交互流畅。管理信息密度，避免拥挤。
    5.  **测试**: 编写单元测试和页面交互测试。

### 3.5 广场模块优化 (`square`)

*   **负责人**: 开发团队A (或 B/C，根据资源协调)
*   **目标**: 根据 `square` 页面的 V2.0 设计规范，优化标签选择、内容展示和发布流程。
*   **实施任务**:
    1.  **优化顶部标签滚动选择器 (`tag-scroll`)**:
        *   提升滑动流畅度和触摸区域。
        *   实现**中心高亮/选中状态**的明显视觉差异。
        *   实现**默认"推荐"状态逻辑**（关联用户活跃计划标签）。
        *   实现标签切换后下方内容的动态更新。
    2.  **优化内容瀑布流 (`waterfall-content`) (性能关键)**:
        *   **采用高性能实现**: 虚拟列表或按需加载技术。
        *   **优化图片**: 懒加载、WebP 格式、合理尺寸。
        *   提供平滑的**骨架屏**加载状态。
        *   **区分内容来源**: 笔记卡片需在视觉上清晰区分"用户生成"和"AI 生成"。
        *   **兼容性设计**: 卡片设计考虑未来兼容视频等类型。
        *   实现高效的无限滚动或分页加载。
    3.  **优化右下角发布按钮 (FAB)**:
        *   样式遵循全局规范。
        *   实现点击跳转至笔记创建页 (`pages/note/edit.wxml`)。
        *   实现**自动关联标签**功能（若发布前已在顶部选中标签）。
    4.  **其他**:
        *   考虑 AI 生成内容的质量预期管理（如明确标识）。
    5.  **测试**: 重点测试滚动性能、瀑布流加载、标签切换逻辑。

## 四、用户体验优化

*   **负责人**: 全体团队，UI 团队主导设计
*   **目标**: 全面提升应用的加载体验、错误容忍度和操作反馈。
*   **实施任务**:
    1.  **加载状态优化**:
        *   开发或完善通用**骨架屏 (Skeleton Screen)** 组件，适配不同卡片和列表。
        *   统一**加载指示器 (Spinner)** 样式和使用场景。
        *   实现平滑的加载过渡效果。
        *   在关键页面（如首页、广场、学习计划）应用数据和页面**预加载**策略。
    2.  **错误处理机制**:
        *   开发统一的错误提示组件（如 Toast、页面级提示）。
        *   视觉区分不同类型的错误（网络、服务、业务逻辑）。
        *   实现友好的错误恢复机制（如重试按钮、引导操作）。
        *   优化网络错误和服务错误的具体处理逻辑。
    3.  **操作反馈机制**:
        *   统一按钮、输入框等交互元素的视觉反馈（如点击效果）。
        *   为常见操作（点赞、切换、展开/折叠）添加**即时、轻量的微交互动效**。
        *   优化操作成功/失败的状态提示（如 Toast、临时高亮）。

## 五、性能优化

*   **负责人**: 全体团队，开发团队 C 重点关注动画性能
*   **目标**: 提升应用整体性能，特别是渲染速度和资源加载。
*   **实施任务**:
    1.  **渲染性能优化**:
        *   审查并优化组件更新逻辑，**减少不必要的 `setData`**。
        *   在长列表场景（如广场瀑布流、学习记录）**强制使用虚拟列表**或类似的优化技术。
        *   进行性能测试和分析，定位瓶颈。
        *   **特别关注**: `index` 页面的 Canvas 动画性能。
    2.  **资源优化**:
        *   **图片优化**: 实现图片**懒加载**，推广使用 **WebP** 格式，根据展示需要请求合适尺寸的图片。
        *   **代码包优化**: 优化小程序**分包策略**，减少主包体积。分析并移除无用代码和资源。
        *   **缓存策略**: 优化静态资源缓存，合理利用数据缓存（API 客户端已实现基础缓存）。
    3.  **小程序特性利用**:
        *   优化**页面预加载**策略。
        *   评估是否需要使用 **Worker** 处理复杂计算（如某些 AI 相关逻辑）。

## 六、测试与质量保障

*   **负责人**: 测试团队，全体开发配合
*   **目标**: 提升代码质量和功能稳定性，达到关键指标。
*   **实施任务**:
    1.  **扩展单元测试**:
        *   为本阶段新增和修改的核心业务逻辑、服务、工具函数编写单元测试。
        *   重点覆盖：学习计划服务、内容交互逻辑、泡泡系统核心算法、用户中心逻辑等。
        *   **目标**: 整体单元测试覆盖率提升至 **80%** 以上。
    2.  **扩展集成测试**:
        *   为涉及 API 调用、跨组件交互、数据流转的关键场景编写集成测试。
        *   重点覆盖：学习计划创建流程、首页泡泡交互完整流程、广场内容加载与筛选等。
    3.  **实现端到端测试**:
        *   覆盖核心用户流程，如：
            *   用户从首页点击泡泡 -> 完成任务 -> 获得奖励 -> 加载下一组。
            *   用户创建学习计划 -> 查看计划详情 -> 查看进度。
            *   用户浏览广场 -> 筛选内容 -> 发布笔记。
            *   用户查看个人中心 -> 编辑资料 -> 查看成就。
        *   建立自动化测试流程。
    4.  **性能测试**: 针对 `index` 页面泡泡系统、`square` 页面瀑布流进行专项性能测试。
    5.  **设计验收**: UI 团队需对最终实现进行视觉和交互验收。

## 七、前后端融合准备

*   **负责人**: 测试团队，开发团队 A/B/C 配合
*   **目标**: 为半年后的前后端正式融合做好技术准备，降低集成风险。
*   **实施任务**:
    1.  **API 契约测试**:
        *   基于后端提供的 OpenAPI 规范，开发和完善 API 契约测试脚本。
        *   验证前端调用的 API 响应结构是否与规范一致。
        *   将契约测试集成到 CI/CD 流程中。
    2.  **数据迁移/转换工具准备**:
        *   评估是否需要开发模拟数据到真实数据的转换或验证工具。
        *   准备数据迁移相关的测试用例。
    3.  **影子测试策略准备**:
        *   评估并准备影子测试所需的基础设施或逻辑（如请求复制、响应比较）。
        *   此阶段可能侧重于方案设计和工具预研。

## 八、时间规划与里程碑 (8周，待细化)

**注意**: 此时间规划基于优化后的任务范围，具体排期需团队根据资源和优先级进一步确认。用户认证模块已完成，无需排期。学习计划管理只需完成剩余 50%。

| 周次 | 主要任务 (示例，待细化) | 负责人/团队 | 预期成果/关键节点 |
|------|-------------------------|------------|-------------------|
| 1-2  | **视觉规范落地 (基础)**<br> - 全局变量 (颜色/字体/间距)<br> - 图标库统一<br> - 基础组件样式更新 | UI, 全体开发 | 全局样式文件、图标库确认、基础组件符合 V2 |
| 2-4  | **学习计划管理 (收尾)**<br> - 创建流程 (含 AI)<br> - 模板市场基础<br> - 进度可视化 | 开发团队 A  | `learn` 页面功能完整 |
| 3-5  | **内容交互模块 (弹窗)**<br> - 统一弹窗<br> - 各类型内容交互实现 | 开发团队 B  | 首页任务弹窗功能完整 |
| 4-6  | **泡泡交互系统 (核心)**<br> - Canvas 动画优化<br> - 核心交互循环<br> - FAB 实现<br> - **性能测试与调优** | 开发团队 C  | `index` 页面核心体验完成且性能达标 |
| 5-7  | **用户中心 & 广场优化**<br> - `profile` 优化 (头/菜单/子页)<br> - `square` 优化 (标签/瀑布流/FAB)<br> - **瀑布流性能优化** | 开发团队 A/B | `profile`, `square` 页面符合 V2 规范 |
| 6-8  | **体验 & 性能优化 (全面)**<br> - 加载状态 (骨架屏/预加载)<br> - 错误处理<br> - 操作反馈 (微交互)<br> - 资源优化 (图片/分包) | 全体团队  | 应用整体体验和性能提升 |
| 7-8  | **测试强化 & 融合准备**<br> - 提升测试覆盖率<br> - 端到端测试完善<br> - API 契约测试<br> - 文档整理 | 测试, 全体 | 测试报告、融合准备文档 |

### 8.1 里程碑

*   **里程碑一：第 2 周末 - 视觉基础规范落地评审**
    *   交付物：全局样式变量文件、统一图标库应用、基础组件符合 V2 规范。
    *   评审标准：代码符合规范，视觉效果符合设计稿。
*   **里程碑二：第 4 周末 - 学习计划与内容交互模块评审**
    *   交付物：`learn` 页面完整功能、首页任务弹窗完整功能。
    *   评审标准：功能符合需求和设计文档，交互流畅。
*   **里程碑三：第 6 周末 - 首页核心体验与性能评审**
    *   交付物：`index` 页面泡泡交互系统稳定版。
    *   评审标准：功能符合设计文档，**性能测试达标**，交互体验流畅。
*   **里程碑四：第 8 周末 - 2.0 阶段验收**
    *   交付物：完成本阶段所有开发任务、性能优化报告、测试报告（含覆盖率）、融合准备文档。
    *   评审标准：通过所有验收测试，满足核心 KPI 要求，达到上线标准。

## 九、风险管理

根据《前端系统升级综合规划 V3.6》并结合本阶段任务，重点关注以下风险：

| 风险点 | 影响 | 可能性 | 应对策略 | 责任人 |
|---|---|---|---|---|
| **泡泡交互系统性能不达标** | 核心体验差，用户流失 | 高 | 1. **早期介入性能测试**，持续监控。<br>2. **算法与渲染优化**，利用硬件加速。<br>3. **设计降级预案**（如简化动画效果）。<br>4. **严格的代码审查**。 | 开发团队 C, 架构师 |
| **视觉设计实现与规范偏差** | UI 不一致，体验割裂 | 中 | 1. **强制使用设计变量**。<br>2. **UI 团队定期走查**，建立检查清单。<br>3. **组件库严格把关**。 | UI 团队, 全体开发 |
| **前后端 API 契约不一致** | 功能阻塞，集成困难 | 中 | 1. **强化 API 契约测试**，尽早发现问题。<br>2. **建立快速沟通渠道**，及时同步变更。<br>3. **前端做好适配层准备**。 | 开发团队 A/B/C, 测试 |
| **学习计划 AI 创建流程复杂** | 开发延期，用户体验差 | 中 | 1. **拆分任务**，分步实现和迭代。<br>2. **优先保证核心流程**，逐步完善 AI 交互。<br>3. **加强 UX 设计评审**。 | 开发团队 A, 产品, UI |
| **瀑布流性能问题** | 广场页面卡顿 | 中 | 1. **强制使用虚拟列表/优化技术**。<br>2. **严格的图片优化**。<br>3. **进行专项性能测试**。 | 开发团队 A/B, 架构师 |
| 功能模块开发延期 | 影响整体进度 | 中 | 1. **细化任务拆解**，明确依赖。<br>2. **加强进度跟踪** (日站会/周会)。<br>3. **灵活调配资源**。 | 项目经理 |

## 十、总结

前端系统升级 2.0 阶段是承上启下的关键阶段。本优化后的指导文档结合了最新的项目进展和详细的设计规范，旨在为前端团队提供清晰、准确、可靠的行动指南。

团队需严格遵循**页面升级保留原则**和**性能优先**原则，紧密围绕《AIBUBB视觉设计文档 V2.0》的要求，完成学习计划、内容交互、泡泡系统、用户中心和广场等核心模块的开发与优化。同时，持续提升测试覆盖率，并为后续的前后端融合做好准备。

通过本阶段的工作，AIBUBB (NebulaLearn) 的前端将实现视觉与交互的统一、核心功能的完善以及性能体验的提升，为用户带来更佳的学习感受。

## 十一、工作进度报告

### 11.1 当前工作进度（截至2025-06-05）

#### 已完成工作

##### 1. 视觉设计系统落地（第一阶段）

- ✅ 创建全局样式变量文件 `styles/variables.wxss`
  - 定义了色彩系统、字体排版、间距、圆角、阴影等变量
  - 支持亮色模式和深色模式

- ✅ 更新全局样式 `app.wxss`
  - 引入新的变量文件
  - 更新全局样式以使用新的变量

- ✅ 创建图标库规范文档 `docs/icon-guidelines.md`
  - 明确图标来源（Remix Icon、Feather Icons）
  - 定义图标存放位置、命名规范、尺寸规范和颜色规范

- ✅ 创建基础组件样式更新计划 `docs/component-style-update-plan.md`
  - 定义更新原则、优先级、具体任务和测试计划
  - 提供组件样式变量对照表

##### 2. 高优先级组件更新（第二阶段）

- ✅ 更新 Button 组件样式
  - 使用新的变量替换硬编码值
  - 优化交互效果和过渡动画

- ✅ 更新 Card 组件样式
  - 使用新的变量替换硬编码值
  - 优化卡片阴影、圆角和内边距

- ✅ 创建 Text 组件
  - 支持不同的文本类型、颜色、对齐方式
  - 支持文本截断、加粗、斜体等功能

- ✅ 创建 Icon 组件
  - 支持不同的图标尺寸、颜色、激活状态
  - 使用新的图标路径 `/assets/icons/new/`

- ✅ 更新 Input 组件样式
  - 使用新的变量替换硬编码值
  - 优化输入框状态和交互效果

##### 3. 中优先级组件更新（第三阶段）

- ✅ 创建 Tag 组件
  - 支持不同的标签类型、尺寸、样式
  - 支持朴素标签、圆角标签、标记样式、可关闭标签

- ✅ 创建 Badge 组件
  - 支持不同的徽章类型、尺寸、位置
  - 支持圆点徽章、数字徽章、文本徽章

- ✅ 创建 Divider 组件
  - 支持水平分割线、垂直分割线
  - 支持实线、虚线、点线等类型
  - 支持带文字的分割线，可自定义文字位置

- ✅ 创建 Switch 组件
  - 支持不同的开关类型、尺寸
  - 支持禁用状态、加载状态
  - 支持标签及标签位置自定义

##### 4. 测试与验证

- ✅ 创建主题测试页面 `pages/test/theme-test`
  - 测试所有更新和新创建的组件
  - 实现主题切换功能，测试深色模式适配
  - 测试不同尺寸、类型和状态的组件表现

#### 进行中工作

##### 1. 低优先级组件更新（第四阶段）

- ⏳ 创建 Checkbox 组件
- ⏳ 创建 Radio 组件
- ⏳ 创建 Progress 组件
- ⏳ 创建 Slider 组件
- ⏳ 创建 Popup 组件

##### 2. 业务组件更新

- ⏳ 更新学习计划相关组件
- ⏳ 更新内容展示相关组件
- ⏳ 更新用户中心相关组件

#### 待开始工作

##### 1. 泡泡交互系统（核心体验）

- 📝 Canvas动画优化
- 📝 核心交互循环实现
- 📝 性能优化（确保流畅60fps）

##### 2. 性能优化

- 📝 渲染性能优化（减少不必要的setData）
- 📝 资源优化（图片懒加载、WebP格式）
- 📝 小程序分包策略优化

##### 3. 文档与规范

- 📝 组件库使用文档
- 📝 设计系统实施指南
- 📝 性能优化最佳实践

### 11.2 工作计划更新

| 阶段 | 时间 | 任务 | 状态 |
|------|------|------|------|
| 第一阶段 | 第1-2周 | 视觉设计系统落地 | ✅ 已完成 |
| 第二阶段 | 第2-3周 | 高优先级组件更新 | ✅ 已完成 |
| 第三阶段 | 第3-4周 | 中优先级组件更新 | ✅ 已完成 |
| 第四阶段 | 第4-5周 | 低优先级组件更新 | ⏳ 进行中 |
| 第五阶段 | 第5-6周 | 业务组件更新 | ⏳ 进行中 |
| 第六阶段 | 第6-7周 | 泡泡交互系统实现 | 📝 待开始 |
| 第七阶段 | 第7-8周 | 性能优化 | 📝 待开始 |
| 第八阶段 | 第8周 | 文档与规范 | 📝 待开始 |

### 11.3 风险与挑战更新

1. **泡泡交互系统性能**：需要确保在低端设备上也能流畅运行
2. **组件兼容性**：确保新组件与现有页面的兼容性
3. **深色模式适配**：确保所有页面在深色模式下的表现一致
4. **团队协作**：需要确保团队成员理解并正确使用新的设计系统

### 11.4 下一步计划

1. 完成低优先级组件的更新
2. 开始业务组件的更新
3. 启动泡泡交互系统的开发
4. 编写组件库使用文档，方便团队成员查阅和使用

## 十二、前后端并行开发规范

为确保前后端并行开发期间的高效协作和稳定进度，特制定本规范，作为前端团队在并行开发阶段遵循的标准指南。

### 12.1 接口规范与契约

#### 12.1.1 API接口文档

1. **接口文档位置**：
   - 所有API接口统一在 `docs/api` 目录维护
   - 前端开发必须参考的唯一接口文档为 `docs/api/openapi.yaml`（OpenAPI 3.0规范）
   - 接口文档变更历史记录在 `docs/api/changelog.md`

2. **接口规范**：
   - URL路径采用 **kebab-case** 格式
   - 请求/响应字段采用 **camelCase** 格式
   - 所有日期时间字段使用 ISO-8601 格式 (YYYY-MM-DDThh:mm:ss.sssZ)
   - 分页参数统一使用 `page` 和 `pageSize`

#### 12.1.2 接口变更管理

1. **变更通知机制**：
   - 后端API变更必须提前5个工作日通知前端团队
   - 变更通知通过"API变更"钉钉群和邮件（<EMAIL>）发出
   - 通知必须包含：变更内容、影响范围、预计生效时间、接口文档链接

2. **版本控制**：
   - 主版本号变更（如v1→v2）表示不兼容更新，必须提前10个工作日通知
   - 次版本号变更（如v1.1→v1.2）表示功能扩展，提前5个工作日通知
   - 修订号变更（如v1.1.0→v1.1.1）表示问题修复，提前2个工作日通知

#### 12.1.3 Mock数据服务

1. **Mock服务地址**：
   - 开发环境：`https://mock-api.dev.aibubb.com`
   - 测试环境：`https://mock-api.test.aibubb.com`
   - 统一前缀：`/api/v1`

2. **Mock数据维护**：
   - Mock数据定义位于 `mock/api/` 目录
   - 由API负责人（张三）统一维护，确保与最新接口规范一致
   - 每周一进行Mock数据更新，同步最新后端接口变更

3. **使用方式**：
   - 前端代码中通过环境变量 `API_BASE_URL` 切换API请求基础路径
   - 在 `.env.local` 文件中设置本地开发的API基础路径

### 12.2 环境与工具链

#### 12.2.1 开发环境搭建

1. **必要依赖**：
   - Node.js 16.x 或更高版本
   - npm 8.x 或更高版本
   - 微信开发者工具 1.05.x 或更高版本

2. **环境搭建步骤**：
   ```bash
   # 1. 克隆项目
   git clone https://github.com/aibubb/frontend.git
   cd frontend

   # 2. 安装依赖
   npm install

   # 3. 配置环境变量
   cp .env.example .env.local
   # 编辑.env.local设置环境变量

   # 4. 启动开发服务
   npm run dev
   ```

3. **VSCode推荐插件**：
   - ESLint
   - Prettier
   - Wechat Mini Program API
   - EditorConfig for VS Code

#### 12.2.2 环境切换

1. **环境配置文件**：
   - `.env.development`：开发环境（默认使用Mock服务）
   - `.env.testing`：测试环境（连接测试服务器）
   - `.env.preview`：预发布环境（连接预发布服务器）
   - `.env.production`：生产环境

2. **切换方式**：
   ```bash
   # 开发环境（默认）
   npm run dev

   # 测试环境
   npm run dev:test

   # 预发布环境
   npm run dev:preview
   ```

3. **环境标识**：
   - 非生产环境会在应用左上角显示环境标识
   - 开发环境：红色"DEV"
   - 测试环境：黄色"TEST"
   - 预发布环境：蓝色"PREVIEW"

#### 12.2.3 构建与打包

1. **构建命令**：
   ```bash
   # 构建测试版本
   npm run build:test

   # 构建预发布版本
   npm run build:preview

   # 构建生产版本
   npm run build
   ```

2. **分包策略**：
   - 主包仅包含首页、用户中心核心功能
   - 其他功能划分到不同分包：
     - `packageLearn`：学习计划相关
     - `packageSocial`：社区相关
     - `packageUser`：用户高级功能

### 12.3 开发协作流程

#### 12.3.1 前后端联调流程

1. **联调时间节点**：
   - 周三和周五下午2:00-5:00为固定联调时间
   - 重大功能可预约专项联调，至少提前一天在"联调申请"群预约

2. **联调流程**：
   - 前端开发提交联调申请，包含功能描述、接口列表、期望结果
   - 后端开发确认申请并准备测试数据
   - 联调时使用专用的联调环境 `https://api-sync.test.aibubb.com`
   - 联调结果记录在JIRA工单中，并标记为"已联调"或"联调中"

3. **问题解决机制**：
   - 联调发现的问题记录在JIRA并分配责任人
   - 优先级标记为"High"的问题需在24小时内解决
   - 每日晨会汇报联调问题的解决进度

#### 12.3.2 接口问题快速响应机制

1. **紧急问题处理**：
   - 生产环境接口问题通过"线上问题"钉钉群报告，必须@相关负责人
   - 线上问题响应SLA：15分钟内响应，2小时内提供解决方案或临时方案

2. **问题升级流程**：
   - 一级升级：开发→团队负责人（响应超时或无法解决）
   - 二级升级：团队负责人→技术总监（复杂问题或跨团队问题）
   - 三级升级：技术总监→CTO（重大问题或影响业务连续性）

#### 12.3.3 沟通渠道与频率

1. **固定会议**：
   - 日站会：每日9:30-9:45，同步进度和问题
   - 前后端周同步会：每周一10:00-11:30，讨论接口规范和问题
   - 隔周评审会：每两周周四14:00-16:00，评审完成功能

2. **即时沟通**：
   - "前端团队"钉钉群：日常沟通
   - "前后端协作"钉钉群：接口和联调相关问题
   - "泡泡交互"钉钉群：专项功能小组讨论

### 12.4 代码管理规范

#### 12.4.1 Git分支策略

1. **分支命名规范**：
   - 主分支：`master`/`main`（保护分支，只能通过PR合并）
   - 开发分支：`develop`（开发集成分支，日常开发合并于此）
   - 功能分支：`feature/功能名称`（如 `feature/bubble-interaction`）
   - 修复分支：`bugfix/问题描述或ID`（如 `bugfix/login-crash`）
   - 发布分支：`release/版本号`（如 `release/v2.0.1`）
   - 热修复分支：`hotfix/问题描述或ID`（如 `hotfix/payment-error`）

2. **提交规范**：
   - 提交信息格式：`类型(范围): 描述`
   - 类型：feat(新功能)、fix(修复)、docs(文档)、style(样式)、refactor(重构)、perf(性能)、test(测试)、chore(构建/工具)
   - 范围：可选，表示修改影响的范围
   - 示例：`feat(bubble): 实现泡泡合并动画效果`、`fix(login): 修复登录弹窗在iOS下的显示问题`

3. **工作流程**：
   - 从 `develop` 分支创建功能分支
   - 在功能分支开发并提交
   - 完成后创建PR到 `develop` 分支
   - 通过代码评审后合并
   - 发布前将 `develop` 合并到 `release` 分支
   - 测试无误后将 `release` 合并到 `master`/`main`

#### 12.4.2 代码审查

1. **审查流程**：
   - 所有PR必须至少有1位审核人通过
   - 核心组件和页面的PR需要至少2位审核人通过
   - 涉及架构和性能的变更需要架构师审核

2. **审查标准**：
   - 代码风格是否符合项目规范
   - 功能实现是否符合需求
   - 是否有潜在的性能问题
   - 是否有足够的单元测试覆盖
   - 是否有冗余或重复代码

3. **审查时间**：
   - 常规PR审查应在24小时内完成
   - 紧急PR审查应在4小时内完成

#### 12.4.3 合并冲突解决

1. **预防措施**：
   - 每日拉取 `develop` 分支最新代码
   - 较长周期的功能开发应定期合并 `develop` 分支

2. **解决流程**：
   - 发生冲突时，冲突文件所在模块的主要负责人协调解决
   - 复杂冲突召集相关开发人员共同解决
   - 解决后由高级开发人员或架构师复核

### 12.5 部署与发布流程

#### 12.5.1 部署流程

1. **环境说明**：
   - 开发环境：开发人员本地环境，使用Mock服务
   - 测试环境：团队内部测试使用，连接测试服务器
   - 预发布环境：功能验收和预上线测试，连接预发布服务器
   - 生产环境：最终用户使用环境，连接生产服务器

2. **部署步骤**：
   ```bash
   # 1. 切换到对应环境的构建命令
   npm run build:test  # 或 build:preview 或 build

   # 2. 生成的代码包位于 dist/ 目录

   # 3. 上传到对应环境
   npm run deploy:test  # 或 deploy:preview 或 deploy
   ```

3. **部署频率**：
   - 测试环境：每日构建，完成PR后自动部署
   - 预发布环境：每周三下午部署下周发布内容
   - 生产环境：每周一上午10:00定时发布

#### 12.5.2 发布前检查清单

1. **代码质量检查**：
   - ESLint 检查通过无错误和警告
   - TypeScript 类型检查通过
   - 单元测试全部通过，覆盖率达到要求（≥80%）

2. **功能测试检查**：
   - 核心功能测试用例全部通过
   - 浏览器兼容性测试通过
   - 移动设备适配测试通过

3. **性能检查**：
   - 首屏加载时间 ≤2秒
   - 响应时间 ≤300ms
   - 内存占用正常

4. **安全检查**：
   - 敏感信息处理符合规范
   - API接口调用安全检查

5. **小程序特有检查**：
   - 包体积检查（主包 ≤2MB）
   - 分包检查（单分包 ≤2MB）
   - 微信开发者工具测试无警告

#### 12.5.3 发布与回滚策略

1. **发布流程**：
   - 创建发布申请，包含版本号、功能清单、测试报告
   - 项目经理审批发布申请
   - 执行发布命令，上传到微信小程序平台
   - 提交审核，等待微信审核通过
   - 灰度发布（10%→50%→100%用户）

2. **回滚策略**：
   - 发现严重问题立即回滚到上一稳定版本
   - 回滚由项目经理批准，技术负责人执行
   - 回滚后1小时内必须组织问题分析会议
   - 修复问题后按加急流程重新发布

3. **紧急修复流程**：
   - 从 `master`/`main` 分支创建 `hotfix` 分支
   - 修复问题并提交PR
   - 通过代码审查后合并回 `master`/`main` 和 `develop`
   - 创建紧急发布申请，按加急流程发布

### 12.6 技术实现指南

#### 12.6.1 核心功能技术方案

1. **泡泡交互系统实现**：
   - 技术选型：使用Canvas 2D API实现
   - 渲染优化：使用离屏Canvas预渲染技术
   - 动画框架：自定义帧动画系统，封装在 `utils/animation.js`
   - 性能目标：稳定60fps，CPU使用率<20%
   - 关键代码示例：
   ```javascript
   // 使用离屏Canvas预渲染泡泡
   function createBubbleTexture(size, color, gradient) {
     const offscreenCanvas = wx.createOffscreenCanvas({ type: '2d', width: size, height: size });
     const ctx = offscreenCanvas.getContext('2d');

     // 绘制泡泡
     const rad = size / 2;
     const grd = ctx.createLinearGradient(0, 0, size, size);
     grd.addColorStop(0, gradient[0]);
     grd.addColorStop(1, gradient[1]);

     ctx.beginPath();
     ctx.arc(rad, rad, rad - 2, 0, Math.PI * 2);
     ctx.fillStyle = grd;
     ctx.fill();

     // 添加高光效果
     ctx.beginPath();
     ctx.arc(rad * 0.7, rad * 0.7, rad * 0.15, 0, Math.PI * 2);
     ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
     ctx.fill();

     return offscreenCanvas;
   }
   ```

2. **瀑布流列表实现**：
   - 技术选型：虚拟列表 + 图片延迟加载
   - 核心组件：`components/waterfall-list`
   - 性能优化：基于可见区域检测的动态渲染
   - 关键实现：
   ```javascript
   /**
    * 瀑布流布局算法
    * @param {Array} items - 数据项列表
    * @param {Number} columnCount - 列数
    * @param {Number} columnGap - 列间距
    * @param {Number} containerWidth - 容器宽度
    * @returns {Object} 布局信息
    */
   function calculateWaterfallLayout(items, columnCount, columnGap, containerWidth) {
     const columnWidth = (containerWidth - columnGap * (columnCount - 1)) / columnCount;
     const columnsHeight = new Array(columnCount).fill(0);
     const positions = [];

     items.forEach(item => {
       // 找到当前最短的列
       const minHeight = Math.min(...columnsHeight);
       const columnIndex = columnsHeight.indexOf(minHeight);

       // 计算位置
       const ratio = item.height / item.width;
       const height = columnWidth * ratio;

       positions.push({
         x: columnIndex * (columnWidth + columnGap),
         y: columnsHeight[columnIndex],
         width: columnWidth,
         height: height
       });

       // 更新列高度
       columnsHeight[columnIndex] += height + 10; // 10是行间距
     });

     return {
       positions,
       contentHeight: Math.max(...columnsHeight)
     };
   }
   ```

3. **学习计划创建流程**：
   - 技术实现：基于状态机模型的多步骤表单
   - 状态管理：使用 `utils/state-manager.js`
   - 数据验证：使用 `utils/validators.js`
   - 结构示例：
   ```
   - 步骤1：选择主题与模板
   - 步骤2：定制学习目标与周期
   - 步骤3：选择标签与内容偏好
   - 步骤4：AI生成计划预览
   - 步骤5：确认与调整
   ```

#### 12.6.2 组件复用与状态管理

1. **组件复用策略**：
   - 原子组件：`components/base/` 目录，如按钮、输入框等
   - 分子组件：`components/common/` 目录，如卡片、列表项等
   - 模板组件：`components/templates/` 目录，如列表页、详情页等
   - 业务组件：`components/business/` 目录，如学习计划卡、泡泡组件等

2. **状态管理**：
   - 页面级状态：使用页面的 `data` 和 `setData`
   - 全局状态：使用 `utils/store.js` 封装的状态管理系统
   - 使用示例：
   ```javascript
   // 在app.js中初始化
   import Store from './utils/store';
   App({
     store: new Store({
       userInfo: null,
       currentPlan: null,
       themeMode: 'light'
     }),
     onLaunch() { /* ... */ }
   });

   // 在页面中使用
   const app = getApp();
   Page({
     onLoad() {
       // 订阅全局状态变化
       app.store.subscribe('userInfo', this.handleUserInfoChange.bind(this));
       // 获取全局状态
       const userInfo = app.store.get('userInfo');
     },
     handleUserInfoChange(newUserInfo) {
       this.setData({ userInfo: newUserInfo });
     },
     updateUserInfo(newInfo) {
       // 更新全局状态
       app.store.set('userInfo', newInfo);
     }
   });
   ```

#### 12.6.3 Canvas性能优化指南

1. **关键优化策略**：
   - 使用离屏Canvas预渲染静态元素
   - 实现对象池管理重复使用的图形对象
   - 使用图层策略分离动态元素和静态元素
   - 应用视窗裁剪，只渲染视口内元素
   - 使用requestAnimationFrame保证平滑动画

2. **性能分析与监测**：
   - 使用 `wx.getPerformance()` API监测
   - 关键指标：
     - 渲染帧率（目标：≥45fps）
     - CPU使用率（目标：≤20%）
     - 内存使用量（目标：≤50MB）
   - 监测代码示例：
   ```javascript
   const performance = wx.getPerformance();
   const observer = performance.createObserver((entryList) => {
     const entry = entryList.getEntries()[0];
     console.log(`帧率: ${1000 / entry.duration} FPS`);
   });
   observer.observe({ entryTypes: ['render'] });
   ```

### 12.7 问题跟踪与反馈

#### 12.7.1 Bug跟踪流程

1. **Bug分类与优先级**：
   - 紧急（P0）：影响核心功能，阻塞用户操作，24小时内解决
   - 高（P1）：影响主要功能，有替代方案，3天内解决
   - 中（P2）：影响次要功能，1周内解决
   - 低（P3）：体验问题，2周内解决

2. **跟踪工具与流程**：
   - 使用JIRA进行Bug跟踪
   - Bug提交必填信息：概述、复现步骤、期望结果、实际结果、环境信息
   - Bug生命周期：New → In Progress → Testing → Resolved → Closed
   - 每日Bug跟进会议：9:45-10:00，紧接站会后进行

#### 12.7.2 进度跟踪

1. **任务管理**：
   - 使用JIRA任务板管理开发任务
   - 每周一进行需求评审和任务分配
   - 每周五进行任务完成情况审核

2. **进度报告**：
   - 每周五下班前提交周进度报告
   - 报告内容包括：本周完成任务、遇到的问题、下周计划、风险提示
   - 进度报告模板见《前端系统升级综合规划.md》第12.3节

#### 12.7.3 阻塞问题升级流程

1. **阻塞问题定义**：
   - 前后端接口不一致导致功能无法实现
   - 接口性能问题导致功能无法正常使用
   - 接口逻辑变更导致需求实现方案改变

2. **升级处理流程**：
   - 一级处理：开发人员在"前后端协作"群直接沟通，@后端对接人
   - 二级升级：前端负责人与后端负责人协商解决，24小时未解决则升级
   - 三级升级：技术经理主持专项会议，必要时产品经理参与决策

*文档结束*