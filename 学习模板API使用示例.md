# 学习模板API使用示例

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-15 |
| 最后更新 | 2025-05-15 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档提供了AIBUBB项目中学习模板API的使用示例，帮助开发人员理解如何使用这些API。学习模板API支持创建、获取、更新、删除、恢复、发布、归档和评分等操作，是学习模板领域的核心功能。

## 2. API基础信息

- **基础URL**: `/api/v2/learning-templates`
- **认证要求**: 大多数操作需要JWT认证
- **响应格式**: JSON
- **版本**: V2（支持软删除和恢复功能）

## 3. 常见操作示例

### 3.1 创建学习模板

```javascript
/**
 * 创建学习模板
 * @param {Object} templateData - 学习模板数据
 * @returns {Promise<Object>} - 创建的学习模板
 */
async function createLearningTemplate(templateData) {
  try {
    const response = await fetch('/api/v2/learning-templates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(templateData)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '创建学习模板失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('创建学习模板时出错:', error);
    throw error;
  }
}

// 使用示例
const newTemplate = {
  themeId: 1,
  title: '高效沟通技巧',
  description: '学习如何在各种场景下进行有效沟通',
  coverImageUrl: 'https://example.com/images/communication.jpg',
  difficulty: 'intermediate',
  estimatedDays: 14,
  dailyGoalMinutes: 30,
  isOfficial: false,
  price: 0,
  tagIds: [5, 8, 12]
};

createLearningTemplate(newTemplate)
  .then(template => {
    console.log('学习模板创建成功:', template);
    // 处理创建成功的模板
  })
  .catch(error => {
    // 处理错误
  });
```

### 3.2 获取学习模板

```javascript
/**
 * 获取单个学习模板
 * @param {number} templateId - 学习模板ID
 * @param {boolean} withDeleted - 是否包含已删除的模板
 * @returns {Promise<Object>} - 学习模板
 */
async function getLearningTemplate(templateId, withDeleted = false) {
  try {
    const url = `/api/v2/learning-templates/${templateId}${withDeleted ? '?withDeleted=true' : ''}`;
    const response = await fetch(url);
    
    if (response.status === 404) {
      return null;
    }
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取学习模板失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取学习模板时出错:', error);
    throw error;
  }
}

// 使用示例
getLearningTemplate(123)
  .then(template => {
    if (template) {
      console.log('获取到的学习模板:', template);
      // 处理获取到的模板
    } else {
      console.log('学习模板不存在');
    }
  })
  .catch(error => {
    // 处理错误
  });
```

### 3.3 获取学习模板列表

```javascript
/**
 * 获取学习模板列表
 * @param {Object} filters - 过滤条件
 * @returns {Promise<Array>} - 学习模板列表
 */
async function getLearningTemplates(filters = {}) {
  try {
    // 构建查询参数
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    const url = `/api/v2/learning-templates?${queryParams.toString()}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取学习模板列表失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取学习模板列表时出错:', error);
    throw error;
  }
}

// 使用示例 - 获取特定主题的官方模板
getLearningTemplates({
  themeId: 2,
  isOfficial: true,
  limit: 10,
  offset: 0
})
  .then(templates => {
    console.log(`获取到${templates.length}个学习模板`);
    // 处理模板列表
  })
  .catch(error => {
    // 处理错误
  });

// 使用示例 - 获取特定标签的模板
getLearningTemplates({
  tagId: 5,
  limit: 20
})
  .then(templates => {
    console.log(`获取到${templates.length}个学习模板`);
    // 处理模板列表
  })
  .catch(error => {
    // 处理错误
  });
```

### 3.4 搜索学习模板

```javascript
/**
 * 搜索学习模板
 * @param {string} keyword - 搜索关键字
 * @param {Object} options - 搜索选项
 * @returns {Promise<Array>} - 搜索结果
 */
async function searchLearningTemplates(keyword, options = {}) {
  try {
    // 构建查询参数
    const queryParams = new URLSearchParams({ keyword });
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    const url = `/api/v2/learning-templates/search?${queryParams.toString()}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '搜索学习模板失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('搜索学习模板时出错:', error);
    throw error;
  }
}

// 使用示例
searchLearningTemplates('沟通', {
  difficulty: 'beginner',
  limit: 10
})
  .then(templates => {
    console.log(`搜索到${templates.length}个学习模板`);
    // 处理搜索结果
  })
  .catch(error => {
    // 处理错误
  });
```

### 3.5 更新学习模板

```javascript
/**
 * 更新学习模板
 * @param {number} templateId - 学习模板ID
 * @param {Object} updateData - 更新数据
 * @returns {Promise<Object>} - 更新后的学习模板
 */
async function updateLearningTemplate(templateId, updateData) {
  try {
    const response = await fetch(`/api/v2/learning-templates/${templateId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(updateData)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '更新学习模板失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('更新学习模板时出错:', error);
    throw error;
  }
}

// 使用示例
updateLearningTemplate(123, {
  title: '高级沟通技巧',
  description: '学习如何在各种复杂场景下进行有效沟通',
  difficulty: 'advanced',
  tagIds: [5, 8, 12, 15]
})
  .then(template => {
    console.log('学习模板更新成功:', template);
    // 处理更新后的模板
  })
  .catch(error => {
    // 处理错误
  });
```

### 3.6 删除学习模板（软删除）

```javascript
/**
 * 删除学习模板（软删除）
 * @param {number} templateId - 学习模板ID
 * @returns {Promise<void>}
 */
async function deleteLearningTemplate(templateId) {
  try {
    const response = await fetch(`/api/v2/learning-templates/${templateId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '删除学习模板失败');
    }
  } catch (error) {
    console.error('删除学习模板时出错:', error);
    throw error;
  }
}

// 使用示例
deleteLearningTemplate(123)
  .then(() => {
    console.log('学习模板删除成功');
    // 处理删除成功
  })
  .catch(error => {
    // 处理错误
  });
```

### 3.7 恢复学习模板

```javascript
/**
 * 恢复学习模板
 * @param {number} templateId - 学习模板ID
 * @returns {Promise<Object>} - 恢复后的学习模板
 */
async function restoreLearningTemplate(templateId) {
  try {
    const response = await fetch(`/api/v2/learning-templates/${templateId}/restore`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '恢复学习模板失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('恢复学习模板时出错:', error);
    throw error;
  }
}

// 使用示例
restoreLearningTemplate(123)
  .then(template => {
    console.log('学习模板恢复成功:', template);
    // 处理恢复后的模板
  })
  .catch(error => {
    // 处理错误
  });
```

## 4. 错误处理

所有API调用都应该包含适当的错误处理。常见的错误包括：

- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未授权（未登录或token无效）
- **404 Not Found**: 资源不存在
- **500 Internal Server Error**: 服务器内部错误

```javascript
// 错误处理示例
async function apiCall(url, options = {}) {
  try {
    const response = await fetch(url, options);
    
    if (response.status === 401) {
      // 处理未授权错误，可能需要重新登录
      redirectToLogin();
      return;
    }
    
    if (response.status === 404) {
      // 处理资源不存在错误
      return null;
    }
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `API调用失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API调用出错:', error);
    // 可以根据需要显示错误消息给用户
    showErrorToUser(error.message);
    throw error;
  }
}
```

## 5. 最佳实践

1. **使用类型定义**: 如果使用TypeScript，为API请求和响应定义类型
2. **集中管理API调用**: 创建专门的服务类或模块处理API调用
3. **缓存常用数据**: 考虑缓存不经常变化的数据，如模板列表
4. **分页处理**: 处理大量数据时使用分页
5. **错误处理**: 实现全局错误处理机制
6. **加载状态**: 在API调用期间显示加载状态
7. **乐观更新**: 在等待API响应的同时先更新UI

## 6. 完整示例

以下是一个完整的学习模板服务类示例：

```javascript
// learningTemplateService.js
class LearningTemplateService {
  constructor(baseUrl = '/api/v2/learning-templates') {
    this.baseUrl = baseUrl;
  }
  
  async getToken() {
    // 从localStorage或其他存储中获取token
    return localStorage.getItem('auth_token');
  }
  
  async createTemplate(templateData) {
    return this._fetchWithAuth(`${this.baseUrl}`, {
      method: 'POST',
      body: JSON.stringify(templateData)
    });
  }
  
  async getTemplate(id, withDeleted = false) {
    const url = `${this.baseUrl}/${id}${withDeleted ? '?withDeleted=true' : ''}`;
    return this._fetch(url);
  }
  
  async getTemplates(filters = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    return this._fetch(`${this.baseUrl}?${queryParams.toString()}`);
  }
  
  async searchTemplates(keyword, options = {}) {
    const queryParams = new URLSearchParams({ keyword });
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    return this._fetch(`${this.baseUrl}/search?${queryParams.toString()}`);
  }
  
  async updateTemplate(id, updateData) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  }
  
  async deleteTemplate(id) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}`, {
      method: 'DELETE'
    });
  }
  
  async restoreTemplate(id) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/restore`, {
      method: 'POST'
    });
  }
  
  async publishTemplate(id) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/publish`, {
      method: 'POST'
    });
  }
  
  async archiveTemplate(id) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/archive`, {
      method: 'POST'
    });
  }
  
  async addRating(id, rating, userId) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/rating`, {
      method: 'POST',
      body: JSON.stringify({ rating, userId })
    });
  }
  
  async _fetch(url, options = {}) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });
      
      if (response.status === 404) {
        return null;
      }
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `API调用失败: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API调用出错:', error);
      throw error;
    }
  }
  
  async _fetchWithAuth(url, options = {}) {
    const token = await this.getToken();
    return this._fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`
      }
    });
  }
}

// 使用示例
const templateService = new LearningTemplateService();

// 获取官方模板
templateService.getTemplates({ isOfficial: true, limit: 10 })
  .then(templates => {
    console.log('官方模板:', templates);
  })
  .catch(error => {
    console.error('获取官方模板失败:', error);
  });
```
