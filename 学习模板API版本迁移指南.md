# 学习模板API版本迁移指南

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-15 |
| 最后更新 | 2025-05-15 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档提供了AIBUBB项目中学习模板API从V1版本迁移到V2版本的指南。V2版本引入了软删除、恢复、批量操作等新功能，并优化了架构和错误处理。本指南将帮助开发人员平滑地将客户端代码从V1版本升级到V2版本。

## 2. 版本差异

### 2.1 功能差异

| 功能 | V1版本 | V2版本 |
|------|--------|--------|
| 基础CRUD操作 | ✅ | ✅ |
| 软删除功能 | ❌ | ✅ |
| 恢复已删除资源 | ❌ | ✅ |
| 查看已删除资源 | ❌ | ✅ |
| 发布/归档功能 | ❌ | ✅ |
| 评分功能 | ❌ | ✅ |
| 标签关联 | 部分支持 | ✅ |

### 2.2 实现差异

| 特性 | V1版本 | V2版本 |
|------|--------|--------|
| 控制器实现 | 直接实现 | 使用依赖注入 |
| 架构风格 | 混合 | 分层架构（DDD） |
| 错误处理 | 基础 | 增强 |
| 参数验证 | 基础 | 增强 |
| 响应格式 | 标准格式 | 标准格式（增加软删除信息） |

### 2.3 URL路径差异

V1版本和V2版本的API使用不同的URL路径前缀：

- V1版本：`/api/v1/learning-templates`
- V2版本：`/api/v2/learning-templates`

## 3. 端点对比

### 3.1 基础CRUD操作

| 操作 | V1版本 | V2版本 | 差异 |
|------|--------|--------|------|
| 创建模板 | `POST /api/v1/learning-templates` | `POST /api/v2/learning-templates` | V2版本支持标签关联 |
| 获取模板 | `GET /api/v1/learning-templates/:id` | `GET /api/v2/learning-templates/:id` | V2版本支持`withDeleted`参数 |
| 更新模板 | `PUT /api/v1/learning-templates/:id` | `PUT /api/v2/learning-templates/:id` | V2版本支持标签关联更新 |
| 删除模板 | `DELETE /api/v1/learning-templates/:id` | `DELETE /api/v2/learning-templates/:id` | V1版本是硬删除，V2版本是软删除 |
| 获取模板列表 | `GET /api/v1/learning-templates` | `GET /api/v2/learning-templates` | V2版本支持更多过滤参数 |

### 3.2 V2版本新增端点

| 操作 | 端点 | 说明 |
|------|------|------|
| 恢复模板 | `POST /api/v2/learning-templates/:id/restore` | 恢复已软删除的模板 |
| 发布模板 | `POST /api/v2/learning-templates/:id/publish` | 将模板状态设置为已发布 |
| 归档模板 | `POST /api/v2/learning-templates/:id/archive` | 将模板状态设置为已归档 |
| 添加评分 | `POST /api/v2/learning-templates/:id/rating` | 为模板添加评分 |
| 搜索模板 | `GET /api/v2/learning-templates/search` | 搜索模板 |

## 4. 请求和响应示例

### 4.1 创建模板

#### V1版本

请求：
```http
POST /api/v1/learning-templates
Authorization: Bearer {token}
Content-Type: application/json

{
  "themeId": 1,
  "title": "高效沟通技巧",
  "description": "学习如何在各种场景下进行有效沟通",
  "coverImageUrl": "https://example.com/images/communication.jpg",
  "difficulty": "intermediate",
  "estimatedDays": 14,
  "dailyGoalMinutes": 30
}
```

响应：
```json
{
  "success": true,
  "data": {
    "id": 123,
    "themeId": 1,
    "title": "高效沟通技巧",
    "description": "学习如何在各种场景下进行有效沟通",
    "coverImageUrl": "https://example.com/images/communication.jpg",
    "difficulty": "intermediate",
    "estimatedDays": 14,
    "dailyGoalMinutes": 30,
    "createdAt": "2025-05-15T10:00:00Z",
    "updatedAt": "2025-05-15T10:00:00Z"
  }
}
```

#### V2版本

请求：
```http
POST /api/v2/learning-templates
Authorization: Bearer {token}
Content-Type: application/json

{
  "themeId": 1,
  "title": "高效沟通技巧",
  "description": "学习如何在各种场景下进行有效沟通",
  "coverImageUrl": "https://example.com/images/communication.jpg",
  "difficulty": "intermediate",
  "estimatedDays": 14,
  "dailyGoalMinutes": 30,
  "isOfficial": false,
  "price": 0,
  "tagIds": [5, 8, 12]
}
```

响应：
```json
{
  "id": 123,
  "themeId": 1,
  "title": "高效沟通技巧",
  "description": "学习如何在各种场景下进行有效沟通",
  "coverImageUrl": "https://example.com/images/communication.jpg",
  "difficulty": "intermediate",
  "estimatedDays": 14,
  "dailyGoalMinutes": 30,
  "isOfficial": false,
  "creatorId": "user123",
  "popularity": 0,
  "rating": 5.0,
  "ratingCount": 0,
  "price": 0,
  "status": "draft",
  "createdAt": "2025-05-15T10:00:00Z",
  "updatedAt": "2025-05-15T10:00:00Z",
  "deletedAt": null,
  "tagIds": [5, 8, 12]
}
```

### 4.2 删除模板

#### V1版本（硬删除）

请求：
```http
DELETE /api/v1/learning-templates/123
Authorization: Bearer {token}
```

响应：
```json
{
  "success": true,
  "message": "学习模板已删除"
}
```

#### V2版本（软删除）

请求：
```http
DELETE /api/v2/learning-templates/123
Authorization: Bearer {token}
```

响应：
```
204 No Content
```

## 5. 迁移步骤

### 5.1 基础URL更新

将所有API请求的基础URL从`/api/v1/learning-templates`更新为`/api/v2/learning-templates`。

```javascript
// 旧版本
const API_BASE_URL = '/api/v1/learning-templates';

// 新版本
const API_BASE_URL = '/api/v2/learning-templates';
```

### 5.2 响应处理更新

V2版本的响应格式与V1版本不同，需要更新响应处理逻辑。

```javascript
// 旧版本
async function getTemplate(id) {
  const response = await fetch(`/api/v1/learning-templates/${id}`);
  const data = await response.json();
  return data.success ? data.data : null;
}

// 新版本
async function getTemplate(id) {
  const response = await fetch(`/api/v2/learning-templates/${id}`);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || '获取学习模板失败');
  }
  return await response.json();
}
```

### 5.3 删除操作更新

V1版本的删除是硬删除，V2版本是软删除，需要更新删除操作的处理逻辑。

```javascript
// 旧版本
async function deleteTemplate(id) {
  const response = await fetch(`/api/v1/learning-templates/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${getToken()}`
    }
  });
  const data = await response.json();
  return data.success;
}

// 新版本
async function deleteTemplate(id) {
  const response = await fetch(`/api/v2/learning-templates/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${getToken()}`
    }
  });
  return response.status === 204;
}
```

### 5.4 添加新功能支持

为V2版本新增的功能添加支持，如恢复、发布、归档和评分等。

```javascript
// 恢复模板
async function restoreTemplate(id) {
  const response = await fetch(`/api/v2/learning-templates/${id}/restore`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${getToken()}`
    }
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || '恢复学习模板失败');
  }
  return await response.json();
}

// 发布模板
async function publishTemplate(id) {
  const response = await fetch(`/api/v2/learning-templates/${id}/publish`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${getToken()}`
    }
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || '发布学习模板失败');
  }
  return await response.json();
}
```

## 6. 完整迁移示例

以下是一个完整的迁移示例，展示了如何从V1版本迁移到V2版本：

```javascript
// 旧版本（V1）
class LearningTemplateServiceV1 {
  constructor() {
    this.baseUrl = '/api/v1/learning-templates';
  }
  
  async getToken() {
    return localStorage.getItem('auth_token');
  }
  
  async createTemplate(templateData) {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await this.getToken()}`
      },
      body: JSON.stringify(templateData)
    });
    
    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || '创建学习模板失败');
    }
    
    return data.data;
  }
  
  async getTemplate(id) {
    const response = await fetch(`${this.baseUrl}/${id}`);
    const data = await response.json();
    return data.success ? data.data : null;
  }
  
  async getTemplates(filters = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    const response = await fetch(`${this.baseUrl}?${queryParams.toString()}`);
    const data = await response.json();
    return data.success ? data.data : [];
  }
  
  async updateTemplate(id, updateData) {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await this.getToken()}`
      },
      body: JSON.stringify(updateData)
    });
    
    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || '更新学习模板失败');
    }
    
    return data.data;
  }
  
  async deleteTemplate(id) {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${await this.getToken()}`
      }
    });
    
    const data = await response.json();
    return data.success;
  }
}

// 新版本（V2）
class LearningTemplateServiceV2 {
  constructor() {
    this.baseUrl = '/api/v2/learning-templates';
  }
  
  async getToken() {
    return localStorage.getItem('auth_token');
  }
  
  async createTemplate(templateData) {
    return this._fetchWithAuth(`${this.baseUrl}`, {
      method: 'POST',
      body: JSON.stringify(templateData)
    });
  }
  
  async getTemplate(id, withDeleted = false) {
    const url = `${this.baseUrl}/${id}${withDeleted ? '?withDeleted=true' : ''}`;
    return this._fetch(url);
  }
  
  async getTemplates(filters = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    return this._fetch(`${this.baseUrl}?${queryParams.toString()}`);
  }
  
  async searchTemplates(keyword, options = {}) {
    const queryParams = new URLSearchParams({ keyword });
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value);
      }
    });
    
    return this._fetch(`${this.baseUrl}/search?${queryParams.toString()}`);
  }
  
  async updateTemplate(id, updateData) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  }
  
  async deleteTemplate(id) {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${await this.getToken()}`
      }
    });
    
    return response.status === 204;
  }
  
  async restoreTemplate(id) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/restore`, {
      method: 'POST'
    });
  }
  
  async publishTemplate(id) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/publish`, {
      method: 'POST'
    });
  }
  
  async archiveTemplate(id) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/archive`, {
      method: 'POST'
    });
  }
  
  async addRating(id, rating, userId) {
    return this._fetchWithAuth(`${this.baseUrl}/${id}/rating`, {
      method: 'POST',
      body: JSON.stringify({ rating, userId })
    });
  }
  
  async _fetch(url, options = {}) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });
      
      if (response.status === 404) {
        return null;
      }
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `API调用失败: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API调用出错:', error);
      throw error;
    }
  }
  
  async _fetchWithAuth(url, options = {}) {
    const token = await this.getToken();
    return this._fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`
      }
    });
  }
}
```

## 7. 迁移检查清单

- [ ] 更新所有API请求的基础URL
- [ ] 更新响应处理逻辑
- [ ] 更新删除操作的处理逻辑
- [ ] 添加对新功能的支持（恢复、发布、归档、评分等）
- [ ] 更新错误处理逻辑
- [ ] 更新UI以支持新功能
- [ ] 测试所有API调用
- [ ] 更新文档和注释

## 8. 常见问题

### 8.1 如何处理已删除的模板？

V2版本支持软删除，可以通过`withDeleted`参数查看已删除的模板，并通过`restore`端点恢复已删除的模板。

```javascript
// 获取已删除的模板
const deletedTemplate = await templateService.getTemplate(123, true);

// 恢复已删除的模板
if (deletedTemplate && deletedTemplate.deletedAt) {
  await templateService.restoreTemplate(123);
}
```

### 8.2 如何处理模板状态？

V2版本引入了模板状态（草稿、已发布、已归档），可以通过`publish`和`archive`端点管理模板状态。

```javascript
// 发布模板
await templateService.publishTemplate(123);

// 归档模板
await templateService.archiveTemplate(123);
```

### 8.3 如何处理标签关联？

V2版本支持在创建和更新模板时关联标签，通过`tagIds`参数指定要关联的标签ID列表。

```javascript
// 创建带标签的模板
const newTemplate = {
  // ...其他属性
  tagIds: [5, 8, 12]
};
await templateService.createTemplate(newTemplate);

// 更新模板标签
await templateService.updateTemplate(123, {
  // ...其他属性
  tagIds: [5, 8, 12, 15]
});
```
