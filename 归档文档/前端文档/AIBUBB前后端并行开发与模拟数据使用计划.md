# AIBUBB前后端并行开发与模拟数据使用计划

## 一、背景与目标

### 1.1 项目背景
AIBUBB（计划改名NebulaLearn）是一个基于五层结构（主题→学习模板→学习计划→标签→内容）的综合学习生态系统，专注于提升用户的人际沟通能力。项目采用领域驱动设计(DDD)架构和API-First设计原则，已完成数据库V3.0升级。目前项目处于前后端并行开发阶段，需要一个清晰的计划来确保两个团队能够独立工作并最终顺利融合。

### 1.2 目标
- 实现前后端团队并行开发，互不干扰
- 确保前端开发使用的模拟数据与后端实际数据结构保持一致
- 为未来前后端融合做好准备，降低集成成本
- 建立清晰的数据流转规则和接口规范

### 1.3 核心业务模型

#### 1.3.1 五层结构关系

```
主题(Theme) → 学习模板(LearningTemplate) → 学习计划(LearningPlan) → 标签(Tag) → 内容(Content)
```

- **主题(Theme)**：最高层分类，定义学习的大方向（如“人际沟通”、“职场技能”）
- **学习模板(LearningTemplate)**：预设的学习路径框架，包含标准标签集和内容组织，由专业设计，确保学习路径的科学性和系统性
- **学习计划(LearningPlan)**：用户个性化的学习实例，可基于模板创建或完全自定义，包含具体的学习目标、时间安排和进度跟踪
- **标签(Tag)**：知识点单元，连接学习计划和具体内容，形成知识网络，支持内容的分类和关联
- **内容(Content)**：具体的学习材料，分为三种形式

#### 1.3.2 三种内容形式

- **练习(Exercise)**：转化知识为行动，强化实践能力。包含任务描述、执行指南、完成标准和反馈机制，需要用户主动参与并产生输出。
- **观点(Insight)**：精炼的思想启发，促进认知转变。简洁有力的核心观点，可能附带来源和背景解释，引发思考而无需复杂操作。
- **笔记(Note)**：系统化知识呈现，支持深度理解。由标题、内容、配图组成的微型文章，提供沉浸式阅读体验，支持社区互动。

## 二、前后端分离策略

### 2.1 技术栈与架构

#### 2.1.1 前端技术栈
- **框架**：微信小程序原生框架
- **UI组件**：自定义组件库
- **状态管理**：页面数据 + 全局数据
- **网络请求**：wx.request / 封装的API层

#### 2.1.2 后端技术栈
- **语言/框架**：Node.js + Express
- **数据库**：MySQL
- **缓存**：Redis
- **架构**：领域驱动设计(DDD)
- **API设计**：API-First设计
- **API文档**：Swagger
- **容器化**：Docker + docker-compose
- **测试**：Jest + Supertest

### 2.2 开发环境隔离
- **前端环境**：独立的开发服务器，使用模拟API服务
- **后端环境**：专注于DDD架构实现，提供API文档和接口规范
- **共享资源**：API接口规范文档、数据模型定义、数据库结构V3.0文档、AIBUBB前后端融合桥梁文档

### 2.3 代码仓库管理
- **代码组织**：维持单一代码仓库，但前后端代码明确分离
- **分支策略**：
  - `main`：稳定主分支
  - `backend-dev`：后端开发分支
  - `frontend-dev`：前端开发分支
  - `feature/*`：具体功能分支
- **合并规则**：前端代码变更不应修改后端代码，反之亦然

### 2.4 开发原则
- **API-First设计**：先设计并文档化API，再进行实现
- **领域驱动设计**：后端采用DDD架构，将业务逻辑与技术实现分离
- **并行开发**：前端使用模拟数据独立开发，后端专注于DDD实现
- **渐进融合**：随着后端实现的成熟，逐步将前端从模拟数据迁移到真实API

### 2.5 沟通与协作机制
- 每周同步会议：周一上午10:00，讨论接口变更和数据结构调整
- 技术讨论群：AIBUBB开发团队（微信群）
- 文档共享：项目Wiki和桥梁文档
- 建立接口变更通知机制，确保及时同步信息

### 2.6 API变更流程
1. 后端团队提出API变更建议
2. 在周会上讨论并确认变更
3. 更新API文档和Swagger定义
4. 通知前端团队并更新模拟数据服务
5. 后端实现API变更
6. 前端适配新API
7. 联合测试变更

## 三、模拟数据规范

### 3.1 数据库结构V3.0特点

数据库采用V3.0设计，主要特点：

- 表名和字段名统一使用`snake_case`命名规范
- 用户表主键从VARCHAR(32)改为BIGINT AUTO_INCREMENT
- 添加deleted_at字段支持软删除
- 拆分JSON字段为独立表
- 采用聚合根设计，将相关实体分组
- 增强了关联关系的设计，支持复杂查询
- 添加了索引和约束，提高数据完整性和查询效率

### 3.2 数据结构规范
- 严格遵循数据库V3.0设计文档中的表结构和字段定义
- 所有字段名使用`snake_case`命名规范
- 模拟数据必须包含所有必填字段，可选字段保持一致性
- 关联ID必须有效且一致，确保数据完整性
- 所有时间字段使用ISO 8601格式（YYYY-MM-DDTHH:mm:ss.sssZ）

### 3.3 模拟数据范围
必须覆盖以下核心实体的完整数据：

1. **用户与认证**
   - 用户基本信息（user）
   - 用户设置（user_setting）
   - 用户通知设置（user_notification_setting）
   - 用户统计信息（user_statistics）

2. **核心层次**
   - 主题（theme）
   - 学习模板（learning_template）
   - 学习计划（learning_plan）
   - 标签分类（tag_category）
   - 标签（tag）
   - 模板标签关联（template_tag）
   - 计划标签关联（plan_tag）

3. **内容形式**
   - 练习（exercise）
   - 观点（insight）
   - 笔记（note）
   - 每日内容（daily_content）
   - 用户内容进度（user_content_progress）

4. **游戏化元素**
   - 成就（achievement）
   - 用户成就（user_achievement）
   - 等级（level）
   - 徒章（badge）
   - 用户徒章（user_badge）

### 3.3 数据量规范
为确保性能测试有效且开发体验良好：
- 每种实体类型至少10条记录
- 主要实体（主题、标签、内容）至少30条记录
- 关联数据保持合理比例，确保数据分布均衡

## 四、模拟数据实现方案

### 4.1 模拟数据生成
创建专门的模拟数据生成脚本，支持以下功能：
- 一键生成所有必要的模拟数据
- 支持增量生成特定类型的数据
- 支持导出为JSON文件和SQL脚本
- 数据之间保持正确的关联关系

**实现状态：已完成 ✅**

我们已经实现了完整的模拟数据生成脚本，包含以下功能：

- 生成所有核心实体的数据：用户、主题、标签、学习模板、学习计划、练习、观点、笔记、每日内容、成就和徽章
- 支持导出为JSON和SQL格式
- 数据之间保持正确的关联关系

```javascript
// 实现的模拟数据生成脚本结构
const generateMockData = {
  // 生成所有数据
  all: async () => {
    await generateMockData.users();
    await generateMockData.userSettings();
    await generateMockData.themes();
    await generateMockData.tagCategories();
    await generateMockData.tags();
    await generateMockData.learningTemplates();
    await generateMockData.learningPlans();
    await generateMockData.exercises();
    await generateMockData.insights();
    await generateMockData.notes();
    await generateMockData.dailyContents();
    await generateMockData.achievements();
    await generateMockData.badges();
  },

  // 生成用户数据
  users: async (count = 10) => {
    // 生成逻辑
  },

  // 生成主题数据
  themes: async (count = 5) => {
    // 生成逻辑
  },

  // 其他实体的生成方法...
};
```

脚本位于 `mock-data/scripts/generateMockData.js`，可以通过以下命令运行：

```bash
cd mock-data
node scripts/generateMockData.js
```

### 4.2 模拟API服务
实现一个轻量级的模拟API服务，提供与实际后端API相同的接口。

**实现状态：已完成 ✅**

我们已经实现了完整的模拟API服务，包含以下功能：

- 所有核心实体的API端点：主题、标签、学习计划、练习、观点、笔记、每日内容、广场、认证等
- 支持查询参数处理、分页、排序和筛选
- 实现了统一的错误处理和响应格式
- 支持软删除功能

```javascript
// 模拟API服务器实现结构
const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 3010;

app.use(cors());
app.use(express.json());

// 加载模拟数据
const mockData = require('../json');

// 通用错误处理
const errorHandler = (code, message) => ({
  success: false,
  error: { code, message }
});

// 通用成功响应
const successResponse = (data, message = '操作成功', meta = {}) => ({
  success: true,
  data,
  message,
  ...meta
});

// 主题相关API
app.get('/api/v1/themes', (req, res) => {
  try {
    // 处理查询参数
    const { is_active, sort_by, page = 1, pageSize = 20 } = req.query;
    let data = [...mockData.themes];

    // 应用筛选
    if (is_active !== undefined) {
      data = data.filter(t => t.is_active === (is_active === 'true'));
    }

    // 应用排序
    if (sort_by) {
      data.sort((a, b) => a[sort_by] - b[sort_by]);
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const paginatedData = data.slice(startIndex, startIndex + parseInt(pageSize));

    res.json(successResponse(paginatedData, '获取主题列表成功', {
      total: data.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }));
  } catch (error) {
    res.status(500).json(errorHandler('INTERNAL_SERVER_ERROR', '服务器内部错误'));
  }
});

// 其他API端点实现...
```

我们已经实现了以下所有核心API端点：

1. **主题相关API**
   - GET `/api/v1/themes` - 获取主题列表
   - GET `/api/v1/themes/:id` - 获取特定主题

2. **学习计划相关API**
   - GET `/api/v1/learning-plans` - 获取学习计划列表
   - GET `/api/v1/learning-plans/:id` - 获取特定学习计划
   - POST `/api/v1/learning-plans` - 创建学习计划
   - PUT `/api/v1/learning-plans/:id` - 更新学习计划
   - DELETE `/api/v1/learning-plans/:id` - 删除学习计划
   - PUT `/api/v1/learning-plans/:id/activate` - 激活学习计划
   - GET `/api/v1/learning-plans/system/default` - 获取系统默认学习计划

3. **练习相关API**
   - GET `/api/v1/exercises` - 获取练习列表
   - GET `/api/v1/exercises/:id` - 获取特定练习
   - POST `/api/v1/exercises/:id/complete` - 标记练习完成

4. **观点相关API**
   - GET `/api/v1/insights` - 获取观点列表
   - GET `/api/v1/insights/:id` - 获取特定观点
   - POST `/api/v1/insights/:id/read` - 标记观点已读

5. **笔记相关API**
   - GET `/api/v1/notes` - 获取笔记列表
   - GET `/api/v1/notes/:id` - 获取特定笔记
   - POST `/api/v1/notes` - 创建笔记
   - PUT `/api/v1/notes/:id` - 更新笔记
   - DELETE `/api/v1/notes/:id` - 删除笔记

6. **每日内容相关API**
   - GET `/api/v1/learning-plans/:planId/daily-content` - 获取每日内容

7. **广场相关API**
   - GET `/api/v1/square/notes` - 获取广场笔记
   - GET `/api/v1/square/tags` - 获取广场标签

8. **认证相关API**
   - POST `/api/v1/auth/login` - 登录

模拟API服务器位于 `mock-data/api/mockApiServer.js`，可以通过以下命令运行：

```bash
cd mock-data
node api/mockApiServer.js
```

服务器默认运行在 http://localhost:3010，提供与实际后端API相同的接口。

### 4.3 模拟数据存储
模拟数据将以多种形式存储，以适应不同的使用场景。

**实现状态：已完成 ✅**

我们已经实现了以下存储形式：

1. **JSON文件**：按实体类型分类存储，便于前端直接导入
   ```
   /mock-data/json/
     ├── users.json
     ├── user_settings.json
     ├── themes.json
     ├── tag_categories.json
     ├── tags.json
     ├── learning_templates.json
     ├── learning_plans.json
     ├── exercises.json
     ├── insights.json
     ├── notes.json
     ├── daily_contents.json
     ├── achievements.json
     └── badges.json
   ```

2. **SQL脚本**：用于快速初始化开发数据库
   ```sql
   -- 示例：主题数据
   INSERT INTO theme (id, name, english_name, description, icon, color, cover_image_url, sort_order, is_active)
   VALUES
   (1, '平台指南', 'Platform Guide', '了解如何使用NebulaLearn平台的各项功能', 'guide', '#4A90E2', 'https://example.com/images/platform-guide.jpg', 1, true),
   (2, '职场技能', 'Career Skills', '提升职场竞争力的必备技能', 'career', '#50E3C2', 'https://example.com/images/career-skills.jpg', 2, true);
   ```

3. **内存数据库**：用于模拟API服务，支持CRUD操作

所有模拟数据和脚本已经组织在 `mock-data` 目录下，并提供了简单的安装和使用指南。

目录结构：
```
mock-data/
  ├── api/                # 模拟API服务
  │   └── mockApiServer.js  # 模拟API服务器主文件
  ├── json/               # 生成的JSON格式模拟数据
  ├── scripts/            # 模拟数据生成脚本
  │   └── generateMockData.js  # 模拟数据生成主文件
  ├── sql/                # 生成的SQL格式模拟数据
  ├── package.json        # 项目依赖
  ├── setup.sh            # 安装和启动脚本
  └── README.md           # 说明文档
```

## 五、工作进度更新

### 5.1 当前完成情况

| 工作项 | 状态 | 备注 |
|---------|------|------|
| 模拟数据生成脚本 | ✅ 已完成 | 实现了所有核心实体的数据生成 |
| 模拟API服务 | ✅ 已完成 | 实现了所有核心API端点 |
| API客户端 | ✅ 已完成 | 实现了统一的API请求处理和数据转换 |
| UI组件升级 | 🔄 进行中 | 回收站组件和内容管理组件升级 |
| 数据处理层升级 | ⏳ 计划中 | 数据模型层和缓存策略优化 |
| 用户体验优化 | ⏳ 计划中 | 加载状态、错误反馈和操作确认机制 |
| 前端业务功能实现 | ⏳ 计划中 | 实现各个页面和功能模块 |

### 5.2 下一步计划

1. **UI组件升级**
   - 设计并实现回收站组件
   - 升级内容管理组件，添加软删除功能
   - 实现批量操作功能

2. **前端集成**
   - 修改前端API配置，指向模拟API服务
   - 测试前端与模拟API的交互
   - 根据前端需求调整模拟数据和API

3. **前端业务功能实现**
   - 实现用户中心相关功能
   - 实现学习计划管理功能
   - 实现内容浏览和交互功能
   - 实现数据统计和可视化功能

## 六、前端开发规范

### 6.1 API调用规范
前端代码必须通过统一的API服务层访问数据：

```javascript
// 使用新实现的API客户端
import api from 'utils/api';

// 获取主题列表
api.theme.getThemes({ page: 1, pageSize: 10 })
  .then(result => {
    console.log('主题列表:', result.data);
    console.log('总数:', result.meta.total);
  })
  .catch(error => {
    console.error('获取主题列表失败:', error.message);
  });

// 使用新实现的API客户端创建笔记
api.note.createNote({
  title: '我的学习笔记',
  content: '# 学习笔记\n\n这是我的学习笔记内容...',
  isPublic: true
})
  .then(result => {
    console.log('笔记创建成功:', result.data);
  })
  .catch(error => {
    console.error('创建笔记失败:', error.message);
  });
```

### 6.2 数据转换规范

前端使用camelCase命名规范，后端使用snake_case命名规范，数据转换由API客户端自动处理：

```javascript
// 前端代码使用camelCase
api.learningPlan.createLearningPlan({
  templateId: 1,
  title: '我的学习计划',
  isPublic: true
});

// 实际发送到服务器的数据是snake_case
// {
//   "template_id": 1,
//   "title": "我的学习计划",
//   "is_public": true
// }
```

### 6.3 组件化开发
前端开发采用组件化开发方式，将UI元素抽象为可复用的组件：

```javascript
// 回收站组件示例
const TrashBin = ({
  items,
  onRestore,
  onDelete,
  loading,
  error
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>回收站</Text>

      {loading && <Loading />}
      {error && <ErrorMessage message={error} />}

      {items.length === 0 ? (
        <EmptyState message="回收站为空" />
      ) : (
        <FlatList
          data={items}
          renderItem={({ item }) => (
            <TrashItem
              item={item}
              onRestore={() => onRestore(item.id)}
              onDelete={() => onDelete(item.id)}
            />
          )}
          keyExtractor={item => item.id.toString()}
        />
      )}

      <Button title="清空回收站" onPress={onEmptyTrash} />
    </View>
  );
};
```

## 七、前端开发路线图

当前阶段为并行开发阶段，前端团队使用模拟数据和API服务进行开发。前端开发将专注于实现完整的前端功能，不依赖后端开发进度。前后端融合将在半年后进行，不是当前前端开发的重点。

### 7.1 前端开发路线图

1. **基础设施阶段**（已完成）
   - 模拟数据生成脚本实现
   - 模拟API服务实现
   - API客户端实现

2. **组件升级阶段**（进行中）
   - UI组件升级
   - 数据处理层升级
   - 用户体验优化

3. **功能实现阶段**（计划中）
   - 用户中心相关功能
   - 学习计划管理功能
   - 内容浏览和交互功能
   - 数据统计和可视化功能

4. **测试与优化阶段**（计划中）
   - 功能测试与问题修复
   - 性能优化
   - 用户体验改进

## 八、总结

本文档提供了NebulaLearn项目前端开发与模拟数据使用的完整计划。通过实现模拟数据生成脚本、模拟API服务和API客户端，前端团队可以独立进行开发，不受后端开发进度的影响。

当前已完成模拟数据生成脚本、模拟API服务和API客户端的实现，为前端开发工作奠定了基础。下一步将进行 UI 组件升级和前端功能实现，确保在半年后与后端融合时，前端已经实现完整的功能。

前端团队将专注于实现高质量的用户界面和交互体验，确保在模拟数据环境下完成所有前端功能的开发和测试。这种开发模式将显著提高开发效率，并确保前端产品的质量和用户体验。

## 九、附录

### 9.1 模拟数据示例

**主题数据示例**：
```json
[
  {
    "id": 1,
    "name": "平台指南",
    "english_name": "Platform Guide",
    "description": "了解如何使用NebulaLearn平台的各项功能",
    "icon": "guide",
    "color": "#4A90E2",
    "cover_image_url": "https://example.com/images/platform-guide.jpg",
    "sort_order": 1,
    "is_active": true,
    "created_at": "2023-05-01T08:00:00Z",
    "updated_at": "2023-05-01T08:00:00Z"
  }
]
```

**标签数据示例**：
```json
[
  {
    "id": 1,
    "name": "时间管理",
    "category_id": 3,
    "relevance_score": 0.95,
    "weight": 0.8,
    "usage_count": 120,
    "is_verified": true,
    "is_official": true,
    "created_at": "2023-05-01T08:00:00Z",
    "updated_at": "2023-05-01T08:00:00Z"
  }
]
```

**学习计划数据示例**：
```json
[
  {
    "id": 1,
    "user_id": 1,
    "template_id": 1,
    "theme_id": 1,
    "title": "掌握高效沟通技巧",
    "description": "7天内提升沟通能力，改善人际关系",
    "cover_image_url": "https://example.com/images/communication.jpg",
    "target_days": 7,
    "completed_days": 3,
    "progress": 43,
    "daily_goal_exercises": 3,
    "daily_goal_insights": 5,
    "daily_goal_minutes": 20,
    "status": "in_progress",
    "start_date": "2023-05-01",
    "end_date": "2023-05-07",
    "is_current": true,
    "is_system_default": false,
    "is_public": false,
    "created_at": "2023-05-01T08:00:00Z",
    "updated_at": "2023-05-03T15:30:00Z"
  }
]
```

### 9.2 有用资源
- [AIBUBB数据库V3.0设计文档](链接)
- [AIBUBB系统全面升级计划-重组](链接)
- [AIBUBB API规范文档](链接)
- [AIBUBB前后端融合桥梁文档](链接)
- [领域驱动设计(DDD)架构说明](链接)
- [API-First设计指南](链接)
- [AIBUBB前端组件库文档](链接)
- [微信小程序开发文档](链接)


### 9.3 数据模型定义

前端应定义与后端一致的数据模型接口：

```typescript
// 主题数据模型示例
interface Theme {
  id: number;
  name: string;
  english_name?: string;
  description?: string;
  icon?: string;
  color?: string;
  cover_image_url?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 学习计划数据模型示例
interface LearningPlan {
  id: number;
  user_id: number;
  template_id?: number;
  theme_id?: number;
  title: string;
  description?: string;
  cover_image_url?: string;
  target_days: number;
  completed_days: number;
  progress: number;
  daily_goal_exercises: number;
  daily_goal_insights: number;
  daily_goal_minutes: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'paused' | 'abandoned';
  start_date?: string;
  end_date?: string;
  is_current: boolean;
  is_system_default: boolean;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 练习数据模型示例
interface Exercise {
  id: number;
  title: string;
  description: string;
  content: string;
  cover_image_url?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimated_minutes: number;
  completion_count: number;
  average_rating: number;
  is_verified: boolean;
  is_official: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 观点数据模型示例
interface Insight {
  id: number;
  title: string;
  content: string;
  source?: string;
  read_count: number;
  like_count: number;
  is_verified: boolean;
  is_official: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 笔记数据模型示例
interface Note {
  id: number;
  user_id: number;
  title: string;
  content: string;
  view_count: number;
  like_count: number;
  comment_count: number;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}
```

## 十、后端接口规范

### 10.1 API响应格式
所有API响应必须遵循统一的格式：

```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "total": 10,  // 适用于列表请求
  "page": 1,    // 适用于分页请求
  "pageSize": 20 // 适用于分页请求
}
```

错误响应：

```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "请求的资源不存在"
  }
}
```

### 10.2 API路由规范
API路由必须遵循RESTful设计原则：

- GET `/api/v1/themes` - 获取主题列表
- GET `/api/v1/themes/:id` - 获取特定主题
- POST `/api/v1/themes` - 创建新主题
- PUT `/api/v1/themes/:id` - 更新主题
- DELETE `/api/v1/themes/:id` - 删除主题

关联资源使用嵌套路由：
- GET `/api/v1/learning-plans/:planId/tags` - 获取特定学习计划的标签
- POST `/api/v1/learning-plans/:planId/tags` - 为学习计划添加标签

### 10.3 查询参数规范
列表API必须支持以下通用查询参数：

- `page`: 页码，默认1
- `pageSize`: 每页记录数，默认20
- `sort_by`: 排序字段
- `sort_order`: 排序方向，`asc`或`desc`
- `search`: 搜索关键词
- `filter_*`: 各种过滤条件

示例：
```
GET /api/v1/themes?page=2&pageSize=10&sort_by=created_at&sort_order=desc&filter_is_active=true
```

## 十一、前端产品交付计划

### 11.1 前端产品目标

- **功能完整**：实现所有设计的前端功能，确保产品完整性
- **用户体验出色**：提供流畅、直觉的用户交互体验
- **性能优化**：确保前端应用在各种设备上运行流畅
- **可维护性高**：代码结构清晰，组件复用性高，易于维护

### 11.2 交付阶段
1. **功能模块交付**：按功能模块分批交付
   - 用户中心模块：第12周
   - 学习计划管理模块：第14周
   - 内容浏览交互模块：第16周
   - 数据统计可视化模块：第18周

2. **测试与优化**：对交付的功能进行测试与优化
   - 功能测试与问题修复：第18-20周
   - 性能优化：第20-21周
   - 用户体验改进：第21-22周

### 11.3 交付成果

1. **前端代码**：完整的前端代码库，包含所有功能实现
2. **模拟数据与API**：模拟数据生成脚本和模拟API服务
3. **技术文档**：包含架构设计、组件说明和开发指南
4. **测试报告**：功能测试和性能测试报告

### 11.4 前后端融合准备

前后端融合将在半年后进行，前端团队将在交付前端产品后，进行以下准备工作：

1. **接口文档准备**：整理模拟API与实际需求的差异文档
2. **数据结构文档**：整理前端使用的数据结构文档
3. **前端配置方案**：准备前端配置切换方案，便于快速切换到实际后端API

## 十二、前端开发时间表

| 阶段 | 任务 | 时间 | 状态 |
|------|------|------|----------|
| **基础设施阶段** | 创建模拟数据生成脚本 | 第1-2周 | ✅ 已完成 |
| | 实现模拟API服务 | 第2-3周 | ✅ 已完成 |
| | 实现API客户端 | 第3-4周 | ✅ 已完成 |
| **组件升级阶段** | 设计并实现回收站组件 | 第5-6周 | ✅ 已完成 |
| | 升级内容管理组件 | 第6-7周 | 🔄 进行中 |
| | 实现批量操作功能 | 第7-8周 | ✅ 已完成 |
| | 数据处理层升级 | 第8-10周 | ⏳ 计划中 |
| **功能实现阶段** | 用户中心相关功能 | 第10-12周 | ⏳ 计划中 |
| | 学习计划管理功能 | 第12-14周 | ⏳ 计划中 |
| | 内容浏览和交互功能 | 第14-16周 | ⏳ 计划中 |
| | 数据统计和可视化功能 | 第16-18周 | ⏳ 计划中 |
| **测试与优化阶段** | 功能测试与问题修复 | 第18-20周 | ⏳ 计划中 |
| | 性能优化 | 第20-21周 | ⏳ 计划中 |
| | 用户体验改进 | 第21-22周 | ⏳ 计划中 |
| | 前端功能完整性验收 | 第22-24周 | ⏳ 计划中 |

## 十三、风险管理

### 13.1 前端开发风险
1. **模拟数据不足**：模拟数据可能不足以支持所有前端功能的开发
2. **组件复用性不足**：组件设计不够通用，导致重复开发
3. **性能问题**：前端应用在复杂场景下可能出现性能问题
4. **用户体验不一致**：不同页面的用户体验不一致
5. **开发进度风险**：功能复杂度超出预期，影响开发进度

### 13.2 风险缓解措施
1. **完善模拟数据**：持续丰富模拟数据，确保覆盖所有功能场景
2. **组件设计评审**：定期进行组件设计评审，提高复用性
3. **性能监控**：实现前端性能监控，及时发现和解决性能问题
4. **设计规范**：制定并执行统一的设计规范，确保用户体验一致性
5. **敏捷开发**：采用敏捷开发方法，分阶段交付，及时调整计划

## 十四、附录

### 14.1 模拟数据示例

**主题数据示例**：
```json
[
  {
    "id": 1,
    "name": "平台指南",
    "english_name": "Platform Guide",
    "description": "了解如何使用NebulaLearn平台的各项功能",
    "icon": "guide",
    "color": "#4A90E2",
    "cover_image_url": "https://example.com/images/platform-guide.jpg",
    "sort_order": 1,
    "is_active": true,
    "created_at": "2023-05-01T08:00:00Z",
    "updated_at": "2023-05-01T08:00:00Z"
  }
]
```

**标签数据示例**：
```json
[
  {
    "id": 1,
    "name": "时间管理",
    "category_id": 3,
    "relevance_score": 0.95,
    "weight": 0.8,
    "usage_count": 120,
    "is_verified": true,
    "is_official": true,
    "created_at": "2023-05-01T08:00:00Z",
    "updated_at": "2023-05-01T08:00:00Z"
  }
]
```

**学习计划数据示例**：
```json
[
  {
    "id": 1,
    "user_id": 1,
    "template_id": 1,
    "theme_id": 1,
    "title": "掌握高效沟通技巧",
    "description": "7天内提升沟通能力，改善人际关系",
    "cover_image_url": "https://example.com/images/communication.jpg",
    "target_days": 7,
    "completed_days": 3,
    "progress": 43,
    "daily_goal_exercises": 3,
    "daily_goal_insights": 5,
    "daily_goal_minutes": 20,
    "status": "in_progress",
    "start_date": "2023-05-01",
    "end_date": "2023-05-07",
    "is_current": true,
    "is_system_default": false,
    "is_public": false,
    "created_at": "2023-05-01T08:00:00Z",
    "updated_at": "2023-05-03T15:30:00Z"
  }
]
```

### 10.2 有用资源
- [AIBUBB数据库V3.0设计文档](链接)
- [AIBUBB系统全面升级计划-重组](链接)
- [AIBUBB API规范文档](链接)
- [AIBUBB前后端融合桥梁文档](链接)
- [领域驱动设计(DDD)架构说明](链接)
- [API-First设计指南](链接)
- [AIBUBB前端组件库文档](链接)
- [微信小程序开发文档](链接)
