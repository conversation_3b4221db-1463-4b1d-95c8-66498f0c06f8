# AIBUBB前后端融合桥梁文档

## 文档目的

本文档旨在为AIBUBB项目的前端和后端开发团队提供共享信息，作为两个团队协作的桥梁。文档包含双方都需要了解的基本信息，以确保在并行开发阶段保持一致性，并为未来的融合做好准备。

## 项目概览

- **项目名称**：AIBUBB（计划改名为NebulaLearn）
- **项目定位**：基于五层结构的综合学习生态系统，专注于提升用户的人际沟通能力
- **架构模式**：领域驱动设计(DDD)
- **开发模式**：前后端分离，并行开发
- **技术栈**：Node.js (Express.js)、MySQL、Redis、微信小程序
- **当前状态**：已完成数据库V3升级，正在进行API-First设计和DDD实施

## 核心业务模型

### 五层结构关系

```
主题(Theme) → 学习模板(LearningTemplate) → 学习计划(LearningPlan) → 标签(Tag) → 内容(Content)
```

- **主题(Theme)**：最高层分类，定义学习的大方向（如“人际沟通”、“职场技能”）
- **学习模板(LearningTemplate)**：预设的学习路径框架，包含标准标签集和内容组织，由专业设计，确保学习路径的科学性和系统性
- **学习计划(LearningPlan)**：用户个性化的学习实例，可基于模板创建或完全自定义，包含具体的学习目标、时间安排和进度跟踪
- **标签(Tag)**：知识点单元，连接学习计划和具体内容，形成知识网络，支持内容的分类和关联
- **内容(Content)**：具体的学习材料，分为三种形式

### 三种内容形式

- **练习(Exercise)**：转化知识为行动，强化实践能力。包含任务描述、执行指南、完成标准和反馈机制，需要用户主动参与并产生输出。
- **观点(Insight)**：精炼的思想启发，促进认知转变。简洁有力的核心观点，可能附带来源和背景解释，引发思考而无需复杂操作。
- **笔记(Note)**：系统化知识呈现，支持深度理解。由标题、内容、配图组成的微型文章，提供沉浸式阅读体验，支持社区互动。

## 技术栈概览

### 前端技术栈

- **框架**：微信小程序原生框架
- **UI组件**：自定义组件库
- **状态管理**：页面数据 + 全局数据
- **网络请求**：wx.request / 封装的API层

### 后端技术栈

- **语言/框架**：Node.js + Express
- **数据库**：MySQL
- **缓存**：Redis
- **架构**：领域驱动设计(DDD)
- **API设计**：API-First设计
- **API文档**：Swagger
- **容器化**：Docker + docker-compose
- **测试**：Jest + Supertest

## 数据库结构

数据库采用V3.0设计，主要特点：

- 表名和字段名统一使用`snake_case`命名规范
- 用户表主键从VARCHAR(32)改为BIGINT AUTO_INCREMENT
- 添加deleted_at字段支持软删除
- 拆分JSON字段为独立表
- 采用聚合根设计，将相关实体分组
- 增强了关联关系的设计，支持复杂查询
- 添加了索引和约束，提高数据完整性和查询效率

### 核心表概览

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| user | 用户表 | id, openid, nickname, avatar_url, level_id, created_at, updated_at, deleted_at |
| theme | 主题表 | id, name, english_name, description, icon, color, cover_image_url, sort_order, is_active |
| learning_template | 学习模板表 | id, theme_id, title, description, difficulty, estimated_days, creator_id, is_official |
| learning_plan | 学习计划表 | id, user_id, template_id, theme_id, title, description, target_days, completed_days, progress, status, is_current |
| tag | 标签表 | id, name, category_id, relevance_score, weight, usage_count, is_verified, is_official |
| tag_category | 标签分类表 | id, name, description, parent_id, level, sort_order |
| exercise | 练习表 | id, tag_id, title, description, difficulty, time_estimate_minutes, creator_id, is_official, status |
| insight | 观点表 | id, tag_id, content, source, background, creator_id, is_official, status |
| note | 笔记表 | id, user_id, tag_id, title, content, image_urls, is_public, like_count, comment_count, status |
| daily_content | 每日内容表 | id, user_id, plan_id, date, exercise_ids, insight_ids, is_completed, completion_time |
| user_achievement | 用户成就表 | id, user_id, achievement_id, earned_at, progress, is_displayed |

## API接口规范

### 基础URL

- 开发环境：`http://localhost:3001/api/v1`
- 测试环境：`http://staging-api.aibubb.com/api/v1`
- 生产环境：`https://api.aibubb.com/api/v1`

### 通用响应格式

成功响应：
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "total": 10,  // 适用于列表请求
  "page": 1,    // 适用于分页请求
  "pageSize": 20 // 适用于分页请求
}
```

错误响应：
```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "请求的资源不存在"
  }
}
```

### 核心API端点

| 方法 | 路径 | 说明 | 权限 |
|------|------|------|------|
| GET | /themes | 获取主题列表 | 公开 |
| GET | /themes/:id | 获取特定主题 | 公开 |
| GET | /learning-templates | 获取学习模板列表 | 公开 |
| GET | /learning-templates/:id | 获取特定学习模板 | 公开 |
| GET | /learning-plans | 获取当前用户的学习计划 | 用户 |
| POST | /learning-plans | 创建学习计划 | 用户 |
| PUT | /learning-plans/:id | 更新学习计划 | 用户 |
| GET | /learning-plans/:id/daily-content | 获取计划的每日内容 | 用户 |
| GET | /tag-categories | 获取标签分类 | 公开 |
| GET | /tags | 获取标签列表 | 公开 |
| GET | /tags/:id/content | 获取标签相关内容 | 公开 |
| GET | /exercises | 获取练习列表 | 公开 |
| POST | /exercises/:id/complete | 标记练习完成 | 用户 |
| GET | /insights | 获取观点列表 | 公开 |
| POST | /insights/:id/read | 标记观点已读 | 用户 |
| GET | /notes | 获取笔记列表 | 公开/用户 |
| POST | /notes | 创建笔记 | 用户 |
| GET | /users/me | 获取当前用户信息 | 用户 |
| GET | /users/me/achievements | 获取用户成就 | 用户 |
| GET | /users/me/statistics | 获取用户学习统计 | 用户 |

### 通用查询参数

所有列表API支持以下查询参数：

- `page`: 页码，默认1
- `pageSize`: 每页记录数，默认20
- `sort_by`: 排序字段
- `sort_order`: 排序方向，`asc`或`desc`
- `search`: 搜索关键词

## 前端页面与后端API对应关系

| 页面 | 功能 | 使用的API |
|------|------|----------|
| 首页 | 展示泡泡界面 | GET /tags, GET /exercises, GET /insights |
| 学习页 | 展示学习计划 | GET /learning-plans, GET /learning-plans/:id/daily-content |
| 创建计划 | 创建学习计划 | GET /themes, GET /learning-templates, POST /learning-plans |
| 计划详情 | 查看计划详情 | GET /learning-plans/:id, GET /tags |
| 标签详情 | 查看标签相关内容 | GET /tags/:id, GET /tags/:id/content |
| 练习详情 | 查看和完成练习 | GET /exercises/:id, POST /exercises/:id/complete |
| 观点详情 | 查看和标记观点 | GET /insights/:id, POST /insights/:id/read |
| 广场 | 浏览公开内容 | GET /notes?is_public=true |
| 笔记创建 | 创建和编辑笔记 | POST /notes, PUT /notes/:id |
| 个人中心 | 用户信息和成就 | GET /users/me, GET /users/me/achievements, GET /users/me/statistics |

## 数据模型定义

为确保前后端数据一致性，以下是核心数据模型的TypeScript接口定义：

```typescript
// 主题
interface Theme {
  id: number;
  name: string;
  english_name?: string;
  description?: string;
  icon?: string;
  color?: string;
  cover_image_url?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 学习计划
interface LearningPlan {
  id: number;
  user_id: number;
  template_id?: number;
  theme_id?: number;
  title: string;
  description?: string;
  cover_image_url?: string;
  target_days: number;
  completed_days: number;
  progress: number;
  daily_goal_exercises: number;
  daily_goal_insights: number;
  daily_goal_minutes: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'paused' | 'abandoned';
  start_date?: string;
  end_date?: string;
  is_current: boolean;
  is_system_default: boolean;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 标签
interface Tag {
  id: number;
  name: string;
  category_id?: number;
  creator_id?: number;
  relevance_score: number;
  weight: number;
  usage_count: number;
  is_verified: boolean;
  is_official: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 练习
interface Exercise {
  id: number;
  tag_id: number;
  title: string;
  description: string;
  expected_result?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  time_estimate_minutes: number;
  creator_id?: number;
  is_official: boolean;
  status: 'draft' | 'published' | 'archived';
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 观点
interface Insight {
  id: number;
  tag_id: number;
  content: string;
  source?: string;
  background?: string;
  creator_id?: number;
  is_official: boolean;
  status: 'draft' | 'published' | 'archived';
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// 笔记
interface Note {
  id: number;
  user_id: number;
  tag_id: number;
  title: string;
  content: string;
  image_urls?: string[];
  is_public: boolean;
  like_count: number;
  comment_count: number;
  view_count: number;
  status: 'draft' | 'published' | 'archived';
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}
```

## 认证与授权

### 认证方式

- 基于JWT的令牌认证
- 令牌通过HTTP头部`Authorization: Bearer {token}`传递
- 令牌有效期为24小时，刷新令牌有效期为7天

### 权限级别

- **公开**：无需认证即可访问
- **用户**：需要有效的用户令牌
- **管理员**：需要管理员权限的用户令牌

## 错误码定义

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| UNAUTHORIZED | 未授权访问 | 401 |
| FORBIDDEN | 禁止访问 | 403 |
| RESOURCE_NOT_FOUND | 资源不存在 | 404 |
| VALIDATION_ERROR | 数据验证错误 | 422 |
| INTERNAL_SERVER_ERROR | 服务器内部错误 | 500 |

## 开发流程与协作

### 开发原则

- **API-First设计**：先设计并文档化API，再进行实现
- **领域驱动设计**：后端采用DDD架构，将业务逻辑与技术实现分离
- **并行开发**：前端使用模拟数据独立开发，后端专注于DDD实现
- **渐进融合**：随着后端实现的成熟，逐步将前端从模拟数据迁移到真实API

### 代码仓库

- 代码仓库：`https://github.com/mccic/AIBUBB`
- 分支策略：
  - `main`：主分支
  - `backend-dev`：后端开发分支
  - `frontend-dev`：前端开发分支
  - `feature/*`：具体功能分支

### 沟通渠道

- 每周同步会议：周一上午10:00
- 技术讨论群：AIBUBB开发团队（微信群）
- 文档共享：项目Wiki

### API变更流程

1. 后端团队提出API变更建议
2. 在周会上讨论并确认变更
3. 更新API文档和Swagger定义
4. 通知前端团队并更新模拟数据服务
5. 后端实现API变更
6. 前端适配新API
7. 联合测试变更

## 环境信息

### 开发环境

- 前端开发服务器：`http://localhost:8080`
- 后端API服务器：`http://localhost:3001`
- 模拟API服务器：`http://localhost:3010`
- 数据库：`localhost:3306`
- Redis：`localhost:6379`

### 测试环境

- 前端应用：`http://staging.aibubb.com`
- 后端API：`http://staging-api.aibubb.com`
- API文档：`http://staging-api.aibubb.com/api-docs`

## 联系人

- **前端负责人**：[姓名] - [联系方式]
- **后端负责人**：[姓名] - [联系方式]
- **产品经理**：[姓名] - [联系方式]
- **项目经理**：[姓名] - [联系方式]

## 常见问题

1. **Q: 如何获取开发环境的测试账号？**
   A: 联系后端负责人获取测试账号和密码。

2. **Q: API返回500错误怎么办？**
   A: 检查后端日志，联系后端开发人员解决。

3. **Q: 前端如何处理后端API变更？**
   A: 关注API文档更新通知，及时调整前端代码。

4. **Q: 如何提出新的API需求？**
   A: 在周会上提出，或通过项目管理工具创建需求。

## 资源链接

- [AIBUBB数据库V3.0设计文档](链接)
- [AIBUBB系统全面升级计划-重组](链接)
- [AIBUBB API文档](链接)
- [AIBUBB前后端并行开发与模拟数据使用计划](链接)
- [领域驱动设计(DDD)架构说明](链接)
- [前端开发指南](链接)
- [后端开发指南](链接)
- [项目Wiki](链接)
