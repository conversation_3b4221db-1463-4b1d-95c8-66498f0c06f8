# AIBUBB前端升级计划

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-04 |
| 最后更新 | 2025-05-04 |
| 作者 | AIBUBB技术团队 |

## 目录

1. [项目背景](#1-项目背景)
2. [当前前端系统评估](#2-当前前端系统评估)
3. [升级目标](#3-升级目标)
4. [升级计划](#4-升级计划)
   - [第一阶段：API层升级](#41-第一阶段api层升级2周)
   - [第二阶段：UI组件升级](#42-第二阶段ui组件升级3周)
   - [第三阶段：数据处理层升级](#43-第三阶段数据处理层升级2周)
   - [第四阶段：用户体验优化](#44-第四阶段用户体验优化2周)
5. [实施建议](#5-实施建议)
6. [风险评估](#6-风险评估)
7. [附录](#7-附录)

## 1. 项目背景

AIBUBB是一个基于微信小程序的AI辅助学习平台，专注于提升用户的人际沟通能力。项目后端在2025年4月完成了数据库V3升级，包括表名规范化为snake_case、用户表主键改为BIGINT、添加软删除机制、拆分JSON字段等重要改进。前端需要相应升级以适应这些变化，并提供更好的用户体验。

## 2. 当前前端系统评估

### 2.1 架构现状

- **API调用层**：使用`utils/api.js`和`utils/api-v2.js`进行后端API调用
- **数据适配层**：使用`utils/statistics-adapter.js`等适配器处理API版本差异
- **UI组件**：使用自定义组件如`waterfall-content`、`theme-manager`等
- **行为复用**：通过behaviors如`data-loading-behavior.js`实现功能复用
- **全局状态**：通过app.js的globalData管理全局状态
- **缓存管理**：简单的缓存机制，缺乏系统性设计

### 2.2 存在问题

#### 2.2.1 API层问题

1. **API版本混乱**：同时存在v1和v2的API调用，缺乏统一管理
2. **缺乏软删除支持**：API调用未支持软删除和恢复功能
3. **错误处理不一致**：不同模块对API错误的处理方式不统一
4. **命名不一致**：前端使用驼峰命名，后端使用snake_case，造成数据转换负担
5. **缺乏类型定义**：没有明确的数据类型定义，增加了维护难度

#### 2.2.2 UI组件问题

1. **缺乏软删除UI**：没有提供软删除和恢复功能的界面
2. **数据状态展示不足**：无法展示已删除内容的状态
3. **批量操作支持不足**：缺乏批量选择、删除、恢复等功能
4. **组件复用性差**：部分组件耦合度高，难以在不同场景复用

#### 2.2.3 数据处理问题

1. **数据转换繁琐**：需要手动处理snake_case和camelCase之间的转换
2. **缓存策略简单**：缓存机制未考虑软删除状态，可能导致数据不一致
3. **数据验证不足**：缺乏统一的数据验证机制
4. **状态管理分散**：状态管理分散在各个组件中，缺乏统一管理

#### 2.2.4 用户体验问题

1. **加载状态不明确**：数据加载过程中缺乏明确的状态指示
2. **错误反馈不友好**：API错误未提供足够友好的用户反馈
3. **操作确认机制不完善**：敏感操作如删除缺乏完善的确认机制
4. **无障碍支持不足**：缺乏对无障碍功能的支持

## 3. 升级目标

1. **统一API调用**：整合API调用层，支持V3数据库的新字段和结构
2. **增加软删除UI**：为软删除功能添加前端界面支持
3. **优化数据处理**：实现统一的数据转换和验证机制
4. **增强用户体验**：提供更友好的界面和交互体验
5. **提高代码质量**：增加类型定义，提高代码可维护性
6. **增强错误处理**：实现统一的错误处理机制

## 4. 升级计划

### 4.1 第一阶段：API层升级（2025年5月上旬至中旬，2周）

#### 4.1.1 创建统一的API客户端

| 任务 | 描述 | 优先级 | 预计工时 |
|-----|------|-------|---------|
| 设计API客户端架构 | 设计统一的API客户端架构，支持版本管理和软删除 | 高 | 2天 |
| 实现核心请求功能 | 实现统一的请求处理、错误处理和缓存机制 | 高 | 3天 |
| 实现数据转换层 | 创建snake_case和camelCase之间的自动转换机制 | 高 | 2天 |
| 添加类型定义 | 为API请求和响应添加TypeScript类型定义 | 中 | 2天 |

#### 4.1.2 实现软删除相关API

| 任务 | 描述 | 优先级 | 预计工时 | 状态 | 备注 |
|-----|------|-------|---------|------|------|
| 实现标签软删除API | 添加标签的软删除、恢复和获取已删除标签API | 高 | 1天 | ✅ 后端已实现 | 需要前端适配 |
| 实现观点软删除API | 添加观点的软删除、恢复和获取已删除观点API | 高 | 1天 | ✅ 后端已实现 | 需要前端适配 |
| 实现练习软删除API | 添加练习的软删除、恢复和获取已删除练习API | 高 | 1天 | ✅ 后端已实现 | 需要前端适配 |
| 实现笔记软删除API | 添加笔记的软删除、恢复和获取已删除笔记API | 高 | 1天 | ✅ 后端已实现 | 需要前端适配 |
| 实现主题软删除API | 添加主题的软删除、恢复和获取已删除主题API | 高 | 1天 | ✅ 后端已实现 | 需要前端适配 |
| 实现每日内容软删除API | 添加每日内容的软删除、恢复和获取已删除内容API | 高 | 1天 | ✅ 后端已实现 | 需要前端适配 |
| 实现学习计划软删除API | 添加学习计划的软删除、恢复和获取已删除计划API | 高 | 1天 | ✅ 后端已实现 | 需要前端适配 |

**后端API实现情况（2025-05-04更新）**：

后端已经实现了以下模块的软删除相关API：

1. **标签（Tag）**：`/api/v2/tags/{id}/soft-delete`（软删除）、`/api/v2/tags/{id}/restore`（恢复）、`/api/v2/tags/deleted`（获取已删除列表）
2. **观点（Insight）**：`/api/v2/insights/{id}/soft-delete`（软删除）、`/api/v2/insights/{id}/restore`（恢复）、`/api/v2/insights/deleted`（获取已删除列表）
3. **练习（Exercise）**：`/api/v2/exercises/{id}/soft-delete`（软删除）、`/api/v2/exercises/{id}/restore`（恢复）、`/api/v2/exercises/deleted`（获取已删除列表）
4. **笔记（Note）**：`/api/v2/notes/{id}/soft-delete`（软删除）、`/api/v2/notes/{id}/restore`（恢复）、`/api/v2/notes/deleted`（获取已删除列表）
5. **主题（Theme）**：`/api/v2/themes/{id}/soft-delete`（软删除）、`/api/v2/themes/{id}/restore`（恢复）、`/api/v2/themes/deleted`（获取已删除列表）
6. **每日内容（DailyContent）**：`/api/v2/daily-contents/{id}/soft-delete`（软删除）、`/api/v2/daily-contents/{id}/restore`（恢复）、`/api/v2/daily-contents/plan/{planId}/deleted`（获取已删除列表）
7. **学习计划（LearningPlan）**：`/api/v2/learning-plans/{id}/soft-delete`（软删除）、`/api/v2/learning-plans/{id}/restore`（恢复）、`/api/v2/learning-plans/deleted`（获取已删除列表）

前端需要适配这些API，实现相应的调用和界面展示。

#### 4.1.3 统一错误处理

| 任务 | 描述 | 优先级 | 预计工时 |
|-----|------|-------|---------|
| 设计错误处理架构 | 设计统一的错误处理架构，包括错误类型和处理流程 | 高 | 1天 |
| 实现错误处理器 | 实现统一的错误处理器，支持不同类型错误的处理 | 高 | 2天 |
| 集成到API客户端 | 将错误处理器集成到API客户端中 | 高 | 1天 |

### 4.2 第二阶段：UI组件升级（2025年5月中旬至6月上旬，3周）

#### 4.2.1 创建回收站组件

| 任务 | 描述 | 优先级 | 预计工时 | 状态 |
|-----|------|-------|---------|------|
| 设计回收站UI | 设计回收站界面，包括列表展示、筛选和操作按钮 | 高 | 2天 | ✅ 已完成 |
| 实现回收站组件 | 实现通用回收站组件，支持不同类型内容的展示 | 高 | 3天 | ✅ 已完成 |
| 实现恢复功能 | 实现内容恢复功能，包括单个恢复和批量恢复 | 高 | 2天 | ✅ 已完成 |
| 实现永久删除功能 | 实现内容永久删除功能，包括单个删除和批量删除 | 中 | 2天 | ⬜ 未开始 |

#### 4.2.2 升级内容管理组件

| 任务 | 描述 | 优先级 | 预计工时 | 状态 |
|-----|------|-------|---------|------|
| 升级标签管理组件 | 为标签管理组件添加软删除功能 | 高 | 2天 | ⬜ 未开始 |
| 升级观点管理组件 | 为观点管理组件添加软删除功能 | 高 | 2天 | ⬜ 未开始 |
| 升级练习管理组件 | 为练习管理组件添加软删除功能 | 高 | 2天 | ⬜ 未开始 |
| 升级笔记管理组件 | 为笔记管理组件添加软删除功能 | 高 | 2天 | ✅ 已完成 |

#### 4.2.3 实现批量操作功能

| 任务 | 描述 | 优先级 | 预计工时 | 状态 |
|-----|------|-------|---------|------|
| 设计批量操作UI | 设计批量操作界面，包括选择模式和操作按钮 | 中 | 1天 | ✅ 已完成 |
| 实现批量选择功能 | 实现内容批量选择功能 | 中 | 2天 | ✅ 已完成 |
| 实现批量删除功能 | 实现内容批量删除功能 | 中 | 1天 | ✅ 已完成 |
| 实现批量恢复功能 | 实现内容批量恢复功能 | 中 | 1天 | ✅ 已完成 |

### 4.3 第三阶段：数据处理层升级（2025年6月上旬至中旬，2周）

#### 4.3.1 实现数据模型层

| 任务 | 描述 | 优先级 | 预计工时 |
|-----|------|-------|---------|
| 设计数据模型架构 | 设计统一的数据模型架构，支持数据转换和验证 | 高 | 2天 |
| 实现基础模型类 | 实现基础数据模型类，提供通用功能 | 高 | 2天 |
| 实现实体模型类 | 实现各实体的数据模型类，如标签、观点、练习、笔记等 | 高 | 3天 |
| 集成到API客户端 | 将数据模型集成到API客户端中 | 高 | 1天 |

#### 4.3.2 优化缓存策略

| 任务 | 描述 | 优先级 | 预计工时 |
|-----|------|-------|---------|
| 设计缓存架构 | 设计统一的缓存架构，支持软删除状态 | 中 | 1天 |
| 实现缓存管理器 | 实现统一的缓存管理器，支持缓存失效和更新 | 中 | 2天 |
| 实现缓存同步机制 | 实现缓存与服务器数据的同步机制 | 中 | 2天 |
| 集成到API客户端 | 将缓存管理器集成到API客户端中 | 中 | 1天 |

### 4.4 第四阶段：用户体验优化（2025年6月中旬至下旬，2周）

#### 4.4.1 优化加载状态

| 任务 | 描述 | 优先级 | 预计工时 |
|-----|------|-------|---------|
| 设计加载状态UI | 设计统一的加载状态界面 | 中 | 1天 |
| 实现加载状态组件 | 实现统一的加载状态组件 | 中 | 2天 |
| 集成到各页面 | 将加载状态组件集成到各页面中 | 中 | 2天 |

#### 4.4.2 优化错误反馈

| 任务 | 描述 | 优先级 | 预计工时 |
|-----|------|-------|---------|
| 设计错误反馈UI | 设计统一的错误反馈界面 | 中 | 1天 |
| 实现错误反馈组件 | 实现统一的错误反馈组件 | 中 | 2天 |
| 集成到各页面 | 将错误反馈组件集成到各页面中 | 中 | 2天 |

#### 4.4.3 增强操作确认机制

| 任务 | 描述 | 优先级 | 预计工时 |
|-----|------|-------|---------|
| 设计操作确认UI | 设计统一的操作确认界面 | 中 | 1天 |
| 实现操作确认组件 | 实现统一的操作确认组件 | 中 | 2天 |
| 集成到敏感操作 | 将操作确认组件集成到敏感操作中 | 中 | 1天 |

## 5. 实施建议

1. **渐进式升级**：采用渐进式升级策略，确保每个阶段都能独立完成并提供价值
2. **兼容性保障**：确保升级过程中保持对现有功能的兼容性
3. **测试驱动开发**：采用测试驱动开发方法，确保代码质量
4. **文档先行**：先完善API文档和组件文档，再进行实现
5. **用户反馈**：在升级过程中收集用户反馈，及时调整计划
6. **性能监控**：实施性能监控，确保升级不会导致性能下降
7. **代码审查**：严格执行代码审查，确保代码质量和一致性

## 6. 风险评估

| 风险 | 影响 | 可能性 | 缓解措施 |
|-----|------|-------|---------|
| API兼容性问题 | 高 | 中 | 实现适配层，保持对旧版API的支持 |
| 数据转换错误 | 高 | 中 | 实现全面的单元测试，确保数据转换正确 |
| 用户体验下降 | 中 | 低 | 进行用户测试，收集反馈并及时调整 |
| 性能问题 | 中 | 中 | 实施性能监控，优化关键路径 |
| 开发资源不足 | 高 | 中 | 合理规划，优先实施关键功能 |

## 7. 附录

### 7.1 API客户端示例代码

```javascript
// api-client.js
const apiClient = {
  // 基础配置
  config: {
    baseUrl: getBaseUrl(),
    version: 'v2', // 默认使用v2 API
    timeout: 5000
  },

  // API模块
  auth: { /* 认证相关API */ },
  user: { /* 用户相关API */ },

  // 标签API
  tag: {
    // 获取标签列表
    getTags: (params) => {
      return apiClient.request('/tags', 'GET', null, { params });
    },

    // 软删除标签
    softDelete: (tagId) => {
      return apiClient.request(`/tags/${tagId}/soft-delete`, 'DELETE');
    },

    // 恢复标签
    restore: (tagId) => {
      return apiClient.request(`/tags/${tagId}/restore`, 'POST');
    },

    // 获取已删除标签
    getDeletedTags: (params) => {
      return apiClient.request('/tags/deleted', 'GET', null, { params });
    }
  },

  // 主题API
  theme: {
    // 获取主题列表
    getThemes: (params) => {
      return apiClient.request('/themes', 'GET', null, { params });
    },

    // 获取主题详情
    getThemeById: (themeId) => {
      return apiClient.request(`/themes/${themeId}`, 'GET');
    },

    // 软删除主题
    softDelete: (themeId) => {
      return apiClient.request(`/themes/${themeId}/soft-delete`, 'DELETE');
    },

    // 恢复主题
    restore: (themeId) => {
      return apiClient.request(`/themes/${themeId}/restore`, 'POST');
    },

    // 获取已删除主题
    getDeletedThemes: (params) => {
      return apiClient.request('/themes/deleted', 'GET', null, { params });
    }
  },

  // 每日内容API
  dailyContent: {
    // 获取学习计划的每日内容
    getPlanContents: (planId, params) => {
      return apiClient.request(`/daily-contents/plan/${planId}`, 'GET', null, { params });
    },

    // 获取内容详情
    getContentById: (contentId) => {
      return apiClient.request(`/daily-contents/${contentId}`, 'GET');
    },

    // 更新内容
    updateContent: (contentId, data) => {
      return apiClient.request(`/daily-contents/${contentId}`, 'PUT', data);
    },

    // 软删除内容
    softDelete: (contentId) => {
      return apiClient.request(`/daily-contents/${contentId}/soft-delete`, 'DELETE');
    },

    // 恢复内容
    restore: (contentId) => {
      return apiClient.request(`/daily-contents/${contentId}/restore`, 'POST');
    },

    // 获取已删除内容
    getDeletedContents: (planId, params) => {
      return apiClient.request(`/daily-contents/plan/${planId}/deleted`, 'GET', null, { params });
    }
  },

  // 学习计划API
  learningPlan: {
    // 获取用户的学习计划
    getUserPlans: (params) => {
      return apiClient.request('/learning-plans', 'GET', null, { params });
    },

    // 获取计划详情
    getPlanById: (planId) => {
      return apiClient.request(`/learning-plans/${planId}`, 'GET');
    },

    // 创建计划
    createPlan: (data) => {
      return apiClient.request('/learning-plans', 'POST', data);
    },

    // 更新计划
    updatePlan: (planId, data) => {
      return apiClient.request(`/learning-plans/${planId}`, 'PUT', data);
    },

    // 软删除计划
    softDelete: (planId) => {
      return apiClient.request(`/learning-plans/${planId}/soft-delete`, 'DELETE');
    },

    // 恢复计划
    restore: (planId) => {
      return apiClient.request(`/learning-plans/${planId}/restore`, 'POST');
    },

    // 获取已删除计划
    getDeletedPlans: (params) => {
      return apiClient.request('/learning-plans/deleted', 'GET', null, { params });
    }
  },

  // 请求方法
  request: async (endpoint, method, data, options) => {
    // 实现请求逻辑
  }
};
```

### 7.2 回收站组件示例代码

```javascript
// components/recycle-bin/index.js
Component({
  properties: {
    type: {
      type: String,
      value: 'note' // 'note', 'insight', 'exercise', 'tag', 'theme', 'dailyContent', 'learningPlan'
    },
    planId: {
      type: Number,
      value: null // 仅当type为'dailyContent'时需要
    }
  },

  data: {
    items: [],
    isLoading: false,
    isSelectMode: false,
    selectedItems: [],
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    }
  },

  methods: {
    // 加载已删除内容
    loadDeletedItems() {
      this.setData({ isLoading: true });

      const { page, pageSize } = this.data.pagination;
      const params = { page, pageSize };

      let promise;

      // 根据type调用不同API
      switch (this.properties.type) {
        case 'note':
          promise = apiClient.note.getDeletedNotes(params);
          break;
        case 'insight':
          promise = apiClient.insight.getDeletedInsights(params);
          break;
        case 'exercise':
          promise = apiClient.exercise.getDeletedExercises(params);
          break;
        case 'tag':
          promise = apiClient.tag.getDeletedTags(params);
          break;
        case 'theme':
          promise = apiClient.theme.getDeletedThemes(params);
          break;
        case 'dailyContent':
          if (!this.properties.planId) {
            wx.showToast({ title: '缺少计划ID', icon: 'none' });
            this.setData({ isLoading: false });
            return;
          }
          promise = apiClient.dailyContent.getDeletedContents(this.properties.planId, params);
          break;
        case 'learningPlan':
          promise = apiClient.learningPlan.getDeletedPlans(params);
          break;
        default:
          wx.showToast({ title: '不支持的内容类型', icon: 'none' });
          this.setData({ isLoading: false });
          return;
      }

      promise.then(res => {
        // 处理不同API返回的数据结构
        let items = [];
        let pagination = { ...this.data.pagination };

        switch (this.properties.type) {
          case 'note':
            items = res.notes || [];
            pagination = res.pagination || pagination;
            break;
          case 'insight':
            items = res.insights || [];
            pagination = res.pagination || pagination;
            break;
          case 'exercise':
            items = res.exercises || [];
            pagination = res.pagination || pagination;
            break;
          case 'tag':
            items = res.tags || [];
            pagination = res.pagination || pagination;
            break;
          case 'theme':
            items = res.themes || [];
            pagination = res.pagination || pagination;
            break;
          case 'dailyContent':
            items = res.contents || [];
            pagination = res.pagination || pagination;
            break;
          case 'learningPlan':
            items = res.plans || [];
            pagination = res.pagination || pagination;
            break;
        }

        this.setData({
          items,
          pagination,
          isLoading: false
        });
      }).catch(err => {
        console.error('加载已删除内容失败:', err);
        wx.showToast({ title: '加载失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 恢复内容
    handleRestore(e) {
      const itemId = e.currentTarget.dataset.id;
      if (!itemId) return;

      wx.showModal({
        title: '恢复确认',
        content: '确定要恢复此内容吗？',
        success: res => {
          if (res.confirm) {
            this.restoreItem(itemId);
          }
        }
      });
    },

    // 执行恢复操作
    restoreItem(itemId) {
      this.setData({ isLoading: true });

      let promise;

      // 根据type调用不同API
      switch (this.properties.type) {
        case 'note':
          promise = apiClient.note.restore(itemId);
          break;
        case 'insight':
          promise = apiClient.insight.restore(itemId);
          break;
        case 'exercise':
          promise = apiClient.exercise.restore(itemId);
          break;
        case 'tag':
          promise = apiClient.tag.restore(itemId);
          break;
        case 'theme':
          promise = apiClient.theme.restore(itemId);
          break;
        case 'dailyContent':
          promise = apiClient.dailyContent.restore(itemId);
          break;
        case 'learningPlan':
          promise = apiClient.learningPlan.restore(itemId);
          break;
        default:
          wx.showToast({ title: '不支持的内容类型', icon: 'none' });
          this.setData({ isLoading: false });
          return;
      }

      promise.then(() => {
        wx.showToast({ title: '恢复成功', icon: 'success' });
        // 重新加载列表
        this.loadDeletedItems();
      }).catch(err => {
        console.error('恢复内容失败:', err);
        wx.showToast({ title: '恢复失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 永久删除
    handlePermanentDelete(e) {
      const itemId = e.currentTarget.dataset.id;
      if (!itemId) return;

      wx.showModal({
        title: '删除确认',
        content: '此操作将永久删除内容，无法恢复，确定继续吗？',
        confirmColor: '#ff0000',
        success: res => {
          if (res.confirm) {
            // 调用永久删除API
            // 注意：目前后端未实现永久删除API，此处为示例代码
            wx.showToast({ title: '功能开发中', icon: 'none' });
          }
        }
      });
    },

    // 切换选择模式
    toggleSelectMode() {
      this.setData({ isSelectMode: !this.data.isSelectMode, selectedItems: [] });
    },

    // 选择/取消选择项目
    handleSelectItem(e) {
      if (!this.data.isSelectMode) return;

      const itemId = e.currentTarget.dataset.id;
      const selectedItems = [...this.data.selectedItems];

      const index = selectedItems.indexOf(itemId);
      if (index > -1) {
        selectedItems.splice(index, 1);
      } else {
        selectedItems.push(itemId);
      }

      this.setData({ selectedItems });
    },

    // 批量操作
    handleBatchOperation(e) {
      const operation = e.currentTarget.dataset.operation;
      const { selectedItems } = this.data;

      if (!selectedItems.length) {
        wx.showToast({ title: '请先选择内容', icon: 'none' });
        return;
      }

      if (operation === 'restore') {
        wx.showModal({
          title: '批量恢复确认',
          content: `确定要恢复选中的${selectedItems.length}项内容吗？`,
          success: res => {
            if (res.confirm) {
              // 批量恢复
              // 注意：目前后端未实现批量恢复API，此处为示例代码
              wx.showToast({ title: '功能开发中', icon: 'none' });
            }
          }
        });
      } else if (operation === 'delete') {
        wx.showModal({
          title: '批量删除确认',
          content: `此操作将永久删除选中的${selectedItems.length}项内容，无法恢复，确定继续吗？`,
          confirmColor: '#ff0000',
          success: res => {
            if (res.confirm) {
              // 批量永久删除
              // 注意：目前后端未实现批量永久删除API，此处为示例代码
              wx.showToast({ title: '功能开发中', icon: 'none' });
            }
          }
        });
      }
    },

    // 分页操作
    handlePageChange(e) {
      const page = e.currentTarget.dataset.page;
      this.setData({
        pagination: {
          ...this.data.pagination,
          page
        }
      }, () => {
        this.loadDeletedItems();
      });
    }
  },

  lifetimes: {
    attached() {
      this.loadDeletedItems();
    }
  }
});
```

### 7.3 数据模型示例代码

```javascript
// models/base-model.js
class BaseModel {
  constructor(rawData = {}) {
    this._rawData = rawData;
    this._processData(rawData);
  }

  // 将snake_case转换为camelCase
  _snakeToCamel(str) {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  // 将camelCase转换为snake_case
  _camelToSnake(str) {
    return str.replace(/([A-Z])/g, (_, letter) => `_${letter.toLowerCase()}`);
  }

  // 处理原始数据
  _processData(rawData) {
    Object.keys(rawData).forEach(key => {
      const camelKey = this._snakeToCamel(key);
      this[camelKey] = rawData[key];
    });

    // 处理软删除状态
    this.isDeleted = !!this.deletedAt;
  }

  // 转换为API请求数据
  toApiData() {
    const apiData = {};

    // 排除内部属性
    const keys = Object.keys(this).filter(key =>
      !key.startsWith('_') && key !== 'isDeleted'
    );

    keys.forEach(key => {
      const snakeKey = this._camelToSnake(key);
      apiData[snakeKey] = this[key];
    });

    return apiData;
  }

  // 静态方法：批量转换
  static fromApiList(rawList) {
    return (rawList || []).map(item => new this(item));
  }
}

// models/tag-model.js
class TagModel extends BaseModel {
  constructor(rawData = {}) {
    super(rawData);

    // 设置默认值
    this.name = this.name || '';
    this.categoryId = this.categoryId || null;
    this.creatorId = this.creatorId || null;
    this.weight = this.weight || 1.0;
    this.usageCount = this.usageCount || 0;
    this.isVerified = this.isVerified || false;
    this.isOfficial = this.isOfficial || true;
  }

  // 检查是否可编辑
  canEdit(currentUserId) {
    return this.creatorId === currentUserId || currentUserId === 'admin';
  }

  // 检查是否可删除
  canDelete(currentUserId) {
    return this.canEdit(currentUserId) && !this.isDeleted;
  }

  // 检查是否可恢复
  canRestore(currentUserId) {
    return this.canEdit(currentUserId) && this.isDeleted;
  }
}

// models/theme-model.js
class ThemeModel extends BaseModel {
  constructor(rawData = {}) {
    super(rawData);

    // 设置默认值
    this.name = this.name || '';
    this.englishName = this.englishName || '';
    this.description = this.description || '';
    this.icon = this.icon || '';
    this.color = this.color || '#000000';
    this.sortOrder = this.sortOrder || 0;
    this.isActive = this.isActive !== undefined ? this.isActive : true;
  }

  // 检查是否可编辑（只有管理员可以编辑主题）
  canEdit(currentUserId) {
    return currentUserId === 'admin';
  }

  // 检查是否可删除
  canDelete(currentUserId) {
    return this.canEdit(currentUserId) && !this.isDeleted;
  }

  // 检查是否可恢复
  canRestore(currentUserId) {
    return this.canEdit(currentUserId) && this.isDeleted;
  }
}

// models/daily-content-model.js
class DailyContentModel extends BaseModel {
  constructor(rawData = {}) {
    super(rawData);

    // 设置默认值
    this.planId = this.planId || null;
    this.dayNumber = this.dayNumber || 1;
    this.title = this.title || '';
    this.content = this.content || '';
    this.summary = this.summary || '';
    this.difficulty = this.difficulty || 1;
    this.isCompleted = this.isCompleted !== undefined ? this.isCompleted : false;
    this.completionDate = this.completionDate || null;
    this.status = this.status || 'active';
  }

  // 检查是否可编辑（只有计划创建者可以编辑内容）
  canEdit(currentUserId, planCreatorId) {
    return currentUserId === planCreatorId || currentUserId === 'admin';
  }

  // 检查是否可删除
  canDelete(currentUserId, planCreatorId) {
    return this.canEdit(currentUserId, planCreatorId) && !this.isDeleted;
  }

  // 检查是否可恢复
  canRestore(currentUserId, planCreatorId) {
    return this.canEdit(currentUserId, planCreatorId) && this.isDeleted;
  }
}

// models/learning-plan-model.js
class LearningPlanModel extends BaseModel {
  constructor(rawData = {}) {
    super(rawData);

    // 设置默认值
    this.title = this.title || '';
    this.description = this.description || '';
    this.themeId = this.themeId || null;
    this.themeName = this.themeName || '';
    this.themeColor = this.themeColor || '#000000';
    this.status = this.status || 'active';
    this.progress = this.progress || 0;
    this.targetDays = this.targetDays || 30;
    this.completedDays = this.completedDays || 0;
    this.startDate = this.startDate || null;
    this.endDate = this.endDate || null;
    this.dailyGoalExercises = this.dailyGoalExercises || 3;
    this.dailyGoalInsights = this.dailyGoalInsights || 5;
    this.dailyGoalTime = this.dailyGoalTime || 15;
  }

  // 检查是否可编辑（只有计划创建者可以编辑计划）
  canEdit(currentUserId) {
    return currentUserId === this.userId || currentUserId === 'admin';
  }

  // 检查是否可删除
  canDelete(currentUserId) {
    return this.canEdit(currentUserId) && !this.isDeleted;
  }

  // 检查是否可恢复
  canRestore(currentUserId) {
    return this.canEdit(currentUserId) && this.isDeleted;
  }
}
```

### 7.4 错误处理示例代码

```javascript
// utils/error-handler.js
const errorHandler = {
  // 错误类型
  errorTypes: {
    NETWORK_ERROR: 'NETWORK_ERROR',
    AUTH_ERROR: 'AUTH_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
    PERMISSION_ERROR: 'PERMISSION_ERROR',
    SERVER_ERROR: 'SERVER_ERROR',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR'
  },

  // 处理API错误
  handleApiError(error, options = {}) {
    console.error('API错误:', error);

    // 默认选项
    const defaultOptions = {
      showToast: true,
      toastDuration: 2000,
      redirectOnAuth: true
    };

    const opts = { ...defaultOptions, ...options };

    // 确定错误类型
    const errorType = this._getErrorType(error);

    // 根据错误类型处理
    switch (errorType) {
      case this.errorTypes.AUTH_ERROR:
        return this._handleAuthError(error, opts);
      case this.errorTypes.VALIDATION_ERROR:
        return this._handleValidationError(error, opts);
      case this.errorTypes.NOT_FOUND_ERROR:
        return this._handleNotFoundError(error, opts);
      case this.errorTypes.PERMISSION_ERROR:
        return this._handlePermissionError(error, opts);
      case this.errorTypes.NETWORK_ERROR:
        return this._handleNetworkError(error, opts);
      case this.errorTypes.SERVER_ERROR:
        return this._handleServerError(error, opts);
      default:
        return this._handleUnknownError(error, opts);
    }
  },

  // 确定错误类型
  _getErrorType(error) {
    if (!error) return this.errorTypes.UNKNOWN_ERROR;

    if (error.statusCode === 401 || (error.message && error.message.includes('未登录'))) {
      return this.errorTypes.AUTH_ERROR;
    }

    if (error.statusCode === 400 || (error.message && error.message.includes('验证失败'))) {
      return this.errorTypes.VALIDATION_ERROR;
    }

    if (error.statusCode === 404 || (error.message && error.message.includes('不存在'))) {
      return this.errorTypes.NOT_FOUND_ERROR;
    }

    if (error.statusCode === 403 || (error.message && error.message.includes('无权限'))) {
      return this.errorTypes.PERMISSION_ERROR;
    }

    if (error.message && (error.message.includes('网络') || error.message.includes('超时'))) {
      return this.errorTypes.NETWORK_ERROR;
    }

    if (error.statusCode >= 500) {
      return this.errorTypes.SERVER_ERROR;
    }

    return this.errorTypes.UNKNOWN_ERROR;
  },

  // 处理认证错误
  _handleAuthError(error, opts) {
    if (opts.showToast) {
      wx.showToast({ title: '请先登录', icon: 'none', duration: opts.toastDuration });
    }

    if (opts.redirectOnAuth) {
      setTimeout(() => {
        wx.navigateTo({ url: '/pages/login/login' });
      }, 1000);
    }

    return { type: this.errorTypes.AUTH_ERROR, handled: true };
  },

  // 其他错误处理方法...
}
```
