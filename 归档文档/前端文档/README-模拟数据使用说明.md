# NebulaLearn 模拟数据使用说明

本文档提供了如何使用NebulaLearn项目的模拟数据和API服务的说明，用于前端开发阶段。

## 目录结构

```
mock-data/
  ├── api/                # 模拟API服务
  │   └── mockApiServer.js  # 模拟API服务器主文件
  ├── json/               # 生成的JSON格式模拟数据
  ├── scripts/            # 模拟数据生成脚本
  │   └── generateMockData.js  # 模拟数据生成主文件
  ├── sql/                # 生成的SQL格式模拟数据
  ├── package.json        # 项目依赖
  ├── setup.sh            # 安装和启动脚本
  └── README.md           # 说明文档
```

## 快速开始

### 安装依赖并生成数据

```bash
cd mock-data
chmod +x setup.sh
./setup.sh
```

或者手动执行以下步骤：

```bash
cd mock-data
npm install
node scripts/generateMockData.js
node api/mockApiServer.js
```

### 模拟API服务器

模拟API服务器默认运行在 http://localhost:3010，提供与实际后端API相同的接口。

## 前端集成

要在前端项目中使用模拟API服务，请修改API基础URL配置：

```javascript
// 在app.js或utils/api.js中修改
const apiBaseUrl = 'http://localhost:3010/api/v1';
```

## 可用的API端点

模拟API服务提供以下核心API端点：

1. **主题相关API**
   - GET `/api/v1/themes` - 获取主题列表
   - GET `/api/v1/themes/:id` - 获取特定主题

2. **学习计划相关API**
   - GET `/api/v1/learning-plans` - 获取学习计划列表
   - GET `/api/v1/learning-plans/:id` - 获取特定学习计划
   - POST `/api/v1/learning-plans` - 创建学习计划
   - PUT `/api/v1/learning-plans/:id` - 更新学习计划
   - DELETE `/api/v1/learning-plans/:id` - 删除学习计划
   - PUT `/api/v1/learning-plans/:id/activate` - 激活学习计划
   - GET `/api/v1/learning-plans/system/default` - 获取系统默认学习计划

3. **练习相关API**
   - GET `/api/v1/exercises` - 获取练习列表
   - GET `/api/v1/exercises/:id` - 获取特定练习
   - POST `/api/v1/exercises/:id/complete` - 标记练习完成

4. **观点相关API**
   - GET `/api/v1/insights` - 获取观点列表
   - GET `/api/v1/insights/:id` - 获取特定观点
   - POST `/api/v1/insights/:id/read` - 标记观点已读

5. **笔记相关API**
   - GET `/api/v1/notes` - 获取笔记列表
   - GET `/api/v1/notes/:id` - 获取特定笔记
   - POST `/api/v1/notes` - 创建笔记
   - PUT `/api/v1/notes/:id` - 更新笔记
   - DELETE `/api/v1/notes/:id` - 删除笔记

6. **每日内容相关API**
   - GET `/api/v1/learning-plans/:planId/daily-content` - 获取每日内容

7. **广场相关API**
   - GET `/api/v1/square/notes` - 获取广场笔记
   - GET `/api/v1/square/tags` - 获取广场标签

8. **认证相关API**
   - POST `/api/v1/auth/login` - 登录

## 示例请求

```javascript
// 获取主题列表
fetch('http://localhost:3010/api/v1/themes')
  .then(response => response.json())
  .then(data => console.log(data));

// 获取学习计划列表
fetch('http://localhost:3010/api/v1/learning-plans')
  .then(response => response.json())
  .then(data => console.log(data));
```

## 自定义模拟数据

如果需要自定义模拟数据，可以修改 `scripts/generateMockData.js` 文件，然后重新生成数据：

```bash
cd mock-data
node scripts/generateMockData.js
```

## 注意事项

- 模拟数据仅用于开发阶段，不应用于生产环境
- 模拟API服务器不包含完整的业务逻辑，仅提供基本的CRUD操作
- 模拟数据之间的关联关系可能不完全符合实际业务需求，需要根据实际情况调整

## 下一步计划

1. **完善模拟数据**
   - 增加更多预定义数据，使模拟数据更加丰富和真实
   - 完善实体之间的关联关系，确保数据一致性

2. **API客户端实现**
   - 实现统一的API客户端架构
   - 实现请求处理、错误处理和缓存机制
   - 实现数据转换层

3. **UI组件升级**
   - 设计并实现回收站组件
   - 升级内容管理组件，添加软删除功能
   - 实现批量操作功能
