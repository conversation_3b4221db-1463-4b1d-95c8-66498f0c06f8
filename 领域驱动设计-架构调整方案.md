# AIBUBB领域驱动设计 - 架构调整方案

## 一、架构调整概述

本文档详细描述了AIBUBB系统从当前架构向领域驱动设计(DDD)架构的调整方案。这一调整旨在提高系统的业务适应性、可维护性和可扩展性，使系统架构更好地反映业务领域模型。

## 二、当前架构分析

### 2.1 现有架构概述

AIBUBB系统当前采用传统的三层架构：

1. **表示层（控制器层）**：处理HTTP请求和响应，包含路由和控制器
2. **业务逻辑层（服务层）**：实现业务逻辑，处理数据转换和验证
3. **数据访问层（仓库层）**：负责数据持久化和查询

### 2.2 现有架构问题

通过分析，我们发现当前架构存在以下问题：

1. **业务逻辑分散**：业务逻辑分散在服务层和控制器层，缺乏明确的领域模型
2. **贫血模型**：模型主要作为数据容器，缺乏行为和业务规则
3. **层次依赖混乱**：部分模块（如统计模块）未遵循分层架构，直接在控制器中访问数据库
4. **业务规则不明确**：业务规则隐藏在代码中，缺乏明确的表达
5. **缺乏领域语言**：代码未充分反映业务领域语言，降低了可理解性

## 三、目标架构设计

### 3.1 DDD分层架构

我们将采用经典的DDD分层架构，包括：

1. **用户界面层（User Interface Layer）**
   - 处理用户请求和响应
   - 转换数据格式，适配前端需求
   - 不包含业务逻辑

2. **应用层（Application Layer）**
   - 协调领域对象完成用户用例
   - 处理事务边界和安全控制
   - 转换领域对象和DTO

3. **领域层（Domain Layer）**
   - 包含业务模型和业务规则
   - 定义实体、值对象、聚合和领域服务
   - 是系统的核心层，不依赖其他层

4. **基础设施层（Infrastructure Layer）**
   - 提供技术支持，如数据持久化、消息传递、外部服务集成等
   - 实现领域层定义的仓库接口
   - 提供技术服务，如日志、缓存、安全等

### 3.2 架构原则

1. **依赖倒置原则**：高层模块不应依赖低层模块，两者都应依赖抽象
2. **领域模型封装**：领域模型应封装业务规则和状态变更逻辑
3. **仓库抽象**：通过仓库接口抽象数据访问，领域层不直接依赖数据库
4. **服务边界明确**：应用服务和领域服务职责明确，不重叠
5. **统一语言**：代码应反映业务领域语言，提高可理解性

## 四、架构调整策略

### 4.1 分层架构调整

#### 4.1.1 用户界面层调整

**当前结构**：
- `backend/controllers/`：控制器
- `backend/routes/`：路由定义

**调整方案**：
1. 重构控制器，移除业务逻辑，仅保留请求处理和响应生成
2. 引入请求验证中间件，统一处理请求验证
3. 引入响应转换器，统一处理响应格式
4. 控制器方法直接调用应用服务，不直接操作领域模型

**示例代码**：
```javascript
// 调整前
class TagController {
  async createTag(req, res) {
    try {
      const { name, categoryId } = req.body;
      // 业务逻辑在控制器中
      if (name.length < 2 || name.length > 4) {
        return res.status(400).json({ error: '标签名称长度应为2-4个字' });
      }
      const tag = await this.tagService.createTag(name, categoryId);
      return res.status(201).json(tag);
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  }
}

// 调整后
class TagController {
  constructor(tagApplicationService) {
    this.tagApplicationService = tagApplicationService;
  }
  
  async createTag(req, res) {
    try {
      const command = { name: req.body.name, categoryId: req.body.categoryId };
      const result = await this.tagApplicationService.createTag(command);
      return res.status(201).json(result);
    } catch (error) {
      // 错误处理委托给统一的错误处理中间件
      next(error);
    }
  }
}
```

#### 4.1.2 应用层调整

**当前结构**：
- `backend/services/`：服务实现

**调整方案**：
1. 创建应用服务，负责协调领域对象完成用例
2. 引入命令（Command）和查询（Query）对象，明确用例意图
3. 实现事务管理和安全控制
4. 转换领域对象和DTO

**新增结构**：
- `backend/application/services/`：应用服务
- `backend/application/commands/`：命令对象
- `backend/application/queries/`：查询对象
- `backend/application/dtos/`：数据传输对象

**示例代码**：
```javascript
// 应用服务
class TagApplicationService {
  constructor(tagRepository, tagDomainService, unitOfWork) {
    this.tagRepository = tagRepository;
    this.tagDomainService = tagDomainService;
    this.unitOfWork = unitOfWork;
  }
  
  async createTag(command) {
    // 验证命令
    if (!command.name) {
      throw new ValidationError('标签名称不能为空');
    }
    
    // 开始事务
    return await this.unitOfWork.runInTransaction(async () => {
      // 创建领域对象
      const tag = new Tag(command.name, command.categoryId);
      
      // 使用领域服务进行业务规则验证
      await this.tagDomainService.validateTag(tag);
      
      // 持久化
      const savedTag = await this.tagRepository.save(tag);
      
      // 转换为DTO返回
      return this.toTagDto(savedTag);
    });
  }
  
  toTagDto(tag) {
    return {
      id: tag.id,
      name: tag.name,
      categoryId: tag.categoryId,
      createdAt: tag.createdAt
    };
  }
}
```

#### 4.1.3 领域层调整

**当前结构**：
- `backend/models/`：数据模型

**调整方案**：
1. 创建富领域模型，包含业务逻辑和验证规则
2. 定义领域事件，捕捉业务中的重要变化
3. 实现领域服务，处理跨实体的业务逻辑
4. 定义仓库接口，抽象数据访问

**新增结构**：
- `backend/domain/models/`：领域模型（实体、值对象、聚合根）
- `backend/domain/events/`：领域事件
- `backend/domain/services/`：领域服务
- `backend/domain/repositories/`：仓库接口

**示例代码**：
```javascript
// 领域模型
class Tag {
  constructor(name, categoryId) {
    this.id = null; // 由仓库生成
    this.name = name;
    this.categoryId = categoryId;
    this.createdAt = new Date();
    this.updatedAt = new Date();
    this.deletedAt = null;
    
    this.validate();
  }
  
  validate() {
    if (!this.name) {
      throw new DomainValidationError('标签名称不能为空');
    }
    
    if (this.name.length < 2 || this.name.length > 4) {
      throw new DomainValidationError('标签名称长度应为2-4个字');
    }
  }
  
  softDelete() {
    if (this.deletedAt) {
      throw new DomainValidationError('标签已被删除');
    }
    
    this.deletedAt = new Date();
    this.updatedAt = new Date();
    
    // 发布领域事件
    DomainEventPublisher.publish(new TagDeletedEvent(this));
  }
  
  restore() {
    if (!this.deletedAt) {
      throw new DomainValidationError('标签未被删除');
    }
    
    this.deletedAt = null;
    this.updatedAt = new Date();
    
    // 发布领域事件
    DomainEventPublisher.publish(new TagRestoredEvent(this));
  }
}

// 领域服务
class TagDomainService {
  constructor(tagRepository) {
    this.tagRepository = tagRepository;
  }
  
  async validateTag(tag) {
    // 检查标签名称是否已存在
    const existingTag = await this.tagRepository.findByName(tag.name);
    if (existingTag && existingTag.id !== tag.id) {
      throw new DomainValidationError('标签名称已存在');
    }
  }
}

// 仓库接口
class TagRepository {
  async findById(id) { throw new Error('Not implemented'); }
  async findByName(name) { throw new Error('Not implemented'); }
  async findByCategoryId(categoryId) { throw new Error('Not implemented'); }
  async save(tag) { throw new Error('Not implemented'); }
  async delete(tag) { throw new Error('Not implemented'); }
}
```

#### 4.1.4 基础设施层调整

**当前结构**：
- `backend/config/`：配置
- `backend/utils/`：工具类
- `backend/middlewares/`：中间件

**调整方案**：
1. 实现领域层定义的仓库接口
2. 提供技术服务，如日志、缓存、安全等
3. 实现领域事件发布订阅机制
4. 提供事务管理和单元工作（Unit of Work）实现

**新增结构**：
- `backend/infrastructure/repositories/`：仓库实现
- `backend/infrastructure/persistence/`：持久化相关
- `backend/infrastructure/services/`：基础设施服务
- `backend/infrastructure/events/`：事件发布订阅实现

**示例代码**：
```javascript
// 仓库实现
class SequelizeTagRepository extends TagRepository {
  constructor(sequelize, tagModel) {
    super();
    this.sequelize = sequelize;
    this.tagModel = tagModel;
  }
  
  async findById(id) {
    const tagData = await this.tagModel.findByPk(id);
    if (!tagData) return null;
    return this.toDomainModel(tagData);
  }
  
  async findByName(name) {
    const tagData = await this.tagModel.findOne({ where: { name } });
    if (!tagData) return null;
    return this.toDomainModel(tagData);
  }
  
  async save(tag) {
    if (tag.id) {
      // 更新
      const tagData = await this.tagModel.findByPk(tag.id);
      if (!tagData) throw new Error('标签不存在');
      
      tagData.name = tag.name;
      tagData.categoryId = tag.categoryId;
      tagData.updatedAt = tag.updatedAt;
      tagData.deletedAt = tag.deletedAt;
      
      await tagData.save();
      return this.toDomainModel(tagData);
    } else {
      // 创建
      const tagData = await this.tagModel.create({
        name: tag.name,
        categoryId: tag.categoryId,
        createdAt: tag.createdAt,
        updatedAt: tag.updatedAt,
        deletedAt: tag.deletedAt
      });
      
      return this.toDomainModel(tagData);
    }
  }
  
  toDomainModel(tagData) {
    const tag = new Tag(tagData.name, tagData.categoryId);
    tag.id = tagData.id;
    tag.createdAt = tagData.createdAt;
    tag.updatedAt = tagData.updatedAt;
    tag.deletedAt = tagData.deletedAt;
    return tag;
  }
}
```

### 4.2 依赖注入调整

**当前结构**：
- `backend/config/serviceContainer.js`：服务容器

**调整方案**：
1. 扩展服务容器，支持更多类型的依赖注入
2. 按层次组织依赖注入配置
3. 支持接口和实现的绑定

**示例代码**：
```javascript
// 依赖注入配置
const container = new Container();

// 基础设施层
container.bind('sequelize', () => new Sequelize(config.database));
container.bind('unitOfWork', (c) => new SequelizeUnitOfWork(c.get('sequelize')));
container.bind('eventBus', () => new EventBus());

// 仓库
container.bind('tagRepository', (c) => new SequelizeTagRepository(c.get('sequelize'), TagModel));
container.bind('learningPlanRepository', (c) => new SequelizeLearningPlanRepository(c.get('sequelize'), LearningPlanModel));

// 领域服务
container.bind('tagDomainService', (c) => new TagDomainService(c.get('tagRepository')));

// 应用服务
container.bind('tagApplicationService', (c) => new TagApplicationService(
  c.get('tagRepository'),
  c.get('tagDomainService'),
  c.get('unitOfWork')
));

// 控制器
container.bind('tagController', (c) => new TagController(c.get('tagApplicationService')));
```

## 五、迁移策略

### 5.1 渐进式迁移

考虑到系统规模和复杂性，我们将采用渐进式迁移策略，分阶段调整架构：

1. **准备阶段**（1周）
   - 建立DDD分层架构的基础结构
   - 创建核心接口和抽象类
   - 实现依赖注入容器

2. **核心领域迁移**（2周）
   - 从标签领域开始，实现完整的DDD架构
   - 包括标签相关的实体、值对象、仓库、服务等
   - 作为其他领域的参考实现

3. **其他领域迁移**（4-6周）
   - 按优先级依次迁移其他领域
   - 学习内容领域（主题、学习模板、学习计划）
   - 内容形式领域（练习、观点、笔记）
   - 用户领域（用户、设置、通知）
   - 游戏化领域（成就、徽章、等级）

4. **横切关注点调整**（2周）
   - 实现领域事件机制
   - 调整事务管理
   - 优化异常处理
   - 完善日志和监控

### 5.2 兼容性策略

为确保迁移过程中系统的稳定性，我们将采取以下兼容性策略：

1. **适配层模式**：为旧架构提供适配层，确保新旧架构可以共存
2. **功能切换**：使用功能开关，控制新架构的逐步启用
3. **双写模式**：在关键功能上实现双写，确保数据一致性
4. **增量测试**：为每个迁移的模块编写全面的测试，确保功能正确性

## 六、实施计划

### 6.1 第一阶段：基础架构搭建（1周）

1. **创建DDD分层结构**
   - 创建领域层、应用层、基础设施层的目录结构
   - 定义核心接口和抽象类
   - 实现基础的领域事件机制

2. **依赖注入容器升级**
   - 扩展现有的服务容器
   - 支持接口和实现的绑定
   - 实现按需加载和延迟初始化

3. **单元工作实现**
   - 实现事务管理
   - 支持仓库的事务一致性
   - 集成领域事件发布

### 6.2 第二阶段：标签领域实现（2周）

1. **领域模型实现**
   - 实现Tag、TagCategory、TagSynonym实体
   - 实现TagFeedbackType值对象
   - 定义领域事件（TagCreated、TagDeleted等）

2. **仓库接口和实现**
   - 定义TagRepository接口
   - 实现SequelizeTagRepository
   - 实现测试用的InMemoryTagRepository

3. **领域服务和应用服务**
   - 实现TagDomainService
   - 实现TagApplicationService
   - 定义命令和查询对象

4. **控制器调整**
   - 重构TagController
   - 集成新的应用服务
   - 调整错误处理和响应格式

### 6.3 第三阶段：学习内容领域实现（3周）

1. **领域模型实现**
   - 实现Theme、LearningTemplate、LearningPlan实体
   - 实现Progress、Difficulty值对象
   - 定义领域事件

2. **仓库和服务实现**
   - 实现相关仓库接口和实现
   - 实现领域服务和应用服务
   - 集成领域事件处理

3. **控制器调整**
   - 重构相关控制器
   - 集成新的应用服务
   - 完善API文档

### 6.4 第四阶段：内容形式领域实现（3周）

类似的实施步骤，针对Exercise、Insight、Note等内容形式领域。

## 七、风险与缓解措施

### 7.1 主要风险

1. **迁移复杂度**：DDD架构调整涉及大量代码重构，可能引入bug
2. **性能影响**：新架构可能引入额外的抽象层，影响性能
3. **团队适应**：团队需要时间适应DDD思想和新架构
4. **进度延迟**：架构调整可能影响其他功能开发进度

### 7.2 缓解措施

1. **增量迁移**：采用渐进式迁移，分模块调整，降低风险
2. **全面测试**：为每个调整的模块编写全面的测试，确保功能正确性
3. **性能监控**：实施性能监控，及时发现和解决性能问题
4. **团队培训**：提供DDD培训，确保团队理解新架构
5. **并行开发**：架构调整与功能开发并行进行，避免进度延迟

## 八、成功标准

1. **业务逻辑内聚**：业务逻辑集中在领域层，提高内聚性
2. **代码可读性**：代码结构清晰，反映业务领域语言
3. **测试覆盖率**：领域层测试覆盖率达到80%以上
4. **性能指标**：关键API响应时间不超过现有架构的110%
5. **开发效率**：新功能开发效率提高，bug率降低

## 九、下一步计划

1. **详细设计**：为每个领域编写详细的设计文档
2. **原型实现**：实现标签领域的原型，验证架构设计
3. **团队培训**：组织DDD培训，确保团队理解新架构
4. **迁移计划细化**：制定详细的迁移计划，包括任务分解和时间安排
