# AIBUBB领域驱动设计 - 标签领域实现

## 一、概述

本文档详细描述了AIBUBB系统标签领域的DDD实现方案，作为核心领域迁移的示范。标签领域是系统中的重要领域，涉及标签的创建、分类、管理和使用，与学习内容、学习计划等其他领域有密切关联。

## 二、领域模型设计

### 2.1 标签聚合

标签聚合是标签领域的核心聚合，包含Tag（标签）、TagSynonym（标签同义词）和TagFeedback（标签反馈）实体。

#### 2.1.1 Tag实体（聚合根）

```typescript
// domain/models/tag/Tag.ts
import { AggregateRootBase } from '../AggregateRootBase';
import { TagCreatedEvent } from '../../events/tag/TagCreatedEvent';
import { TagUpdatedEvent } from '../../events/tag/TagUpdatedEvent';
import { TagDeletedEvent } from '../../events/tag/TagDeletedEvent';
import { TagRestoredEvent } from '../../events/tag/TagRestoredEvent';
import { TagSynonym } from './TagSynonym';
import { TagFeedback } from './TagFeedback';

export class Tag extends AggregateRootBase<number> {
  private _name: string;
  private _categoryId: number | null;
  private _description: string;
  private _creatorId: string;
  private _popularity: number;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _deletedAt: Date | null;
  private _synonyms: TagSynonym[];
  private _feedbacks: TagFeedback[];

  constructor(
    id: number,
    name: string,
    categoryId: number | null,
    description: string,
    creatorId: string,
    popularity: number = 0,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    deletedAt: Date | null = null
  ) {
    super(id);
    this._name = name;
    this._categoryId = categoryId;
    this._description = description;
    this._creatorId = creatorId;
    this._popularity = popularity;
    this._createdAt = createdAt;
    this._updatedAt = updatedAt;
    this._deletedAt = deletedAt;
    this._synonyms = [];
    this._feedbacks = [];

    this.validate();
  }

  // 获取器
  get name(): string { return this._name; }
  get categoryId(): number | null { return this._categoryId; }
  get description(): string { return this._description; }
  get creatorId(): string { return this._creatorId; }
  get popularity(): number { return this._popularity; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }
  get deletedAt(): Date | null { return this._deletedAt; }
  get synonyms(): TagSynonym[] { return [...this._synonyms]; }
  get feedbacks(): TagFeedback[] { return [...this._feedbacks]; }
  get isDeleted(): boolean { return this._deletedAt !== null; }

  // 业务方法
  updateName(name: string): void {
    if (name === this._name) return;

    this.validate({ name });

    const oldName = this._name;
    this._name = name;
    this._updatedAt = new Date();

    this.addEvent(new TagUpdatedEvent(this.id, {
      name: { from: oldName, to: name }
    }));
  }

  updateCategory(categoryId: number | null): void {
    if (categoryId === this._categoryId) return;

    const oldCategoryId = this._categoryId;
    this._categoryId = categoryId;
    this._updatedAt = new Date();

    this.addEvent(new TagUpdatedEvent(this.id, {
      categoryId: { from: oldCategoryId, to: categoryId }
    }));
  }

  updateDescription(description: string): void {
    if (description === this._description) return;

    const oldDescription = this._description;
    this._description = description;
    this._updatedAt = new Date();

    this.addEvent(new TagUpdatedEvent(this.id, {
      description: { from: oldDescription, to: description }
    }));
  }

  increasePopularity(amount: number = 1): void {
    if (amount <= 0) throw new Error('人气增加量必须为正数');

    const oldPopularity = this._popularity;
    this._popularity += amount;
    this._updatedAt = new Date();

    this.addEvent(new TagUpdatedEvent(this.id, {
      popularity: { from: oldPopularity, to: this._popularity }
    }));
  }

  softDelete(): void {
    if (this.isDeleted) throw new Error('标签已被删除');

    this._deletedAt = new Date();
    this._updatedAt = new Date();

    this.addEvent(new TagDeletedEvent(this.id, this._name));
  }

  restore(): void {
    if (!this.isDeleted) throw new Error('标签未被删除');

    this._deletedAt = null;
    this._updatedAt = new Date();

    this.addEvent(new TagRestoredEvent(this.id, this._name));
  }

  addSynonym(synonym: TagSynonym): void {
    // 检查是否已存在相同名称的同义词
    if (this._synonyms.some(s => s.name === synonym.name)) {
      throw new Error(`同义词 "${synonym.name}" 已存在`);
    }

    this._synonyms.push(synonym);
    this._updatedAt = new Date();
  }

  removeSynonym(synonymId: number): void {
    const index = this._synonyms.findIndex(s => s.id === synonymId);
    if (index === -1) throw new Error(`同义词ID ${synonymId} 不存在`);

    this._synonyms.splice(index, 1);
    this._updatedAt = new Date();
  }

  addFeedback(feedback: TagFeedback): void {
    this._feedbacks.push(feedback);
  }

  // 验证方法
  private validate(props: Partial<{ name: string }> = {}): void {
    const name = props.name || this._name;

    if (!name) throw new Error('标签名称不能为空');
    if (name.length < 2 || name.length > 4) {
      throw new Error('标签名称长度应为2-4个字（以两个字为最佳）');
    }
  }

  // 工厂方法
  static create(
    name: string,
    categoryId: number | null,
    description: string,
    creatorId: string
  ): Tag {
    const tag = new Tag(0, name, categoryId, description, creatorId);
    tag.addEvent(new TagCreatedEvent(tag.id, name, categoryId, creatorId));
    return tag;
  }
}
```

#### 2.1.2 TagSynonym实体

```typescript
// domain/models/tag/TagSynonym.ts
import { EntityBase } from '../EntityBase';

export class TagSynonym extends EntityBase<number> {
  private _tagId: number;
  private _name: string;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _deletedAt: Date | null;

  constructor(
    id: number,
    tagId: number,
    name: string,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    deletedAt: Date | null = null
  ) {
    super(id);
    this._tagId = tagId;
    this._name = name;
    this._createdAt = createdAt;
    this._updatedAt = updatedAt;
    this._deletedAt = deletedAt;

    this.validate();
  }

  // 获取器
  get tagId(): number { return this._tagId; }
  get name(): string { return this._name; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }
  get deletedAt(): Date | null { return this._deletedAt; }
  get isDeleted(): boolean { return this._deletedAt !== null; }

  // 业务方法
  updateName(name: string): void {
    if (name === this._name) return;

    this.validate({ name });

    this._name = name;
    this._updatedAt = new Date();
  }

  softDelete(): void {
    if (this.isDeleted) throw new Error('标签同义词已被删除');

    this._deletedAt = new Date();
    this._updatedAt = new Date();
  }

  restore(): void {
    if (!this.isDeleted) throw new Error('标签同义词未被删除');

    this._deletedAt = null;
    this._updatedAt = new Date();
  }

  // 验证方法
  private validate(props: Partial<{ name: string }> = {}): void {
    const name = props.name || this._name;

    if (!name) throw new Error('同义词名称不能为空');
    if (name.length < 1 || name.length > 10) {
      throw new Error('同义词名称长度应为1-10个字');
    }
  }

  // 工厂方法
  static create(tagId: number, name: string): TagSynonym {
    return new TagSynonym(0, tagId, name);
  }
}
```

#### 2.1.3 TagFeedback实体

```typescript
// domain/models/tag/TagFeedback.ts
import { EntityBase } from '../EntityBase';

export enum FeedbackType {
  ERROR = 'error',
  SUGGESTION = 'suggestion',
  QUESTION = 'question'
}

export enum FeedbackStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  RESOLVED = 'resolved',
  REJECTED = 'rejected'
}

export class TagFeedback extends EntityBase<number> {
  private _tagId: number;
  private _userId: string;
  private _feedbackType: FeedbackType;
  private _content: string;
  private _status: FeedbackStatus;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _deletedAt: Date | null;

  constructor(
    id: number,
    tagId: number,
    userId: string,
    feedbackType: FeedbackType,
    content: string,
    status: FeedbackStatus = FeedbackStatus.PENDING,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    deletedAt: Date | null = null
  ) {
    super(id);
    this._tagId = tagId;
    this._userId = userId;
    this._feedbackType = feedbackType;
    this._content = content;
    this._status = status;
    this._createdAt = createdAt;
    this._updatedAt = updatedAt;
    this._deletedAt = deletedAt;

    this.validate();
  }

  // 获取器
  get tagId(): number { return this._tagId; }
  get userId(): string { return this._userId; }
  get feedbackType(): FeedbackType { return this._feedbackType; }
  get content(): string { return this._content; }
  get status(): FeedbackStatus { return this._status; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }
  get deletedAt(): Date | null { return this._deletedAt; }
  get isDeleted(): boolean { return this._deletedAt !== null; }

  // 业务方法
  updateStatus(status: FeedbackStatus): void {
    if (status === this._status) return;

    this._status = status;
    this._updatedAt = new Date();
  }

  softDelete(): void {
    if (this.isDeleted) throw new Error('标签反馈已被删除');

    this._deletedAt = new Date();
    this._updatedAt = new Date();
  }

  restore(): void {
    if (!this.isDeleted) throw new Error('标签反馈未被删除');

    this._deletedAt = null;
    this._updatedAt = new Date();
  }

  // 验证方法
  private validate(): void {
    if (!this._content) throw new Error('反馈内容不能为空');
    if (this._content.length > 500) throw new Error('反馈内容不能超过500个字符');
  }

  // 工厂方法
  static create(
    tagId: number,
    userId: string,
    feedbackType: FeedbackType,
    content: string
  ): TagFeedback {
    return new TagFeedback(0, tagId, userId, feedbackType, content);
  }
}
```

### 2.2 标签分类聚合

标签分类聚合包含TagCategory（标签分类）实体。

#### 2.2.1 TagCategory实体（聚合根）

```typescript
// domain/models/tag/TagCategory.ts
import { AggregateRootBase } from '../AggregateRootBase';
import { TagCategoryCreatedEvent } from '../../events/tag/TagCategoryCreatedEvent';
import { TagCategoryUpdatedEvent } from '../../events/tag/TagCategoryUpdatedEvent';
import { TagCategoryDeletedEvent } from '../../events/tag/TagCategoryDeletedEvent';
import { TagCategoryRestoredEvent } from '../../events/tag/TagCategoryRestoredEvent';

export class TagCategory extends AggregateRootBase<number> {
  private _name: string;
  private _description: string;
  private _parentId: number | null;
  private _level: number;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _deletedAt: Date | null;

  constructor(
    id: number,
    name: string,
    description: string,
    parentId: number | null = null,
    level: number = 1,
    createdAt: Date = new Date(),
    updatedAt: Date = new Date(),
    deletedAt: Date | null = null
  ) {
    super(id);
    this._name = name;
    this._description = description;
    this._parentId = parentId;
    this._level = level;
    this._createdAt = createdAt;
    this._updatedAt = updatedAt;
    this._deletedAt = deletedAt;

    this.validate();
  }

  // 获取器
  get name(): string { return this._name; }
  get description(): string { return this._description; }
  get parentId(): number | null { return this._parentId; }
  get level(): number { return this._level; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }
  get deletedAt(): Date | null { return this._deletedAt; }
  get isDeleted(): boolean { return this._deletedAt !== null; }

  // 业务方法
  updateName(name: string): void {
    if (name === this._name) return;

    this.validate({ name });

    const oldName = this._name;
    this._name = name;
    this._updatedAt = new Date();

    this.addEvent(new TagCategoryUpdatedEvent(this.id, {
      name: { from: oldName, to: name }
    }));
  }

  updateDescription(description: string): void {
    if (description === this._description) return;

    const oldDescription = this._description;
    this._description = description;
    this._updatedAt = new Date();

    this.addEvent(new TagCategoryUpdatedEvent(this.id, {
      description: { from: oldDescription, to: description }
    }));
  }

  updateParent(parentId: number | null): void {
    if (parentId === this._parentId) return;

    // 防止循环引用
    if (parentId === this.id) throw new Error('分类不能将自己设为父分类');

    const oldParentId = this._parentId;
    this._parentId = parentId;
    this._level = parentId ? 2 : 1; // 简化处理，只支持两级分类
    this._updatedAt = new Date();

    this.addEvent(new TagCategoryUpdatedEvent(this.id, {
      parentId: { from: oldParentId, to: parentId },
      level: { from: oldParentId ? 2 : 1, to: this._level }
    }));
  }

  softDelete(): void {
    if (this.isDeleted) throw new Error('标签分类已被删除');

    this._deletedAt = new Date();
    this._updatedAt = new Date();

    this.addEvent(new TagCategoryDeletedEvent(this.id, this._name));
  }

  restore(): void {
    if (!this.isDeleted) throw new Error('标签分类未被删除');

    this._deletedAt = null;
    this._updatedAt = new Date();

    this.addEvent(new TagCategoryRestoredEvent(this.id, this._name));
  }

  // 验证方法
  private validate(props: Partial<{ name: string }> = {}): void {
    const name = props.name || this._name;

    if (!name) throw new Error('分类名称不能为空');
    if (name.length < 2 || name.length > 10) {
      throw new Error('分类名称长度应为2-10个字');
    }
  }

  // 工厂方法
  static create(
    name: string,
    description: string,
    parentId: number | null = null
  ): TagCategory {
    const level = parentId ? 2 : 1;
    const category = new TagCategory(0, name, description, parentId, level);
    category.addEvent(new TagCategoryCreatedEvent(category.id, name, description, parentId, level));
    return category;
  }
}
```

## 三、领域事件设计

### 3.1 标签领域事件

#### 3.1.1 TagCreatedEvent

```typescript
// domain/events/tag/TagCreatedEvent.ts
import { DomainEventBase } from '../DomainEventBase';

export class TagCreatedEvent extends DomainEventBase {
  constructor(
    tagId: number,
    name: string,
    categoryId: number | null,
    creatorId: string
  ) {
    super(
      'tag.created',
      tagId.toString(),
      'Tag',
      {
        name,
        categoryId,
        creatorId
      }
    );
  }
}
```

#### 3.1.2 TagUpdatedEvent

```typescript
// domain/events/tag/TagUpdatedEvent.ts
import { DomainEventBase } from '../DomainEventBase';

export class TagUpdatedEvent extends DomainEventBase {
  constructor(
    tagId: number,
    changes: {
      name?: { from: string, to: string },
      categoryId?: { from: number | null, to: number | null },
      description?: { from: string, to: string },
      popularity?: { from: number, to: number }
    }
  ) {
    super(
      'tag.updated',
      tagId.toString(),
      'Tag',
      { changes }
    );
  }
}
```

#### 3.1.3 TagDeletedEvent

```typescript
// domain/events/tag/TagDeletedEvent.ts
import { DomainEventBase } from '../DomainEventBase';

export class TagDeletedEvent extends DomainEventBase {
  constructor(tagId: number, name: string) {
    super(
      'tag.deleted',
      tagId.toString(),
      'Tag',
      { name }
    );
  }
}
```

#### 3.1.4 TagRestoredEvent

```typescript
// domain/events/tag/TagRestoredEvent.ts
import { DomainEventBase } from '../DomainEventBase';

export class TagRestoredEvent extends DomainEventBase {
  constructor(tagId: number, name: string) {
    super(
      'tag.restored',
      tagId.toString(),
      'Tag',
      { name }
    );
  }
}
```

## 四、仓库接口设计

### 4.1 标签仓库接口

```typescript
// domain/repositories/tag/TagRepository.ts
import { Repository } from '../Repository';
import { Tag } from '../../models/tag/Tag';

export interface TagRepository extends Repository<Tag, number> {
  findByName(name: string): Promise<Tag | null>;
  findByCategoryId(categoryId: number): Promise<Tag[]>;
  findByPopularity(minPopularity: number, limit?: number): Promise<Tag[]>;
  findWithSynonyms(tagId: number): Promise<Tag | null>;
  findWithFeedbacks(tagId: number): Promise<Tag | null>;
  searchByKeyword(keyword: string, limit?: number): Promise<Tag[]>;
}
```

### 4.2 标签分类仓库接口

```typescript
// domain/repositories/tag/TagCategoryRepository.ts
import { Repository } from '../Repository';
import { TagCategory } from '../../models/tag/TagCategory';

export interface TagCategoryRepository extends Repository<TagCategory, number> {
  findByName(name: string): Promise<TagCategory | null>;
  findByParentId(parentId: number): Promise<TagCategory[]>;
  findRootCategories(): Promise<TagCategory[]>;
  findWithChildren(categoryId: number): Promise<{ category: TagCategory, children: TagCategory[] }>;
}
```

## 五、领域服务设计

### 5.1 标签领域服务

```typescript
// domain/services/tag/TagDomainService.ts
import { Tag } from '../../models/tag/Tag';
import { TagRepository } from '../../repositories/tag/TagRepository';
import { TagSynonym } from '../../models/tag/TagSynonym';

export class TagDomainService {
  constructor(private readonly tagRepository: TagRepository) {}

  async validateTag(tag: Tag): Promise<void> {
    // 检查标签名称是否已存在
    const existingTag = await this.tagRepository.findByName(tag.name);
    if (existingTag && existingTag.id !== tag.id) {
      throw new Error(`标签名称 "${tag.name}" 已存在`);
    }

    // 检查同义词是否与其他标签名称冲突
    for (const synonym of tag.synonyms) {
      const tagWithSameName = await this.tagRepository.findByName(synonym.name);
      if (tagWithSameName && tagWithSameName.id !== tag.id) {
        throw new Error(`同义词 "${synonym.name}" 与现有标签名称冲突`);
      }
    }
  }

  async mergeTags(sourceTagId: number, targetTagId: number): Promise<void> {
    if (sourceTagId === targetTagId) {
      throw new Error('源标签和目标标签不能相同');
    }

    const sourceTag = await this.tagRepository.findWithSynonyms(sourceTagId);
    const targetTag = await this.tagRepository.findWithSynonyms(targetTagId);

    if (!sourceTag) throw new Error(`源标签ID ${sourceTagId} 不存在`);
    if (!targetTag) throw new Error(`目标标签ID ${targetTagId} 不存在`);

    // 将源标签的同义词添加到目标标签
    for (const synonym of sourceTag.synonyms) {
      try {
        const newSynonym = TagSynonym.create(targetTag.id, synonym.name);
        targetTag.addSynonym(newSynonym);
      } catch (error) {
        // 忽略已存在的同义词
        if (!(error instanceof Error && error.message.includes('已存在'))) {
          throw error;
        }
      }
    }

    // 将源标签名称添加为目标标签的同义词
    try {
      const newSynonym = TagSynonym.create(targetTag.id, sourceTag.name);
      targetTag.addSynonym(newSynonym);
    } catch (error) {
      // 忽略已存在的同义词
      if (!(error instanceof Error && error.message.includes('已存在'))) {
        throw error;
      }
    }

    // 软删除源标签
    sourceTag.softDelete();

    // 保存更改
    await this.tagRepository.save(targetTag);
    await this.tagRepository.save(sourceTag);
  }

  async suggestRelatedTags(tagId: number, limit: number = 5): Promise<Tag[]> {
    const tag = await this.tagRepository.findById(tagId);
    if (!tag) throw new Error(`标签ID ${tagId} 不存在`);

    // 这里可以实现更复杂的相关标签推荐算法
    // 简化实现：返回同类别下的热门标签
    if (tag.categoryId) {
      const categoryTags = await this.tagRepository.findByCategoryId(tag.categoryId);
      return categoryTags
        .filter(t => t.id !== tag.id)
        .sort((a, b) => b.popularity - a.popularity)
        .slice(0, limit);
    }

    // 如果没有分类，返回热门标签
    return this.tagRepository.findByPopularity(10, limit);
  }
}
```

## 六、应用服务设计

### 6.1 标签应用服务

```typescript
// application/services/tag/TagApplicationService.ts
import { Tag } from '../../../domain/models/tag/Tag';
import { TagSynonym } from '../../../domain/models/tag/TagSynonym';
import { TagRepository } from '../../../domain/repositories/tag/TagRepository';
import { TagDomainService } from '../../../domain/services/tag/TagDomainService';
import { UnitOfWork } from '../../../infrastructure/persistence/UnitOfWork';
import { CreateTagCommand } from '../../commands/tag/CreateTagCommand';
import { UpdateTagCommand } from '../../commands/tag/UpdateTagCommand';
import { DeleteTagCommand } from '../../commands/tag/DeleteTagCommand';
import { RestoreTagCommand } from '../../commands/tag/RestoreTagCommand';
import { AddTagSynonymCommand } from '../../commands/tag/AddTagSynonymCommand';
import { RemoveTagSynonymCommand } from '../../commands/tag/RemoveTagSynonymCommand';
import { MergeTagsCommand } from '../../commands/tag/MergeTagsCommand';
import { GetTagQuery } from '../../queries/tag/GetTagQuery';
import { SearchTagsQuery } from '../../queries/tag/SearchTagsQuery';
import { TagDto } from '../../dtos/tag/TagDto';
import { TagSynonymDto } from '../../dtos/tag/TagSynonymDto';

export class TagApplicationService {
  constructor(
    private readonly tagRepository: TagRepository,
    private readonly tagDomainService: TagDomainService,
    private readonly unitOfWork: UnitOfWork
  ) {}

  // 命令处理方法
  async createTag(command: CreateTagCommand): Promise<TagDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const tag = Tag.create(
        command.name,
        command.categoryId,
        command.description,
        command.creatorId
      );

      await this.tagDomainService.validateTag(tag);
      const savedTag = await this.tagRepository.save(tag);

      return this.toTagDto(savedTag);
    });
  }

  async updateTag(command: UpdateTagCommand): Promise<TagDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const tag = await this.tagRepository.findById(command.tagId);
      if (!tag) throw new Error(`标签ID ${command.tagId} 不存在`);

      if (command.name !== undefined && command.name !== tag.name) {
        tag.updateName(command.name);
      }

      if (command.categoryId !== undefined && command.categoryId !== tag.categoryId) {
        tag.updateCategory(command.categoryId);
      }

      if (command.description !== undefined && command.description !== tag.description) {
        tag.updateDescription(command.description);
      }

      await this.tagDomainService.validateTag(tag);
      const savedTag = await this.tagRepository.save(tag);

      return this.toTagDto(savedTag);
    });
  }

  async deleteTag(command: DeleteTagCommand): Promise<void> {
    return this.unitOfWork.runInTransaction(async () => {
      const tag = await this.tagRepository.findById(command.tagId);
      if (!tag) throw new Error(`标签ID ${command.tagId} 不存在`);

      tag.softDelete();
      await this.tagRepository.save(tag);
    });
  }

  async restoreTag(command: RestoreTagCommand): Promise<void> {
    return this.unitOfWork.runInTransaction(async () => {
      const tag = await this.tagRepository.findById(command.tagId);
      if (!tag) throw new Error(`标签ID ${command.tagId} 不存在`);

      tag.restore();
      await this.tagRepository.save(tag);
    });
  }

  async addTagSynonym(command: AddTagSynonymCommand): Promise<TagDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const tag = await this.tagRepository.findWithSynonyms(command.tagId);
      if (!tag) throw new Error(`标签ID ${command.tagId} 不存在`);

      const synonym = TagSynonym.create(tag.id, command.name);
      tag.addSynonym(synonym);

      await this.tagDomainService.validateTag(tag);
      const savedTag = await this.tagRepository.save(tag);

      return this.toTagDto(savedTag);
    });
  }

  async removeTagSynonym(command: RemoveTagSynonymCommand): Promise<TagDto> {
    return this.unitOfWork.runInTransaction(async () => {
      const tag = await this.tagRepository.findWithSynonyms(command.tagId);
      if (!tag) throw new Error(`标签ID ${command.tagId} 不存在`);

      tag.removeSynonym(command.synonymId);

      const savedTag = await this.tagRepository.save(tag);

      return this.toTagDto(savedTag);
    });
  }

  async mergeTags(command: MergeTagsCommand): Promise<TagDto> {
    return this.unitOfWork.runInTransaction(async () => {
      await this.tagDomainService.mergeTags(command.sourceTagId, command.targetTagId);

      const targetTag = await this.tagRepository.findById(command.targetTagId);
      if (!targetTag) throw new Error(`目标标签ID ${command.targetTagId} 不存在`);

      return this.toTagDto(targetTag);
    });
  }

  // 查询处理方法
  async getTag(query: GetTagQuery): Promise<TagDto | null> {
    const tag = await this.tagRepository.findById(query.tagId);
    return tag ? this.toTagDto(tag) : null;
  }

  async searchTags(query: SearchTagsQuery): Promise<TagDto[]> {
    let tags: Tag[] = [];

    if (query.keyword) {
      tags = await this.tagRepository.searchByKeyword(query.keyword, query.limit);
    } else if (query.categoryId) {
      tags = await this.tagRepository.findByCategoryId(query.categoryId);
    } else {
      tags = await this.tagRepository.findAll();
    }

    // 应用过滤条件
    if (query.includeDeleted === false) {
      tags = tags.filter(tag => !tag.isDeleted);
    }

    // 应用排序
    if (query.sortBy === 'popularity') {
      tags = tags.sort((a, b) => b.popularity - a.popularity);
    } else if (query.sortBy === 'name') {
      tags = tags.sort((a, b) => a.name.localeCompare(b.name));
    }

    // 应用分页
    if (query.limit) {
      tags = tags.slice(0, query.limit);
    }

    return tags.map(tag => this.toTagDto(tag));
  }

  async getSuggestedTags(tagId: number, limit: number = 5): Promise<TagDto[]> {
    const tags = await this.tagDomainService.suggestRelatedTags(tagId, limit);
    return tags.map(tag => this.toTagDto(tag));
  }

  // DTO转换方法
  private toTagDto(tag: Tag): TagDto {
    return {
      id: tag.id,
      name: tag.name,
      categoryId: tag.categoryId,
      description: tag.description,
      popularity: tag.popularity,
      creatorId: tag.creatorId,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
      deletedAt: tag.deletedAt,
      isDeleted: tag.isDeleted,
      synonyms: tag.synonyms.map(synonym => this.toTagSynonymDto(synonym))
    };
  }

  private toTagSynonymDto(synonym: TagSynonym): TagSynonymDto {
    return {
      id: synonym.id,
      tagId: synonym.tagId,
      name: synonym.name,
      createdAt: synonym.createdAt,
      updatedAt: synonym.updatedAt,
      deletedAt: synonym.deletedAt,
      isDeleted: synonym.isDeleted
    };
  }
}
```

## 七、仓库实现

### 7.1 标签仓库实现

```typescript
// infrastructure/persistence/repositories/tag/SequelizeTagRepository.ts
import { Tag } from '../../../../domain/models/tag/Tag';
import { TagSynonym } from '../../../../domain/models/tag/TagSynonym';
import { TagFeedback, FeedbackType, FeedbackStatus } from '../../../../domain/models/tag/TagFeedback';
import { TagRepository } from '../../../../domain/repositories/tag/TagRepository';
import { RepositoryBase } from '../RepositoryBase';
import { UnitOfWork } from '../../UnitOfWork';
import { EventPublisher } from '../../../events/EventPublisher';
import { Sequelize, Op } from 'sequelize';
import { TagModel, TagSynonymModel, TagFeedbackModel } from '../../models/tag';

export class SequelizeTagRepository extends RepositoryBase<Tag, number> implements TagRepository {
  constructor(
    unitOfWork: UnitOfWork,
    eventPublisher: EventPublisher,
    private readonly sequelize: Sequelize,
    private readonly tagModel: typeof TagModel,
    private readonly tagSynonymModel: typeof TagSynonymModel,
    private readonly tagFeedbackModel: typeof TagFeedbackModel
  ) {
    super(unitOfWork, eventPublisher);
  }

  async findById(id: number): Promise<Tag | null> {
    const tagData = await this.tagModel.findByPk(id);
    if (!tagData) return null;

    return this.toDomainModel(tagData);
  }

  async findByName(name: string): Promise<Tag | null> {
    const tagData = await this.tagModel.findOne({
      where: { name }
    });

    if (!tagData) return null;

    return this.toDomainModel(tagData);
  }

  async findByCategoryId(categoryId: number): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll({
      where: { category_id: categoryId }
    });

    return tagData.map(tag => this.toDomainModel(tag));
  }

  async findByPopularity(minPopularity: number, limit?: number): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll({
      where: { popularity: { [Op.gte]: minPopularity } },
      order: [['popularity', 'DESC']],
      limit: limit
    });

    return tagData.map(tag => this.toDomainModel(tag));
  }

  async findWithSynonyms(tagId: number): Promise<Tag | null> {
    const tagData = await this.tagModel.findByPk(tagId, {
      include: [
        {
          model: this.tagSynonymModel,
          as: 'synonyms'
        }
      ]
    });

    if (!tagData) return null;

    return this.toDomainModelWithRelations(tagData);
  }

  async findWithFeedbacks(tagId: number): Promise<Tag | null> {
    const tagData = await this.tagModel.findByPk(tagId, {
      include: [
        {
          model: this.tagFeedbackModel,
          as: 'feedbacks'
        }
      ]
    });

    if (!tagData) return null;

    return this.toDomainModelWithRelations(tagData);
  }

  async findAll(): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll();
    return tagData.map(tag => this.toDomainModel(tag));
  }

  async searchByKeyword(keyword: string, limit?: number): Promise<Tag[]> {
    const tagData = await this.tagModel.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ]
      },
      limit: limit
    });

    return tagData.map(tag => this.toDomainModel(tag));
  }

  protected async doSave(tag: Tag): Promise<Tag> {
    const transaction = (this.unitOfWork as any).getTransaction();

    if (tag.id === 0) {
      // 创建新标签
      const tagData = await this.tagModel.create({
        name: tag.name,
        category_id: tag.categoryId,
        description: tag.description,
        creator_id: tag.creatorId,
        popularity: tag.popularity,
        created_at: tag.createdAt,
        updated_at: tag.updatedAt,
        deleted_at: tag.deletedAt
      }, { transaction });

      // 更新ID
      const newTag = new Tag(
        tagData.id,
        tag.name,
        tag.categoryId,
        tag.description,
        tag.creatorId,
        tag.popularity,
        tag.createdAt,
        tag.updatedAt,
        tag.deletedAt
      );

      // 保存同义词
      for (const synonym of tag.synonyms) {
        const synonymData = await this.tagSynonymModel.create({
          tag_id: tagData.id,
          name: synonym.name,
          created_at: synonym.createdAt,
          updated_at: synonym.updatedAt,
          deleted_at: synonym.deletedAt
        }, { transaction });

        newTag.addSynonym(new TagSynonym(
          synonymData.id,
          tagData.id,
          synonym.name,
          synonym.createdAt,
          synonym.updatedAt,
          synonym.deletedAt
        ));
      }

      return newTag;
    } else {
      // 更新现有标签
      await this.tagModel.update({
        name: tag.name,
        category_id: tag.categoryId,
        description: tag.description,
        popularity: tag.popularity,
        updated_at: tag.updatedAt,
        deleted_at: tag.deletedAt
      }, {
        where: { id: tag.id },
        transaction
      });

      // 处理同义词（简化实现，实际应该处理更新和删除）
      for (const synonym of tag.synonyms) {
        if (synonym.id === 0) {
          await this.tagSynonymModel.create({
            tag_id: tag.id,
            name: synonym.name,
            created_at: synonym.createdAt,
            updated_at: synonym.updatedAt,
            deleted_at: synonym.deletedAt
          }, { transaction });
        }
      }

      return tag;
    }
  }

  protected async doDelete(tag: Tag): Promise<void> {
    const transaction = (this.unitOfWork as any).getTransaction();

    // 软删除
    await this.tagModel.update({
      deleted_at: new Date(),
      updated_at: new Date()
    }, {
      where: { id: tag.id },
      transaction
    });
  }

  private toDomainModel(tagData: any): Tag {
    return new Tag(
      tagData.id,
      tagData.name,
      tagData.category_id,
      tagData.description,
      tagData.creator_id,
      tagData.popularity,
      tagData.created_at,
      tagData.updated_at,
      tagData.deleted_at
    );
  }

  private toDomainModelWithRelations(tagData: any): Tag {
    const tag = this.toDomainModel(tagData);

    // 添加同义词
    if (tagData.synonyms) {
      for (const synonymData of tagData.synonyms) {
        tag.addSynonym(new TagSynonym(
          synonymData.id,
          tag.id,
          synonymData.name,
          synonymData.created_at,
          synonymData.updated_at,
          synonymData.deleted_at
        ));
      }
    }

    // 添加反馈
    if (tagData.feedbacks) {
      for (const feedbackData of tagData.feedbacks) {
        tag.addFeedback(new TagFeedback(
          feedbackData.id,
          tag.id,
          feedbackData.user_id,
          feedbackData.feedback_type as FeedbackType,
          feedbackData.content,
          feedbackData.status as FeedbackStatus,
          feedbackData.created_at,
          feedbackData.updated_at,
          feedbackData.deleted_at
        ));
      }
    }

    return tag;
  }
}
```

## 八、控制器设计

### 8.1 标签控制器

```typescript
// interfaces/api/controllers/tag/TagController.ts
import { Request, Response, NextFunction } from 'express';
import { TagApplicationService } from '../../../../application/services/tag/TagApplicationService';
import { CreateTagCommand } from '../../../../application/commands/tag/CreateTagCommand';
import { UpdateTagCommand } from '../../../../application/commands/tag/UpdateTagCommand';
import { DeleteTagCommand } from '../../../../application/commands/tag/DeleteTagCommand';
import { RestoreTagCommand } from '../../../../application/commands/tag/RestoreTagCommand';
import { AddTagSynonymCommand } from '../../../../application/commands/tag/AddTagSynonymCommand';
import { RemoveTagSynonymCommand } from '../../../../application/commands/tag/RemoveTagSynonymCommand';
import { MergeTagsCommand } from '../../../../application/commands/tag/MergeTagsCommand';
import { GetTagQuery } from '../../../../application/queries/tag/GetTagQuery';
import { SearchTagsQuery } from '../../../../application/queries/tag/SearchTagsQuery';

export class TagController {
  constructor(private readonly tagApplicationService: TagApplicationService) {}

  async createTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: CreateTagCommand = {
        name: req.body.name,
        categoryId: req.body.categoryId,
        description: req.body.description,
        creatorId: req.user.id
      };

      const result = await this.tagApplicationService.createTag(command);
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  async updateTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: UpdateTagCommand = {
        tagId: parseInt(req.params.id),
        name: req.body.name,
        categoryId: req.body.categoryId,
        description: req.body.description
      };

      const result = await this.tagApplicationService.updateTag(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  async deleteTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: DeleteTagCommand = {
        tagId: parseInt(req.params.id)
      };

      await this.tagApplicationService.deleteTag(command);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  }

  async restoreTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RestoreTagCommand = {
        tagId: parseInt(req.params.id)
      };

      await this.tagApplicationService.restoreTag(command);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  }

  async getTag(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: GetTagQuery = {
        tagId: parseInt(req.params.id)
      };

      const result = await this.tagApplicationService.getTag(query);

      if (!result) {
        res.status(404).json({ message: '标签不存在' });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  async searchTags(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query: SearchTagsQuery = {
        keyword: req.query.keyword as string,
        categoryId: req.query.categoryId ? parseInt(req.query.categoryId as string) : undefined,
        includeDeleted: req.query.includeDeleted === 'true',
        sortBy: req.query.sortBy as 'name' | 'popularity',
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined
      };

      const results = await this.tagApplicationService.searchTags(query);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }

  async addTagSynonym(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: AddTagSynonymCommand = {
        tagId: parseInt(req.params.id),
        name: req.body.name
      };

      const result = await this.tagApplicationService.addTagSynonym(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  async removeTagSynonym(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: RemoveTagSynonymCommand = {
        tagId: parseInt(req.params.id),
        synonymId: parseInt(req.params.synonymId)
      };

      const result = await this.tagApplicationService.removeTagSynonym(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  async mergeTags(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const command: MergeTagsCommand = {
        sourceTagId: parseInt(req.body.sourceTagId),
        targetTagId: parseInt(req.body.targetTagId)
      };

      const result = await this.tagApplicationService.mergeTags(command);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  async getSuggestedTags(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const tagId = parseInt(req.params.id);
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 5;

      const results = await this.tagApplicationService.getSuggestedTags(tagId, limit);
      res.status(200).json(results);
    } catch (error) {
      next(error);
    }
  }
}
```

### 8.2 标签路由

```typescript
// interfaces/api/routes/tag/tagRoutes.ts
import { Router } from 'express';
import { TagController } from '../../controllers/tag/TagController';
import { authMiddleware } from '../../middlewares/authMiddleware';
import { validateRequest } from '../../middlewares/validateRequestMiddleware';
import { createTagSchema, updateTagSchema, addSynonymSchema, mergeTagsSchema } from '../../validators/tag/tagValidators';

export const tagRoutes = (router: Router, tagController: TagController): void => {
  // 标签CRUD
  router.post(
    '/tags',
    authMiddleware,
    validateRequest(createTagSchema),
    tagController.createTag.bind(tagController)
  );

  router.get(
    '/tags/:id',
    tagController.getTag.bind(tagController)
  );

  router.put(
    '/tags/:id',
    authMiddleware,
    validateRequest(updateTagSchema),
    tagController.updateTag.bind(tagController)
  );

  router.delete(
    '/tags/:id',
    authMiddleware,
    tagController.deleteTag.bind(tagController)
  );

  router.post(
    '/tags/:id/restore',
    authMiddleware,
    tagController.restoreTag.bind(tagController)
  );

  router.get(
    '/tags',
    tagController.searchTags.bind(tagController)
  );

  // 标签同义词
  router.post(
    '/tags/:id/synonyms',
    authMiddleware,
    validateRequest(addSynonymSchema),
    tagController.addTagSynonym.bind(tagController)
  );

  router.delete(
    '/tags/:id/synonyms/:synonymId',
    authMiddleware,
    tagController.removeTagSynonym.bind(tagController)
  );

  // 标签合并
  router.post(
    '/tags/merge',
    authMiddleware,
    validateRequest(mergeTagsSchema),
    tagController.mergeTags.bind(tagController)
  );

  // 相关标签推荐
  router.get(
    '/tags/:id/suggested',
    tagController.getSuggestedTags.bind(tagController)
  );
};
```

## 九、依赖注入配置

```typescript
// infrastructure/config/container.ts
import { ContainerImpl } from './ContainerImpl';
import { TagRepository } from '../../domain/repositories/tag/TagRepository';
import { TagDomainService } from '../../domain/services/tag/TagDomainService';
import { TagApplicationService } from '../../application/services/tag/TagApplicationService';
import { TagController } from '../../interfaces/api/controllers/tag/TagController';
import { SequelizeTagRepository } from '../persistence/repositories/tag/SequelizeTagRepository';
import { UnitOfWork } from '../persistence/UnitOfWork';
import { SequelizeUnitOfWork } from '../persistence/SequelizeUnitOfWork';
import { EventPublisher } from '../events/EventPublisher';
import { EventPublisherImpl } from '../events/EventPublisherImpl';
import { EventBus } from '../events/EventBus';
import { EventBusImpl } from '../events/EventBusImpl';
import { Sequelize } from 'sequelize';
import { TagModel, TagSynonymModel, TagFeedbackModel } from '../persistence/models/tag';

export function configureTagDependencies(container: ContainerImpl): void {
  // 基础设施
  if (!container.has('sequelize')) {
    container.bind('sequelize', () => new Sequelize(/* 配置 */));
  }

  if (!container.has('unitOfWork')) {
    container.bind('unitOfWork', (c) => new SequelizeUnitOfWork(c.get('sequelize')));
  }

  if (!container.has('eventBus')) {
    container.bind('eventBus', () => new EventBusImpl());
  }

  if (!container.has('eventPublisher')) {
    container.bind('eventPublisher', (c) => new EventPublisherImpl(c.get('eventBus')));
  }

  // 仓库
  container.bind<TagRepository>('tagRepository', (c) => new SequelizeTagRepository(
    c.get('unitOfWork'),
    c.get('eventPublisher'),
    c.get('sequelize'),
    TagModel,
    TagSynonymModel,
    TagFeedbackModel
  ));

  // 领域服务
  container.bind<TagDomainService>('tagDomainService', (c) => new TagDomainService(
    c.get('tagRepository')
  ));

  // 应用服务
  container.bind<TagApplicationService>('tagApplicationService', (c) => new TagApplicationService(
    c.get('tagRepository'),
    c.get('tagDomainService'),
    c.get('unitOfWork')
  ));

  // 控制器
  container.bind<TagController>('tagController', (c) => new TagController(
    c.get('tagApplicationService')
  ));
}
```

## 十、总结与下一步

### 10.1 标签领域实现总结

本文档详细描述了AIBUBB系统标签领域的DDD实现方案，包括：

1. **领域模型设计**：设计了Tag、TagSynonym、TagFeedback和TagCategory等实体，明确了它们的属性、行为和关系。
2. **领域事件设计**：设计了TagCreatedEvent、TagUpdatedEvent等领域事件，捕捉业务中的重要变化。
3. **仓库接口设计**：设计了TagRepository和TagCategoryRepository接口，抽象数据访问。
4. **领域服务设计**：设计了TagDomainService，处理跨实体的业务逻辑，如标签验证和标签合并。
5. **应用服务设计**：设计了TagApplicationService，协调领域对象完成用例，处理命令和查询。
6. **仓库实现**：实现了SequelizeTagRepository，将领域模型映射到数据库。
7. **控制器设计**：设计了TagController，处理HTTP请求和响应。
8. **依赖注入配置**：配置了标签领域的依赖注入，实现松耦合。

### 10.2 下一步工作

1. **实现标签分类领域**：完成TagCategory相关的实现，包括仓库、应用服务和控制器。
2. **单元测试**：为标签领域的各个组件编写单元测试，确保功能正确性。
3. **集成测试**：编写集成测试，验证标签领域与其他领域的集成。
4. **文档更新**：更新API文档，反映新的标签领域API。
5. **迁移其他领域**：基于标签领域的实现经验，迁移学习内容领域、内容形式领域等其他领域。
```
