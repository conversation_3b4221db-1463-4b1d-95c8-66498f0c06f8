# 首页 Canvas 组件说明

## 概述

首页 Canvas 组件是 AI 互动泡泡项目的核心视觉和交互元素，包括泡泡组件和星星组件两种展示形式。通过本次重构和升级，我们将原本与首页紧密耦合的实现拆分为独立的模块化组件，提高了代码的可维护性和复用性，同时优化了视觉效果和交互体验。

## 组件架构

### 文件结构

```
components/
  canvas-base/
    index.js       - 基础画布组件
    index.wxml     - 基础画布模板
    index.wxss     - 基础画布样式
    index.json     - 组件配置
  bubble-canvas/
    index.js       - 泡泡画布组件
    index.wxml     - 泡泡画布模板
    index.wxss     - 泡泡画布样式
    index.json     - 组件配置
  star-canvas/
    index.js       - 星星画布组件
    index.wxml     - 星星画布模板
    index.wxss     - 星星画布样式
    index.json     - 组件配置
utils/
  theme-manager.js - 主题数据管理
  canvas-manager.js - 画布管理器
```

### 组件层次结构

1. **基础画布组件 (canvas-base)**：
   - 实现共享的基础功能
   - 提供统一的接口和生命周期管理
   - 处理通用的动画和交互逻辑

2. **泡泡画布组件 (bubble-canvas)**：
   - 继承自基础画布组件
   - 实现圆形泡泡的渲染和特有效果
   - 处理泡泡特有的交互逻辑

3. **星星画布组件 (star-canvas)**：
   - 继承自基础画布组件
   - 实现星形元素的渲染和特有效果
   - 处理星星特有的交互逻辑

4. **辅助模块**：
   - **主题数据管理 (theme-manager)**：负责主题数据的加载和缓存
   - **画布管理器 (canvas-manager)**：负责创建和管理不同类型的画布组件

## 功能特性

### 共享功能（基础画布组件）

1. **画布初始化和设置**：
   - 自适应屏幕尺寸
   - 支持高 DPI 显示
   - 配置参数化

2. **动画循环管理**：
   - 使用 requestAnimationFrame 实现高效动画
   - 支持暂停和恢复
   - 帧率优化

3. **触摸事件处理**：
   - 点击检测
   - 拖拽交互
   - 悬停效果

4. **碰撞检测和物理模拟**：
   - 边界碰撞
   - 元素间碰撞
   - 速度和加速度模拟

5. **资源管理**：
   - 自动清理
   - 内存优化
   - 错误恢复

### 泡泡组件特有功能

1. **视觉效果**：
   - 圆形泡泡渲染
   - 渐变填充效果
   - 立体感光影效果
   - 高光位置优化

2. **动画效果**：
   - 泡泡脉动效果
   - 拖动时的光晕效果
   - 平滑的移动和碰撞

3. **交互特性**：
   - 限制拖动距离
   - 松手后反向加速
   - 点击打开主题详情

### 星星组件特有功能

1. **视觉效果**：
   - 圆角五角星形状
   - 渐变填充效果
   - 立体感光影效果

2. **动画效果**：
   - 星星旋转效果
   - 拖动时的光晕效果
   - 平滑的移动和碰撞

3. **交互特性**：
   - 限制拖动距离
   - 松手后反向加速
   - 点击打开主题详情

## 重构和升级内容

### 架构优化

1. **模块化重构**：
   - 将紧耦合的代码拆分为独立组件
   - 实现基于继承的组件架构
   - 清晰定义组件接口和生命周期

2. **代码复用**：
   - 抽取共享逻辑到基础组件
   - 减少重复代码
   - 提高可维护性

3. **依赖管理**：
   - 使用依赖注入模式
   - 减少组件间硬编码依赖
   - 便于单元测试

### 视觉效果升级

1. **泡泡组件**：
   - 增强立体感和质感
   - 优化高光位置，更适合圆形
   - 调整渐变色分布，增强视觉层次
   - 优化拖动时的光晕效果

2. **星星组件**：
   - 实现圆角星星形状
   - 优化渐变效果
   - 调整光影效果，增强立体感
   - 优化拖动时的光晕效果

### 交互体验优化

1. **拖动逻辑统一**：
   - 泡泡和星星使用相同的拖动逻辑
   - 限制拖动距离不超过元素半径
   - 松手后向反方向加速飞去

2. **动画效果控制**：
   - 关闭了闪烁效果（透明度变化）
   - 保留了脉动效果（大小变化）
   - 优化了拖动时的视觉反馈

3. **性能优化**：
   - 减少不必要的渲染
   - 优化碰撞检测算法
   - 实现简单的对象池

## 使用方法

### 初始化和配置

```javascript
// 创建主题管理器
this.themeManager = new ThemeManager({
  api: { tagAPI },
  cacheExpiry: 30 * 60 * 1000 // 30分钟缓存过期时间
});

// 创建画布管理器
this.canvasManager = new CanvasManager({
  page: this,
  themeManager: this.themeManager
});

// 加载主题数据并初始化画布
this.themeManager.loadCurrentPlanTags(true)
  .then(themes => {
    // 创建对应类型的画布
    return this.canvasManager.createCanvas(this.data.interfaceStyle, {
      canvasId: `${this.data.interfaceStyle}-canvas`,
      config: {
        animationSpeedMultiplier: 0.064 * 2,
        baseSpeed: 0.64 * 1.2,
        shadowBlur: 4
      }
    });
  })
  .then(canvas => {
    console.log(`${this.data.interfaceStyle}画布创建成功`);
    this.setData({ initialized: true });
  });
```

### 事件处理

```javascript
// 在页面中处理触摸事件
onTouchStart(e) {
  if (this.canvasManager) {
    const result = this.canvasManager.handleTouchStart(e);
    if (result && result.theme) {
      // 处理主题点击
      this.setData({
        currentTheme: result.theme,
        showThemeModal: true
      });
    }
  }
}

onTouchMove(e) {
  if (this.canvasManager) {
    const cursor = this.canvasManager.handleTouchMove(e);
    // 可以根据返回的cursor值设置鼠标样式
  }
}

onTouchEnd() {
  if (this.canvasManager) {
    const result = this.canvasManager.handleTouchEnd();
    // 处理触摸结束事件
  }
}
```

### 生命周期管理

```javascript
// 页面显示时恢复动画
onShow() {
  if (this.canvasManager) {
    this.canvasManager.resumeAnimation();
  }
}

// 页面隐藏时暂停动画
onHide() {
  if (this.canvasManager) {
    this.canvasManager.pauseAnimation();
  }
}

// 页面卸载时清理资源
onUnload() {
  if (this.canvasManager) {
    this.canvasManager.cleanup();
  }
}
```

## 配置参数

### 基础配置

| 参数名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| animationSpeedMultiplier | number | 0.064 | 动画速度乘数 |
| baseSpeed | number | 0.64 | 基础移动速度 |
| shadowBlur | number | 4 | 阴影模糊半径 |
| maxDeltaTime | number | 50 | 最大时间增量(毫秒) |
| dragThreshold | number | 10 | 拖动阈值(像素) |
| clickThreshold | number | 300 | 点击阈值(毫秒) |

### 泡泡组件配置

| 参数名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| pulseAmplitude | number | 0.03 | 脉动幅度 |
| pulseFrequency | number | 0.001 | 脉动频率 |
| textSizeRatio | number | 0.3 | 文字大小与泡泡半径的比例 |
| hoverScale | number | 1.15 | 悬停时的缩放比例 |

### 星星组件配置

| 参数名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| points | number | 5 | 星星角数 |
| innerRadiusRatio | number | 0.6 | 内半径比例 |
| cornerRadius | number | 15 | 角的圆角半径 |
| textSizeRatio | number | 0.25 | 文字大小与星星半径的比例 |
| hoverScale | number | 1.15 | 悬停时的缩放比例 |

## 未来扩展

1. **更多元素类型**：
   - 可以基于基础画布组件开发更多类型的元素
   - 例如：方形、六边形、自定义形状等

2. **更丰富的交互**：
   - 添加双击、长按等交互方式
   - 实现元素间的连线效果
   - 添加更多的动画效果

3. **性能优化**：
   - 实现更高效的碰撞检测算法
   - 添加元素可见性检测，只渲染可见区域
   - 优化大量元素时的性能

4. **主题定制**：
   - 支持用户自定义元素外观
   - 实现主题色系统
   - 支持动态切换主题

## 总结

通过这次重构和升级，我们将首页的泡泡组件和星星组件进行了模块化拆分，实现了基于继承的组件架构，提高了代码的可维护性和复用性。同时，我们优化了视觉效果和交互体验，使组件更加美观和易用。

新组件已经正式替代了旧组件，并且在功能和性能上都有所提升。未来，我们将继续优化和扩展这些组件，为用户提供更好的体验。
